

# 新零售选品平台
主要功能:
* 商品池和门店池的管理创建
* 投放活动的创建管理

# 文件夹
```bash
└── app
    ├── a.entry                     # 墨子登录登出页面
    ├── components                  # 组件
    │   ├── BlockWrapper            # 投选设置块儿
    │   ├── Button                  # 按钮组件
    │   ├── CascaderSelect          # 级连选择框
    │   ├── FoldBlock               # 折叠组件
    │   ├── FuzzySearch             # 模糊查询组件
    │   ├── Label                   # 标签：状态标签
    │   ├── PageWrapper             # 页面头部标题公共组件
    │   ├── PermissionAccess        # 判断权限组件
    │   ├── PreviewImage            # 图片预览
    │   ├── ProgressView            # 下载进度查看组件
    │   ├── TimeRangePicker         # 时间段选择组件
    │   └── Tip                     # 提示
    ├── config                      # 环境配置
    ├── constants                   # 静态变量
    ├── containers                  # 页面
    │   ├── App                     # 入口
    │   ├── base                    # PageBase
    │   ├── NotFoundPage            # NotFoundPage 
    │   ├── PoolPage                # 老的选品选店池管理创建 —— 嵌入招商的用的
    │   ├── PutInPage               # 老的投放活动管理创建
    │   ├── channel                 # 频道管理
    │   ├── report                  # 报表
    │   └── routes.js               # 路由配置
    ├── home                        # 新页面
    │   ├── poolPage                # 现在的选投平台里的新选品
    │   ├── rank                    # 全能榜单管理
    │   ├── activity                # 投放活动
    │   ├── setting                 # 设置schema
    │   ├── superMarket             # 全能超市频道2.0
    │   └── channel                 # 频道管理
    ├── images                      # 图片
    ├── styles                      # 样式
    ├── utils
    │   ├── api                     # 请求API
    │   ├── Form                    # 频道管理的SchemaForm 
    │   ├── validators              # 检测
    │   └── [OTHER FILES]           # other util functions
    ├── app.js                      # project entry file
    └── index.html                  
```

# 开发

## 配置环境
* install tnpm: 官网：https://web.npm.alibaba-inc.com/package/tnpm
* config your hosts:
```
# /etc/hosts
127.0.0.1   test.selection.kunlun.alibaba-inc.com
```

## 命令
```shell
# 安装项目依赖
tnpm install

# node 版本 >= 18

# start your dev, 占用80端口，需通过sudo启动
sudo tnpm run start
```

# DEF发布

* 首先创建 daily/x.x.x 类似的分支
* `def publish`, 发布到daily环境上
* 等迭代分支发布成功之后, 就可以在def工作台找到对应的迭代分支, 点进入详情发布到线上了


# 域名申请, HTTPS申请, 域名绑定
目前使用了证书域名为 `*.kunlun.ele.alibaba-inc.com` 的单泛san申请. 申请备注填 `cdn https需求`

* https://www.atatech.org/articles/69154

域名绑定需要提交工单, 地址, http://air.alibaba-inc.com/help#join

```
品牌的可以随意用域名，比如 nr-brand.alibaba-inc.com 。 新昆仑用 kunlun.alibaba-inc.com ， 招选搭投用 *.kunlun.alibaba-inc.com 。只要能申请就能用，没有说必须有 ele
```

# 代理调试

通过socks mode来代理电脑的网络请求, 一是非常强大, 第二配置简单. 当然也可以用浏览器插件来显式制定proxy server.

核心的链接: Tracing All Network Machine Traffic Using MITMProxy for Mac OSX

`mitmproxy --mode socks5 --showhost --ssl-insecure`
打开mac网络设置, 点击到你链接的Internet网络上, 点击Advanced-> Proxies -> toogle SOCKS Proxy -> 在 sockets proxy server 下输入 127.0.0.1 端口 8080

# 其他

* 线上cdn地址: https://g.alicdn.com/op-fe/o2o-selection-admin/index.html#/


