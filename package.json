{"name": "react-redux-boilerplate", "version": "1.0.0", "description": "A minimal react-redux boilerplate with all the best practices", "repository": {"type": "git", "url": "**************************:op-fe/o2o-selection-admin.git"}, "license": "MIT", "author": "<PERSON><PERSON>", "scripts": {"prebuild": "tnpm run build:clean", "build": "cross-env NODE_ENV=production webpack --config config/webpack.prod.babel.js --color -p --progress --hide-modules --display-optimization-bailout", "build:clean": "rimraf ./build", "clean": "tnpm run test:clean && tnpm run build:clean", "eslint:fix": "eslint -- . --ignore-path .gitignore --fix", "lint": "tnpm run lint:js", "lint:eslint": "eslint --ignore-path .gitignore", "lint:js": "tnpm run lint:eslint -- . ", "start": "cross-env NODE_ENV=development PORT=8080 HOST=test.selection.kunlun.alibaba-inc.com node server", "start:pre-kunlun": "cross-env NODE_ENV=development PORT=8080 HOST=pre-kunlun.alibaba-inc.com node server", "start:3000": "cross-env NODE_ENV=development node server", "start:prod": "cross-env NODE_ENV=production node server", "start:production": "tnpm run test && tnpm run build && tnpm run start:prod", "pretest": "tnpm run test:clean && tnpm run lint", "test": "cross-env NODE_ENV=test jest --coverage", "test:clean": "rimraf ./coverage", "test:watch": "cross-env NODE_ENV=test jest --watchAll", "prepare": "tnpm-lock-adapter"}, "devDependencies": {"@ali/tnpm-lock-adapter": "^1.8.0", "@alife/nr-crud-hooks": "^0.0.3", "@babel/cli": "7.1.2", "@babel/core": "7.1.2", "@babel/plugin-proposal-class-properties": "7.1.0", "@babel/plugin-proposal-decorators": "7.4.0", "@babel/plugin-syntax-dynamic-import": "7.0.0", "@babel/plugin-transform-modules-commonjs": "7.1.0", "@babel/plugin-transform-react-constant-elements": "7.0.0", "@babel/plugin-transform-react-inline-elements": "7.0.0", "@babel/preset-env": "7.1.0", "@babel/preset-react": "7.0.0", "@babel/register": "7.0.0", "add-asset-html-webpack-plugin": "3.0.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-loader": "8.0.4", "babel-plugin-codegen": "3.0.0", "babel-plugin-dynamic-import-node": "2.2.0", "babel-plugin-import": "1.11.0", "babel-plugin-lodash": "3.3.4", "babel-plugin-react-intl": "3.0.1", "babel-plugin-react-transform": "3.0.0", "babel-plugin-transform-react-remove-prop-types": "0.4.19", "circular-dependency-plugin": "5.0.2", "css-loader": "1.0.0", "cssnano": "^4.1.10", "enzyme": "^3.3.0", "enzyme-adapter-react-16": "^1.1.1", "eslint": "5.7.0", "eslint-import-resolver-webpack": "0.10.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-react": "^7.12.4", "eslint-plugin-redux-saga": "^1.0.0", "eventsource-polyfill": "0.9.6", "exports-loader": "0.7.0", "file-loader": "1.1.11", "html-loader": "0.5.5", "html-webpack-plugin": "4.0.0-alpha", "imports-loader": "0.8.0", "interpolate-html-plugin": "^3.0.0", "jest-cli": "23.6.0", "lint-staged": "7.3.0", "mini-css-extract-plugin": "^0.7.0", "node-plop": "0.16.0", "null-loader": "0.1.1", "optimize-css-assets-webpack-plugin": "^5.0.1", "plop": "2.1.0", "postcss-discard-comments": "^4.0.2", "postcss-flexbugs-fixes": "^4.1.0", "postcss-loader": "^3.0.0", "react-json-view": "^1.21.3", "react-test-renderer": "^16.5.2", "rimraf": "2.6.2", "sass": "^1.55.0", "sass-loader": "^7.0.1", "shelljs": "^0.8.1", "style-loader": "0.23.1", "url-loader": "1.1.2", "webpack": "4.20.2", "webpack-cli": "3.2.1", "webpack-dev-middleware": "3.4.0", "webpack-hot-middleware": "2.24.3", "yargs-parser": "^13.0.0"}, "dependencies": {"@ali/aes-tracker": "^3.3.9", "@ali/aes-tracker-plugin-autolog": "^3.0.11", "@ali/aes-tracker-plugin-pv": "^3.0.6", "@ali/alsc-exlog-web": "^1.0.53", "@ali/alsc-gateway-web-client": "^1.1.9", "@ali/alsc-mod-widget-engine": "^0.0.2", "@ali/lib-mtop": "^2.5.4", "@ali/luna-rmc": "1.2.7", "@ali/mtop-core": "^3.0.5", "@ali/nr-resource-components": "0.0.13", "@ali/pcom-mtop": "^4.1.3", "@ali/tracker": "^4.1.2", "@alife/kunlun-base": "^2.0.2", "@alife/next": "^1.14.0", "@alife/theme-nr-op": "^1.0.0", "@alife/waterMark": "^1.2.2", "@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/polyfill": "^7.0.0", "@simonwep/pickr": "^1.4.8", "axios": "^0.18.0", "bizcharts": "^4.1.11", "chalk": "^2.3.2", "classnames": "^2.2.6", "clipboard": "^2.0.8", "compression": "1.7.3", "connected-react-router": "^6.3.2", "core-js": "2.6.3", "cross-env": "5.2.0", "debug": "^4.4.0", "echarts": "^4.0.0", "express": "4.16.3", "history": "4.7.2", "hoist-non-react-statics": "3.0.1", "immutable": "3.8.2", "invariant": "2.2.4", "ip": "1.1.5", "jsonpath": "^1.0.2", "lodash": "4.17.11", "minimist": "1.2.0", "moment": "^2.24.0", "prop-types": "^15.6.2", "qrcode.react": "^1.0.0", "qs": "^6.7.0", "query-string": "^6.4.2", "react": "^16.5.2", "react-amap": "^1.2.8", "react-dnd": "^10.0.2", "react-dnd-html5-backend": "^10.0.2", "react-dom": "^16.5.2", "react-helmet": "^5.2.0", "react-loadable": "^5.5.0", "react-redux": "^7.0.2", "react-router-dom": "^5.0.0", "redux": "^4.0.1", "redux-immutable": "^4.0.0", "redux-logger": "^3.0.6", "redux-saga": "^1.0.2", "reselect": "4.0.0", "shortid": "^2.2.15", "simple-evaluate": "^1.4.0", "url-parse": "^1.4.6", "uuid4": "^2.0.3", "vanilla-jsoneditor": "^0.17.8", "warning": "^4.0.1"}, "overrides": {"node-sass": "npm:sass@^1.55.0"}, "resolutions": {"babel-core": "7.0.0-bridge.0", "node-sass": "npm:sass@^1.55.0"}, "engines": {"node": ">=8.10.0", "npm": ">=5"}}