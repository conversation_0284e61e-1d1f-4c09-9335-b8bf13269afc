{"parser": "babel-es<PERSON>", "extends": ["eslint:recommended"], "env": {"browser": true, "node": true, "jest": true, "es6": true}, "plugins": ["redux-saga", "react"], "parserOptions": {"ecmaVersion": 6, "sourceType": "module", "ecmaFeatures": {"jsx": true, "legacyDecorators": true}}, "rules": {"react/jsx-uses-react": 2, "react/jsx-uses-vars": 2, "react/jsx-no-undef": 2, "react/jsx-no-duplicate-props": 2, "react/no-children-prop": 2, "react/no-string-refs": 2, "react/require-render-return": 2, "react/no-direct-mutation-state": 2, "no-param-reassign": "off", "function-paren-newline": "off", "arrow-parens": 0, "arrow-body-style": [0, "as-needed"], "comma-dangle": ["error", "only-multiline"], "import/no-extraneous-dependencies": 0, "import/prefer-default-export": 0, "indent": [2, 2, {"SwitchCase": 1}], "max-len": 0, "no-console": [1, {"allow": ["warn", "error", "info"]}], "redux-saga/no-yield-in-race": 2, "redux-saga/yield-effects": 2, "no-unused-vars": [2, {"argsIgnorePattern": "^_"}], "semi": 0, "no-multiple-empty-lines": 0, "padded-blocks": 0}, "settings": {"import/resolver": {"webpack": {"config": "./config/webpack.prod.babel.js"}}}}