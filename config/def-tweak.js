
/* eslint-disable */
const path = require('path')
const parse = require('yargs-parser');

let ENV = process.env

let processEnv = {
    IS_PRODUCTION: ENV.NODE_ENV === 'production',
    BUILD_GIT_GROUP : ENV['BUILD_GIT_GROUP'],
    BUILD_GIT_PROJECT : ENV['BUILD_GIT_PROJECT'],
    BUILD_GIT_COMMITID : ENV['BUILD_GIT_COMMITID'],
    BUILD_GIT_BRANCH : ENV['BUILD_GIT_BRANCH'],
    BUILD_GIT_VERSION : ENV['BUILD_GIT_BRANCH'] ? ENV['BUILD_GIT_BRANCH'].split(path.sep).pop() : undefined,
    BUILD_ARGV_STR: ENV['BUILD_ARGV_STR'],
    BUILD_ENV: ENV['BUILD_ENV']
}

let argv = !!processEnv.BUILD_ARGV_STR ? parse(processEnv.BUILD_ARGV_STR) : {};

console.log('app-build-args: ', JSON.stringify(argv))

// cdn路径
function cdnBase(){
  // 预发环境
  if (isDaily()) {
    // return '//g-assets.daily.taobao.net/';
    return '//dev.g.alicdn.com/';
  }
  return '//g.alicdn.com/';
}

function isInCloud(){
  return processEnv.BUILD_ENV == 'cloud'
}

function getBuildEnv(){
  return argv['def_publish_env']
}

function isDaily(){
  return getBuildEnv() === 'daily'
}

function getPublicPath(){
  if (!processEnv.IS_PRODUCTION || !isInCloud()) return '/'

  publicPath = cdnBase() + path.join(processEnv.BUILD_GIT_GROUP, processEnv.BUILD_GIT_PROJECT, processEnv.BUILD_GIT_VERSION) + '/'
  let context = {
    publicPath,
    env: process.env,
  }
  console.log('pathlic Path is: ', JSON.stringify(context))
  return publicPath
}

/**
 * @returns env: string, daily | prod | localdev
 */
function getEnv(){
  if (getBuildEnv() === 'daily') return 'daily'
  if (getBuildEnv() ===  'prod') return 'prod'
  else return 'localdev'
}

module.exports = {
  getPublicPath,
  getEnv,
  cdnBase
}

/* eslint-enable */
