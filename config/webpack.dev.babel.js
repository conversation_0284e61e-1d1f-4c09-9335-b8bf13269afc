/**
 * DEVELOPMENT WEBPACK CONFIGURATION
 */

const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CircularDependencyPlugin = require('circular-dependency-plugin');
const { defaultConfig, entryKeys, entrys } = require('./webpack.base.babel');
const MiniCssExtractPlugin =require('mini-css-extract-plugin');

module.exports = defaultConfig({
  mode: 'development',
  // Add hot reloading in development
  entry: {
    [entryKeys.app]: [
      'eventsource-polyfill', // Necessary for hot reloading with IE
      'webpack-hot-middleware/client?reload=true',
      path.join(process.cwd(), 'app/app.js') // Start with js/app.js
    ],
    [entryKeys.login]: entrys[entryKeys.login],
    [entryKeys.logout]: entrys[entryKeys.logout]
  },

  // Don't use hashes in dev mode for better performance
  output: {
    filename: '[name].js',
    chunkFilename: '[name].chunk.js'
  },
  // Add development plugins
  plugins: [
    new webpack.HotModuleReplacementPlugin(), // Tell webpack we want hot reloading
    new HtmlWebpackPlugin({
      inject: true, // Inject all files that are generated by webpack, e.g. bundle.js
      template: 'app/index.html',
      chunks: [entryKeys.app]
    }),
    new CircularDependencyPlugin({
      exclude: /a\.js|node_modules/, // exclude node_modules
      failOnError: false // show a warning when there is a circular dependency
    }),
    new MiniCssExtractPlugin({
      filename: '[name].css',
      chunkFilename: '[id].css',
    })
  ],

  // Emit a source map for easier debugging
  // See https://webpack.js.org/configuration/devtool/#devtool
  devtool: 'eval-source-map',

  performance: {
    hints: false
  }
});
