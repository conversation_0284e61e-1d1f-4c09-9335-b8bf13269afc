/**
 * COMMON WEBPACK CONFIGURATION
 */

const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin =require('mini-css-extract-plugin');
const InterpolateHtmlPlugin = require('interpolate-html-plugin')
const {getEnv} = require('./def-tweak')
const defTweak = require('./def-tweak')

process.noDeprecation = true;

const defaultConfig =  (options) => ({
  mode: options.mode,
  entry: options.entry,
  output: Object.assign(
    {
      // Compile into js/build.js
      path: path.resolve(process.cwd(), 'build'),
      publicPath: '/'
    },
    options.output
  ), // Merge with env dependent settings
  module: {
    rules: [
      {
        test: /\.js$/, // Transform all .js files required somewhere with Babel
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: options.babelQuery
        }
      },
      {
        // Preprocess our own .scss files
        test: /\.scss$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: {
              hmr: process.env.NODE_ENV === 'development',
              publicPath: '../'
            },
          },
          'css-loader', 
          'sass-loader',
        ]
      },
      {
        // Preprocess 3rd party .css files located in node_modules
        test: /\.css$/,
        include: /node_modules/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(eot|svg|otf|ttf|woff|woff2)$/,
        use: 'file-loader'
      },
      {
        test: /\.(jpg|png|gif)$/,
        use: 'file-loader'
      },
      {
        test: /\.html$/,
        use: 'html-loader'
      },
      {
        test: /\.(mp4|webm)$/,
        use: {
          loader: 'url-loader',
          options: {
            limit: 10000
          }
        }
      }
    ]
  },
  plugins: options.plugins.concat([
    // new webpack.ProvidePlugin({
    //   // make fetch available
    //   fetch: 'exports-loader?self.fetch!whatwg-fetch'
    // }),

    // Always expose NODE_ENV to webpack, in order to use `process.env.NODE_ENV`
    // inside your code for any environment checks; UglifyJS will automatically
    // drop any unreachable code.
    new HtmlWebpackPlugin({
      template: path.join(process.cwd(), 'app/a.entry/login/sendBucSSOToken.htm'),
      filename: 'sendBucSSOToken.htm',
      chunks: [entryKeys.login],
      inlineSource: '.(js|css)$',
    }),

    new HtmlWebpackPlugin({
      template: path.join(process.cwd(), 'app/a.entry/logout/bucSSOLogout.htm'),
      filename: 'bucSSOLogout.htm',
      chunks: [entryKeys.logout],
      inlineSource: '.(js|css)$',
    }),
    new InterpolateHtmlPlugin({
      'publicPath': defTweak.getPublicPath()
    }),
    new webpack.DefinePlugin({
      'process.env': {
        NODE_ENV: JSON.stringify(process.env.NODE_ENV),
        APP_ENV: JSON.stringify(getEnv())
      },
    })
  ]),
  resolve: {
    extensions: ['.js', '.jsx', '.scss', '.react.js'],
    mainFields: ['browser', 'main', 'jsnext:main'],
    alias: {
      '@': path.resolve(__dirname, '../app/')
    }
  },
  devtool: options.devtool,
  target: 'web', // Make web variables accessible to webpack, e.g. window
  performance: options.performance || {},
  optimization: {
    namedModules: true,
    splitChunks: {
      name: true,
      cacheGroups: {
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          chunks: 'all',
          enforce: true
        }
      }
    }
  }
});


const entryKeys = {
  'app': 'app',
  'login': 'login',
  'logout':'logout'
}

const entrys = {
  [entryKeys.app]: path.join(process.cwd(), 'app/app.js'),
  [entryKeys.login]: path.join(process.cwd(), 'app/a.entry/login/index.js'),
  [entryKeys.logout]: path.join(process.cwd(), 'app/a.entry/logout/index.js')
}

module.exports = {
  defaultConfig, entryKeys, entrys
}
