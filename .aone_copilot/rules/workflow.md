# 开发工作流程规范

本文档定义了新零售选品平台的开发工作流程、Git管理策略和发布规范。

## Git 工作流程

### 分支管理策略

#### 主要分支
- **master**: 主分支，保存稳定的生产环境代码
- **develop**: 开发分支，集成最新的开发功能
- **daily/x.x.x**: 日常迭代分支，用于日常环境发布
- **feature/功能名**: 功能开发分支
- **bugfix/问题描述**: 问题修复分支
- **hotfix/紧急修复**: 生产环境紧急修复分支

#### 分支命名规范
```bash
# 功能开发分支
feature/pool-management-enhancement
feature/activity-create-optimization

# 问题修复分支
bugfix/pool-detail-loading-issue
bugfix/activity-time-validation-error

# 日常迭代分支
daily/1.2.3
daily/1.2.4

# 紧急修复分支
hotfix/urgent-api-fix
hotfix/security-patch
```

### 代码提交规范

#### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 提交类型（type）
- **feat**: 新功能
- **fix**: 修复问题
- **docs**: 文档修改
- **style**: 代码格式修改（不影响代码运行）
- **refactor**: 代码重构
- **test**: 测试相关修改
- **chore**: 构建过程或工具修改

#### 提交示例
```bash
feat(pool): 新增标签选品功能

- 添加标签规则配置组件
- 实现商品筛选逻辑
- 添加预览功能

Closes #123

fix(activity): 修复活动时间验证问题

- 修正时间范围验证逻辑
- 更新错误提示文案
- 添加边界条件检查

Fixes #456
```

## 开发流程

### 1. 功能开发流程

```bash
# 1. 从 develop 分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 2. 开发过程中定期提交
git add .
git commit -m "feat(module): 实现xxx功能"

# 3. 开发完成后推送分支
git push origin feature/new-feature

# 4. 创建 Merge Request 到 develop 分支
# 5. 代码审查通过后合并
# 6. 删除功能分支
git branch -d feature/new-feature
```

### 2. 日常发布流程

#### 创建日常分支
```bash
# 从 develop 分支创建日常分支
git checkout develop
git pull origin develop
git checkout -b daily/x.x.x
git push origin daily/x.x.x
```

#### DEF 发布步骤
1. **创建迭代分支**: 创建 `daily/x.x.x` 格式的分支
2. **DEF 发布**: 执行 `def publish` 命令发布到日常环境
3. **测试验证**: 在日常环境进行功能测试和回归测试
4. **线上发布**: 在 DEF 工作台找到对应迭代分支，发布到线上环境

#### 发布检查清单
- [ ] 代码已通过所有单元测试
- [ ] ESLint 检查无错误
- [ ] 功能在日常环境测试通过
- [ ] 相关文档已更新
- [ ] 数据库迁移脚本已准备（如需要）
- [ ] 监控和日志配置已就绪

### 3. 紧急修复流程

```bash
# 1. 从 master 分支创建 hotfix 分支
git checkout master
git pull origin master
git checkout -b hotfix/urgent-fix

# 2. 快速修复问题
git add .
git commit -m "hotfix: 修复紧急问题"

# 3. 合并到 master 和 develop
git checkout master
git merge --no-ff hotfix/urgent-fix
git checkout develop
git merge --no-ff hotfix/urgent-fix

# 4. 立即发布
git tag v1.x.x
git push origin master develop --tags
```

## 开发环境配置

### 环境要求
- **Node.js**: >= 18
- **npm**: 使用 tnpm (阿里内部 npm)
- **系统权限**: 开发环境需要 sudo 权限（80端口占用）

### 本地开发设置

#### 1. hosts 配置
```bash
# 添加到 /etc/hosts
127.0.0.1   test.selection.kunlun.alibaba-inc.com
```

#### 2. 安装依赖
```bash
tnpm install
```

#### 3. 启动开发服务器
```bash
# 标准启动（占用80端口）
sudo tnpm run start

# 其他启动方式
sudo tnpm run start:pre-kunlun  # 预发环境
tnpm run start:3000             # 3000端口启动
```

### 代理调试配置

#### SOCKS 代理设置
```bash
# 启动 mitmproxy
mitmproxy --mode socks5 --showhost --ssl-insecure

# Mac 网络设置
# Network -> Advanced -> Proxies -> SOCKS Proxy
# 服务器: 127.0.0.1 端口: 8080
```

## 代码审查流程

### 审查清单

#### 代码质量
- [ ] 代码符合项目编码规范
- [ ] 没有明显的性能问题
- [ ] 错误处理恰当
- [ ] 代码可读性良好
- [ ] 没有硬编码的配置信息

#### 功能实现
- [ ] 功能实现符合需求
- [ ] 边界条件处理正确
- [ ] 用户体验友好
- [ ] 兼容性考虑充分

#### 安全性
- [ ] 没有安全漏洞
- [ ] 权限控制正确
- [ ] 数据验证充分
- [ ] 敏感信息处理安全

### 审查工具
- **ESLint**: 自动代码检查
- **SonarQube**: 代码质量分析（如配置）
- **人工审查**: Merge Request 代码审查

## 测试策略

### 测试类型

#### 1. 单元测试
```bash
# 运行单元测试
tnpm run test

# 监听模式运行测试
tnpm run test:watch

# 生成覆盖率报告
tnpm run test --coverage
```

#### 2. 集成测试
- API 接口测试
- 组件集成测试
- 端到端功能测试

#### 3. 手工测试
- 功能测试
- 兼容性测试
- 用户体验测试

### 测试环境

#### 环境列表
- **localdev**: 本地开发环境
- **daily**: 日常测试环境
- **ppe**: 预发布环境
- **prod**: 生产环境

#### 环境域名
```javascript
const domains = {
  localdev: 'http://localhost:8080',
  daily: 'https://market.wapa.taobao.com',
  ppe: 'https://pre-kunlun.alibaba-inc.com',
  prod: 'https://selection.kunlun.alibaba-inc.com'
};
```

## 域名和证书管理

### 域名申请流程
1. **证书申请**: 使用 `*.kunlun.ele.alibaba-inc.com` 单泛san证书
2. **申请备注**: 填写 "cdn https需求"
3. **工单提交**: 在 http://air.alibaba-inc.com/help#join 提交域名绑定工单

### 域名规范
- **品牌域名**: `nr-brand.alibaba-inc.com`
- **新昆仑域名**: `kunlun.alibaba-inc.com`
- **招选搭投域名**: `*.kunlun.alibaba-inc.com`

## 监控和日志

### 错误监控
- 前端错误上报
- API 请求监控
- 性能指标监控

### 日志管理
```javascript
// 开发环境日志
console.info('功能执行信息');
console.warn('警告信息');
console.error('错误信息');

// 生产环境避免 console.log
// 使用统一的日志服务
```

## 文档维护

### 文档更新要求
- 新功能必须更新相关文档
- API 变更需要更新接口文档
- 重要配置变更需要更新部署文档
- 定期检查文档的准确性和完整性

### 文档类型
- **技术文档**: API 接口、架构设计
- **用户文档**: 功能使用说明
- **运维文档**: 部署、监控、故障处理
- **开发文档**: 环境配置、开发规范

## 故障处理流程

### 紧急故障响应
1. **问题确认**: 快速确认问题影响范围
2. **临时处理**: 执行回滚或临时修复
3. **根因分析**: 深入分析问题根本原因
4. **永久修复**: 实施完整的解决方案
5. **复盘总结**: 记录问题和改进措施

### 故障等级
- **P0**: 系统完全不可用
- **P1**: 核心功能不可用
- **P2**: 部分功能异常
- **P3**: 轻微问题或优化需求

这些流程规范确保项目开发的有序进行和代码质量的持续提升。