# 新零售选品平台项目结构

新零售选品平台（o2o-selection-admin）是一个基于 React + Redux 的前端管理系统，主要提供商品池和门店池的管理创建，以及投放活动的创建管理功能。

## 主要目录结构

- `app/`: 主要源代码目录
  - `components/`: 通用组件库
    - `BlockWrapper/`: 投选设置块组件
    - `Button/`: 按钮组件
    - `CascaderSelect/`: 级联选择框组件
    - `FoldBlock/`: 折叠组件
    - `FuzzySearch/`: 模糊查询组件
    - `Label/`: 状态标签组件
    - `PageWrapper/`: 页面头部标题公共组件
    - `PermissionAccess/`: 权限判断组件
    - `PreviewImage/`: 图片预览组件
    - `ProgressView/`: 下载进度查看组件
    - `TimeRangePicker/`: 时间段选择组件
    - `Tip/`: 提示组件
  - `containers/`: 老版本页面模块
    - `App/`: 应用入口
    - `base/`: 页面基础类
    - `NotFoundPage/`: 404页面
    - `PoolPage/`: 老版本选品选店池管理
    - `PutInPage/`: 老版本投放活动管理
    - `channel/`: 频道管理
    - `report/`: 报表模块
  - `home/`: 新版本页面模块
    - `poolPage/`: 新版本选品平台
      - `tagPool/`: 标签选品功能
      - `intelligentPool/`: 智能选品功能
      - `labelPool/`: 标签池管理
      - `fileUpload/`: 文件上传选品
      - `poolDetail/`: 选品池详情
      - `comps/`: 选品相关公共组件
    - `rank/`: 全能榜单管理
    - `activity/`: 投放活动模块
    - `setting/`: 设置模块
    - `superMarket/`: 全能超市频道2.0
    - `channel/`: 频道管理模块
  - `config/`: 环境配置
    - `daily.js`: 日常环境配置
    - `localdev.js`: 本地开发环境配置
    - `ppe.js`: 预发布环境配置
    - `pre.js`: 预发环境配置
    - `prod.js`: 生产环境配置
  - `constants/`: 静态常量定义
  - `utils/`: 工具函数
    - `api/`: API 请求相关
    - `Form/`: 表单相关工具
    - `validators/`: 数据验证工具
  - `styles/`: 样式文件
    - `common.scss`: 公共样式
    - `global-styles.scss`: 全局样式
    - `theme.scss`: 主题样式

- `config/`: 构建配置目录
  - `webpack.base.babel.js`: Webpack 基础配置
  - `webpack.dev.babel.js`: 开发环境配置
  - `webpack.prod.babel.js`: 生产环境配置
  - `jest.config.js`: Jest 测试配置

- `server/`: 开发服务器配置
  - `index.js`: 服务器入口文件
  - `middlewares/`: 中间件配置

## 关键文件

- [package.json](mdc:package.json): 项目依赖和脚本配置
- [README.md](mdc:README.md): 项目说明文档
- [.eslintrc](mdc:.eslintrc): ESLint 代码检查配置
- [config/webpack.base.babel.js](mdc:config/webpack.base.babel.js): Webpack 基础构建配置
- [app/app.js](mdc:app/app.js): 应用程序入口文件
- [app/containers/routes.js](mdc:app/containers/routes.js): 路由配置文件
- [app/configureStore.js](mdc:app/configureStore.js): Redux store 配置
- [app/reducers.js](mdc:app/reducers.js): Redux reducers 配置

## 技术架构

### 前端技术栈
- **框架**: React 16.5.2
- **状态管理**: Redux + Redux-Saga
- **路由**: React Router v5
- **UI组件库**: @alife/next (阿里内部组件库)
- **构建工具**: Webpack 4 + Babel 7
- **样式**: SCSS
- **代码检查**: ESLint

### 构建和运行

```bash
# 安装依赖
tnpm install

# 开发环境启动 (需要 sudo 权限，占用80端口)
sudo tnpm run start

# 生产环境构建
tnpm run build

# 运行测试
tnpm run test

# 代码检查
tnpm run lint
```

### 路径别名配置
- `@/`: 指向 `app/` 目录，用于简化模块导入路径

### 多入口配置
- `app`: 主应用入口
- `login`: 登录页面入口
- `logout`: 登出页面入口