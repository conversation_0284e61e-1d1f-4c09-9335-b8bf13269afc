# 新零售选品平台代码风格规范

本项目遵循以下代码风格规范和约定，确保代码的一致性和可维护性。

## JavaScript/React 代码风格

### 基础配置
- **解析器**: babel-eslint
- **代码检查**: ESLint (基于 eslint:recommended)
- **支持语法**: ES6+, JSX, 装饰器语法

### 缩进和格式
```javascript
// 使用 2 空格缩进
const component = {
  render() {
    return (
      <div>
        <span>Hello World</span>
      </div>
    );
  }
};

// Switch 语句缩进
switch (type) {
  case 'ADD':
    return state + 1;
  case 'SUBTRACT':
    return state - 1;
  default:
    return state;
}
```

### 命名约定

#### 文件命名
- **组件文件**: 使用 PascalCase，如 `PageWrapper.jsx`、`CascaderSelect.js`
- **工具文件**: 使用 camelCase，如 `validators.js`、`formTool.js`
- **常量文件**: 使用 camelCase，如 `constants/index.js`
- **样式文件**: 使用 kebab-case 或与组件同名，如 `style.scss`

#### 变量和函数命名
```javascript
// 变量使用 camelCase
const userName = 'admin';
const poolDataSource = [];

// 函数使用 camelCase
const getUserInfo = () => {};
const handleSubmit = () => {};

// 常量使用 UPPER_SNAKE_CASE
const PAGE_SIZE = 50;
const API_BASE_URL = 'https://api.example.com';

// React 组件使用 PascalCase
const PageWrapper = () => {};
class PoolDetailPage extends React.Component {}
```

#### React 特定命名
```javascript
// 事件处理函数以 handle 开头
const handleClick = () => {};
const handleSubmit = () => {};
const handleChange = () => {};

// 布尔值变量使用 is/has/can 前缀
const isLoading = false;
const hasPermission = true;
const canEdit = false;

// 组件 Props 类型定义
const propTypes = {
  poolId: PropTypes.string,
  isVisible: PropTypes.bool,
  onSubmit: PropTypes.func
};
```

## 代码组织

### 文件结构
```javascript
// 文件顶部：导入顺序
// 1. React 相关
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';

// 2. 第三方库
import { Button, Table, Dialog } from '@alife/next';
import moment from 'moment';
import _ from 'lodash';

// 3. 内部模块 (使用 @ 别名)
import { PageWrapper } from '@/components/PageWrapper';
import * as api from '@/utils/api';
import { formatTime } from '@/utils/time';

// 4. 相对路径导入
import './style.scss';
```

### 组件结构
```javascript
// 类组件结构
class ComponentName extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  // 生命周期方法
  componentDidMount() {}
  componentWillReceiveProps(nextProps) {}

  // 事件处理方法
  handleClick = () => {};
  handleSubmit = () => {};

  // 渲染方法
  render() {
    return <div></div>;
  }
}

// 函数组件结构
const ComponentName = ({ prop1, prop2 }) => {
  // Hooks
  const [state, setState] = useState();
  useEffect(() => {}, []);

  // 事件处理函数
  const handleClick = () => {};

  // 渲染
  return <div></div>;
};
```

## React/JSX 规范

### JSX 语法
```javascript
// 使用双引号
<Button type="primary" size="large">提交</Button>

// 多行 JSX 使用括号包裹
const element = (
  <div className="container">
    <span className="title">标题</span>
  </div>
);

// 条件渲染
{isVisible && <Modal />}
{type === 'edit' ? <EditForm /> : <ViewForm />}

// 列表渲染
{items.map(item => (
  <Item key={item.id} data={item} />
))}
```

### Props 传递
```javascript
// 布尔值 Props 简写
<Component isVisible />

// 对象展开
<Component {...props} />

// 函数传递参数使用箭头函数
<Button onClick={() => handleClick(id)}>删除</Button>
```

## Redux/状态管理规范

### Action 命名
```javascript
// 使用 UPPER_SNAKE_CASE
const FETCH_POOL_LIST_REQUEST = 'FETCH_POOL_LIST_REQUEST';
const FETCH_POOL_LIST_SUCCESS = 'FETCH_POOL_LIST_SUCCESS';
const FETCH_POOL_LIST_FAILURE = 'FETCH_POOL_LIST_FAILURE';
```

### Reducer 结构
```javascript
const initialState = {
  loading: false,
  data: [],
  error: null
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case FETCH_REQUEST:
      return { ...state, loading: true };
    default:
      return state;
  }
}
```

## 注释规范

### 文件头注释
```javascript
/**
 * 选品池管理页面
 * 主要功能：选品池的创建、编辑、查看和删除
 * <AUTHOR>
 * @date 2023-XX-XX
 */
```

### 函数注释
```javascript
/**
 * 获取选品池详情
 * @param {string} poolId - 选品池ID
 * @param {Object} options - 查询选项
 * @returns {Promise} 返回选品池详情数据
 */
const getPoolDetail = async (poolId, options = {}) => {
  // 实现逻辑
};
```

### 组件注释
```javascript
/**
 * 选品池列表组件
 * Props:
 * - dataSource: 数据源数组
 * - loading: 加载状态
 * - onEdit: 编辑回调函数
 */
const PoolList = ({ dataSource, loading, onEdit }) => {
  // 组件实现
};
```

## 错误处理和日志

### 异常处理
```javascript
// API 调用异常处理
try {
  const result = await api.getPoolList();
  // 处理成功结果
} catch (error) {
  console.error('获取选品池列表失败:', error);
  Message.error('获取数据失败，请重试');
}
```

### 控制台输出
```javascript
// 开发环境调试信息
console.info('Pool data loaded:', poolData);

// 警告信息
console.warn('该功能即将废弃，请使用新版本API');

// 错误信息
console.error('API请求失败:', error);

// 生产环境避免 console.log，使用 console.info/warn/error
```

## SCSS/样式规范

### 类名命名
```scss
// 使用 BEM 命名法或语义化命名
.pool-list {
  &__item {
    padding: 16px;
    
    &--selected {
      background-color: #f0f0f0;
    }
  }
  
  &__actions {
    text-align: right;
  }
}

// 页面级样式使用页面名前缀
.pool-detail-page {
  .form-section {
    margin-bottom: 24px;
  }
}
```

### 变量使用
```scss
// 使用项目主题变量
@import '@/styles/theme.scss';

.component {
  color: $primary-color;
  font-size: $font-size-base;
}
```

## 性能优化考虑

### 组件优化
```javascript
// 使用 React.memo 优化函数组件
const MemoizedComponent = React.memo(({ data }) => {
  return <div>{data}</div>;
});

// 使用 useCallback 优化函数传递
const handleClick = useCallback(() => {
  // 处理逻辑
}, [dependency]);

// 使用 useMemo 优化计算结果
const processedData = useMemo(() => {
  return expensiveCalculation(data);
}, [data]);
```

### 列表渲染优化
```javascript
// 大列表使用 key 优化
{items.map(item => (
  <Item key={item.id} data={item} />
))}

// 避免在 render 中创建新对象
// ❌ 不好的方式
<Component style={{marginTop: 10}} />

// ✅ 好的方式
const itemStyle = { marginTop: 10 };
<Component style={itemStyle} />
```

## 代码检查规则

项目使用 ESLint 进行代码检查，主要规则包括：
- 禁止未使用的变量（下划线开头的参数除外）
- React 组件必须正确使用 JSX
- Redux-Saga 相关语法检查
- 强制使用分号结尾（关闭）
- 允许 console.warn/error/info，警告 console.log