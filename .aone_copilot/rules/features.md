# 主要功能模块

新零售选品平台（o2o-selection-admin）是一个基于 React + Redux 的前端管理系统，主要提供以下功能模块：

## 核心功能

### 1. 商品池管理

商品池管理是平台的核心功能，提供多种选品方式来创建和管理商品池。

相关代码目录:
- `app/home/<USER>/`: 新版选品平台主目录
- `app/containers/PoolPage/`: 老版选品池管理
- `app/home/<USER>/comps/`: 选品相关公共组件

#### 1.1 标签选品
- **功能描述**: 根据商品特征规则进行圈选，支持最大1000万商品量
- **相关代码**: `app/home/<USER>/tagPool/`
- **主要特点**:
  - 基于商品属性、销量、价格等维度设置筛选规则
  - 支持多条件组合筛选
  - 实时预览筛选结果
  - 支持规则保存和复用

#### 1.2 智能选品
- **功能描述**: 基于算法智能选品，支持最大100万商品量
- **相关代码**: `app/home/<USER>/intelligentPool/`
- **主要特点**:
  - AI算法驱动的商品推荐
  - 基于历史数据和用户行为优化选品
  - 自动化规则生成
  - 智能化商品排序

#### 1.3 文件上传选品
- **功能描述**: 通过文件批量导入商品，支持上限10万商品
- **相关代码**: `app/home/<USER>/fileUpload/`
- **主要特点**:
  - Excel/CSV文件批量上传
  - 数据格式验证和错误提示
  - 上传进度显示
  - 导入结果预览和确认

#### 1.4 专家池创建
- **功能描述**: 基于专家经验和场景定义的专业选品方式
- **相关代码**: `app/home/<USER>/expertPoolCreate/`
- **主要特点**:
  - 场景化底池规则配置
  - 自定义筛选条件设置
  - 运营可见性筛选
  - 分步骤创建流程

#### 1.5 选品池详情管理
- **功能描述**: 查看和管理已创建的选品池
- **相关代码**: `app/home/<USER>/poolDetail/`、`app/containers/PoolPage/PoolDetailPage/`
- **主要特点**:
  - 选品池基本信息展示
  - 商品列表查看和搜索
  - 商品状态管理（上架/下架）
  - 选品池规则查看
  - 批量操作功能

### 2. 门店池管理

门店池管理提供门店的筛选、组织和管理功能。

相关代码目录:
- `app/containers/PoolPage/`: 包含门店池相关功能
- `app/home/<USER>/`: 新版门店管理功能

主要特点:
- 门店地理位置筛选
- 门店经营状态管理
- 门店主营类目配置
- 门店池创建和编辑
- 门店数据导入导出

### 3. 投放活动管理

投放活动管理负责营销活动的创建、配置和执行。

相关代码目录:
- `app/home/<USER>/`: 新版投放活动模块
- `app/containers/PutInPage/`: 老版投放活动管理

主要特点:
- 活动基本信息配置
- 商品池和门店池关联
- 活动时间和规则设置
- 活动效果数据监控
- 活动状态管理（草稿/发布/暂停/结束）

### 4. 频道管理

频道管理提供不同业务频道的配置和运营功能。

相关代码目录:
- `app/home/<USER>/`: 新版频道管理
- `app/containers/channel/`: 老版频道管理
- `app/containers/channel/market/`: 市场频道相关功能

#### 4.1 频道配置
- **功能描述**: 频道基础信息和展示规则配置
- **主要特点**:
  - 频道基本信息设置
  - 展示模板配置
  - 商品排序规则设置
  - 频道权限管理

#### 4.2 市场频道
- **相关代码**: `app/containers/channel/market/`
- **主要特点**:
  - 门店主营类目管理
  - 商品品类配置
  - 供应链数据管理
  - 市场数据分析

### 5. 全能榜单管理

全能榜单管理提供榜单的创建、配置和数据管理功能。

相关代码目录:
- `app/home/<USER>/`: 全能榜单管理模块
- `app/home/<USER>/`: 榜单管理V2版本

主要特点:
- 榜单规则配置
- 商品排行算法设置
- 榜单数据更新和维护
- 榜单展示效果预览
- 多维度数据分析

### 6. 全能超市频道2.0

全能超市频道2.0是针对超市业态的专门频道管理功能。

相关代码目录:
- `app/home/<USER>/`: 全能超市频道2.0模块
- `app/home/<USER>/`: 大店频道相关功能

主要特点:
- 超市专属商品管理
- 大店小店差异化配置
- 超市业态数据分析
- 专门的选品策略
- 超市频道效果监控

### 7. 报表分析

报表分析模块提供各类数据统计和分析功能。

相关代码目录:
- `app/containers/report/`: 报表模块

主要特点:
- 选品效果数据分析
- 投放活动效果统计
- 频道运营数据报表
- 商品销售数据分析
- 自定义报表生成
- 数据导出功能

## 配置与服务

### 环境配置管理
- **相关代码**: `app/config/`
- **功能**: 多环境配置管理（本地开发、日常、预发布、生产）
- **特点**: 环境变量配置、API地址配置、功能开关配置

### 权限管理
- **相关代码**: `app/components/PermissionAccess/`、`app/components/ACLAccess/`
- **功能**: 用户权限控制和访问管理
- **特点**: 页面级权限控制、按钮级权限控制、数据权限控制

### 设置管理
- **相关代码**: `app/home/<USER>/`
- **功能**: 系统设置和用户偏好配置
- **特点**: Schema配置管理、用户个性化设置、系统参数配置

## 通用组件库

### UI 组件
- **PageWrapper**: 页面头部标题公共组件
- **Button**: 自定义按钮组件
- **CascaderSelect**: 级联选择框组件
- **FoldBlock**: 折叠组件
- **FuzzySearch**: 模糊查询组件
- **PreviewImage**: 图片预览组件
- **ProgressView**: 下载进度查看组件
- **TimeRangePicker**: 时间段选择组件

### 业务组件
- **BlockWrapper**: 投选设置块组件
- **Label**: 状态标签组件
- **Tip**: 提示组件
- **SamplePool**: 选品结果预览组件

## 数据流管理

### API 管理
- **相关代码**: `app/utils/api/`、`app/adator/api.js`
- **功能**: 统一的API请求管理
- **特点**: 请求拦截器、响应处理、错误统一处理

### 状态管理
- **相关代码**: `app/configureStore.js`、`app/reducers.js`
- **功能**: Redux状态管理
- **特点**: 模块化状态管理、异步操作处理、状态持久化

### 工具函数
- **相关代码**: `app/utils/`
- **功能**: 通用工具函数库
- **特点**: 表单验证、时间处理、数据转换、分页处理等

## 技术特性

### 路由管理
- **相关代码**: `app/containers/routes.js`
- **功能**: 页面路由配置和导航
- **特点**: 懒加载、路由守卫、动态路由

### 样式管理
- **相关代码**: `app/styles/`
- **功能**: 全局样式和主题管理
- **特点**: SCSS模块化、主题变量、全局重置样式

### 构建优化
- **相关代码**: `config/webpack.*.babel.js`
- **功能**: 项目构建和优化
- **特点**: 代码分割、资源优化、多入口配置

这些功能模块共同构成了一个完整的新零售选品平台，为用户提供从商品选择到投放执行的全流程管理能力。