import { login } from '@/utils/api';
import { getUrlQuery } from '@/utils/others';
import { SSO_TOKEN as lkey} from '@/constants';

export function sendToken() {
  const { SSO_TOKEN, BACK_URL } = getUrlQuery(window.location.href);
  localStorage.setItem(lkey, SSO_TOKEN);
  login({ SSO_TOKEN, BACK_URL })
    .then(res => {
      console.log(res);
      // if (res.status === 200) {
      window.location = decodeURIComponent(BACK_URL);
      // } else {
      //   console.log('error', res);
      // }
    })
    .catch(console.error);
}

sendToken()
