import { logout, onRequestSuccess, onRequestError} from '@/utils/api';
import {config} from '@/config';
import { Message } from '@alife/next';

export function loginOut() {
  const SSO_TOKEN = localStorage.getItem(SSO_TOKEN);
  logout({ SSO_TOKEN }).then(onRequestSuccess).then(() => {
    const { LOGIN_CALLBACK_PAGE } = config;
    Message.success('退出登录成功');
    location.href = LOGIN_CALLBACK_PAGE;

  }).catch(onRequestError);
}

loginOut()
