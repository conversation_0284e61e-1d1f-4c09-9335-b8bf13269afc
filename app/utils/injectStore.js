import React from 'react';
import hoistNonReactStatics from 'hoist-non-react-statics';
import { ReactReduxContext } from 'react-redux'


export function withStoreContext(WrappedComponent){
  class storeContext extends React.Component {
    static WrappedComponent = WrappedComponent;
    static displayName = `withStore(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

    render() {
      return <ReactReduxContext.Consumer>
        {
          ({store})=> {
            return <WrappedComponent {...this.props} store={store}></WrappedComponent>
          }
        }
      </ReactReduxContext.Consumer>
    }
  }

  return hoistNonReactStatics(storeContext, WrappedComponent);
}
