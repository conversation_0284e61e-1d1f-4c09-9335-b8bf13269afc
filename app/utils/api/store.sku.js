
import { request } from '../request';
import { onRequestSuccess } from './req.trans'
import { safeJSONParse, omit } from '../others';


const requestInstance = request()

export function getBasePoolList(type,isInvite) {
  if (isInvite) {
    return requestInstance.get(`/api/common/invite/queryBasePoolList/${type}`)
  } else {
    return requestInstance.get(`/api/common/queryBasePoolList/${type}`)
  }
}

export function getSkuList(params) {
  return requestInstance.post('/api/pickSku/querySkuList', params)
}

export function getStoreList(params) {
  return requestInstance.post('/api/storePoolCreate/queryTargetStoreList', params)
}

export function getStorePoolList(params) {
  return requestInstance.post('/api/storeManager/queryStoreList', params)
}

export function getSkuPoolList(params) {
  return requestInstance.post('/api/pickSku/querySkuInPool', params)
}

// 全部类目
export function getCategorySku() {
  return requestInstance.get('/api/pickSku/queryAllSkuCategory')
}

// 门店类目
export function getCategoryStore() {
  return requestInstance.get('/api/storePoolCreate/queryAllStoreCategory')
}

// 主营类目
export function getStoreMainCatetories() {
  return requestInstance.get('/api/common/queryAllStoreMainCategory.json')
}

export function getCities() {
  return requestInstance.get('/api/common/queryCityInfo')
}

export function saveAsSkuPool(data) {
  return requestInstance.post('/api/pickSku/saveSkuPool', data)
}

export function saveAsStorePool(data) {
  return requestInstance.post('/api/storeManager/saveStorePool', data)
}


export function getPoolRule({ isStore, id }) {
  if (isStore) return requestInstance.get(`/api/storePoolCreate/queryStorePoolRule/${id}`)
  return requestInstance.get(`/api/pickSku/querySkuPoolRule/${id}`)
}

export function publishPool({ isStore, id }) {
  if (isStore) {
    return requestInstance.post(`/api/storeManager/publishStorePool/${id}`).then(onRequestSuccess)
  }
  return requestInstance.post(`/api/pickSku/publishSkuPool/${id}`).then(onRequestSuccess)
}

export function removeStorePool(id) {
  return requestInstance.post(`/api/storeManager/deleteStorePool/${id}`)
}

export function removeSkuPool(id) {
  return requestInstance.post(`/api/pickSku/deleteSkuPool/${id}`)
}

/**下线操作 */
export function poolOffLine(poolId) {
  return requestInstance.post(`/api/common/poolOffLine/${poolId}`)
}

export function checkProgress({ id, isStore }) {
  if (isStore) return requestInstance.get(`/api/storeManager/getPublishProgress/${id}`)
  return requestInstance.get(`/api/pickSku/getPublishProgress/${id}`)
}

export function getDetail({ id, isStore, query }) {
  const { pagination, ids, operateType, brandName, shopNames } = query;

  let params = {
    page: pagination.page,
    size: pagination.size,
    query: {
      ids,
      brandName,
      shopNames,
      operateType,
    }
  }
  if (isStore) {
    return requestInstance.post(`/api/storeManager/queryStorePoolDetail/${id}`, params)
  }else {
    params.query = {...query};
    return requestInstance.post(`/api/pickSku/querySkuPoolDetail/${id}`, params)
  }
}

export function addItemsToPool({ id, isStore, itemIds, operateTab }) {
  let data = {
    ids: itemIds,
    operateTab
  }
  if (isStore) {
    return requestInstance.post(`/api/storePoolCreate/addStore/${id}`, data)
  }
  return requestInstance.post(`/api/pickSku/addSku/${id}`, data)
}


export function batchDeleteItemsInPool({ id, isStore, itemIds, operateTab }) {
  let data = {
    ids: itemIds,
    operateTab
  }
  if (isStore) {
    return requestInstance.post(`/api/storePoolCreate/deleteStore/${id}`, data).then(onRequestSuccess)
  }

  return requestInstance.post(`/api/pickSku/deleteSku/${id}`, data).then(onRequestSuccess)
}

/**导出文件 */
export function exportFile({ fileName, poolId, isStore }) {
  let data = {
    fileName,
    poolId
  }

  if(isStore){
    return requestInstance.post('/api/common/exportStorePoolContext', data).then(onRequestSuccess)
  }

  return requestInstance.post('/api/common/exportSkuPoolContext', data).then(onRequestSuccess)
}

/**
 * 查看导出进度
 */
export function getExportProgress(data){
  return requestInstance.get(`/api/common/queryExportProgress/${data}`).then(onRequestSuccess)
}


export function getTargetSkuByIds(ids) {
  return requestInstance.post('/api/pickSku/findOutTargetSkuByIds', ids)
}

export function getTargetStoreByIds(ids) {
  return requestInstance.post(`/api/storePoolCreate/findOutTargetStoreByIds`, ids)
}

export function modifyPoolTitle(id, data) {
  return requestInstance.post(`/api/common/updatePoolName/${id}`, data)
}

export function getPoolDetail({isStore, id}){
  let req = requestInstance.post(`/api/pickSku/editSkuPool/${id}`)
  if (isStore){
    req = requestInstance.post(`/api/storeManager/editStorePool/${id}`)
  }

  return req.then(onRequestSuccess).then(resp => {
    let {filterRules} = resp;
    filterRules = omit(safeJSONParse(filterRules), ['basePoolId'])
    return {
      ...resp,
      filterRules
    }
  })
}

// 查询营销信息中的选项列表
export function queryMarketingType() {
  return requestInstance.get(`/api/common/queryMarketingType`)
}

//人群属性中的适用人群列表
export function getCrowdAttributes() {
  return requestInstance.get('/api/common/invite/crowdAttribute')
}

/**编辑选品集 */
export function editPoolResult(params) {
  return requestInstance.post('/api/manual/poolresult', params).then(onRequestSuccess)
}

export function getSynPoolPlatformList(params) {
  return requestInstance.get(`/api/common/synPoolPlatformList1/${params}`)
}

export function validateComponentsPermission(params) {
  return requestInstance.post(`/api/acl/validateComponentsPermission`,params)
}

// 业务归属
export function getManageorg() {
  return requestInstance.get(`/api/dynamic/manageorgv2` );
}

// 业务线
export function getBizLine() {
  return requestInstance.get(`/api/dynamic/bizlinev2`)
}
