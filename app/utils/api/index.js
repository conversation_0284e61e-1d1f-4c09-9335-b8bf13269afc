import { client as yundingClient } from '@/adator/api/yunding';
import { putInReq } from '../request';
import { onRequestSuccess, onRequestError } from './req.trans'

export { onRequestSuccess, onRequestError }
export * from './auth'
export * from './store.sku'
export * from './ali.store.sku'
export * from './permission'
export * from './rankManage'

//获取城市接口
export function getCityList() {
  return putInReq.get('/api/common/cityInfo').then(onRequestSuccess)
}

//获取城市城市对应网格
export function getGridList({ cityId }) {
  return putInReq.get(`/api/putInActivity/listGridByCityId/${cityId}`).then(onRequestSuccess)
}

//根据搜索词搜索 省、市、网格
export function getSearchList({ key }) {
  return putInReq.get(`/api/putInActivity/getGridDetailByKey/${key}`).then(onRequestSuccess)
}

/** 根据poolId获取维度 poolId*/
export function getPoolType(params) {
  return putInReq.post('/api/common/poolType', params).then(onRequestSuccess)
}

/**获取门店分类 */
export function getStoreCategory() {
  return putInReq.get('/api/common/storeCategory').then(onRequestSuccess)
}
// 联盟标签
export function queryEatUnionTag() {
  return putInReq.get('/api/common/queryEatUnionTag').then(onRequestSuccess)
}

// 获取门店经营类型
export function queryStoreMajorCategory() {
  return putInReq.get('/api/common/queryStoreMajorCategory').then(onRequestSuccess)
}


/**获取所有投放渠道 */
export function getAllDeliveryChannel() {
  return putInReq.get('/api/pageManage/queryAllDeliveryChannel').then(onRequestSuccess)
}

/**获取权限 */
export function getPermission() {
  return putInReq.get('/api/v1/acl/menuTree.json').then(onRequestSuccess)
}

export async function getAllMenu() {
  const res = await yundingClient.fetch({
    apiKey:
      "ele-newretail-delivery-platform.AclPermissionService.listAppAllMenuTree",
  });
  return res;
}

/**获取商品品牌 */
export function queryBrand(params) {
  return putInReq.post('/api/common/queryBrandList', params).then(onRequestSuccess)
}

/**获取营销活动类型接口 */
export function queryMarketActivityType(activityType) {
  return putInReq.get(`/api/common/queryMarketActivityType/${activityType}`).then(onRequestSuccess)
}

/**获取AOI */
export function queryAoiList() {
  return putInReq.post('/api/common/queryAoiList').then(onRequestSuccess)
}