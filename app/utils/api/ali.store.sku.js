
import { request } from '../request';
import { onRequestSuccess } from './req.trans'
import { safeJSONParse, omit } from '../others';


const requestInstance = request()

/*start 入淘的接口*/

/*管理商品池-商品池列表*/
export function getSkuPoolList(params, onlyMine) { //dataType  1:淘内   0：淘外    onlyMine 1.'我创建的',2.已申请的,3.可查看的
  let _request = '/api/ali/item/queryItemPoolList';
  if (onlyMine == 2) {
    _request = '/api/ali/item/queryItemSharePassPoolList';
  } else if (onlyMine == 3) {
    _request = '/api/ali/item/queryItemViewablePoolList';
  }
  return requestInstance.post(_request, params)
}

/*管理门店池-门店池列表*/
export function getStorePoolList(params, onlyMine,isInvite) {
  let _request = `/api/ali${isInvite?"/invite":""}/store/queryStorePoolList`;
  if (onlyMine == 2) {
    _request = `/api/ali${isInvite?"/invite":""}/store/queryStoreSharePassPoolList`;
  } else if (onlyMine == 3) {
    _request = `/api/ali${isInvite?"/invite":""}/store/queryStoreViewablePoolList`;
  }
  return requestInstance.post(_request, params)
}

/*删除操作*/
export function removePool(id) {
  return requestInstance.post(`/api/ali/common/deletePool/${id}`)
}

/*导出文件*/
export function exportFile({ fileName, poolId}) {
  let data = {
    fileName,
    poolId
  }
  return requestInstance.post('/api/ali/common/exportPoolContext', data).then(onRequestSuccess);
}

/*查看导出进度*/
export function getExportProgress(data){
  return requestInstance.get(`/api/ali/common/queryExportProgress/${data}`).then(onRequestSuccess)
}

/*编辑选品池*/
export function getPoolDetail({id}){
  let req = requestInstance.post(`/api/ali/common/editPool/${id}`)

  return req.then(onRequestSuccess).then(resp => {
    let {filterRules} = resp;
    filterRules = omit(safeJSONParse(filterRules), ['basePoolId'])
    // 处理标签群组。标签群组ID的前缀I_或者S_只在传给后端时添加，运营不可见。
    // if(filterRules.baseInfo && filterRules.baseInfo.labelGroupIds && filterRules.baseInfo.labelGroupIds.length) {
    //   filterRules.baseInfo.labelGroupIds = filterRules.baseInfo.labelGroupIds.map(idItem => {
    //     if (idItem.indexOf('_') == 0) {
    //       const index = idItem.indexOf('_');
    //       return idItem.slice(index+1);
    //     } else {
    //       return idItem;
    //     }});
    // }
    return {
      ...resp,
      filterRules
    }
  })
}

/*下线操作*/
export function poolOffLine(poolId) {
  return requestInstance.post(`/api/ali/common/offLinePool/${poolId}`)
}

/*查看发布进度*/
export function checkProgress({ id }) {
  return requestInstance.get(`/api/ali/common/getPublishProgress/${id}`)
}

/*发布操作*/
export function publishPool({ id }) {
  return requestInstance.post(`/api/ali/common/publishPool/${id}`).then(onRequestSuccess)
}

/*创建选品池或选店池-添加商品或添加门店*/
export function addItemsToPool({ id, isStore, itemIds, operateTab,isInvite, dataFlag }) {
  let data = {
    ids: itemIds,
    operateTab,
    dataFlag
  }
  if (isStore) {
    return requestInstance.post(`/api/ali${isInvite?"/invite":""}/store/addStore/${id}`, data)
  }
  return requestInstance.post(`/api/ali${isInvite?"/invite":""}/item/addItem/${id}`, data)
}

/*创建商品池/们电池-查询-点击查看按钮*/
export function getDetail({ id, isStore, query,basePoolId,isInvite }) {
  const { pagination, ids, operateType,brandName,resultStoreNames } = query;

  let params = {
    page: pagination.page,
    size: pagination.size,
    query: {
      resultStoreIds:ids,
      operateType,
      resultStoreNames,
      resultBrandNames:brandName
    }
  }
  if (isStore || basePoolId==30004 || basePoolId==31004 ) {
    return requestInstance.post(`/api/ali${isInvite?"/invite":""}/store/queryStorePoolDetail/${id}`, params)
  }else {
    params.query = {...query};
    return requestInstance.post(`/api/ali${isInvite?"/invite":""}/item/queryItemPoolDetail/${id}`, params)
  }
}

/*管理选品池或选店池-批量删除*/
export function batchDeleteItemsInPool({ id, isStore, itemIds, operateTab,isInvite }) {
  let data = {
    ids: itemIds,
    operateTab
  }
  if (isStore) {
    return requestInstance.post(`/api/ali${isInvite?"/invite":""}/store/deleteStore/${id}`, data).then(onRequestSuccess)
  }
  return requestInstance.post(`/api/ali${isInvite?"/invite":""}/item/deleteItem/${id}`, data).then(onRequestSuccess)
}

/*管理选品池-添加商品-输入upc/sku查询商品*/
export function getTargetSkuByIds(ids) {
  return requestInstance.post('/api/ali/item/findOutTargetItemByIds', ids)
}

/*管理选店池-添加商品-输入upc/sku查询商品*/
export function getTargetStoreByIds(ids) {
  return requestInstance.post(`/api/ali/store/findOutTargetStoreByIds`, ids)
}

/*全部类目*/
export function getCategorySku(isInvite) {
  return requestInstance.get(`/api/ali${isInvite?"/invite":""}/item/queryAllSkuCategory`)
}

/*门店类目*/
export function getCategoryStore(isInvite) {
  return requestInstance.get(`/api/ali${isInvite?"/invite":""}/store/queryAllStoreCategory`)
}

/* 创建商品池-查询列表 */
export function getSkuList(params,isInvite) {
  return requestInstance.post(`/api/ali${isInvite?"/invite":""}/item/queryItemList`, params)
}

/* 创建门店池-查询列表 */
export function getStoreList(params,isInvite) {
  return requestInstance.post(`/api/ali${isInvite?"/invite":""}/store/queryStoreList`, params)
}

/*创建商品池-保存接口*/
export function saveAsSkuPool(data,isInvite) {
  return requestInstance.post(`/api/ali${isInvite?"/invite":""}/item/saveItemPool`, data)
}

/*创建门店池-保存接口*/
export function saveAsStorePool(data,isInvite) {
  return requestInstance.post(`/api/ali${isInvite?"/invite":""}/store/saveStorePool`, data)
}

// 查询营销信息中的选项列表
export function queryMarketingType({ id } ) {
  return requestInstance.get(`/api/ali/common/queryActivityType/${id}`)
}

// 查询营销信息中的选项列表
export function queryNewMarketingType() {
  return requestInstance.get('/api/ali/common/queryActivityType/multi')
}

/**获取商品场景属性 */
export function getSceneInfo({ id }) {
  return requestInstance.get(`/api/ali/common/querySceneInfo/${id}`)
}

/**获取商品状态 */
export function getItemStatusOptions() {
  return requestInstance.get('/api/ali/common/queryItemStateEnum')
}

/**数据集上传---下载模板 */
export function downFile() {
  return requestInstance.get('/api/ali/common/download')
}






export const ali = {
  getSkuPoolList,
  getStorePoolList,
  removePool,
  exportFile,
  getExportProgress,
  getPoolDetail,
  poolOffLine,
  checkProgress,
  publishPool,
  addItemsToPool,
  getDetail,
  batchDeleteItemsInPool,
  getTargetSkuByIds,
  getTargetStoreByIds,
  getCategorySku,
  getCategoryStore,
  getSkuList,
  getStoreList,
  saveAsSkuPool,
  saveAsStorePool,
  queryMarketingType,
  queryNewMarketingType,
  getSceneInfo,
  getNewAllStoreMainCategory,
  getItemStatusOptions,
  downFile
}


/*end 入淘的接口*/

export function getBasePoolList(type) {
  return requestInstance.get(`/api/common/queryBasePoolList/${type}`)
}

// 老的主营类目
export function getStoreMainCatetories() {
  return requestInstance.get('/api/common/queryAllStoreMainCategory.json')
}

// 新的主营类目
export function getNewAllStoreMainCategory() {
  return requestInstance.get('/api/common/queryNewAllStoreMainCategory')
}

export function getCities() {
  return requestInstance.get('/api/common/queryCityInfo')
}

export function getPoolRule({ isStore, id }) {
  if (isStore) return requestInstance.get(`/api/storePoolCreate/queryStorePoolRule/${id}`)
  return requestInstance.get(`/api/pickSku/querySkuPoolRule/${id}`)
}


export function removeStorePool(id) {
  return requestInstance.post(`/api/storeManager/deleteStorePool/${id}`)
}

export function removeSkuPool(id) {
  return requestInstance.post(`/api/pickSku/deleteSkuPool/${id}`)
}

export function modifyPoolTitle(id, data) {
  return requestInstance.post(`/api/common/updatePoolName/${id}`, data)
}

/**设置-补全商户基础 */
export function completedGoodsPool(data) {
  return requestInstance.post('/api/manual/item/add',data).then(onRequestSuccess)
}

export function completedStorePool(id) {
  return requestInstance.get(`/api/manual/store/add/${id}`).then(onRequestSuccess)
}

export function extensionMuti(params) {
  return requestInstance.post(`/api/v2/pool/extensionMuti`,params).then(onRequestSuccess)
}
