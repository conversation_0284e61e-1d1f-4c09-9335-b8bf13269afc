import { putInReq } from '../request';
import { onRequestSuccess, onRequestError } from './req.trans'


/*榜单列表*/
export function getRankList(params) {
  return putInReq.post('/api/rank/list', params).then(onRequestSuccess)
}

/*榜单下线*/
export function getRankOffline(params) {
  return putInReq.post('api/rank/offline', params).then(onRequestSuccess)
}

/*榜单下线*/
export function checkRankPool(id) {
  return putInReq.post(`/api/acl/rank/validate/${id}`).then(onRequestSuccess)
}

/*榜单新建*/
export function saveRank(params) {
  return putInReq.post(`/api/rank/save`,params).then(onRequestSuccess)
}


/*榜单详情*/
export function viewRank(params) {
  return putInReq.post(`/api/rank/detail`,params).then(onRequestSuccess)
}
