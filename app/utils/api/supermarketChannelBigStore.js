import { putInReq, request } from "../request";
import { onRequestSuccess } from "./req.trans";

const requestInstance = request()

/*全能超市大店列表*/
export function getBigStoreList(params) {
  return putInReq.post("/api/sceneManager/listSceneCard", params);
}

/*全能超市大店下线*/
export function delMall4Scene(params) {
  return putInReq
    .post("api/sceneManager/delSceneCard", params)
    .then(onRequestSuccess);
}

/*全能超市大店新建*/
export function editMall4Scene(params) {
  return putInReq
    .post(`/api/sceneManager/editMall4Scene`, params)
    .then(onRequestSuccess);
}

/*全能超市大店详情*/
export function getMall4Scene(params) {
  return putInReq
    .post(`/api/sceneManager/getMall4Scene`, params)
    .then(onRequestSuccess);
}

/*全部类目*/
export function getSkuCategory() {
  return requestInstance
    .get(`/api/v2/config/queryAllSkuCategory`)
    .then(onRequestSuccess);
}

/**获取投放门店list*/
export function getPoolInfoSurge (params) {
  return putInReq.post('/api/deliverymanage/getPoolInfoSurge',params).then(onRequestSuccess)
}

/**资源位权限校验**/
export function validateAclResource(params) {
  return putInReq.post('/api/acl/channel/validateAclResource',params).then(onRequestSuccess)
}

/**按群群组id搜索人群标签**/
export function getUserTagGroupIdsList(params) {
  return putInReq.get(`/api/putInActivity/queryRegionGroupByKeyword/${params.keyword}`).then(onRequestSuccess)
}