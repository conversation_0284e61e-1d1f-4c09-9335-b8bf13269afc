import { request, putInReq } from '../request';

const requestInstance = request()


export function login(params) {
  // return requestInstance.get('/sendBucSSOToken.do', { params });
  return putInReq.get('/sendBucSSOToken.do', { params });
}

export function logout(params) {
  // return requestInstance.get('/bucSSOLogout.do', { params });
  return putInReq.get('/bucSSOLogout.do', { params });
}

// 用户权限相关 start
export function getUserInfo() {
  // return requestInstance.get('/api/v1/user/currentUser')
  return putInReq.get('/api/v1/user/currentUser')
}
