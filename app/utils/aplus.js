import React from 'react';
import { isBlank, isObject } from '@/utils/validators';

const SPM_A = 'a2ogi'

class APlus {
  constructor(spma) {
    this.spma = spma
    this.gd_q = window.goldlog_queue || (window.goldlog_queue = [])
  }

  _pushQueue(action, args) {
    this.gd_q.push({ action, arguments: args })
  }

  // 更换 spm(默认发送 pv)
  setSpm(spmb, shouldSendPV = true) {
    this._pushQueue('goldlog.setPageSPM', [this.spma, spmb])
    shouldSendPV && this.pv()
  }

  // 发送 pv
  pv() {
    this._pushQueue('goldlog.sendPV', [{ is_auto: false }])
  }

  // 发送 click 事件
  clickEvent(logKey, goKey = '', method = 'GET') {
    this._pushQueue('goldlog.record', [logKey, 'CLK', goKey, method])
  }

  // 发送 expose 事件
  exposeEvent(logKey, goKey = '', method = 'GET') {
    this._pushQueue('goldlog.record', [logKey, 'EXP', goKey, method])
  }

  // 发送其它事件
  otherEvent(logKey, goKey = '', method = 'GET') {
    this._pushQueue('goldlog.record', [logKey, 'OTHER', goKey, method])
  }
}

export const aplus = new APlus(SPM_A)

//track  打点
export function track(funcName, params) {
  if (!funcName || !aplus[funcName]) throw new Error('Track: invalid funcName')
  aplus[funcName].apply(aplus, params)
}

// goldLog  黄金令箭
export function goldLog(params, method){
  method = method || 'otherEvent';
  track(method, params)
}

/**统计指标的黄金令箭 */
function isNotBlankAndLog(data, key, logKey = '') {
  const isBlankErros = isBlank(data[key]);
  !isBlankErros.length && goldLog([
    logKey,
    // '/selection_kunlun.COMMODITY_POOL_CREATION.commodity-config-other',
    `tagName=${key}`
  ]);
}

export function configTagGoldLog(data, logKey) {
  Object.keys(data).forEach((key) => {
    if(isObject(data[key])) {
      const paramObj = data[key];
      Object.keys(paramObj).forEach(key => {
        isNotBlankAndLog(paramObj, key, logKey)
      })
    } else {
      isNotBlankAndLog(data, key, logKey)
    }
  })
}

/**记录配置时间的componentDecorate */
export function logTimeComponent(Component, logFunc = () => {}){
  return class NewComponent extends React.Component {
    constructor(props){
      super(props);
    }

    componentDidMount(){
      localStorage.setItem('config_start', Date.now());
    }

    componentWillUnmount(){
      localStorage.removeItem('config_start');
    }

    goldLogTimeConsume = (...params) => {
      const configTimeConsume = (Date.now() - localStorage.getItem('config_start')) / 1000;
      logFunc.apply(this, [configTimeConsume, ...params]);
    }

    render(){
      return <Component {...this.props} goldLogTimeConsume = { this.goldLogTimeConsume } />
    }
  }
}
