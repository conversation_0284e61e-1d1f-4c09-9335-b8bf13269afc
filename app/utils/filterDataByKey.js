import { branch, switcher } from './switcher';
import { deepClone } from './others';
import { isArray, isFunction, isString } from './validators'; 


function getData(dataIndex, data) {
  return dataIndex.reduce((value, item) => {
    let res = data[item];
    if(typeof res === 'object'){
      res = deepClone(res);
    }
    value[item] = res
    return value;
  }, {})
}

function formatData(data, funcs){
  return funcs.reduce((val, func) => {
    return func.apply(func, [val]);
  }, data)
}

/**
 * @param 数组或者获取key的函数
 * @param 需要筛选的data
 */
export const filterDataByKey = switcher(
  branch(isArray, 
    (dataIndex, data, afterGet) => {
      return formatData(getData(dataIndex, data), afterGet);
    }),
  branch(isString, (dataIndex, data, ...afterGet) => {
    return formatData(data[dataIndex], ...afterGet);
  }),
  branch(isFunction, (dataIndex, data, afterGet) => {
    const indexAr = dataIndex.apply(dataIndex);
    return formatData(getData(indexAr, data), afterGet);
  })
)

export const filterData = (data) => {
  return function(dataIndexAr, ...after){
    return filterDataByKey(dataIndexAr, data, after);
  }
}
