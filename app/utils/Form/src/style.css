.alsc-schema-form {
  overflow: auto;
}
.alsc-schema-form .fieldset {
  margin-bottom: 20px;
}
.alsc-schema-form .fieldset > h1 {
  padding: 0;
  margin: 0;
  font-size: 14px;
}
.alsc-schema-form .fieldset > p {
  padding: 0;
  margin: 0;
  font-size: 14px;
  color: #666;
}
.alsc-schema-form .fieldset > .legend {
  position: relative;
  height: 24px;
  line-height: 24px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}
.alsc-schema-form .fieldset > .legend > * {
  vertical-align: top;
}
.alsc-schema-form .fieldset > .legend .empty-switch {
  position: relative;
  top: 1px;
  margin-left: 8px;
}
.alsc-schema-form .fieldset > .field-description:not(:last-child) {
  margin-bottom: 16px;
}
.alsc-schema-form .fieldset .next-form-item-label {
  width: 200px;
  min-width: 200px;
  flex: 0 0 auto;
  overflow: hidden;
}
.alsc-schema-form .fieldset .next-form-item-label label {
  display: block;
  white-space: nowrap;
  font-size: 14px;
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #000;
  line-height: 28px;
}
.alsc-schema-form .fieldset .next-form-item-control {
  flex: 1 1 auto;
  max-width: 100%;
}
.alsc-schema-form .fieldset .alsc-form-item-range {
  padding-top: 8px;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper {
  width: 80px;
  height: 80px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool {
  height: 20px;
  display: block;
  font-size: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-download-link,
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-close {
  position: relative;
  display: inline-block;
  width: 50%;
  line-height: 20px;
  text-decoration: none;
  text-align: center;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-download-link::before,
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-close::before {
  position: absolute;
  left: 50%;
  width: 100%;
  content: "删除";
  color: #fff;
  font-size: 14px;
  text-align: center;
  transform: translateX(-50%);
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-download-link
  > i,
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-close
  > i {
  opacity: 0;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-download-link::before {
  content: "查看";
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-wrapper
  .next-upload-tool
  .next-upload-tool-download-link::after {
  content: "";
  position: absolute;
  background: #d8d8d8;
  width: 1px;
  height: 14px;
  right: 0;
  top: 3px;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-list-item
  .next-upload-list-item-name {
  display: none;
}
.alsc-schema-form .fieldset .alsc-form-item-uploader .next-upload-card {
  width: 80px;
  height: 80px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-card
  .next-icon-add {
  color: #000;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-card
  .next-icon-add::before {
  font-size: 12px;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-card
  .next-upload-text {
  margin-top: 4px;
  color: #000;
  font-size: 14px;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-card
  .next-upload-text:focus {
  outline: none;
}
.alsc-schema-form .fieldset .restriction-text {
  font-size: 12px;
  line-height: 1.5;
  color: #999999;
}
.alsc-schema-form .fieldset:not(.level-0) {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}
.alsc-schema-form .fieldset:not(.level-0) > .legend {
  font-size: 16px;
}
.alsc-schema-form .fieldset:empty {
  padding: 0;
  margin: 0;
}
.alsc-schema-form .any-of-field {
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
}
.alsc-schema-form .any-of-field .scene-select {
  margin-bottom: 12px;
}
.alsc-schema-form .one-of-field {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 20px;
}
.alsc-schema-form .one-of-field > .legend {
  padding: 0;
  margin: 0;
  position: relative;
  height: 24px;
  line-height: 24px;
  font-size: 16px;
  font-weight: 500;
}
.alsc-schema-form .one-of-field > .legend > * {
  vertical-align: top;
}
.alsc-schema-form .one-of-field > .legend .empty-switch {
  position: relative;
  top: 1px;
  margin-left: 8px;
}
.alsc-schema-form .one-of-field > .legend span.description {
  margin-left: 12px;
  font-weight: normal;
  font-size: 14px;
  color: #666;
}
.alsc-schema-form .one-of-field > .fieldset {
  padding: 0;
}
.alsc-schema-form .one-of-field > .fieldset > :first-child {
  margin-top: 18px;
}
.alsc-schema-form .array-item {
  background-color: #eaeaea;
}
.alsc-schema-form .array-item .item-title {
  display: flex;
  height: 32px;
  font-size: 14px;
  align-items: center;
  user-select: none;
  cursor: pointer;
  border-bottom: 1px solid #fff;
}
.alsc-schema-form .array-item .item-title .drag-handler .icon {
  cursor: -webkit-grab;
}
.alsc-schema-form .array-item .item-title .icon {
  padding-left: 10px;
  padding-right: 6px;
}
.alsc-schema-form .array-item .item-title .icon::before {
  font-size: 14px;
}
.alsc-schema-form .array-item .item-title .no {
  margin-right: 6px;
}
.alsc-schema-form .array-item .item-title .title {
  flex: 1;
}
.alsc-schema-form .array-item .item-title .action-btn {
  margin-right: 6px;
  opacity: 0;
}
.alsc-schema-form .array-item .item-body {
  overflow: hidden;
  border-bottom: 1px solid #fff;
}
.alsc-schema-form .array-item .item-body .fieldset {
  background: none !important;
}
.alsc-schema-form .array-item .item-body .fieldset:last-of-type {
  margin-bottom: 0;
}
.alsc-schema-form .array-item .item-body .any-of-field {
  margin: 12px;
}
.alsc-schema-form .array-item .item-body .any-of-field .fieldset {
  padding: 0;
}
.alsc-schema-form .array-item.collapsed .item-body {
  display: none;
  border-bottom: 0;
}
.alsc-schema-form .array-item.collapsed:hover {
  background-color: #ddd;
}
.alsc-schema-form .array-item.dragging {
  border: 1px dashed #666;
  background: #f4f4f4;
  margin-top: -2px;
}
.alsc-schema-form .array-item.dragging .item-title {
  visibility: hidden;
}
.alsc-schema-form .array-item:hover .action-btn {
  opacity: 1;
}
.alsc-schema-form .array-item:nth-last-child(2).collapsed .item-title {
  border-bottom: 0;
}
.alsc-schema-form .array-item:nth-last-child(2) .item-body {
  border-bottom: 0;
}
.alsc-schema-form .array-item.plain .item-title {
  height: auto;
}
.alsc-schema-form .array-item.plain .item-title .title {
  flex: 0 0 auto;
  padding-right: 12px;
  width: 120px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.alsc-schema-form .array-item.plain .item-title .inline-control {
  flex: 1;
  padding-top: 16px;
  padding-right: 4px;
}
.alsc-schema-form .array-item.plain:hover {
  background-color: #eaeaea;
}
.alsc-schema-form .array-item.static .item-title {
  padding-left: 32px;
  padding-right: 12px;
}
.alsc-schema-form .add-btn-wrapper {
  margin-top: 12px;
}
.alsc-schema-form .add-btn-wrapper .add-btn {
  display: block;
}
.alsc-schema-form .add-btn-wrapper:only-child {
  margin-top: 0;
}
.alsc-schema-form .alsc-form-select,
.alsc-schema-form .alsc-form-date-picker {
  display: block !important;
  width: 100% !important;
}
.alsc-schema-form .alsc-form-select-popup .next-menu-item-inner {
  text-align: left;
}
.alsc-schema-form
  .alsc-form-select-popup
  .next-menu-item-inner
  .next-icon.next-icon-select {
  right: 16px;
}
.alsc-schema-form .range-view {
  padding-top: 6px;
  text-align: center;
  color: #666;
  font-weight: bold;
}
.alsc-schema-form .number-picker {
  width: 130px;
}
.alsc-schema-form .nested-item + :not(.nested-item) {
  margin-top: 24px;
}
.alsc-schema-form .nested-item {
  position: relative;
  padding: 12px;
  padding-bottom: 0;
  overflow: hidden;
}
.alsc-schema-form .nested-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
}
.alsc-schema-form .nested-item > div {
  position: relative;
}
.alsc-schema-form .next-form-item-help {
  color: #777;
}
.alsc-schema-form .mod-widget > .message {
  display: block;
  white-space: nowrap;
  line-height: 28px;
  font-size: 14px;
  font-weight: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #777;
}
.alsc-schema-form .nested-form-item {
  margin-bottom: 0;
}
.alsc-schema-form .color-widget {
  position: relative;
  padding: 1px;
  height: 28px;
  width: 200px;
  border: 1px solid #c4c6cf;
  border-radius: 3px;
  background: #fff;
  font-size: 12px;
  cursor: pointer;
}
.alsc-schema-form .color-widget.disabled {
  cursor: not-allowed;
}
.alsc-schema-form .color-widget > span {
  position: relative;
  top: -1px;
  padding-left: 6px;
  color: #333;
  font-size: 12px;
  vertical-align: middle;
}
.alsc-schema-form .color-widget .pickr {
  display: inline-block;
  width: 24px;
}
.alsc-schema-form .color-widget .pickr .pcr-button {
  box-shadow: none !important;
}
.alsc-schema-form .color-widget .pickr .pcr-button.clear {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAAGXcA1uAAAABGdBTUEAALGPC/xhBQAAANFJREFUSA3tk8ERwiAQAIMvKECbsAr/8KRKirAJbUILgB96zFyGEAhHIA9n5JEccHsbCExSSj/FrT4QZ2fiVYE0h5agtT6nJPRP8ChNhon+SQaOuGFJBoG19iWEuBhj3nFSLp4rUcEZwGo1cAXUwCJQAqtACk5KqQcOUt7hn7ZCwfKHvvsLm/AjG0E+S7geBoH3/soYe3LOb5RbRzlzmBO+CC6Zc+5+hGix5CNECwEua6QoKxgp2hSMEJEEPaImwR7RLkGLqEtAEQ0RbImGCnKiDzH27FBK9/SQAAAAAElFTkSuQmCC");
  background-size: 100%;
}
.alsc-schema-form .object-switch-field {
  position: relative;
  border-radius: 4px 4px 0 0;
  padding: 20px;
  padding-bottom: 0;
  overflow: hidden;
}
.alsc-schema-form .object-switch-field::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
}
.alsc-schema-form .object-switch-field > .next-form-item {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.alsc-schema-form .object-switch-field > .next-form-item > .next-col-6 {
  width: initial;
  min-width: initial;
}
.alsc-schema-form .object-switch-field > .next-form-item > .next-col-6 label {
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
}
.alsc-schema-form .object-switch-field > .next-form-item > .next-col-8 {
  width: initial;
  min-width: initial;
  height: 26px;
}
.alsc-schema-form .object-switch-field.nested-item::before {
  background: rgba(0, 0, 0, 0.05);
}
.alsc-schema-form .object-switch-field ~ .nested-item {
  padding-top: 0;
}
.alsc-schema-form .object-switch-field ~ .nested-item::before {
  background: rgba(0, 0, 0, 0.02);
}
.alsc-schema-form .object-switch-field ~ .nested-item .one-of-field {
  margin-bottom: 12px;
}
.alsc-schema-form .object-switch-field + .nested-item .one-of-field {
  margin-top: 12px;
}
.pcr-app {
  font-size: 16px;
}
.pcr-app.opacity-disabled .pcr-color-opacity {
  display: flex !important;
  cursor: not-allowed !important;
  opacity: 0.5;
}
.pcr-app.opacity-disabled .pcr-color-opacity .pcr-picker {
  left: calc(100% - 9px) !important;
}
.pcr-app .pcr-interaction {
  font-size: 12px;
}
.range-time-picker .separator {
  display: inline-block;
  padding: 0 6px;
}
.add-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 60px;
  margin-bottom: 16px;
}
.add-btn:hover {
  cursor: pointer;
}
.add-btn .add-icon {
  height: 16px;
  width: 16px;
  background: url("./add.png") center no-repeat;
  background-size: cover;
}
.btn-title {
  color: #ff7c4d;
  display: inline-block;
  padding-left: 4px;
}
.remove-item {
  color: #ff7c4d;
  line-height: 11px;
  text-align: center;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
  margin-left: 16px;
  cursor: pointer;
}
.remove-item .remove-icon {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: url("./remove.png") center no-repeat;
  background-size: cover;
}
.array-item-list .array-item {
  background: transparent;
  display: flex;
  flex-direction: row;
}
.array-item-list .array-item .item-body {
  border: none;
}
.array-item-list .fieldset {
  padding: 0 !important;
}
.array-item-list .next-form-item-label {
  width: 0 !important;
  min-width: 0 !important;
  padding: 0 !important;
}
.alsc-schema-form .fieldset .next-form-item-label label {
  color: #666666;
}
.alsc-schema-form .fieldset .next-radio-label label {
  color: #333333;
}
.alsc-schema-form .fieldset .alsc-form-item-uploader .next-upload-card {
  width: 94px;
  height: 94px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-card
  .next-upload-text {
  display: none;
}
.next-upload:hover {
  color: #ff7c4d;
  border-color: #ff7c4d;
}
.alsc-schema-form
  .fieldset
  .alsc-form-item-uploader
  .next-upload-card
  .next-icon-add::before {
  font-size: 42px;
  height: 80px;
  width: 80px;
  line-height: 80px;
  font-weight: 200;
  color: #999999;
}
.nested-form-item .next-form-item-control {
  /*display: flex;*/
  /*flex-direction: row;*/
}
.nested-form-item .next-form-item-control .restriction-text {
  margin-left: 16px;
}
.nested-form-item .next-form-item-control .restriction-text .restrict-item {
  margin-bottom: 8px;
}
.selection-form .next-checkbox-group-ver .next-checkbox-wrapper {
  margin-right: 16px;
}
.selection-form .next-checkbox-group,
.selection-form .next-radio-group {
  display: flex;
}
.dayOfWeek-widget .next-checkbox-wrapper {
  margin-right: 26px;
}
.dayOfWeek-widget .next-checkbox-wrapper.checked .next-checkbox-inner {
  border-color: transparent;
  background-color: #ff7c4d;
}
.dayOfWeek-widget
  .next-checkbox-wrapper.indeterminate:not(.disabled):hover
  .next-checkbox-inner,
.dayOfWeek-widget
  .next-checkbox-wrapper.indeterminate:not(.disabled).hovered
  .next-checkbox-inner,
.dayOfWeek-widget
  .next-checkbox-wrapper.indeterminate.focused
  .next-checkbox-inner,
.dayOfWeek-widget
  .next-checkbox-wrapper.checked:not(.disabled):hover
  .next-checkbox-inner,
.dayOfWeek-widget
  .next-checkbox-wrapper.checked:not(.disabled).hovered
  .next-checkbox-inner,
.dayOfWeek-widget .next-checkbox-wrapper.checked.focused .next-checkbox-inner {
  border-color: transparent;
  background-color: #ff5d2d;
}
.dayOfWeek-widget
  .next-checkbox-wrapper:not(.disabled):hover
  .next-checkbox-inner,
.dayOfWeek-widget .next-checkbox-wrapper.hovered .next-checkbox-inner,
.dayOfWeek-widget .next-checkbox-wrapper.focused .next-checkbox-inner {
  border-color: #ff7c4d;
  background-color: #ffffff;
}
.red-pack-text-widget{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.red-pack-text-widget .seperator{
  margin: 0 8px;
}

.activity-and-benefit-widget{
  display: flex;
  flex-direction: row;
  align-items: center;
}

.activity-and-benefit-widget .seperator {
  margin: 0 8px;
}
