import PropTypes from 'prop-types';

export const registry = PropTypes.shape({
  FieldTemplate: PropTypes.func,
  ArrayFieldTemplate: PropTypes.func,
  ObjectFieldTemplate: PropTypes.func,
  definitions: PropTypes.object.isRequired,
  fields: PropTypes.objectOf(PropTypes.func).isRequired,
  formContext: PropTypes.object.isRequired,
  widgets: PropTypes.objectOf(
    PropTypes.oneOfType([PropTypes.func, PropTypes.object])
  ).isRequired,
});

export const fieldProps = {
  disabled: PropTypes.bool,
  errorSchema: PropTypes.object,
  formData: PropTypes.any,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  onChange: PropTypes.func.isRequired,
  onFieldError: PropTypes.func,
  onFieldSuccess: PropTypes.func,
  readonly: PropTypes.bool,
  registry: registry.isRequired,
  required: PropTypes.bool,
  schema: PropTypes.object.isRequired,
  jsonPath: PropTypes.string,
  widgetParam: PropTypes.any,
  evaOptions: PropTypes.object,
};
