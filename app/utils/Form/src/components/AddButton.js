import React from 'react';
import { bool, func } from 'prop-types';
import { Button, Icon } from '@alife/next';

export default function AddButton({ disabled, onClick }) {
  if (disabled) {
    return null;
  }
  return (
    <div className="add-btn" onClick={onClick}>
      <div className="add-icon"/>
      <span className="btn-title">新增</span>
    </div>
  );
}

AddButton.propTypes = {
  disabled: bool,
  onClick: func,
};
