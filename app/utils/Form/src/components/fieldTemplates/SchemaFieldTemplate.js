import React from 'react';
import { string, bool, object, node, element, oneOfType, arrayOf } from 'prop-types';
import { Form } from '@alife/next';

function SchemaFieldTemplate(props) {
  const {
    className,
    label,
    schema,
    children,
    errors,
    help,
    description,
    hidden,
    required,
    noLabel,
  } = props;

  if (hidden) {
    return <div className="hidden">{children}</div>;
  }

  const types = [].concat(schema.type);

  if (types.indexOf('object') !== -1) {
    return children;
  }

  if (types.indexOf('array') !== -1) {
    return children;
  }

  const hasError = errors && errors.length > 0;

  return (
    <Form.Item
      className={className}
      label={noLabel ? '' : label}
      validateState={hasError ? 'error' : 'success'}
      hasFeedback={hasError}
      autoValidate={false}
      help={(errors && errors[0]) || help || description}
      required={required}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      title={noLabel ? '' : label}
    >
      <div>{children}</div>
    </Form.Item>
  );
}

SchemaFieldTemplate.propTypes = {
  className: string,
  schema: object,
  type: string,
  noLabel: bool,
  label: string,
  children: node.isRequired,
  errors: arrayOf(string),
  help: oneOfType([string, element]),
  description: oneOfType([string, element]),
  hidden: bool,
  required: bool,
};

SchemaFieldTemplate.defaultProps = {
  hidden: false,
  required: false,
};

export default SchemaFieldTemplate;
