import React from 'react';
import { string, number, bool, any, array, func } from 'prop-types';
import classNames from 'classnames';
import ArrayItem from './ArrayItem';
import AddButton from '../AddButton';

function NormalArrayFieldTemplate(props) {
  return (
    <div className={
      classNames({
        "array-item-list-block-show-label": props.showLabel
      }, 'next-row',)
    }>
      <div dir="ltr" role="gridcell" className="next-col next-col-6 next-form-item-label">
        {props.title && (
          <label required={props.required}>{props.title}</label>
        )}
      </div>
      <div className={
        classNames({
          "array-item-list": !props.showLabel

        }, 'next-col next-col-16',)
      }>
        {props.items && props.items.map((p, index) => {

          return <ArrayItem {...p} listNo={`#${index+1}`} hasListNo={props.showListNo} totalLength={props.items.length} hasBorder={props.showLabel}/>;
        })}
        <AddButton
          className="array-item-add"
          onClick={props.onAddClick}
          disabled={!props.canAdd || props.disabled || props.readonly}
        />
        {(props.required && props.items.length == 0) && <span className='next-form-item-help' style={{color: '#FF2D4B'}}>此项必填</span>}
      </div>
    </div>
  );
}

NormalArrayFieldTemplate.propTypes = {
  TitleField: any,
  DescriptionField: any,
  level: number,
  title: string,
  description: string,
  items: array,
  required: bool,
  canAdd: bool,
  disabled: bool,
  readonly: bool,
  onAddClick: func,
  showLabel: bool,
};

export default NormalArrayFieldTemplate;
