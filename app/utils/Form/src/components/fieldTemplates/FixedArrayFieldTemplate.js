import React from 'react';
import { string, number, bool, object, any, array, func } from 'prop-types';
import classNames from 'classnames';
import ArrayItem from './ArrayItem';
import AddButton from '../AddButton';

function FixedArrayFieldTemplate(props) {
  const { TitleField, DescriptionField, level } = props;
  return (
    <div className={classNames('fieldset', `level-${level}`)}>
      {props.title && (
        <TitleField title={props.title} required={props.required} />
      )}
      {props.description && (
        <DescriptionField
          description={props.description}
          useMessageBlock={level === 0}
        />
      )}
      <div className="array-item-list" key={`array-item-list-${props.$id}`}>
        {props.items && props.items.map((p) => <ArrayItem {...p} />)}
      </div>

      {props.canAdd && (
        <AddButton
          className="array-item-add"
          onClick={props.onAddClick}
          disabled={props.disabled || props.readonly}
        />
      )}
    </div>
  );
}

FixedArrayFieldTemplate.propTypes = {
  $id: string,
  TitleField: any,
  DescriptionField: any,
  level: number,
  title: string,
  description: string,
  items: array,
  required: bool,
  canAdd: object,
  disabled: bool,
  readonly: bool,
  onAddClick: func,
};

export default FixedArrayFieldTemplate;
