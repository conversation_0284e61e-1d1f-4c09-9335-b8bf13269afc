import React from 'react';
import { string, number, bool, object, any, array, func } from 'prop-types';
import classNames from 'classnames';

function ObjectFieldTemplate(props) {
  const { schema, formData, TitleField, DescriptionField, level, toggleNullValue } = props;

  const nullable = [].concat(schema.type).indexOf('null') !== -1;

  return (
    <div className={classNames('fieldset', `level-${level}`)}>

      {props.title && (
        <TitleField
          title={props.title}
          disabled={props.disabled}
          required={props.required}
          nullable={nullable}
          nullValue={!!formData}
          toggleNullValue={toggleNullValue}
        />
      )}
      {props.description && (
        <DescriptionField
          description={props.description}
          useMessageBlock={level === 0}
        />
      )}
      {
        (nullable && formData === null) ||
        props.properties.map((prop) => prop.content)
      }
    </div>
  );
}

ObjectFieldTemplate.propTypes = {
  schema: object,
  formData: any,
  TitleField: any,
  DescriptionField: any,
  level: number,
  title: string,
  description: string,
  required: bool,
  disabled: bool,
  properties: array,
  toggleNullValue: func,
};

export default ObjectFieldTemplate;
