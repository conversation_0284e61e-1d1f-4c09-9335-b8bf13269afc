import React from 'react';
import { string, number, bool, func, any, object } from 'prop-types';
import classNames from 'classnames';
import { useDrag, useDrop } from 'react-dnd';
import { Icon, Button } from '@alife/next';

const Types = {
  ITEM: 'item',
};

const ArrayItem = ({
  className,
  children,
  collapsed,
  removeItem,
  toggleItem,
  moveItem,
  summaryTitle,
  index,
  $id,
  schema,
  hasRemove,
  hasMoveUp,
  hasMoveDown,
  hasBorder,
  listNo,
  hasListNo,
  totalLength
}) => {
  const {hasDrag = false} = schema;
  const [{ isDragging }, drag, preview] = useDrag({
    item: { type: Types.ITEM, $id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: Types.ITEM,
    hover({ $id: draggedId }) {
      if (draggedId !== $id) {
        moveItem(draggedId, $id);
      }
    },
  });

  const isPlain = schema.type !== 'object' && schema.type !== 'array';
  const hasMove = (hasMoveUp || hasMoveDown) && hasDrag && totalLength >= 1;

  const wrapperClassNames = classNames(
    className,
    collapsed && 'collapsed',
    hasMove || 'static',
    hasBorder && 'has-border',
  );

  const buttonStyleOverride = {
    flexShrink: 0,
    alignSelf: schema.type === 'object' ? 'flex-start' : undefined,
    marginTop: schema.type === 'object' ? '10px' : undefined,
    marginLeft: schema.type === 'object' ? '0px' : undefined
  }

  return (
    <div key={$id} className={wrapperClassNames} ref={hasMove ? drop : null}>
      {hasListNo ?
        <div style={{ padding: '6px 6px 12px' }}>{listNo}</div> :
        null}
      <div
        className={`item-title ${!hasDrag?'item-title-nodrag':''}`}
        ref={hasMove ? preview : null}
      >
        {hasMove && (
          <div className="drag-handler" ref={drag}>
            <Icon className="icon" type="sorting" />
          </div>
        )}
        {/*<div className="no">{index + 1}. </div>*/}
        {/*{(!isPlain || title) && <div className="title">{title}</div>}*/}

        {/*{isPlain && <div className="inline-control">{children}</div>}*/}

        {/*{hasRemove && (*/}
        {/*  <Button*/}
        {/*    className="action-btn"*/}
        {/*    type="primary"*/}
        {/*    text*/}
        {/*    onClick={removeItem(index)}*/}
        {/*  >*/}
        {/*    删除*/}
        {/*  </Button>*/}
        {/*)}*/}
        {!isPlain && <div className="item-body">{children}</div>}
        {isPlain && <div className="inline-control">{children}</div>}
        {hasRemove && <div className='remove-item' style={buttonStyleOverride} onClick={removeItem(index)}><div className="remove-icon"/><span className="btn-title">删除</span></div>}

        {!isPlain ? (
          <div className="remove-item" style={buttonStyleOverride} onClick={toggleItem}>
            <div className="remove-icon" />
            <span className="btn-title">{collapsed ? '展开' : '收起'}</span>
          </div>
        ) : null}
      </div>
    </div>
  );
};

ArrayItem.propTypes = {
  className: string,
  children: any,
  collapsed: bool,
  removeItem: func,
  toggleItem: func,
  moveItem: func,
  summaryTitle: string,
  schema: object,
  hasRemove: bool,
  hasMoveUp: bool,
  hasMoveDown: bool,
  $id: string,
  index: number,
};

export default ArrayItem;
