import React from 'react';
import PropTypes from 'prop-types';

function UnsupportedField({ schema, reason }) {
  return (
    <div className="unsupported-field">
      <p>
        Unsupported field schema
        {reason && <em>: {reason}</em>}.
      </p>
      {schema && <pre>{JSON.stringify(schema, null, 2)}</pre>}
    </div>
  );
}

UnsupportedField.propTypes = {
  schema: PropTypes.object.isRequired,
  reason: PropTypes.string,
};

export default UnsupportedField;
