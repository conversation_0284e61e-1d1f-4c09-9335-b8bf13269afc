import Array<PERSON>ield from './ArrayField';
import Boolean<PERSON>ield from './BooleanField';
import Description<PERSON>ield from './DescriptionField';
import OneOfField from './OneOfField';
import MultiSchemaField from './MultiSchemaField';
import NumberField from './NumberField';
import ObjectField from './ObjectField';
import SchemaField from './SchemaField';
import StringField from './StringField';
import AtomField from './AtomField';
import TitleField from './TitleField';
import NullField from './NullField';
import UnsupportedField from './UnsupportedField';

export default {
  AnyOfField: MultiSchemaField,
  ArrayField,
  BooleanField,
  DescriptionField,
  AtomField,
  NumberField,
  ObjectField,
  OneOfField,
  SchemaField,
  StringField,
  TitleField,
  NullField,
  UnsupportedField,
};
