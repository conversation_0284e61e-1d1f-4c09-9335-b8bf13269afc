import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import { init, loadWidget } from '@ali/alsc-mod-widget-engine';

import * as types from '../../types';

import {
  retrieveSchema,
  getDefaultRegistry,
  evaluateUiOptions,
} from '../../utils';

init({ React, ReactDOM });

class ModWidgetField extends Component {
  static defaultProps = {
    formData: {},
    errorSchema: {},
    required: false,
    disabled: false,
    readonly: false,
  };

  state = {};

  static getDerivedStateFromError(err) {
    console.error('[Mod Widget] Runtime Error');
    console.error(err);
    return {
      loading: false,
      innerError: err,
      errorMessage: '插件异常，请联系插件开发者',
    };
  }

  state = {
    loading: true,
    errorMessage: '',
    Widget: null,
  }

  constructor(props) {
    super(props);
    if (!props.formContext.fieldWidgetMap) {
      this.state.loading = false;
      this.state.errorMessage = '插件初始化中，请稍后...';
    } else if (!this.widget) {
      this.state.loading = false;
      this.state.errorMessage = `没有找到插件 ${this.widgetName}`;
    } else if (
      !this.widget.versions ||
      !this.version
    ) {
      this.state.loading = false;
      this.state.errorMessage = `插件 ${this.widgetName} 未在 ${this.defEnv} 发布版本`;
    }
  }

  get defEnv() {
    const { formContext: { defEnv } } = this.props;
    return defEnv || 'publish';
  }

  get widgetName() {
    const { schema } = this.props;
    return schema['x-ui-field'].split('/')[1];
  }

  get version() {
    const { versions = [] } = this.widget || {};
    return (versions[0] || {}).version;
  }

  get widget() {
    const { formContext: { fieldWidgetMap = {} } } = this.props;
    return fieldWidgetMap[this.widgetName];
  }

  loadWidget = () => {
    if (this.widget && this.version) {
      this.setState({ loading: true, errorMessage: '' });
      loadWidget(
        this.widgetName,
        this.version,
        this.defEnv
      ).then((Widget) => {
        console.info('[Mod Widget] Loaded', this.widgetName, this.version);
        this.setState({ loading: false, errorMessage: '', Widget });
      }).catch((err) => {
        console.error('[Mod Widget] Load Error', this.widgetName, this.version, err);
        this.setState({ loading: false, errorMessage: '插件加载异常，请稍后刷新页面再试' });
      });
    }
  }

  isRequired(name) {
    const { schema } = this.props;
    return (
      Array.isArray(schema.required) && schema.required.indexOf(name) !== -1
    );
  }

  onPropertyChange = (name) => {
    return (value, errorSchema, skipValidate) => {
      const newFormData = { ...this.props.formData, [name]: value };
      this.props.onChange(
        newFormData,
        errorSchema &&
          this.props.errorSchema && {
          ...this.props.errorSchema,
          [name]: errorSchema,
        },
        skipValidate,
      );
    };
  };

  componentDidMount() {
    this.loadWidget();
  }

  componentDidUpdate() {
    if (!this.state.loading && !this.state.Widget) {
      this.loadWidget();
    }
  }

  renderField = (itemName, CustomFieldComponent, customProps) => {
    const {
      schema,
      formData,
      errorSchema,
      disabled,
      readonly,
      onFieldError,
      onFieldSuccess,
      registry = getDefaultRegistry(),
      level,
      onBlur,
      onFocus,
      jsonPath,
    } = this.props;
    const { SchemaField } = registry.fields;
    const itemSchema = schema.properties[itemName];
    const evaOpt = evaluateUiOptions(formData || {}, itemSchema);
    const subJsonPath = `${jsonPath}['${itemName}']`;
    const schemaField = (
      <SchemaField
        key={itemName}
        name={itemName}
        required={this.isRequired(itemName)}
        schema={schema.properties[itemName]}
        errorSchema={errorSchema[itemName]}
        formData={(formData || {})[itemName]}
        onChange={this.onPropertyChange(itemName)}
        onBlur={onBlur}
        onFocus={onFocus}
        onFieldError={onFieldError}
        onFieldSuccess={onFieldSuccess}
        registry={registry}
        disabled={disabled || evaOpt.disabled}
        readonly={readonly || evaOpt.readonly}
        onDropPropertyClick={this.onDropPropertyClick}
        level={level + 1}
        jsonPath={subJsonPath}
        CustomFieldComponent={CustomFieldComponent}
        customProps={customProps}
      />
    );
    const withWrapper = (
      schema.properties[itemName]['x-ui-className'] ||
      schema.properties[itemName]['x-ui-style']
    ) ?
      (
        <section
          key={itemName}
          className={schema.properties[itemName]['x-ui-className']}
          style={schema.properties[itemName]['x-ui-style']}
        >
          {schemaField}
        </section>
      ) : schemaField;
    return withWrapper;
  }

  renderMain = () => {
    const { loading, errorMessage, innerError, Widget } = this.state;
    if (loading) {
      return (
        <div className="message">加载中...</div>
      );
    }
    if (errorMessage) {
      if (innerError) {
        return (
          <div className="message">
            插件异常，请联系插件开发者 {this.widget.versions[0].nick}
          </div>
        );
      }
      return (
        <div className="message">
          {errorMessage}
        </div>
      );
    }
    if (Widget) {
      const {
        formData,
        errorSchema,
        required,
        disabled,
        readonly,
        onFieldError,
        onFieldSuccess,
        registry = getDefaultRegistry(),
        level,
        onChange
      } = this.props;
      const { definitions, formContext = {} } = registry;
      const schema = retrieveSchema(this.props.schema, definitions, formData);
      const { widgetExtData, defEnv } = formContext;
      const title = schema.title === undefined ? name : schema.title;
      const { description } = schema;

      return (
        <Widget
          slot="schema-form-field"
          schema={schema}
          value={formData}
          formData={formData}
          onChange={onChange}
          onFieldSuccess={onFieldSuccess}
          onFieldError={onFieldError}
          readonly={readonly}
          disabled={disabled}
          required={required}
          registry={registry}
          errorSchema={errorSchema}
          level={level}
          title={title}
          description={description}
          renderField={this.renderField}
          extData={{
            widgetExtData,
            defEnv,
          }}
        />
      );
    }
  }

  render() {
    const { level } = this.props;
    return (
      <div className={`fieldset level-${level} field-widget`}>
        {this.renderMain()}
      </div>
    );
  }
}

ModWidgetField.propTypes = types.fieldProps;

export default ModWidgetField;
