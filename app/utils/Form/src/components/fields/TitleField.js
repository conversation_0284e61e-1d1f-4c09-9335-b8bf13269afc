import React from 'react';
import PropTypes from 'prop-types';
import { Switch } from '@alife/next';

const REQUIRED_FIELD_SYMBOL = '*';

function TitleField(props) {
  const { title, required, disabled, nullable, nullValue, toggleNullValue, children } = props;
  return (
    <h1 className="legend">
      {title}
      {required && <span className="required">{REQUIRED_FIELD_SYMBOL}</span>}
      {
        nullable &&
        <Switch
          className="empty-switch"
          size="small"
          disabled={disabled}
          checked={nullValue}
          onChange={toggleNullValue}
        />
      }
      {children}
    </h1>
  );
}

TitleField.propTypes = {
  title: PropTypes.string,
  required: PropTypes.bool,
  nullable: PropTypes.bool,
  nullValue: PropTypes.bool,
  disabled: PropTypes.bool,
  toggleNullValue: PropTypes.func,
  children: PropTypes.any,
};

export default TitleField;
