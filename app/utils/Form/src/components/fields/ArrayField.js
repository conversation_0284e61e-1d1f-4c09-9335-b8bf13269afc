import React, { Component } from 'react';
import { Form } from '@alife/next';
import { DndProvider } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';

import includes from 'core-js/library/fn/array/includes';
import * as types from '../../types';
import {
  getSummaryTitle,
  getWidget,
  getDefaultFormState,
  getUiOptionsFromSchema,
  isMultiSelect,
  isFilesArray,
  isUploadsArray,
  isFixedItems,
  allowAdditionalItems,
  optionsList,
  retrieveSchema,
  getDefaultRegistry,
  isSelect
} from '../../utils';

import UnsupportedField from './UnsupportedField';
import NormalArrayFieldTemplate from '../fieldTemplates/NormalArrayFieldTemplate';
import FixedArrayFieldTemplate from '../fieldTemplates/FixedArrayFieldTemplate';

import shortid from 'shortid';

function generateRowId() {
  return shortid.generate();
}

function generateKeyedFormData(formData) {
  return !Array.isArray(formData)
    ? []
    : formData.map((item) => {
      return {
        key: generateRowId(),
        item,
      };
    });
}

function keyedToPlainFormData(keyedFormData) {
  return keyedFormData.map((keyedItem) => keyedItem.item);
}

class ArrayField extends Component {
  static defaultProps = {
    formData: [],
    required: false,
    disabled: false,
    readonly: false,
    jsonPath: '$',
  };

  constructor(props) {
    super(props);
    const { formData } = props;
    this.state = {
      prevFormData: formData,
      keyedFormData: generateKeyedFormData(formData),
      expandedCollection: {},
    };
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    const nextFormData = nextProps.formData || [];
    const { prevFormData, keyedFormData } = prevState;
    if (nextFormData === prevFormData) {
      return null;
    }
    const newKeyedFormData =
      nextFormData.length === keyedFormData.length
        ? keyedFormData.map((previousKeyedFormDatum, index) => {
          return {
            key: previousKeyedFormDatum.key,
            item: nextFormData[index],
          };
        })
        : generateKeyedFormData(nextFormData);
    return {
      keyedFormData: newKeyedFormData,
      prevFormData: nextFormData,
    };
  }

  get itemTitle() {
    const { schema } = this.props;
    return schema.items.title || schema.items.description || 'Item';
  }

  isItemRequired(itemSchema) {
    if (Array.isArray(itemSchema.type)) {
      // While we don't yet support composite/nullable jsonschema types, it's
      // future-proof to check for requirement against these.
      return !includes(itemSchema.type, 'null');
    }
    // All non-null array item types are inherently required by design
    return itemSchema.type !== 'null';
  }

  canAddItem(formItems) {
    const { schema } = this.props;
    let { addable } = getUiOptionsFromSchema(schema);
    if (addable !== false) {
      // if x-ui-options.addable was not explicitly set to false, we can add
      // another item if we have not exceeded maxItems yet
      if (schema.maxItems !== undefined) {
        addable = formItems.length < schema.maxItems;
      } else {
        addable = true;
      }
    }
    return addable;
  }

  onAddClick = (event) => {
    event.preventDefault();
    const { schema, registry = getDefaultRegistry(), onChange, onFieldSuccess, jsonPath } = this.props;
    const { definitions } = registry;
    let itemSchema = schema.items;
    if (isFixedItems(schema) && allowAdditionalItems(schema)) {
      itemSchema = schema.additionalItems;
    }
    const newFormDataRow = getDefaultFormState(
      itemSchema,
      undefined,
      definitions
    );
    const key = generateRowId();
    const newKeyedFormData = [
      ...this.state.keyedFormData,
      {
        key,
        item: newFormDataRow || "",
      },
    ];

    this.setState(
      {
        keyedFormData: newKeyedFormData,
      },
      () => {
        onChange(keyedToPlainFormData(newKeyedFormData));
        //this.toggleArrayItem(key)();
        onFieldSuccess(jsonPath);
      }
    );
  };

  removeItem = (index) => {
    return (event) => {
      if (event) {
        event.preventDefault();
      }
      const { onChange, onFieldError, jsonPath, onFieldSuccess } = this.props;
      const { keyedFormData } = this.state;
      // refs #195: revalidate to ensure properly reindexing errors
      let newErrorSchema;
      if (this.props.errorSchema) {
        newErrorSchema = {};
        const { errorSchema } = this.props;
        // eslint-disable-next-line guard-for-in
        for (let i in errorSchema) {
          i = parseInt(i, 10);
          if (i < index) {
            newErrorSchema[i] = errorSchema[i];
          } else if (i > index) {
            newErrorSchema[i - 1] = errorSchema[i];
          }
        }
      }
      const newKeyedFormData = keyedFormData.filter((_, i) => i !== index);
      this.setState(
        {
          keyedFormData: newKeyedFormData,
        },
        () => {
          onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema);
          onFieldSuccess(jsonPath);
          // if(!newKeyedFormData.length) {
          //   onFieldError(jsonPath, { validateState: 'error', help: '此项必填' });
          // } else {
          //   onFieldSuccess(jsonPath);
          // }
        }
      );
    };
  };

  moveItemByIndex = (index, newIndex) => {
    const { onChange } = this.props;
    let newErrorSchema;
    if (this.props.errorSchema) {
      newErrorSchema = {};
      const { errorSchema } = this.props;
      for (const i in errorSchema) {
        if (parseInt(i, 10) === parseInt(index, 10)) {
          newErrorSchema[newIndex] = errorSchema[index];
        } else if (parseInt(i, 10) === parseInt(newIndex, 10)) {
          newErrorSchema[index] = errorSchema[newIndex];
        } else {
          newErrorSchema[i] = errorSchema[i];
        }
      }
    }

    const { keyedFormData } = this.state;
    function reOrderArray() {
      // Copy item
      const _newKeyedFormData = keyedFormData.slice();

      // Moves item from index to newIndex
      _newKeyedFormData.splice(index, 1);
      _newKeyedFormData.splice(newIndex, 0, keyedFormData[index]);

      return _newKeyedFormData;
    }
    const newKeyedFormData = reOrderArray();
    this.setState(
      {
        keyedFormData: newKeyedFormData,
      },
      () => onChange(keyedToPlainFormData(newKeyedFormData), newErrorSchema)
    );
  };

  moveItemByKey = (originKey, targetKey) => {
    const { keyedFormData } = this.state;
    let index = -1;
    let newIndex = -1;
    keyedFormData.forEach(({ key }, i) => {
      if (key === originKey) {
        index = i;
      } else if (key === targetKey) {
        newIndex = i;
      }
    });
    if (index !== -1 && newIndex !== -1) {
      this.moveItemByIndex(index, newIndex);
    }
  };

  onChangeForIndex = (index) => {
    return (value, errorSchema) => {
      const { formData, onChange } = this.props;
      const newFormData = formData.map((item, i) => {
        // We need to treat undefined items as nulls to have validation.
        // See https://github.com/tdegrunt/jsonschema/issues/206
        const jsonValue = typeof value === 'undefined' ? null : value;
        return index === i ? jsonValue : item;
      });
      onChange(
        newFormData,
        errorSchema &&
          this.props.errorSchema && {
          ...this.props.errorSchema,
          [index]: errorSchema,
        }
      );
    };
  };

  onSelectChange = (value) => {
    this.props.onChange(value);
  };

  toggleArrayItem = (key) => () => {
    const { expandedCollection } = this.state;
    this.setState({
      expandedCollection: {
        ...expandedCollection,
        [key]: !expandedCollection[key],
      },
    });
  };

  collapseItems = () => {
    this.setState({
      expandedCollection: {},
    });
  };

  render() {
    const { schema, registry = getDefaultRegistry() } = this.props;
    const { definitions } = registry;
    if (typeof schema.items === 'undefined' 
          && schema['x-ui-widget'] != 'dynamicCascadeSelectWidget' 
          && schema['x-ui-widget'] != 'poolOrShopSelectWidget') {
      return (
        <UnsupportedField schema={schema} reason="Missing items definition" />
      );
    }
    if (schema['x-ui-widget'] == 'dynamicCascadeSelectWidget' || schema['x-ui-widget'] == 'poolOrShopSelectWidget') {
      return this.renderCustomerWidget({widgetType: schema['x-ui-widget']});
    }
    if (isFixedItems(schema)) {
      return this.renderFixedArray();
    }
    if (isFilesArray(schema, definitions)) {
      return this.renderFiles();
    }
    if (isUploadsArray(schema, definitions)) {
      return this.renderUploader();
    }
    if (isMultiSelect(schema, definitions)) {
      return this.renderMultiSelect();
    }
    return this.renderNormalArray();
  }

  // 常规数组
  renderNormalArray() {
    const {
      schema,
      formData: originData,
      errorSchema,
      name,
      required,
      disabled,
      readonly,
      registry = getDefaultRegistry(),
      onBlur,
      onFocus,
      level,
      evaOptions
    } = this.props;
    const formData = originData || [];
    const title = schema.title === undefined ? name : schema.title;
    const { ArrayFieldTemplate, definitions, fields, formContext } = registry;
    const { TitleField, DescriptionField } = fields;
    const itemsSchema = retrieveSchema(schema.items, definitions);
    const arrayProps = {
      canAdd: this.canAddItem(formData),
      items: this.state.keyedFormData.map((keyedItem, index) => {
        const { key, item } = keyedItem;
        const itemSchema = retrieveSchema(schema.items, definitions, item);
        const itemErrorSchema = errorSchema ? errorSchema[index] : undefined;
        return this.renderArrayFieldItem({
          key,
          index,
          canMoveUp: index > 0,
          canMoveDown: index < formData.length - 1,
          itemSchema,
          itemErrorSchema,
          itemData: item,
          onBlur,
          onFocus,
        });
      }),
      className: `field field-array field-array-of-${itemsSchema.type}`,
      description: schema.description,
      showLabel: schema.showLabel,
      showListNo: schema.showListNo,
      DescriptionField,
      disabled,
      onAddClick: this.onAddClick,
      readonly,
      required,
      schema,
      title,
      TitleField,
      formContext,
      formData,
      registry,
      level,
      evaOptions
    };

    const Comp = ArrayFieldTemplate || NormalArrayFieldTemplate;
    return (
      <DndProvider backend={HTML5Backend}>
        <Comp {...arrayProps} />
      </DndProvider>
    );
  }

  renderMultiSelect() {
    const {
      className,
      schema,
      formData,
      disabled,
      readonly,
      required,
      label,
      placeholder,
      onBlur,
      onFocus,
      registry = getDefaultRegistry(),
    } = this.props;
    const items = this.props.formData;
    const { widgets, definitions, formContext } = registry;
    const itemsSchema = retrieveSchema(schema.items, definitions, formData);
    const enumOptions = optionsList(itemsSchema);
    const { widget = 'select', ...options } = {
      ...getUiOptionsFromSchema(schema),
      enumOptions,
    };
    const Widget = getWidget(schema, widget, widgets);
    return (
      <Form.Item
        className={className}
        label={schema.title}
        help={schema.description}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        title={schema.title}
      >
        <div>
          <Widget
            multiple
            onChange={this.onSelectChange}
            onBlur={onBlur}
            onFocus={onFocus}
            options={options}
            schema={schema}
            registry={registry}
            value={items}
            disabled={disabled}
            readonly={readonly}
            required={required}
            label={label}
            placeholder={placeholder}
            formContext={formContext}
          />
        </div>
      </Form.Item>
    );
  }

  /**
   * @param { widgetType: string } options 
   */
  renderCustomerWidget(_options) {
    const {
      className,
      schema,
      formData,
      disabled,
      readonly,
      required,
      label,
      onBlur,
      onFocus,
      registry = getDefaultRegistry(),
    } = this.props;
    const items = this.props.formData;
    const { widgets, definitions, formContext } = registry;
    const {
      widget = _options && _options.widgetType,
      placeholder = '',
      ...options
    } = getUiOptionsFromSchema(schema);
    const Widget = getWidget(schema, widget, widgets);
    return (
      <Form.Item
        className={className}
        label={schema.title}
        help={schema.description}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        title={schema.title}
        required={required || schema.required}
      >
        <div>
          <Widget
            multiple
            onChange={(v) => {
              this.props.onFieldSuccess(this.props.jsonPath);
              this.onSelectChange(v)
            }}
            onBlur={onBlur}
            onFocus={onFocus}
            options={options}
            schema={schema}
            registry={registry}
            value={items}
            disabled={disabled}
            readonly={readonly}
            required={required}
            label={label}
            placeholder={placeholder}
            formContext={formContext}
            onFieldError={(err) => this.props.onFieldError(this.props.jsonPath, err)}
            errors={this.props.errors || []}
          />
        </div>
      </Form.Item>
    );
  }

  renderUploader() {
    const {
      className,
      schema,
      name,
      disabled,
      readonly,
      onBlur,
      onFocus,
      registry = getDefaultRegistry(),
    } = this.props;
    const title = schema.title || name;
    const items = this.props.formData;
    const { widgets, formContext } = registry;
    const { widget = 'img-upload', ...options } = getUiOptionsFromSchema(schema);
    const Widget = getWidget(schema, widget, widgets);
    return (
      <Form.Item
        className={className}
        label={schema.title}
        help={schema.description}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        title={schema.title}
      >
        <div>
          <Widget
            options={options}
            multiple
            onChange={this.onSelectChange}
            onBlur={onBlur}
            onFocus={onFocus}
            schema={schema}
            title={title}
            value={items}
            disabled={disabled}
            readonly={readonly}
            formContext={formContext}
          />
        </div>
      </Form.Item>
    );
  }

  // TODO: 文件数组
  renderFiles() {
    const {
      schema,
      name,
      disabled,
      readonly,
      onBlur,
      onFocus,
      registry = getDefaultRegistry(),
    } = this.props;
    const title = schema.title || name;
    const items = this.props.formData;
    const { widgets, formContext } = registry;
    const { widget = 'files', ...options } = getUiOptionsFromSchema(schema);
    const Widget = getWidget(schema, widget, widgets);
    return (
      <Widget
        options={options}
        multiple
        onChange={this.onSelectChange}
        onBlur={onBlur}
        onFocus={onFocus}
        schema={schema}
        title={title}
        value={items}
        disabled={disabled}
        readonly={readonly}
        formContext={formContext}
      />
    );
  }

  renderFixedArray() {
    const {
      schema,
      formData,
      errorSchema,
      name,
      required,
      disabled,
      readonly,
      registry = getDefaultRegistry(),
      onBlur,
      onFocus,
      level,
    } = this.props;
    const title = schema.title || name;
    let items = this.props.formData;
    const { definitions, fields, formContext } = registry;
    const { TitleField } = fields;
    const itemSchemas = schema.items.map((item, index) =>
      retrieveSchema(item, definitions, formData[index]));
    const additionalSchema = allowAdditionalItems(schema)
      ? retrieveSchema(schema.additionalItems, definitions, formData)
      : null;

    if (!items || items.length < itemSchemas.length) {
      // to make sure at least all fixed items are generated
      items = items || [];
      items = items.concat(new Array(itemSchemas.length - items.length));
    }

    // These are the props passed into the render function
    const arrayProps = {
      canAdd: this.canAddItem(items) && additionalSchema,
      className: 'field field-array field-array-fixed-items',
      disabled,
      formData,
      items: this.state.keyedFormData.map((keyedItem, index) => {
        const { key, item } = keyedItem;
        const additional = index >= itemSchemas.length;
        const itemSchema = additional
          ? retrieveSchema(schema.additionalItems, definitions, item)
          : itemSchemas[index];
        const itemErrorSchema = errorSchema ? errorSchema[index] : undefined;

        return this.renderArrayFieldItem({
          key,
          index,
          canRemove: additional,
          canMoveUp: index >= itemSchemas.length + 1,
          canMoveDown: additional && index < items.length - 1,
          itemSchema,
          itemData: item,
          itemErrorSchema,
          onBlur,
          onFocus,
        });
      }),
      onAddClick: this.onAddClick,
      readonly,
      required,
      schema,
      title,
      TitleField,
      formContext,
      level: level + 1,
    };

    return (
      <DndProvider backend={HTML5Backend}>
        <FixedArrayFieldTemplate {...arrayProps} />
      </DndProvider>
    );
  }

  renderArrayFieldItem(props) {
    const {
      key,
      index,
      canRemove = true,
      canMoveUp = true,
      canMoveDown = true,
      itemSchema,
      itemData,
      itemErrorSchema,
      onBlur,
      onFocus,
    } = props;
    const {
      disabled,
      readonly,
      schema,
      registry = getDefaultRegistry(),
      level,
      jsonPath,
      onFieldError,
      onFieldSuccess,
    } = this.props;
    const {
      fields: { SchemaField },
    } = registry;
    const { orderable, removable } = {
      orderable: true,
      removable: true,
      ...schema['x-ui-options'],
    };
    const has = {
      moveUp: orderable && canMoveUp,
      moveDown: orderable && canMoveDown,
      remove: removable && canRemove,
    };
    const subJsonPath = `${jsonPath}[${index}]`;
    return {
      children: (
        <SchemaField
          schema={itemSchema}
          formData={itemData}
          errorSchema={itemErrorSchema}
          required={this.isItemRequired(itemSchema)}
          onChange={this.onChangeForIndex(index)}
          onBlur={onBlur}
          onFocus={onFocus}
          onFieldError={onFieldError}
          onFieldSuccess={onFieldSuccess}
          registry={this.props.registry}
          disabled={this.props.disabled}
          readonly={this.props.readonly}
          level={level + 1}
          jsonPath={subJsonPath}
          noLabel={itemSchema.type !== 'object' && itemSchema.type !== 'array'}
        />
      ),
      summaryTitle: getSummaryTitle(itemSchema, itemData),
      className: 'array-item',
      disabled,
      readonly,
      hasMoveUp: has.moveUp,
      hasMoveDown: has.moveDown,
      hasRemove: has.remove,
      index, // 索引位置
      $id: key, // 唯一ID
      key: `${key}_${index}`,
      schema: itemSchema,
      collapsed: this.state.expandedCollection[key], // 面板是否收起
      toggleItem: this.toggleArrayItem(key), // 转换面板收起状态
      collapseItems: this.collapseItems, // 收起所有面板
      removeItem: this.removeItem, // 删除条目
      moveItem: this.moveItemByKey, // 移动条目
      jsonPath: subJsonPath,
    };
  }
}

ArrayField.propTypes = types.fieldProps;

export default ArrayField;
