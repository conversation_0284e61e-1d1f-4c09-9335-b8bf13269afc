import React, { Component } from 'react';

import * as types from '../../types';

import ObjectFieldTemplate from '../fieldTemplates/ObjectFieldTemplate';

import {
  orderProperties,
  retrieveSchema,
  getDefaultRegistry,
  evaluateUiOptions,
  evaluateWidgetParam,
} from '../../utils';

class ObjectField extends Component {
  static defaultProps = {
    formData: {},
    errorSchema: {},
    required: false,
    disabled: false,
    readonly: false,
    jsonPath: '$',
  };

  state = {};

  prevFormData = null;

  isRequired(name) {
    const { schema } = this.props;
    return (
      Array.isArray(schema.required) && schema.required.indexOf(name) !== -1
    );
  }

  toggleNullValue = (value) => {
    if (!value) {
      if (this.prevFormData) {
        this.prevFormData = this.props.formData;
      }
      this.props.onChange(null, {});
    } else {
      this.props.onChange(this.prevFormData || {}, {});
    }
  }

  onPropertyChange = (name) => {
    return (value, errorSchema, skipValidate) => {
      const newFormData = { ...this.props.formData, [name]: value };
      this.props.onChange(
        newFormData,
        errorSchema &&
          this.props.errorSchema && {
          ...this.props.errorSchema,
          [name]: errorSchema,
        },
        skipValidate,
      );
    };
  };

  onDropPropertyClick = (key) => {
    return (event) => {
      event.preventDefault();
      const { onChange, formData } = this.props;
      const copiedFormData = { ...formData };
      delete copiedFormData[key];
      onChange(copiedFormData);
    };
  };

  getAvailableKey = (preferredKey, formData) => {
    let index = 0;
    let newKey = preferredKey;
    while (typeof formData[newKey] !== 'undefined') {
      // eslint-disable-next-line no-plusplus
      newKey = `${preferredKey}-${++index}`;
    }
    return newKey;
  };

  onKeyChange = (oldValue) => {
    return (value, errorSchema) => {
      if (oldValue === value) {
        return;
      }
      value = this.getAvailableKey(value, this.props.formData);
      const newFormData = { ...this.props.formData };
      const newKeys = { [oldValue]: value };
      const keyValues = Object.keys(newFormData).map((key) => {
        const newKey = newKeys[key] || key;
        return { [newKey]: newFormData[key] };
      });
      const renamedObj = Object.assign({}, ...keyValues);
      this.props.onChange(
        renamedObj,
        errorSchema &&
          this.props.errorSchema && {
          ...this.props.errorSchema,
          [value]: errorSchema,
        }
      );
    };
  };

  getDefaultValue(type) {
    switch (type) {
      case 'string':
        return 'New Value';
      case 'array':
        return [];
      case 'boolean':
        return false;
      case 'null':
        return null;
      case 'number':
        return 0;
      case 'object':
        return {};
      default:
        return '';
    }
  }

  render() {
    const {
      formData,
      errorSchema,
      name,
      required,
      disabled,
      readonly,
      onBlur,
      onFocus,
      onFieldError,
      onFieldSuccess,
      registry = getDefaultRegistry(),
      level,
      jsonPath,
    } = this.props;
    const { definitions, fields, formContext } = registry;
    const { SchemaField, TitleField, DescriptionField } = fields;
    const schema = retrieveSchema(this.props.schema, definitions, formData);
    const title = schema.title === undefined ? name : schema.title;
    const { description } = schema;
    let orderedProperties;
    try {
      const properties = Object.keys(schema.properties || {});
      orderedProperties = orderProperties(properties, schema['x-ui-order']);
    } catch (err) {
      return (
        <div>
          <p className="config-error" style={{ color: 'red' }}>
            Invalid {name || 'root'} object field configuration:
            <em>{err.message}</em>.
          </p>
          <pre>{JSON.stringify(schema)}</pre>
        </div>
      );
    }

    const Template = registry.ObjectFieldTemplate || ObjectFieldTemplate;

    const templateProps = {
      title,
      description,
      TitleField,
      DescriptionField,
      properties: orderedProperties.map((itemName) => {
        const itemSchema = schema.properties[itemName];
        const evaOpt = evaluateUiOptions(formData || {}, itemSchema);
        const widgetParam = evaluateWidgetParam(formData || {}, itemSchema);
        const subJsonPath = `${jsonPath}['${itemName}']`;
        const schemaField = (
          <SchemaField
            key={itemName}
            name={itemName}
            required={this.isRequired(itemName)}
            schema={schema.properties[itemName]}
            errorSchema={errorSchema[itemName]}
            formData={(formData || {})[itemName]}
            onKeyChange={this.onKeyChange(itemName)}
            onChange={this.onPropertyChange(itemName)}
            onBlur={onBlur}
            onFocus={onFocus}
            onFieldError={onFieldError}
            onFieldSuccess={onFieldSuccess}
            registry={registry}
            disabled={disabled || evaOpt.disabled}
            readonly={readonly || evaOpt.readonly}
            onDropPropertyClick={this.onDropPropertyClick}
            level={level + 1}
            jsonPath={subJsonPath}
            widgetParam={widgetParam}
            evaOptions={evaOpt}
          />
        );
        const withWrapper = (
          schema.properties[itemName]['x-ui-className'] ||
          schema.properties[itemName]['x-ui-style']
        ) ?
          (
            <section
              key={itemName}
              className={schema.properties[itemName]['x-ui-className']}
              style={schema.properties[itemName]['x-ui-style']}
            >
              {schemaField}
            </section>
          ) : schemaField;
        return {
          content: (evaOpt.hidden ? null : withWrapper),
          name: itemName,
          readonly,
          disabled,
          required,
          jsonPath: subJsonPath,
        };
      }),
      readonly,
      disabled,
      required,
      schema,
      formData,
      formContext,
      level,
      toggleNullValue: this.toggleNullValue,
    };
    return <Template {...templateProps} />;
  }
}

ObjectField.propTypes = types.fieldProps;

export default ObjectField;
