import React from "react";
import { env } from '@/config';

import * as types from "../../types";
import {
  getWidget,
  getUiOptionsFromSchema,
  getDefaultRegistry,
} from "../../utils";

function AtomField(props) {
  const {
    schema,
    name,
    formData,
    required,
    disabled,
    readonly,
    onChange,
    onFieldSuccess,
    onFieldError,
    jsonPath,
    registry = getDefaultRegistry(),
    widgetParam,
    evaOptions,
  } = props;
  const { title } = schema;
  const { widgets, formContext } = registry;
  const {
    widget,
    placeholder = "",
    ...options
  } = getUiOptionsFromSchema(schema);
  if (!widget) {
    console.error("x-ui-widget must be defined for atom type proeprty");
    return <></>;
  }
  const Widget = getWidget(schema, widget, widgets);
  return (
    <Widget
      options={{ ...options, ...evaOptions }}
      schema={schema}
      env={env === "ppe" ? "pre" : env}
      label={title === undefined ? name : title}
      value={formData}
      onChange={(v) => {
        onFieldSuccess(jsonPath);
        onChange(v);
      }}
      required={required}
      disabled={disabled}
      readonly={readonly}
      formContext={formContext}
      registry={registry}
      placeholder={placeholder}
      onFieldError={(err) => onFieldError(jsonPath, err)}
      widgetParam={widgetParam}
    />
  );
}

AtomField.propTypes = types.fieldProps;

AtomField.defaultProps = {
  disabled: false,
  readonly: false,
  onFieldError: () => {},
  onFieldSuccess: () => {},
};

export default AtomField;
