import React from 'react';
import * as types from '../../types';

import {
  getWidget,
  getUiOptionsFromSchema,
  optionsList,
  getDefaultRegistry,
} from '../../utils';

function BooleanField(props) {
  const {
    schema,
    name,
    formData,
    registry = getDefaultRegistry(),
    required,
    disabled,
    readonly,
    onChange,
    onFocus,
    onBlur,
  } = props;
  const { title } = schema;
  const { widgets, formContext } = registry;
  const { widget = 'switch', ...options } = getUiOptionsFromSchema(schema);
  const Widget = getWidget(schema, widget, widgets);

  let enumOptions;

  if (Array.isArray(schema.oneOf)) {
    enumOptions = optionsList({
      oneOf: schema.oneOf.map((option) => ({
        ...option,
        title: option.title || (option.const === true ? '是' : '否'),
      })),
    });
  } else {
    enumOptions = optionsList({
      enum: schema.enum || [true, false],
      enumNames:
        schema.enumNames ||
        (schema.enum && schema.enum[0] === false ? ['否', '是'] : ['是', '否']),
    });
  }

  return (
    <Widget
      options={{ ...options, enumOptions }}
      schema={schema}
      onChange={(v) => onChange(v)}
      onFocus={onFocus}
      onBlur={onBlur}
      label={title === undefined ? name : title}
      value={formData}
      required={required}
      disabled={disabled}
      readonly={readonly}
      registry={registry}
      formContext={formContext}
    />
  );
}

BooleanField.propTypes = types.fieldProps;

BooleanField.defaultProps = {
  disabled: false,
  readonly: false,
};

export default BooleanField;
