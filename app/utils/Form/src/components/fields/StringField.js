import React from 'react';
import * as types from '../../types';

import {
  getWidget,
  getUiOptionsFromSchema,
  isSelect,
  optionsList,
  getDefaultRegistry,
  hasWidget,
} from '../../utils';

function StringField(props) {
  const {
    schema,
    name,
    formData,
    required,
    disabled,
    readonly,
    onChange,
    onBlur,
    onFocus,
    onFieldSuccess,
    onFieldError,
    jsonPath,
    registry = getDefaultRegistry(),
    widgetParam,
    evaOptions,
  } = props;
  const { title, format } = schema;
  const { widgets, formContext } = registry;
  const enumOptions = isSelect(schema) && optionsList(schema);
  let defaultWidget = enumOptions ? 'select' : 'text';
  if (format && hasWidget(schema, format, widgets)) {
    defaultWidget = format;
  }
  const {
    widget = defaultWidget,
    placeholder = '',
    ...options
  } = getUiOptionsFromSchema(schema);
  const Widget = getWidget(schema, widget, widgets);
  return (
    <Widget
      options={{ ...options, enumOptions, ...evaOptions }}
      schema={schema}
      label={title === undefined ? name : title}
      value={formData}
      onChange={(v) => {
        onFieldSuccess(jsonPath);
        onChange(v);
      }}
      onBlur={onBlur}
      onFocus={onFocus}
      required={required}
      disabled={disabled}
      readonly={readonly}
      formContext={formContext}
      registry={registry}
      placeholder={placeholder}
      onFieldError={(err) => onFieldError(jsonPath, err)}
      widgetParam={widgetParam}
    />
  );
}

StringField.propTypes = types.fieldProps;

StringField.defaultProps = {
  disabled: false,
  readonly: false,
  onFieldError: () => {},
  onFieldSuccess: () => {},
};

export default StringField;
