import React, { Component } from 'react';
import { arrayOf, object, string, bool, any, func } from 'prop-types';
import <PERSON><PERSON>ield from './TitleField';

import * as types from '../../types';
import {
  getUiOptionsFromSchema,
  getWidget,
  guessType,
  retrieveSchema,
  getDefaultFormState,
  getMatchingOption,
} from '../../utils';

class OneOfField extends Component {
  static getDerivedStateFromProps(nextProps, prevState) {
    const { definitions } = nextProps.registry;
    const option = getMatchingOption(nextProps.formData, nextProps.options, definitions);
    return {
      ...prevState,
      selectedOption: option || 0
    };
  }

  state = {}

  getMatchingOption(formData, options) {
    const { definitions } = this.props.registry;

    const option = getMatchingOption(formData, options, definitions);
    if (option !== 0) {
      return option;
    }
    // If the form data matches none of the options, use the currently selected
    // option, assuming it's available; otherwise use the first option
    return this && this.state ? this.state.selectedOption : 0;
  }

  onOptionChange = (option) => {
    const selectedOption = parseInt(option, 10);
    const { formData, onChange, options, registry } = this.props;
    const { definitions } = registry;
    const newOption = retrieveSchema(
      options[selectedOption],
      definitions,
      formData
    );

    // If the new option is of type object and the current data is an object,
    // discard properties added using the old option.
    let newFormData;
    if (
      guessType(formData) === 'object' &&
      (newOption.type === 'object' || newOption.properties)
    ) {
      newFormData = Object.assign({}, formData);

      const optionsToDiscard = options.slice();
      optionsToDiscard.splice(selectedOption, 1);

      // Discard any data added using other options
      for (const opt of optionsToDiscard) {
        if (opt.properties) {
          for (const key in opt.properties) {
            if (typeof newFormData[key] !== 'undefined') {
              delete newFormData[key];
            }
          }
        }
      }
    }
    // Call getDefaultFormState to make sure defaults are populated on change.
    onChange(
      getDefaultFormState(options[selectedOption], newFormData, definitions),
      undefined,
      true
    );

    this.setState({
      selectedOption: parseInt(option, 10),
    });
  };

  onOptionToggle = (switchResult) => {
    const selectedOption = switchResult ? 1 : 0;
    this.onOptionChange(selectedOption);
  }

  render() {
    const {
      baseType,
      disabled,
      errorSchema,
      formData,
      onBlur,
      onChange,
      onFocus,
      options,
      registry,
      safeRenderCompletion,
      schema,
    } = this.props;

    const { SchemaField } = registry.fields;
    const { widgets } = registry;
    const { selectedOption } = this.state;
    const { widget = 'select', ...uiOptions } = getUiOptionsFromSchema(schema);
    const Widget = getWidget({ type: 'number' }, widget, widgets);

    const option = options[selectedOption] || null;
    let optionSchema;

    if (option) {
      // If the subschema doesn't declare a type, infer the type from the
      // parent schema
      optionSchema = option.type
        ? option
        : Object.assign({}, option, { type: baseType });
    }

    let sceneSelectElem = null;

    if (options.length === 2 && schema.title) {
      sceneSelectElem = (
        <>
          <TitleField
            title={schema.title}
            nullable
            nullValue={selectedOption !== 0}
            toggleNullValue={this.onOptionToggle}
            disabled={disabled}
          >
            <span className="description">{schema.description}</span>
          </TitleField>
        </>
      );
    } else {
      const enumOptions = options.map((opt, index) => ({
        label: opt.title || `Option ${index + 1}`,
        value: index,
      }));
      sceneSelectElem = (
        <div className="scene-select">
          <Widget
            schema={{ type: 'number', default: 0 }}
            onChange={this.onOptionChange}
            onBlur={onBlur}
            onFocus={onFocus}
            value={selectedOption}
            options={{ enumOptions }}
            {...uiOptions}
          />
        </div>
      );
    }

    return (
      <div className="one-of-field">
        {sceneSelectElem}

        {option !== null && (
          <SchemaField
            schema={optionSchema}
            errorSchema={errorSchema}
            formData={formData}
            onChange={onChange}
            onBlur={onBlur}
            onFocus={onFocus}
            registry={registry}
            safeRenderCompletion={safeRenderCompletion}
            disabled={disabled}
          />
        )}
      </div>
    );
  }
}

OneOfField.defaultProps = {
  disabled: false,
  errorSchema: {},
};

OneOfField.propTypes = {
  options: arrayOf(object).isRequired,
  baseType: string,
  formData: any,
  errorSchema: object,
  registry: types.registry.isRequired,
  disabled: bool,
  onChange: func,
  onBlur: func,
  onFocus: func,
  safeRenderCompletion: bool,
  schema: any,
};

export default OneOfField;
