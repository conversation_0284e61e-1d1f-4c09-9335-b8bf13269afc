import React from 'react';
import { string, bool, object, any, func } from 'prop-types';

import * as types from '../../types';

import {
  isMultiSelect,
  isSelect,
  retrieveSchema,
  getDefaultRegistry,
  getUiOptionsFromSchema,
  isFilesArray,
  deepEquals,
  getSchemaType,
} from '../../utils';
import UnsupportedField from './UnsupportedField';
import ModWidgetField from './ModWidgetField';
import SchemaFieldTemplate from '../fieldTemplates/SchemaFieldTemplate';

// const REQUIRED_FIELD_SYMBOL = "*";
const COMPONENT_TYPES = {
  array: 'ArrayField',
  boolean: 'BooleanField',
  integer: 'NumberField',
  number: 'NumberField',
  object: 'ObjectField',
  string: 'StringField',
  atom: 'AtomField',
  null: 'NullField',
};

function getFieldComponent(schema, fields) {
  const field = schema['x-ui-field'];
  if (typeof field === 'function') {
    return field;
  }
  if (typeof field === 'string' && field in fields) {
    return fields[field];
  }

  if (typeof field === 'string' && field.indexOf('mod/') === 0) {
    return ModWidgetField;
  }

  const componentName = COMPONENT_TYPES[getSchemaType(schema)];

  // If the type is not defined and the schema uses 'anyOf' or 'oneOf', don't
  // render a field and let the MultiSchemaField component handle the form display
  if (!componentName && (schema.anyOf || schema.oneOf)) {
    return () => null;
  }

  return componentName in fields
    ? fields[componentName]
    : () => {
      return (
        <UnsupportedField
          schema={schema}
          reason={`Unknown field type ${schema.type}`}
        />
      );
    };
}

class SchemaField extends React.Component {
  shouldComponentUpdate(nextProps) {
    // if schemas are equal idSchemas will be equal as well,
    // so it is not necessary to compare
    return !deepEquals(this.props, nextProps);
  }

  render() {
    const {
      formData,
      errorSchema = {},
      name,
      onKeyChange,
      onDropPropertyClick,
      required,
      registry = getDefaultRegistry(),
      noLabel,
      CustomFieldComponent,
      customProps,
      evaOptions = {},
    } = this.props;
    const { definitions, fields, formContext } = registry;
    const FieldTemplate = registry.FieldTemplate || SchemaFieldTemplate;
    const schema = retrieveSchema(this.props.schema || {}, definitions, formData);

    const FieldComponent = CustomFieldComponent || getFieldComponent(schema, fields);
    const uiDisabled = schema['x-ui-disabled'];
    const disabled = Boolean(
      this.props.disabled ||
        (typeof uiDisabled === 'boolean' && uiDisabled) ||
        this.props.schema.disabled ||
        schema.disabled
    );

    const uiReadonly = schema['x-ui-readonly'];
    const readonly = Boolean(
      this.props.readonly ||
        (typeof uiReadonly === 'boolean' && uiReadonly) ||
        this.props.schema.readOnly ||
        schema.readOnly
    );
    if (Object.keys(schema).length === 0) {
      return null;
    }

    const uiRequired = schema['x-ui-required'];

    const uiOptions = getUiOptionsFromSchema(schema);
    let { label: displayLabel = true } = uiOptions;
    if (schema.type === 'array') {
      displayLabel =
        isMultiSelect(schema, definitions) || isFilesArray(schema, definitions);
    }
    if (schema.type === 'object') {
      displayLabel = false;
    }
    if (schema.type === 'boolean' && !schema['x-ui-widget']) {
      displayLabel = false;
    }
    if (schema['x-ui-field']) {
      displayLabel = false;
    }

    const { __errors, ...fieldErrorSchema } = errorSchema;

    const field = (
      <FieldComponent
        {...this.props}
        {...customProps}
        schema={schema}
        disabled={disabled}
        readonly={readonly}
        required={required || uiRequired}
        errorSchema={fieldErrorSchema}
        formContext={formContext}
        errors={__errors}
      />
    );

    const { type } = schema;
    const label = evaOptions.title || this.props.schema.title || schema.title || name;
    const description = evaOptions.description || this.props.schema.description || schema.description;
    const errors = __errors;
    const help = schema['x-ui-help'];
    const hidden = schema['x-ui-widget'] === 'hidden';
    const classNames = [
      `field-${type}`,
      errors && errors.length > 0 ? 'field-error has-error has-danger' : '',
      // uiSchema.classNames,
    ]
      .join(' ')
      .trim();

    const fieldProps = {
      description,
      help,
      errors,
      label,
      hidden,
      onKeyChange,
      onDropPropertyClick,
      required: required || uiRequired,
      disabled,
      readonly,
      displayLabel,
      classNames,
      formContext,
      fields,
      schema,
      noLabel,
    };

    const { AnyOfField, OneOfField } = registry.fields;

    return (
      <FieldTemplate {...fieldProps}>
        {!(schema.oneOf || schema.anyOf) && field}

        {/*
          If the schema `anyOf` or 'oneOf' can be rendered as a select control, don't
          render the selection and let `StringField` component handle
          rendering
        */}
        {schema.anyOf && !isSelect(schema) && (
          <AnyOfField
            disabled={disabled}
            errorSchema={errorSchema}
            formData={formData}
            onBlur={this.props.onBlur}
            onChange={this.props.onChange}
            onFocus={this.props.onFocus}
            options={schema.anyOf}
            baseType={schema.type}
            registry={registry}
            safeRenderCompletion={this.props.safeRenderCompletion}
            schema={schema}
          />
        )}

        {schema.oneOf && !isSelect(schema) && (
          <OneOfField
            disabled={disabled}
            errorSchema={errorSchema}
            formData={formData}
            onBlur={this.props.onBlur}
            onChange={this.props.onChange}
            onFocus={this.props.onFocus}
            options={schema.oneOf}
            baseType={schema.type}
            registry={registry}
            safeRenderCompletion={this.props.safeRenderCompletion}
            schema={schema}
          />
        )}
      </FieldTemplate>
    );
  }
}

SchemaField.propTypes = {
  formData: any,
  schema: object,
  errorSchema: object,
  name: string,
  required: bool,
  registry: types.registry,
  noLabel: bool,
  disabled: bool,
  readonly: bool,
  safeRenderCompletion: bool,
  onKeyChange: func,
  onDropPropertyClick: func,
  onBlur: func,
  onChange: func,
  onFocus: func,
  CustomFieldComponent: func,
  customProps: object,
  evaOptions: object,
};

export default SchemaField;
