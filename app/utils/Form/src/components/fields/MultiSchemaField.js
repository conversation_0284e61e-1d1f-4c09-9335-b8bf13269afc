import React, { Component } from 'react';
import { arrayOf, object, string, bool, any, func } from 'prop-types';
import * as types from '../../types';
import {
  getUiOptionsFromSchema,
  getWidget,
  guessType,
  retrieveSchema,
  getDefaultFormState,
  getMatchingOption,
} from '../../utils';

class AnyOfField extends Component {
  static getDerivedStateFromProps(nextProps, prevState) {
    const { definitions } = nextProps.registry;
    const option = getMatchingOption(nextProps.formData, nextProps.options, definitions);
    return {
      ...prevState,
      selectedOption: option || 0
    };
  }

  state = {}

  onOptionChange = (option) => {
    const selectedOption = parseInt(option, 10);
    const { formData, onChange, options, registry } = this.props;
    const { definitions } = registry;
    const newOption = retrieveSchema(
      options[selectedOption],
      definitions,
      formData
    );

    // If the new option is of type object and the current data is an object,
    // discard properties added using the old option.
    let newFormData;
    if (
      guessType(formData) === 'object' &&
      (newOption.type === 'object' || newOption.properties)
    ) {
      newFormData = Object.assign({}, formData);

      const optionsToDiscard = options.slice();
      optionsToDiscard.splice(selectedOption, 1);

      // Discard any data added using other options
      for (const opt of optionsToDiscard) {
        if (opt.properties) {
          for (const key in opt.properties) {
            if (typeof newFormData[key] !== 'undefined') {
              delete newFormData[key];
            }
          }
        }
      }
    }
    // Call getDefaultFormState to make sure defaults are populated on change.
    onChange(
      getDefaultFormState(options[selectedOption], newFormData, definitions)
    );

    this.setState({
      selectedOption: parseInt(option, 10),
    });
  };

  render() {
    const {
      baseType,
      disabled,
      errorSchema,
      formData,
      onBlur,
      onChange,
      onFocus,
      options,
      registry,
      safeRenderCompletion,
      schema,
    } = this.props;

    const { SchemaField } = registry.fields;
    const { widgets } = registry;
    const { selectedOption } = this.state;
    const { widget = 'select', ...uiOptions } = getUiOptionsFromSchema(schema);
    const Widget = getWidget({ type: 'number' }, widget, widgets);

    const option = options[selectedOption] || null;
    let optionSchema;

    if (option) {
      // If the subschema doesn't declare a type, infer the type from the
      // parent schema
      optionSchema = option.type
        ? option
        : Object.assign({}, option, { type: baseType });
    }

    const enumOptions = options.map((opt, index) => ({
      label: opt.title || `Option ${index + 1}`,
      value: index,
    }));

    return (
      <div className="any-of-field">
        <div className="scene-select">
          <Widget
            schema={{ type: 'number', default: 0 }}
            onChange={this.onOptionChange}
            onBlur={onBlur}
            onFocus={onFocus}
            value={selectedOption}
            options={{ enumOptions }}
            {...uiOptions}
          />
        </div>

        {option !== null && (
          <SchemaField
            schema={optionSchema}
            errorSchema={errorSchema}
            formData={formData}
            onChange={onChange}
            onBlur={onBlur}
            onFocus={onFocus}
            registry={registry}
            safeRenderCompletion={safeRenderCompletion}
            disabled={disabled}
          />
        )}
      </div>
    );
  }
}

AnyOfField.defaultProps = {
  disabled: false,
  errorSchema: {},
};

AnyOfField.propTypes = {
  options: arrayOf(object).isRequired,
  baseType: string,
  formData: any,
  errorSchema: object,
  registry: types.registry.isRequired,
  disabled: bool,
  onChange: func,
  onBlur: func,
  onFocus: func,
  safeRenderCompletion: bool,
  schema: any,
};

export default AnyOfField;
