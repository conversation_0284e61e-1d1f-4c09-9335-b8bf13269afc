import React from 'react';
import { string, bool, element, oneOfType } from 'prop-types';
import { Message } from '@alife/next';

function DescriptionField(props) {
  const { description, useMessageBlock } = props;
  if (!description) {
    return null;
  }
  if (useMessageBlock) {
    return (
      <Message className="field-description" type="notice">
        {description}
      </Message>
    );
  }
  if (typeof description === 'string') {
    return <p className="field-description">{description}</p>;
  } else {
    return <div className="field-description">{description}</div>;
  }
}

DescriptionField.propTypes = {
  description: oneOfType([string, element]),
  useMessageBlock: bool,
};

export default DescriptionField;
