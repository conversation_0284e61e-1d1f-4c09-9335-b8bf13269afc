import React, { Component } from 'react';
import { string, bool, object, func, any, oneOfType, arrayOf } from 'prop-types';
import { Form, Upload, Message } from '@alife/next';

import { parseUiValidate } from '../../utils';
import { putInReq } from '@/utils/request'

class ImgUploadWidgetNew extends Component {
  state = { values: [] }

  constructor(props) {
    super(props);
    const { value, options } = props;
    const values = [].concat(value);
    const validateInfo = parseUiValidate(options.validate);
    this.state = { values, validateInfo };
  }

  validate = (value) => {
    const { schema, required, options } = this.props;
    const { pattern } = schema;
    if (required && !value) {
      return { validateState: 'error', help: '此项必填' };
    } else if (typeof value === 'string' && !new RegExp(pattern).test(value)) {
      return { validateState: 'error', help: options.patternMsg || '格式有误' };
    } else {
      this.setState({ validateState: 'success', help: '' });
      return { validateState: 'success', help: '' };
    }
  }

  formatter = (res) => {
    const { success, data = {} } = res;
    return {
      success,
      url: data.h5url,
      downloadURL: data.h5url,
      imgURL: data.h5url,
    };
  }

  beforeUpload = (file) => {
    const { validateInfo: v } = this.state;
    if (!v) return true;
    const reader = new FileReader();
    reader.onload = () => {
      const img = new Image();
      img.onload = () => {
        const { size, type } = file;
        const { width, height } = img;
        if (v.width && Math.abs(v.width - width) > 1) return Message.error(`图片宽度需为${v.width}px，目前宽度${width}px`);
        if (v.maxWidth && v.maxWidth < width) return Message.error(`图片宽度需小于${v.maxWidth}px`);
        if (v.minWidth && v.minWidth > width) return Message.error(`图片宽度需大于${v.minWidth}px`);
        if (v.height && Math.abs(v.height - height) > 1) return Message.error(`图片高度需为${v.height}px，目前高度${height}px`);
        if (v.maxHeight && v.maxHeight < height) return Message.error(`图片高度需小于${v.maxHeight}px`);
        if (v.minHeight && v.minHeight > height) return Message.error(`图片高度需大于${v.minHeight}px`);
        if (v.maxSize && (v.maxSize * 1024) < size) return Message.error(`图片体积需小于${v.maxSize}kb`);
        if (v.minSize && (v.minSize * 1024) > size) return Message.error(`图片体积需大于${v.minSize}kb`);
        if (v.accept) {
          const imageType = type.replace('image/', '');
          const availableTypes = v.accept.split(',').map((item = '') => (item.trim()));
          if (availableTypes.indexOf('jpg') >= 0) availableTypes.push('jpeg');
          if (availableTypes.indexOf(imageType) === -1) {
            return Message.error(`图片格式需为${v.accept}，检测到格式为${imageType}`);
          }
        }
        this.request(reader.result, file.name, width, height);
      };
      img.src = reader.result;
    };
    reader.readAsDataURL(file);
    return false;
  }

  onChange = (info) => {
    const { multiple, onChange, onFieldError } = this.props;
    const urls = info.map((item) => item.url);
    if (multiple) {
      onChange(urls);
    } else {
      const value = urls[0] || '';
      const res = this.validate(value);
      this.setState({ values: [value], ...res });
      if (res.validateState === 'success') {
        onChange(value);
      } else {
        onFieldError(res);
      }
    }
  };

  request = (base64, fileName, width, height) => {
    base64 = base64.replace(/^data\:.*?;base64,/, '');
    return putInReq.post('/api/pic/uploadPic', {
      userId: '1',
      base64,
      name: fileName
    }).then((res) => {
      if(res.status === 200 && res.data.code === '200') {
        const { values } = this.state;
        const newVal = values.slice(0);
        newVal.push(JSON.stringify({
          url: res.data.data,
          width,
          height
        }));
        Message.show('上传成功');
        this.setState({
          values: newVal
        });
        this.validate(res.data.data);
        this.props.onChange(JSON.stringify({
          url: res.data.data,
          width,
          height
        }));
      } else {
        Message.error(`上传失败，${res.data && (res.data.msg || '未知错误')}`);
      }
    }).catch((res) => {
      Message.error(`上传失败，${res.error && (res.error.message || '未知错误')}`);
    })
  }

  render() {
    const { schema, multiple, disabled, formContext: { imgUploadConfig } = {} } = this.props;
    const { values = [], validateInfo, help, validateState } = this.state;
    const filteredValue = values
      .filter((value) => !!value)
      .map((item) => {
        let {url} = JSON.parse(item);
        return { downloadURL: url, imgURL: url, url };
      });
    return (
      <Form.Item
        className="nested-form-item"
        help={help}
        validateState={validateState}
      >
        <Upload.Card
          className="alsc-form-item-uploader"
          accept="image/png, image/jpg, image/jpeg, image/apng, image/gif, image/bmp"
          limit={1}
          beforeUpload={this.beforeUpload}
          onChange={this.onChange}
          value={filteredValue}
        />
        {
          validateInfo &&
          <div className="restriction-text">
            <div className="restrict-item">
              上传尺寸：{
                [
                  validateInfo.width && `宽${validateInfo.width}px`,
                  validateInfo.minWidth && `宽>${validateInfo.minWidth}px`,
                  validateInfo.maxWidth && `宽<${validateInfo.maxWidth}px`,
                  validateInfo.height && `高${validateInfo.height}px`,
                  validateInfo.minHeight && `高>${validateInfo.minHeight}px`,
                  validateInfo.maxHeight && `高<${validateInfo.maxHeight}px`,

                ].filter((x) => !!x).join(', ')
              }
            </div>
            <div className="restrict-item">
              上传格式：{ validateInfo.accept }
            </div>
            {(validateInfo.minSize || validateInfo.maxSize) && <div className="restrict-item">
              图片大小：{
                [validateInfo.minSize && `体积>${validateInfo.minSize}kb`,
                  validateInfo.maxSize && `体积<${validateInfo.maxSize}kb`
                ].filter((x) => !!x).join(', ')
              }
            </div>}
          </div>
        }
      </Form.Item>
    );
  }
}

ImgUploadWidgetNew.defaultProps = {};

ImgUploadWidgetNew.propTypes = {
  formContext: any,
  multiple: bool,
  schema: object,
  disabled: bool,
  value: oneOfType([
    string,
    arrayOf(string),
  ]),
  options: object,
  onChange: func,
  required: bool,
  label: string,
  onFieldError: func,
};

export default ImgUploadWidgetNew;
