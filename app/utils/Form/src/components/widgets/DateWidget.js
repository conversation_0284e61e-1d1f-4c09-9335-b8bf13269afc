import React from 'react';
import { string, bool, func, any } from 'prop-types';
import { DatePicker } from '@alife/next';
import moment from 'moment';

function DateWidget(props) {
  const { placeholder, value, disabled, readonly, onChange, onBlur } = props;
  return (
    <DatePicker
      format="YYYY-MM-DD"
      placeholder={placeholder}
      value={value ? moment(value) : null}
      disabled={disabled}
      readOnly={readonly}
      onChange={(v) => {
        onChange(v.format('YYYY-MM-DD'));
      }}
      onBlur={onBlur}
    />
  );
}

DateWidget.propTypes = {
  value: any,
  placeholder: string,
  disabled: bool,
  readonly: bool,
  onChange: func,
  onBlur: func,
};

export default DateWidget;
