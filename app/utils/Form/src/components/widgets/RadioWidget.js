import React from 'react';
import { bool, func, array, shape, any } from 'prop-types';
import { Radio } from '@alife/next';

function RadioWidget(props) {
  const { options, value, disabled, readonly, onChange } = props;
  const { enumOptions, enumDisabled, inline, valueLabel = {}, valueHidden = {} } = options;

  return (
    <Radio.Group
      itemDirection={inline ? 'hoz' : 'ver'}
      disabled={disabled || readonly}
      value={value}
      onChange={(v) => onChange(v)}
    >
      {enumOptions.map((option, i) => (
        valueHidden[String(option.value)] ?
          null :
          <Radio
            key={i}
            value={option.value}
            disabled={enumDisabled && enumDisabled.indexOf(option.value) !== -1}
          >
            {valueLabel[String(option.value)] || option.label}
          </Radio>
      ))}
    </Radio.Group>
  );
}

RadioWidget.propTypes = {
  enumDisabled: array,
  options: shape({
    enumOptions: array,
    inline: bool,
  }).isRequired,
  value: any,
  disabled: bool,
  readonly: bool,
  onChange: func,
};

export default RadioWidget;
