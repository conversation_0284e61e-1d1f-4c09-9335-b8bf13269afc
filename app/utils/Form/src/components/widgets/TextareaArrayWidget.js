import React, {useState} from 'react';
import PropTypes from 'prop-types';
import {Form, Input} from '@alife/next';
import {rangeSpec} from "@/utils/Form/src/utils";

function TextareaArrayWidget(props) {
  // console.log(props);
  const {
    options,
    required,
    disabled,
    readonly,
    onChange,
    onBlur,
    onFocus,
    schema,
    onFieldError,
  } = props;
  let {value,placeholder} = props;

  if (value && value.length > 0) {
    value = value.join(',');
  }

  let {rows = 3, maxLength = 10} = schema;
  if (schema['x-ui-placeholder']) {
    placeholder = schema['x-ui-placeholder']
  }
  const [widgetValue, setWidgetValue] = useState(value);
  const [validateInfo, setValidateInfo] = useState({ validateState: '', help: '' });

  const validate = (value) => {
    // const pattern = /^[A-Za-z0-9,]*$/g;
    const {pattern = ''} = schema || {};
    const {required} = props;
    console.log(new RegExp(pattern).test(value));
    if (required && value=='') {
      return { validateState: 'error', help: `此项必填` };
    } else if (typeof value === 'string' && value.split(",").length > maxLength) {
      return { validateState: 'error', help: `此项最多需要${maxLength}个` };
    } else if (typeof value === 'string' && pattern && !new RegExp(pattern).test(value)) {
      return { validateState: 'error', help: `格式有误` };
    } else {
      return { validateState: 'success', help: undefined };
    }
  }

  const _onChange = (v) => {
    const res = validate(v);
    setValidateInfo({...res});
    setWidgetValue(v);
    if (res.validateState === 'success' || !onFieldError) {
      return onChange(v === '' ? options.emptyValue : v.split(','));
    } else {
      onFieldError && onFieldError(res);
    }
  };

  return (
    <Form.Item
      className="nested-form-item"
      help={validateInfo.help}
      validateState={validateInfo.validateState}
    >
      <Input.TextArea
        size="large"
        className="alsc-form-textarea-array"
        style={{width: '100%'}}
        value={typeof widgetValue === 'undefined' ? '' : widgetValue}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        readOnly={readonly}
        rows={rows}
        onBlur={onBlur && ((event) => onBlur(event.target.value))}
        onFocus={onFocus && ((event) => onFocus(event.target.value))}
        maxLength={options.maxLength || 2000}
        showLimitHint={true}
        onChange={_onChange}
      />
    </Form.Item>
  );
}

TextareaArrayWidget.defaultProps = {
  options: {},
};

TextareaArrayWidget.propTypes = {
  // schema: PropTypes.object.isRequired,
  placeholder: PropTypes.string,
  options: PropTypes.shape({
    rows: PropTypes.number,
    emptyValue: PropTypes.any,
  }),
  value: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  readonly: PropTypes.bool,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  onFieldError: PropTypes.func,
};

export default TextareaArrayWidget;
