import React from 'react';
import { oneOfType, string, number, object, func } from 'prop-types';
import { Range } from '@alife/next';

import { rangeSpec } from '../../utils';

function RangeWidget(props) {
  const { schema, value, onChange } = props;
  const { min, max, step } = rangeSpec(schema);
  return (
    <Range
      className="alsc-form-item-range"
      min={min}
      max={max}
      step={step}
      value={value}
      onChange={(v) => onChange(v)}
    />
  );
}

RangeWidget.propTypes = {
  schema: object,
  value: oneOfType([string, number]),
  onChange: func,
};

export default RangeWidget;
