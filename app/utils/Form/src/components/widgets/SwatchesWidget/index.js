import React, { Component } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { Button, Radio, Input } from '@alife/next';

import './style.scss';

const defaultSwatches = [
  ['红色系', 'R', ['#FF4847'], ['#FF6D6A'], ['#FF64A6'], ['#FF83B8']],
  ['橙色系', 'O', ['#FF8241'], ['#FF9C65'], ['#FFC75D'], ['#FFD533']],
  ['紫色系', 'P', ['#B94CFF'], ['#C760FF'], ['#E84CFE'], ['#EE81FE']],
  ['蓝色系', 'B', ['#707DFF'], ['#60A0FF'], ['#4CDAFF']],
  ['绿色系', 'G', ['#33D971'], ['#66E394'], ['#66DEBE']],
];

class SwatchesWidget extends Component {
  static propTypes = {
    value: PropTypes.any,
    disabled: PropTypes.bool,
    readonly: PropTypes.bool,
    options: PropTypes.object,
    onChange: PropTypes.func,
  };

  state = {
    enableCustom: true,
  }

  items = []


  get widgetParam() {
    const { options: { widgetParam = {} } = {} } = this.props;
    return widgetParam;
  }

  get swatches() {
    const { swatches = defaultSwatches } = this.widgetParam;
    return swatches;
  }

  constructor(props) {
    super(props);
    this.items = this.swatches.map((row) => {
      const [desc, prefix, ...colors] = row;
      const colorList = colors.map(([value, label], i) => {
        if (!label) return { value, label: `${prefix}${i + 1}` };
        return { value, label };
      });
      return { desc, colorList };
    });
    this.state.enableCustom = this.initEnableCustom(props);
  }

  initEnableCustom(props = this.props) {
    const { options: { widgetParam = {} } = {}, value } = props;
    const { swatches = defaultSwatches } = widgetParam;
    // 没有颜色值，不启用色板
    if (!value) return false;
    // 有颜色值，默认使用色板
    let enableCustom = true;
    for (const row of swatches) {
      const [, , ...colors] = row;
      for (const color of colors) {
        const reg = new RegExp(color, 'i');
        if (reg.test(value)) {
          enableCustom = false;
          break;
        }
      }
      if (!enableCustom) break;
    }
    return enableCustom;
  }

  renderRadios() {
    const values = {};
    const radios = this.items.map((item, i) => {
      const { colorList, desc } = item;
      const onSet = colorList.map((color) => {
        const val = color.value;
        // 清空重复的数据
        if (values[val]) {
          return null;
        }
        values[val] = true;

        return (
          <Radio value={color.value} key={color.value}>
            <div className="color-block" style={{ backgroundColor: color.value }}>{color.label}</div>
          </Radio>);
      });

      return (
        <div key={desc + i}>
          <p className="color-desc">{desc}</p>
          <div className="color-set">{onSet}</div>
        </div>
      );
    });
    return radios;
  }

  onRadioChange = (value) => {
    const { onChange } = this.props;
    onChange(value);
  }

  // 清空radio按钮选择
  clearChoose = () => {
    const { onChange } = this.props;
    onChange(null);
  }

  toggleCustom = () => {
    const { enableCustom } = this.state;
    this.setState({
      enableCustom: !enableCustom,
    });
  }

  handleCustomChange = (value) => {
    const { onChange, options = {} } = this.props;
    if (!value) {
      onChange(options.emptyValue || null);
      return;
    }
    onChange(value);
  }

  render() {
    const { value, disabled, readonly } = this.props;
    const { enableCustom, customError } = this.state;
    const actualDisabled = disabled || readonly;

    return (
      <div className={classNames('swatches-widget', actualDisabled && 'disabled')}>
        {
          !enableCustom &&
          <div className="radio-wrapper">
            <Radio.Group value={value} onChange={this.onRadioChange} disabled={actualDisabled}>
              {this.renderRadios()}
            </Radio.Group>
            <div className="float-btn-wrapper">
              <Button text type="primary" className="float-btn" onClick={this.clearChoose}>
                清空选择
              </Button>
              {
                this.widgetParam.allowCustom &&
                <Button text type="primary" className="float-btn" onClick={this.toggleCustom}>
                  自定义颜色
                </Button>
              }
            </div>
          </div>
        }
        {
          enableCustom &&
          <div className="radio-wrapper">
            <p className="color-desc">自定义</p>
            <Input
              trim
              value={value}
              onChange={this.handleCustomChange}
              disabled={actualDisabled}
              placeholder="请输入色值，如：#39C5BB"
            />
            {
              customError &&
              <div className="error-info">
                请输入正确的色值，例如：#FFFFFF
              </div>
            }
            <div className="float-btn-wrapper">
              <Button text type="primary" className="float-btn" onClick={this.toggleCustom}>
                使用标准色板
              </Button>
            </div>
          </div>
        }
      </div>
    );
  }
}

export default SwatchesWidget;

