.swatches-widget {
  &.disabled {
    opacity: 0.5;
  }

  .radio-wrapper {
    min-width: 200px;
    display: inline-block;
    position: relative;
  }

  .float-btn-wrapper {
    position: absolute;
    right: 0;
    top: 0;

    .float-btn {
      float: right;
      margin: 4px 4px 0 6px;
      font-size: 12px;
    }
  }

  .color-desc {
    height: 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    color: #6F6F6F;
    margin: 6px 0 4px;
  }

  .color-set {
    display: flex;

    .next-radio-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      border: 1px solid transparent;
      transition:
        border-color linear 200ms,
        background-color linear 200ms;

      &.checked:not(.disabled) {
        background-color: rgba(47, 84, 235, 0.2);
        border: 1px solid #2F54EB;
      }

      .next-radio {
        display: none;
      }

      .next-radio-label {
        padding: 3px;
        min-width: 24px;
        height: 30px;
        margin: 0;

        .color-block {
          padding: 0 3px;
          min-width: 24px;
          height: 24px;
          text-align: center;
          color: #fff;
          font-size: 12px;
          font-weight: 500;
          line-height: 24px;
          border-radius: 4px;
          display: inline-block;
          position: relative;
        }
      }
    }
  }
}
