import React from 'react';
import { string, bool, object, shape, array, func, any } from 'prop-types';
import { Select } from '@alife/next';

import { asNumber, guessType } from '../../utils';

const nums = new Set(['number', 'integer']);

/**
 * This is a silly limitation in the DOM where option change event values are
 * always retrieved as strings.
 */
function processValue(schema, value) {
  // "enum" is a reserved word, so only "type" and "items" can be destructured
  const { type, items } = schema;
  if (value === '') {
    return undefined;
  } else if (type === 'array' && items && nums.has(items.type)) {
    return value.map(asNumber);
  } else if (type === 'boolean') {
    return value === 'true';
  } else if (type === 'number') {
    return asNumber(value);
  }

  // If type is undefined, but an enum is present, try and infer the type from
  // the enum values
  if (schema.enum) {
    if (schema.enum.every((x) => guessType(x) === 'number')) {
      return asNumber(value);
    } else if (schema.enum.every((x) => guessType(x) === 'boolean')) {
      return value === 'true';
    }
  }

  return value;
}

function getValue(value) {
  return value;
}

function SelectWidget(props) {
  const {
    schema,
    options,
    value,
    required,
    disabled,
    readonly,
    multiple,
    onChange,
    onBlur,
    onFocus,
    placeholder,
  } = props;
  const { enumOptions, enumDisabled, valueLabel = {}, valueHidden = {} } = options;
  const emptyValue = multiple ? [] : '';

  let widgetValue = value;
  if (typeof value === 'undefined') widgetValue = emptyValue;
  if (typeof value === 'boolean' || typeof value === 'number') widgetValue = String(value);


  return (
    <Select
      className="alsc-form-select"
      popupClassName="alsc-form-select-popup"
      mode={multiple ? 'multiple' : 'single'}
      value={widgetValue}
      hasClear={!required}
      required={required}
      disabled={disabled}
      placeholder={placeholder}
      onBlur={
        !readonly && onBlur ?
          ((event) => {
            const newValue = getValue(event, multiple);
            onBlur(processValue(schema, newValue));
          }) : undefined
      }
      onFocus={
        !readonly && onFocus ?
          ((event) => {
            const newValue = getValue(event, multiple);
            onFocus(processValue(schema, newValue));
          }) : undefined
      }
      onChange={
        !readonly ? ((event) => {
          const newValue = getValue(event, multiple);
          onChange(processValue(schema, newValue));
        }) : undefined
      }
    >
      {enumOptions.map(({ value: v, label }, i) => {
        const optionDisabled = enumDisabled && enumDisabled.indexOf(v) !== -1;
        if (valueHidden[String(v)]) return null;
        return (
          <Select.Option key={i} value={String(v)} disabled={optionDisabled || readonly}>
            {valueLabel[String(v)] || label}
          </Select.Option>
        );
      })}
    </Select>
  );
}

SelectWidget.defaultProps = {};

SelectWidget.propTypes = {
  schema: object.isRequired,
  enumDisabled: array,
  options: shape({
    enumOptions: array,
  }).isRequired,
  placeholder: string,
  value: any,
  required: bool,
  disabled: bool,
  readonly: bool,
  multiple: bool,
  onChange: func,
  onBlur: func,
  onFocus: func,
};

export default SelectWidget;
