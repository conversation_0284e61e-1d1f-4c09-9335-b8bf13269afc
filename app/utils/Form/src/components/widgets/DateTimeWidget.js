import React from 'react';
import { string, bool, func, any } from 'prop-types';
import { DatePicker } from '@alife/next';
import moment from 'moment';

function DateTimeWidget(props) {
  const { placeholder, value, disabled, readonly, onChange, onBlur } = props;
  // hideTime: true 隐藏 时分秒的选择
  const {defaultTimeValue, hideTime} = props.schema || {};
  const _showTime = {defaultValue: moment(defaultTimeValue, 'HH:mm:ss', true), format: 'HH:mm:ss' }
  return (
    <DatePicker
      format="YYYY-MM-DD"
      showTime={!hideTime ? _showTime : undefined}
      placeholder={placeholder}
      value={value ? moment(value) : null}
      disabled={disabled}
      readOnly={readonly}
      onChange={(v) => {
        onChange(v ? parseFloat(v.format('x')) : '');
      }}
      onBlur={onBlur}
    />
  );
}

DateTimeWidget.propTypes = {
  value: any,
  placeholder: string,
  disabled: bool,
  readonly: bool,
  onChange: func,
  onBlur: func,
};

export default DateTimeWidget;
