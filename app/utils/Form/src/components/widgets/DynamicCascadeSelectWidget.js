import React, { useEffect, useState } from "react";
import PropTypes, { string, bool, object, shape, array, func, any } from "prop-types";
import {CascaderSelect, Form, Input, Message} from "@alife/next";
import { putInReq } from "@/utils/request";
import { request } from "@/adator/request";
import * as api from "@/utils/api";
import { onRequestSuccess } from "@/utils/api";

function DynamicCascadeSelectWidget(props) {
  const { schema, options, onChange, onBlur, onFocus, value, placeholder,required : isRequired,onFieldError,errors = []} =
    props;
  const {
    disabled,
    required,
    multiple = false,
    canGetThreeLevel = false,
    readonly,
    showSearch,
    requestOptions = {
      method: "get",
      apiUrl: "api/dynamic/queryAllSkuCategory",
      domainInfo: "admin", // 'putIn','admin',
      searchField: "",
      resultParams: {}
    },
  } = schema;

  let widgetProps = {
    required :required || isRequired,
    disabled,
    readonly,
    multiple,
    showSearch,
  };
  // const {enumOptions, enumDisabled, valueLabel = {}, valueHidden = {}} = options;
  let widgetValue = value;
  const emptyValue = multiple ? [] : "";
  if (multiple && value) {
    if (canGetThreeLevel) {
      // 是否需要到三级分类
      const { hasSelectedDisplay = [] } = value;
      widgetValue = hasSelectedDisplay.map((v) => v.value);
    } else {
      widgetValue = value && value.length > 0 ? value.map((v) => v.value) : [];
    }
  }
  if (typeof value === "undefined") widgetValue = emptyValue;
  const [dataSource, setDataSource] = useState([]);
  const [validateInfo, setValidateInfo] = useState({ validateState: '', help: '' });

  const [widgetCasValue, setWidgetCasValue] = useState(widgetValue);

  // 获取动态接口
  const getDynamic = (params = {}) => {
    const { method, apiUrl, domainInfo, searchField,type } = requestOptions;
    let requestDomain = domainInfo === "admin" ? request() : putInReq;
    const { keyword } = params;
    if (method == "get") {
      return requestDomain.get(apiUrl).then(onRequestSuccess);
    } else {
      let requestParams = { [searchField]: keyword }
      if (typeof type !== 'undefined') {
        requestParams.type = type
      }
      return requestDomain
        .post(apiUrl, requestParams)
        .then(onRequestSuccess);
    }
  };

  const handleDynamicData = () => {
    getDynamic()
      .then((result) => {
        let _dataSource = result;
        setDataSource(_dataSource);
      })
      .catch(api.onRequestError);
  };

  if (requestOptions.searchField) {
    widgetProps.onSearch = (keyword) => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
      let searchTimeout = setTimeout(async () => {
        if (keyword) {
          getDynamic({ keyword }).then((result) => {
            const relValue = props.value || [];
            let _dataSource;
            if (requestOptions.resultParams && Object.keys(requestOptions.resultParams).length > 0) {
              const {
                label = "label",
                value = "value",
                hierarchy = 0,
                tree = [],
              } = requestOptions.resultParams;
              let getData = [];
              if (hierarchy > 0) {
                for (let index = 0; index < hierarchy; index++) {
                  getData = result[tree[index]];
                }
              } else {
                getData = result;
              }
              getData = getData.map((item) => {
                return {
                  label: item[label],
                  value: item[value],
                };
              });
              _dataSource =
                relValue && relValue.length > 0
                  ? relValue.concat(getData)
                  : getData;
            } else {
              _dataSource =
                relValue && relValue.length > 0
                  ? relValue.concat(result)
                  : result;
            }
            setDataSource(_dataSource);
          });
        }
      }, 800);
    };
  }

  useEffect(() => {
    if (!requestOptions.searchField) {
      handleDynamicData();
    } else {
      // 需要搜索的动态数据源，如果有值，需要复制数据源，否则删除会存在问题
      setDataSource(value ? value : []);
    }
  }, []);

  useEffect(() => {
    if(errors.length > 0){
      setValidateInfo({validateState: 'error', help: errors[0] || ''})
    }
  }, [errors]);

  const onDynamicDataSourceChange = (value, data, extra) => {
    let hasSelected;
    let hasSelectedDisplay = [];
    const res = validate(value);
    setWidgetCasValue(value && value.length > 0 ? value : '');
    setValidateInfo({...res});
    if (multiple) {
      data.map((item) => {
        const { value, label, pos } = item;
        hasSelectedDisplay.push({
          value,
          label,
          level: pos.split("-").length - 1,
        });
      });
      if (canGetThreeLevel) {
        hasSelected = extra.checkedData
          .map((item) => {
            const { value, label, pos } = item;
            let level = pos.split("-").length - 1;
            return {
              value,
              label,
              level,
            };
          })
          .filter((o) => o.level == 3);
        if (res.validateState === 'success' || !onFieldError) {
          onChange({
            hasSelected,
            hasSelectedDisplay,
          });
        } else {
          onFieldError && onFieldError(res);
        }
      } else {
        if (res.validateState === 'success' || !onFieldError) {
          onChange(hasSelectedDisplay);
        } else {
          onFieldError && onFieldError(res);
        }
      }
    } else {
      hasSelectedDisplay = value;
      if (res.validateState === 'success' || !onFieldError) {
        onChange(hasSelectedDisplay);
      } else {
        onFieldError && onFieldError(res);
      }
    }
  };

  const validate = (value) => {
    const {pattern = ''} = schema || {};
    const {required} = props;
    if (required && (value == '' || (value && value.length==0))) {
      return {validateState: 'error', help: `此项必填`};
    } else {
      return {validateState: 'success', help: undefined};
    }
  }

  return (
    <Form.Item
      className="nested-form-item"
      help={validateInfo.help}
      validateState={validateInfo.validateState}
    >
      <CascaderSelect
        {...widgetProps}
        className="dynamic-datasource-form"
        multiple={multiple}
        required={props.required}
        dataSource={dataSource}
        style={{ width: "90%" }}
        value={typeof widgetCasValue === 'undefined' ? '' : widgetCasValue}
        placeholder={placeholder}
        onChange={(value, data, extra) =>
          onDynamicDataSourceChange(value, data, extra)
        }
        notFoundContent={"糟糕，没有找到对应的数据～"}
      />
    </Form.Item>
  );
}

DynamicCascadeSelectWidget.defaultProps = {};

DynamicCascadeSelectWidget.propTypes = {
  schema: shape({
    requestOptions: object,
  }).isRequired,
  enumDisabled: array,
  options: shape({
    enumOptions: array,
  }).isRequired,
  placeholder: string,
  value: any,
  required: bool,
  disabled: bool,
  readonly: bool,
  multiple: bool,
  onChange: func,
  onBlur: func,
  onFocus: func,
  onFieldError: PropTypes.func,
};

export default DynamicCascadeSelectWidget;
