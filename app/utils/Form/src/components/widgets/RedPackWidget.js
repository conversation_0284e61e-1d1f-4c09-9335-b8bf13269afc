import { Input } from '@alife/next'
import React, { Component } from 'react';
import { string, bool, object, func } from 'prop-types';

export default class RedPackTextWidget extends Component {
  constructor(props) {
    super(props);
    const { value = '-' } = props;
    this.state = {
      leftPart: value.split('-')[0],
      rightPart: value.split('-')[1]
    }
  }

  static propTypes = {
    onChange: func,
    value: string
  }

  onChange = (isFirst = false, val) => {
    const { onChange } = this.props;
    let combinedText = '';
    if(isFirst) {
      this.setState({
        leftPart: val
      })
      combinedText = `${val}-${this.state.rightPart}`
    } else {
      this.setState({
        rightPart: val
      })
      combinedText = `${this.state.leftPart}-${val}`
    }
    onChange(combinedText);
  }

  render () {
    const {leftPart, rightPart} = this.state;
    return <div className="red-pack-text-widget">
      <Input default={leftPart} value={leftPart} onChange={(value) => {
        this.onChange(true, value)
      }}/>
      <span className="seperator">N</span>
      <Input default={rightPart} value={rightPart} onChange={(value) => {
        this.onChange(false, value)
      }}/>
    </div>
  }
}