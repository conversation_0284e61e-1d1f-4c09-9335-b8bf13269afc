import { Input, NumberPicker } from '@alife/next'
import React, { Component } from 'react';
import { string, bool, object, func } from 'prop-types';

export default class ActivityAndBenefitWidget extends Component {
  constructor(props) {
    super(props);
    const { value = '-', schema } = props;
    const {placeholder = '-',max} = schema;
    this.state = {
      leftPart: value.split('-')[0],
      rightPart: value.split('-')[1],
      leftPlaceholder: placeholder.split('-')[0],
      rightPlaceholder: placeholder.split('-')[1],
      leftMax: max.split('-')[0],
      rightMax: max.split('-')[1],
    }
  }

  static propTypes = {
    onChange: func,
    value: string
  }

  onChange = (isFirst = false, val='') => {
    const { onChange } = this.props;
    let {leftPart, rightPart} = this.state;
    let combinedText = '';
    if (isFirst) {
      this.setState({
        leftPart: val
      })
      combinedText = `${val}-${rightPart ? rightPart : ''}`
    } else {
      this.setState({
        rightPart: val
      })
      combinedText = `${leftPart ? leftPart : ''}-${val}`
    }
    onChange(combinedText);
  }

  render () {
    const {leftPart, rightPart, leftPlaceholder, rightPlaceholder,rightMax} = this.state;
    return <div className="activity-and-benefit-widget">
      <NumberPicker placeholder={leftPlaceholder} className="number-picker" default={leftPart} value={leftPart} onChange={(value) => {
        this.onChange(true, value)
      }}/>
      <span className="seperator"></span>
      <Input placeholder={`${rightPlaceholder},最多${rightMax}个字`} maxLength={rightMax} default={rightPart} value={rightPart} onChange={(value) => {
        this.onChange(false, value)
      }}/>
    </div>
  }
}
