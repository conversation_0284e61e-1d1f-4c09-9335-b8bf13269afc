import React, { useState } from "react";
import PropTypes from "prop-types";
import { Input } from "@alife/next";

function TextareaWidget(props) {
  const {
    options,
    placeholder,
    value,
    required,
    disabled,
    readonly,
    schema,
    onChange,
    onBlur,
    onFocus,
  } = props;
  // 直接用 value, onChange 会导致每次输入都触发渲染，无法输入中文。
  const [text, setText] = useState(value || "");

  const _onChange = (v) => {
    if(v === undefined) {
      setText(options.emptyValue);
      onChange(options.emptyValue);
    } else {
      setText(v);
      onChange(v);
    }
  };

  return (
    <Input.TextArea
      size="large"
      className="alsc-form-textarea"
      value={text}
      placeholder={placeholder}
      required={required}
      disabled={disabled}
      readOnly={readonly}
      rows={schema.rows || 10}
      onBlur={onBlur && ((event) => onBlur(event.target.value))}
      onFocus={onFocus && ((event) => onFocus(event.target.value))}
      maxLength={schema.maxLength || 2000}
      showLimitHint={true}
      onChange={_onChange}
    />
  );
}

TextareaWidget.defaultProps = {
  options: {},
};

TextareaWidget.propTypes = {
  // schema: PropTypes.object.isRequired,
  placeholder: PropTypes.string,
  options: PropTypes.shape({
    rows: PropTypes.number,
    emptyValue: PropTypes.any,
  }),
  value: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  readonly: PropTypes.bool,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
};

export default TextareaWidget;
