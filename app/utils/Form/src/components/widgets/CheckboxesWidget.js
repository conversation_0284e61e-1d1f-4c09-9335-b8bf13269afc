import React from 'react';
import { bool, array, func, any, shape } from 'prop-types';
import { Checkbox } from '@alife/next';

function CheckboxesWidget(props) {
  const { disabled, options, value, readonly, onChange } = props;
  const { enumOptions, enumDisabled, inline, valueLabel = {}, valueHidden = {} } = options;
  return (
    <Checkbox.Group
      value={value}
      onChange={onChange}
      itemDirection={inline ? 'hoz' : 'ver'}
    >
      {enumOptions.map((option, index) => {
        const itemDisabled =
          enumDisabled && enumDisabled.indexOf(option.value) !== -1;
        if (valueHidden[String(option.value)]) return null;
        return (
          <Checkbox
            disabled={disabled || itemDisabled || readonly}
            value={option.value}
            key={index}
          >
            {valueLabel[String(option.value)] || option.label}
          </Checkbox>
        );
      })}
    </Checkbox.Group>
  );
}

CheckboxesWidget.defaultProps = {};

CheckboxesWidget.propTypes = {
  options: shape({
    enumDisabled: array,
    enumOptions: array,
    inline: bool,
  }).isRequired,
  value: any,
  readonly: bool,
  disabled: bool,
  // multiple: bool,
  onChange: func,
};

export default CheckboxesWidget;
