import React, { Component } from 'react';
import { any, bool, func, object } from 'prop-types';
import classNames from 'classnames';
import Pickr from '@simonwep/pickr';
import '@simonwep/pickr/dist/themes/nano.min.css';

const formatInput = (value) => {
  if (typeof value === 'undefined') return null;
  if (value === null) return null;
  if (typeof value === 'string') return value;
  const hex = value.toString(16);
  const pad = '000000';
  return `#${pad.substring(0, pad.length - hex.length)}${hex}`;
};

class ColorWidget extends Component {
  static propTypes = {
    schema: object.isRequired,
    value: any,
    disabled: bool,
    readonly: bool,
    options: object,
    onChange: func,
  };

  pickerElem = React.createRef();

  get widgetParam() {
    const { schema, options = {} } = this.props;
    const {
      widgetParam: {
        opacity,
      } = {},
    } = options;

    return {
      opacity: schema.type !== 'number' && opacity,
    };
  }

  initPickr = () => {
    const { schema, value, disabled, readonly, options = {}, onChange } = this.props;
    const { opacity } = this.widgetParam;

    this.pickr = Pickr.create({
      el: this.pickerElem.current,
      appClass: classNames(!opacity && 'opacity-disabled'),
      theme: 'nano',
      default: formatInput(value),
      disabled: disabled || readonly,
      position: 'bottom-end',
      outputPrecision: 2,
      swatches: [],
      components: {
        preview: true,
        hue: true,
        opacity,
        interaction: {
          hex: true,
          rgba: true,
          hsla: true,
          input: true,
          clear: true,
          save: true
        },
      },
      strings: {
        save: '确定',
        clear: '清空',
        cancel: '取消',
      },
    });

    this.pickr.on('clear', () => {
      onChange(options.emptyValue || null);
    }).on('save', (color) => {
      if (!color) {
        onChange(options.emptyValue || null);
      } else if (schema.type === 'number') {
        const hex = color.toHEXA().toString().substring(0, 7);
        const num = parseInt(hex.replace('#', ''), 16);
        onChange(num);
      } else if (this.widgetParam.opacity) {
        const [r, g, b, a] = color.toRGBA();
        const rgba = `rgba(${r.toFixed(0)}, ${g.toFixed(0)}, ${b.toFixed(0)}, ${a.toFixed(2)})`;
        onChange(rgba);
      } else {
        onChange(color.toHEXA().toString());
      }
    });
  }

  updatePickr = () => {
    const { schema, value, disabled, readonly } = this.props;
    if (this.pickr) {
      if (disabled || readonly) {
        this.pickr.disable();
      } else {
        this.pickr.enable();
      }
      if (schema.type === 'number' && typeof value === 'number') {
        this.pickr.setColor(formatInput(value), true);
        this.pickr.applyColor(true);
      } else {
        this.pickr.setColor(value || null, true);
        this.pickr.applyColor(true);
      }
    }
  }

  openPickr = (e) => {
    e.stopPropagation();
    if (this.pickr) {
      this.pickr.show();
    }
  }

  componentDidMount() {
    this.initPickr();
  }

  componentDidUpdate() {
    this.updatePickr();
  }

  render() {
    const { value, disabled, readonly } = this.props;

    return (
      <div className={classNames('color-widget', (disabled || readonly) && 'disabled')} onClick={this.openPickr}>
        <div ref={this.pickerElem} />
        <span>{typeof value !== 'undefined' ? value : '无'}</span>
      </div>
    );
  }
}

export default ColorWidget;
