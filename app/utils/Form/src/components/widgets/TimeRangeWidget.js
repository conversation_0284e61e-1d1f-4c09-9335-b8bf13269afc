import React from 'react';
import { string, bool, func, any } from 'prop-types';
import { TimePicker } from '@alife/next';

function TimeRangeWidget(props) {
  const { placeholder, disabled, readonly, onChange, onBlur } = props;
  let { value } = props;
  if(!value) {
    value = '-';
  }

  return (
    <div className='range-time-picker'>
      <TimePicker
        placeholder={placeholder}
        value={value.split('-')[0]}
        disabled={disabled}
        readOnly={readonly}
        defaultValue={''}
        onChange={(v) => {
          const startTime = v ? v : '';
          const endTime = value.split('-')[1];
          onChange(startTime+'-'+endTime);
        }}
        onBlur={onBlur}
      />
      <span className='separator'>-</span>
      <TimePicker
        placeholder={placeholder}
        value={value.split('-')[1]}
        disabled={disabled}
        readOnly={readonly}
        defaultValue={''}
        onChange={(v) => {
          const startTime = value.split('-')[0];
          const endTime = v ? v : '';
          onChange(startTime + '-' + endTime);
        }}
        onBlur={onBlur}
      />
    </div>
  );
}

TimeRangeWidget.propTypes = {
  value: any,
  placeholder: string,
  disabled: bool,
  readonly: bool,
  onChange: func,
  onBlur: func,
};

export default TimeRangeWidget;
