import React, { Component } from 'react';
import { string, bool, func, any, object } from 'prop-types';
import { Form, Input } from '@alife/next';

import * as types from '../../types';

export default class BaseInput extends Component {
  static propTypes = {
    label: string,
    options: object,
    schema: object,
    registry: types.registry,
    formContext: object,
    placeholder: string,
    value: any,
    required: bool,
    disabled: bool,
    readonly: bool,
    onChange: func,
    onBlur: func,
    onFocus: func,
    onFieldError: func,
    onFieldSuccess: func,
    widgetParam: any,
  };

  static defaultProps = {
    required: false,
    disabled: false,
    readonly: false,
  };

  state = {}

  constructor(props) {
    super(props);
    this.state = {
      ...this.state,
      value: this.props.value,
    };
  }

  validate = (value) => {
    const { schema, required, options } = this.props;
    const { minLength = 0, pattern } = schema;
    if (schema.required === false) {
      return { validateState: 'success', help: undefined };
    }
    if (required && !value) {
      return { validateState: 'error', help: '此项必填' };
    } else if (typeof value === 'string' && value.length < minLength) {
      return { validateState: 'error', help: `此项最少需要${minLength}个字符` };
    } else if (typeof value === 'string' && !new RegExp(pattern).test(value)) {
      return { validateState: 'error', help: options.patternMsg || '格式有误' };
    } else {
      return { validateState: 'success', help: undefined };
    }
  }

  handleChange = (value) => {
    const { onChange, onFieldError } = this.props;
    const res = this.validate(value);
    this.setState({ value, ...res });
    // console.log(res.validateState);
    if (res.validateState === 'success') {
      onChange(value);
    } else {
      onFieldError(res);
    }
  };

  render() {
    const {
      label, // 从 inputProps 中去除
      required,
      readonly,
      disabled,
      onBlur,
      onFocus,
      options,
      schema,
      formContext,
      registry,
      onChange,
      onFieldError,
      widgetParam,
      ...inputProps
    } = this.props;
    const { value, validateState, help } = this.state;

    // If options.inputType is set use that as the input type
    if (options.inputType) {
      inputProps.type = options.inputType;
    } else if (!inputProps.type) {
      // If the schema is of type number or integer, set the input type to number
      if (schema.type === 'number') {
        inputProps.type = 'number';
        // Setting step to 'any' fixes a bug in Safari where decimals are not
        // allowed in number inputs
        inputProps.step = 'any';
      } else if (schema.type === 'integer') {
        inputProps.type = 'number';
        // Since this is integer, you always want to step up or down in multiples
        // of 1
        inputProps.step = '1';
      } else {
        inputProps.type = 'text';
      }
    }

    // If multipleOf is defined, use this as the step value. This mainly improves
    // the experience for keyboard users (who can use the up/down KB arrows).
    if (schema.multipleOf) {
      inputProps.step = schema.multipleOf;
    }

    if (typeof schema.minimum !== 'undefined') {
      inputProps.min = schema.minimum;
    }

    if (typeof schema.maximum !== 'undefined') {
      inputProps.maxLength = schema.maximum;
      inputProps.hasLimitHint = true;
    }
    return (
      <Form.Item
        className="nested-form-item"
        help={help}
        validateState={validateState}
      >
        <Input
          className="alsc-form-input"
          readOnly={readonly}
          disabled={disabled}
          {...inputProps}
          value={value == null ? '' : value}
          onChange={this.handleChange}
          onBlur={onBlur && ((event) => onBlur(event.target.value))}
          onFocus={onFocus && ((event) => onFocus(event.target.value))}
          addonTextBefore={options.addonTextBefore}
          addonTextAfter={options.addonTextAfter}
        />
      </Form.Item>
    );
  }
}
