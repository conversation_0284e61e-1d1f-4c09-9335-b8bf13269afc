import React, { useEffect, useMemo, useRef, useState } from "react";
import { Form, Message, Select } from "@alife/next";
import _ from "lodash";
import PropTypes, {
  string,
  bool,
  object,
  shape,
  array,
  func,
  any,
} from "prop-types";
import { putInReq } from "@/utils/request";
import * as api from "@/utils/api";

const formItemLayoutChannel = {
  labelCol: { span: 6 },
  wrapperCol: {
    span: 18,
  },
  style: {
    width: "100%",
  },
  labelAlign: "center",
  labelTextAlign: "center",
};

const getDynamic = (apiUrl, requestParams) => {
  return putInReq.post(apiUrl, requestParams).then(api.onRequestSuccess);
};

const PoolOrShopSelectWidget = ({
  errors,
  options,
  placeholder,
  poolTypeConfig,
  required,
  schema,
  value,
  onBlur,
  onChange,
  onFieldError,
  onFocus,
}) => {
  const {
    disabled,
    multiple = false,
    readonly,
    maxLength, // -1 不限量
    requestOptions = {
      apiUrl: "/api/deliverymanage/getPoolInfoSurge",
      searchKey: "searchKey",
      params: {},
    },
  } = schema;

  const [dataSource, setDataSource] = useState([]);
  const [validateInfo, setValidateInfo] = useState({
    validateState: "",
    help: "",
  });

  useEffect(() => {
    if (value && value.length > 0 && maxLength != -1) {
      // 编辑回填，需要查 key/value 对应值回显
      const { apiUrl, searchKey, params = {} } = requestOptions;
      const _dataSource = [];

      const resList = value.map(async (item) => {
        try {
          // 使用try catch 避免一个接口报错，所有结果都无法回填
          return getDynamic(apiUrl, { ...params, [searchKey]: item });
        } catch (e) {
          return null;
        }
      });

      Promise.all(resList).then((_res) => {
        _res.filter(Boolean).map((res) => {
          if (res && res.length > 0) {
            _dataSource.push({
              label: res[0].poolContent,
              value: res[0].poolId,
              newPlatformFlag: res[0].newPlatformFlag,
            });
          }
        });
        setDataSource(_dataSource);
      });
    }
  }, []);

  const _placeholder = useMemo(() => {
    let typeStr = "店池";
    if (requestOptions.params && requestOptions.params.poolType) {
      if (requestOptions.params.poolType == 1) {
        typeStr = "品集";
      } else if (requestOptions.params.poolType == 2) {
        typeStr = "店池";
      }
    }
    return placeholder || `请输入选${typeStr}ID，支持输入最多${maxLength}个`;
  }, [maxLength, requestOptions.params.poolType]);

  const validate = (value) => {
    if (required && (value === "" || (value && value.length === 0))) {
      return { validateState: "error", help: `此项必填` };
    } else {
      return { validateState: "success", help: undefined };
    }
  };

  useEffect(() => {
    if (errors && errors.length > 0) {
      setValidateInfo({ validateState: "error", help: errors[0] || "" });
    }
  }, [errors]);

  const onPoolSelectChange = (value, actionType, record) => {
    setValidateInfo({ ...validate(value) });

    if (value.length > maxLength && maxLength != -1) {
      Message.warning(`最多支持输入${maxLength}个`);
    } else {
      if (actionType == "itemClick" || actionType == "tag") {
        if (value && value.length === 0) {
          onChange(undefined);
        } else {
          onChange(value);
        }
      }
    }
  };

  // 使用 useRef 保证方法不会每次渲染都重新创建新方法
  const onSearch = useRef(
    _.debounce((keyword) => {
      const { apiUrl, searchKey, params = {} } = requestOptions;

      let requestParams = { ...params, [searchKey]: keyword };
      if (keyword) {
        getDynamic(apiUrl, requestParams).then((data) => {
          const dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
            newPlatformFlag: item.newPlatformFlag,
          }));
          setDataSource(dataSource);
        });
      } else {
        setDataSource([]);
      }
    }, 800)
  );

  return (
    <Form.Item
      className="nested-form-item"
      help={validateInfo.help}
      validateState={validateInfo.validateState}
    >
      <Select
        className="dynamic-datasource-form"
        mode="multiple"
        showSearch
        placeholder={_placeholder}
        dataSource={dataSource}
        style={{ width: "100%" }}
        value={value}
        onChange={(value, data, extra) =>
          onPoolSelectChange(value, data, extra)
        }
        onSearch={onSearch.current}
        notFoundContent={"糟糕，没有找到对应的数据～"}
      />
    </Form.Item>
  );
};

PoolOrShopSelectWidget.propTypes = {
  schema: shape({
    label: string,
    placeholder: string,
    disabled: bool,
    required: bool,
    requestOptions: {
      apiUrl: string,
      searchKey: string,
      params: object,
    },
  }).isRequired,
  enumDisabled: array,
  options: shape({
    enumOptions: array,
  }).isRequired,
  placeholder: string,
  value: any,
  required: bool,
  disabled: bool,
  readonly: bool,
  multiple: bool,
  onChange: func,
  onBlur: func,
  onFocus: func,
  onFieldError: PropTypes.func,
};
export default PoolOrShopSelectWidget;
