import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import { init, loadWidget } from '@ali/alsc-mod-widget-engine';

init({ React, ReactDOM });

export default class ModWidget extends Component {
  static propTypes = {
    required: PropTypes.bool,
    disabled: PropTypes.bool,
    readonly: PropTypes.bool,
    schema: PropTypes.object,
    value: PropTypes.any,
    onChange: PropTypes.func,
    formContext: PropTypes.shape({
      widgetMap: PropTypes.object,
      widgetExtData: PropTypes.object,
      defEnv: PropTypes.string,
    }),
    widgetParam: PropTypes.any,
  }

  static getDerivedStateFromError(err) {
    console.error('[Mod Widget] Runtime Error');
    console.error(err);
    return {
      loading: false,
      innerError: err,
      errorMessage: '插件异常，请联系插件开发者',
    };
  }

  state = {
    loading: true,
    errorMessage: '',
    Widget: null,
  }

  constructor(props) {
    super(props);
    if (!props.formContext.widgetMap) {
      this.state.loading = false;
      this.state.errorMessage = '插件初始化中，请稍后...';
    } else if (!this.widget) {
      this.state.loading = false;
      this.state.errorMessage = `没有找到插件 ${this.widgetName}`;
    } else if (
      !this.widget.versions ||
      !this.version
    ) {
      this.state.loading = false;
      this.state.errorMessage = `插件 ${this.widgetName} 未在 ${this.defEnv} 发布版本`;
    }
  }

  get defEnv() {
    const { formContext: { defEnv } } = this.props;
    return defEnv || 'publish';
  }

  get widgetName() {
    const { schema } = this.props;
    return schema['x-ui-widget'].split('/')[1];
  }

  get version() {
    const { versions = [] } = this.widget || {};
    return (versions[0] || {}).version;
  }

  get widget() {
    const { formContext: { widgetMap = {} } } = this.props;
    return widgetMap[this.widgetName];
  }

  loadWidget = () => {
    if (this.widget && this.version) {
      this.setState({ loading: true, errorMessage: '' });
      loadWidget(
        this.widgetName,
        this.version,
        this.defEnv
      ).then((Widget) => {
        console.info('[Mod Widget] Loaded', this.widgetName, this.version);
        this.setState({ loading: false, errorMessage: '', Widget });
      }).catch((err) => {
        console.error('[Mod Widget] Load Error', this.widgetName, this.version, err);
        this.setState({ loading: false, errorMessage: '插件加载异常，请稍后刷新页面再试' });
      });
    }
  }

  componentDidMount() {
    this.loadWidget();
  }

  componentDidUpdate() {
    if (!this.state.loading && !this.state.Widget) {
      this.loadWidget();
    }
  }


  renderMain = () => {
    const { loading, errorMessage, innerError, Widget } = this.state;
    if (loading) {
      return (
        <div className="message">加载中...</div>
      );
    }
    if (errorMessage) {
      if (innerError) {
        return (
          <div className="message">
            插件异常，请联系插件开发者 {this.widget.versions[0].nick}
          </div>
        );
      }
      return (
        <div className="message">
          {errorMessage}
        </div>
      );
    }
    if (Widget) {
      const {
        schema,
        value,
        onChange,
        formContext,
        widgetParam,
        required,
        disabled,
        readonly,
      } = this.props;
      const { widgetExtData, defEnv } = formContext;
      return (
        <Widget
          slot="schema-form-widget"
          required={required}
          disabled={disabled}
          readonly={readonly}
          schema={schema}
          value={value}
          onChange={onChange}
          param={widgetParam}
          extData={{ widgetExtData, defEnv }}
        />
      );
    }
  }

  render() {
    return (
      <div className="mod-widget">
        {this.renderMain()}
      </div>
    );
  }
}
