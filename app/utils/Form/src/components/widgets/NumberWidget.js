import React from 'react';
import { string, number, bool, object, func, oneOfType } from 'prop-types';
import { NumberPicker } from '@alife/next';

import { rangeSpec } from '../../utils';

function NumberWidget(props) {
  const {
    schema,
    readonly,
    disabled,
    options,
    value,
    onChange,
    onBlur,
    onFocus,
  } = props;
  const rangeProps = rangeSpec(schema);

  const _onChange = (v) => {
    const { type } = schema;
    if (v === undefined) {
      return onChange(options.emptyValue);
    }
    if (type === 'string') {
      return onChange(String(v));
    }
    return onChange(v);
  };

  return (
    <NumberPicker
      className="number-picker"
      step={1}
      disabled={disabled}
      value={value && Number(value)}
      onChange={!readonly ? _onChange : undefined}
      onBlur={!readonly && onBlur ? ((event) => onBlur(event.target.value)) : undefined}
      onFocus={!readonly && onFocus ? ((event) => onFocus(event.target.value)) : undefined}
      addonTextBefore={options.addonTextBefore}
      addonTextAfter={options.addonTextAfter}
      {...rangeProps}
    />
  );
}

NumberWidget.propTypes = {
  schema: object,
  readonly: bool,
  disabled: bool,
  options: object,
  value: oneOfType([number, string]),
  onChange: func,
  onBlur: func,
  onFocus: func,
};

export default NumberWidget;
