import React from 'react';
import { string, bool, func, any } from 'prop-types';
import {NumberPicker, Range, TimePicker} from '@alife/next';
import {rangeSpec} from "@/utils/Form/src/utils";


function PriceRangeWidget(props) {
  const {placeholder, disabled, readonly, onChange, onBlur, schema, options} = props;
  let {value} = props;
  const rangeProps = rangeSpec(schema);
  if (!value) {
    value = '-';
  }

  const onNumberChange = (startPrice,endPrice) =>{
    onChange(startPrice + '-' + endPrice);
  }

  return (
    <div className='range-time-picker'>
      <NumberPicker
        {...rangeProps}
        className="number-picker"
        disabled={disabled}
        value={value.split('-')[0] && Number(value.split('-')[0])}
        onChange={(v) => {
          const startPrice = v ? v : '';
          const endPrice = value.split('-')[1];
          onNumberChange(startPrice,endPrice);
        }}
        onBlur={onBlur}
      />
      <span className='separator'>-</span>
      <NumberPicker
        className="number-picker"
        {...rangeProps}
        disabled={disabled}
        value={value.split('-')[1] && Number(value.split('-')[1])}
        onChange={(v) => {
          const startPrice = value.split('-')[0];
          const endPrice = v ? v : '';
          onNumberChange(startPrice,endPrice);
        }}
        onBlur={onBlur}
      />
    </div>
  );
}

PriceRangeWidget.propTypes = {
  value: any,
  placeholder: string,
  disabled: bool,
  readonly: bool,
  onChange: func,
  onBlur: func,
};

export default PriceRangeWidget;
