import React from 'react';
import { string, bool, func, any } from 'prop-types';
import { TimePicker } from '@alife/next';

function TimeWidget(props) {
  const { placeholder, value, disabled, readonly, onChange, onBlur } = props;
  return (
    <TimePicker
      placeholder={placeholder}
      value={value || null}
      disabled={disabled}
      readOnly={readonly}
      onChange={(v) => {
        onChange(v ? v.format('HH:mm:ss') : '');
      }}
      onBlur={onBlur}
    />
  );
}

TimeWidget.propTypes = {
  value: any,
  placeholder: string,
  disabled: bool,
  readonly: bool,
  onChange: func,
  onBlur: func,
};

export default TimeWidget;
