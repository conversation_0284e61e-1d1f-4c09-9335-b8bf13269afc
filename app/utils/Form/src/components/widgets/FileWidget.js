import React, { Component } from 'react';
import { string, bool, func, oneOfType, arrayOf } from 'prop-types';

import { dataURItoBlob, shouldRender } from '../../utils';

function addNameToDataURL(dataURL, name) {
  return dataURL.replace(';base64', `;name=${encodeURIComponent(name)};base64`);
}

function processFile(file) {
  const { name, size, type } = file;
  return new Promise((resolve, reject) => {
    const reader = new window.FileReader();
    reader.onerror = reject;
    reader.onload = (event) => {
      resolve({
        dataURL: addNameToDataURL(event.target.result, name),
        name,
        size,
        type,
      });
    };
    reader.readAsDataURL(file);
  });
}

function processFiles(files) {
  return Promise.all([].map.call(files, processFile));
}

function FilesInfo(fileList) {
  if (fileList.length === 0) {
    return null;
  }
  return (
    <ul className="file-info">
      {fileList.map((fileInfo, key) => {
        const { name, size, type } = fileInfo;
        return (
          <li key={key}>
            <strong>{name}</strong> ({type}, {size} bytes)
          </li>
        );
      })}
    </ul>
  );
}

function extractFileInfo(dataURLs) {
  return dataURLs
    .filter((dataURL) => typeof dataURL !== 'undefined')
    .map((dataURL) => {
      const { blob, name } = dataURItoBlob(dataURL);
      return {
        name,
        size: blob.size,
        type: blob.type,
      };
    });
}

class FileWidget extends Component {
  constructor(props) {
    super(props);
    const { value } = props;
    const values = Array.isArray(value) ? value : [value];
    this.state = { filesInfo: extractFileInfo(values) };
  }

  setRef = (ref) => {
    this.inputRef = ref;
  };

  shouldComponentUpdate(nextProps, nextState) {
    return shouldRender(this, nextProps, nextState);
  }

  onChange = (event) => {
    const { multiple, onChange } = this.props;
    processFiles(event.target.files).then((filesInfo) => {
      const state = {
        values: filesInfo.map((fileInfo) => fileInfo.dataURL),
        filesInfo,
      };
      this.setState(state, () => {
        if (multiple) {
          onChange(state.values);
        } else {
          onChange(state.values[0]);
        }
      });
    });
  };

  render() {
    const { multiple, readonly, disabled } = this.props;
    const { filesInfo } = this.state;
    return (
      <div>
        <p>
          <input
            ref={this.setRef}
            type="file"
            disabled={readonly || disabled}
            onChange={this.onChange}
            defaultValue=""
            multiple={multiple}
          />
        </p>
        <FilesInfo filesInfo={filesInfo} />
      </div>
    );
  }
}

FileWidget.defaultProps = {};

FileWidget.propTypes = {
  multiple: bool,
  readonly: bool,
  disabled: bool,
  value: oneOfType([
    string,
    arrayOf(string),
  ]),
  onChange: func,
};

export default FileWidget;
