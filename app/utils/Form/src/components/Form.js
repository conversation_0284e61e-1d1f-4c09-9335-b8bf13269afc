/* eslint-disable react/no-unused-prop-types */
/* eslint-disable no-console */
import React, { Component } from 'react';
import classNames from 'classnames';
import { string, bool, object, func, any, shape, objectOf, arrayOf, oneOfType } from 'prop-types';
import { Form } from '@alife/next';
import _pick from 'lodash/pick';
import _get from 'lodash/get';
import jp from 'jsonpath';

import {
  getDefaultFormState,
  recursiveRetrieveSchema,
  retrieveSchema,
  shouldRender,
  setState,
  getDefaultRegistry,
  toPathSchema,
  deepEquals,
  removeNullValue,
} from '../utils';
import validateFormData, { toErrorList } from '../validate';

export default class AForm extends Component {
  static defaultProps = {
    noValidate: false,
    liveValidate: false,
    safeRenderCompletion: false,
    omitExtraData: false,
  };


  static getRegistry(props) {
    // For BC, accept passed SchemaField and TitleField props and pass them to
    // the "fields" registry one.
    const { fields, widgets } = getDefaultRegistry();
    const schema = props.schema || {};
    return {
      fields: { ...fields, ...props.fields },
      widgets: { ...widgets, ...props.widgets },
      ArrayFieldTemplate: props.ArrayFieldTemplate,
      ObjectFieldTemplate: props.ObjectFieldTemplate,
      FieldTemplate: props.FieldTemplate,
      definitions: schema.definitions || {},
      formContext: props.formContext || {},
    };
  }

  static validate(props, formData = props.formData) {
    const { additionalMetaSchemas, customFormats, validate, transformErrors } = props;
    const schema = props.schema || {};
    const { definitions } = AForm.getRegistry(props);
    const resolvedSchema = retrieveSchema(schema, definitions, formData);
    return validateFormData(
      formData,
      resolvedSchema,
      validate,
      transformErrors,
      additionalMetaSchemas,
      customFormats
    );
  }

  static getDerivedStateFromProps(nextProps, prevState = {}) {
    // console.log('getDefaultFormState start');
    if (!nextProps.schema) console.warn('[Schema Form] Schema 不可为空！');
    const { formData, additionalMetaSchemas } = nextProps;
    const schema = nextProps.schema || {};
    const edit = typeof formData !== 'undefined';
    const { definitions } = schema;
    const formDataWithOutNull = removeNullValue(formData);
    const formDataWithDefault = getDefaultFormState(schema, formDataWithOutNull, definitions);
    const retrievedSchema = recursiveRetrieveSchema(schema, definitions, formDataWithDefault);
    const pathSchema = toPathSchema(retrievedSchema, '', definitions, formDataWithOutNull);
    const mustValidate = edit && !nextProps.noValidate && nextProps.liveValidate;
    const { errors, errorSchema } = mustValidate
      ? AForm.validate(nextProps)
      : {
        errors: prevState.errors || [],
        errorSchema: prevState.errorSchema || {},
      };
    // console.log('getDerivedStateFromProps', formDataWithDefault);
    return {
      ...prevState,
      schema,
      formData: formDataWithDefault,
      edit,
      errors,
      errorSchema,
      additionalMetaSchemas,
      pathSchema,
    };
  }

  state = {}

  fieldErrorMap = {}

  shouldComponentUpdate(nextProps, nextState) {
    const result = shouldRender(this, nextProps, nextState);
    // console.log('shouldComponentUpdate', result, nextProps, nextState);
    return result;
  }

  checkError = () => {
    if (
      !this.props.noValidate &&
      this.props.liveValidate &&
      this.props.onError
    ) {
      const { errors = [], errorSchema } = this.state;
      const fieldErrors = Object.keys(this.fieldErrorMap).map((path) => {
        const errorItem = this.fieldErrorMap[path];
        const data = jp.query(this.state.formData, path)[0];
        if (typeof data === 'undefined') {
          // 这项数据被删除，只交给 ajv 校验
          delete this.fieldErrorMap[path];
          return null;
        }
        return {
          ...errorItem,
          path,
          stack: `${path}: ${errorItem.help}`,
        };
      }).filter((item) => (!!item));
      this.props.onError([...errors, ...fieldErrors], errorSchema);
    }
  }

  componentDidMount() {
    // console.log('componentDidMount', deepEquals(this.state.formData, this.props.formData));
    if (
      this.props.onChange &&
      !deepEquals(this.state.formData, this.props.formData)
    ) {
      this.props.onChange(this.state.formData, this.state);
    }
    this.checkError();
  }

  componentDidUpdate() {
    // console.log('componentDidUpdate', deepEquals(this.state.formData, this.props.formData));
    if (
      this.props.onChange &&
      !deepEquals(this.state.formData, this.props.formData)
    ) {
      this.props.onChange(this.state.formData, this.state);
    }
    this.checkError();
  }

  getUsedFormData = (formData, fields) => {
    // for the case of a single input form
    if (fields.length === 0 && typeof formData !== 'object') {
      return formData;
    }

    return _pick(formData, fields);
  };

  getFieldNames = (pathSchema, formData) => {
    const getAllPaths = (_obj, acc = [], paths = []) => {
      Object.keys(_obj).forEach((key) => {
        if (typeof _obj[key] === 'object') {
          if (!paths.length) {
            getAllPaths(_obj[key], acc, [key]);
          } else {
            let newPaths = [];
            paths.forEach((path) => {
              newPaths.push(path);
            });
            newPaths = newPaths.map((path) => `${path}.${key}`);
            getAllPaths(_obj[key], acc, newPaths);
          }
        } else if (key === '$name') {
          paths.forEach((path) => {
            const formValue = _get(formData, path);
            if (typeof formValue !== 'object') {
              acc.push(path);
            }
          });
        }
      });
      return acc;
    };

    return getAllPaths(pathSchema);
  };

  onChange = (formData, newErrorSchema, skipValidate) => {
    // console.log('onChange start');
    const mustValidate = !this.props.noValidate && this.props.liveValidate && !skipValidate;
    let state = { formData };
    if (mustValidate) {
      const { errors, errorSchema } = AForm.validate(this.props, formData);
      state = { formData, errors, errorSchema };
    } else if (!this.props.noValidate && newErrorSchema) {
      state = {
        formData,
        errorSchema: newErrorSchema,
        errors: toErrorList(newErrorSchema),
      };
    }

    setState(this, state, () => {
      if (this.props.onChange) {
        this.props.onChange(formData, { ...this.state, formData });
      }
    });
  };

  onBlur = (...args) => {
    if (this.props.onBlur) {
      this.props.onBlur(...args);
    }
  };

  onFocus = (...args) => {
    if (this.props.onFocus) {
      this.props.onFocus(...args);
    }
  };

  onFieldError = (jsonPath, error) => {
    this.fieldErrorMap[jsonPath] = error;
    this.checkError();
  }

  clearFieldError = (jsonPath) => {
    delete this.fieldErrorMap[jsonPath];
    this.checkError();
  }

  submit = () => {
    let newFormData = this.state.formData;

    const { pathSchema } = this.state;

    if (this.props.omitExtraData === true) {
      const fieldNames = this.getFieldNames(pathSchema, this.state.formData);
      newFormData = this.getUsedFormData(this.state.formData, fieldNames);
    }

    if (!this.props.noValidate) {
      const { errors, errorSchema } = AForm.validate(this.props, newFormData);
      if (Object.keys(errors).length > 0) {
        setState(this, { errors, errorSchema }, () => {
          if (this.props.onError) {
            this.props.onError(errors, errorSchema);
          } else {
            console.error('Form validation failed', errors);
          }
        });
        return null;
      }
    }

    return newFormData;
  }

  componentDidCatch(err) {
    console.error('Schema Form Crashed', err);
    console.info('====== props ======');
    console.log(this.props);
    console.info('====== state ======');
    console.log(this.state);
  }

  render() {
    const {
      safeRenderCompletion,
      id,
      className,
      autocomplete,
      disabled,
    } = this.props;

    const { schema, formData, errorSchema } = this.state;
    const registry = AForm.getRegistry(this.props);
    const { SchemaField } = registry.fields;
    return (
      <Form
        field={this.field}
        labelAlign="left"
        labelTextAlign="left"
        id={id}
        className={classNames('alsc-schema-form', className)}
        autoComplete={autocomplete}
        noValidate
      >
        <SchemaField
          schema={schema}
          errorSchema={errorSchema}
          formData={formData}
          onChange={this.onChange}
          onBlur={this.onBlur}
          onFocus={this.onFocus}
          onFieldError={this.onFieldError}
          onFieldSuccess={this.clearFieldError}
          registry={registry}
          safeRenderCompletion={safeRenderCompletion}
          disabled={disabled}
          level={0}
        />
      </Form>
    );
  }
}

AForm.propTypes = {
  // 数据配置
  schema: object.isRequired,
  // 表单数据
  formData: any,
  // 额外控件列表
  widgets: objectOf(
    oneOfType([func, object])
  ),
  // 额外字段列表
  fields: objectOf(func),
  // 对象字段模板
  ObjectFieldTemplate: func,
  // 数组字段模板
  ArrayFieldTemplate: func,
  // 字段处理器（不开放）
  FieldTemplate: func,
  onChange: func,
  onError: func,
  onFocus: func,
  onBlur: func,
  id: string,
  className: string,
  autocomplete: string,
  disabled: bool,
  noValidate: bool,
  liveValidate: bool,
  validate: func,
  transformErrors: func,
  // 安全渲染Hack，先 setState 再 onChange
  safeRenderCompletion: bool,
  formContext: shape({
    imgUploadConfig: object,
  }),
  customFormats: object,
  additionalMetaSchemas: arrayOf(object),
  liveOmit: bool,
  omitExtraData: bool,
};
