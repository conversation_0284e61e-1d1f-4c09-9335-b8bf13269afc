.alsc-schema-form {
  overflow: auto;
  .fieldset {
    margin-bottom: 20px;

    > h1 {
      padding: 0;
      margin: 0;
      font-size: 14px;
    }

    > p {
      padding: 0;
      margin: 0;
      font-size: 14px;
      color: #666;
    }

    > .legend {
      position: relative;
      height: 24px;
      line-height: 24px;
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;

      > * {
        vertical-align: top;
      }

      .empty-switch {
        position: relative;
        top: 1px;
        margin-left: 8px;
      }
    }

    > .field-description {
      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .next-form-item-label {
      width: 200px;
      min-width: 200px;
      flex: 0 0 auto;
      overflow: hidden;

      label {
        display: block;
        white-space: nowrap;
        font-size: 14px;
        font-weight: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #000;
        line-height: 28px;
      }
    }

    .next-form-item-control {
      flex: 1 1 auto;
      max-width: 100%;
    }

    .alsc-form-item-range {
      padding-top: 8px;
    }

    .alsc-form-item-uploader {
      .next-upload-list-item {
        // margin-bottom: 6px;

        .next-upload-list-item-wrapper {
          width: 80px;
          height: 80px;
          border: 1px solid #D9D9D9;
          border-radius: 4px;
          overflow: hidden;

          .next-upload-tool {
            height: 20px;
            display: block;
            font-size: 0; // 避免空白文字节点
            background-color: rgba(0, 0, 0, 0.5);

            .next-upload-tool-download-link,
            .next-upload-tool-close {
              position: relative;
              display: inline-block;
              width: 50%;
              line-height: 20px;
              text-decoration: none;
              text-align: center;

              &::before {
                position: absolute;
                left: 50%;
                width: 100%;
                content: "删除";
                color: #FFF;
                font-size: 14px;
                text-align: center;
                transform: translateX(-50%);
              }

              > i {
                opacity: 0;
              }
            }

            .next-upload-tool-download-link {
              &::before {
                content: "查看";
              }

              &::after {
                content: "";
                position: absolute;
                background: #D8D8D8;
                width: 1px;
                height: 14px;
                right: 0;
                top: 3px;
              }
            }
          }
        }

        .next-upload-list-item-name {
          display: none;
        }
      }

      .next-upload-card {
        width: 80px;
        height: 80px;
        border: 1px solid #D9D9D9;
        border-radius: 4px;
        overflow: hidden;

        .next-icon-add {
          color: #000;

          &::before {
            font-size: 12px;
          }
        }

        .next-upload-text {
          margin-top: 4px;
          color: #000;
          font-size: 14px;

          &:focus {
            outline: none;
          }
        }
      }
    }

    .restriction-text {
      font-size: 12px;
      line-height: 1.5;
      color: #999999;
    }

    &:not(.level-0) {
      padding: 20px;
      background-color: #F5F5F5;
      border-radius: 4px;
      margin-bottom: 20px;

      > .legend {
        font-size: 16px;
      }
    }

    &:empty {
      padding: 0;
      margin: 0;
    }
  }

  .any-of-field {
    padding: 12px;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    background: #FFF;

    .scene-select {
      margin-bottom: 12px;
    }
  }

  .one-of-field {
    padding: 20px;
    background-color: #F5F5F5;
    border-radius: 4px;
    margin-bottom: 20px;

    > .legend {
      padding: 0;
      margin: 0;
      position: relative;
      height: 24px;
      line-height: 24px;
      font-size: 16px;
      font-weight: 500;

      > * {
        vertical-align: top;
      }

      .empty-switch {
        position: relative;
        top: 1px;
        margin-left: 8px;
      }

      span.description {
        margin-left: 12px;
        font-weight: normal;
        font-size: 14px;
        color: #666;

      }
    }

    > .fieldset {
      padding: 0;

      > :first-child {
        margin-top: 18px;
      }
    }
  }

  .array-item {
    background-color: #eaeaea;

    .item-title {
      display: flex;
      //height: 32px;
      font-size: 14px;
      align-items: center;
      user-select: none;
      cursor: pointer;
      border-bottom: 1px solid #fff;

      .drag-handler {
        .icon {
          cursor: -webkit-grab;
        }
      }

      .icon {
        padding-left: 10px;
        padding-right: 6px;

        &::before {
          font-size: 14px;
        }
      }

      .no {
        margin-right: 6px;
      }

      .title {
        flex: 1;
      }

      .action-btn {
        margin-right: 6px;
        opacity: 0;
      }
    }

    .item-body {
      overflow: hidden;
      border-bottom: 1px solid #fff;

      .fieldset {
        background: none !important;

        &:last-of-type {
          margin-bottom: 0;
        }
      }

      .any-of-field {
        margin: 12px;

        .fieldset {
          padding: 0;
        }
      }
    }

    &.collapsed {
      .item-body {
        display: none;
        border-bottom: 0;
      }

      &:hover {
        background-color: #ddd;
      }
    }

    &.has-border {
      background-color: #fff;
      border: 1px solid #eee;
      margin: 10px 0;
    }

    &.dragging {
      border: 1px dashed #666;
      background: #F4F4F4;
      margin-top: -2px;

      .item-title {
        visibility: hidden;
      }
    }

    &:hover {
      .action-btn {
        opacity: 1;
      }
    }

    &:nth-last-child(2) {
      &.collapsed {
        .item-title {
          border-bottom: 0;
        }
      }

      .item-body {
        border-bottom: 0;
      }
    }

    &.plain {
      .item-title {
        height: auto;

        .title {
          flex: 0 0 auto;
          padding-right: 12px;
          width: 120px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .inline-control {
          flex: 1;
          padding-top: 16px;
          padding-right: 4px;
        }
      }

      &:hover {
        background-color: #eaeaea;
      }
    }

    &.static {
      .item-title {
        padding-left: 32px;
        padding-right: 12px;
      }

      .item-title-nodrag {
        padding-left: 0 !important;
      }

    }
  }

  // 增加按钮
  .add-btn-wrapper {
    margin-top: 12px;

    .add-btn {
      display: block;
    }

    &:only-child {
      margin-top: 0;
    }
  }

  // 下拉选择占满行
  .alsc-form-select,
  .alsc-form-date-picker {
    display: block !important;
    width: 100% !important;
  }

  // 弹层样式，左文字，右 check
  .alsc-form-select-popup {
    .next-menu-item-inner {
      text-align: left;

      .next-icon.next-icon-select {
        right: 16px;
      }
    }
  }

  .range-view {
    padding-top: 6px;
    text-align: center;
    color: #666;
    font-weight: bold;
  }

  .number-picker {
    width: 130px !important;
  }

  .nested-item + :not(.nested-item) {
    margin-top: 24px;
  }

  .nested-item {
    position: relative;
    padding: 12px;
    padding-bottom: 0;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.05);
    }

    > div {
      position: relative;
    }
  }

  .next-form-item-help {
    color: #777;
  }

  .mod-widget {
    > .message {
      display: block;
      white-space: nowrap;
      line-height: 28px;
      font-size: 14px;
      font-weight: normal;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #777;
    }
  }

  .nested-form-item {
    margin-bottom: 0;

  }

  .nested-form-item-disnone{
    display: none;
  }

  .color-widget {
    position: relative;
    padding: 1px;
    height: 28px;
    width: 200px;
    border: 1px solid #c4c6cf;
    border-radius: 3px;
    background: #fff;
    font-size: 12px;
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
    }

    > span {
      position: relative;
      top: 2px;
      padding-left: 6px;
      color: #333;
      font-size: 14px;
      vertical-align: middle;
    }

    .pickr {
      display: inline-block;
      width: 24px;

      .pcr-button {
        box-shadow: none !important;

        &.clear {
          background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAAGXcA1uAAAABGdBTUEAALGPC/xhBQAAANFJREFUSA3tk8ERwiAQAIMvKECbsAr/8KRKirAJbUILgB96zFyGEAhHIA9n5JEccHsbCExSSj/FrT4QZ2fiVYE0h5agtT6nJPRP8ChNhon+SQaOuGFJBoG19iWEuBhj3nFSLp4rUcEZwGo1cAXUwCJQAqtACk5KqQcOUt7hn7ZCwfKHvvsLm/AjG0E+S7geBoH3/soYe3LOb5RbRzlzmBO+CC6Zc+5+hGix5CNECwEua6QoKxgp2hSMEJEEPaImwR7RLkGLqEtAEQ0RbImGCnKiDzH27FBK9/SQAAAAAElFTkSuQmCC');
          background-size: 100%;
        }
      }
    }
  }

  .object-switch-field {
    position: relative;
    border-radius: 4px 4px 0 0;
    padding: 20px;
    padding-bottom: 0;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.05);
    }

    > .next-form-item {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      > .next-col-6 {
        width: initial;
        min-width: initial;

        label {
          font-size: 16px;
          font-weight: 500;
          line-height: 26px;
        }
      }

      > .next-col-8 {
        width: initial;
        min-width: initial;
        height: 26px;
      }
    }

    &.nested-item {
      &::before {
        background: rgba(0, 0, 0, 0.05);
      }
    }

    ~ .nested-item {
      padding-top: 0;

      &::before {
        background: rgba(0, 0, 0, 0.02);
      }

      .one-of-field {
        margin-bottom: 12px;
      }
    }

    + .nested-item {
      .one-of-field {
        margin-top: 12px;
      }
    }
  }
}

.pcr-app {
  font-size: 16px;

  &.opacity-disabled {
    .pcr-color-opacity {
      display: flex !important;
      cursor: not-allowed !important;
      opacity: 0.5;

      .pcr-picker {
        left: calc(100% - 9px) !important;
      }
    }
  }

  .pcr-interaction {
    font-size: 12px;
  }
}

.range-time-picker {
  .separator {
    display: inline-block;
    padding: 0 6px;
  }
}

.add-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 60px;
  margin-bottom: 16px;
  &:hover {
    cursor: pointer;
  }
}
.add-btn .add-icon {
  height: 16px;
  width: 16px;
  background: url('./add.png') center no-repeat;
  background-size: cover;
}
.btn-title {
  color: #FF7C4D;
  display: inline-block;
  padding-left: 4px;
}

.remove-item {
  color: #FF7C4D;
  line-height: 11px;
  text-align: center;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
  margin-left: 16px;
  cursor: pointer;
  .remove-icon {
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: url('./remove.png') center no-repeat;
    background-size: cover;
  }
}

.array-item-list-block-show-label {
  min-width: 1000px !important;
  overflow-x: auto;
}

.array-item-list {
  .array-item {
    background: transparent;
    display: flex;
    flex-direction: row;
    .item-body {
      border: none;
    }
    .inline-control {
      flex: 1;
      padding-top: 16px;
      padding-right: 4px;
    }
  }
  .fieldset {
    padding: 0 !important;
  }
  .next-form-item-label {
    width: 0 !important;
    min-width: 0 !important;
    padding: 0 !important;
  }
}

.alsc-schema-form .fieldset .next-form-item-label label {
  color: #666666;
}

.alsc-schema-form .fieldset .next-radio-label label {
  color: #333333;
}

.alsc-schema-form .fieldset .alsc-form-item-uploader .next-upload-card {
  width: 94px;
  height: 94px;
  border: 1px dashed #D9D9D9;
  border-radius: 4px;
  overflow: hidden;
  .next-upload-text {
    display: none;;
  }
}

.next-upload:hover {
  color: #FF7C4D;
  border-color: #FF7C4D;
}

.alsc-schema-form .fieldset .alsc-form-item-uploader .next-upload-card .next-icon-add::before {
  font-size: 42px;
  height: 80px;
  width: 80px;
  line-height: 80px;
  font-weight: 200;
  color: #999999;
}

.nested-form-item .next-form-item-control{
  display: flex;
  flex-direction: row;

  .restriction-text {
    margin-left: 16px;
    .restrict-item {
      margin-bottom: 8px;
    }
  }
}

.selection-form {
  .next-checkbox-group-ver .next-checkbox-wrapper {
    margin-right: 16px;
  }

  .next-checkbox-group,
  .next-radio-group {
    display: flex;
    flex-wrap: wrap;
  }
}

.dayOfWeek-widget {
  .next-checkbox-wrapper {
    margin-right: 26px;
  }

  .next-checkbox-wrapper.checked .next-checkbox-inner {
    border-color: transparent;
    background-color: #FF7C4D;
  }

  .next-checkbox-wrapper.indeterminate:not(.disabled):hover .next-checkbox-inner, .next-checkbox-wrapper.indeterminate:not(.disabled).hovered .next-checkbox-inner, .next-checkbox-wrapper.indeterminate.focused .next-checkbox-inner, .next-checkbox-wrapper.checked:not(.disabled):hover .next-checkbox-inner, .next-checkbox-wrapper.checked:not(.disabled).hovered .next-checkbox-inner, .next-checkbox-wrapper.checked.focused .next-checkbox-inner {
    border-color: transparent;
    background-color: #FF5D2D;
  }

  .next-checkbox-wrapper:not(.disabled):hover .next-checkbox-inner, .next-checkbox-wrapper.hovered .next-checkbox-inner, .next-checkbox-wrapper.focused .next-checkbox-inner {
    border-color: #FF7C4D;
    background-color: #FFFFFF;
  }
}

.red-pack-text-widget,.activity-and-benefit-widget {
  display: flex;
  flex-direction: row;
  align-items: center;
  .seperator {
    margin: 0 8px;
  }
}

.alsc-form-textarea {
  width: 100%;
}
