import debugFn from 'debug';
import { fromJS } from 'immutable';
import parseUrl from 'url-parse';
import { switcher, branch } from './switcher';
import { isBlank, isNull, isArray, isUndefined, isFunction, isObject } from './validators';

const debug = debugFn('selection:utils');

export function omit(obj, keys){
  return copyBase(obj, k=> keys.indexOf(k) === -1)
}

export function pick(obj, keys){
  return copyBase(obj, k=> keys.indexOf(k) !== -1)
}

export function copyBase(obj, cb){
  return Object.keys(obj).filter(k=> cb(k)).reduce((p, c)=> {
    p[c] = obj[c]
    return p
  }, {})
}


export function deepClone(obj){
  if(!obj) return obj
  return fromJS(obj).toJS()
}

export function getUrlQuery(url = window.location.href) {
  return parseUrl(url, true).query;
}

export function promisify(func){
  return function(...args){
    return new Promise((resole, reject)=>{
      func.apply(func, [...args, (error, value)=>{
        if (error) reject(error)
        else resole(value)
      }])
    })
  }
}

export function sleep(milisecs){
  return new Promise((resolve)=>{
    setTimeout(resolve, milisecs)
  })
}

/**
 *
 * @param {*} str , '0 ：下架 , 1 ：上架 ,2 ：删除'
 * @returns {object} {0: '下架', ...}
 */
export function toKeyMap(str){
  return str.split(/[,，]/).map(e=> {
    let [k, v] = e.split(/[:：]/)
    return {[k.trim()]: v.trim()}
  }).reduce((p, c)=> {
    return {
      ...p,
      ...c
    }
  }, {})
}

export function objectToDataSource(object){
  return Object.keys(object).map(key=>{
    return {
      label: object[key],
      value: key
    }
  })
}

/**
 * 判断传入值是否为函数，如果是运行否则直接返回
 * @param {any} value 需要判断的value值
 */
const runFunction = switcher(
  branch(isFunction, (func, arg) => {
    return func.apply(func, [arg])
  }),
  branch(isUndefined, (func, value) => value),
  branch(() => true, (func) => func)
)

/**
 * 将bull转化成指定值
 * @param {any} value
 * @param {any} render
 */
export const switchNullTo = (value, render) => {
  if(isNull(value)){
    return runFunction(render, value)
  } else {
    return value
  }
}

/**
 * 将undefined转化成指定值
 * @param {any} value
 * @param {any} render
 */
export const switchUndefinedTo = (value, render) => {
  if(isUndefined(value)){
    return runFunction(render, value)
  } else {
    return value
  }
}


/**
 * 判断是val是否为空，补充为'---'
 * 在详情展示时使用
 * @param {all} value 需要判断的value值
 *  @param {any} fill 当value值不为空的时候需要展示的值
 */
export const fillBlank = switcher(
  branch(isArray, (value, render) => {
    return value.length ? runFunction(render, value) : '---'
  }),
  branch(() => true, (value, render) => !value ? '---' : runFunction(render, value))
)


/**
 * 复制操作时判断链接参数（value_copy）中是否有copy字端， 返回value值
 * @param {string} params 需要判断的value值
 * @param {function} without 没有copy字端是要执行的函数
 */
export function withoutCopy(params, without){
  if(!params) return null
  const [ value, copy ] = params.split('_');

  if(!copy && without){
    without(value)
  }

  return value
}

/**
 * 对store中单独的一部分key进行更新
 * @param {object} state
 * @param {object} action
 */
export function updateStoreByKey(state, action){
  const keys = Object.keys(action).slice(1);
  return function(updater){
    return keys.reduce((_state, key) => {
      const params = [_state, action, key]
      return updater.apply(updater, params)
    }, state)
  }
}

export function isEqual(target, value) {
  return fromJS(target).equals(fromJS(value));
}

/**
 * 去掉对象中blank元素：'', null, undefined, NaN,
 */

export function removeNullOfObj(obj){
  return Object.keys(obj).reduce((target, item) => {
    const validatorVal = isBlank(obj[item]);
    if(!validatorVal.length) {
      target[item] = isObject(obj[item]) ? removeNullOfObj(obj[item]) : obj[item];
    }
    return target
  }, {})
}


export function safeJSONParse(str, fallback = {}){
  try {
    return JSON.parse(str)
  } catch (error) {
    if (process.env.NODE === 'development'){
      console.warn('invalid json str: ', str)
    }
    return fallback
  }
}

export const getQueryString = function (name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  var url = window.location.href;
  var index = url.lastIndexOf("?");
  var query = decodeURI(url.slice(index + 1, url.length));
  var r = query.match(reg);
  if (r != null) {
    return unescape(r[2]);
  }
  return null;
}

export const replaceParamVal = function(paramName,replaceWith) {
  var oUrl = location.href.toString();
  var nUrl;
  if (oUrl.includes(paramName)) {
    var re = eval("/(" + paramName + "=)([^&]*)/gi");
    nUrl = oUrl.replace(re, paramName + "=" + replaceWith);
  } else {
    nUrl = `${oUrl}&step=${replaceWith}`;
  }
  debug("replaceParamVal", { paramName, replaceWith, newUrl: nUrl });
  // this.location = nUrl;
  window.location.href = nUrl;
}

/*
export const uniq = function(array){
  var temp = [];
  for(var i = 0; i < array.length; i++){
    if(temp.indexOf(array[i]) == -1){
      temp.push(array[i]);
    }
  }
  return temp;
}
*/
export function getValueByUrlKey (queryName) {
  // var _href = window.location.href;
  // if (_href.split('?')[0] === _href) {
  //   return "";
  // }
  // var arr = _href.split('?')[1].split('&');
  // var obj = {}
  // for (var i = 0; i < arr.length; i++) {
  //   const _data = arr[i].split('=');
  //   obj[_data[0]] = _data[1];
  // }
  // return obj[key];
  let url = window.location.href;
  if (url.split("?").length > 0) {
    // var query = decodeURI(window.location.href.split("?")[1]);
    var index = url.lastIndexOf("?");
    var query = decodeURI(url.slice(index + 1,url.length));
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      if (pair[0] == queryName) {
        return pair[1];
      }
    }
  }
  return null;
}

export function flatten(arr) {
  let res = arr.reduce((pre, cur) => {
    if (cur.children && cur.children.length > 0) {
      return pre.concat(cur).concat(cur.children);
    } else {
      return pre.concat(cur)
    }
  }, []);
  return changeToObject(res);
}

export function changeToObject(arr) {
  let obj = {};
  arr.map((v) => {
    obj[v.value] = v.label;
  })
  return obj;
}
