import React from 'react';
import hoistNonReactStatics from 'hoist-non-react-statics';
import getInjectors from './reducerInjectors';
import { withStoreContext } from './injectStore';

/**
 * Dynamically injects a reducer
 *
 * @param {string} key A key of the reducer
 * @param {function} reducer A reducer that will be injected
 *
 */
export default ({ key, reducer }) => (WrappedComponent) => {
  let ReducerInjector = withStoreContext(
    class extends React.Component {
      static WrappedComponent = WrappedComponent;
      static displayName = `withReducer(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

      injectors = getInjectors(this.props.store); // eslint-disable-line react/destructuring-assignment

      componentWillMount() {
        const { injectReducer } = this.injectors;
        injectReducer(key, reducer);
      }

      render() {
        return <WrappedComponent {...this.props} />;
      }
    }

  )
  return hoistNonReactStatics(ReducerInjector, WrappedComponent);
};
