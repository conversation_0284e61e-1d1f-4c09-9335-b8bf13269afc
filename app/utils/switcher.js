export function branch(condition, option) {
  return function(type, ...args) {
    const params = [type].concat(args);
    let ret;

    if(condition['apply']) {
      ret = condition.apply(condition, params);
    } else if(typeof condition == "string") {
      ret = !!(condition == type)
    }

    if(ret) {
      return option.apply(option, params)
    }
  }
}

export function switcher(...branches) {
  const branchLength = branches.length;
  return function(type, ...args) {
    const params = [type].concat(args);
    for(let index = 0; index < branchLength; index++) {
      const branch = branches[index];
      const ret = branch.apply(branch, params);
      if(ret) return ret;
    }
  }
}
