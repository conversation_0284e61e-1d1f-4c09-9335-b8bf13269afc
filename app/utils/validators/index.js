//判断类型函数

/**is Array */
export const isArray = (target) => {
  return target instanceof Array
}

/**is function */
export const isFunction = (target) => {
  return target instanceof Function
}

/**is String */
export const isString = (target) => {
  return typeof target == "string";
}

/**is Undefined */
export const isUndefined = (target) => {
  return typeof target == "undefined"
}

/**is NUll */
export const isNull = (target) => {
  return !target && typeof(target)!=  "undefined" && target!=0
}

/** is Object */
export const isObject = (o) => {
  return Object.prototype.toString.call(o) === "[object Object]";
}

/**
 * 正则表达式集合
 */

function validator(message, fun){
  var f = function(){
    return fun.apply(fun, arguments);
  }

  f['message'] = message;
  return f;
}

export function checker(...validator){
  return function(value){
    return validator.reduce((errs, validate) => {
      if(validate(value)){
        return errs
      } else {
        errs.push(validate.message);
        return errs
      }
    }, [])
  }
}

/**
 * 检测字符串是否包含数字
 */
// export const isAllNumber = validator('is not all number', (value) => /^[0-9]*$/.test(value))
export const isAllNumber = checker(
  validator('is not all number', (value) => /^[0-9]*$/.test(value))
)

/**
 * 检测数组中是否包含null, undefined, '', NaN
 */
export const isArrayWithBlank = checker(
  validator('array with null', (value) => !value.includes(null)),
  validator('array with blank string', (value) => !value.includes('')),
  validator('array with undefined', (value) => !value.includes(undefined)),
  validator('array with NaN', (value) => !value.includes(NaN)),
)

/**
 * 测对象是否是null, undefined, '', {}, []
 */
export const isBlank = checker(
  validator('is null', (value) => !isNull(value)),
  validator('is undefined', (value) => !isUndefined(value)),
  validator('is blank string', (value) => !(isString(value) && value === '')),
  validator('is blank array', (value) => !(isArray(value) && value.length === 0)),
  validator(
    'is blank obj', 
    (value) => !(
      isObject(value) && 
      !Object.keys(value).filter(key => !isBlank(value[key]).length).length
    )
  ),
)


export const createRegexMatchMiddleware = (regex, msg)=>{
  return (rule, value, callback)=>{
    if (regex.test(value)){
      return callback()
    }
    else callback(msg)
  }
}


export function idCommaSeperated(rule, value, cb){
  if (!value) cb()
  else idCommaSeperatedRequired(rule, value, cb)
}

export const idCommaSeperatedRequired = createRegexMatchMiddleware(/^(\d+)(,\s*\d+)*$/, '输入格式不合法')

export function upcIdCommaSeperated(rule, value, cb){
  if(!value) cb()
  else upcIdCommaSeperatedRequired(rule, value, cb)
}
export const upcIdCommaSeperatedRequired = createRegexMatchMiddleware(/^([\w\-]+)(,\s*[\w\-]+)*$/, '输入格式不合法')

export function commaSeperated(rule, value, cb){
  if (!value) cb()
  else commaSeperatedRequired(rule, value, cb)
}

export const commaSeperatedRequired = createRegexMatchMiddleware(/([^,]+)(,\s*[^,]+)*/, '输入格式不合法')

export function createMaxCommaItem(max){
  return (rule, value, cb)=>{
    if(value.split(',').length <= max) cb()
    else cb(`输入超过最大长度:${max}个`)
  }
}

export function required(rule, value, cb){
  if (!value || !value.trim()) cb('该字段不能为空')
  else cb()
}

export function chain(middlewares){
  return function(rule, value, callback){
    let i = 0;
    const next =()=>{
      let m = middlewares[i++]
      if (m){
        m(rule, value, (msg)=>{
          if (!msg) next()
          else callback(msg)
        })
      }else {
        callback()
      }
    }
    next()
  }
}

export function optional(middleware){
  return function(rule, value, callback){
    if (!value) return callback()
    else return middleware(rule, value, callback)
  }
}

export function joinArray(middleware){
  return map(middleware, (_rule, value)=>({value: (value||[]).join(',')}))
}

export function map(middleware, mapF){
  return (arule, avalue, acb)=>{
    const {rule = arule, value = avalue, callback = acb} = mapF(arule, avalue, acb)
    return middleware(rule, value, callback)
  }
}
