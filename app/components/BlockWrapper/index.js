import React from 'react';
import { Grid } from '@alife/next';
import clz from 'classnames'

const { Row, Col } = Grid;

import './style.scss';

export const SettingBlock = ({ title = '', children = null, className }) => (
  <Row className="setting-block-wrapper">
    <Col span={3} className='rules-title'>{title}</Col>
    <Col span={21} className={clz('rules-form', className)}>
      {children}
    </Col>
  </Row>
)
