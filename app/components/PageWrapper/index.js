import './style.scss';

import React from 'react';
import clz from 'classnames'

export function PageWrapper({title, children, className, withBorder = false, secondTitle}){
  const rootClassName = 'c-page-wrapper'
  return <div className={clz(rootClassName, className)}>
    <h2 className={clz({withBorder})}>
      {title}
      {secondTitle && <p className="second-title">{secondTitle}</p> }
    </h2>
    <div className={`${rootClassName}__content`}>
      {children}
    </div>
  </div>
}


export function PageSection({title, children, className}){
  const rootClassName = 'c-page-section'
  return <div className={clz(rootClassName, className)}>
    {
      typeof title === 'string' ? <h3 className="title">{title}</h3> : title
    }
    <div className={`${rootClassName}__content`}>
      {children}
    </div>
  </div>
}
