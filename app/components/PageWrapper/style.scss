
.c-page-wrapper {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;

  h2 {
    font-size: 20px;
    line-height: 20px;
    padding: 10px 0 20px 0;
  }

  .withBorder{
    border-bottom: 1px solid #D9D9D9;
  }

  .second-title {
    font-size: 14px;
    color: #999999;
  }

  &__content {
    background-color: white;
    padding: 20px;
    flex: 1;
  }
}

.c-page-section {
  margin: 20px 0;

  .title {
    font-size: 16px;
    color: #999999;
    line-height: 16px;
    margin-bottom: 30px;
  }

  &__content {
    // nothing right now
  }
}
