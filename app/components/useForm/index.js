import { useState, useEffect } from 'react'
import { Message } from '@alife/next'
import { isComposing } from '@/lib/composing'

export default function useForm (initial, onSubmit) {
  let data
  let setData
  if (Array.isArray(initial) && initial.length === 2 && typeof initial[1] === 'function') {
    ;([data, setData] = initial)
  } else {
    ;([data, setData] = useState(initial || {}))
  }
  const [lastBlur, setLastBlur] = useState('')
  const [error, setError] = useState({})
  const [submitLoading, setSubmitLoading] = useState(false)
  const validates = []
  function set (key, value) {
    if (typeof key === 'object') {
      setData({
        ...data,
        ...key,
      })
    } else {
      setData({
        ...data,
        [key]: value,
      })
    }
  }
  function bind (key) {
    const ret = {
      onChange (val) {
        if (val && typeof val === 'object' && 'value' in val) {
          val = val.value
        }
        set(key, val)
      },
      value: data[key],
      onBlur () {
        setLastBlur(key)
      },
    }
    if (onSubmit) {
      //ret.onKeyUp = (event) => {
      //  if (event && typeof event === 'object' && event.keyCode === 13 && !isComposing()) {
      //    const el = event.currentTarget
      //    if (el && el.tagName === 'INPUT' && !el.getAttribute('role')) {
      //      submit()
      //    }
      //  }
      //}
      ret.onCompositionStart = () => {
      }
      ret.onCompositionEnd = () => {
      }
    }
    if (error[key]) {
      ret.state = 'error'
    }
    return ret
  }
  function bindField (key) {
    const ret = {}
    const err = error[key]
    if (err) {
      ret.help = err
      ret.validateState = 'error'
    }
    return ret
  }
  function useValidate (key, fn) {
    validates.push([key, fn])
    function runValidate (strict) {
      Promise.resolve()
        .then(() => fn(data[key], strict))
        .catch((error) => {
          console.error(error)
          return (error && error.message) || error
        })
        .then((result) => {
          setError((e) => {
            return { ...e, [key]: result }
          })
        })
    }
    useEffect(() => {
      runValidate(false)
    }, [data[key]])
    useEffect(() => {
      if (lastBlur === key) {
        runValidate(true)
        setLastBlur('')
      }
    }, [lastBlur])
  }
  function validate (strict, getError) {
    const error = {}
    let hasError = false
    return Promise.all(validates.map(([key, fn]) => {
      return Promise.resolve()
        .then(() => fn(data[key], strict))
        .catch((error) => {
          return (error && error.message) || error
        })
        .then((result) => {
          if (result) {
            hasError = true
            error[key] = result
          }
        })
    }))
    .then(() => {
      setError(error)
      if (getError) {
        return [!hasError, error]
      } else {
        return !hasError
      }
    })
  }
  function submit () {
    if (submitLoading) {
      Message.warning('请不要重复提交')
    } else {
      setSubmitLoading(true)
      validate(true, true)
        .then(([ok, error]) => {
          if (ok) {
            return onSubmit(data)
          } else {
            const errKeys = Object.keys(error)
            if (errKeys.length === 1) {
              Message.error(error[errKeys[0]])
            } else {
              Message.error('表单填写有误，请检查')
            }
          }
        })
        .catch((error) => {
          console.error(error)
          if (error && error.message) {
            Message.error(error.message)
          }
        })
        .then((after) => {
          setSubmitLoading(false)
          if (typeof after === 'function') {
            after()
          }
        })
    }
  }
  function bindSubmit () {
    return {
      onClick: submit,
      loading: submitLoading,
    }
  }
  return {
    value: data,
    error,
    bind,
    bindField,
    set,
    useValidate,//: () => null,
    validate,
    submit,
    bindSubmit,
    submitLoading,
  }
}
