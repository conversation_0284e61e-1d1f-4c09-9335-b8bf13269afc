import React from 'react'
import { TimePicker } from '@alife/next'
import './index.scss'
/*大促氛围*/
export default function TimeRange ({ value, onChange, ...thru }) {
  const [start, end] = value || []
  const change = (key) => {
    if (onChange) {
      return (value) => {
        if (value && typeof value.format === 'function') {
          value = value.format('HH:mm:ss')
        }
        const send = [start, end]
        send[key] = value
        onChange(send)
      }
    }
  }
  return (
    <div className="time-range-picker">
      <TimePicker {...thru} placeholder="开始时间" value={start} onChange={change(0)} />
      -
      <TimePicker {...thru} placeholder="结束时间" value={end} onChange={change(1)} />
    </div>
  )
}
