/**
 * 展开折叠效果的组件
 */
import React from 'react';
import { Animate } from '@alife/next';

import './index.scss'

export class FoldBlock extends React.Component {
  constructor(props) {
    super(props);
    // ['beforeEnter', 'onEnter', 'afterEnter', 'beforeLeave', 'onLeave', 'afterLeave', 'handleToggle'].forEach(method => {
    //   this[method] = this[method].bind(this);
    // });
  }

  beforeEnter = (node) => {
    this.height = node.offsetHeight;
    node.style.height = '0px';
  }

  onEnter = (node) => {
    node.style.height = `${this.height}px`;
  }

  afterEnter = (node) => {
    this.height = null;
    node.style.height = null;
  }

  beforeLeave = (node) => {
    node.style.height = `${this.height}px`;
  }

  onLeave = (node) => {
    node.style.height = '0px';
  }

  afterLeave = (node) => {
    node.style.height = null;
  }

  render() {
    return (
      <div className="fold-block">
        <Animate animation="expand"
          beforeEnter={this.beforeEnter}
          onEnter={this.onEnter}
          afterEnter={this.afterEnter}
          beforeLeave={this.beforeLeave}
          onLeave={this.onLeave}
          afterLeave={this.afterLeave}>
          {this.props.expand ?
            <div className="notice">{this.props.children}</div> :
            null
          }
        </Animate>
      </div>
    );
  }
}
