import React from 'react';
import { PageWrapper } from '@/components/PageWrapper';

import './style.scss';

export function ProgressView({
  intervalTime = 5 * 1000,
  title = '查看进度',
  from = "",
  Header = null,
  ProgressBar = null,
  InfoBar = null,
  Breadcrumb = null,
  Link = null,
  getProgress = () => {}
}) {
  return class Component extends React.Component {
    constructor(props){
      super(props)
      this.state = {
        percent: 0,
        state: 'progressing',
        otherData: {}
      }
    }

    componentDidMount() {

      this.timer = setInterval(async()=>{
        try {
          let { percent, ...otherData } = await getProgress();

          if (percent >= 100 ){
            this.setState({
              state: 'success',
              otherData
            })
            clearInterval(this.timer)
          } else {
            this.setState({
              percent: percent,
              state: 'progressing',
              otherData
            })
          }
        } catch (error) {
          this.setState({
            state: 'failed'
          })
          console.error(error)
          clearInterval(this.timer)
        }

      }, intervalTime)
    }

    componentWillUnmount() {
      clearInterval(this.timer)
    }

    render(){
      let {percent, state, otherData} = this.state;
      // let step = state === 'success' ? 2 : 1

      return (
        <div className="progressPage">
          {from==="invite"&&<div className="nav-wrapper">
            <Breadcrumb>
              <Breadcrumb.Item><Link to={`/storepoolInvite/list`}>{`管理门店池`}</Link></Breadcrumb.Item>
              <Breadcrumb.Item>导出进度查看</Breadcrumb.Item>
            </Breadcrumb>
          </div>}
          <PageWrapper title={title}>
            { Header && <Header state={state} data={otherData}/> }
            <div className="content">
              { ProgressBar && <ProgressBar state={state} percent={percent} data={otherData}/> }
              { InfoBar && <InfoBar state={state} percent={percent} data={otherData}/> }
            </div>
          </PageWrapper>
        </div>
      )
    }
  }
}
