import React from 'react';
import { CascaderSelect as BaseCascaderSelect, Icon } from '@alife/next';
import {queryDataSourceByKeyWord} from '@/adator/api'
import './index.scss';
let searchTimeout;

export class CascaderSelectWithClear extends React.Component {
  constructor(props){
    super(props);
    this.state = {
      dataSource:[]
    }
  }

  onClick = () => {
    this.props.onChange([], []);
  }

  // 模块搜索
  handleSearch = (keyword,key) => {
    let dataSource = this.state.dataSource;
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(async () => {
      if (keyword) {
        try {
          let newDataSource = await queryDataSourceByKeyWord({configType:key,keyword:keyword})
          if (dataSource.length > 0) {
            const originData = dataSource.length > 0 ? dataSource : [];
            const wholeData = newDataSource.concat(originData);
            this.setState({dataSource:Array.from(new Set(wholeData))})
          }else{
            this.setState({dataSource:newDataSource})
          }
        } catch (error) {
          console.log("🚀error", error)
        }
      } else {
        this.setState({dataSource:this.state.dataSource})
      }
    }, 800);
  }

  render(){
    const { value = [], dataSourceName = '' ,name, queryField } = this.props;
    const { dataSource } = this.state;
    const deleteStyle = {
      visibility:  value && value.length ? 'visible' : 'hidden'
    }
    if (dataSourceName.length > 0) {
      let newValue = value.map((item)=>{
        return item.value
      })
      return (
        <div className="CascaderSelect">
          <BaseCascaderSelect {...this.props} value={newValue} hasClear={false}  dataSource={dataSource} onSearch={(value) => this.handleSearch(value,dataSourceName)} onChange={(event)=>{
            let nextValue = dataSource.map((item)=>{
              if (event && event.includes(item.value)) {
                return {
                  label: item.label,
                  value: item.value
                }
              }else{
                return null
              }
            })
            nextValue = nextValue.filter(i => i)
            queryField.setValue(name,nextValue)
          }}/>
          <Icon type="delete-filling" size='small' className="select-delete-filling" onClick={this.onClick} style={deleteStyle}/>
        </div>
      )
    }
    return (
      <div className="CascaderSelect">
        <BaseCascaderSelect {...this.props} />
        <Icon type="delete-filling" size='small' className="select-delete-filling" onClick={this.onClick} style={deleteStyle}/>
      </div>
    )
  }
}