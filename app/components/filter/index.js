import React,{ useContext, createContext } from 'react'
import { Form, Grid, Input, Select } from '@alife/next'

const { Row, Col } = Grid
const FormItem = Form.Item
/*大促氛围*/
export function FilterForm ({ children = [], layout = {}, query = null, ...formProps  }) {
  const { itemPerRow = 3, labelSpan = 10, inputSpan = 14 } = layout
  const labelLayout = {
    style: {
      width: '100%',
    },
    labelCol: {
      span: labelSpan,
    },
    wrapperCol: {
      span: inputSpan,
    },
  }
  const rows = []
  if (children && !Array.isArray(children)) {
    children = [children]
  }
  let curRow
  children.forEach((child, i) => {
    if (i % itemPerRow === 0) {
      curRow = null
    }
    if (!curRow) {
      curRow = []
      rows.push(curRow)
    }
    if (child.type === FilterItem) {
      child = {
        ...child,
        props: {
          ...child.props,
          ...labelLayout,
          query,
        },
      }
    }
    curRow.push(
      <Col span="8" key={curRow.length}>
        {child}
      </Col>
    )
  })
  return (
    <Form inline {...formProps}>
      <Row>
        <Col span="24" l="22">
          {rows.map((row, index) => <Row key={index}>{row}</Row>)}
        </Col>
      </Row>
    </Form>
  )
}

export function FilterItem ({ query, name, label, placeholderPrefix = 'auto', children, ...itemProps }) {
  if (children) {
    if (!Array.isArray(children)) {
      children = [children]
    }
    if (children[0] && children[0].props.placeholder === 'auto') {
      let placeholder = placeholderPrefix
      if (placeholder === 'auto') {
        if (children[0].type === Input) {
          placeholder = '请填写'
        } else if (children[0].type === Select) {
          placeholder = '请选择'
        } else {
          placeholder = '请输入'
        }
      }
      placeholder += label
      children[0] = {
        ...children[0],
        props: {
          ...children[0].props,
          style: {
            ...children[0].props.style,
            width: '100%',
          },
          placeholder,
        },
      }
    }
    if (name && query) {
      children[0] = {
        ...children[0],
        props: {
          ...query.bindInput(name),
          ...children[0].props,
        },
      }
    }
  }
  if (label) {
    return (
      <FormItem label={label} {...itemProps} labelAlign="left">
        {children}
      </FormItem>
    )
  } else {
    const { labelCol = {} , wrapperCol = {} } = itemProps
    return (
      <Row>
        <Col span={labelCol.span || 8}></Col>
        <Col span={wrapperCol.span || 15}>
          {children}
        </Col>
      </Row>
    )
  }
}
