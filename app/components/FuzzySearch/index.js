import React from 'react';
import { Select } from '@alife/next';

const { Option } = Select;

export function FuzzySearch(getData = () => {  }){
  return class FuzzySearch extends React.Component {
    static defaultProps = {
      showSearch:true,
      hasClear: true
    }

    constructor(props){
      super(props);

      this.state = {
        options:[]
      }
    }

    async getOptions(value) {
      try {
        const data = await getData.apply(getData, [value]);

        const options = data.map((item) => <Option value={item.value} key={item.value}>{item.name}</Option>)

        return options;
      } catch(error) {
        console.log(error);
      }
    }

    onSearch = async (value) => {
      try {
        const options = await this.getOptions(value);
        this.setState({options})
      } catch(error) {
        console.log(error);
      }
    }

    render() {
      const { options } = this.state;
      return (
        <Select {...this.props} onSearch={this.onSearch} style={{width: '100%'}}>
          { options }
        </Select>
      )
    }
  }
}
