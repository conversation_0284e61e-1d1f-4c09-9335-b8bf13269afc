import React from 'react';
import clz from 'classnames'
import './style.scss';

export const StatisticsList = ({children = null, className, data = []}) => (
  <div className={clz("statistics-list-wrapper", className)}>
    {data.map((v) => {
      const width = `${100/data.length}%`;
      const num = (v.value === null || v.value == '' || v.value == 0) ? v.value : v.value.toFixed(2);
      return <section style={{width}}>
        <p className='title'>{v.title}</p>
        <p className='num'>{num}</p>
        {v.contrast && <p className='contrast'>对比值{v.contrast}</p>}
        {children}
      </section>
    })}
  </div>
)
