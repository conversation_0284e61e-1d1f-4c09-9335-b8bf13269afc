.page-preview {
  width: 452px;

  .search-wrapper {
    display: flex;

    .input-wrapper {
      flex: 1 1 auto;
      position: relative;

      .city-select {
        width: 100px;
      }

      .serach-result {
        position: absolute;
        height: 364px;
        top: 36px;
        left: 100px;
        right: 0;
        overflow: scroll;
        background: #FFFFFF;
        border: 1px solid #D9D9D9;
        border-radius: 4px;
        padding: 8px 0;
        z-index: 100;

        .result-item {
          padding: 8px 12px;
          cursor: pointer;

          .name {
            font-size: 14px;
            color: #333333;
            line-height: 14px;
            padding-bottom: 4px;
          }

          .address {
            font-size: 12px;
            color: #666666;
            line-height: 14px;
          }
        }

        .no-result {
          padding: 8px 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #ccc;

          .main-text {
            font-size: 14px;
            line-height: 14px;
            padding-bottom: 6px;
          }

          .sub-text {
            font-size: 12px;
            line-height: 14px;
          }
        }
      }
    }

    .serach-btn {
      width: 88px;
      margin-left: 10px;
    }
  }

  .current-location {
    .title {
      font-size: 14px;
      color: #CCCCCC;
      line-height: 14px;
      padding: 10px 0;
    }

    .location-info {
      display: flex;
      align-items: center;
      padding-bottom: 20px;

      .location-icon {
        width: 13px;
        margin-right: 6px;
      }

      .location-text {
        flex: 1 1 auto;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .relocate {
        flex: 0 0 auto;
        font-size: 14px;
        color: #FF7C4D;
        line-height: 14px;
        cursor: pointer;
      }
    }
  }

  .iframe-wrapper {
    display: flex;
    justify-content: center;

    .preview-iframe {
      border: 1px solid #EBEBEB;
      width: 375px;
      height: 400px;
    }
  }
}

.mask-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
