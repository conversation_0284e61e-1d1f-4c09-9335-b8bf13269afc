import React from 'react';
import { Dialog, Input, Select, Button, Message } from '@alife/next';
import { cityList } from './constants'
import './index.scss'
const LOCAITON_IMG = require('../../images/location.png');

const {Option, OptionGroup} = Select;

let BITS = [16, 8, 4, 2, 1];
let BASE32 = "0123456789bcdefghjkmnpqrstuvwxyz";

const defaultPosition = {
  position: {
    lng: 121.382379,
    lat: 31.232878,
  },
  formattedAddress: "近铁城市广场",
  addressComponent: {
    citycode: "021"
  }
}

export default class PagePreview extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentLocation: {}, // 当前定位地址
      selectedCity: "", //当前选择城市
      searchText: "", //搜索信息
      searchResult: [], //搜索结果
      showResult: false, // 是否展示搜索结果
    };
  }

  componentDidMount() {
    this.mapObj = new AMap.Map('container');
    this.getCurrentLocation();
  }

  /**
   * 获取当前定位地址
   * @return {[type]} [description]
   */
  getCurrentLocation = () => {
    let _this = this;
    this.mapObj.plugin('AMap.Geolocation', function() {
      let geolocation = new AMap.Geolocation({
        enableHighAccuracy: true, // 是否使用高精度定位，默认:true
        timeout: 10000, // 超过10秒后停止定位，默认：无穷大
        maximumAge: 0, // 定位结果缓存0毫秒，默认：0
        convert: true, // 自动偏移坐标，偏移后的坐标为高德坐标，默认：true
        showButton: true, // 显示定位按钮，默认：true
        buttonPosition: 'LB', // 定位按钮停靠位置，默认：'LB'，左下角
        buttonOffset: new AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
        showMarker: true, // 定位成功后在定位到的位置显示点标记，默认：true
        showCircle: true, // 定位成功后用圆圈表示定位精度范围，默认：true
        panToLocation: true, // 定位成功后将定位到的位置作为地图中心点，默认：true
        zoomToAccuracy: true // 定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
      });
      _this.mapObj.addControl(geolocation);
      geolocation.getCurrentPosition();
      AMap.event.addListener(geolocation, 'complete', _this.onComplete); // 返回定位信息
      AMap.event.addListener(geolocation, 'error', _this.onError); // 返回定位出错信息
    });
  }
  onComplete = (obj) => {
    let { position, formattedAddress, addressComponent } = obj;
    this.setState({
      currentLocation: {
        geohash: this.encodeGeoHash(position.lat,position.lng),
        name: formattedAddress
      },
      selectedCity: addressComponent.citycode
    })
  }
  onError = (obj) => {
    Message.notice('定位失败，选取默认地址')
    let { position, formattedAddress, addressComponent } = defaultPosition;
    this.setState({
      currentLocation: {
        geohash: this.encodeGeoHash(position.lat,position.lng),
        name: formattedAddress
      },
      selectedCity: addressComponent.citycode
    })
  }

  /**
   * 地点搜索相关
   */
  handleInputChange = (value) => {
    this.setState({searchText:value});
  }
  search = () => {
    let { selectedCity,searchText } = this.state;
    let _this = this;
    AMap.plugin('AMap.Autocomplete', function() {
      // 实例化Autocomplete
      var autoOptions = {
        city: selectedCity,
        datatype: "poi",
        citylimit: true,
      }
      var autoComplete = new AMap.Autocomplete(autoOptions);
      autoComplete.search(searchText, function(status, result) {
        // 搜索成功时，result即是对应的匹配数据
        _this.setState({
          showResult: true,
          searchResult: result.tips && result.tips.length > 0 ? result.tips : []
        })
      })
    })
  }

  /**
   * 下拉城市列表相关
   */
  renderOptions = () => {
    let optionList = [];
    for (var key in cityList) {
      if (cityList.hasOwnProperty(key)) {
        optionList.push({
          label: key,
          list:cityList[key]
        })
      }
    }
    return optionList.sort((a,b)=>a.label>b.label?1:-1).map(item=>{
      return <OptionGroup label={item.label}>
        {item.list.map(_item=>{
          return <Option value={_item[5]}>{_item[1]}</Option>
        })}
      </OptionGroup>
    })
  }
  handleSelectChange = (value, actionType, item) => {
    this.setState({ selectedCity: value });
  }

  /**
   * 更改当前定位
   */
  changeLocation = (item) => {
    let { name, location } = item;
    if (location.lng&&location.lat) {
      this.setState({
        currentLocation: {},
        searchResult: [],
        showResult: false,
      }, () => {
        this.setState({
          currentLocation: {
            geohash: this.encodeGeoHash(location.lat, location.lng),
            name
          },
          searchText: name,

        })
      })
    }
  }

  /**
   * 重新定位-恢复默认定位
   */
  relocate = () => {
    this.setState({
      currentLocation: {},
      selectedCity:"",
      searchText: ""
    },()=>{
      this.getCurrentLocation()
    })
  }

  /**
   * 经纬度转化为geohash
   */
  encodeGeoHash = (latitude, longitude) => {
  	var is_even=1;
  	var i=0;
  	var lat = []; var lon = [];
  	var bit=0;
  	var ch=0;
  	var precision = 12;
  	let geohash = "";

  	lat[0] = -90.0;  lat[1] = 90.0;
  	lon[0] = -180.0; lon[1] = 180.0;

  	while (geohash.length < precision) {
  	  if (is_even) {
  			let mid = (lon[0] + lon[1]) / 2;
  	    if (longitude > mid) {
  				ch |= BITS[bit];
  				lon[0] = mid;
  	    } else
  				lon[1] = mid;
  	  } else {
  			let mid = (lat[0] + lat[1]) / 2;
  	    if (latitude > mid) {
  				ch |= BITS[bit];
  				lat[0] = mid;
  	    } else
  				lat[1] = mid;
  	  }

  		is_even = !is_even;
  	  if (bit < 4)
  			bit++;
  	  else {
  			geohash += BASE32[ch];
  			bit = 0;
  			ch = 0;
  	  }
  	}
  	return geohash;
  }

  hideResult = () => {
    this.setState({
      showResult: false,
    })
  }

  render() {
    let { currentLocation, selectedCity, searchText, searchResult, showResult } = this.state;
    let { visible, title, onClose, previewSrc, join } = this.props;

    const select = (
      <Select
        aria-label="please select"
        value={selectedCity}
        onChange={this.handleSelectChange}
        autoWidth={false}
        popupStyle={{width:"221px",height:"367px"}}
        className="city-select"
        showSearch >
          {this.renderOptions()}
      </Select>
    );

    return (
      <Dialog
        title={title+"-预览"}
        visible={visible}
        height="614px"
        onClose={onClose}
        footer={false}
        >
        <div className="page-preview">
          <div className="search-wrapper">
            <div className="input-wrapper">
              <Input
                addonBefore={select}
                aria-label="please input"
                id='tipinput'
                value={searchText}
                placeholder="小区/商场/学校等"
                onChange={this.handleInputChange}/>
              {showResult>0&&<div className="serach-result">
                {searchResult.map(item=>{
                  let { name, district, address } = item;
                  return <div className="result-item" onClick={()=>this.changeLocation(item)}>
                    <div className="name">{name}</div>
                    <div className="address">{district+address}</div>
                  </div>
                })}
                <div className="no-result">
                  <span className="main-text">找不到地址？</span>
                  <span className="sub-text">请尝试只输入小区、商场或学校名，<br/>详细地址（如门牌号）可稍后输入</span>
                </div>
              </div>}
            </div>
            <Button onClick={this.search} className="serach-btn" type="secondary" disabled={!searchText}>搜索</Button>
          </div>
          <div className="current-location">
            <div className="title">当前定位</div>
            <div className="location-info">
              <img src={LOCAITON_IMG} alt="" className="location-icon"/>
              <div className="location-text">{currentLocation.name}</div>
              <div className="relocate" onClick={this.relocate}>重新定位</div>
            </div>
          </div>
          <div className="iframe-wrapper">
            {currentLocation.geohash&&<iframe
              src={`${previewSrc}${join}geohash=${currentLocation.geohash}`}
              frameBorder="0"
              className="preview-iframe">
            </iframe>}
          </div>
          <div id="container"></div>
        </div>
        {showResult&&<div className="mask-wrapper" onClick={this.hideResult}></div>}
      </Dialog>
    )
  }
}
