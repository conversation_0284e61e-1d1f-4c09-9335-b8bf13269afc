import React from 'react';
import { Table } from '@alife/next';

/**
 * @param {Array} columnsAr:表头列表参数
 * @param  {Function} getColFuns:columnsAr的处理函数
 */
function tableColumns(columnsAr, getColFuns) {
  return columnsAr.map(
    (col) => getColFuns.reduce((colType, fun) => fun.apply(fun, [colType]), col)
  ).map((colType) => {
    const { cell } = colType;
    if(cell){
      colType.cell = (value, index, record) => cell.apply(this, [value, index, record])
    }
    return <Table.Column
      {...colType}
      key={colType.title}
    />
  }
  )
}

/**
 * 更具传入的表头渲染Table
 * @param {array} cols: 表头数据
 * @param {*} getColFun:处理表头数据的函数，非必须填
 */
export function setTableCol(cols, ...getColFun) {

  return function TableList(props) {
    let ncols = cols
    if (typeof cols === 'function'){
      ncols = cols(this)
    }
    return (
      <Table dataSource={props.dataSource} hasBorder={false} style={{ marginTop:'20px' }} className='manage-list'>
        { tableColumns.apply(this, [ncols, getColFun]) }
      </Table>
    )
  }
}
