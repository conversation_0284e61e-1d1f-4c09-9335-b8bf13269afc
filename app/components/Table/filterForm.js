import React from 'react';
import { Field } from '@alife/next';

export function filterForm(FormBody, filters) {
  return class FilterForm extends React.Component {
    constructor(props) {
      super(props);

      this.field = new Field(this);
    }

    get query() {
      const { getValue } = this.field;
      return filters.reduce((query, search) => ({ ...query, [search]: (getValue(search) || getValue(search) == 0) ? getValue(search) : ''}), {})
    }

    reload = () => {
      const blankVal = filters.reduce((query, search) => ({ ...query, [search]:'' }), {})

      this.field.setValues(blankVal)
      this.props.reload();
    }

    render() {
      const {searchData} = this.props;
      return (
        <FormBody {...this.props} field={this.field} searchData={()=> searchData(this.query)} reload={this.reload} />
      )
    }
  }
}
