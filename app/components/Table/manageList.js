import React from 'react';
import { Pagination } from '@alife/next';


import './style.scss';

const LIMIT = 10;
export function manageList(TableComponent, FilterFormComponent, fetchData, settings = {}) {
  return class List extends React.Component {
    constructor(props) {
      super(props);
      this.state = {
        dataSource: [],
        pagination: {
          page: 1,
          total: 0,
          size: settings.size || LIMIT
        }
      }

      this.query = {}
    }

    async componentDidMount(){
      const { pagination: { page, size } } = this.state;
      await this.fetchData({ page, size, query:{} });
    }

    // 分页器
    onPageChange = (page) => {
      const {query} = this;
      this.fetchData({page, query}, this.state.reservedParams);
    }

    onPageSizeChange = (size) => {
      this.fetchData({size}, this.state.reservedParams)
    }
    // 查询列表数据
    searchData = (query) => {
      this.fetchData({page: 1, query}, this.state.reservedParams)

      this.query = {...this.query, ...query}
    }
    reload = () => {
        this.query = {}
        this.onPageChange(this.state.pagination.page)
    }

    // 获取数据
    fetchData = async ({page, size, query}, reservedParams) => { // reservedParams 预留的其他参数
      const { pagination } = this.state;
      page = page || pagination.page
      size = size || pagination.size
      query = query || this.query

      const params = {page, size, query}
      const {rows, total} = await fetchData.apply(this, [params,reservedParams])
      this.setState({
        rows,
        query,
        reservedParams,
        pagination: {
          ...pagination,
          total,
          page,
          size
        }
      },()=>{
        this.props.handleSource && this.props.handleSource(this.state);
      })
    }

    render() {
      const { rows, pagination } = this.state;
      return (
        <div className='filter-list'>
          <FilterFormComponent {...this.props} searchData={this.searchData} reload={this.reload} />
          { this.props.children }
          {/*{ React.cloneElement(this.props.children, {dataSource: rows}) }*/}
          { TableComponent.apply(this, [{dataSource: rows}]) }
          {/* <TableComponent dataSource={rows} searchData={this.searchData}/> */}
          <div className="pagination" style={{textAlign: 'right', marginTop: '20px'}}>
            <Pagination
              popupProps = {{align:'bl tl'}}
              current={pagination.page} total={pagination.total} pageSize={pagination.size}
              totalRender={() => `共${pagination.total}条`}
              onChange={this.onPageChange} pageSizeSelector="dropdown" pageSizePosition="end"
              onPageSizeChange={this.onPageSizeChange} />
          </div>
        </div>
      )
    }
  }
}
