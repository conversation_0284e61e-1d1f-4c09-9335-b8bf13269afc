import React from 'react';
import {Upload, Icon} from '@alife/next';
import './index.scss';
const Hint = '支持扩展名：.xlsx，.xls，.txt';
const Accept = 'text/plain,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
export default class UploadDragger extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hint: props.hint || Hint,
      accept: props.accept || Accept
    }
  }

  onDragOver = () => {
    console.log('dragover callback');
  }

  onDrop = (fileList) => {
    console.log('drop callback : ', fileList);
  }

  downloadUrl = (e) => {
    e.stopPropagation();
    location.href = 'http://bwm-icms.oss-cn-hangzhou.aliyuncs.com/b/wmcrm/bannerTemplate.xlsx';
  }

  render() {
    let {Hint, accept} = this.state;
    return (
      <div className='upload-source'>
        <Upload.Dragger
          listType="text"
          action=""
          accept={accept}
          onDragOver={this.onDragOver}
          onDrop={this.onDrop}
        >
          <div className="upload-text">
            <Icon type="upload" style={{color: '#333333'}} size={'xl'}/>
            <p className='download'>点击<a onClick={(e) => this.downloadUrl(e)}>下载表格模板</a>或将文件拖拽到这里上传</p>
            <p className='hint'>{Hint}</p>
          </div>
        </Upload.Dragger>
      </div>
    )
  }
}
