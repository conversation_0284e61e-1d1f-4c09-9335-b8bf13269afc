.preview-img-wrapper {
  width: 60px; height: 60px;
  position: relative;
  border-radius: 4px;
  overflow: hidden;

  .preview-img{
    width: 60px;
    height: 60px;
  }

  img {
    width: 100%;
  }

  &:hover {
    .bar {
      transform: translateY(0);
    }
  }

  .bar {
    transform: translateY(100%);
    transition: all 300ms ease-in-out;
    cursor: pointer;
    text-align: center;
    position: absolute;
    width: 100%;
    height: 20px;
    bottom: 0;
    background-color: rgba(0,0,0,0.40);
    color: white;
    font-size: 12px;
  }
}

.g-preview-dialog {
  .next-dialog-body {
    padding: 0;
  }
}

.g-imgpreview-content {
  width: 400px;
  max-height: 420px;
  padding: 0 20px 20px 20px;

  .title {
    margin: 10px 0;
    color: #333333;
    font-size: 16px;
  }

  .img-wrapper {
    width: 360px; max-height: 360px;
    img {
      object-fit: cover;
      width: 100%;
      max-height: 100%;
    }
  }
}