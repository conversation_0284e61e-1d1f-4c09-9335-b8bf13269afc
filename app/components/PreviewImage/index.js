import React from 'react';
import { Dialog } from '@alife/next';

import './index.scss';

export default class PreviewImage extends React.Component {
  constructor(props){
    super(props);

    this.state = {
      isDialogPreviewVisible: false
    }
  }

  onPreviewClicked = () => {
    this.setState({isDialogPreviewVisible: true})
    this.props.onPreviewClicked();
  }

  onPreviewClose = () => {
    this.setState({ isDialogPreviewVisible: false })
  }

  render(){ 
    const { src, title } = this.props;
    const { isDialogPreviewVisible } = this.state;

    return (
      <div>
        <div className="preview-img-wrapper">
          <img src={src} className="preview-img"/>
          <div className="bar" onClick={this.onPreviewClicked}>预览</div>
        </div>
        <Dialog
          className="g-preview-dialog"
          visible={isDialogPreviewVisible}
          closeable="mask"
          footer={false}
          onClose={this.onPreviewClose} >
          <div className="g-imgpreview-content">
            <div className="title">{title}</div>
            <div className="img-wrapper">
              <img src={src} />
            </div>
          </div>
        </Dialog>
      </div>
    )
  }
}