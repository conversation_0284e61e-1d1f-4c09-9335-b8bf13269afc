import React from 'react';

import './index.scss';

const DEFAULT_DATA = {
  rediectUrl: ''
}

const ToPermission = function ({rediectUrl, msg}) {
  return (
    <div>
      {(rediectUrl != "") ? <h5 className="without_permission">您当前没有权限，点击<a href={rediectUrl}>申请权限</a></h5>: <h5 className="without_permission">{msg}</h5> }
    </div>
  )
}

export function permissionAccess(Component, getPermission = () => {}){ //页面级别
  return class NewComponent extends React.Component {
    constructor(props){
      super(props);

      this.state = {
        withPermission: true,
        rediectUrl: ''
      }
    }

    getPermission = async (...params) => {
      const data = await getPermission.apply(this, params)
      const {rediectUrl} = {...DEFAULT_DATA, ...data};
      const {msg} = {...data};
      let withPermissionValue = (rediectUrl != "") ? !rediectUrl : !msg
      this.setState({
        msg,
        withPermission: withPermissionValue,
        rediectUrl
      })

      return {withPermission: withPermissionValue}
    }

    render(){
      const {rediectUrl, withPermission, msg} = this.state;
      if (withPermission) return <Component {...this.props} getPermission={this.getPermission}/>
      return <ToPermission rediectUrl={rediectUrl} msg={msg}/>
    }
  }
}
