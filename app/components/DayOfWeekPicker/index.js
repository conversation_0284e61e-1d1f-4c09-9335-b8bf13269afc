import { Grid, Input, Select, DatePicker, Checkbox, Tag } from '@alife/next'
const { Row, Col } = Grid
import React, { useState } from 'react'

/*大促氛围*/
export default function DayOfWeekPicker ({ value, onChange }) {
  if (!onChange) {
    ;([value, onChange] = useState([]))
  }
  if (!value) {
    value = []
  }
  const full = value.length === 7
  const onChangeFull = (isFull) => {
    if (isFull) {
      onChange([1, 2, 3, 4, 5, 6, 7])
    } else {
      onChange([])
    }
  }
  const onCheck = (v) => {
    return (checked) => {
      if (checked) {
        onChange([v, ...value].sort())
      } else {
        const index = value.indexOf(v)
        if (index > -1) {
          onChange([...value.slice(0, index), ...value.slice(index + 1)])
        }
      }
    }
  }
  return (
    <Row className='dayOfWeek-picker'>
      <Col fixedSpan="3">
        <Checkbox checked={full} onChange={onChangeFull}>全选</Checkbox>
      </Col>
      <Col>
        <div style={{ maxWidth: 300 }}>
          {['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'].map((day, i) => {
            const v = i + 1
            return (
              <span key={i}>
                <Tag.Selectable size="small" checked={value.indexOf(v) > -1} onChange={onCheck(v)}>
                  {day}
                </Tag.Selectable>
              </span>
            )
          })}
        </div>
      </Col>
    </Row>
  )
}
