.trend-chart{
  .next-select{
    .next-input{
      border-color: #FF7C4D;
      &.next-select-inner{
        color: #FF7C4D;
        .next-input-control .next-icon{
          color: #FF7C4D;
        }
      }
      .next-input-text-field{
        border-right:1px solid #FF7C4D;
        margin-right:4px;
      }
      &.next-disabled{
        border-color: #CCCCCC;
        .next-input-text-field{
          border-right:1px solid #CCCCCC;
        }
        .next-input-control .next-icon{
          color: #CCCCCC;
        }
      }
    }

    .next-input-control .next-icon{
      color: #FF7C4D;
    }
    //.next-menu-item{
    //  color: #FF7C4D;
    //}
  }
  .noData{
    width:100%;
     height:100%;
    background:url("../../images/nodata-bg.png") no-repeat center 46%;
    background-size:108px 55px;
    position: relative;
    h3{
      position:absolute;
      top:0;
      left:0;
      font-size: 20px;
      color: #333333;
    }
    .content{
      position:absolute;
      top:56%;
      text-align:center;
      width:100%;
      font-size: 14px;
      color: #CCCCCC;
    }
  }
}
