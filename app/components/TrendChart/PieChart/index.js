import React from 'react';
import { TrendChart } from '@/components/TrendChart/trendChart';
import 'echarts/lib/chart/pie'
import './index.scss'

/*
* 饼图
* */
export class Pie<PERSON>hart extends React.Component {
  constructor(props){
    super(props);
  }

  getBaseOption = (settings) =>{
    return {
      title: {
        text:settings.title,
        x: 'left',
        textStyle:{
          fontFamily:'PingFangSC-Regular',
          fontSize:'20',
          fontWeight:'normal'
        }
      },
      tooltip : {
        trigger: 'item',
        formatter: "{a} <br/>{b} : {c} ({d}%)"
      },
      color:settings.colors,
      legend: {
        x: '70%',
        y: '30%',
        orient: 'vertical',
        data:settings.legendData,
        icon:'circle'
      },
      calculable: true,
      series: [
        {
          name:settings.title,
          type: 'pie',
          radius: [60, 110],
          center: ['33%', '65%'],
          roseType: 'radius',
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: true
            }
          },
          labelLine: {
            normal: {
              show: false
            },
            emphasis: {
              show: true
            }
          },
          data:settings.seriesData
        }
      ]
    };

  }

  render() {
    const {
      width,
      height,
      colors = ['#26DAD0', '#5B89FF','#7C69F2','#C95FF2','#FF6382','#FF9F40','#FFCB48','#31DC72','#FF6A00','#18B1F7'],
      chartData,
      noDataContent = '选择唯一投放活动，才可展示趋势图',
      noDataHeight,
      title = '类型占比'
    } = this.props;
    const attribute = { width, height, title};
    let legendData = [];
    let seriesData = [];
    if(chartData) {
      legendData = chartData.legendData; //['服饰', '家具']
      seriesData = chartData.seriesData; // [{value: 10, name: '服饰'},{value: 5, name: '家具'}]
    }
    const option = {
      ...this.getBaseOption({
        title,
        colors,
        legendData,
        seriesData
      })
    }
    return <div className="pie-chart trend-chart"  style={{width, height}}>
      {this.props.children}
      {chartData ?
        <TrendChart option={option}  {...attribute} /> :
        <div className='noData'  style={{height:noDataHeight}}>
          <h3>{title}</h3>
          <p className='content'><span>{noDataContent}</span></p>
        </div>}
    </div>
  }
}
