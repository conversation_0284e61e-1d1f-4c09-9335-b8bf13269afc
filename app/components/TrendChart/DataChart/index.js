import React from 'react';
import { TrendChart } from '@/components/TrendChart/trendChart';
import 'echarts/lib/chart/bar'
import './index.scss'

/*
* 数据集的图
* */
export class DataChart extends React.Component {
  constructor(props) {
    super(props);
  }

  getBaseOption = (settings) =>{
    return {
      title: {
        text:settings.title,
        textStyle:{
          fontFamily:'PingFangSC-Regular',
          fontSize:'20',
          fontWeight:'normal'
        }
      },
      grid: {
        left: '1%',
        right: '5%',
        bottom: '3%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter:settings.formatter || null
      },
      // tooltip : {
      //   trigger: 'axis',
      //   axisPointer: {
      //     type: 'cross',
      //     label: {
      //       backgroundColor: '#6a7985'
      //     }
      //   }
      // },
      xAxis: {
        type: 'value',
        boundaryGap: [0, 0.01],
        axisLabel:{
          show:false,
        },
        axisLine: {
          show: false,
        },
        splitLine:{
          show:false
        },
        axisTick: {
          show: false
        },
      },
      yAxis: {
        type: 'category',
        axisLine: {
          lineStyle:{
            color:'#C4C6CF'
          }
        },
        axisLabel:{
          // interval: 0,
          // rotate:30,
          color:'#666',
          formatter: function (name) {
            return (name.length > 4 ? (name.slice(0,4)+"...") : name );
          },
        },
        axisTick: {
          show: false
        },
        data:settings.yAxisData
      },
      series: [
        {
          name: '',
          type: 'bar',
          barMaxWidth:'10',
          itemStyle:{
            color:'#FF6A00',
          },
          label: {
            normal: {
              show: true,
              position: 'right',
              color:'#000'
            }
          },
          data:settings.seriesData
        }
      ]
    };
  }

  render() {
    const {
      width,
      height,
      title,
      chartData,
      noDataContent = '选择唯一投放活动，才可展示趋势图',
      noDataHeight
    } = this.props;
    const attribute = {
      width,
      height,
      title
    }
    let yAxisData = [];
    let seriesData = [];
    let formatter;
    if(chartData) {
      yAxisData = chartData.yAxisData;
      seriesData = chartData.seriesData;
      formatter = chartData.formatter;
    }
    const option = {
      ...this.getBaseOption({
        title,
        yAxisData,
        seriesData,
        formatter
      })
    }
    return <div className="data-chart trend-chart" style={{width,height}}>
      {this.props.children}
      {chartData ?
        <TrendChart option={option}  {...attribute} /> :
        <div className='noData' style={{height:noDataHeight}}>
          <h3>{title}</h3>
          <p className='content'><span>{noDataContent}</span></p>
        </div>}
    </div>
  }
}
