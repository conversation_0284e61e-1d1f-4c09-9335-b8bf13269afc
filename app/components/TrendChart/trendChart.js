import React from 'react';
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/component/title';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import './index.scss'
export class TrendChart extends React.Component {
  constructor(props){
    super(props);
    this.state = {
      title:this.props.title || ''
    }
  }

  initChart = () => {
    const { option={} } = this.props;
    let myChart = echarts.init(this.ID);
    myChart.setOption(option)
    window.onresize = function() {
      myChart.resize()
    }
  }

  componentDidMount() {
    this.initChart()
  }

  componentDidUpdate() {
    this.initChart()
  }

  render() {
    const { width="100%", height="400px" } = this.props;
    return <div ref={ID => this.ID = ID} style={{width, height}}></div>
  }
}


