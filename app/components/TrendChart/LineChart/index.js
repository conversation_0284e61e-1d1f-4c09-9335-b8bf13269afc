import React from 'react';
import { TrendChart } from '@/components/TrendChart/trendChart';
import 'echarts/lib/chart/line'
import './index.scss'

/*
* 折线图
* */
export class LineChart extends React.Component {
  constructor(props){
    super(props);
  }

  getBaseSeriesData = (colors, index) => {
    return {
      type: 'line',
      smooth: true,
      areaStyle: {
        opacity: 0.4,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: colors[index] // 0% 处的颜色
          }, {
            offset: 0.7, color: '#fff' // 100% 处的颜色
          }],
          global: false // 缺省为 false
        }
      }
    }
  }

  getBaseOption = (settings) =>{
    return {
      title: {
        show:true,
        text: settings.title,
        textAlign:'left',
        top:'0',
        textStyle:{
          fontFamily:'PingFangSC-Regular',
          fontSize:'20',
          fontWeight:'normal'
        }
      },
      tooltip : {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      grid: {
        top:90,
        bottom:50,
        left:80,
        right:20
      },
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: "#666"
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: ['#EBECF0']
            }
          },
        }
      ],
      color: settings.colors,
      legend: {
        show:settings.showLegend,
        top:'40',
        left:'left',
        data:settings.legendData,
        icon:'stack'
      },
      xAxis: [
        {
          type: 'category',
          axisLine: {
            show: false,
            lineStyle: {
              color: "#666"
            }
          },
          data:settings.xAxisData
        }
      ],
      series:settings.newSeriesData
    }
  }

  render() {
    const {
      width,
      height,
      title,
      showLegend = true,
      colors = ['#FF7C4D', '#88DBA3'],
      chartData,
      noDataContent = '选择唯一投放活动，才可展示趋势图',
      noDataHeight
    } = this.props;
    const attribute = { width, height, title};
    let newSeriesData = [];
    let legendData = [];
    let xAxisData = [];
    if(chartData) {
      legendData = chartData.legendData; //['主活动']
      xAxisData = chartData.xAxisData; //["08-23", "08-24"]
      chartData.seriesData.map((v,index) => { //[[800, 580]]
        newSeriesData.push({
          ...this.getBaseSeriesData(colors,index),
          ...v
        },)
      })
    }
    const option = {
      ...this.getBaseOption({
        title,
        colors,
        showLegend,
        legendData,
        xAxisData,
        newSeriesData
      })
    }
    return <div className="line-chart trend-chart" style={{width, height}}>
      {this.props.children}
      {chartData ?
        <TrendChart option={option}  {...attribute} /> :
        <div className='noData' style={{height:noDataHeight}}>
          <h3>{title}</h3>
          <p className='content'><span>{noDataContent}</span></p>
        </div>}
    </div>
  }
}
