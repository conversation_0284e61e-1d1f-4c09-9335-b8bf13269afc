/**
 * 列表查询
 */
import React, { useState, useEffect } from 'react'
import { Message } from '@alife/next'
import {withRouter} from 'react-router-dom';
// import createHistory from 'history/createBrowserHistory';

function emptyResult () {
  return { total: 0, list: [] }
}

const defaultPagination = {
  current : 1,
  pageSize: 10,
}

const noop = () => {}

let resultCache
let resultCacheKey

export default function useTableQuery (history, request = noop, defaultQuery = {}, queryOnStart = false) {
  let filterQuery = defaultQuery
  let pagination = defaultPagination
  const { filter: qFilter, page: qPage } = history.location.query
  if (qFilter) {
    filterQuery = JSON.parse(qFilter)
  }
  if (qPage) {
    pagination = JSON.parse(qPage)
  }
  function setFilterQuery (value) {
    setUrlQuery({ filter: JSON.stringify(value) })
  }

  function setPagination (value) {
    setUrlQuery({ page: JSON.stringify(value) })
  }

  function setUrlQuery (query) {
    query = {
      ...history.location.query,
      ...query,
      __nounce: Math.random(),
    }
    const search = Object.keys(query).map((k) => [k, query[k]].map(encodeURIComponent).join('=')).join('&');
    let url = history.location.pathname + '?' + search;
    let queryFilter = query.filter ? JSON.parse(query.filter) : '';
    let queryPage = query.page ? JSON.parse(query.page) : '';
    if(queryFilter.showType){
      sessionStorage.setItem("showType",queryFilter.showType);
    }
    searchResult(queryFilter, queryPage);
    history.replace(url);
  }

  function searchResult(filterQuery,pagination) {
    if (qFilter || qPage || queryOnStart) {
      const query = { ...filterQuery }
      Object.keys(query).forEach((key) => {
        if (query[key] === '') {
          delete query[key]
        }
      })
      if(query && query.showType){
        sessionStorage.setItem("showType",query.showType);
      }
      Promise.resolve(request(query, pagination.current, pagination.pageSize))
        .then((result) => {
          if (result && result.list && typeof result.total === 'number') {
            if (result.list.length === 0 && result.total > 0) {
              setPagination({
                ...pagination,
                current: 1,
              })
            } else {
              setQueryResult(result)
            }
          } else {
            throw new Error('查询结果必须是 { total: 0, list: [...] }')
          }
        })
        .catch((err) => {
          console.error(err)
          Message.error('请求服务异常')
        })
    }
  }

  const [filterInput, setFilterInput] = useState(filterQuery)     // 用户输入的部分

  const defaultResult = resultCacheKey === history.location.pathname ? resultCache : emptyResult
  const [queryResult, setQueryResult] = useState(defaultResult)   // 查询结果
  resultCache = queryResult
  resultCacheKey = history.location.pathname

  useEffect(() => {
    setUrlQuery({ filter: JSON.stringify(filterQuery) });
    searchResult(filterQuery,pagination);
  }, [0])

  const setField = (obj, sendRequest = false) => {
    setFilterInput({
      ...filterInput,
      ...obj,
    })
    if (sendRequest) {
      setFilterQuery({
        ...filterQuery,
        ...obj,
      })
    }
  }

  const doQuery = () => {
    setUrlQuery({})
  }

  return {
    filterInput,
    filterQuery,
    doQuery,
    result: queryResult,
    pagination: {
      ...pagination,
      pageSizeSelector: 'dropdown',
      popupProps: {
        align: 'bl tl',
      },
      pageSizeList: [10, 20, 50],
      pageSizePosition: 'end',
      style: { textAlign: 'right' },
      total: queryResult.total,
      onPageSizeChange (value) {
        const index = (pagination.current - 1) * pagination.pageSize
        const current = Math.floor(index / value) + 1
        setPagination({ ...pagination, current, pageSize: value })
      },
      onChange (value) {
        setPagination({ ...pagination, current: value })
      },
    },
    setField,
    confirmInput () {
      setFilterQuery(filterInput)
    },
    resetInput (sendRequest) {
      setFilterInput(defaultQuery)
      if (sendRequest) {
        setFilterQuery(defaultQuery)
      }
    },
    bindInput (key) {
      return {
        value: filterInput[key],
        onChange (value) {
          if (value && typeof value === 'object' && 'value' in value) {
            value = value.value
          }
          setField({ [key]: value })
        },
      }
    },
  }
}


