import React from 'react';
import { DatePicker } from '@alife/next';

const { RangePicker } = DatePicker

export class TimeRangePicker extends React.Component {
  constructor(props){
    super(props)
  }

  onChange = (value) => {
    const [ from, to ] = value;
    
    if(from && to) {
      if(to.get('seconds') == 0 && to.get('minute') == 0 && to.get('hour') == 0){
        to.set('hour', 23);
        to.set('minute', 59);
        to.set('seconds', 59);
      }
    }

    this.props.onChange([from, to]);
  }

  render(){
    return (
      <RangePicker {...this.props} onChange={this.onChange}/>
    )
  }
}