import React, { Fragment } from 'react';
import { Dialog, Field } from '@alife/next';

const dialogProps = {
  title:'确认',
  autoFocus: true,
  cancelProps: { 'aria-label': '取消' },
  okProps: { 'aria-label': '确认' },
  style:{width: '400px'}
}

export class DialogBtn extends React.Component {
  static defaultProps = {
    title:'',
    onClick:() => {}
  }

  constructor(props){
    super(props);

    this.state = {
      setVisible: false
    }
  }

  switchSetDialog = (isOpen) => {
    this.setState({ setVisible: isOpen })
  }

  onCancel = () => {
    this.switchSetDialog(false);
  }

  onOpen = () => {
    this.props.onClick();
    this.switchSetDialog(true);
  }

  onOk = async () => {
    await this.switchSetDialog(false);
    this.props.onOk();
  }

  render(){
    const { setVisible } = this.state;
    const {content, ...setProps} = this.props;
    const props = { ...dialogProps, ...setProps }
    return (
      <Fragment>
        <Dialog
          {...props}
          visible={setVisible}
          onOk={this.onOk}
          onCancel={this.onCancel}
          onClose={this.onCancel}>
          { content }
        </Dialog>
        <span onClick={this.onOpen}>{this.props.children}</span>
      </Fragment>
    )
  }
}

export function DialogBtnDecoration(Content) {
  return class Component extends React.Component {
    constructor(props){
      super(props);

      this.field = new Field(this);
    }


    onOk = () => {
      this.props.onOk(this.field);
    }

    render() {
      return (
        <DialogBtn {...this.props} onOk={this.onOk} content={<Content field={this.field} {...this.props}/>} />
      )
    }
  }
}
