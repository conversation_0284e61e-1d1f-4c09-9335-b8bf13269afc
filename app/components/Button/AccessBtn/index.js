import React from 'react';
import {Dialog,Button} from '@alife/next';
import './index.scss';

/*
* 权限按钮
* */

export class AccessBtn extends React.Component {
  constructor(props){
    super(props);
    this.state = {
      btnText:props.btnText, // 按钮文案
      btnType:props.btnType || 1,  // 1:申请权限弹窗 ， 2：直接弹msg(比如提示无管理员)   （默认为1）
      // disabled:props.disabled || false, //按钮是否禁用
      // text: props.text ? false : true, //是否为文本按钮
    }
  }

  handleOps = async () => {
    const {btnType} = this.state;
    const data = await this.props.getPermission.apply(this);
    if (data != null) {
      let msg = <p className='without_permission_text'>您当前没有权限，点击<a href={data} target='_blank'>申请权限</a></p>;
      let title = '提示';
      if (btnType == 2) {
        msg = <p style={{margin:'20px 0'}}>{data}</p>;
        title = '申请权限';
      }
      Dialog.confirm({
        title: title,
        footer: false,
        content: [msg],
      })
    } else {
      this.props.callback();
    }
  }

  render(){
    let {className, type = 'primary', text = true} = this.props;
    let {btnText} = this.state;
    return (
      <Button {...this.props} type={type} text={text} className={className} onClick={this.handleOps}>{btnText}</Button>
    )
  }
}
