import "./index.scss";

import React from "react";
import { v4 as uuid4 } from "uuid";
import debugFn from "debug";
import { Button, Icon, Message, NumberPicker, Select } from "@alife/next";

const debug = debugFn("selection:pooling:MktConditions");
const genKey = () => uuid4();

function isEmpty(v) {
  return v === "" || v === null || v === undefined;
}

function parseValueIfNeed(_value) {
  let value = undefined;
  if (Array.isArray(_value)) {
    value = _value;
  } else if (typeof _value === "string" && _value) {
    try {
      value = JSON.parse(_value);
    } catch (e) {
      console.error(e);
    }
  }
  if (Array.isArray(value)) {
    value = value.map((item) => {
      return { ...item, key: item.key || genKey() };
    });
  }
  debug("parseValueIfNeed", { input: _value, result: value });
  return value;
}

/**
 * 年货节买一赠一营销玩法圈品定制组件
 * PRD: https://alidocs.dingtalk.com/i/nodes/R1zknDm0WR3ArzaXuaklqZXwVBQEx5rG?utm_scene=team_space
 */
export function MktConditions(props) {
  const { value: _value, dataSource, onChange, limit = 5 } = props;
  let value = parseValueIfNeed(_value);
  const selectedActs = (value || []).map((item) => item.value);
  debug("render", { props, dataSource });

  function updateCond(key, partial) {
    onChange &&
      onChange(
        value
          ? value.map((cond) => {
              if (cond.key === key) {
                return { ...cond, ...partial };
              } else {
                return cond;
              }
            })
          : undefined,
      );
  }

  return (
    <div>
      {(value || []).map((item) => (
        <div key={item.key} className="xt-mkt-condition">
          <div className="xt-act">
            玩法：
            <Select
              hasClear
              style={{ width: 150 }}
              dataSource={dataSource.map((op) => {
                // const disabled = selectedActs.includes(op.value);
                // return { ...op, disabled };
                // 统一个玩法可以被选择多次，这里不用对选项进行禁用了
                return op;
              })}
              mode="single"
              placeholder="请选择"
              value={item.value}
              onChange={(v) => updateCond(item.key, { value: v })}
            />
          </div>
          <div className="xt-config">
            条件：
            <span>门槛</span>
            <NumberPicker
              style={{ marginLeft: 8, width: 64, marginRight: 8 }}
              value={item.condition}
              onChange={(condition) => updateCond(item.key, { condition })}
              min={0}
              precision={2}
              // @ts-ignore
              hasTrigger={false}
            />
            <span>优惠</span>
            <NumberPicker
              style={{ marginLeft: 8, width: 64 }}
              value={item.discount}
              onChange={(discount) => updateCond(item.key, { discount })}
              min={0.01}
              precision={2}
              // @ts-ignore
              hasTrigger={false}
            />
          </div>
          <Button
            className="xt-delete"
            text
            onClick={() => {
              onChange &&
                onChange(
                  value
                    ? value.filter((cond) => cond.key !== item.key)
                    : undefined,
                );
            }}
          >
            <Icon
              type={"delete-filling"}
              style={{ color: "#ccc" }}
              size="small"
            />
          </Button>
        </div>
      ))}

      {selectedActs.length < limit ? (
        <div className="xt-mkt-add-condition">
          <Button
            text
            type="primary"
            onClick={() => {
              if (value && value.length >= 5) {
                Message.error("最多只允许配置 5 种玩法");
                return;
              }
              onChange && onChange([...(value || []), { key: genKey() }]);
            }}
          >
            新增玩法配置
          </Button>
        </div>
      ) : null}
    </div>
  );
}

export function MktConditionsPreview({ value: _value, dataSource, style }) {
  let value = parseValueIfNeed(_value);

  try {
    return (
      <div style={style}>
        {(value || [])
          .map((item) => {
            const target = dataSource.find(
              (_item) => _item.value === item.value,
            );
            const label = target ? target.label : "-";
            return `玩法:${label || "-"},门槛:${
              isEmpty(item.condition) ? "-" : item.condition
            },优惠:${isEmpty(item.discount) ? "-" : item.discount}`;
          })
          .join("、")}
      </div>
    );
  } catch (e) {
    console.error(e);
    return <>{JSON.stringify(_value)}</>;
  }
}
