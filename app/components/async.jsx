import { createElement, useState, useEffect } from 'react'

export default function Async ({ await: subject, children, reject, resolve }) {
  const [state, setState] = useState({
    state: 'loading',
    result: null,
  })
  useEffect(() => {
    subject().then((result) => {
      setState({ state: 'resolved', result })
    }, (err) => {
      console.error(err)
      setState({ state: 'rejected', result })
    })
  }, [0])
  if (state.state === 'loading') {
    return children || null
  }
  if (state.state === 'resolved') {
    if (typeof resolve === 'function') {
      return createElement(resolve, { ...state.result })
    } else if (resolve) {
      return resolve
    } else if (state.result && typeof state.result.default === 'function') {
      return createElement(state.result.default)
    } else {
      return state.result
    }
    return typeof resolve === 'function' ? createElement(resolve, { ...state.result }) : resolve || state.result
  }
  if (state.state === 'rejected') {
    return typeof reject === 'function' ? createElement(reject, { ...state.result }) : reject
  }
}
