import "./style.scss";

import React from "react";
import { useLocation } from "react-router";

import { env } from "@/config";

export function IframeContainer({ pageMap, syncQuery }) {
  const location = useLocation();

  let url = pageMap[env || "prod"];
  if (syncQuery) {
    url = url + location.search;
  }

  return (
    <div className="selection-ext-iframe-wrap">
      <iframe key={url} src={url} />
    </div>
  );
}
