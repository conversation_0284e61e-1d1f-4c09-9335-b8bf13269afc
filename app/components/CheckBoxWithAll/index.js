import React from 'react';
import { Checkbox } from '@alife/next';
import './index.scss';

export class CheckBoxWithAll extends React.Component {
  constructor(props) {
    super(props)
    let { dataSource, value } = props;
    let dataSourceCanChoose = dataSource.filter(item => !item.disabled);
    this.state = {
      checkAll: this.isCheckAll(dataSourceCanChoose, value),
      dataSource: dataSource,
      dataSourceCanChoose,
    }
  }

  componentWillReceiveProps(nextProps){
    let { dataSource, value } = nextProps;
    let dataSourceCanChoose = dataSource.filter(item => !item.disabled);
    this.setState({
      dataSource,
      checkAll: this.isCheckAll(dataSourceCanChoose, value),
      dataSourceCanChoose,
    });
  }
  isCheckAll = (dataSourceCanChoose, value = []) => {
    console.log(value, 'value')
    if (value.length == 0) return false;
    let tempArr = dataSourceCanChoose.filter(item => value.indexOf(item.value) != -1);
    return tempArr.length == dataSourceCanChoose.length;
  }

    onChange = (checkList) => {
      let { dataSourceCanChoose } = this.state;
      let checked = checkList.length == dataSourceCanChoose.length ? true : false;
      this.setState({
        checkAll: checked,
      })
      this.props.onChange(checkList, checked);
    }

    onCheckAllChange = (checked) => {
      let { dataSourceCanChoose, value } = this.state;
      if (checked) {
        value = dataSourceCanChoose.map(r => r.value);
      } else {
        value = [];
      }
      this.setState({
        checkAll: checked
      })
      this.props.onChange(value, checked)
    }

    render(){
      const { checkAll, dataSource, dataSourceCanChoose } = this.state;
      let { name = '', value = [] } = this.props;
      return (
        <>
            <Checkbox
              style={{marginRight: '8px'}}
              onChange={this.onCheckAllChange}
              checked={checkAll}
              disabled={dataSourceCanChoose.length == 0}
            >
                全部
            </Checkbox>
            <Checkbox.Group name={name} value={value} dataSource={dataSource} onChange={(value) => this.onChange(value)}/>
        </>
      )
    }
}


