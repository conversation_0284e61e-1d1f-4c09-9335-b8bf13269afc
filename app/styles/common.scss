//border color
$line_light: #FAFAFA;
$line_common: #EBEBEB;
$line_high: #D9D9D9;
$line_higher: #EBEBEB;

//fill color
$fill_common: #FFFFFF;
$fill_light: #FAFAFA;
$fill_high: #FAFAFA;
$fill_higher: #EBEBEB;

//font color
$font_disable: #CCCCCC;
$font_tip: #999999;
$font_main_light: #666666;
$font_main_height: #333;

//color
$link_main: #FF7C4D;


// success
$success_light: #E4FDDA;
$success_common: #CDF2BE;
$success_high: #46BC15;
$success_higher: #41A716;

// notice
$notice_light: #E3F2FD;
$notice_common: #BAD9FF;
$notice_high: #4494F9;
$notice_higher: #2E7DE0;

// warning
$warning_light: #FFF3E0;
$warning_common: #FFE6BD;
$warning_high: #FF9300;
$warning_higher: #EB7E10;

//error
$error_light: #FFECE4;
$error_common: #FFC8B2;
$error_high: #FF3000;
$error_higher: #E72B00;

//help
$help_light: #E3FFF8;
$help_common: #C0ECE2;
$help_high: #01C1B2;
$help_higher: #01A79A;

//link
$help_light: #E3FFF8;
$help_common: #C0ECE2;
$help_high: #01C1B2;
$help_higher: #01A79A;

//font
$font-medium: PingFangSC-Medium;
$font-regular: PingFangSC-Regular;
