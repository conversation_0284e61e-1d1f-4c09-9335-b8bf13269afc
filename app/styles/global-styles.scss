:root {
  --colorBg: #fafafa;
  --padding-page: 20px;
}

html,
body {
  height: 100%;
  width: 100%;
}

body {
  font-family: PingFangSC-Regular Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #333333;
  line-height: 22px;
  //overflow: hidden;
}

body.fontLoaded {
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

#app {
  background-color: var(--colorBg);
  width: 100%;
  height: 100%;
  overflow: hidden;
}

p,
label {
  font-family: Georgia, Times, 'Times New Roman', serif;
  line-height: 1.5em;
}


h1 {
  font-size: 20px;
  line-height: 28px;
}

h2 {
  font-size: 16px;
  line-height: 24px;
}

.page {
}

.header-wrap {
  flex-shrink: 0;
}

label, p {
  font-family: PingFangSC-Medium !important;
}

h2, h3 {
  margin: 0;
}

// start: side bar
.kl-sider {
  a:visited, a:link {
    color: #333333;
    text-decoration: none;
  }

  .next-menu-item.next-selected{
    span {color: #FF7C4D;}
    i.next-icon {
      display: none;
    }
  }

  .next-icon.next-xs:before{
    font-size: 16px;
    margin-bottom: 3px;
  }
}


// start: table

.next-table td .next-table-cell-wrapper {
  //word-break: unset;
}

// start: steps

.step-wraper{
  // width: 860px;
  margin: 0 auto;
}

.g-good-pic{
  width: 60px;
  height: 60px;
}

.help {
  cursor: pointer;
}

.next-number-picker-normal .next-input .next-input-control{
  padding-right:0 !important;
}

.next-pagination .next-pagination-item.next-current{
  border-color: #FF7C4D;
  background: #FF7C4D;
}

.next-pagination .next-pagination-item.next-current:hover, .next-pagination .next-pagination-item.next-current:focus{
  background:#FF7C4D;
}

.next-table-header-inner{ //表格头滚动
  overflow:unset;
}

.kl-sider{
  overflow: auto;
}
