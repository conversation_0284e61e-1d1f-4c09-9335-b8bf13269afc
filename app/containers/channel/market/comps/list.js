import {Table, Button, Dialog, Breadcrumb, Balloon, Icon, Loading} from '@alife/next'
import React, {useState, useEffect, useMemo, useRef} from 'react'
import {track} from '@/utils/aplus';
import {Link} from 'react-router-dom'
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {getQueryString} from '@/utils/others';
import {filtrationAlpha} from '../common'
import {queryResourceList,validateAclResource} from '../request';
import * as api from '@/utils/api';
import * as apiL from '@/adator/api';
import * as qs from 'query-string';
import {AccessBtn} from '@/components/Button/AccessBtn';
import {onRequestError} from '@/utils/api';
import {getResourceMenuList} from "@/containers/report/request";

const {Tooltip} = Balloon;

function ImgExp({resource}) {
  const [wrapperClass, setWrapperClass] = useState();
  function preview () {
    track('clickEvent', ['/selection_kunlun.CHANNEL-MANAGE-index.preview-img-btn', `pageId=${resource.pageId}&resourceId=${resource.resourceId}`])
    Dialog.show({
      title: '图片预览',
      footer: false,
      content: (
        <div style={{textAlign: 'center'}}>
          <img src={resource.imageUrl} width='375'/>
        </div>
      ),
      cancelProps: {style: {display: 'none'}},
    })
  }

  function handleImageLoaded(e) {
    if (e.target.height < 140) {
      setWrapperClass('center');
    } else {
      setWrapperClass('common');
    }
  }

  return (
    <div className='img-exp'>
      <p>C端示例<a onClick={preview}>预览图片</a></p>
      <div className={`wrap ${wrapperClass}`}>
        <img src={resource.imageUrl} width='375' onLoad={(e) => handleImageLoaded(e)}/>
      </div>
    </div>
  )
}

export default function MarketResourceList({ pageMap = [],pageId=''}) {
  const triggerTooltip = <Icon type="prompt" style={{color: '#999', marginLeft: '5px'}}/>;
  // const { id, state, yn } = record;
  const [resourceData, setResourceData] = useState([]);
  const [viewResource, setViewResource] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [canSetSchema, setCanSetSchema] = useState(false);
  const isInit = useRef(true);

  const ctrlMenuArray = (list = [], result = []) => {
    if (!list.length) return [];
    for (let item of list) {
      let node = {
        ...item,
      }
      if (!item.children) {
        result.push(node.value);
      }else{
        ctrlMenuArray(item.children, result);
      }
    }
    return result;
  }

  const getViewResourceList = () => {
    try {
      getResourceMenuList().then((res)=>{
        setViewResource(ctrlMenuArray(res));
      });
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const getSetSchemaAuthority = async () => {
    try {
      let resp = await apiL.queryRoleValidate("set_schema");
      setCanSetSchema(resp === 'YES')
    } catch (error) {
      apiL.onRequestError(error);
    }
  };

  function showCur() {
    let resourceId =  getQueryString('anchorId');
    if(resourceId && document.getElementById(`item${resourceId}`)) {
      document.getElementById(`item${resourceId}`).scrollIntoView(false);
    }
  }

  function getResourceData() {
    if (isInit.current) {
      isInit.current = false;
    }

    if (isNaN(Number(pageId))) {
      setLoading(false);
      return;
    }

    let queryResource = {
      page: 1,
      size: 80,
      query: {
        pageId
      }
    }
    queryResourceList(queryResource)
      .then((result) => {
        // if (result.total > 0) {
        setLoading(false);
        setResourceData(result.resourceList);
        showCur();
        // }

      }).catch(api.onRequestError);
  }

  useEffect(() => {
    getViewResourceList();
    getSetSchemaAuthority();
    getResourceData();
  }, [0])

  useEffect(() => {
    if (pageId && pageMap.length > 0) {
      getResourceData();
    }
  }, [pageId, pageMap])

  const { pageName, pageUrl } = useMemo(() => {
    try {
      const isUniversalPageEnabled = window.configEnv === "ppe";
      if (isUniversalPageEnabled) {
        const pattern = /#\/channelManage\/__universal_page__\/(\d+)/;
        const result = pattern.exec(window.location.hash);
        if (result) {
          const _pageId = result[1];
          return {
            pageName: "-",
            pageUrl: `/channelManage/__universal_page__/${_pageId}`,
          };
        }
      }
    } catch(e) {
      console.error(e);
    }

    const _curPage = pageMap.filter((v) => +v.pageId === +pageId)[0];
    return {
      pageName: _curPage ? _curPage.pageName : "",
      pageUrl: _curPage ? _curPage.url : "",
    };
  }, [pageId, pageMap]);

  if (!pageName && !pageUrl && !isInit.current) {
    // 当前页面不存在，说明 menuTree.json 中没有返回当前页面，可能是没有权限或者是页面不存在，
    // 本地切换菜单会重新请求 menuTree.json, getResourceData 返回比 menuTree.json 块，会导致删一下这个提示。
    return (
      <div
        style={{ textAlign: "center", marginTop: "200px", fontSize: "20px" }}
      >
        请检查“地址是否正确”或“是否拥有权限”。
      </div>
    );
  }
  
  return (
    <div className="resource">
      <div className="nav-wrapper">
        <Breadcrumb>
          <Breadcrumb.Item>频道管理</Breadcrumb.Item>
          <Breadcrumb.Item>商超频道页</Breadcrumb.Item>
          <Breadcrumb.Item>{pageName}</Breadcrumb.Item>
        </Breadcrumb>
      </div>
      <div className='body'>
        {/*{isInside && <p className='resource-title'>{pageName}</p>}*/}
        <div className="resource-list">
          {!isLoading ? <>{(resourceData.length > 0 && pageMap.length > 0) ? resourceData.map((v) => {
            let putInUrl = `${pageUrl}/resourcePutInSet?pageId=${pageId}&resourceId=${v.resourceId}&resourceName=${v.name}&resourceType=${v.type}`;
            let authUrl = `${pageUrl}/resourceAuthSet?pageId=${pageId}&resourceId=${v.resourceId}&resourceName=${v.name}`;
            const goldLinkParams = `pageId=${pageId}&resourceId=${v.resourceId}`; //埋点参数
            const getPermission = async () => {
              let validateAclUserRequest = {
                pageId: pageId,
                resourceId: v.resourceId,
              }
              try {
                let data = await validateAclResource({...validateAclUserRequest});
                return data.msg;
              } catch (error) {
                onRequestError(error)
              }
            }
            const showViewData =  (viewResource && viewResource.length>0 && viewResource.includes(v.resourceId));
            return <div className='resource-item' id={`item${v.resourceId}`}>
              <div className='resource-item-title'>
                {v.name} {`(ID:${v.resourceId})`}
                <div className="btn-putin-panel">
                  <Button className="btn-putin" type='default'><Link to={authUrl} style={{color: '#333'}}>权限配置</Link></Button>
                  <AccessBtn getPermission={getPermission} type='default' btnType={2} className="btn-putin"
                    btnText={'投放配置'} callback={() => {
                      track('clickEvent', ['/selection_kunlun.CHANNEL-MANAGE-index.putin-set-btn', goldLinkParams]);
                      location.href = `#${putInUrl}`;
                    }}/>
                  <Button className="btn-putin" onClick={()=>{
                    location.href = `#/channelManage/market/debuggingPage?formChannel=${encodeURIComponent(JSON.stringify({
                      pageId,
                      resourceId: v.resourceId,
                      prePageUrl: pageUrl
                    }))}`
                  }} type='default'>预览</Button>
                  {showViewData && <Button className="btn-putin"  onClick={()=>{
                    location.href = `#/report/resource?resourceId=${v.resourceId}`
                  }}>数据</Button>}
                </div>
                <Tooltip trigger={triggerTooltip} id="aria-tooltip" align="r">
                  <div className='tooltip-text'>
                    <p>创建时间：{formatTimeStamp(v.createTime, FORMAT.DATE)}</p>
                    <p>锚点：{v.anchor}</p>
                    <p>spmc：{v.spmc}</p>
                    <p>名称：{v.componentName}</p>
                    <p>版本号：{v.versionNo}</p>
                    <p>版本ID：{v.versionId}</p>
                    <p>描述：{v.description}</p>
                    <p>维护者：{v.adminName}</p>
                    <p>资源位类型：{v.type}</p>
                  </div>
                </Tooltip>
              </div>
              <div className="resource-row">
                <div className="resource-row-left"><ImgExp resource={v}/></div>
                <div className="resource-row-right">
                  {v.locationList && <Table dataSource={v.locationList}>
                    <Table.Column title="资源位名称" dataIndex="name" width='130px'
                      cell={(o, i, record) => <span>{`${record.name}`}</span>}/>
                    <Table.Column title="操作" dataIndex="name"
                      cell={(o, i, record) => <Operation  getPermission={getPermission} pageId={v.pageId} canSetSchema={canSetSchema} pageMap={pageMap}
                        record={record} resourceId={v.resourceId} resourceName={v.name} resourceType={v.type} showViewData={showViewData}/>}/>
                  </Table>}
                </div>
              </div>
            </div>
          }) : <div className='load-wrapper'>暂无数据</div>}</>
            :
            <div className='load-wrapper'><Loading/></div>}
        </div>
      </div>
    </div>
  )
}

function Operation({
  getPermission,
  pageId,
  record,
  resourceId,
  resourceName,
  resourceType,
  showViewData,
  canSetSchema,
  pageMap,
}) {
  const pageUrl = useMemo(() => {
    let ret =
      pageMap.filter((v) => +v.pageId === +pageId)[0] &&
      pageMap.filter((v) => +v.pageId === +pageId)[0].url;
    try {
      if (
        window.location.hash.startsWith("#/channelManage/__universal_page__")
      ) {
        ret = "/channelManage/__universal_page__";
      }
    } catch (e) {
      console.error(e);
    }
    return ret;
  }, [pageId, pageMap]);
  // const filter = `?pageId=${pageId}&posName=${record.name}&posId=${record.id}&resourceId=${resourceId}&step=0`;
  const goldLinkParams = `pageId=${pageId}&resourceId=${resourceId}&posId=${record.id}`;
  const query = {
    pageId,
    resourceId,
    resourceName,
    resourceType,
    posId: record.id,
    posName: record.name,
    showViewData,
    step: 0,
  };
  const filter = `?${qs.stringify(query)}`;
  return (
    <div>
      <AccessBtn
        getPermission={getPermission}
        btnType={2}
        btnText={"查看配置"}
        callback={() => {
          track("clickEvent", [
            "/selection_kunlun.CHANNEL-MANAGE-index.view-set-btn",
            goldLinkParams,
          ]);
          location.href = `#${pageUrl}/viewConfigure${filter}`;
        }}
      />
      {filtrationAlpha.includes(resourceId) ? (
        ""
      ) : (
        <>
          <i className="sep"></i>
          <AccessBtn
            getPermission={getPermission}
            btnType={2}
            btnText={"去投放"}
            callback={() => {
              track("clickEvent", [
                "/selection_kunlun.CHANNEL-MANAGE-index.putin-btn",
                goldLinkParams,
              ]);
              location.href = `#${pageUrl}/edit${filter}`;
            }}
          />
          <i className="sep"></i>
          <AccessBtn
            getPermission={getPermission}
            btnType={2}
            btnText={"操作记录"}
            callback={() => {
              track("clickEvent", [
                "/selection_kunlun.CHANNEL-MANAGE-index.operate-log-btn",
                goldLinkParams,
              ]);
              location.href = `#${pageUrl}/log${filter}`;
            }}
          />
        </>
      )}
      {canSetSchema && (
        <>
          <i className="sep"></i>
          <AccessBtn
            getPermission={getPermission}
            btnType={2}
            btnText={"schema"}
            callback={() => {
              track("clickEvent", [
                "/selection_kunlun.CHANNEL-MANAGE-index.set-schema",
                goldLinkParams,
              ]);
              location.href = `#${pageUrl}/setSchema${filter}`;
            }}
          />
        </>
      )}
    </div>
  );
}
