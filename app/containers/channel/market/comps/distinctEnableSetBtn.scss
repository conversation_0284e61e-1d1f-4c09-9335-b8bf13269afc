.distinct-enable-set-btn {

    .go-set-btn {
        margin-left: 21px;
    } 

}

.distinct-enable-dialog {

    .next-dialog-header {
        padding:24px 30px 2px 30px;
        font-size: 20px;
    }

    .next-dialog-body {
        padding: 12px 30px;

        .dialog-content {
            margin-left: 30px;

            .next-form-item-label {
                padding-right: 0;
                color: #333;
            }

            .next-form-item-control{
                margin-left: -8px;
            }

            #dataDistinct {

                .text {
                    display: inline-block;
    
                    .tip {
                        margin-left: 10px;
                        margin-right: 40px;
                        font-size: 14px;
                        color: #999999;
                        line-height: 14px;
    
                        i {
                            margin-right: 4px;
                        }
                    }
    
                }

            }

            .table-title {
                padding: 7px 0 10px 0;
                font-size: 14px;
                color: #333333;
                line-height: 14px;
            }
        }
    }

    .next-dialog-footer {
        padding: 18px 30px 30px 30px;

        .next-medium {
            padding: 0 20px;

            &.next-btn-primary {
                margin-left: 11px;
            }
        }
    }
}