import {
  Grid,
  Table,
  Button,
  Dialog,
  Breadcrumb,
  Balloon,
  Icon,
  Loading,
  Message,
  Card,
  Form,
  Input,
  Select, CascaderSelect
} from '@alife/next'
const{Row,Col} = Grid;
import {FilterForm, FilterItem} from '@/components/filter'
import React, {useState, useEffect, useRef} from 'react'
import {ACLPermission} from '@/containers//PutInPage/request';
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link, withRouter} from 'react-router-dom'
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {getQueryString} from '@/utils/others';
import {
  pageMap,
  filtrationAlpha,
  supplyTypeDataSource,
  jumpTypeMap,
  insertMap,
  skipIconContentTypeMap,
  positionMap
} from '../common'
import {getPoolInfoSurge, getStoreMajorCategory} from '../request';
import * as api from '@/utils/api';
import * as apiL from '@/adator/api';
import {DndProvider, useDrag, useDrop} from "react-dnd";
import HTML5Backend from "react-dnd-html5-backend";
import {deepCopy} from "@/home/<USER>/common";
// import CommodityDataSource from "@/containers/channel/market/comps/commodityDataSource";
// import MultipleSupplySourcesDragger from "@/containers/channel/market/comps/multipleSupplySourcesDragger";
import SchemaForm from "@/utils/Form/src";
// import MultilineIdText from "@/containers/channel/market/comps/multilineIdText";
import PutInPosition from "@/containers/channel/market/comps/putInPosition";
import SimpleTable from "@/components/SimpleTable";
import QRCode from "qrcode.react";
import {handleField} from "@/home/<USER>/common";
// import ShopMainCategory from "@/containers/channel/market/comps/shopMainCategory";
let tabMapList = []
let moverId = -1
const formItemLayoutChannel = {
  labelCol: { span: 6},
  wrapperCol: {
    span: 18
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

export default function RankDetail(props) {
  // console.log(props);
  const {rankInfo,rankSchema,deliveryPosition} = props;
  const {isHidePosition} = (rankSchema && rankSchema.length > 0) ? rankSchema[0] : false;
  useEffect(()=>{
    if(!isHidePosition) {
      sessionStorage.setItem('deliveryPosition', JSON.stringify(deliveryPosition));
    }
  },[deliveryPosition])
  const renderDataSourceElement = (item,index,rankIndex) => {
    if(Object.keys(item).length > 0) {
      let Text = [];
      if (item.supplyType == 1 && item.shopList && item.shopList.length > 0) {
        item.shopList.map((comItem) => {
          try {
            if (item.poolIds.includes(comItem.value) || item.poolIds.includes(Number(comItem.value))) {
              Text.push({
                value: comItem.value,
                label: comItem.label
              })
            }
          } catch (error) {
          }
        })
      }

      if (item.supplyType == 2) {
        item.itemCat.map((catItem) => {
          Text.push(catItem)
        })
      }
      // let showTab = false
      // if (oversupply.field && item[oversupply.field] && item[oversupply.field].length > 0) {
      //   showTab = true
      // }
      // console.log(Text);
      // console.log(rankIndex);
      return <>
        {/*{showTab && <SimpleTable.Item label={`${oversupply.name}-${index+1}`}>*/}
        {/*  {item[oversupply.field]}*/}
        {/*</SimpleTable.Item>}*/}
        <SimpleTable.Item label={'tab名称：'}>{item.tabName}</SimpleTable.Item>
        <SimpleTable.Item label={'供给来源：'}>{item.supplyType == 1 ? '选品集ID' : '不限定'}</SimpleTable.Item>
        {item.supplyType == 1 ? <SimpleTable.Item label={`选品集ID：`}>
          {Text.map((comItem) => {
            return <>
              <span>ID:{comItem.value}</span>&nbsp;&nbsp;
              <span>名称:{comItem.label}</span>
              <br></br>
            </>
          })}
        </SimpleTable.Item> : <SimpleTable.Item label={`商品类目：`}>
          {Text.map((comItem, index) => {
            return <>
              {`${comItem.label}${index != Text.length - 1 ? "、" : ""}`}
            </>
          })}
        </SimpleTable.Item>}
        {rankSchema[rankIndex].schema.showMultilineIdText && (rankSchema[rankIndex].schema.showMultilineIdText.map((o, index) => {
          return renderMultilineIdText(o, index, item)
        }))}

        {item.topItemCatList && item.topItemCatList.length > 0 && <SimpleTable.Item label={`指定叶子类目置顶：`}>
          {item.topItemCatList.map((o) => {
            return <>
              <span>ID:{o.value}</span>&nbsp;&nbsp;
              <span>名称:{o.label}</span>
              <br></br>
            </>})
          }
        </SimpleTable.Item>}
      </>
    }
  }

  const renderRecallRule = (recallRule, rankIndex) => {
    const {schema} = rankSchema[rankIndex];
    const fieldKeys = schema.detail.properties;
    return <div className={'detail-block'}>
      {Object.keys(fieldKeys).map((item) => {
        return <SimpleTable.Item label={fieldKeys[item].title}>
          {handleField(item, recallRule, fieldKeys[item])}
        </SimpleTable.Item>
      })}
      {recallRule.shopMajorCategoryContent && recallRule.shopMajorCategoryContent.length > 0 && <SimpleTable.Item label={`主营类目：`}>
        {recallRule.shopMajorCategoryContent.map((o) => {
          return <>
            <span>ID:{o.value}</span>&nbsp;&nbsp;
            <span>名称:{o.label}</span>
            <br></br>
          </>})
        }
      </SimpleTable.Item>}

    </div>
  }

  const renderMultilineIdText = (o,index,item) =>{
    // console.log(o,index,item);
    let {name,key} = o;
    return <SimpleTable.Item label={o.name}>{item[key]}</SimpleTable.Item>
  }
  return <>
    <SimpleTable.Item label="榜单设置">
      {(rankInfo && rankInfo.length > 0) && rankInfo.map((item, rankIndex) => {
        return <div className={'rank-item-detail'}>
          <SimpleTable.Item label="榜单Tab类型：">{item.type == 1 ? '人气榜' : '品牌榜'}</SimpleTable.Item>
          {/*<SimpleTable.Item label="榜单Tab总榜名称：">{item.name}</SimpleTable.Item>*/}
          <SimpleTable.Item label="tab设置：">
            {item.pureDataSourceList && (item.pureDataSourceList.map((item, index) => {
              return <div className={'detail-block'}>{renderDataSourceElement(item, index, rankIndex)}</div>
            }))} </SimpleTable.Item>
          {item.recallRule &&
          <SimpleTable.Item label={'商品召回规则：'}>{renderRecallRule(item.recallRule, rankIndex)}</SimpleTable.Item>}
        </div>
      })}
    </SimpleTable.Item>
    {!isHidePosition && <SimpleTable.Item label="投放位置：">
      <PutInPosition positionDisabled={true}  />
    </SimpleTable.Item>}
  </>
}
