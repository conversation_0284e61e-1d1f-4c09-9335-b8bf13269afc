import {
  Grid,
  Table,
  Button,
  Dialog,
  Breadcrumb,
  Balloon,
  Icon,
  Loading,
  Message,
  Card,
  Form,
  Input,
  Select, CascaderSelect
} from '@alife/next'
const{Row,Col} = Grid;
import {FilterForm, FilterItem} from '@/components/filter'
import React, {useState, useEffect, useRef} from 'react'
import {ACLPermission} from '@/containers//PutInPage/request';
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link, withRouter} from 'react-router-dom'
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {getQueryString} from '@/utils/others';
import {pageMap, filtrationAlpha, supplyTypeDataSource} from '../common'
import {getPoolInfoSurge} from '../request';
import * as api from '@/utils/api';
import * as apiL from '@/adator/api';
import {DndProvider, useDrag, useDrop} from "react-dnd";
import HTML5Backend from "react-dnd-html5-backend";
import {deepCopy} from "@/home/<USER>/common";
import SchemaForm from "@/utils/Form/src";
const formItemLayoutChannel3 = {
  labelCol: { span: 4},
  wrapperCol: {
    span: 17
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const resourceDataSource = [
  {
    "value": "1",
    "label": "全能商厦首页推荐",
  },
  {
    "value": "100090001",
    "label": "全能商厦金刚tab",
    "children": [{
      "value": "201220320",//二级就是排期id了
      "label": "腌腊制品"//排期名
    },
      {
        "value": "201220321",//二级就是排期id了
        "label": "腌腊制品2"//排期名
      }
    ]
  }
]


export default function PutInPosition(props) {
  // const {positionData = []} = props;
  const _positionData = sessionStorage.getItem('deliveryPosition') ? JSON.parse(sessionStorage.getItem('deliveryPosition')) : [];
  const [positionData, setPositionData] = useState(_positionData || []);
  const [positionGroup, setPositionGroup] = useState([]);
  const [cascaderValue, setCascaderValue] = useState([]);
  const [deliveryGroup, setDeliveryGroup] = useState([]);
  const {positionDisabled = false} = props;
  const changeResource = (value, data) => {
    setPositionGroup(data);
    let _cascaderValue = JSON.parse(JSON.stringify(cascaderValue));
    _cascaderValue = data.map((o)=>o.value);
    _cascaderValue = Array.from(new Set(_cascaderValue))
    setCascaderValue(_cascaderValue);
  }

  useEffect(()=>{
    analysisDeliveryPosition();
    fetchDeliveryGroup();
  },[])

  const analysisDeliveryPosition = () => {
    let _positionGroup = JSON.parse(JSON.stringify(positionGroup));
    positionData.map((item)=>{
      if (item.scheduleId == '' || !item.scheduleId) { //全能排期
        _positionGroup.push({
          label: item.name,
          location: item.location,
          value: item.type
        })
      } else { //全能资源位
        let parentNode = _positionGroup.filter((o) => o.value == item.type);
        if (parentNode.length > 0) {
          parentNode[0].children.push({
            label: item.scheduleName,
            location: item.location,
            value:  item.scheduleId
          })
        }else{
          _positionGroup.push({
            label: item.name,
            value: item.type,
            children:[]
          });
          let parentNode = _positionGroup.filter((o) => o.value == item.type);
          parentNode[0].children.push({
            label: item.scheduleName,
            location: item.location,
            value:  item.scheduleId
          })
        }
      }
    })
    let _cascaderValue = JSON.parse(JSON.stringify(cascaderValue));
    _cascaderValue = positionData.map((o)=>o.type);
    _cascaderValue = Array.from(new Set(_cascaderValue))
    // console.log('_positionGroup', positionData,_positionGroup,_cascaderValue);
    setCascaderValue(_cascaderValue);
    setPositionGroup(_positionGroup);
  }

  const onInputChange = (value,posIndex,scheduleIndex) => {
    let _positionGroup = deepCopy(positionGroup);
    if ((scheduleIndex && scheduleIndex!='') || scheduleIndex===0) {
      _positionGroup[posIndex]['children'][scheduleIndex].location = value;
    } else {
      _positionGroup[posIndex].location = value;
    }
    setPositionGroup(_positionGroup);
    ctrlDeliveryPosition(_positionGroup);
    // console.log('_positionGroup',_positionGroup);
  }

  const ctrlDeliveryPosition = (positionGroup) =>{
    let deliveryPosition = [];
    positionGroup.map((item) => {
      if (item.children && item.children.length>0) {
        item.children.map((o) => {
          deliveryPosition.push({
            type: item.value,
            name: item.label,
            location: o.location,
            scheduleId: o.value,
            scheduleName: o.label
          })
        });
      } else {
        deliveryPosition.push({
          type: item.value,
          location: item.location,
          scheduleId: '',
          name: item.label
        })
      }
    });
    console.log('deliveryPosition',deliveryPosition);
    sessionStorage.setItem('deliveryPosition',JSON.stringify(deliveryPosition));
  }

  /*获取商品分类数据*/
  const fetchDeliveryGroup =  () => {
    try {
      let request = apiL.queryDeliveryPosition;
      request().then((resp)=>{
        setDeliveryGroup(resp);
      })
    } catch (error) {
      api.onRequestError(error);
    }
  };


  // value={['1','201220320','201220321']}

  return <div>
    {!positionDisabled && <span className={'rank-title'}>投放位置：</span>}
    <div className={`${!positionDisabled?'rank-item':'rank-item-detail'}`}>
      {!positionDisabled && <CascaderSelect style={{width: '100%'}} multiple value={cascaderValue}  name='cateLevel'  dataSource={deliveryGroup} onChange={(v,d) => changeResource(v,d)}  />}
      {/*<Select dataSource={resourceDataSource} onChange={(value)=>onChange(value,'type')} placeholder={'一级投放配置'} style={{width: '200px'}} />*/}
      {/*<Select dataSource={scheduleDataSource} onChange={(value)=>onChange(value,'scheduleId')} placeholder={'二级投放配置'} style={{width: '200px'}} />*/}
      {(positionGroup && positionGroup.length > 0) && positionGroup.map((item,posIndex)=> {
        return <Row className={'rank-position-group'} key={posIndex}>
          {(!item.children || item.children.length==0) && <Col span={4}><span className={'rank-position'}>投放位置：{item.label}</span> </Col>}
          <Col span={20}>
            {(item.children && item.children.length > 0) ? item.children.map((o, scheduleIndex) => {
              console.log(o);
              return <div className={'rank-schedule'} key={scheduleIndex}>
                <span style={{marginRight: '10px'}}>投放位置：{item.label}-{o.label}</span>
                投放坑位： <Input value={o.location} onChange={(value) => onInputChange(value, posIndex, scheduleIndex)}/>
              </div>
            }) : <div className={`${!positionDisabled?'rank-schedule':'rank-schedule-detail'}`}>
              投放坑位： {!positionDisabled ?<Input value={item.location} onChange={(value) => onInputChange(value, posIndex, '')}/>:<span>{item.location}</span>}
            </div>}
            </Col>
        </Row>
      })}
    </div>
  </div>

}
