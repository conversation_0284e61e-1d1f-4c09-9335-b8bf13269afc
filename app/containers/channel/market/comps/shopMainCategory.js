import {
  Grid,
  Table,
  Button,
  Dialog,
  Breadcrumb,
  Balloon,
  Icon,
  Loading,
  Message,
  Card,
  Form,
  Input,
  Select, CascaderSelect
} from '@alife/next'
import {FilterForm, FilterItem} from '@/components/filter'
import React, {useState, useEffect, useRef} from 'react'
import {ACLPermission} from '@/containers//PutInPage/request';
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link, withRouter} from 'react-router-dom'
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {getQueryString} from '@/utils/others';
import {pageMap, filtrationAlpha, supplyTypeDataSource} from '../common'
import {getPoolInfoSurge, getStoreMajorCategory} from '../request';
import * as api from '@/utils/api';
import * as apiL from '@/adator/api';
import {DndProvider, useDrag, useDrop} from "react-dnd";
import HTML5Backend from "react-dnd-html5-backend";
import {deepCopy} from "@/home/<USER>/common";
const selectionCommodityUrl = {
  'daily': 'https://market.wapa.taobao.com/app/op-fe/o2o-selection-admin/index.html#/pool/list',
  'localdev': 'https://pre-kunlun.alibaba-inc.com/selection#/pool/list',
  'ppe': 'https://pre-kunlun.alibaba-inc.com/selection#/pool/list',
  'prod': 'https://selection.kunlun.alibaba-inc.com/#/pool/list'
}

const formItemLayoutChannel = {
  labelCol: { span: 6},
  wrapperCol: {
    span: 18
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};
export default class ShopMainCategory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      shopMainCategoryDataSource:[],
      shop_main_category:JSON.parse(sessionStorage.getItem('shop_main_category')) || [], //list map
      shopMajorCategoryList:JSON.parse(sessionStorage.getItem('shopMajorCategoryList')) || [], //list string
      showMainCategory: this.props.showMainCategory || {}
    }
  }
  componentDidMount() {
    this.getMainCategory();
  }

  getMainCategory = () => {
    getStoreMajorCategory().then((result) => {
      this.setState({
        shopMainCategoryDataSource: result.map((item)=>{
          return {
            label:item.name,
            value:item.code
          }
        })
      })
    }).catch(api.onRequestError);
  }

  changMainCategory = (newShopMainCategory,data) => {
    const newData = data.map(item => {
      const {value, label, pos} = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    this.setState( {shop_main_category: newData,shopMajorCategoryList: newShopMainCategory},()=>{
      sessionStorage.setItem("shop_main_category",JSON.stringify(this.state.shop_main_category));
      sessionStorage.setItem("shopMajorCategoryList",JSON.stringify(this.state.shopMajorCategoryList));
      if(this.props.saveShopMainCategory){
        this.props.saveShopMainCategory(this.state.shop_main_category);
      }
    })
  }

  render()
  {
    const { required = false } = this.state.showMainCategory || {};
    return (
      <Form className='newputin'>
        <Form.Item label="商家主营类目:" {...formItemLayoutChannel} required={required}>
          <CascaderSelect
            showSearch
            style={{ width: '302px' }}
            name="shopMainCategory"
            multiple
            value={this.state.shopMajorCategoryList}
            dataSource={this.state.shopMainCategoryDataSource}
            onChange={(value,item) => this.changMainCategory(value,item)}
          />
        </Form.Item>
      </Form>
    )
  }
}
