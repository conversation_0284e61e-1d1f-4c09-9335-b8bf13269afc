import {
  Grid,
  Table,
  Button,
  Dialog,
  Breadcrumb,
  Balloon,
  Icon,
  Loading,
  Message,
  Card,
  Form,
  Input,
  Select,
  CascaderSelect,
} from "@alife/next";
import { getDynamicDataSource } from "../request";
const { Row, Col } = Grid;
import React, { useState, useEffect, useRef } from "react";

export default function DynamicDataSourceForm(props) {
  console.log(
    "🚀 ~ file: dynamicDataSourceForm.js:21 ~ DynamicDataSourceForm ~ props:",
    props
  );
  const [dataSource, setDataSource] = useState([]);
  const [value, setValue] = useState([]);
  const [attr, setAttr] = useState({});
  const { isHiddenMultilineIdText, dynamicDataSource, formData } = props;

  const {
    fieldName,
    componentName,
    isMultiple,
    placeholder,
    isSearch,
    isDynamicDataSource,
    apiName,
    isRequire,
    hidden,
    maxFields,
    requestMethod,
    requestGateway,
  } = dynamicDataSource;
  const [isHidden, setIsHidden] = useState(true);
  useEffect(() => {
    (async () => {
      if (!Boolean(isDynamicDataSource)) {
        // 不是动态的
        try {
          let resp = await getDynamicDataSource(
            apiName,
            requestMethod,
            requestGateway
          );
          setDataSource(
            resp.map((item) => {
              return {
                label: item.name,
                value: item.code,
              };
            })
          );
        } catch (e) {
          console.log(
            "🚀 ~ file: dynamicDataSourceForm.js:49 ~ useEffect ~ e:",
            e
          );
        }
      } else {
        let newAttr = {
          onSearch: (keyword) => {
            if (searchTimeout) {
              clearTimeout(searchTimeout);
            }
            console.log(
              "🚀 ~ file: dynamicDataSourceForm.js:66 ~ newAttr.onSearch:",
              dynamicDataSource
            );
            let searchTimeout = setTimeout(async () => {
              if (keyword) {
                let resp = await getDynamicDataSource(
                  apiName,
                  requestMethod,
                  requestGateway,
                  { searchKey: keyword, poolType: 1 }
                );
                console.log(
                  "🚀 ~ file: dynamicDataSourceForm.js:69 ~ searchTimeout ~ keyword:",
                  resp
                );
                setDataSource(
                  resp.map((item) => {
                    return {
                      label: item.poolContent,
                      value: item.poolId,
                    };
                  })
                );
              } else {
              }
            }, 800);
          },
        };
        setAttr(newAttr);
        setDataSource(JSON.parse(sessionStorage.getItem(`${fieldName}`)) || [])
      }
    })();
    setValue(JSON.parse(sessionStorage.getItem(`${fieldName}`)) || []);
  }, [dynamicDataSource]);

  useEffect(() => {
    // 如果当前组件被隐藏，则删除掉当前的所有信息
    let newIsHidden = isHiddenMultilineIdText(hidden, formData);
    // if (!newIsHidden) {
    //   sessionStorage.setItem(`${fieldName}`, "[]");
    // }
    setIsHidden(newIsHidden);
    // setValue([])
  }, [formData]);

  const onDynamicDataSourceChange = (value, data, extra) => {
    const haveSelected = data.map((item) => {
      const { value, label, pos } = item;
      return {
        value,
        label,
        level: pos.split("-").length - 1,
      };
    });
    if (haveSelected.length > maxFields && isMultiple) {
      Message.error(`${componentName}的数量不能大于${maxFields}`)
    }else{
      setValue(haveSelected);
      sessionStorage.setItem(`${fieldName}`, JSON.stringify(haveSelected));
    }
  };

  return (
    !isHidden && (
      <div className="newputin">
        <Form.Item
          label={`${componentName}：`}
          {...props.formItemLayoutChannel}
          required={isRequire}
        >
          <CascaderSelect
            className="dynamic-datasource-form"
            name={`${fieldName}`}
            dataSource={dataSource}
            showSearch={isSearch}
            style={{ width: "100%" }}
            multiple={isMultiple}
            placeholder={placeholder}
            value={value.map((i) => {
              return i.value;
            })}
            onChange={(value, data, extra) =>
              onDynamicDataSourceChange(value, data, extra)
            }
            {...attr}
            popupClassName={`dynamic-datasource-form-popup`}
            notFoundContent={"糟糕，没有找到对应的数据～"}
          />
        </Form.Item>
      </div>
    )
  );
}
