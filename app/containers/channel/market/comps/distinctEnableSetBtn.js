
import React from 'react';
import { Form, Radio, Field, Icon, Button, Table, Dialog, Pagination, Message } from '@alife/next';
import { PageBase } from '@/containers/base';
import { onRequestError }  from '@/utils/api';
import { getDistinctEnableConfig, queryDistinctResourceList, saveDistinctEnableConfig } from '../request';
import './distinctEnableSetBtn.scss'

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const putInSetItemLayout = {
    labelCol: { 
        span: 4, 
    },
    wrapperCol: {
        span: 20
    },
    labelAlign: 'left',
    labelTextAlign: 'left'
}

const formLayout = {
    style: {
      margin: '20px auto'
    },
  }

const DeRepeatShopGoodText = () => {
    return (
        <div className="text">
            门店<span className='tip'><Icon type='prompt' size="x"/>按门店id去重</span>
            商品<span className='tip'><Icon type='prompt' size="x"/>按商品item-id去重</span>
        </div>
    )
}

  const dataDistinctRadioMap = [
    {
      value:1,
      label:<DeRepeatShopGoodText/>,
    }
  ]

  const componentDistinctRadioMap = [
    {
      value:1,
      label:'从上到下依次去重'
    }
  ]

export default class DistinctEnableSetBtn extends PageBase {
    constructor(props) {
      super(props)
      this.state = {
        pageId: props.pageId,
        visible: false,
        dataSource: [],
        rowSelection: {
          onChange: this.selectedRowKeysChange.bind(this),
          selectedRowKeys: [],
        },
        total: 0,
        page: 1,
        pageSize: 50,

      }

      this.field = new Field(this, {
        values: { 
            dataDistinct: 1,
            componentDistinct: 1,
        }
      })
    }

    componentDidMount () {
        
    }

    switchSetDialog = (isOpen) => {
        this.setState({ visible: isOpen })
    }

    openDialog = () => {
      this.switchSetDialog(true);
      this.getDistinctEnableConfig();
      this.queryDistinctResourceList();
    }

    getDistinctEnableConfig = () => {
        let { pageId } = this.state;
        let params = {
            pageId
        }
        // 发请求
        // const data = await getDistinctEnableConfig(params);
        getDistinctEnableConfig(params).then((data) => { 
          const {rowSelection} = this.state;
          rowSelection.selectedRowKeys = data.resourceIds.filter(id => !!id);
          this.setState({ rowSelection });
          this.field.setValues({ 
            dataDistinct: 1,
            componentDistinct: 1,
         })
        }).catch(onRequestError)

        
    }

    queryDistinctResourceList = () => {
        console.log('query')
        let { pageId, page, pageSize } = this.state;
        let params = {
            pageId,
        }
        queryDistinctResourceList(params).then((data) => { 
          this.setState({
            dataSource: data.resourceList.map( ({resourceId, name}) => ({ id: resourceId, resourceId, name })),
            total: data.total,
          })
        }).catch(onRequestError)
    }

    saveDistinctEnableConfig = () => {
        let { pageId, rowSelection } = this.state;
        let params = {
            pageId,
            ...this.field.getValues(),
            resourceIds: rowSelection.selectedRowKeys
        }
        saveDistinctEnableConfig(params).then((data) => { 
          console.log(data,'data')
          Message.success('操作成功~')
        }).catch(onRequestError)
    }

    selectedRowKeysChange = (ids, records) => {
      const {rowSelection} = this.state;
      rowSelection.selectedRowKeys = ids;
      console.log('onChange', ids, records);
      this.setState({ rowSelection });
    }

    // 分页器
    onPageChange = (page) => {
        this.setState({
            page,
        }, () => this.queryResourceList())
      }
  
    onPageSizeChange = (pageSize) => {
        this.setState({
            pageSize,
        }, () => this.queryResourceList())
    }

    confirm = () => {
        this.switchSetDialog(false);
        this.saveDistinctEnableConfig();
    }

    render() {
      let { dataSource, total, page, pageSize, rowSelection } = this.state;
      let dataDistinct = this.field.getValue('dataDistinct');
      let componentDistinct = this.field.getValue('componentDistinct');
      return (
        <div className="distinct-enable-set-btn">
            <Button className="go-set-btn" type="primary" text="true" onClick={this.openDialog}>去设置</Button>
            <Dialog
                title="设置"
                className="distinct-enable-dialog"
                style={{ width: 680 }}
                visible={this.state.visible}
                onClose={() => this.switchSetDialog(false)}
                footer={[
                    <Button onClick={() => this.switchSetDialog(false)}>取消</Button>, 
                    <Button 
                        type="primary" 
                        disabled={rowSelection.selectedRowKeys.length == 0}
                        onClick={this.confirm}
                    >确定</Button>
                ]}
            >
                <div className="dialog-content">
                    <Form className="" {...formLayout} field={this.field}>
                        <FormItem label="数据去重：" { ...putInSetItemLayout }>
                            <RadioGroup name="dataDistinct" value={dataDistinct} dataSource={dataDistinctRadioMap}/>
                        </FormItem>
                        <FormItem label="组件去重：" { ...putInSetItemLayout }>
                            <RadioGroup name="componentDistinct" value={componentDistinct} dataSource={componentDistinctRadioMap}/>
                        </FormItem>
                    </Form>
                    <div className="table-title">请勾选参与去重的组件</div>
                    <Table dataSource={dataSource} rowSelection={rowSelection} fixedHeader >
                        <Table.Column title="组件名称" dataIndex="name"/>
                    </Table>
                    {total > pageSize &&
                        <div className="pagination" style={{textAlign: 'right', marginTop: '20px'}}>
                            <Pagination
                                popupProps = {{align:'bl tl'}}
                                current={page} total={total} pageSize={pageSize}
                                onChange={this.onPageChange} pageSizeSelector="dropdown" pageSizePosition="end"
                                onPageSizeChange={this.onPageSizeChange} />
                        </div>
                    }
                </div>
            </Dialog>
        </div>
        
      )
    }
  }
