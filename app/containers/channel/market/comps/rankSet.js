import {
  Grid,
  Table,
  Button,
  Dialog,
  <PERSON>readcrumb,
  Balloon,
  Icon,
  Loading,
  Message,
  Card,
  Form,
  Input,
  Select, CascaderSelect
} from '@alife/next'
const{Row,Col} = Grid;
import {FilterForm, FilterItem} from '@/components/filter'
import React, {useState, useEffect, useRef, useMemo} from 'react'
import {ACLPermission} from '@/containers//PutInPage/request';
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link, withRouter} from 'react-router-dom'
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {getQueryString} from '@/utils/others';
import {pageMap, filtrationAlpha, supplyTypeDataSource} from '../common'
import {getPoolInfoSurge, getStoreMajorCategory} from '../request';
import * as api from '@/utils/api';
import * as apiL from '@/adator/api';
import {DndProvider, useDrag, useDrop} from "react-dnd";
import HTML5Backend from "react-dnd-html5-backend";
import {deepCopy} from "@/home/<USER>/common";
// import CommodityDataSource from "@/containers/channel/market/comps/commodityDataSource";
// import MultipleSupplySourcesDragger from "@/containers/channel/market/comps/multipleSupplySourcesDragger";
import SchemaForm from "@/utils/Form/src";
// import MultilineIdText from "@/containers/channel/market/comps/multilineIdText";
import PutInPosition from "@/containers/channel/market/comps/putInPosition";
// import ShopMainCategory from "@/containers/channel/market/comps/shopMainCategory";
let tabMapList = []
let moverId = -1
const formItemLayoutChannel = {
  labelCol: { span: 6},
  wrapperCol: {
    span: 18
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};
const selectionCommodityUrl = {
  'daily': 'https://market.wapa.taobao.com/app/op-fe/o2o-selection-admin/index.html#/pool/list',
  'localdev': 'https://pre-kunlun.alibaba-inc.com/selection#/pool/list',
  'ppe': 'https://pre-kunlun.alibaba-inc.com/selection#/pool/list',
  'prod': 'https://selection.kunlun.alibaba-inc.com/#/pool/list'
}

function isHiddenMultilineIdText(text,form) {
  let indexsigh = text.indexOf('!');
  let indexEqual = text.indexOf('=');
  let lastIndexEqual = text.lastIndexOf('=');
  let name = indexsigh > 0 ? text.substring(0,indexsigh).trim() : text.substring(0,indexEqual).trim();
  let value = text.substring(lastIndexEqual+1).trim()
  return indexsigh > 0 ? form[name] != value : (indexEqual > 0 ? form[name] == value : true);
}

class CommodityDataSource extends React.Component{
  constructor(props) {
    super(props);
    // let _commodityCollection = JSON.parse(sessionStorage.getItem(props.selectionFields?props.selectionFields:'pureDataSourceList'));
    let _commodityCollection = props.getRankInfo(props.rankIndex,'pureDataSourceList');
    // let _commodityCollection = props.commodityCollection || {};
    debugger
    this.state = {
      rankIndex:props.rankIndex || 0,
      poolType: props.poolType? props.poolType : 1,//1是选品,2选店
      commodityCollection: _commodityCollection, //当前所有选品集的list
      commodityIndex: props.commodityIndex || 0 ,//当前修改的是第几个index
      categoryDataSource:[],
      oversupply:props.oversupply || {},
      resourceId:props.resourceId || "",
      formData:props.formData || {},
      selectionName:props.selectionName || "",
      selectionFields:props.selectionFields?props.selectionFields :'pureDataSourceList',
      maxCommodityLength: props.oversupply && props.oversupply.maxCommodityLength || props.maxCommodityLength,
      hidden: props.oversupply && props.oversupply.hidden || props.hidden,
      isNotRequired : typeof(props.oversupply) != 'undefined' && typeof(props.oversupply.isNotRequired)!= 'undefined' ? props.oversupply.isNotRequired : props.isNotRequired,
      topPoolsHidden: (props.oversupply && props.oversupply && props.oversupply.topPools && props.oversupply.topPools.hidden)
    }
  }

  componentDidMount() {
    // this.fetchSkuCategory()
    // 初始化
    this.initData(this.props.commodityCollection)
  }

  componentWillReceiveProps(newProps){
    this.setState({
      formData:newProps.formData,
      commodityIndex:newProps.commodityIndex
    })

    // this.setState({
    //   formData: newProps.formData,
    //   commodityIndex: newProps.commodityIndex,
    //   rankIndex: newProps.rankIndex,
    //   oversupply: newProps.oversupply || {},
    // },()=>{
    //   this.initData(newProps.commodityCollection)
    // })



    // this.initData(newProps.commodityCollection);
    // if (newProps.commodityCollection) {
    //   console.log('newProps.commodityCollection', newProps.commodityCollection);
    //   this.setState({
    //     commodityCollection: newProps.commodityCollection || {}
    //   })
    // }
  }

  initData = (commodityCollection)=>{
    const {commodityIndex,oversupply,selectionFields,rankIndex} = this.state;
    // console.log('commodityCollection', commodityCollection,rankIndex,commodityIndex);
    if (typeof commodityIndex == 'number') {
      let newCommodityCollection = deepCopy(commodityCollection || {})
      // let testInit = newCommodityCollection[commodityIndex];
      if (!newCommodityCollection || JSON.stringify(newCommodityCollection) == '{}') {
        let emptyObj = {
          supplyType:oversupply.defaultSupplyType ? oversupply.defaultSupplyType:1,
          shopList:[],
          poolIds:[],
          itemCat:[],
          categoryData:[],
          tabIndex: commodityIndex + 1,
          topItemPoolIds: [],
          topItemShopList: []
        };
        newCommodityCollection = JSON.parse(JSON.stringify(emptyObj));
        this.setState({commodityCollection:newCommodityCollection})
        let newPureDataSourceList = deepCopy(this.props.getRankInfo(rankIndex,'pureDataSourceList'))
        newPureDataSourceList[commodityIndex] = newCommodityCollection;
        this.props.setRankInfo(rankIndex, 'pureDataSourceList', newPureDataSourceList);
      }
    }
  }
  /*获取商品分类数据*/
  // fetchSkuCategory = async () => {
  //   try {
  //     let request = apiL.getSkuCategory;
  //     let resp = await request();
  //     const dataSource = resp.data.map((dataItem) => {
  //       dataItem.value = dataItem.value.toString();
  //       dataItem.children &&
  //       dataItem.children.map((subItem) => {
  //         subItem.value = subItem.value.toString();
  //         subItem.children &&
  //         subItem.children.map((thirdItem) => {
  //           thirdItem.value = thirdItem.value.toString();
  //         });
  //       });
  //       return dataItem;
  //     });
  //     this.setState({categoryDataSource:dataSource})
  //   } catch (error) {
  //     api.onRequestError(error);
  //   }
  // };

  checkDataSources = (rule, value, callback) => {
    if (value.length > this.state.maxCommodityLength) {
      callback(`${this.state.selectionName}选品集ID输入最多${this.state.maxCommodityLength}个`);
    } else {
      callback();
    }
  };

  onPoolChange = (value, actionType, commodityIndex, fieldKey, fieldDisplayKey) => {
    let {rankIndex} = this.state;
    if (value.length > this.state.maxCommodityLength) {
      Message.warning(`${this.state.selectionName}选品集ID输入最多${this.state.maxCommodityLength}个`);
    }else{
      if (actionType == "itemClick" || actionType == "tag") {
        // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
        let commodityCollection = deepCopy(this.props.getRankInfo(rankIndex,'pureDataSourceList'))
        // let {commodityCollection} = this.state;
        commodityCollection[commodityIndex][fieldKey] = value;
        const ext_pool_set = commodityCollection[commodityIndex][fieldDisplayKey]
        let newShopList = []
        if ( ext_pool_set && ext_pool_set.length > 0) {
          ext_pool_set.map((item)=>{
            if (value.includes(item.value)) {
              newShopList.push(item)
            }
          })
        }
        commodityCollection[commodityIndex][fieldDisplayKey] = Array.from(new Set(newShopList))
        this.setState({commodityCollection})
        if(this.props.saveCommodityDataSource){
          this.props.saveCommodityDataSource(commodityIndex,fieldDisplayKey,Array.from(new Set(newShopList)));
          this.props.saveCommodityDataSource(commodityIndex,fieldKey,value);
        }
        // sessionStorage.setItem(`${this.state.selectionFields}`, JSON.stringify(newCommodityCollection))
      }
    }
  };

  onCategoryChange = (value, data, commodityIndex) => {
    const haveSelected = data.map(item => {
      const {value, label, pos} = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    let {rankIndex} = this.state;
    let commodityCollection = deepCopy(this.props.getRankInfo(rankIndex, 'pureDataSourceList'))
    commodityCollection[commodityIndex].itemCat = haveSelected;
    this.setState({commodityCollection}, () => {
      if (this.props.saveCommodityDataSource) {
        this.props.saveCommodityDataSource(commodityIndex, 'itemCat', haveSelected);
      }
    })
    // sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(commodityCollection))
  };

  removeRepeat = (arr) =>{
    const hash = {};
    const newArray = arr.reduce((item, next)=>{
      hash[next.value] ? '' : hash[next.value] = true && item.push(next);
      return item;
    },[]);
    console.log(arr);
    console.log(newArray);
    return newArray;
  }

  onSearch = (keyword,commodityIndex, fieldKey, fieldDisplayKey) => {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    const {rankIndex} = this.state;
    this.searchTimeout = setTimeout(() => {
      if (keyword) {
        getPoolInfoSurge({searchKey: keyword, poolType: this.state.poolType}).then((data) => {
          const dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
            newPlatformFlag: item.newPlatformFlag
          }));
          // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
          let commodityCollection = deepCopy(this.props.getRankInfo(rankIndex,'pureDataSourceList'))
          // let {commodityCollection} = this.state;
          commodityCollection[commodityIndex][fieldDisplayKey] = this.removeRepeat([...(commodityCollection[commodityIndex][fieldDisplayKey] || []), ...dataSource]);
          this.setState({commodityCollection}, () => {
            if (this.props.saveCommodityDataSource) {
              this.props.saveCommodityDataSource(commodityIndex, fieldDisplayKey, this.removeRepeat([...(commodityCollection[commodityIndex][fieldDisplayKey] || []), ...dataSource]));
            }
          });
        })
      } else {
        // let {commodityCollection} = this.state;
        let commodityCollection = deepCopy(this.props.getRankInfo(rankIndex,'pureDataSourceList'))
        // let {commodityCollection} = this.state;
        commodityCollection[commodityIndex][fieldDisplayKey] = this.removeRepeat([...commodityCollection[commodityIndex][fieldDisplayKey]]);
        this.setState({commodityCollection}, () => {
          if (this.props.saveCommodityDataSource) {
            this.props.saveCommodityDataSource(commodityIndex, fieldDisplayKey, this.removeRepeat([...commodityCollection[commodityIndex][fieldDisplayKey]]));
          }
        });
        // sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(commodityCollection))
      }
    }, 800);
  };

  // 供给来源改变
  supplyTypeChange = (value,commodityIndex) => {
    let commodityCollection = deepCopy(this.props.getRankInfo(this.state.rankIndex,'pureDataSourceList'))
    commodityCollection[commodityIndex].supplyType = value;
    if (value == 1) {
      commodityCollection[commodityIndex].itemCat = [];
    }else{
      commodityCollection[commodityIndex].poolIds = []
    }
    if (this.props.saveCommodityDataSource) {
      let _key = (value == 1) ? 'itemCat' : 'poolIds';
      this.props.saveCommodityDataSource(commodityIndex, 'supplyType', value);
      this.props.saveCommodityDataSource(commodityIndex, _key, []);
    }
    this.setState({commodityCollection})
    // sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(commodityCollection))
  }

  render() {
    let {commodityCollection = [], commodityIndex = 0, categoryDataSource = [], oversupply = {},resourceId = "",formData = {},maxCommodityLength,selectionName,hidden = '',isNotRequired = false,rankIndex, topPoolsHidden = true} = this.state;
    let {isNotNull} = oversupply; // isNotRequired 兼容之前的情况，默认必填 (isRequired 弃用)
    const isHidden = isHiddenMultilineIdText(hidden,this.props.formData)
    const isNotNullNow = isNotNull || ['100090002','90002'].includes(resourceId) && formData.sceneType == 1;
    // console.log('commodityCollection',commodityCollection);
    let {supplyType = 1,shopList = [],poolIds = [], itemCat = [], topItemPoolIds = [], topItemShopList = []} = commodityCollection[commodityIndex] || {}
    return isHidden && <div key={`${rankIndex}-${commodityIndex}`}>
      <Form.Item
        {...formItemLayoutChannel}
        label={`${selectionName? selectionName+'-':""}${isNotNullNow?"判空":""}供给来源:`}
        requiredMessage="供给来源不能为空"
        required={!isNotRequired}
        hasFeedback
      >
        <Select
          value={supplyType}
          name="supplyType"
          style={{ width: "100%" }}
          dataSource={supplyTypeDataSource}
          onChange={value => this.supplyTypeChange(value,commodityIndex)}
        />
      </Form.Item>
      {supplyType == 1 && <>
        <Form.Item
          {...formItemLayoutChannel}
          label={`${selectionName? selectionName+'-':""}${isNotNullNow?"判空":""}选品集ID:`}
          requiredMessage="选品集ID不能为空"
          required={!isNotRequired}
          hasFeedback
        >
          <Select
            mode="multiple"
            showSearch
            placeholder={`请输入选品集ID${maxCommodityLength?'，支持输入最多'+maxCommodityLength+'个':''}`}
            name="poolIds"
            value={poolIds}
            onChange={(value, actionType) => this.onPoolChange(value, actionType,commodityIndex,"poolIds","shopList")}
            onSearch={key => this.onSearch(key,commodityIndex,"poolIds","shopList")}
            dataSource={shopList}
            style={{ width: "100%" }}
          />
          <a href={selectionCommodityUrl[window.configEnv]} target="_blank">去创建</a>
        </Form.Item>
      </>
      }
      {supplyType == 2 && <Form.Item
        {...formItemLayoutChannel}
        label={`${selectionName? selectionName+'-':""}${isNotNull?"判空":""}商品类目:`}
        requiredMessage={`${selectionName? selectionName+'-':""}商品类目不能为空`}
        required={!isNotRequired}
        hasFeedback
      >
        <CascaderSelect
          showSearch
          multiple={true}
          expandTriggerType={"hover"}
          placeholder="请选择商品类目"
          name="category"
          value={itemCat.map(i =>{return i.value})}
          onChange={(value,data) => this.onCategoryChange(value,data,commodityIndex)}
          dataSource={this.props.skuCategoryData || []}
          style={{ width: "100%" }}
        />
      </Form.Item>}
      { !topPoolsHidden && <Form.Item
        {...formItemLayoutChannel}
        label={`置顶选品集ID:`}
        requiredMessage="置顶选品集ID不能为空"
        required={!isNotRequired}
        hasFeedback
      >
        <Select
          mode="multiple"
          showSearch
          placeholder={`请输入选品集ID${maxCommodityLength?'，支持输入最多'+maxCommodityLength+'个':''}`}
          name="topItemPoolIds"
          value={topItemPoolIds}
          onChange={(value, actionType) => this.onPoolChange(value, actionType, commodityIndex, "topItemPoolIds", "topItemShopList")}
          onSearch={key => this.onSearch(key,commodityIndex, "topItemPoolIds", "topItemShopList")}
          dataSource={topItemShopList}
          style={{ width: "100%" }}
        />
        <a href={selectionCommodityUrl[window.configEnv]} target="_blank">去创建</a>
      </Form.Item>}

    </div>
  }
}
function MultipleRankDragger(props) {
  return <DndProvider backend={HTML5Backend}>
    <MultipleSupplySources {...props}></MultipleSupplySources>
  </DndProvider>
}

function MultipleSupplySources(props){
  // const [commodityCollection,setCommodityCollection] = useState(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || [])
  const [commodityCollection,setCommodityCollection] = useState( [])
  const {maxTabLength = 1, nameSetting = "", name = "", field = "", initTabLength = 1} = props.oversupply;
  const {showMultilineIdText, showItemCat, rankIndex, rankField, setRankInfo, getRankInfo, itemIndex, getRankInfoByIndex} = props;
  let requestedFrame;
  const rootRef = useRef(null);


  /**
   * 根据initTabLength初始化个数多tab
   * */
  const initMuiltipleData = () => {
    const dataSourceTabs = getRankInfoByIndex(itemIndex, 'pureDataSourceList');
    let _tabLength = (dataSourceTabs && dataSourceTabs.length > 0) ? dataSourceTabs.length : initTabLength;
    if (_tabLength >= 1 && commodityCollection.length <= 0) { //只有在初始化时候需要
      let _tabs = [];
      for (var i = 1; i <= _tabLength; i++) {
        _tabs.push({});
      }
      setCommodityCollection(_tabs);
    }
  }

  useEffect(()=>{
    initMuiltipleData();
    // initList();
    return ()=>{
      if (requestedFrame !== undefined) {
        cancelAnimationFrame(requestedFrame)
      }
    }
  },[]);


  // 卡片上移
  const cardMoveUp = (index)=>{
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex,'pureDataSourceList'))
    let newCommodityItem = newCommodityCollection[index - 1];
    newCommodityCollection[index - 1] = newCommodityCollection[index]
    newCommodityCollection[index] = newCommodityItem
    setCommodityCollection(newCommodityCollection)
    setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection)
    // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
  }

  // 卡片下移
  const cardMoveDown = (index)=>{
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex,'pureDataSourceList'))
    // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || [])
    let newCommodityItem = newCommodityCollection[index + 1];
    newCommodityCollection[index + 1] = newCommodityCollection[index]
    newCommodityCollection[index] = newCommodityItem
    setCommodityCollection(newCommodityCollection)
    setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection)
    // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
  }

  // 卡片删除
  const cardDelete = (index)=>{
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex,'pureDataSourceList'))
    // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || [])
    delete newCommodityCollection[index]
    newCommodityCollection = newCommodityCollection.filter(i => i)
    setCommodityCollection(newCommodityCollection)
    setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection)
    // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
  }

  // 卡片新增
  const cardAdd = ()=>{
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex,'pureDataSourceList'))
    // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || [])
    if (maxTabLength && newCommodityCollection.length == maxTabLength) {
      Message.error(`最多设置${maxTabLength}个`)
    }else{
      newCommodityCollection.push({})
      setCommodityCollection(newCommodityCollection)
      setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection)
      // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
    }
  }

  // 自定义表单onchange
  const inputOnChange = (key,value,index) => {
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex, 'pureDataSourceList') || []);
    // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || [])
    newCommodityCollection[index][key] = value
    setCommodityCollection(newCommodityCollection)
    setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection)
    // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
  }

  /**
   * 拖动卡片
   * @param {当前卡片id} id
   * @param {移动到卡片id} moveId
   */
  const moveCard = (id, moveId) => {
    if (!requestedFrame && id !=moveId && moveId != -1) {
      requestedFrame = requestAnimationFrame(()=>{
        let newCommodityCollection = deepCopy(getRankInfo(rankIndex,'pureDataSourceList'))
        // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || [])
        let exchangeMap;
        exchangeMap = newCommodityCollection[moveId]
        newCommodityCollection[moveId] = newCommodityCollection[id]
        newCommodityCollection[id] = exchangeMap
        if (id > moveId) {
          for (let index = moveId + 1; index <= id; index++) {
            let exchangeMapTemporary;
            exchangeMapTemporary = newCommodityCollection[index]
            newCommodityCollection[index] = exchangeMap
            exchangeMap = exchangeMapTemporary
          }
        }else{
          for (let index = moveId -1 ; index >= id; index--) {
            let exchangeMapTemporary;
            exchangeMapTemporary = newCommodityCollection[index]
            newCommodityCollection[index] = exchangeMap
            exchangeMap = exchangeMapTemporary
          }
        }
        setCommodityCollection(newCommodityCollection)
        setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection)
        // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
        requestedFrame = undefined
      })
    }
  }

  const [{isOver}, drop] = useDrop({
    accept:'card',
    drop:({id}) => {
      return {id}
    },
    hover:(props,monitor)=>{
      const {id, ref, ref2, commodityLength} = props;
      deleteClassListClassName()
      document.getElementsByClassName('right-part')[0].style.overflowY = 'hidden'
      let buttonRef = ref.current.getBoundingClientRect()
      let cardRef = ref2.current.getBoundingClientRect()
      let translation = monitor.getDifferenceFromInitialOffset()
      const buttonToCard = buttonRef.y - cardRef.y
      let translationTop = 0
      let element = document.getElementsByClassName("tabCard")
      if(translation.y < 0) {
        translationTop = tabMapList[id].start + ( buttonToCard + translation.y)
        tabMapList.map((item,index)=>{
          if (index != id) {
            if (index == 0) {
              if (translationTop < item.middle) {
                element[index].classList.add('drop-over-upward')
                moverId = 0
              }else if(translationTop >= item.middle && translationTop < item.end){
                element[index].classList.add('drop-over-downward')
                moverId = index + 1
              }
            }else{
              if (translationTop < item.middle && translationTop >= tabMapList[index - 1].end) {
                element[index].classList.add('drop-over-upward')
                moverId = index
              }else if(translationTop >= item.middle && translationTop < item.end){
                element[index].classList.add('drop-over-downward')
                moverId = index + 1
              }
            }
          }
        })
      }else{
        translationTop = tabMapList[id].start + translation.y + buttonToCard
        tabMapList.map((item,index)=>{
          if (index != id) {
            if (index == commodityLength - 1) {
              if (translationTop >= item.middle) {
                element[index].classList.add('drop-over-downward')
                moverId = commodityLength - 1
              }else if(translationTop < item.middle && translationTop > item.start){
                element[index].classList.add('drop-over-upward')
                moverId = index - 1
              }
            }else{
              if (translationTop >= item.middle && translationTop < tabMapList[index + 1].start) {
                element[index].classList.add('drop-over-downward')
                moverId = index
              }else if(translationTop < item.middle && translationTop > item.start){
                element[index].classList.add('drop-over-upward')
                moverId = index - 1
              }
            }
          }
        })
      }
    },
    collect: monitor => ({
      isOver: !!monitor.isOver(),
    }),
  })
  drop(rootRef)

  return <div ref={rootRef} className='multiple-tab-father'>
    <Row className='multiple-tab'>
      <Col span={4} style={{width:'200px',maxWidth: '200px'}}>
        <div className='multiple-tab-title'>{nameSetting}</div>
      </Col>
      <Col span={18}>
        <div className='multiple-tab-context-dragger'>
          {commodityCollection.map((item,index)=>{
            return <MultipleSupplyCardItem
              id={index}
              commodityLength={commodityCollection.length}
              item={item}
              index={index}
              oversupply={props.oversupply}
              resourceId={props.resourceId}
              cardMoveUp={cardMoveUp}
              cardMoveDown={cardMoveDown}
              cardDelete={cardDelete}
              inputOnChange={inputOnChange}
              moveCard={moveCard}
              showMultilineIdText={showMultilineIdText}
              showItemCat={showItemCat}
              getRankInfo={getRankInfo}
              setRankInfo={setRankInfo}
              rankIndex = {rankIndex}
              rankField={rankField}
              commodityCollection={commodityCollection}
              skuCategoryData={props.skuCategoryData || []}
            ></MultipleSupplyCardItem>
          })}
        </div>
        <Button text onClick={()=>cardAdd()}>新增</Button>
      </Col>
    </Row>
  </div>
}
function deleteClassListClassName(){
  let tabCardList = document.getElementsByClassName("tabCard")
  let map;
  for (let index = 0; index < tabCardList.length; index++) {
    map = {}
    tabCardList[index].classList.remove('drop-over-downward')
    tabCardList[index].classList.remove('drop-over-upward')
    map.height = tabCardList[index].clientHeight
    if (index == 0) {
      map.start = 0
      map.middle = tabCardList[index].clientHeight / 2
      map.end = tabCardList[index].clientHeight + 20
      tabMapList[index] = map
    }else{
      map.start = tabMapList[index - 1].end;
      map.middle = map.start + tabCardList[index].clientHeight / 2
      map.end = map.start + tabCardList[index].clientHeight + 20
      tabMapList[index] = map
    }
  }
}
function MultipleSupplyCardItem(props){
  const {id, index, commodityLength, cardMoveUp, cardMoveDown, cardDelete, inputOnChange, oversupply, resourceId, moveCard, showMultilineIdText, showItemCat, getRankInfo, setRankInfo, rankIndex,rankField} = props
  const { name = "", field = "", maxFieldLength, hideTabName = false} = oversupply;
  const ref =
    (null)
  const ref2 =
    (null)
  const [{ opacity, isDragging }, connectDrag, preview] = useDrag({
    item:{ type: "card", id, ref, ref2, commodityLength},
    collect: (monitor) => ({
      opacity: monitor.isDragging() ? 0.4 : 1,
      isDragging: !!monitor.isDragging(),
    }),
    end:(props, monitor) => {
      // 批量去除类名
      deleteClassListClassName()
      document.getElementsByClassName('right-part')[0].style.overflowY = 'scroll'
      moveCard(id,moverId)
    }
  })
  connectDrag(ref)
  preview(ref2)

  let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(rankField)) || []);
  let _newCommodityCollection = (newRankInfo[rankIndex] && newRankInfo[rankIndex]['pureDataSourceList']) ? newRankInfo[rankIndex]['pureDataSourceList'] : [];
  const [newCommodityCollection, setNewCommodityCollection] = useState(_newCommodityCollection);
  const _commodityItem = _newCommodityCollection[index];
  // console.log('newCommodityCollection 22', newRankInfo, _newCommodityCollection, _commodityItem);
  const _tabName = (_commodityItem && _commodityItem[field]) ? _commodityItem[field]:'';
  const [commodityItem, setCommodityItem] = useState(_commodityItem);
  const [tabName, setTabName] = useState(_tabName);

  useEffect(() => {
    // fetchSkuCategory();
    // initList();
  }, []);

  useEffect(()=>{
    console.log(props,multilineIdText);
    // initNewCommodityCollection();
  },[multilineIdText])

  const initNewCommodityCollection = () =>{
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(rankField)) || []);
    let _newCommodityCollection = (newRankInfo[rankIndex] && newRankInfo[rankIndex]['pureDataSourceList']) ? newRankInfo[rankIndex]['pureDataSourceList'] : [];
    const _commodityItem = newCommodityCollection[index];
    setNewCommodityCollection(_newCommodityCollection);
    setCommodityItem(_commodityItem);
  }

//   const initList = () =>{
//     let newCommodityCollection = deepCopy(getRankInfo(rankIndex, 'pureDataSourceList') || []);
//     let testInit = newCommodityCollection[rankIndex]
//     if (!testInit || JSON.stringify(testInit) == '{}') {
//       newCommodityCollection[index] = {
//         supplyType: oversupply.defaultSupplyType ? oversupply.defaultSupplyType : 1,
//         shopList: [],
//         poolIds: [],
//         itemCat: [],
//         categoryData: [],
//         tabIndex: index + 1
//       }
//     }
//     setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection);
//     setNewCommodityCollection(newCommodityCollection);
// };

  const [categoryDataSource, setCategoryDataSource] = useState([]);
  const _multilineIdText = _commodityItem && showMultilineIdText && showMultilineIdText.length > 0 && _commodityItem[showMultilineIdText[0].key] ? _commodityItem[showMultilineIdText[0].key] : '';
  const [multilineIdText, setMultilineIdText] = useState(_multilineIdText);
  const _itemCatData = _commodityItem && _commodityItem[showItemCat.field] ? _commodityItem[showItemCat.field].map((o) => o.value) : []
  const [itemCatData, setItemCatData] = useState(_itemCatData);

  /*获取商品分类数据*/
  // const fetchSkuCategory =  () => {
  //   try {
  //     let request = apiL.getSkuCategory;
  //     request().then((resp)=>{
  //       const dataSource = resp.data.map((dataItem) => {
  //         dataItem.value = dataItem.value.toString();
  //         dataItem.children &&
  //         dataItem.children.map((subItem) => {
  //           subItem.value = subItem.value.toString();
  //           subItem.children &&
  //           subItem.children.map((thirdItem) => {
  //             thirdItem.value = thirdItem.value.toString();
  //           });
  //         });
  //         return dataItem;
  //       });
  //       setCategoryDataSource(dataSource);
  //     })
  //
  //   } catch (error) {
  //     api.onRequestError(error);
  //   }
  // };

  const changMultilineIdText = (text,key) => {
    setMultilineIdText(text);
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex, 'pureDataSourceList') || []);
    // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || [])
    newCommodityCollection[index][key] = text;
    setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection);
    // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
  }

  const tabNameOnChange = (field,e,index) =>{
    props.inputOnChange(field,e,index);
    setTabName(e);
  }

  const onCategoryChange = (value,data,key) =>{
    const haveSelected = data.map(item => {
      const {value, label, pos} = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex, 'pureDataSourceList') || []);
    // let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem('pureDataSourceList')) || []);
    newCommodityCollection[index][key] = haveSelected;
    setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection);
    // let _itemCatData = haveSelected.map((o) => o.value) || [];
    setItemCatData(value);
    // sessionStorage.setItem('pureDataSourceList',JSON.stringify(newCommodityCollection))
  }

  const saveCommodityDataSource = (commodityIndex,key,data) =>{
    debugger
    console.log('saveCommodityDataSource',commodityIndex,key,data,rankIndex);
    let newCommodityCollection = deepCopy(getRankInfo(rankIndex, 'pureDataSourceList') || []);
    newCommodityCollection[index][key] = data;
    // console.log('newCommodityCollection',commodityCollection,newCommodityCollection);
    // newCommodityCollection.map((o,index) => {
    //   console.log(o);
    //   if (o['topClusterIdList'] && o['topClusterIdList']!='') {
    //     commodityCollection[index]['topClusterIdList'] = o['topClusterIdList'];
    //   }
    //   if (o['topItemBrandIdList'] && o['topItemBrandIdList']!='') {
    //     commodityCollection[index]['topItemBrandIdList'] = o['topItemBrandIdList'];
    //   }
    //   if (o['topItemCatList'] && o['topItemCatList']!='') {
    //     commodityCollection[index]['topItemCatList'] = o['topItemCatList'];
    //   }
    // })
    setNewCommodityCollection(newCommodityCollection);
    setRankInfo(rankIndex,'pureDataSourceList',newCommodityCollection);
  }


  return <div ref={ref2} style={{width: 580,opacity:opacity}} id={`tabCard-${rankIndex}-${index}`} className={`tabCard`}>
    <Card free style={{ width: '100%' }} key={index}>
      <Card.Header
        title={<i className="order-num">{index + 1}</i>}
        extra={<>
          {index != 0 && <Button text onClick={()=>cardMoveUp(index)}>上移</Button>}
          {index != commodityLength -1 && <Button text onClick={()=>cardMoveDown(index)}>下移</Button>}
          {<Button text onClick={()=>cardDelete(index)}>删除</Button>}
        </>}
      />
      <Card.Content>
        <div className='dragger' ref={ref}></div>
        {(!hideTabName && field)&&<Form.Item
          {...formItemLayoutChannel}
          label={name+":"}
          requiredMessage={name+"不能为空"}
          required
          hasFeedback
        >
          <Input name={field} value={tabName} onChange={(e)=>tabNameOnChange(field,e,index)} placeholder={"请输入"+name} maxLength={maxFieldLength?maxFieldLength:6}></Input>
        </Form.Item>}
        <div key={rankIndex}>
          <CommodityDataSource
            setRankInfo={setRankInfo}
            getRankInfo={getRankInfo}
            rankField={rankField}
            rankIndex={rankIndex}
            commodityIndex={index}
            commodityCollection={newCommodityCollection[index]}
            oversupply={oversupply}
            resourceId={resourceId}
            saveCommodityDataSource={saveCommodityDataSource}
            skuCategoryData={props.skuCategoryData || []}
          />
        </div>
        <div style={{marginLeft:'-55px'}}  className='newputin'>
          {(showMultilineIdText) && showMultilineIdText.map((item) =>{
            const { name, maxLength, key, placeholder, hidden = false, required = false } = item;
            if(!hidden) {
              return <Form.Item label={`${name}:`} {...formItemLayoutChannel} required={required}>
                <Input.TextArea
                  style={{width: '302px'}}
                  name={`${key}`}
                  placeholder={`请输入${placeholder || name},多个${placeholder || name}使用英文逗号隔开,至多${maxLength}个`}
                  onChange={(value) => changMultilineIdText(value, key)}
                  value={multilineIdText}
                ></Input.TextArea>
              </Form.Item>
            }
          })}
        </div>
        {(showItemCat && showItemCat.hasOwnProperty('name') && !showItemCat.hidden) && <Form.Item
          {...formItemLayoutChannel}
          label={`${showItemCat.name}`}
          hasFeedback
        >
          <CascaderSelect
            showSearch
            multiple={true}
            expandTriggerType={"hover"}
            placeholder={`请选择${showItemCat.name}`}
            name={`${showItemCat.field}`}
            value={itemCatData}
            dataSource={props.skuCategoryData || []}
            onChange={(value,data) => onCategoryChange(value,data,showItemCat.field)}
            style={{ width: "100%" }}
          />
        </Form.Item>}

      </Card.Content>
    </Card>
  </div>
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 4,
  },
  wrapperCol: {
    span: 18
  },
}

const formItemLayoutChannel3 = {
  labelCol: { span: 4},
  wrapperCol: {
    span: 17
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

class ShopMainCategory extends React.Component {
  constructor(props) {
    super(props);
    let _recallRule = props.getRankInfo(props.rankIndex,'recallRule');
    let _shopMajorCategoryList = (_recallRule && _recallRule['shopMajorCategoryContent']) ? _recallRule['shopMajorCategoryContent'] : [];
    let _shopMainCategory = _shopMajorCategoryList.map((o) => o.value) || [];
    this.state = {
      shopMainCategoryDataSource: [],
      shop_main_category: _shopMajorCategoryList, //list map
      shopMajorCategoryList:  _shopMainCategory, //list string
      showMainCategory: this.props.showMainCategory || {}
    }
  }
  componentDidMount() {
    // this.getMainCategory();
  }

  componentWillReceiveProps(nextProps) {
    let _recallRule = nextProps.getRankInfo(nextProps.rankIndex,'recallRule');
    let _shopMajorCategoryList = (_recallRule && _recallRule['shopMajorCategoryContent']) ? _recallRule['shopMajorCategoryContent'] : [];
    let _shopMainCategory = _shopMajorCategoryList.map((o) => o.value) || [];
    this.setState({
      shopMajorCategoryList:  _shopMainCategory, //list string
    })
  }

  // getMainCategory = () => {
  //   getStoreMajorCategory().then((result) => {
  //     this.setState({
  //       shopMainCategoryDataSource: result.map((item)=>{
  //         return {
  //           label:item.name,
  //           value:item.code
  //         }
  //       })
  //     })
  //   }).catch(api.onRequestError);
  // }



  changMainCategory = (newShopMainCategory,data) => {
    const newData = data.map(item => {
      const {value, label, pos} = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    this.setState( {shop_main_category: newData,shopMajorCategoryList: newShopMainCategory},()=>{
      sessionStorage.setItem("shop_main_category",JSON.stringify(this.state.shop_main_category));
      sessionStorage.setItem("shopMajorCategoryList",JSON.stringify(this.state.shopMajorCategoryList));
      if(this.props.saveShopMainCategory){
        this.props.saveShopMainCategory(this.state.shop_main_category);
      }
    })
  }

  render()
  {
    const { required = false } = this.state.showMainCategory || {};
    return (
      <Form className='newputin'>
        <Form.Item label="商家主营类目:" {...formItemLayoutChannel} required={required}>
          <CascaderSelect
            showSearch
            style={{ width: '302px' }}
            name="shopMainCategory"
            multiple
            value={this.state.shopMajorCategoryList}
            dataSource={this.props.shopMainCategoryData}
            onChange={(value,item) => this.changMainCategory(value,item)}
          />
        </Form.Item>
      </Form>
    )
  }
}

export default function RankSet(props) {
  const [refresh, setRefresh] = useState(false);
  const [newRankInfo, setNewRankInfo] = useState([]);
  const [curTab, setCurTab] = useState(1);
  const {rankSchema = {}, rankData = {}, rankInfoDisplayType = '' } = props;
  const {isHidePosition} = (rankSchema && rankSchema.length > 0) ? rankSchema[0] : false;
  const [shopMainCategoryData, setShopMainCategoryData] = useState([]);
  const [skuCategoryData, setSkuCategoryData] = useState([]);

  const boardCategory = props.boardCategory || '1';

  useEffect(()=>{
    initRank();
  },[rankSchema])

  useEffect(() => {
    // emptyInitRank();
    // let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem('rankInfo')) || []);
    // changeTab(newRankInfo && newRankInfo.length > 0 ? newRankInfo[0].index : 1);
  }, [boardCategory])

  // props.changeBoardCategory = (value) =>{
  //   console.log(value);
  //   emptyInitRank();
  // }

  const changeTab = (index) =>{
    console.log('changeTab',index);
    setCurTab(index);
  };

  const initRank = () => {
    // sessionStorage.removeItem('pureDataSourceList');
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem('rankInfo')) || []);
    if (!newRankInfo.length && rankSchema && rankSchema.length > 0) {
      rankSchema.map((o, index) => {
        newRankInfo.push({
          type: o.type,
          index: 1 + index,
          pureDataSourceList: [{}],
          recallRule: {shopMajorCategoryContent: [], openStatusSet: [], maxDeliveryPrice: null}
        })
      })
      // newRankInfo = [
      //   {
      //     type: 1,
      //     index: 1,
      //     pureDataSourceList: [{}],
      //     recallRule: {shopMajorCategoryContent: [], openStatusSet: [], maxDeliveryPrice: null}
      //   },
      //   {
      //     type: 2,
      //     index: 2,
      //     pureDataSourceList: [{}],
      //     recallRule: {shopMajorCategoryContent: [], openStatusSet: [], maxDeliveryPrice: null}
      //   },
      // ];
      setNewRankInfo(newRankInfo);
      sessionStorage.setItem('rankInfo', JSON.stringify(newRankInfo));
    } else if (rankSchema.length > newRankInfo.length) {
      let _newRankInfo = [];
      rankSchema.map((o, index) => {
        let newRankInfoIndexGroup = newRankInfo.map((p) => p.index);
        if (!newRankInfoIndexGroup.includes(o.index)) {
          _newRankInfo.push({
            type: o.type,
            index: 1 + index,
            pureDataSourceList: [{}],
            recallRule: {shopMajorCategoryContent: [], openStatusSet: [], maxDeliveryPrice: null}
          })
        } else {
          let thatRankGroup = newRankInfo.filter((i) => i.index == o.index);
          _newRankInfo.push(thatRankGroup[0]);
        }
      })
      changeTab(newRankInfo[0].index);
      setNewRankInfo(_newRankInfo);
      sessionStorage.setItem('rankInfo', JSON.stringify(_newRankInfo));
    } else {
      setNewRankInfo(newRankInfo);
      changeTab(newRankInfo[0].index);
    }
  };

  const setRankInfo = (i, key, value) => {
    // sessionStorage.removeItem('pureDataSourceList');
    let currentTab = rankSchema[i];
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(currentTab.field)) || []);
    if(newRankInfo[i]) {
      newRankInfo[i][key] = value;
      sessionStorage.setItem(currentTab.field, JSON.stringify(newRankInfo));
    }
  }

  const getRankInfo = (i, key) => {
    let currentTab = rankSchema[i];
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(currentTab.field)) || []);
    let rankItem = (newRankInfo && newRankInfo.length > 0 && newRankInfo[i]) ? newRankInfo[i] : {};
    return rankItem[key] ? rankItem[key] : {};
  }

  // 取schema和rankIndex自带的index
  const getRankInfoByIndex = (itemIndex, key) => {
    let currentTabGroup = rankSchema.filter((o) => o.index == itemIndex);
    let currentTab = currentTabGroup && currentTabGroup.length > 0 ? currentTabGroup[0] : {};
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(currentTab.field)) || []);
    console.log('getRankInfo',newRankInfo);
    let rankItem = {};
    // if(newRankInfo && newRankInfo.length > 0 && newRankInfo[i]) ? newRankInfo[i] : {};
    if (newRankInfo && newRankInfo.length > 0) {
      const rankItemGroup = newRankInfo.filter((o) => o.index == itemIndex);
      rankItem = rankItemGroup && rankItemGroup.length > 0 ? rankItemGroup[0] : {};
    }
    return rankItem[key] ? rankItem[key] : {};
  }

  // const testSchema = [
  //   {
  //   "name": '人气榜',
  //   "field": "rankInfo",
  //   "schema": {
  //     "oversupply": { //全能超市专用
  //       "maxTabLength": 2, //最多有几个TAB
  //       "maxCommodityLength": 2, //选品集最多有几个
  //       "nameSetting": "tab设置（1-6）", //整个TAB的lable名称
  //       "name": "tab名称", // 每个TAB下多出来的字段的名称
  //       "field": "tabName", //每个TAB下多出来的字段的key，有此字段才会展示
  //       "maxFieldLength": 4, //每个TAB下多出来的字段的最大字数
  //       "isAllowShow": true, //本资源位没用到的,是否一直展示供给类型
  //       "isRequired": false, // 是否必填  弃用
  //       "isNotRequired": false, // 非必填
  //       "initTabLength": 2, // 初始化几个tab
  //       "hideTabName": false  //隐藏tab名称
  //     },
  //     "showItemCat": {
  //       "name":"指定叶子类目置顶",
  //       "field":"topItemCatList"
  //     },
  //     "showMultilineIdText": [{
  //       "key": "topClusterIdList",
  //       "name": "指定商品置顶",
  //       "placeholder": "指定商品置顶",
  //       "required": false,
  //       "maxLength": 20,
  //     }],
  //     "detail": {
  //       "title": "商品召回规则：",
  //       "required": ["configName"],
  //       "field": "recallRule",
  //       "type": "object",
  //       "properties": {
  //         "openStatusSet": {
  //           "type": "string",
  //           "title": "时段:",
  //           "enum": ["1", "5", "4", "0"],
  //           "x-ui-className": "selection-form",
  //           "x-ui-valueLabel": {
  //             "1": "预定中",
  //             "5": "营业中",
  //             "4": "即将休息",
  //             "0": "休息中"
  //           },
  //           "default": "0",
  //           "x-ui-widget": "radio"
  //         },
  //         "shopScatter": {
  //           "type": "string",
  //           "title": "是否按照店铺打散:",
  //           "enum": ["1", "0"],
  //           "x-ui-className": "selection-form",
  //           "x-ui-valueLabel": {
  //             "1": "是",
  //             "0": "否",
  //           },
  //           "default": "0",
  //           "x-ui-widget": "radio"
  //         },
  //         "maxDeliveryPrice": {
  //           "title": "最大配送费：",
  //           "type": "number",
  //           "maximum": 999999,
  //           "hasLimitHint": true
  //         },
  //       }
  //     },
  //   }
  // },
  //   {
  //   "name": '品牌榜',
  //   "field": "rankInfo",
  //   "schema": {
  //     "oversupply": { //全能超市专用
  //       "maxTabLength": 2, //最多有几个TAB
  //       "maxCommodityLength": 2, //选品集最多有几个
  //       "nameSetting": "tab设置（1-6）", //整个TAB的lable名称
  //       "name": "tab名称", // 每个TAB下多出来的字段的名称
  //       "field": "tabName", //每个TAB下多出来的字段的key，有此字段才会展示
  //       "maxFieldLength": 4, //每个TAB下多出来的字段的最大字数
  //       "isAllowShow": true, //本资源位没用到的,是否一直展示供给类型
  //       "isRequired": false, // 是否必填  弃用
  //       "isNotRequired": false, // 非必填
  //       "initTabLength": 2, // 初始化几个tab
  //       "hideTabName": false  //隐藏tab名称
  //     },
  //     "showMultilineIdText": [{
  //       "key": "topItemBrandIdList",
  //       "name": "指定品牌置顶",
  //       "placeholder": "指定品牌置顶",
  //       "required": false,
  //       "maxLength": 20,
  //     }],
  //     "detail": {
  //       "title": "商品召回规则：",
  //       "required": ["configName"],
  //       "type": "object",
  //       "properties": {
  //         "openStatusSet": {
  //           "type": "string",
  //           "title": "时段:",
  //           "enum": ["1", "5", "4", "0"],
  //           "x-ui-className": "selection-form",
  //           "x-ui-valueLabel": {
  //             "1": "预定中",
  //             "5": "营业中",
  //             "4": "即将休息",
  //             "0": "休息中"
  //           },
  //           "default": "0",
  //           "x-ui-widget": "radio"
  //         },
  //         "shopScatter": {
  //           "type": "string",
  //           "title": "是否按照店铺打散:",
  //           "enum": ["1", "0"],
  //           "x-ui-className": "selection-form",
  //           "x-ui-valueLabel": {
  //             "1": "是",
  //             "0": "否",
  //           },
  //           "default": "0",
  //           "x-ui-widget": "radio"
  //         },
  //         "maxDeliveryPrice": {
  //           "title": "最大配送费：",
  //           "type": "number",
  //           "maximum": 999999,
  //           "hasLimitHint": true
  //         },
  //       }
  //     },
  //   }
  // }];

  const onNameChange = (value,rankIndex) =>{
    let currentTab = rankSchema[rankIndex];
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(currentTab.field)) || []);
    newRankInfo[rankIndex]['name'] = value;
    newRankInfo[rankIndex]['index'] = 1 + rankIndex;
    newRankInfo[rankIndex]['type'] = currentTab.type;
    setNewRankInfo(newRankInfo);
    sessionStorage.setItem(currentTab.field, JSON.stringify(newRankInfo));
  }

  const onFormItemChange = (rankIndex,itemData) => {
    let currentTab = rankSchema[rankIndex];
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(currentTab.field)) || []);
    if(newRankInfo[rankIndex] && newRankInfo[rankIndex]['recallRule']) {
      for (const o in itemData) {
        newRankInfo[rankIndex]['recallRule'][o] = itemData[o];
      }
      setNewRankInfo(newRankInfo);
      sessionStorage.setItem(currentTab.field, JSON.stringify(newRankInfo));
    }
    // // console.log('itemData', itemData, _multipleSchemaData);
    // setMultipleSchemaData(_multipleSchemaData);
    // sessionStorage.setItem("multipleSchemaData",JSON.stringify(_multipleSchemaData));
  }

  useEffect(() => {
    refresh && setTimeout(() => setRefresh(false))
  }, [refresh])

  const saveShopMainCategory = (data,rankIndex) =>{
    let currentTab = rankSchema[rankIndex];
    let newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(currentTab.field)) || []);
    newRankInfo[rankIndex]['recallRule']['shopMajorCategoryContent'] = data;
    setNewRankInfo(newRankInfo);
    sessionStorage.setItem(currentTab.field, JSON.stringify(newRankInfo));
  }

  const positionData  = [];

  // const positionData = [
  //   {type: '1', name:'全能商厦首页推荐', location: '2', scheduleId: ''},
  //   {type: '100090001', name:'全能商厦首页推荐', location: '3', scheduleId: '201220320', scheduleName: '腌腊制品'},
  //   {type: '100090001', name: '全能商厦金刚tab', location: '44', scheduleId: '201220321', scheduleName: '腌腊制品2'}
  // ]

  useEffect(()=>{
    getMainCategory();
    fetchSkuCategory();
  },[])

  /*取主营类目*/
  const getMainCategory = () => {
    getStoreMajorCategory().then((result) => {
      const _shopMainCategoryDataSource = result.map((item)=>{
        return {
          label:item.name,
          value:item.code
        }
      });
      setShopMainCategoryData(_shopMainCategoryDataSource);
    }).catch(api.onRequestError);
  }

  /*获取商品分类数据*/
  const fetchSkuCategory = async () => {
    try {
      let request = apiL.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map((dataItem) => {
        dataItem.value = dataItem.value.toString();
        dataItem.children &&
        dataItem.children.map((subItem) => {
          subItem.value = subItem.value.toString();
          subItem.children &&
          subItem.children.map((thirdItem) => {
            thirdItem.value = thirdItem.value.toString();
          });
        });
        return dataItem;
      });
      setSkuCategoryData(dataSource);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  return <>
    <div className={'rank'}>
      <span className={'rank-title'}>榜单配置：</span>
      <div className={'rank-group'}>
        <div className={'rank-tab'}>
          {rankSchema.length > 0 && rankSchema.map((item, index) => {
            return <span onClick={() => changeTab(item.index)} className={`${curTab == item.index ? 'current' : ''}`}>{item.name}</span>
          })}
        </div>
        {rankSchema.length > 0 && rankSchema.map((item,rankIndex) => {
          // const newRankInfo = deepCopy(JSON.parse(sessionStorage.getItem(item.field)) || []);
          const recallRuleData = (newRankInfo[rankIndex] && newRankInfo[rankIndex]['recallRule']) ? newRankInfo[rankIndex]['recallRule'] : {};
          const currentRank = newRankInfo[rankIndex] ? newRankInfo[rankIndex] : {};
          return <div className={`${curTab == item.index ? 'rank-item' : 'rank-item-none'}`}>
            <Form.Item
              {...formItemLayoutChannel3}
              label={'榜单Tab类型：'}
            >
              <label className='tab-type'>{item.name}</label>
            </Form.Item>
            {/*<Form.Item*/}
            {/*  {...formItemLayoutChannel3}*/}
            {/*  label={'榜单Tab总榜名称：'}*/}
            {/*>*/}
            {/*  <Input placeholder={'请输入榜单Tab总榜名称'} value={currentRank.name || ''} onChange={(value) => onNameChange(value, rankIndex)}/>*/}
            {/*</Form.Item>*/}
            <MultipleRankDragger
              oversupply={item.schema.oversupply}
              rankField={item.field}
              rankIndex={rankIndex}
              itemIndex={item.index}
              resourceId={'222'}
              showMultilineIdText={item.schema.showMultilineIdText}
              showItemCat={item.schema.showItemCat || {}}
              setRankInfo={setRankInfo}
              getRankInfo={getRankInfo}
              getRankInfoByIndex={getRankInfoByIndex}
              skuCategoryData={skuCategoryData}
            />
            <Row className={'single-schema'}>
              <Col span={4} style={{width: '200px', maxWidth: '200px'}}>
                <div className='single-schema-title'>{item.schema.detail.title}</div>
              </Col>
              <Col span={18} className={'alsc-form-tab'}>
                {(JSON.stringify(recallRuleData) != "{}" && Boolean(item.schema.detail) && !refresh) && <SchemaForm {...formItemLayout} schema={item.schema.detail} formData={recallRuleData} onChange={(itemData) => onFormItemChange(rankIndex, itemData)}/>}
                <ShopMainCategory
                  showMainCategory={true}
                  setRankInfo={setRankInfo}
                  getRankInfo={getRankInfo}
                  rankIndex={rankIndex}
                  shopMainCategoryData={shopMainCategoryData}
                  saveShopMainCategory={(data) => saveShopMainCategory(data, rankIndex)}
                />
              </Col>
            </Row>
          </div>
        })}
      </div>
    </div>
    {!isHidePosition && <PutInPosition />}
  </>
}
