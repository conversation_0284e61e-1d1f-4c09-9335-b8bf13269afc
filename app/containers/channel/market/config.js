const baseSchema = {
  "configId": {
    "type": "string",
    "title": "配置ID:",
    "disabled": true,
    "x-ui-placeholder": "保存后自动生成"
  },
  "configName": {
    "type": "string",
    "title": "配置名称:",
    "x-ui-placeholder": "请输入配置名称，最多20个字",
    "maximum": 20
  },
  "beginTime": {
    "type": "string",
    "title": "开始时间:",
    "format": "date-time"
  },
  "endTime": {
    "type": "string",
    "title": "结束时间:",
    "format": "date-time"
  },
  "workDay": {
    "type": "array",
    "title": "星期:",
    "x-ui-className": "selection-form",
    "x-ui-widget": "dayOfWeek",
    "items": {
      "type": "number",
      "enum": [0, 1, 2, 3, 4, 5, 6, 7],
      "enumNames": [
        "全选",
        "周一",
        "周二",
        "周三",
        "周四",
        "周五",
        "周六",
        "周日"
      ]
    },
    "uniqueItems": true
  },
  "timeSelection": {
    "type": "number",
    "title": "时段:",
    "enum": ["0","1"],
    "x-ui-className": "selection-form",
    "x-ui-valueLabel": {
      "0": "全天",
      "1": "选择"
    },
    "default": "0",
    "x-ui-widget": "radio"
  },
  "timeRanges": {
    "type": "array",
    "title": " ",
    "x-ui-hidden": "$.timeSelection == 0",
    "maxItems": 3,
    "default":["-"],
    "items": {
      "type": "string",
      "format": "time-range",
      "default": "-"
    }
  },
}
const categorySchema = { //资源位配置-养成品类(1-10)-养成品类(1-10)-基础配置
  "title": "基础配置",
  "type": "object",
  "properties": {
    ...baseSchema,
    "title": {
      "title": "标题名称：",
      "type": "string",
      "maximum": 4,
      "x-ui-placeholder": "请输入标题名称，最多4个字"
    },
    "jumpType": {
      "title": "跳转形式：",
      "type": "number",
      "enum": ["1"],
      "x-ui-className": "selection-form",
      "x-ui-valueLabel": {
        "1": "内页",
      },
      "default": "1",
      "x-ui-widget": "select"
    },
    "priority": {
      "title": "权重：",
      "type": "string",
      "x-ui-placeholder": "取值优先级：数值＞时间",
      "maximum": 20,
      "hasLimitHint": true
    }
  },
};
const bannerSchema = {
  "title": "基础配置",
  "type": "object",
  "properties": {
    ...baseSchema,
    "imageUrl": {
      "title": "banner图片:",
      "type": "string",
      "format": "uri",
      "x-ui-widget": "img-upload",
      "x-ui-validate": {
        "width": 702,
        "height": 176,
        "maxSize": 500,
        "accept": "png,jpeg,jpg,apng"
      }
    },
    "jumpUrl": {
      "title": "跳转链接：",
      "type": "string",
      "x-ui-placeholder": "请输入ele加密的h5链接"
    },
    "activityType": {
      "title": "投放活动ID：",
      "type": "number",
      "x-ui-className": "selection-form",
      "enum": ["1", "2"],
      "x-ui-valueLabel": {
        "1": "商品",
        "2": "门店"
      },
      "x-ui-widget": "radio"
    },
    "activityIds": {
      "title": "投放活动ID：",
      "type": "array",
      "maxItems": 2,
      "items": {
        "type": "string",
        "x-ui-placeholder": "请输入投放活动ID",
        "default": "",
        "required": false
      }
    },
    "priority": {
      "title": "权重：",
      "type": "string",
      "x-ui-placeholder": "取值优先级：数值＞时间",
      "maximum": 20,
      "hasLimitHint": true
    }
  },
};
const benefitMarketOtherSchema = { // 真实惠入口-商品、门店
  "title": "基础配置",
  "type": "object",
  "properties": {
    ...baseSchema,
    "title": {
      "title": "标题：",
      "type": "string",
      "maximum": 4,
      "x-ui-placeholder": "请输入标题文案，最多4个字"
    },
    "jumpType": {
      "title": "跳转形式：",
      "type": "number",
      "enum": ["1", "2"],
      "x-ui-className": "selection-form",
      "x-ui-valueLabel": {
        "1": "内页",
        "2": "h5页"
      },
      "default": "1",
      "x-ui-widget": "select"
    },
    "jumpUrl": {
      "title": "跳转链接：",
      "type": "string",
      "x-ui-hidden": "$.jumpType != 2",
      "x-ui-placeholder": "请输入ele加密的h5链接"
    },
    "insertInto": {
      "title": "加塞模块：",
      "type": "array",
      "x-ui-className": "selection-form",
      "items": {
        "type": "number",
        "enum": [17, 18],
        "enumNames": [
          "超级爆品",
          "分类tab"
        ],
      },
      "default": [],
      "x-ui-widget": "checkboxes",
      "uniqueItems": true
    },
    "priority": {
      "title": "权重：",
      "type": "string",
      "x-ui-placeholder": "取值优先级：数值＞时间",
      "maximum": 20,
      "hasLimitHint": true
    }
  },
};
const benefitMarketOtherSchema2 = { // 真实惠入口-品牌
  "title": "基础配置",
  "type": "object",
  "properties": {
    ...baseSchema,
    "title": {
      "title": "标题：",
      "type": "string",
      "maximum": 4,
      "x-ui-placeholder": "请输入标题文案，最多4个字"
    },
    "jumpType": {
      "title": "跳转形式：",
      "type": "number",
      "enum": ["2"],
      "x-ui-className": "selection-form",
      "x-ui-valueLabel": {
        "2": "h5页"
      },
      "default": "2",
      "x-ui-widget": "select"
    },
    "jumpUrl": {
      "title": "跳转链接：",
      "type": "string",
      "x-ui-hidden": "$.jumpType != 2",
      "x-ui-placeholder": "请输入ele加密的h5链接"
    },
    "insertInto": {
      "title": "加塞模块：",
      "type": "array",
      "x-ui-className": "selection-form",
      "items": {
        "type": "number",
        "enum": [17, 18],
        "enumNames": [
          "超级爆品",
          "分类tab"
        ]
      },
      "default": [],
      "x-ui-widget": "checkboxes",
      "uniqueItems": true
    },
    "priority": {
      "title": "权重：",
      "type": "string",
      "x-ui-placeholder": "取值优先级：数值＞时间",
      "maximum": 20,
      "hasLimitHint": true
    }
  },
};
const goodList = { //有数据配置  资源位配置-商品瀑布流-基础配置
  "title": "基础配置",
  "type": "object",
  "properties": {
    ...baseSchema,
    "mainImageUrl": {
      "title": "主标题图:",
      "type": "string",
      "format": "uri",
      "x-ui-widget": "img-upload",
      "x-ui-validate": {
        "width": 240,
        "height": 60,
        "maxSize": 500,
        "accept": "png,jpeg,jpg,apng"
      }
    },
    "mainTitle": {
      "title": "主标题：",
      "type": "string",
      "maximum": 4,
      "x-ui-placeholder": "请输入主标题文案，最多4个字"
    },
    "subTitle": {
      "title": "副标题：",
      "type": "string",
      "maximum": 4,
      "x-ui-placeholder": "请输入主标题文案，最多4个字"
    },
    "priority": {
      "title": "权重：",
      "type": "string",
      "x-ui-placeholder": "取值优先级：数值＞时间",
      "maximum": 20,
      "hasLimitHint": true
    }
  }
};
const majorSuit = { //有数据配置  资源位配置-大牌驾到-基础配置
  "title": "基础配置",
  "type": "object",
  "properties": {
    ...baseSchema,
    "imageUrl": {
      "title": "背景图:",
      "type": "string",
      "format": "uri",
      "x-ui-widget": "img-upload",
      "x-ui-validate": {
        "width": 488,
        "height": 600,
        "maxSize": 500,
        "accept": "png,jpeg,jpg,apng"
      }
    },
    "mainTitle": {
      "title": "主标题：",
      "type": "string",
      "maximum": 4,
      "x-ui-placeholder": "请输入主标题文案，最多9个字"
    },
    "subTitle": {
      "title": "副标题：",
      "type": "string",
      "maximum": 4,
      "x-ui-placeholder": "请输入副标题文案，最多11个字"
    },
    "brandLogo": {
      "title": "品牌logo图:",
      "type": "string",
      "format": "uri",
      "x-ui-widget": "img-upload",
      "x-ui-validate": {
        "width": 30,
        "height": 30,
        "maxSize": 500,
        "accept": "png,jpeg,jpg,apng"
      }
    },
    "jumpType": {
      "title": "跳转形式：",
      "type": "number",
      "enum": ["3", "2"],
      "x-ui-className": "selection-form",
      "x-ui-valueLabel": {
        "3": "门店",
        "2": "h5页"
      },
      "default": "3",
      "x-ui-widget": "select"
    },
    "jumpUrl": {
      "title": "跳转链接：",
      "type": "string",
      "x-ui-hidden": "$.jumpType != 2",
      "x-ui-placeholder": "请输入ele加密的h5链接"
    },
    "priority": {
      "title": "权重：",
      "type": "string",
      "x-ui-placeholder": "取值优先级：数值＞时间",
      "maximum": 20,
      "hasLimitHint": true
    }
  },
};

const category = {
  detail: {...categorySchema},
  showPutIn:true,
  showDataConfig:false
};
export const SchemaMap = {
  "2001_1": {...category},
  "2001_2": {...category},
  "2002_3": {
    detail: {...bannerSchema},
  },
  "2004_5": {
    "detail": { //资源位配置-金刚位-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "title": {
          "title": "金刚位名称：",
          "type": "string",
          "maximum": 4,
          "x-ui-placeholder": "请输入金刚位名称，最多4个字",
        },
        "imageUrl": {
          "title": "金刚位图片:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 488,
            "height": 600,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng"
          }
        },
        "jumpType": {
          "title": "跳转形式：",
          "type": "number",
          "enum": ["1", "2"],
          "x-ui-className": "selection-form",
          "x-ui-valueLabel": {
            "1": "内页",
            "2": "h5页"
          },
          "default": 1,
          "x-ui-widget": "select"
        },
        "jumpUrl": {
          "title": "跳转链接：",
          "type": "string",
          "x-ui-hidden": "$.jumpType == 1",
          "x-ui-placeholder": "请输入ele加密的h5链接"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showPutIn": true
  },
  "2005_6": {
    "detail": { // 资源位配置-大促入口-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "imageUrl": {
          "title": "banner图片:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 488,
            "height": 600,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng"
          }
        },
        "activityType": {
          "title": "投放活动ID：",
          "type": "number",
          "x-ui-className": "selection-form",
          "enum": ["1", "2"],
          "x-ui-valueLabel": {
            "1": "商品",
            "2": "门店"
          },
          "x-ui-widget": "radio"
        },
        "activityIds": {
          "title": "投放活动ID：",
          "type": "array",
          "maxItems": 3,
          "items": {
            "type": "string",
            "x-ui-placeholder": "请输入投放活动ID",
            "default": "",
            "required": false
          }
        },
        "jumpUrl": {
          "title": "跳转链接：",
          "type": "string",
          "x-ui-placeholder": "请输入ele加密的h5链接"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    }
  },
  "2006_7": { //真实惠-标题
    "detail": { //资源位配置-真实惠入口-标题-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "subTitle": {
          "title": "副标题：",
          "type": "string",
          "maximum": 6,
          "x-ui-placeholder": "请输入副标题文案，最多6个字"
        },
        "imageUrl": {
          "title": "按钮图片:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 198,
            "height": 60,
            "maxSize": 500,
            "accept": "png,gif,apng"
          }
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showPutIn": true
  },
  "2006_8": { //真实惠-商品
    "detail": {...benefitMarketOtherSchema},
    "showDataConfig": true
  },
  "2006_9": { //真实惠-门店
    "detail": {...benefitMarketOtherSchema},
    "showDataConfig": true
  },
  "2006_10": {//真实惠-品牌
    "detail": {...benefitMarketOtherSchema2},
    "showDataConfig": true
  },
  "2007_11": {
    "detail": { //资源位配置-场景卡片-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "mainTitle": {
          "title": "主标题：",
          "type": "string",
          "maximum": 12,
          "x-ui-placeholder": "请输入主标题文案，最多12个字"
        },
        "imageUrl": {
          "title": "背景图:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 703,
            "height": 312,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng"
          }
        },
        "jumpType":{
          "title": "跳转形式：",
          "type": "number",
          "enum": ["3", "2"],
          "x-ui-className": "selection-form",
          "x-ui-valueLabel": {
            "3": "门店",
            "2": "h5页"
          },
          "default": "3",
          "x-ui-widget": "select"
        },
        "jumpUrl": {
          "title": "跳转链接：",
          "type": "string",
          "x-ui-hidden": "$.jumpType !=2 ",
          "x-ui-placeholder": "请输入ele加密的h5链接"
        },
        "activityType":{
          "title": "投放活动ID：",
          "type": "number",
          "x-ui-className": "selection-form",
          "enum": ["1", "2"],
          "x-ui-valueLabel": {
            "1": "商品",
            "2": "门店"
          },
          "x-ui-widget": "radio"
        },
        "activityIds": {
          "title": "投放活动ID：",
          "type": "array",
          "maxItems": 3,
          "items": {
            "type": "string",
            "x-ui-placeholder": "请输入投放活动ID",
            "default": "",
            "required": false
          }
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    }
  },
  "2008_12": { // 资源位配置-商品瀑布流-基础配置
    "detail": {...goodList},
    "showDataConfig": true
  },
  "2008_13": {
    "detail": {...goodList},
    "showDataConfig": true
  },
  "2009_17": {  //资源位配置-大牌驾到-基础配置
    "detail": {...majorSuit},
    "showDataConfig": true
  },
  "2009_18": {
    "detail": {...majorSuit},
    "showDataConfig": true
  },
  "2010_19": {
    "detail": {//资源位配置-简单入口-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "mainTitle": {
          "title": "主标题：",
          "type": "string",
          "maximum": 4,
          "x-ui-placeholder": "请输入主标题文案，最多5个字"
        },
        "subTitle": {
          "title": "副标题：",
          "type": "string",
          "maximum": 4,
          "x-ui-placeholder": "请输入副标题文案，最多8个字"
        },
        "imageUrl": {
          "title": "活动图:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 420,
            "height": 300,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng"
          }
        },
        "jumpType": {
          "title": "跳转形式：",
          "type": "number",
          "enum": ["3", "2"],
          "x-ui-className": "selection-form",
          "x-ui-valueLabel": {
            "3": "门店",
            "2": "h5页"
          },
          "default": "3",
          "x-ui-widget": "select"
        },
        "jumpUrl": {
          "title": "跳转链接：",
          "type": "string",
          "x-ui-hidden": "$.jumpType != 2",
          "x-ui-placeholder": "请输入ele加密的h5链接"
        },
        "activityType": {
          "title": "投放活动ID：",
          "type": "number",
          "x-ui-className": "selection-form",
          "enum": ["1", "2"],
          "x-ui-valueLabel": {
            "1": "商品",
            "2": "门店"
          },
          "x-ui-widget": "radio"
        },
        "activityIds": {
          "title": "投放活动ID：",
          "type": "array",
          "maxItems": 3,
          "items": {
            "type": "string",
            "x-ui-placeholder": "请输入投放活动ID",
            "default": "",
            "required": false
          }
        },
        "position": {
          "title": "位置：",
          "type": "number",
          "enum": ["1", "2", "3", "4", "5", "6", "7", "8", "9"],
          "x-ui-className": "selection-form",
          "x-ui-valueLabel": {
            "1": "F1",
            "2": "F2",
            "3": "F3",
            "4": "F4",
            "5": "F5",
            "6": "F6",
            "7": "F7",
            "8": "F8",
            "9": "F9"
          },
          "default": "1",
          "x-ui-widget": "select"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    }
  },
  "2011_20": {
    "detail": { //资源位配置-安心菜场-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "mainTitle": {
          "title": "主标题：",
          "type": "string",
          "maximum": 4,
          "x-ui-placeholder": "请输入主标题文案，最多4个字"
        },
        "subTitle": {
          "title": "副标题：",
          "type": "string",
          "maximum": 10,
          "x-ui-placeholder": "请输入副标题文案，最多10个字"
        },
        "jumpUrl": {
          "title": "h5跳转链接：",
          "type": "string",
          "x-ui-placeholder": "请输入H5链接"
        },
        "activityType": {
          "title": "投放活动ID：",
          "type": "number",
          "x-ui-className": "selection-form",
          "enum": ["1", "2"],
          "x-ui-valueLabel": {
            "1": "商品",
            "2": "门店"
          },
          "x-ui-widget": "radio"
        },
        "activityIds": {
          "title": "投放活动ID：",
          "type": "array",
          "maxItems": 3,
          "items": {
            "type": "string",
            "x-ui-placeholder": "请输入投放活动ID",
            "default": "",
            "required": false
          }
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    }
  },
  "2012_21": {
    "detail": { //资源位配置-超值低价-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "title": {
          "title": "标题：",
          "type": "string",
          "maximum": 5,
          "x-ui-placeholder": "请输入主标题文案，最多5个字"
        },
        "jumpUrl": {
          "title": "h5跳转链接：",
          "type": "string",
          "x-ui-placeholder": "请输入H5链接"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "newsChannelPoolType":1
  },
  "2013_22": {
    "detail": { //资源位配置-优选商家-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "title": {
          "title": "标题：",
          "type": "string",
          "maximum": 5,
          "x-ui-placeholder": "请输入主标题文案，最多5个字"
        },
        "jumpUrl": {
          "title": "h5跳转链接：",
          "type": "string",
          "x-ui-placeholder": "请输入H5链接"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "newsChannelPoolType":2
  },
  "2014_23": {
    "detail": { //有投放渠道、投放城市、投放人群 资源位配置-真实惠(标题)-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "mainTitle": {
          "title": "主标题：",
          "type": "string",
          "maximum": 7,
          "x-ui-placeholder": "请输入主标题名称，最多7个字",
        },
        "subTitle": {
          "title": "副标题：",
          "type": "string",
          "maximum": 8,
          "x-ui-placeholder": "请输入副标题文案，最多8个字"
        },
      },
    },
    "showPutIn": true
  },
  "2014_24": {
    "detail": { //有数据配置 资源位配置-真实惠(1)-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showDataConfig": true,
    "newsChannelPoolType":1
  },
  "2014_25": {
    "detail": { //有数据配置 资源位配置-真实惠(兜底)-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showDataConfig": true,
    "newsChannelPoolType":1
  },
  "2015_26": {
    "detail": {//资源位配置-红包雨-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "rightId": {
          "title": "权益投放ID：",
          "type": "string",
          "x-ui-placeholder": "请输入权益投放ID",
        },
      },
    }
  },
  "2020_39": {
    "detail": {//资源位配置-浮窗-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "activityType": {
          "title": "投放活动ID：",
          "type": "number",
          "x-ui-className": "selection-form",
          "enum": ["1", "2"],
          "x-ui-valueLabel": {
            "1": "商品",
            "2": "门店"
          },
          "x-ui-widget": "radio"
        },
        "activityIds": {
          "title": "投放活动ID：",
          "type": "array",
          "maxItems": 2,
          "items": {
            "type": "string",
            "x-ui-placeholder": "请输入投放活动ID",
            "default": "",
            "required": false
          }
        },
        "smallFloatWindow": {
          "title": "浮窗小图:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 138,
            "height": 198,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,gif"
          }
        },
        "bigFloatWindow": {
          "title": "浮窗大图:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 750,
            "height": 700,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,gif"
          }
        },
        "jumpUrl": {
          "title": "h5跳转链接：",
          "type": "string",
          "x-ui-placeholder": "请输入ele加密的h5链接"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    }
  },
  "2019_38": {
    "detail": {//资源位配置-大促氛围-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "headerImg": {
          "title": "大促头图:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 1125,
            "height": 294,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng"
          }
        },
        "centerImg": {
          "title": "大促中图:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 1125,
            "height": 192,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng"
          }
        },
      },
    }
  },
  "2017_36": {//资源位配置-大促入口-医药-基础配置
    detail: {...bannerSchema}
  },
  "2018_37": {
    "detail": {//资源位配置-简单入口-医药-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "imageUrl": {
          "title": "背景图:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 234,
            "height": 234,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,gif"
          }
        },
        "mainTitle": {
          "title": "主标题：",
          "type": "string",
          "maximum": 9,
          "x-ui-placeholder": "请输入主标题名称，最多9个字",
        },
        "subTitle": {
          "title": "副标题：",
          "type": "string",
          "maximum": 11,
          "x-ui-placeholder": "请输入副标题文案，最多11个字"
        },
        "activityType": {
          "title": "投放活动ID：",
          "type": "number",
          "x-ui-className": "selection-form",
          "enum": ["1", "2"],
          "x-ui-valueLabel": {
            "1": "商品",
            "2": "门店"
          },
          "x-ui-widget": "radio"
        },
        "activityIds": {
          "title": "投放活动ID：",
          "type": "array",
          "maxItems": 5,
          "items": {
            "type": "string",
            "x-ui-placeholder": "请输入投放活动ID",
            "default": "",
            "required": false
          }
        },
        "jumpType": {
          "title": "跳转形式：",
          "type": "number",
          "enum": ["3", "2"],
          "x-ui-className": "selection-form",
          "x-ui-valueLabel": {
            "3": "门店",
            "2": "h5页"
          },
          "default": "3",
          "x-ui-widget": "select"
        },
        "jumpUrl": {
          "title": "跳转链接：",
          "type": "string",
          "x-ui-hidden": "$.jumpType != 2",
          "x-ui-placeholder": "请输入ele加密的h5链接"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    }
  },
  "3001_29": {
    "detail": { //有关联上级配置  养成品类内页-banner-基础配置
      ...bannerSchema
    },
    "supChannel":true
  },
  "3002_30": {
    "detail": { //关联上级配置、数据配置 养成品类内页-二级tab-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "title": {
          "title": "二级tab标题：",
          "type": "string",
          "maximum": 5,
          "x-ui-placeholder": "请输入二级tab标题名称，最多5个字",
        },
        "imageUrl": {
          "title": "二级tab图片:",
          "type": "string",
          "format": "uri",
          "x-ui-widget": "img-upload",
          "x-ui-validate": {
            "width": 488,
            "height": 600,
            "maxSize": 500,
            "accept": "png,jpeg,jpg,apng"
          }
        },
        "showShopWindow": {
          "title": "是否展示橱窗：",
          "type": "boolean",
          "x-ui-className": "selection-form",
          "x-ui-valueLabel": {
            "true": "是",
            "false": "否"
          },
          "x-ui-widget": "radio"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showDataConfig": true,
    "supChannel":true
  },
  "4001_31": {
    "detail": { //关联上级配置   金刚位内页-banner-基础配置
      ...bannerSchema
    },
    "supChannel":true
  },
  "4002_32": {
    "detail": { //关联上级配置、数据配置  金刚位内页-二级tab-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "title": {
          "title": "二级tab标题：",
          "type": "string",
          "maximum": 5,
          "x-ui-placeholder": "请输入二级tab标题名称，最多5个字",
        },
        "showShopWindow": {
          "title": "是否展示橱窗：",
          "type": "boolean",
          "x-ui-className": "selection-form",
          "x-ui-valueLabel": {
            "true": "是",
            "false": "否"
          },
          "x-ui-widget": "radio"
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showDataConfig": true,
    "supChannel":true
  },
  "5001_33": {
    "detail": { //真实惠内页-领红包-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "rightId": {
          "title": "权益投放ID：",
          "type": "string",
          "x-ui-placeholder": "请输入权益投放ID",
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    }
  },
  "5002_34": {
    "detail": { //有数据配置  真实惠内页-超级爆品-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showDataConfig": true
  },
  "5003_35": {
    "detail": { //有数据配置   真实惠内页-分类Tab-基础配置
      "title": "基础配置",
      "type": "object",
      "properties": {
        ...baseSchema,
        "title": {
          "title": "标题名称：",
          "type": "string",
          "maximum": 6,
          "x-ui-placeholder": "请输入标题名称，最多6个字",
        },
        "priority": {
          "title": "权重：",
          "type": "string",
          "x-ui-placeholder": "取值优先级：数值＞时间",
          "maximum": 20,
          "hasLimitHint": true
        }
      },
    },
    "showDataConfig": true
  },
}


