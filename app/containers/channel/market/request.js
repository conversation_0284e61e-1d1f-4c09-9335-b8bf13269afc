import { putInReq } from '@/utils/request';
import { request } from '@/adator/request';
import { onRequestSuccess }  from '@/utils/api'
import axios from 'axios'

/**资源位配置-查看配置 获取配置信息*/
export function queryConfigList(params) {
  return putInReq.post('/api/deliverymanage/queryConfigList', params).then(onRequestSuccess)
}

/**更新配置状态 1删除、2停用**/
export function updateConfigState(params) {
  return putInReq.post('/api/deliverymanage/updateConfigState', params).then(onRequestSuccess)
}

/**撤回**/
export function recallReview(params) {
  return putInReq.post('/api/deliverymanage/recallReview', params).then(onRequestSuccess)
}


/**复制配置信息 **/
export function copyDelevery(params) {
  return putInReq.post('/api/deliverymanage/copyDelivery', params).then(onRequestSuccess)
}

/**资源位配置-投放配置页 获取单条资源信息**/
export function getResource(params) {
  return putInReq.get(`/api/pageManage/getResource/${params.pageId}/${params.resourceId}`).then(onRequestSuccess)
}

/**修改资源位投放配置 **/
export function saveResourcePutInConfig(params) {
  return putInReq.post('/api/pageManage/saveResourcePutInConfig', params).then(onRequestSuccess)
}

/**获取商超频道页-投放配置信息 **/
export function getPagePutInConfig(params) {
  return putInReq.get(`/api/pageManage/getPagePutInConfig/${params.pageId}`).then(onRequestSuccess)
}

/**保存资源位投放配置 **/
export function savePagePutInConfig(params) {
  return putInReq.post('/api/pageManage/savePagePutInConfig', params).then(onRequestSuccess)
}

/**获取楼层去重设置弹窗去重信息 **/
export function getDistinctEnableConfig(params) {
  return putInReq.get(`/api/pageManage/getDistinctEnableConfig/${params.pageId}`).then(onRequestSuccess)
}

/**获取楼层去重设置弹窗资源位列表 **/
export function queryDistinctResourceList(params) {
  return putInReq.get(`/api/pageManage/queryDistinctResourceList/${params.pageId}`).then(onRequestSuccess)
}

/**资源位列表**/
export function queryResourceList(params) {
  return putInReq.post('/api/pageManage/queryResourceList', params).then(onRequestSuccess)
}

/**保存楼层去重设置信息 **/
export function saveDistinctEnableConfig(params) {
  return putInReq.post('/api/pageManage/saveDistinctEnableConfig', params).then(onRequestSuccess)
}

/**去投放**/
export function createDelivery(params) {
  return putInReq.post('/api/deliverymanage/createDelivery', params).then(onRequestSuccess)
}

/*去投放-保存*/
export function saveDelivery(params) {
  return putInReq.post('/api/deliverymanage/saveDelivery', params).then(onRequestSuccess)
}

/**获取页面配置信息**/
export function getPageBaseConfig(params) {
  return putInReq.get(`/api/pageManage/getPageBaseConfig/${params.pageId}`).then(onRequestSuccess)
}

/**查看投放配置信息**/
export function queryPosConfig(params) {
  return putInReq.post('/api/deliverymanage/queryPosConfig',params).then(onRequestSuccess)
}

/**排期信息提交触发审核流**/
export function submitScheduleCheck(params) {
  return putInReq.post('/api/deliverymanage/submitScheduleCheck',params).then(onRequestSuccess)
}

/**获得操作记录**/
export function getOperationList(params) {
  return putInReq.post('/api/operation/getOperationList',params).then(onRequestSuccess)
}

/**获得操作记录**/
export function getParentConfigList(params) {
  return putInReq.post('/api/deliverymanage/getParentConfigList',params).then(onRequestSuccess)
}

/**获得业态金刚排期**/
export function getConfigList(params) {
  return putInReq.post('/api/deliverymanage/queryConfigList',params).then(onRequestSuccess)
}

/**设置schema**/
export function setSchema(params) {
  return putInReq.post('/api/deliverymanage/setSchema',params).then(onRequestSuccess)
}

/**设置元数据schema**/
export function setMetadataSchema(params) {
  return putInReq.post('/api/deliverymanage/setMetadataSchema',params).then(onRequestSuccess)
}

/** schema 设置成功后发送钉钉消息, Schema 发布通知群 **/
export function rootSendMsg(params) {
  return request().post('/api/dingding/markdown',params).then(onRequestSuccess)
}

/**设置资源位是否需要校验**/
export function setResourcePermission(params) {
  let {pageId,resourceId,ignorePermission} = params;
  return putInReq.get(`/api/acl/channel/setResourcePermission/${pageId}/${resourceId}/${ignorePermission}`).then(onRequestSuccess)
}


/**获取schema**/
export function getScheduleTemplate(params) {
  return putInReq.post('/api/deliverymanage/getScheduleTemplate',params).then(onRequestSuccess)
}

/**氛围设置**/
export function editPageStyle(params) {
  return putInReq.post('/api/pageManage/editPageStyle',params).then(onRequestSuccess)
}

/**氛围设置**/
export function getSceneWord() {
  return putInReq.get('/api/deliverymanage/getSceneWord',).then(onRequestSuccess)
}

/**通过关键字模糊匹配查询用户**/
export function queryUserByKeyWord(userKeyWord) {
  return putInReq.get(`/api/acl/channel/queryUserByKeyWord/${userKeyWord}`).then(onRequestSuccess)
}

/**给用户添加资源位权限**/
export function createAclUser(params) {
  return putInReq.post('/api/acl/channel/createAclUser',params).then(onRequestSuccess)
}

/**给用户删除权限**/
export function deleteAclUser(params) {
  return putInReq.post('/api/acl/channel/deleteAclUser',params).then(onRequestSuccess)
}

/**给用户查询权限**/
export function queryAclUser(params) {
  return putInReq.post('/api/acl/channel/queryAclUser',params).then(onRequestSuccess)
}

/**审核开启关闭接口**/
export function reviewHandle(params) {
  return putInReq.post('/api/acl/channel/reviewHandle',params).then(onRequestSuccess)
}

/**查询审核开关**/
export function queryReview(params) {
  return putInReq.post('/api/acl/channel/queryReview',params).then(onRequestSuccess)
}

/**资源位权限校验**/
export function validateAclResource(params) {
  return putInReq.post('/api/acl/channel/validateAclResource',params).then(onRequestSuccess)
}

/**配置权限校验**/
export function validateAclSchedule(params) {
  return putInReq.post('/api/acl/channel/validateAclSchedule', params).then(onRequestSuccess)
}

/**获取频道页可用的投放渠道key值 */
export function getResourceDeliveryChannel(params) {
  return putInReq.get(`/api/pageManage/queryResourceDeliveryChannel/${params.pageId}/${params.resourceId}`).then(onRequestSuccess)
}

/**通过活动ID获取活动类型**/
export function queryActivityPoolType(params) {
  return putInReq.post('/api/putInActivity/queryActivityPoolType', params).then(onRequestSuccess)
}

/**支付宝接口-城市**/
export function cityInfoDistrictCode() {
  return putInReq.get('/api/common/cityInfoDistrictCode').then(onRequestSuccess)
}

/**资源位预览接口*/
export function queryChannelPage(params) {
  return putInReq.post('/api/templatemanage/queryChannelPage', params).then(onRequestSuccess)
}

// 购后的门店类目
export function getEleCategory() {
  return putInReq.get(`/api/common/eleCategory`)
}

/**品牌霸屏-获取视频*/
export function queryViewList(params) {
  return putInReq.post('/api/deliverymanage/queryViewList',params).then(onRequestSuccess)
}
/**判断当前用户是否有审核权限*/
export function queryIsReviewer (params) {
  return putInReq.post('/api/acl/channel/queryIsReviewer',params).then(onRequestSuccess)
}
/**获取审批失败原因接口*/
export function queryReviewRecorde (params) {
  return putInReq.post('/api/deliverymanage/queryReviewRecorde',params).then(onRequestSuccess)
}

/**获取品类金刚关联算法策略二级类目*/
export function queryAllCategoryKingKongLinkAlgorithmPolicy () {
  return putInReq.get('/api/common/queryAllCategoryKingKongLinkAlgorithmPolicy').then(onRequestSuccess)
}

/**获取品类金刚关联算法策略二级类目--从品类中心*/
export function queryAlgorithmStrategyCenterList () {
  return putInReq.post('/api/deliverymanage/queryAlgorithmStrategyList').then(onRequestSuccess)
}

/**获取所有快筛词的关联算法三级类目*/
export function queryAllFastSearchWordLinkAlgorithmPolicy () {
  return putInReq.get('/api/common/queryAllFastSearchWordLinkAlgorithmPolicy').then(onRequestSuccess)
}

/**获取投放门店list*/
export function getPoolInfoSurge (params) {
  return putInReq.post('/api/deliverymanage/getPoolInfoSurge',params).then(onRequestSuccess)
}

// 获取门店主营类目
export function getStoreMajorCategory() {
  return putInReq.get('/api/common/queryStoreMajorCategory').then(onRequestSuccess)
}

export function getDynamicDataSource(apiName,requestMethod,requestGateway,params) {
  console.log("🚀 ~ file: request.js:242 ~ getDynamicDataSource ~ requestMethod,requestGateway,apiName:", requestMethod,requestGateway,apiName)
  let netRequest = requestGateway === "putInReq" ? putInReq : request()
  if (requestMethod === 'GET') {
    return netRequest.get(`${apiName}`,params).then(onRequestSuccess)
  }else{
    return netRequest.post(`${apiName}`,params).then(onRequestSuccess)
  }
}