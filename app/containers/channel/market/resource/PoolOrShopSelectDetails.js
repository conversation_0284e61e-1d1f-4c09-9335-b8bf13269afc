import React, { useEffect, useState } from "react";
import { object, string, shape, bool } from "prop-types";
import { putInReq } from "@/utils/request";
import * as api from "@/utils/api";

const getDynamic = (apiUrl, requestParams) => {
  return putInReq.post(apiUrl, requestParams).then(api.onRequestSuccess);
};

const PoolOrShopSelectDetails = ({value, schema}) => {
  const { requestOptions } = schema || {};

  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    if (value && value.length > 0) {
      // 编辑回填，需要查 key/value 对应值回显
      const { apiUrl, searchKey, params = {} } = requestOptions;
      const _dataSource = [];

      const resList = value.map(async (item) => {
        try {
          // 使用try catch 避免一个接口报错，所有结果都无法回填
          return getDynamic(apiUrl, { ...params, [searchKey]: item });
        } catch (e) {
          return null
        }
      });

      Promise.all(resList).then((_res) => {
        _res.filter(Boolean).map((res) => {
          if (res && res.length > 0) {
            _dataSource.push({
              label: res[0].poolContent,
              value: res[0].poolId,
              newPlatformFlag: res[0].newPlatformFlag,
            });
          }
        });
        setDataSource(_dataSource);
      });
    }
  }, []);

  return (
    <div>
      {dataSource.map((item, i) => {
        return <div key={item.value}>{item.label}{i !== dataSource.length - 1 ? '，' : '' }</div>
      })}
    </div>
  )
}
PoolOrShopSelectDetails.propTypes = {
  schema: shape({
    label: string,
    placeholder: string,
    disabled: bool,
    required: bool,
    requestOptions: {
      apiUrl: string,
      searchKey: string,
      params: object,
    },
  }).isRequired,
  value: object
};
export default PoolOrShopSelectDetails;
