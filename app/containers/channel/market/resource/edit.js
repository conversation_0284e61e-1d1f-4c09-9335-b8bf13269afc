import debugFn from 'debug';
import {
  Step,
  Breadcrumb,
  Grid,
  Button,
  Form,
  CascaderSelect,
  Message,
  Dialog,
  Radio,
  Input,
  NumberPicker,
  Select,
  Switch,
  Search,
  Pagination,
  Card
} from '@alife/next'

import React, {useState, useEffect,useRef} from 'react'
import {Redirect, Link, withRouter} from 'react-router-dom'
import {ACLPermission} from '@/containers//PutInPage/request';
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {PageBase} from '@/containers/base';
import { exlog } from '@/containers/App/exlog';
import {permissionAccess} from '@/components/PermissionAccess';
import { SchemaMap } from '../config'
import {getQueryString,replaceParamVal} from '@/utils/others';
import SchemaForm from '@/utils/Form/src';
import {CreatePutInPage} from '@/containers/PutInPage';
import moment from 'moment';
import './index.scss';
import {pageMap,resourceTypeMap,showQrCodeMap,supplyTypeDataSource} from '../common';
import * as api from '@/utils/api';
import * as apiL from "@/adator/api";
import * as qs from 'query-string';
import ImgUploadWidget from '../../../../utils/Form/src/components/widgets/ImgUploadWidget';
import {
  createDelivery,
  saveDelivery,
  queryPosConfig,
  getParentConfigList,
  getConfigList,
  getScheduleTemplate,
  getSceneWord,
  validateAclResource,
  queryActivityPoolType,
  queryReview,
  cityInfoDistrictCode, queryViewList, getPoolInfoSurge,
  queryAllCategoryKingKongLinkAlgorithmPolicy,
  queryAlgorithmStrategyCenterList,
  queryAllFastSearchWordLinkAlgorithmPolicy,
  getStoreMajorCategory
} from '../request';
import {basepoolIds} from "@/constants";
import {config} from "@/config";
import {object} from "prop-types";
import {call} from "redux-saga/effects";
import {getAllDeliveryChannel} from "@/utils/api";
import {composeSelectorGrid} from "@/components/CascaderSelect";
import {getPoolIdsGroup, getUserTagGroupIdsList} from "@/containers/PutInPage/request";
import {NewPutInfoPage} from './newPutInfo'
import {versionGroupSet} from "@/containers/PutInPage/constants";
import {isArray,isString} from "@/utils/validators";
import { deepCopy } from '@/home/<USER>/common';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import HTML5Backend from 'react-dnd-html5-backend';
// import MultipleSupplySourcesDragger from "@/containers/channel/market/comps/multipleSupplySourcesDragger";
// import CommodityDataSource from "@/containers/channel/market/comps/commodityDataSource";
import RankSet from "@/containers/channel/market/comps/rankSet";
import TextareaArrayWidget from "../../../../utils/Form/src/components/widgets/TextareaArrayWidget";
import DynamicDataSourceForm from "@/containers/channel/market/comps/dynamicDataSourceForm";
// import MultilineIdText from "@/containers/channel/market/comps/multilineIdText";
// import ShopMainCategory from "@/containers/channel/market/comps/shopMainCategory";
// import { Schema } from '../../../App/tests/schemaDemo'
const {Row, Col} = Grid
const debug = debugFn("selection:channel:resource:edit")
const debugLF = debugFn("selection:channel:resource:edit:lifecycle")
let GridSelector = composeSelectorGrid([]);
const formItemLayout = {
  labelCol: {
    fixedSpan: 8,
  },
  wrapperCol: {},
}
const formItemLayoutChannel = {
  labelCol: { span: 6},
  wrapperCol: {
    span: 18
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const formItemLayoutChannel2 = {
  labelCol: { span: 7},
  wrapperCol: {
    span: 17
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};
const formItemLayoutChannel3 = {
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const validateMap = {
  1:'结束时间不能早于开始时间',
  2:'结束时段和开始时段不能同时为空',
  3:'结束时段不能早于开始时段',
  4:'结束时间不能早于当前时间',
  5:'红包结束时间不能早于红包开始时间',
  6:'投放活动id不合规'
}

const poolTypeSet = [{
  value: '1',
  label: '昆仑'
},{
  value: '2',
  label: '智库',
}]

// 普通跳转品类中心的链接
const centerUrl = {
  'daily': 'https://market.waptest.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/list/attract-merchant',
  'localdev': 'https://market.waptest.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/list/attract-merchant',
  'ppe': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/category-rack',
  'prod': 'https://market.m.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/category-rack'
}

// 全能商厦-品类金刚跳品类中心的链接
const centerGoldUrl = {
  'daily': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/all-supermarket',
  'localdev': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/all-supermarket',
  'ppe': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/all-supermarket',
  'prod': 'https://market.m.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/all-supermarket'
}

// 全能商厦-商品tab跳品类中心的链接
const centerTabUrl = {
  'daily': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/recommend-tab',
  'localdev': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/recommend-tab',
  'ppe': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/recommend-tab',
  'prod': 'https://market.m.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/recommend-tab'
}




const centerCreateUrl = {
  'daily': 'https://market.waptest.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/list/attract-merchant',
  'localdev': 'https://market.waptest.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/list/attract-merchant',
  'ppe': 'https://market.wapa.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/create-category-rack/xt_ytjg/empty',
  'prod': 'https://market.m.taobao.com/app/eleme-xcy-fed/category-operating-center/index.html#/store-center/create-category-rack/xt_ytjg/empty'
}

const selectionCommodityUrl = {
  'daily': 'https://market.wapa.taobao.com/app/op-fe/o2o-selection-admin/index.html#/pool/list',
  'localdev': 'https://pre-kunlun.alibaba-inc.com/selection#/pool/list',
  'ppe': 'https://pre-kunlun.alibaba-inc.com/selection#/pool/list',
  'prod': 'https://selection.kunlun.alibaba-inc.com/#/pool/list'
}

let tabMapList = []
let moverId = -1

const topImgResourceGroup = ['100003049', '3049', '100009039', '9039','100007009','7009', '100250003', '250003'];
export default class MarketEdit extends PageBase{
  constructor(props) {
    super(props);
    debugLF('MarketEdit constructor');
    let newProps = props.pageId ? props : this.query;
    let {pageId = '', resourceId = '', resourceName = '', posId = '', posName = '', resourceType=''} = newProps;
    let {configId} = this.params;
    const {pathname=''} = this.location;
    let isCopy = (configId != '' && parseInt(configId) < 0);
    if (isCopy) {
      sessionStorage.setItem('isCopy', isCopy);
      configId = ('' + -configId);
    }
    // const schemaObj = SchemaMap[`${resourceId}_${posId}`];
    // const showDataConfig = schemaObj["showDataConfig"];
    // const showPutIn = schemaObj["showPutIn"];
    // const showSupChannel = schemaObj["supChannel"];
    let step = getQueryString('step') ? getQueryString('step') : 0;
    this.state = {
      step,
      configId,
      pageId,
      isCopy,
      resourceId,
      resourceType,
      resourceName,
      posId,
      posName,
      formData:  {},
      submitLoading:false,
      pageUrl: (configId) ? pathname.substring(0, pathname.lastIndexOf('/', pathname.lastIndexOf("/") - 1)) : pathname.substring(0, pathname.lastIndexOf('/'))
      // pageUrl: pathname.substring(0, pathname.lastIndexOf('/'))
    }
  }

  getSchema = () => {
    let that = this;
    let {resourceId, posId} = this.state;
    let baseQueryRequestDTO = {
      resourceId,
      posId
    }
    function updateSchema (nowSchemaJson){
      let scheduleTemplate = JSON.parse(nowSchemaJson);
      let showSceneWords = scheduleTemplate['showSceneWords'];
      // scheduleTemplate['detail'] = Schema;
      that.setState({
        scheduleTemplate,
        showDataConfig: scheduleTemplate['showDataConfig'],
        showConfigGroup: scheduleTemplate['showConfigGroup'],
        showPutIn: scheduleTemplate['showPutIn'],
        showAddConfig: scheduleTemplate['showAddConfig'],
        retailConfig: scheduleTemplate['retailConfig'],
        showCategory: scheduleTemplate["showCategory"],
        showSupChannel: scheduleTemplate['supChannel'],
        showSign: scheduleTemplate['showSign'],
        showSignEntry: scheduleTemplate['showSignEntry'],
        oversupply:scheduleTemplate['oversupply'] || false,
        isCategoryCenter: scheduleTemplate['isCategoryCenter'] || false, //算法策略，取品类中心的数据，是否需要支持这个设置
        removeCheckJumpUrl: scheduleTemplate['removeCheckJumpUrl'] || false,
        isJumpCategoryCenter:scheduleTemplate['isJumpCategoryCenter'] || false, // 点击保存是否跳转品类中心
        poolTypeConfig: scheduleTemplate['poolTypeConfig'] || '', //投放池子
        showSceneWords,
        showAoi: scheduleTemplate['showAoi'] || false,
        showMainCategory: scheduleTemplate['showMainCategory'] || false,
        showMultilineIdText: scheduleTemplate['showMultilineIdText'] || false,
        multipleSchema: scheduleTemplate['multipleSchema'] || false,
        selectionCollection : scheduleTemplate['selectionCollection'] || [],
        showSingleCommodityDataSource: scheduleTemplate["showSingleCommodityDataSource"],
        showSingleOversupply: scheduleTemplate["showSingleOversupply"],
        rankSchema:scheduleTemplate['rankSchema'],
        dynamicDataSourceForm:scheduleTemplate['dynamicDataSourceForm'],
      }, () => {
        sessionStorage.setItem('SchemaMap', nowSchemaJson);
        if(that.state.configId){
          sessionStorage.setItem('configId',that.state.configId);
          that.getDetailData();
        }
        if(that.state.showSingleOversupply){
          const nowOversupply = that.state.oversupply || {}
          if (typeof nowOversupply.tabKey != 'undefined') {
            sessionStorage.setItem(`${nowOversupply.tabKey}`,'[]');
          }else{
            sessionStorage.setItem('pureDataSourceList','[{}]');
          }
        }
      });
    }
    if (this.props.newSchemaJson) {
      updateSchema(this.props.newSchemaJson)
    }else{
      getScheduleTemplate(baseQueryRequestDTO)
        .then((result) => {
          updateSchema(result)
        }).catch(api.onRequestError);
    }
  }

  async componentDidMount() {
    debugLF('MarketEdit did mount');
    let {pageId,resourceId} = this.state;
    const {withPermission} = await this.props.getPermission({
      pageId,
      resourceId,
    });
    if(!withPermission) return

    let {configId} = this.state;
    let baseConfig = JSON.parse(sessionStorage.getItem('baseConfig'));
    if (baseConfig && baseConfig.configId && baseConfig.configId != configId) {
      sessionStorage.clear();
    }
    this.getSchema();

    try {
      exlog.logCustomEvent({
        code: "resource-config",
        message: "MarketEditPage visit",
        c1: configId,
        d1: pageId,
        d2: resourceId,
        d3: this.state.posId
      });
    } catch (e) {
      console.error(e);
    }
  }

  componentWillUnmount() {
    debugLF("MarketEdit will unmount");
    sessionStorage.clear();
  }

  componentDidUpdate(prevProps) {
    debugLF("MarketEdit did update");
    if (this.props.newSchemaJson !== prevProps.newSchemaJson) {
      this.getSchema();
    }
  }

  getDetailData = () =>{
    let {resourceId, posId, configId, retailConfig, resourceType, showMultilineIdText,dynamicDataSourceForm,multipleSchema,selectionCollection,rankSchema,oversupply} = this.state;
    let baseQueryRequestDTO = {
      resourceId,
      posId,
      configId
    }
    queryPosConfig(baseQueryRequestDTO)
      .then((result) => {
        let scheduleInfo = JSON.parse(result.scheduleInfo);
        // let scheduleTemplate = JSON.parse(result.scheduleTemplate);
        this.setState({
          // scheduleTemplate,
          scheduleInfo,
        },()=>{
          if(scheduleInfo.sceneWords){
            sessionStorage.setItem('sceneWords',scheduleInfo.sceneWords);
          }
          if(scheduleInfo.relatedSup){
            sessionStorage.setItem('relatedSup',scheduleInfo.relatedSup);
          }
          if(scheduleInfo.topImgUrl && topImgResourceGroup.includes(resourceId)){
            sessionStorage.setItem('topImgUrl',JSON.stringify(scheduleInfo.topImgUrl));
          }
          if(scheduleInfo.status){
            sessionStorage.setItem('status',scheduleInfo.status);
          }
          if (scheduleInfo.adFloat) {
            const advertiseGroup = {
              adFloat: scheduleInfo.adFloat,
              brandBaPingUrl: scheduleInfo.brandBaPingUrl,
              materialType: scheduleInfo.materialType,
              selectViewList: scheduleInfo.selectViewList,
              viewId: scheduleInfo.viewId
            }
            sessionStorage.setItem('advertiseGroup', JSON.stringify(advertiseGroup));
          }
          // sessionStorage.setItem("baseConfig", JSON.stringify(scheduleInfo));
          if (scheduleInfo.putInActivityId) {
            if (scheduleInfo.putInActivityId.length >= 0) {
              sessionStorage.setItem('putInActivityId', JSON.stringify(scheduleInfo.putInActivityId));
              sessionStorage.setItem('putPoolType', scheduleInfo.putPoolType ? JSON.stringify(scheduleInfo.putPoolType) : JSON.stringify([]));
            } else {
              let putInActivityId = [];
              let putPoolType = [];
              putInActivityId.push(scheduleInfo.putInActivityId);
              putPoolType.push(scheduleInfo.putPoolType);
              sessionStorage.setItem('putInActivityId', JSON.stringify(putInActivityId));
              sessionStorage.setItem('putPoolType', JSON.stringify(putPoolType));
            }
          } else {
            let putInInfo = {
              channelList: scheduleInfo.channelList,
              channelLabel: scheduleInfo.channelLabel,
              citys:scheduleInfo.citys,
              selectedCitys:scheduleInfo.selectedCitys,
              userTagGroupIdsList:scheduleInfo.userTagGroupIdsList?scheduleInfo.userTagGroupIdsList:[],
              peopleType: scheduleInfo.peopleType,
              peopleOrderType: scheduleInfo.peopleOrderType,
            }
            if (scheduleInfo.channelVersionList) {
              putInInfo.channelVersionList = JSON.parse(scheduleInfo.channelVersionList);
            }
            if(scheduleInfo.eleCategoryFirList || scheduleInfo.eleCategorySecList){
              putInInfo.eleCategoryFirList = scheduleInfo.eleCategoryFirList;
              putInInfo.eleCategorySecList = scheduleInfo.eleCategorySecList;
            }
            sessionStorage.setItem('putInInfo',JSON.stringify(putInInfo));
          }
          let {showConfigGroup, showDataConfig, showAddConfig} = this.state;
          if ((showConfigGroup || showDataConfig) && showAddConfig) {
            let newPutInInfo = {
              channelList: scheduleInfo.channelList,
              channelLabel: scheduleInfo.channelLabel,
              citys:scheduleInfo.citys,
              selectedCitys:scheduleInfo.selectedCitys,
              userTagGroupIdsList:scheduleInfo.userTagGroupIdsList?scheduleInfo.userTagGroupIdsList:[],
              peopleType: scheduleInfo.peopleType,
              peopleOrderType: scheduleInfo.peopleOrderType,

            }
            if (scheduleInfo.channelVersionList) {
              newPutInInfo.channelVersionList = JSON.parse(scheduleInfo.channelVersionList);
            }
            if (scheduleInfo.griddings) {
              newPutInInfo.griddings = scheduleInfo.griddings;
            }
            if(scheduleInfo.eleCategoryFirList || scheduleInfo.eleCategorySecList){
              newPutInInfo.eleCategoryFirList = scheduleInfo.eleCategoryFirList;
              newPutInInfo.eleCategorySecList = scheduleInfo.eleCategorySecList;
            }
            sessionStorage.setItem('newPutInInfo',JSON.stringify(newPutInInfo));
          }
          this.ctrlFormData();
          if(scheduleInfo.dataResourceDTOList){ /*支付宝*/
            let putInInfo = {
              citys:scheduleInfo.citys,
              cityCode:scheduleInfo.cityCode,
              dataResourceDTOList:scheduleInfo.dataResourceDTOList
            }
            sessionStorage.setItem('putInInfo',JSON.stringify(putInInfo));
          }

          if(scheduleInfo.searchLabelList){ //商品类目
            sessionStorage.setItem('searchLabelList',JSON.stringify(scheduleInfo.searchLabelList));
          }
          if(resourceType=='143'){
            sessionStorage.setItem('linkAlgorithmPolicy',scheduleInfo.linkAlgorithmPolicy ? scheduleInfo.linkAlgorithmPolicy : "");
          }
          if(this.state.isCategoryCenter){
            sessionStorage.setItem('linkAlgorithmCenter',scheduleInfo.linkAlgorithmPolicy);
          }
          if(scheduleInfo.signRedBagList){
            sessionStorage.setItem('signRedBagList',JSON.stringify(scheduleInfo.signRedBagList));
          }
          if(scheduleInfo.noSignInCopyList){
            sessionStorage.setItem('noSignInCopyList',JSON.stringify(scheduleInfo.noSignInCopyList));
          }
          if(scheduleInfo.signInCopyList){
            sessionStorage.setItem('signInCopyList',JSON.stringify(scheduleInfo.signInCopyList));
          }
          if(scheduleInfo.ext_pool_ids){
            sessionStorage.setItem('ext_pool_ids',scheduleInfo.ext_pool_ids);
            sessionStorage.setItem('ext_pool_group_label',JSON.stringify(scheduleInfo.ext_pool_group_label));
            sessionStorage.setItem('ext_pool_set',JSON.stringify(scheduleInfo.ext_pool_group_label));
          }
          if(scheduleInfo.ext_aoi_codes){
            sessionStorage.setItem('ext_aoi_codes',JSON.stringify(scheduleInfo.ext_aoi_codes));
          }
          if (oversupply && typeof oversupply.tabKey != 'undefined') {
            if(scheduleInfo[oversupply.tabKey]){
              sessionStorage.setItem(`${oversupply.tabKey}`,JSON.stringify(scheduleInfo[oversupply.tabKey]));
            }
          }
          if(scheduleInfo.pureDataSourceList){
            sessionStorage.setItem('pureDataSourceList',JSON.stringify(scheduleInfo.pureDataSourceList));
          }
          if(scheduleInfo.shopMajorCategoryList){
            sessionStorage.setItem('shopMajorCategoryList',JSON.stringify(scheduleInfo.shopMajorCategoryList));
          }
          if (scheduleInfo.shop_main_category) {
            sessionStorage.setItem('shop_main_category',JSON.stringify(scheduleInfo.shop_main_category));
          }
          if (showMultilineIdText && showMultilineIdText.length > 0) {
            showMultilineIdText.map((item)=>{
              if(scheduleInfo[item.parentKey] && scheduleInfo[item.parentKey].length > 0){
                let _text = scheduleInfo[item.parentKey][0][item.key] || [];
                sessionStorage.setItem(`${item.key}`, JSON.stringify(_text.join()));
              }else{
                let _subText = scheduleInfo[item.key] ? scheduleInfo[item.key] : [];
                sessionStorage.setItem(`${item.key}`, JSON.stringify(_subText.join()));
              }
              // scheduleInfo[item.key] && sessionStorage.setItem(`${item.key}`, JSON.stringify(scheduleInfo[item.key].join()));
            })
          }
          if (dynamicDataSourceForm && dynamicDataSourceForm.length > 0) {
            dynamicDataSourceForm.map((item)=>{
              sessionStorage.setItem(`${item.fieldName}`, JSON.stringify(scheduleInfo[item.fieldName] || []));
            })
          }
          if (selectionCollection && selectionCollection.length > 0) {
            selectionCollection.map((item)=>{
              if (scheduleInfo[item.selectionFields]) {
                sessionStorage.setItem(`${item.selectionFields}`,JSON.stringify(scheduleInfo[item.selectionFields]));
              }
            })
          }
          if (multipleSchema) {
            sessionStorage.setItem("multipleSchemaData", scheduleInfo[multipleSchema.key] ? JSON.stringify(scheduleInfo[multipleSchema.key]) : "");
          }

          if (rankSchema) {
            sessionStorage.setItem("rankInfo", scheduleInfo[rankSchema[0].field] ? JSON.stringify(scheduleInfo[rankSchema[0].field]) : "");
            sessionStorage.setItem("deliveryPosition", scheduleInfo['deliveryPosition'] ? JSON.stringify(scheduleInfo['deliveryPosition']) : '');
          }
        })
      }).catch(api.onRequestError);
  }

  ctrlFormData = () =>{
    let rejectMap = [
      "userTagGroupIdsList",
      "peopleType",
      "peopleOrderType",
      "channelList",
      "channelLabel",
      "citys",
      "selectedCitys",
      "resourceId",
      "pageId",
      "status",
      "yn",
      "id",
      "createUser",
      "eleCategoryFirList",
      "eleCategorySecList",
    ];
    let {formData,scheduleInfo,configId} = this.state;
    for(var o in scheduleInfo){
      if(!rejectMap.includes(o)){
        formData[o] = scheduleInfo[o]
      }
    }
    formData.configId = configId;
    this.setState({
      formData
    },()=>{
      sessionStorage.setItem('baseConfig', JSON.stringify(formData));
    })
  }

  trackSpecialFormData = (formData) => {
    try {
      const specialBizPropList = [
        "sceneType",
        "supernatantTemplate",
        "materialType",
        "sameStore",
      ];
      const matchedPropList = specialBizPropList.filter((p) => {
        return formData && formData.hasOwnProperty(p);
      });
      if (matchedPropList.length) {
        debug(
          "MarketEdit.trackSpecialFormData, specialBizProps:",
          matchedPropList.join(","),
        );
        exlog.logCustomEvent({
          code: "resource-config",
          message: "special form data",
          c1: JSON.stringify(formData),
          d1: matchedPropList.sort().join(","),
        });
      }
    } catch (e) {
      console.error(e);
    }
  }

  filterRankData = (rankData) => {
    let _rankData = JSON.parse(JSON.stringify(rankData));
    if (rankData && rankData.length > 0) {
      _rankData.map((item) => {
        const {pureDataSourceList} = item;
        let newPureDataSourceList = [];
        if(pureDataSourceList && pureDataSourceList.length>0) {
          newPureDataSourceList = pureDataSourceList.filter((o) => !(!o.categoryData.length && !o.itemCat.length && !o.poolIds.length && !o.shopList.length && !o.tabName));
        }
        item.pureDataSourceList = JSON.parse(JSON.stringify(newPureDataSourceList));
      })
    }
    return _rankData;
  }

  submit = () =>{
    let {pageUrl, configId, resourceId, resourceName, pageId, posId, posName, showPutIn, isCopy, resourceType, retailConfig, showCategory, showSign, showSignEntry, showAoi, formData, isCategoryCenter, poolTypeConfig, isJumpCategoryCenter, oversupply, showMainCategory, showMultilineIdText,dynamicDataSourceForm, multipleSchema, selectionCollection, rankSchema} = this.state;
    // const showPutIn = SchemaMap[`${resourceId}_${posId}`]["showPutIn"];
    let putInInfo;
    if(showPutIn || retailConfig){
      putInInfo = JSON.parse(sessionStorage.getItem('putInInfo'));
    }
    let baseConfig = JSON.parse(sessionStorage.getItem('baseConfig'));
    let relatedSup = sessionStorage.getItem('relatedSup') ? sessionStorage.getItem('relatedSup') : '';
    let topImgUrl = sessionStorage.getItem('topImgUrl') ? JSON.parse(sessionStorage.getItem('topImgUrl')) : '';
    if(baseConfig.redPacketTitle){
      baseConfig.redPacketTitleLeft = baseConfig.redPacketTitle.split('-')[0];
      baseConfig.redPacketTitleRight = baseConfig.redPacketTitle.split('-')[1];
    }
    if(relatedSup){
      baseConfig.relatedSup = relatedSup;
    }
    if(topImgUrl && topImgResourceGroup.includes(resourceId)){
      baseConfig.topImgUrl = topImgUrl;
    }
    if (baseConfig.activityIds && baseConfig.activityIds.length > 0) {
      baseConfig.activityIds = baseConfig.activityIds.filter(v => v != '');
    }
    if(baseConfig.jumpUrl && baseConfig.jumpUrl!=""){
      baseConfig.jumpUrl = baseConfig.jumpUrl.trim();
    }
    if(baseConfig.meetingPlaceUrl && baseConfig.meetingPlaceUrl!=""){
      baseConfig.meetingPlaceUrl = baseConfig.meetingPlaceUrl.trim();
    }
    let advertiseGroup = {};
    if (resourceId == '100051001' || resourceId == '51001') {
      advertiseGroup = JSON.parse(sessionStorage.getItem("advertiseGroup"));
    }

    if (showAoi) {
      baseConfig.ext_aoi_codes = JSON.parse(sessionStorage.getItem("ext_aoi_codes"));
    }

    if (showMainCategory) {
      baseConfig.shop_main_category = JSON.parse(sessionStorage.getItem("shop_main_category"));
      baseConfig.shopMajorCategoryList = JSON.parse(sessionStorage.getItem("shopMajorCategoryList"));
    }

    if (showMultilineIdText  && showMultilineIdText.length > 0 ) {
      showMultilineIdText.map((item)=>{
        let newText = sessionStorage.getItem(`${item.key}`);
        let _newText = (newText && JSON.parse(newText || "\"\"").length > 0) ? JSON.parse(newText || "\"\"").split(",").map((newItem) => newItem.replace(/(^\s*)|(\s*$)/g, "")) : null;
        // if (newText && JSON.parse(newText || "\"\"").length > 0) {
        //   baseConfig[item.key] =  JSON.parse(newText || "\"\"").split(",").map((newItem) => newItem.replace(/(^\s*)|(\s*$)/g, ""));
        // }else{
        //   baseConfig[item.key] = null
        // }
        if (item.parentKey) {
          baseConfig[item.parentKey] = [{
            [item.key]: _newText
          }]
        } else {
          baseConfig[item.key] = _newText;
        }
      })
    }
    if (dynamicDataSourceForm  && dynamicDataSourceForm.length > 0 ) {
      dynamicDataSourceForm.map((item)=>{
        let newText = sessionStorage.getItem(`${item.fieldName}`);
        baseConfig[item.fieldName] = newText;
      })
    }


    if (multipleSchema) {
      let _multipleData = sessionStorage.getItem('multipleSchemaData') ? (JSON.parse(sessionStorage.getItem('multipleSchemaData')) != "{}" ? JSON.parse(sessionStorage.getItem('multipleSchemaData')) : []) : [];
      const newMultipleData = _multipleData.map(obj => {
        const filteredObj = {};
        Object.keys(obj).forEach(key => {
          if (obj[key]) {
            filteredObj[key] = obj[key];
          }
        });
        return filteredObj;
      });
      baseConfig[multipleSchema.key] = newMultipleData;
    }

    if(rankSchema){
      let _rankData = sessionStorage.getItem('rankInfo') ? (JSON.parse(sessionStorage.getItem('rankInfo')) != "{}" ? JSON.parse(sessionStorage.getItem('rankInfo')) : []) : [];
      baseConfig[rankSchema[0].field] = this.filterRankData(_rankData);

      let _deliveryPosition = sessionStorage.getItem('deliveryPosition') ? (JSON.parse(sessionStorage.getItem('deliveryPosition')) != "{}" ? JSON.parse(sessionStorage.getItem('deliveryPosition')) : []) : [];
      baseConfig['deliveryPosition'] = _deliveryPosition;
    }

    let deliveryInfoObj = {
      ...putInInfo,
      ...baseConfig,
      ...advertiseGroup
    }

    if (deliveryInfoObj.materialType == '2' && deliveryInfoObj.selectViewList && deliveryInfoObj.selectViewList.length > 0) {
      delete deliveryInfoObj.selectViewList;
    }

    if(deliveryInfoObj.channelVersionList && putInInfo.channelVersionList){
      deliveryInfoObj.channelVersionList = JSON.parse(JSON.stringify(putInInfo.channelVersionList));
    }
    if(deliveryInfoObj.channelVersionList && !(deliveryInfoObj.channelList.includes("*") || deliveryInfoObj.channelList.includes("android") || deliveryInfoObj.channelList.includes("ios"))){
      delete deliveryInfoObj.channelVersionList;
    }
    if(deliveryInfoObj.dataResourceDTOList && putInInfo.dataResourceDTOList){
      deliveryInfoObj.dataResourceDTOList = JSON.parse(JSON.stringify(putInInfo.dataResourceDTOList));
    }
    if (showCategory) {
      deliveryInfoObj.searchLabelList = JSON.parse(sessionStorage.getItem("searchLabelList"));
      if (resourceType == '143') {
        deliveryInfoObj.linkAlgorithmPolicy = sessionStorage.getItem("linkAlgorithmPolicy");
      }
    }
    if (isCategoryCenter) {
      deliveryInfoObj.linkAlgorithmPolicy = sessionStorage.getItem("linkAlgorithmCenter");
    }
    if(showSign|| (resourceId=='100071002' || resourceId=='71002')){
      deliveryInfoObj.signRedBagList = JSON.parse(sessionStorage.getItem("signRedBagList"));
    }
    if(showSignEntry){
      deliveryInfoObj.noSignInCopyList = JSON.parse(sessionStorage.getItem("noSignInCopyList"));
      deliveryInfoObj.signInCopyList = JSON.parse(sessionStorage.getItem("signInCopyList"));
    }

    let extPoolId = sessionStorage.getItem("ext_pool_ids");
    let extPoolGroupLabel = sessionStorage.getItem("ext_pool_group_label");
    if(poolTypeConfig){
      deliveryInfoObj.ext_pool_ids = extPoolId ? extPoolId.split(",") : [];
      deliveryInfoObj.ext_pool_group_label = extPoolGroupLabel ? JSON.parse(extPoolGroupLabel) : [];
    }
    if (typeof oversupply == 'object') {
      const isDynamic = typeof oversupply.tabKey != 'undefined';
      let request = JSON.parse((isDynamic ? sessionStorage.getItem(`${oversupply.tabKey}`) : sessionStorage.getItem('pureDataSourceList')) || "[]")
      // 切换瀑布流需要更改字段&加tabinex
      request = request.map((item,index)=>{
        if (oversupply.field && item[oversupply.field] && item[oversupply.field].length > 0) {
          if ((deliveryInfoObj.sceneType == "1" || deliveryInfoObj.supernatantTemplate == "0")) {
            delete item[oversupply.field]
          }else{
            item.tabIndex = index + 1
          }
        }
        return item
      })
      if (isDynamic) {
        deliveryInfoObj[oversupply.tabKey] = request
      } else {
        deliveryInfoObj.pureDataSourceList = request
        // 如果 pureDataSourceList 是空数组，则将 pureDataSourceList 删除
        // 否则，后端接收到空数组会将投放计划配置判定为非法数据，导致投放计划配置在 C 端不生效
        if (Array.isArray(request) && request.length === 0) {
          delete deliveryInfoObj.pureDataSourceList
        }
      }
    }

    if (selectionCollection  && selectionCollection.length > 0 ) {
      selectionCollection.map((item)=>{
        let newText = sessionStorage.getItem(`${item.selectionFields}`) || "[]";
        deliveryInfoObj[item.selectionFields] = JSON.parse(newText);
      })
    }

    let deliveryRequestDTO = {
      pageId,
      resourceId,
      locationId:posId,
      deliveryInfo:JSON.stringify(deliveryInfoObj)
    }
    let text = '创建';
    if(configId && !isCopy){
      deliveryRequestDTO.id = configId;
      text = '编辑';
    }
    if(configId && isCopy){
      text = '复制';
    }
    if(relatedSup){
      deliveryRequestDTO.parentId = relatedSup;
    }
    this.setState({submitLoading: true});

    let customTask = Promise.resolve()
    const originDeliveryInfoJSON = deliveryRequestDTO.deliveryInfo
    // 目前的配置表单能力并不支持比较灵活的字段映射等功能，并且也不应该有这类功能，但是有些需求确实需要上传表单以外的内容，比如图片的宽度和高度
    // 为了满足这类需求，目前只能考虑针对资源位或者坑位，增加定制逻辑
    if (`${resourceId}` === '100160012' || `${resourceId}` === '160012') {
      // 触发定制逻辑：全能超市频道3.0-垂类馆-小酒馆组货品
      // 需要额外为组货品图片上传图片的宽度和高度
      try {
        const deliveryInfo = JSON.parse(originDeliveryInfoJSON)
        const url = deliveryInfo.mainImageUrl
        if (url) {
          customTask = new Promise((resolve, reject) => {
            const img = new Image()
            img.onerror = e => reject(e)
            img.onload = () => {
              deliveryInfo.ext_main_image_width = `${img.width}`
              deliveryInfo.ext_main_image_high = `${img.height}`
              const deliveryInfoJSON = JSON.stringify(deliveryInfo)
              deliveryRequestDTO.deliveryInfo = deliveryInfoJSON
              console.log('customTask activated', 'prev deliveryInfo', originDeliveryInfoJSON, 'current deliveryInfo', deliveryInfoJSON)
              resolve()
            }
            img.src = url
          })
        } else {
          // impossible
          throw new Error('请上传组货品图片')
        }
      } catch(e) {
        Message.error(e.message)
        throw e
      }
    }

    customTask.then(() => {
      // const pageUrl = pageMap.filter(v => v.pageId == pageId)[0].url;
      if(!showQrCodeMap[pageUrl]){
        createDelivery(deliveryRequestDTO)
          .then((result) => {
            Message.success(`${text}成功`);
            // const prevPageUrl = (configId) ? nowUrl.substring(0, nowUrl.lastIndexOf('/', nowUrl.lastIndexOf("/") - 1)) : nowUrl.substring(0, location.href.lastIndexOf('/'));
            // const prevPageUrl = pageUrl.substring(0, pageUrl.lastIndexOf('/'));
            let viewConfigureUrl = `#${pageUrl}/viewConfigure?pageId=${pageId}&posName=${posName}&posId=${posId}&resourceId=${resourceId}&resourceName=${resourceName}&resourceType=${resourceType}`;
            if((resourceId!='100072001' && resourceId!='72001' && resourceId!='100072005' && resourceId!='72005'&& resourceId!='100007046' && resourceId!='7046' && !((resourceId=='100160001' || resourceId == '160001') && deliveryInfoObj.displayFilterProperty == 'true')) && !isJumpCategoryCenter){
              sessionStorage.clear();
              // const pageUrl = pageMap.filter(v => v.pageId == pageId)[0].url;
              location.href = viewConfigureUrl;
            }else{
              Dialog.confirm({
                title: "",
                content: "点击跳转到品类运营中心",
                onOk: () => {
                  if(resourceId=='100080001' || resourceId=='80001'){
                    // location.href = centerGoldUrl[window.configEnv];
                    window.open(centerGoldUrl[window.configEnv])
                  }else if(resourceId=='100080002' || resourceId=='80002'){
                    // location.href = centerTabUrl[window.configEnv];
                    window.open(centerTabUrl[window.configEnv])
                  }else {
                    // location.href = centerUrl[window.configEnv];
                    window.open(centerUrl[window.configEnv])
                  }
                },
                onCancel: () => {
                  location.href = viewConfigureUrl;
                }
              });
            }
            }).catch(api.onRequestError).finally(() => this.setState({submitLoading: false}))
      }else{
        saveDelivery(deliveryRequestDTO)
          .then((result) => {
            Message.success(`${text}成功`);
            sessionStorage.clear();
            // const pageUrl = pageMap.filter(v => v.pageId == pageId)[0].url;
            let query = {
              resourceId,
              posId,
              resourceName,
              posName,
              pageId,
              resourceType
            }
            location.href = `#${pageUrl}/viewConfig/${result}?${qs.stringify(query)}`;
            // location.href = `#${pageUrl}/viewConfigure?pageId=${pageId}&posName=${posName}&posId=${posId}&resourceId=${resourceId}&resourceName=${resourceName}&resourceType=${resourceType}`;
          }).catch(api.onRequestError).finally(() => this.setState({submitLoading: false}))
      }
    }, e => {
      this.setState({ submitLoading: false })
      Message.error((e && e.message) || '系统异常，请稍后重试')
    })
  }

  nextStep = () => {
    debugLF('MarketEdit.nextStep');
    let { showDataConfig, showConfigGroup,step } = this.state;
    if (showDataConfig || showConfigGroup) {
      sessionStorage.setItem('step', 1 + parseInt(step));
      this.setState({
        step: 1 + parseInt(this.state.step)
      }, () => {
        replaceParamVal('step', this.state.step);
      })
    } else {
      this.submit();
    }
  }

  componentWillReceiveProps(newProps){
    debugLF("Step1 will receive props");
    if(getQueryString('step')) {
      this.setState({
        step: parseInt(getQueryString('step'))
      },()=>{
        // sessionStorage.setItem("step",this.state.step);
      })
    }
  }

  render() {
    let {pageUrl, step, showDataConfig, showConfigGroup, showSupChannel, resourceId, resourceName, posId, posName, pageId, formData, scheduleTemplate} = this.state;
    // const curPage = pageMap.filter(v => v.pageId == pageId)[0];
    const configGroup = scheduleTemplate ? scheduleTemplate.configGroup : [];

    this.trackSpecialFormData(formData);

    return (
      <div className="container resource-edit">
        {!this.props.pageId && <div className="title">
          <Breadcrumb>
            <Breadcrumb.Item>频道管理</Breadcrumb.Item>
            {/*<Breadcrumb.Item>{curPage.pageName}</Breadcrumb.Item>*/}
            <Breadcrumb.Item><Link to={`${pageUrl}/${pageId}?anchorId=${resourceId}`}>{resourceName}</Link></Breadcrumb.Item>
            <Breadcrumb.Item>{posName}</Breadcrumb.Item>
            <Breadcrumb.Item>去投放</Breadcrumb.Item>
          </Breadcrumb>
        </div>}
        <div className="body">
          {showConfigGroup && <Step shape="circle" current={step}>
            {configGroup.map((v) => {
              return <Step.Item title={v.title}/>
            })}
          </Step>}
          {showDataConfig && <Step shape="circle" current={step}>
            <Step.Item title="基础配置"/>
            <Step.Item title="数据配置"/>
          </Step>}
          <Row>
            <Col span='2'></Col>
            {showConfigGroup ? <Col span='19'>
              {configGroup.map((v, i) => {
                if (v.type == 1 && step == 0) {
                  return <Step1
                    nextStep={this.nextStep}
                    {...this.state}
                  />
                }
                if (v.type == 2 && step == i) {
                  return <Step2 {...this.state}/>
                }
              })}
            </Col> : <Col span="19">
              {step == 0 ? (
                <Step1
                  nextStep={this.nextStep}
                  {...this.state}
                />
              ) : (step == 1 && showDataConfig) ? (
                <Step2 {...this.state}/>
              ) : null}
            </Col>}
          </Row>
        </div>
      </div>
    )
  }
}

class Step1 extends React.Component {
  constructor(props) {
    super(props);
    console.log("🚀 ~ file: edit.js:843 ~ constructor ~ props:", props)
    debugLF('Step1 constructor', { props })
    this.state = {
      // formData:props.formData,
      formData:sessionStorage.getItem('baseConfig') ? JSON.parse(sessionStorage.getItem('baseConfig')) : props.formData,
      pageId:props.pageId,
      resourceId:props.resourceId,
      resourceName:props.resourceName,
      curPage:props.curPage,
      retatedData:[],
      sceneWordsData:[],
      relatedSup: sessionStorage.getItem('relatedSup')?sessionStorage.getItem('relatedSup'):'',
      sceneWords:sessionStorage.getItem('sceneWords')?sessionStorage.getItem('sceneWords'):'',
      topImgUrl:sessionStorage.getItem('topImgUrl')?JSON.parse(sessionStorage.getItem('topImgUrl')):'',
      isSubmit: false,
      posId: props.posId,
      reviewOpen: 0,
      hasAlipayApp: false,
      poolTypeConfig:props.poolTypeConfig,
      pageUrl: props.pageUrl
    }
  }

  componentDidMount() {
    debugLF("Step1 did mount")
    let {curPage} = this.state;
    // if(curPage.isInside) {
    //   this.getRetatedData();
    // }
    if(this.props.showSceneWords){
      this.getSceneWords();
    }
    this.getReview();

    const orignalSetItem = sessionStorage.setItem
    sessionStorage.setItem = function (key, newValue) {
      const setItemEvent = new Event("setItemEvent")
      setItemEvent.newValue = newValue
      window.dispatchEvent(setItemEvent)
      orignalSetItem.apply(this, arguments)
    }

    window.addEventListener("setItemEvent", (e) => {
      //监听
      const storeString = e.newValue
      let storeJson;
      try {
        storeJson = storeString ? JSON.parse(storeString) : {};
      } catch (error) {
        storeJson = storeString;
      }
      if (!storeJson.channelList) {
        return false
      }
      const channelList = storeJson.channelList
      if (channelList.length === 0) {
        return false
      }
      this.setState({
        hasAlipayApp: false
      })
      const alipayArr = ['miniapp.koubeitab', 'miniapp.alipay', 'miniapp.newretailalipay']
      let diff = channelList.filter(function (val) { return alipayArr.indexOf(val) === -1 })
      console.log(diff)
      if (diff.length === 0) {
        this.setState({
          hasAlipayApp: true
        })
      }
    })
  }

  componentWillUnmount() {
    debugLF('Step1 will unmount');
  }

  componentWillUpdate(prevProps,prevState){
    debugLF("Step1 will update")
    if (prevState.formData.sceneType !== this.state.formData.sceneType || prevState.formData.supernatantTemplate !== this.state.formData.supernatantTemplate) {
      if (this.props.oversupply && typeof this.props.oversupply.tabKey != 'undefined') {
        sessionStorage.setItem(`${this.props.oversupply.tabKey}`,'[]')
      }else{
        sessionStorage.setItem('pureDataSourceList','[{}]')
      }
    }
    // 时段更改特定的枚举
    if (prevState.formData.timeSelection !== this.state.formData.timeSelection) {
      const nowBaseConfig = deepCopy(JSON.parse(sessionStorage.getItem('baseConfig')));
      const nowFormData = deepCopy(prevState.formData)
      let timeRanges = []
      switch (prevState.formData.timeSelection) {
        case '0':
        case '1':
          timeRanges = []
          nowBaseConfig.timeRanges = []
          nowFormData.timeRanges = []
          break;
        case '2':
          timeRanges = ['04:00:00-10:00:00']
          nowBaseConfig.timeRanges = ['04:00:00-10:00:00']
          nowFormData.timeRanges = ['04:00:00-10:00:00']
          break;
        case '3':
          timeRanges = ['10:00:00-14:00:00']
          nowBaseConfig.timeRanges = ['10:00:00-14:00:00']
          nowFormData.timeRanges = ['10:00:00-14:00:00']
          break;
        case '4':
          timeRanges = ['14:00:00-17:00:00']
          nowBaseConfig.timeRanges = ['14:00:00-17:00:00']
          nowFormData.timeRanges = ['14:00:00-17:00:00']
          break;
        case '5':
          timeRanges = ['17:00:00-20:00:00']
          nowBaseConfig.timeRanges = ['17:00:00-20:00:00']
          nowFormData.timeRanges = ['17:00:00-20:00:00']
          break;
        case '6':
          timeRanges = ['20:00:00-23:59:59','00:00:00-04:00:00']
          nowBaseConfig.timeRanges = ['20:00:00-23:59:59','00:00:00-04:00:00']
          nowFormData.timeRanges = ['20:00:00-23:59:59','00:00:00-04:00:00']
          break;
        default:
          timeRanges = []
          nowBaseConfig.timeRanges = []
          nowFormData.timeRanges = []
          break;
      }
      this.setState({formData:nowFormData})
      sessionStorage.setItem('baseConfig',JSON.stringify(nowBaseConfig))
    }
  }

  getReview = () =>{
    let {resourceId, pageId} = this.state;
    let reviewRequestDTO = {
      pageId,
      resourceId
    }
    if(pageId) {
      queryReview({...reviewRequestDTO}).then((data) => {
        this.setState({reviewOpen: data});
        sessionStorage.setItem('reviewOpen', data);
      }).catch(api.onRequestError)
    }
  }

  getSceneWords = () => {
    // let {resourceId, pageId} = this.state;
    // let queryResourceRequestDTO = {
    //   resourceId,
    //   pageId,
    // }
    getSceneWord()
      .then((result) => {
        this.setState({
          sceneWordsData: result
        })
      }).catch(api.onRequestError);
  }

  getRetatedData = () => {
    let {resourceId, pageId, configId,posId,step} = this.state;
    let queryResourceRequestDTO = {
      resourceId,
      pageId,
    }
    if (resourceId=='100025023' || resourceId=='25023') {
      queryResourceRequestDTO = {
        page: 1,
        size: 50,
        query: {
          configId,
          pageId:resourceId=='100025023'?"100003000":"3000",
          resourceId:resourceId=='100025023'?"100003070":"3070",
          step,
          posId:resourceId=='100025023'?"944":"957",
          type: "0",
          status: 6
        },
      };
      getConfigList(queryResourceRequestDTO)
      .then((result) => {
        this.setState({
          retatedData: result.rows.map((item)=>{
            return {
              value : ""+item.id,
              label : "("+item.id+") "+item.name
            }
          })
        })
      }).catch(api.onRequestError);
    }else{
      getParentConfigList(queryResourceRequestDTO)
      .then((result) => {
        this.setState({
          retatedData: result
        })
      }).catch(api.onRequestError);
    }
  }

  onFormChange = (formData) => {
    sessionStorage.setItem('baseConfig', JSON.stringify(formData));
    this.setState({
      formData
    });
  }
  handleChange = (value) =>{
    let newValue = (value === null || value == '') ? '' : value
    this.setState({
      relatedSup:newValue
    },()=>{
      sessionStorage.setItem('relatedSup', newValue);
    })
  }
  topImgUrlChange = (value) =>{
    let newValue = (value === null || value == '') ? '' : value
    this.setState({
      topImgUrl:newValue
    },()=>{
      sessionStorage.setItem('topImgUrl', JSON.stringify(newValue));
    })
  }

  handleSceneWordsChange = (value,data) =>{
    if (value == '1') return;  // 默认分组不让选
    let result = data ? data.label : '';
    this.setState({
      sceneWords: result
    }, () => {
      sessionStorage.setItem('sceneWords', result);
    })
  }

  componentWillReceiveProps(newProps) {
    debugLF('Step1 will receive props', { newProps })
    console.log("🚀 ~ file: edit.js:988 ~ componentWillReceiveProps ~ newProps:", newProps)
    if(newProps.showSceneWords){
      this.getSceneWords();
    }
    if(newProps.showSupChannel) {
      this.getRetatedData();
    }
    this.setState({
      poolTypeConfig: newProps.poolTypeConfig,
      curPage: newProps.curPage,
      formData: sessionStorage.getItem('baseConfig') ? JSON.parse(sessionStorage.getItem('baseConfig')) : newProps.formData,
      // formData:newProps.formData,
      // relatedSup: (newProps.scheduleInfo) ? newProps.scheduleInfo.relatedSup : '',
      topImgUrl: sessionStorage.getItem('topImgUrl')?JSON.parse(sessionStorage.getItem('topImgUrl')):'',
      relatedSup: sessionStorage.getItem('relatedSup')?sessionStorage.getItem('relatedSup'):'',
      sceneWords:sessionStorage.getItem('sceneWords')?sessionStorage.getItem('sceneWords'):''
    })
  }

  submit = async(event) => {
    console.log(this.state);
    let {isCopy, showDataConfig, showConfigGroup} = this.props;
    let {resourceId, posId, configId} = this.state;
    let isShowDialog = this.state.reviewOpen==1 && sessionStorage.getItem('status') == 5 || sessionStorage.getItem('status') == 6;

    // if (!isCopy && !(showDataConfig || showConfigGroup) && isShowDialog) {
    //   Dialog.confirm({
    //     title: '提示',
    //     content:'保存后，原先活动失效，会重新发起审核流程，请确认？',
    //     onOk: () => this.confirmSubmit(event)
    //   });
    // }else{
    //   this.confirmSubmit(event);
    // }
    this.confirmSubmit(event)
  }

  confirmSubmit =  async(event) => {
    event.stopPropagation();
    event.preventDefault();
    this.setState({
      isSubmit:true
    })
    const isValidate = await this.validate();
    // 看看schemaform是否有报错，如果有则不能发布。
    const errors = this.refs.formRef.fieldErrorMap;
    console.warn(errors);
    const formData = this.refs.formRef.submit();
    if (!Object.keys(errors).length && formData && isValidate) {
      this.props.nextStep();
    }
  }

  countSecond = (time) => {
    var hour = time.split(':')[0];
    var min = time.split(':')[1];
    var sec = time.split(':')[2];
    return Number(hour * 3600) + Number(min * 60) + Number(sec);
  }

  validateActivityPoolType = async (activityIds, poolType) => {
    let activityMsg = [];
    let newActivityIds = await this.getActivityPoolType(activityIds);
    activityIds.map((v) => {
      let filterGroup = newActivityIds.filter(item => item.activityId == v);
      if (v != '') {
        if (filterGroup.length == 0) {
          activityMsg.push(`【${v}】格式错误，请自查`);
        } else if (filterGroup.length > 0 && filterGroup[0].poolType != parseInt(poolType)) {
          activityMsg.push(`【${v}】类型不对，请填写为${poolType == 1 ? '商品' : '门店'}类型的投放活动ID`);
        }
      }
    })
    return activityMsg;
  }

  getActivityPoolType = async (activityIds) => {
    try {
      const res = await queryActivityPoolType(activityIds);
      const newActivityIds = res.data;
      return newActivityIds;
    } catch (error) {
      api.onRequestError(error)
    }
  }

  validateTimeRange = () => {
    let baseConfig = JSON.parse(sessionStorage.getItem('baseConfig'));
    let result = false;
    if (baseConfig.timeSelection == '1' && baseConfig.timeRanges) {
      baseConfig.timeRanges.map((v) => {
        if (v.split('-')[0] == '' || v.split('-')[1] == '') {
          result = 2;
        } else if (this.countSecond(v.split('-')[0]) > this.countSecond(v.split('-')[1])) {
          result = 3;
        }
      })
    }
    if (moment(baseConfig.endTime).diff(moment()) < 0) {
      result = 4;
    }else if (moment(baseConfig.endTime).diff(moment(baseConfig.beginTime)) <= 0) {
      result = 1;
    }
    if (baseConfig.redPacketEndTime && baseConfig.redPacketStartTime && moment(baseConfig.redPacketEndTime).diff(moment(baseConfig.redPacketStartTime)) <= 0) {
      result = 5;
    }
    return result;
  }

  checkUrl = (url) => {
    if (!this.state.hasAlipayApp) {
      return url && (url.indexOf('eleme://') < 0 || url.includes('url=eleme'));
    } else {
      return false
    }
  }

  validate = async() =>{
    let {sceneWords,relatedSup} = this.state;
    let {scheduleTemplate, removeCheckJumpUrl, oversupply, showMainCategory, showMultilineIdText,dynamicDataSourceForm, selectionCollection} = this.props;
    let newActivityMsg = [];
    let baseConfig = JSON.parse(sessionStorage.getItem('baseConfig'));
    if (this.validateTimeRange()) {
      Message.error(validateMap[this.validateTimeRange()]);
      return false;
    }
    if (baseConfig.timeSelection == "1" && (!baseConfig.timeRanges || baseConfig.timeRanges.length < 1)) {
      Message.error('请选择时段');
      return false;
    }
    if (baseConfig.priority && baseConfig.priority < 0) {
      Message.error('权重格式错误，请自查');
      return false;
    }
    if (this.props.showSceneWords && !sceneWords) {
      return false;
    }

    let rejectCheckMap = ["100061003", "100061005", "100071012", "100071013","100003073", "61003", "61005", "71012", "71013","3073"];
    if (this.checkUrl(baseConfig.jumpUrl) && !rejectCheckMap.includes(this.state.resourceId) && !removeCheckJumpUrl) {
    // if (this.checkUrl(baseConfig.jumpUrl)) {
      Message.error('跳转链接格式错误，请自查');
      return false;
    }


    // if (baseConfig.meetingPlaceUrl && !baseConfig.meetingPlaceUrl.includes('/wow/luna/welkin/') && !baseConfig.meetingPlaceUrl.includes('%2Fwow%2Fluna%2Fwelkin%2F')) {
    //   Message.error('只允许填写兵马俑链接');
    //   return false;
    // }
    if (scheduleTemplate && scheduleTemplate['detail']['required'].includes('activityIds') && (typeof baseConfig.activityIds == 'undefined' || baseConfig.activityIds.length == 0 || baseConfig.activityIds.filter(v => v != '').length == 0)) { //临时方案，换变更在schema中
      Message.error('投放活动ID必填');
      return false;
    }
    const resourceIdGroup = ['25018', '25019', '100025018', '100025019'];
    let resourceId = getQueryString('resourceId');
    let resourceType = getQueryString('resourceType');
    if (this.props.showSupChannel && !relatedSup && !(resourceId == "100025023" || resourceId == "25023")) {
      return false;
    }
    if (resourceId && (resourceIdGroup.includes(resourceId)) && (typeof baseConfig.brandIdList == 'undefined' || baseConfig.brandIdList.length == 0)) {
      Message.warning('品牌类型必填');
      return false;
    }
    if (baseConfig.activityIds && baseConfig.activityIds.length > 0) { //投放活动ID的提示消息
      newActivityMsg = await this.validateActivityPoolType(baseConfig.activityIds, baseConfig.activityType);
      if (newActivityMsg.length > 0) {
        let msgEle = [];
        newActivityMsg.map((v) => {
          msgEle.push(<span style={{display: 'block', marginBottom: '7px'}}>{v}</span>)
        })
        Message.show({
          type: 'error',
          title: '投放活动ID校验提示',
          content: msgEle
        });
      }
      if (newActivityMsg.length > 0) {
        return false;
      }
    }
    if (baseConfig.goodsActivityIds && baseConfig.goodsActivityIds.length) {
      newActivityMsg = await this.validateActivityPoolType(baseConfig.goodsActivityIds, 1);
      if (newActivityMsg.length > 0) {
        let msgEle = [];
        newActivityMsg.map((v) => {
          msgEle.push(<span style={{ display: 'block', marginBottom: '7px' }}>{v}</span>)
        })
        Message.show({
          type: 'error',
          title: '投放活动ID校验提示',
          content: msgEle
        });
      }
      if (newActivityMsg.length > 0) {
        return false;
      }
    }
    if (baseConfig.shopActivityIds && baseConfig.shopActivityIds.length) {
      newActivityMsg = await this.validateActivityPoolType(baseConfig.shopActivityIds, 2);
      if (newActivityMsg.length > 0) {
        let msgEle = [];
        newActivityMsg.map((v) => {
          msgEle.push(<span style={{ display: 'block', marginBottom: '7px' }}>{v}</span>)
        })
        Message.show({
          type: 'error',
          title: '投放活动ID校验提示',
          content: msgEle
        });
      }
      if (newActivityMsg.length > 0) {
        return false;
      }
    }
    if (baseConfig.putInActivityIds && baseConfig.putInActivityIds.length > 0) { //投放活动ID的提示消息
      newActivityMsg = await this.validateActivityPoolType(baseConfig.putInActivityIds, baseConfig.cardType);
      if (newActivityMsg.length > 0) {
        let msgEle = [];
        newActivityMsg.map((v) => {
          msgEle.push(<span style={{display: 'block', marginBottom: '7px'}}>{v}</span>)
        })
        Message.show({
          type: 'error',
          title: '投放活动ID校验提示',
          content: msgEle
        });
      }
      if (newActivityMsg.length > 0) {
        return false;
      }
    }
    let putInInfo = JSON.parse(sessionStorage.getItem('putInInfo'));
    if (putInInfo && putInInfo.channelVersionList && !putInInfo.channelVersionList.allVersion) {
      let tmp = putInInfo.channelVersionList.versionGroup.versionList.filter(v => v.value == '' || v.operateType == '');
      if (tmp.length > 0) {
        Message.warning('非全版本时，版本类型和版本号必填');
        return false;
      }
      let tmpArr = putInInfo.channelVersionList.versionGroup.versionList.map(v=>v.value).join(",");
      if (this.validateVersionGroup(tmpArr).length > 0) {
        Message.warning('版本号不符合规范');
        return false;
      }
    }
    if (this.props.retailConfig && baseConfig.sceneId && isNaN(Number(baseConfig.sceneId))) {
      Message.warning('场景ID必须输入数字');
      return false;
    }
    if(this.props.retailConfig  && (!putInInfo || !putInInfo.dataResourceDTOList)){
      Message.warning('选品集ID必填');
      return false;
    }
    if(this.props.retailConfig && putInInfo && putInInfo.dataResourceDTOList){
      let tmp = putInInfo.dataResourceDTOList.map(v => v.dataResourceList);
      let tmpPoolIds = putInInfo.dataResourceDTOList.filter(v => v.dataResourceList == "");
      if (tmpPoolIds.length > 0) {
        Message.warning('选品集ID必填');
        return false;
      }
      // if (this.validatePoolIds(tmp).length > 0) {
      //   Message.warning('选品集不符合规范');
      //   return false;
      // }
    }
    let {cardType} = baseConfig; //商超频道页-营销卡
    let keyFieldGroup = {
      "1": "putInActivityIds",
      "2": "putInActivityIds",
      "3": "activityIds"
    }
    let keyField = cardType ? keyFieldGroup[cardType] : '';
    if (Boolean(cardType) && (typeof baseConfig[keyField] == 'undefined' || baseConfig[keyField].length == 0 || baseConfig[keyField].filter(v => v != '').length == 0)) {
      Message.error('投放活动ID必填');
      return false;
    }

    let advertiseGroup = JSON.parse(sessionStorage.getItem("advertiseGroup")); //品牌霸屏对视频和图片的校验
    if(advertiseGroup && advertiseGroup.adFloat && advertiseGroup.adFloat=='1' && (!advertiseGroup.brandBaPingUrl || advertiseGroup.brandBaPingUrl == '')){
      Message.error('增加广告位浮层已选是，视频或者图片必填');
      return false;
    }
    // if ((resourceType == '143') && (resourceId != '100009040' && resourceId != '9040' && resourceId != '100072001' && resourceId != '72001' && resourceId != '100072005' && resourceId != '72005' && resourceId != '100007046' && resourceId != '7046' && resourceId != '100120001' && resourceId != '20001') && (!sessionStorage.getItem("linkAlgorithmPolicy"))) {
    //   Message.error('关联算法策略必填');
    //   return false;
    // }
    // if (this.props.isCategoryCenter && (!sessionStorage.getItem("linkAlgorithmCenter"))) {
    //   Message.error('关联算法策略必填');
    //   return false;
    // }
    // 校验activityAndBenefitRelations
    if(baseConfig.activityAndBenefitRelations) {
      let validate = true;
      baseConfig.activityAndBenefitRelations.forEach(item => {
        const idArray = item.split('-');
        if(idArray.length != 2 || idArray[0] == '' || idArray[1] == '') {
          validate = false;
          Message.error('投放活动和利益点填写有误!');
        }
      })
      if(!validate) {
        return validate;
      }
    }
    // 投放商品/门店改为非必填——许仲 淘宝闪购 - 盒马频道， 后期如果有必填的需求，新增组件支持配置，目前黑盒，不能配置是否必填。
    // let {poolTypeConfig} = this.state;
    // if(poolTypeConfig && !sessionStorage.getItem("ext_pool_ids")){
    //   Message.error(`投放${poolTypeConfig == 1 ? '商品' : '门店'}必填`);
    //   return false;
    // }
    // 特价小程序校验权益池  暂时注释掉
    // if(((resourceId=='100071002' || resourceId=='71002') && this.state.formData.gameType=='sign')){
    //   let validate = true;
    //   let signRedBagList = JSON.parse(sessionStorage.getItem('signRedBagList'));
    //   signRedBagList.map((item,index)=>{
    //     if (item.appointRightId===""||(!item.appointRightId===0 || !item.appointRightId)) {
    //       Message.error('权益池ID不能为空!')
    //       validate = false;
    //     }
    //   })
    //   if(!validate) {
    //     return validate;
    //   }
    // }

    // 全能超市3.0校验

    // 是否是热卖：全能超市3.0 常买热卖资源位
    let showReSell = JSON.parse(sessionStorage.getItem('baseConfig')) != null && (!JSON.parse(sessionStorage.getItem('baseConfig')).resourceSubType || (JSON.parse(sessionStorage.getItem('baseConfig')).resourceSubType != null && JSON.parse(sessionStorage.getItem('baseConfig')).resourceSubType == "2"));
    if (typeof oversupply == 'object' && showReSell && !oversupply.isNotRequired) {
      let superMarket = (typeof oversupply.tabKey != 'undefined' ? JSON.parse(sessionStorage.getItem(`${oversupply.tabKey}`)) : JSON.parse(sessionStorage.getItem('pureDataSourceList'))) || []
      let errorText = ""
      // const {ids,shopList,supplyType,itemCat,categoryData} = superMarket
      const isHidden = isHiddenMultilineIdText(oversupply.hidden || '',baseConfig)
      isHidden && superMarket.map((item,index)=>{
        if(Object.keys(item).length >0){
          const {poolIds = [],shopList,supplyType = 1,itemCat = [], categoryData = [], marketActivityIds = [], investIdList = [],assembleItemPoolIds = [], cornerIconUrl} = item
          if ((this.state.formData.supernatantTemplate == '1' || typeof this.state.formData.supernatantTemplate == 'undefined') && oversupply && oversupply.field && superMarket.length >= 1 && (!item[oversupply.field] || item[oversupply.field].length<=0) && (oversupply.fieldRequired || !(typeof oversupply.fieldRequired == 'undefined'))) {
            errorText = (superMarket.length > 1 ? `第${index+1}个`: "") + oversupply.name + "不能为空"
          }
          if (supplyType == 1 && poolIds.length <= 0) {
            errorText = superMarket.length > 1 ? `第${index + 1}个选品集ID必填` : "选品集ID必填"
          }
          if (supplyType == 2 && itemCat.length <= 0) {
            errorText = superMarket.length > 1 ? `第${index + 1}个商品类目必填` : "商品类目必填"
          }
          if (supplyType == 3 && marketActivityIds.length <= 0) {
            errorText = superMarket.length > 1 ? `第${index + 1}个营销活动ID必填` : "营销活动ID必填"
          }
          if (supplyType == 4 && investIdList.length <= 0) {
            errorText = superMarket.length > 1 ? `第${index + 1}个招商活动ID必填` : "招商活动ID必填"
          }
          if(oversupply.assemblePools && !oversupply.assemblePools.hidden && this.state.formData.sameStore == '0' && assembleItemPoolIds <= 0){
            errorText = superMarket.length > 1 ? `第${index + 1}个店铺选品集必填` : "店铺选品集必填"
          }
          if (oversupply.showCornerIcon && !cornerIconUrl) {
            errorText = superMarket.length > 1 ? `第${index + 1}个角标必填` : "角标必填"
          }
        }
      })
      if(superMarket.length <=0 && oversupply.nameSetting){
        errorText = oversupply.nameSetting + '最少一个'
      }
      if (errorText) {
        Message.error(errorText)
        return false
      }
    }

    if (!!showMainCategory) {
      const { required = false, maxLength , minLength } = showMainCategory;
      let shopMainCategory = JSON.parse(sessionStorage.getItem('shop_main_category')) || []
      let errorText = ""
      if (maxLength && shopMainCategory.length > maxLength) {
        errorText = `商家主营类目最多不超过${maxLength}个`
      }
      if (minLength && minLength > shopMainCategory) {
        errorText = `商家主营类目最少为${minLength}个`
      }
      if (required && shopMainCategory.length <= 0) {
        errorText = `商家主营类目必填`
      }
      if (errorText) {
        Message.error(errorText)
        return false
      }
    }

    // 晚点放开
    let queryWordList = sessionStorage.getItem('multipleSchemaData') ? (JSON.parse(sessionStorage.getItem('multipleSchemaData')) != '{}' ? JSON.parse(sessionStorage.getItem('multipleSchemaData')) : []) : [];
    if (queryWordList && queryWordList.length > 0) {
      let errorText = ""
      queryWordList.map((item) => {
        let {logoImgUrl, word} = item;
        if((item.hasOwnProperty('logoImgUrl') && item.hasOwnProperty('word')) && ((logoImgUrl === '' && word !== '') || (logoImgUrl !== '' && word === ''))) {
          errorText = `配置图片和文案需要同时存在`
        }
      })
      if (errorText) {
        Message.error(errorText)
        return false
      }
    }

    if (showMultilineIdText && showMultilineIdText.length > 0) {
      let errorText = ""
      showMultilineIdText.map((item)=>{
        const { required = false, maxLength, hidden = '' } = item;
        const isHidden = isHiddenMultilineIdText(hidden,baseConfig)
        if (isHidden) {
          let itemValue = JSON.parse(sessionStorage.getItem(`${item.key}`))
          if (!itemValue && required) {
            errorText = `${item.name}为必填项`
          }
          if(maxLength && itemValue && itemValue.split(',').length > maxLength){
            errorText = `${item.name}输入超过最大长度${maxLength}个`
          }else if(itemValue && (!(/^(\d+)(,\s*\d+)*$/).test(itemValue))){
            errorText = `${item.name}输入格式不合法`
          }else if(itemValue && itemValue.indexOf(" ")!=-1){
            errorText = `${item.name}输入不能有空格`
          }
        }
      })
      if (errorText) {
        Message.error(errorText)
        return false
      }
    }

    if (dynamicDataSourceForm && dynamicDataSourceForm.length > 0) {
      let errorText = ""
      dynamicDataSourceForm.map((item) => {
        console.log("🚀 ~ file: edit.js:1429 ~ dynamicDataSourceForm.map ~ item:", item)
          const { isRequire = false, hidden = '',fieldName } = item;
        	const isHidden = isHiddenMultilineIdText(hidden,baseConfig)
        	if (!isHidden) {
        	  let itemValue = JSON.parse(sessionStorage.getItem(`${fieldName}`))
        	  if (!(itemValue && itemValue.length > 0) && isRequire) {
        	    errorText = `${item.componentName}为必填项`
        	  }
        	}
      })


      if (errorText) {
        Message.error(errorText)
        return false
      }
    }

    if (selectionCollection) {
      let errorText = ""
      selectionCollection.map((item)=>{
        const isHidden = isHiddenMultilineIdText(item.hidden || '',baseConfig)
        let superMarket = JSON.parse(sessionStorage.getItem(`${item.selectionFields}`))
        isHidden && superMarket.map((cItem,index)=>{
          if(Object.keys(cItem).length >0){
            const {poolIds = [],shopList,supplyType = 1,itemCat = [],categoryData = [],} = cItem
            if (supplyType == 1 && poolIds.length <=0 && item.isNotRequired == false) {
              errorText = `${item.selectionName || ''}选品集ID必填`;
            }
            if (supplyType == 2 && itemCat.length <=0 && item.isNotRequired == false) {
              errorText = `${item.selectionName || ''}商品类目必填`;
            }
          }
        })
      })
      if (errorText) {
        Message.error(errorText)
        return false
      }
    }

    // 全能榜单判断
    if (scheduleTemplate.rankSchema) {
      let deliveryPosition = JSON.parse(sessionStorage.getItem('deliveryPosition')) || [];
      let rankInfo = JSON.parse(sessionStorage.getItem('rankInfo')) || [];
      if (deliveryPosition && deliveryPosition.length > 0) {
        const positionGroup = deliveryPosition.map((o) => o.location).filter(item => item && item.trim());
        if (positionGroup.length == 0) {
          Message.error('请填写投放位置')
          return false
        }
      } else {
        Message.error('请填写投放位置')
        return false
      }

      // if (rankInfo && rankInfo.length > 0) {
      //   const nameGroup = rankInfo.map((o) => o.name).filter(item => item && item.trim());
      //   if (nameGroup.length < rankInfo.length) {
      //     Message.error('请填写榜单总榜名称')
      //     return false
      //   }
      // }

    }

    return true;
  }

  validateVersionGroup = (group) => {
    let vGroup = group.split(",");
    let error = [];
    let reg1 = /^\d+\.\d+$/;
    let reg2 = /^\d+\.\d+\.\d+$/;
    if (vGroup.length > 0) {
      vGroup.map((v) => {
        if (!reg1.test(v) && !reg2.test(v)) {
          error.push(v);
        }
      })
    } else {
      if (!reg1.test(group) && !reg2.test(group)) {
        error.push(group);
      }
    }
    return error;
  }

  validatePoolIds = (group) =>{
    let error = [];
    if (group.length > 0) {
      group.map((v) => {
        console.log(v);
        if (v && (!(/^(\d+)(,\s*\d+)*$/).test(v) || v.split(',').length > 10)) {
          error.push(v);
        }
      })
    }
    return error;
  }

  render() {
    let { scheduleInfo, resourceId,pageId,resourceName, showAoi, resourceType, showDataConfig, showConfigGroup, showPutIn, showSupChannel,retailConfig, configId, scheduleTemplate, showSceneWords, showCategory, showSign, showSignEntry,showAddConfig,isCategoryCenter,poolTypeConfig,oversupply,showMainCategory,showMultilineIdText,dynamicDataSourceForm,multipleSchema,selectionCollection,showSingleCommodityDataSource,showSingleOversupply,rankSchema} = this.props;
    let {formData, retatedData, sceneWordsData, sceneWords, isSubmit, relatedSup, topImgUrl } = this.state;
    let showMarkingCard = (formData.sceneType && formData.sceneType == 1 && typeof oversupply == 'object') || (formData.sceneType && formData.sceneType == 0 && formData.supernatantTemplate == 0 && typeof oversupply == 'object') || (typeof oversupply == 'object' && oversupply.isAllowShow)
    let showOversupply = (formData.sceneType && formData.sceneType == 0 && formData.supernatantTemplate == 1 && typeof oversupply == 'object')
    // let showSchemaForm =  (configId && formData.configId) || !configId; //
    let showSchemaForm =  (configId && formData.configId) || !configId;
    // let schema = (scheduleTemplate) ? scheduleTemplate["detail"] : SchemaMap[`${resourceId}_${posId}`]["detail"];
    let schema = scheduleTemplate ? scheduleTemplate['detail'] : '';
    let outRequired = (scheduleTemplate && scheduleTemplate['outRequired']) ? scheduleTemplate['outRequired'] : [];
    const topImgUrlList = [{
      url:'https://img.alicdn.com/imgextra/i2/O1CN01DDUwN81x4DCOuiljl_!!6000000006389-2-tps-750-827.png',
      title:"红色1"
    },{
      url:'https://img.alicdn.com/imgextra/i4/O1CN01nA1zkM1hYPSdX8iwu_!!6000000004289-2-tps-750-827.png',
      title:"红色2"
    },{
      url:'https://img.alicdn.com/imgextra/i3/O1CN011jQ6Ou1bdYDDZz6JA_!!6000000003488-2-tps-750-827.png',
      title:"橙色"
    },{
      url:'https://img.alicdn.com/imgextra/i3/O1CN01SGEYPw1paFz2Mn7dR_!!6000000005376-2-tps-750-827.png',
      title:"黄色"
    },{
      url:'https://img.alicdn.com/imgextra/i4/O1CN01zJbJVw24JVjpZHXJ7_!!6000000007370-2-tps-750-827.png',
      title:"浅粉色"
    },{
      url:'https://img.alicdn.com/imgextra/i3/O1CN01epSDIA1UNKsU7Ch5c_!!6000000002505-2-tps-750-827.png',
      title:"粉色"
    },{
      url:'https://img.alicdn.com/imgextra/i2/O1CN01rr1FGs1owPWiNknS9_!!6000000005289-2-tps-750-827.png',
      title:"紫色"
    },{
      url:'https://img.alicdn.com/imgextra/i4/O1CN01YBZQIq1uSzmf41chH_!!6000000006037-2-tps-750-827.png',
      title:"蓝色"
    },{
      url:'https://img.alicdn.com/imgextra/i3/O1CN01smsEjP1Qexenqi8Uy_!!6000000002002-2-tps-750-827.png',
      title:"绿色"
    },{
      url:'https://img.alicdn.com/imgextra/i4/O1CN01IHgeap1RuMao4P5Im_!!6000000002171-2-tps-750-827.png',
      title:"金色"
    },];
    if (!topImgUrl && topImgResourceGroup.includes(resourceId)) {
      this.topImgUrlChange(topImgUrlList[0].url)
    }
    // 防止一开始进去sessionStorage中没有baseConfig
    let relatedSupIsShow = JSON.parse(sessionStorage.getItem('baseConfig')) != null && JSON.parse(sessionStorage.getItem('baseConfig')).acceptMode != null && JSON.parse(sessionStorage.getItem('baseConfig')).acceptMode == "1"

    // 是否是热卖：全能超市3.0 常买热卖资源位
    let showReSell = JSON.parse(sessionStorage.getItem('baseConfig')) != null && (!JSON.parse(sessionStorage.getItem('baseConfig')).resourceSubType || (JSON.parse(sessionStorage.getItem('baseConfig')).resourceSubType != null && JSON.parse(sessionStorage.getItem('baseConfig')).resourceSubType == "2"));
    let resourceIdPool = ['100090004','90004'];
    let showCommodityDataSource = (formData.materialType && formData.materialType == '2') && resourceIdPool.includes(resourceId);
    const rankData = [];

    try {
      debug("Step1.render key flags:", {
        showMarkingCard,
        showReSell,
        showCommodityDataSource,
        showOversupply,
        showSingleCommodityDataSource,
        showSingleOversupply,
        topImgResourceGroup,
        relatedSupIsShow,
        showSupChannel,
        showSceneWords,
        showMainCategory,
        showMultilineIdText,
        dynamicDataSourceForm,
        selectionCollection,
        rankSchema,
        showSign,
        showSignEntry,
        isCategoryCenter,
        showPutIn,
        showDataConfig,
        showConfigGroup,
        showAddConfig,
        showAoi,
        retailConfig,
        showCategory,
        multipleSchema,
      });
    } catch(e) {
      console.error(e)
    }

    return (
      <div style={{marginTop:'20px'}}>
        {Boolean(showSchemaForm && scheduleTemplate) && <>
          <SchemaForm ref='formRef' formData={formData} schema={schema} onChange={(formData)=>this.onFormChange(formData)} />
          {((!resourceIdPool.includes(resourceId) && showMarkingCard && showReSell) || showCommodityDataSource || showSingleCommodityDataSource) &&  <div className="newputin"><CommodityDataSource commodityIndex={0} oversupply={oversupply} resourceId={resourceId} formData={formData}></CommodityDataSource></div>}
          {/* {showOversupply && <MultipleSupplySources oversupply={oversupply} resourceId={resourceId}></MultipleSupplySources>} */}
          {(showOversupply || showSingleOversupply) && <MultipleSupplySourcesDragger oversupply={oversupply} resourceId={resourceId} formData={formData}></MultipleSupplySourcesDragger>}
          {/* 商超大促入口+banner融合 */}
          {topImgResourceGroup.includes(resourceId) && <Form.Item required  name='topImgUrl'  className='top-background' label="顶部背景图：" {...formItemLayoutChannel3}>
          <Radio.Group
              defaultValue={topImgUrlList[0].url}
              value={topImgUrl?topImgUrl:topImgUrlList[0].url}
              onChange={(e)=>{this.topImgUrlChange(e)}}
            >
              {topImgUrlList.map((item,index)=>{
                return (<div className='topImageItem'>
                  <div className={`topImageItem-img-border active-${item.url==topImgUrl||(index==0&&topImgUrl==='')}`} onClick={()=>{this.topImgUrlChange(item.url)}}>
                    <div className='topImageItem-img' style={{backgroundImage: `url(${item.url})`}}>
                      <Radio value={item.url}></Radio>
                    </div>
                  </div>
                  <div className='topImageItem-title'>
                    {item.title}
                  </div>
                </div>)
              })}
            </Radio.Group>
          </Form.Item>}
          {relatedSupIsShow && (resourceId=='100025023' || resourceId=='25023') && typeof(showSupChannel) != 'undefined' && showSupChannel && <Form.Item required={showSupChannel} validateState={(isSubmit && !relatedSup) ? 'error' : ''} help={(isSubmit && !relatedSup) ? '请选择要承接的业态金刚' : ''} className='related-channel' label="关联业态金刚排期：" {...formItemLayoutChannel}>
            <CascaderSelect showSearch hasClear style={{ width: '302px' }} value={this.state.relatedSup} changeOnSelect={true} dataSource={retatedData} onChange={this.handleChange} />
          </Form.Item>}
          {!(resourceId=='100025023' || resourceId=='25023') && (typeof(showSupChannel) != 'undefined' && showSupChannel) && <Form.Item required={outRequired.length>0 && outRequired.includes('relatedSup')} validateState={(isSubmit && !relatedSup) ? 'error' : ''} help={(isSubmit && !relatedSup) ? '请选择关联上级配置' : ''} className='related-channel' label="关联上级配置：" {...formItemLayoutChannel}>
            <CascaderSelect showSearch hasClear style={{ width: '302px' }} value={this.state.relatedSup} changeOnSelect={true} dataSource={retatedData} onChange={this.handleChange} />
          </Form.Item>}
          {(typeof(showSceneWords) != 'undefined' && showSceneWords) && <Form.Item required validateState={(isSubmit && !sceneWords) ? 'error' : ''} help={(isSubmit && !sceneWords) ? '请输入场景词' : ''} name='sceneWords'  className='related-channel' label="场景词：" {...formItemLayoutChannel}>
            <CascaderSelect showSearch hasClear style={{ width: '302px' }} value={sceneWords} changeOnSelect={true} dataSource={sceneWordsData} onChange={(value,data)=>this.handleSceneWordsChange(value,data)} />
          </Form.Item>}
          {showMainCategory && <ShopMainCategory showMainCategory={showMainCategory} />}
          {(showMultilineIdText && showReSell) && showMultilineIdText.map((item) => <MultilineIdText showMultilineIdText={item} formData={formData}/>)}
          {dynamicDataSourceForm && dynamicDataSourceForm.map((item) => <DynamicDataSourceForm formItemLayoutChannel={formItemLayoutChannel2 } isHiddenMultilineIdText={isHiddenMultilineIdText} dynamicDataSource={item} formData={formData}/>)}
          {selectionCollection.length > 0?selectionCollection.map((item)=>{
            return <div className="newputin" key={JSON.stringify(item)}><CommodityDataSource commodityIndex={0} resourceId={resourceId} formData={formData} {...item} ></CommodityDataSource></div>
          }):""}
          {rankSchema && <RankSet rankSchema={rankSchema} rankData={rankData} ref='rankRef' />}
          {(showSign) && <SignComps scheduleInfo={scheduleInfo} resourceId={resourceId}/>}
          {showSignEntry && <SignEntryComps scheduleInfo={scheduleInfo} />}
          {(resourceId=='100051001' || resourceId=='51001') && <AdvertiseLayer scheduleInfo={scheduleInfo}/>}
          {(isCategoryCenter) && <CategoryComps resourceType={resourceType} resourceId={resourceId} isCategoryCenter={isCategoryCenter}/>}
          {showPutIn && <CreatePutInPage resourceType={resourceType}/>}
          {/*{(showDataConfig && showAddConfig) && <NewPutInfo resourceId={resourceId} pageId={pageId} />}*/}
          {((showDataConfig || showConfigGroup) && showAddConfig) && <NewPutInfoPage resourceId={resourceId} pageId={pageId} resourceName={resourceName}/>}
          {showAoi && <AoiList />}
          {retailConfig && <RetailPutIn resourceType={resourceType}/>}
          {showCategory && <CategoryComps resourceType={resourceType} resourceId={resourceId}/>}
          {poolTypeConfig && <PutInfoShop poolTypeConfig={poolTypeConfig}/>}
          {(multipleSchema && showReSell) && <MultipleSchemaComp multipleSchema={multipleSchema} />}
        </>}
        <Row>
          <Col {...formItemLayout.labelCol} />
          <Col {...formItemLayout.wrapperCol}>
            {/*<Button>取消</Button>*/}
            &nbsp;
            {Boolean(showSchemaForm && scheduleTemplate) && <Button type="primary" disabled={this.props.submitLoading} onClick={this.submit}>
              {(showDataConfig || showConfigGroup) ? '下一步' : (!showQrCodeMap[this.state.pageUrl] ? '发布' : '保存') }
            </Button>}
          </Col>
        </Row>
      </div>
    )
  }
}

/**
 * 定坑设置Item
 */
class CommodityDataSource extends React.Component{
  constructor(props) {
    super(props);
    //tabName:"", //当前tab名称
    //supplyType:1,//1代表选品集，2代表商品类目
    //shopList:[],//数据来源
    //poolIds:"",//当前选中了哪些id
    //groupLabel:[],//选中了id的Label
    const nowSelectedField = (props.selectionFields || (props.oversupply && props.oversupply.tabKey)) || 'pureDataSourceList';
    const defaultCornerIconOptions = {
      format: "uri",
      type: "string",
      validate: {
        accept: "png,jpeg,jpg,apng,gif",
        maxSize: 500,
        width: 123,
        height: 113,
      }
    }
    this.state = {
      poolType: props.poolType? props.poolType : 1,//1是选品,2选店
      commodityCollection: JSON.parse(sessionStorage.getItem(`${nowSelectedField}`)) || [], //当前所有选品集的list
      commodityIndex: props.commodityIndex || 0 ,//当前修改的是第几个index
      categoryDataSource:[],
      oversupply:props.oversupply || {},
      resourceId:props.resourceId || "",
      formData:props.formData || {},
      selectionName:props.selectionName || "",
      selectionFields:nowSelectedField,
      maxCommodityLength: props.oversupply && props.oversupply.maxCommodityLength || props.maxCommodityLength,
      hidden: props.oversupply && props.oversupply.hidden || props.hidden,
      // 当组件隐藏时清空数据
      onHiddenClearData: props.oversupply && props.oversupply.onHiddenClearData || props.onHiddenClearData,
      isNotRequired : typeof(props.oversupply) != 'undefined' && typeof(props.oversupply.isNotRequired)!= 'undefined' ? props.oversupply.isNotRequired : props.isNotRequired,
      supplyTypeDataSourceEum:  props.supplyTypeDataSource || props.oversupply && props.oversupply.supplyTypeDataSource || supplyTypeDataSource,
      supplyOptions: props.oversupply && props.oversupply.supplyOptions || {},
      cornerIconOptions: props.oversupply && props.oversupply.cornerIconOptions || defaultCornerIconOptions,
      assembleItemPoolIdsHidden: (props.oversupply && props.oversupply && props.oversupply.assemblePools && props.oversupply.assemblePools.hidden)
    }
  }

  componentDidMount() {
    this.fetchSkuCategory()
    // 初始化
    this.initData()
  }

  componentWillReceiveProps(newProps){
    this.setState({
      formData:newProps.formData,
      commodityIndex:newProps.commodityIndex
    });
    this.initData()
  }

  initData = ()=>{
    const {commodityIndex,oversupply,selectionFields, formData, supplyTypeDataSourceEum, onHiddenClearData} = this.state;
    if (typeof commodityIndex == 'number') {
      let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${selectionFields}`)) || [])
      let testInit = newCommodityCollection[commodityIndex]

      // 切换卡类型 dataSource 中无选中项时，设置为当前存在的并且清空数据;
      if (!testInit || JSON.stringify(testInit) == '{}' || (testInit && supplyTypeDataSourceEum.filter((o) => !isHiddenMultilineIdText(o.hidden, formData)).filter(o => o.value === testInit.supplyType).length === 0)) {
        newCommodityCollection[commodityIndex] = {
          supplyType:oversupply.defaultSupplyType ? oversupply.defaultSupplyType:1,
          shopList:[],
          poolIds:[],
          investIdList: undefined,
          itemCat:[],
          categoryData:[],
          tabIndex:commodityIndex + 1,
          assembleItemPoolIds:[],
          assembleItemShopList:[],
          cornerIconUrl: oversupply.showCornerIcon ? '' : undefined,
        }
      }
      if (onHiddenClearData && !isHiddenMultilineIdText(this.state.hidden, this.props.formData) ){
        newCommodityCollection = [];
      }
      this.setState({commodityCollection:newCommodityCollection})
      sessionStorage.setItem(`${selectionFields}`,JSON.stringify(newCommodityCollection))
    }
  }
  /*获取商品分类数据*/
  fetchSkuCategory = async () => {
    try {
      let request = apiL.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map((dataItem) => {
        dataItem.value = dataItem.value.toString();
        dataItem.children &&
          dataItem.children.map((subItem) => {
            subItem.value = subItem.value.toString();
            subItem.children &&
              subItem.children.map((thirdItem) => {
                thirdItem.value = thirdItem.value.toString();
              });
          });
        return dataItem;
      });
      this.setState({categoryDataSource:dataSource})
    } catch (error) {
      api.onRequestError(error);
    }
  };

  checkDataSources = (rule, value, callback) => {
    if (value.length > this.state.maxCommodityLength) {
      callback(`${this.state.selectionName}选品集ID输入最多${this.state.maxCommodityLength}个`);
    } else {
      callback();
    }
  };

  onPoolChange = (value, actionType, commodityIndex,fieldKey, fieldDisplayKey) => {
    if (value.length > this.state.maxCommodityLength) {
      Message.warning(`${this.state.selectionName}选品集ID输入最多${this.state.maxCommodityLength}个`);
    }else{
      if (actionType == "itemClick" || actionType == "tag") {
        let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
        newCommodityCollection[commodityIndex][fieldKey] = value;
        const ext_pool_set = newCommodityCollection[commodityIndex][fieldDisplayKey]
        let newShopList = []
        if ( ext_pool_set && ext_pool_set.length > 0) {
          ext_pool_set.map((item)=>{
            if (value.includes(item.value)) {
              newShopList.push(item)
            }
          })
        }
        newCommodityCollection[commodityIndex][fieldDisplayKey] = Array.from(new Set(newShopList))
        this.setState({commodityCollection:newCommodityCollection})
        sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(newCommodityCollection))
      }
    }
  };

  onCategoryChange = (value, data, commodityIndex) => {
    const haveSelected = data.map(item => {
      const {value, label, pos} = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
    newCommodityCollection[commodityIndex].itemCat = haveSelected;
    this.setState({commodityCollection:newCommodityCollection})
    sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(newCommodityCollection))
  };

  textArrayChange = (value, commodityIndex, key) => {
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
    newCommodityCollection[commodityIndex][key] = value;
    this.setState({commodityCollection: newCommodityCollection})
    sessionStorage.setItem(`${this.state.selectionFields}`, JSON.stringify(newCommodityCollection))
  }

  onSearch = (keyword,commodityIndex, fieldKey, fieldDisplayKey) => {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if (keyword) {
        getPoolInfoSurge({ searchKey: keyword,poolType:this.state.poolType}).then((data) => {
          const dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
            newPlatformFlag:item.newPlatformFlag
          }));

          let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
          newCommodityCollection[commodityIndex][fieldDisplayKey] = [...newCommodityCollection[commodityIndex][fieldDisplayKey],...dataSource]
          console.log("🚀 ~ file: edit.js:1841 ~ CommodityDataSource ~ getPoolInfoSurge ~ newCommodityCollection:", newCommodityCollection)
          this.setState({ commodityCollection: newCommodityCollection });
          sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(newCommodityCollection))
        });
      } else {
        let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
        newCommodityCollection[commodityIndex][fieldDisplayKey] = [...newCommodityCollection[commodityIndex][fieldDisplayKey]]
        this.setState({ commodityCollection: newCommodityCollection });
        sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(newCommodityCollection))
      }
    }, 800);
  };

  // 供给来源改变
  supplyTypeChange = (value,commodityIndex) => {
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${this.state.selectionFields}`)) || [])
    newCommodityCollection[commodityIndex].supplyType = value
    if (value == 1) {
      newCommodityCollection[commodityIndex].itemCat = []
    }else{
      newCommodityCollection[commodityIndex].poolIds = []
    }
    this.setState({commodityCollection:newCommodityCollection})
    sessionStorage.setItem(`${this.state.selectionFields}`,JSON.stringify(newCommodityCollection))
  }

  render() {
    let {commodityCollection = [], commodityIndex = 0, categoryDataSource = [], oversupply = {},resourceId = "",formData = {},maxCommodityLength,selectionName,hidden = '', isNotRequired = false, supplyTypeDataSourceEum,assembleItemPoolIdsHidden} = this.state;
    console.log("🚀 ~ file: edit.js:1871 ~ CommodityDataSource ~ render ~ formData:", formData)
    let {isNotNull, showCornerIcon} = oversupply; // isNotRequired 兼容之前的情况，默认必填 (isRequired 弃用)
    const isHidden = isHiddenMultilineIdText(hidden,this.props.formData)
    const isNotNullNow = isNotNull || ['100090002','90002'].includes(resourceId) && formData.sceneType == 1
    let {supplyType = 1,shopList = [],poolIds = [], itemCat = [],marketActivityIds=[],investIdList=[],assembleItemPoolIds = [],assembleItemShopList = [], cornerIconUrl} = commodityCollection[commodityIndex] || {};
    let supplyOptions = {}
    if (supplyTypeDataSourceEum.filter((o) => !isHiddenMultilineIdText(o.hidden, this.props.formData)).filter((o) => o.value == supplyType).length > 0) {
      supplyOptions = supplyTypeDataSourceEum.filter((o) => !isHiddenMultilineIdText(o.hidden, this.props.formData)).filter((o) => o.value == supplyType)[0].options
    }

    return isHidden && <div key={commodityIndex}>
      <Form.Item
        {...formItemLayoutChannel}
        label={`${selectionName? selectionName+'-':""}${isNotNullNow?"判空":""}供给来源:`}
        requiredMessage="供给来源不能为空"
        required={!isNotRequired}
        hasFeedback
      >
        <Select
          value={supplyType}
          name="supplyType"
          style={{ width: "100%" }}
          dataSource={supplyTypeDataSourceEum.filter((o) => !isHiddenMultilineIdText(o.hidden, this.props.formData))}
          onChange={value => this.supplyTypeChange(value,commodityIndex)}
        />
      </Form.Item>
      {supplyType == 1 && <>
        <Form.Item
          {...formItemLayoutChannel}
          label={`${selectionName? selectionName+'-':""}${isNotNullNow?"判空":""}选品集ID:`}
          requiredMessage="选品集ID不能为空"
          required={!isNotRequired}
          hasFeedback
        >
          <Select
            mode="multiple"
            showSearch
            placeholder={`请输入选品集ID${maxCommodityLength?'，支持输入最多'+maxCommodityLength+'个':''}`}
            name="poolIds"
            value={poolIds}
            onChange={(value, actionType) => this.onPoolChange(value, actionType,commodityIndex,"poolIds","shopList")}
            onSearch={key => this.onSearch(key,commodityIndex,"poolIds","shopList")}
            dataSource={shopList}
            style={{ width: "100%" }}
          />
          <a href={selectionCommodityUrl[window.configEnv]} target="_blank">去创建</a>
        </Form.Item>
      </>
      }
      {supplyType == 2 && <Form.Item
        {...formItemLayoutChannel}
        label={`${selectionName? selectionName+'-':""}${isNotNull?"判空":""}商品类目:`}
        requiredMessage={`${selectionName? selectionName+'-':""}商品类目不能为空`}
        required={!isNotRequired}
        hasFeedback
      >
        <CascaderSelect
          showSearch
          multiple={true}
          expandTriggerType={"hover"}
          placeholder="请选择商品类目"
          name="category"
          value={itemCat.map(i =>{return i.value})}
          onChange={(value,data) => this.onCategoryChange(value,data,commodityIndex)}
          dataSource={categoryDataSource}
          style={{ width: "100%" }}
        />
      </Form.Item>}
      {supplyType == 3 && <Form.Item
        {...formItemLayoutChannel}
        label={`营销活动ID:`}
        requiredMessage={`营销活动ID不能为空`}
        required={!isNotRequired}
        hasFeedback
      >
        <TextareaArrayWidget value={marketActivityIds} schema={supplyOptions}  onChange={(value) => this.textArrayChange(value, commodityIndex,'marketActivityIds')} />
      </Form.Item>}
      {supplyType == 4 && <Form.Item
        {...formItemLayoutChannel}
        label={`招商活动ID:`}
        requiredMessage={`招商活动ID不能为空`}
        required={!isNotRequired}
        hasFeedback
      >
        <TextareaArrayWidget value={investIdList}  schema={supplyOptions} onChange={(value)=>this.textArrayChange(value, commodityIndex,'investIdList')}/>
      </Form.Item>}
      { !assembleItemPoolIdsHidden && formData.sameStore == '0' && <Form.Item
        {...formItemLayoutChannel}
        label={`店铺选品集ID:`}
        requiredMessage="置顶选品集ID不能为空"
        required={!isNotRequired}
        hasFeedback
      >
        <Select
          mode="multiple"
          showSearch
          placeholder={`请输入选品集ID${maxCommodityLength?'，支持输入最多'+maxCommodityLength+'个':''}`}
          name="assembleItemPoolIds"
          value={assembleItemPoolIds}
          onChange={(value, actionType) => this.onPoolChange(value, actionType, commodityIndex, "assembleItemPoolIds", "assembleItemShopList")}
          onSearch={key => this.onSearch(key,commodityIndex, "assembleItemPoolIds", "assembleItemShopList")}
          dataSource={assembleItemShopList}
          style={{ width: "100%" }}
        />
        <a href={selectionCommodityUrl[window.configEnv]} target="_blank">去创建</a>

      </Form.Item>}

      {showCornerIcon && <Form.Item
        {...formItemLayoutChannel}
        label={`角标:`}
        requiredMessage={`角标不能为空`}
        required={!isNotRequired}
        hasFeedback
      >
        <ImgUploadWidget options={this.state.cornerIconOptions} value={cornerIconUrl} schema={supplyOptions || {}} onChange={(info)=>{
          this.textArrayChange(info, commodityIndex, 'cornerIconUrl')
        }}/>
      </Form.Item>}
    </div>
  }
}
function MultipleSupplySourcesDragger(props){
  return <DndProvider backend={HTML5Backend}>
    <MultipleSupplySources {...props}></MultipleSupplySources>
  </DndProvider>
}

function MultipleSupplySources(props){
  const { maxTabLength = 1,nameSetting = "",name = "", field = "", initTabLength = 1, tabKey } = props.oversupply;
  const newSelectionField = tabKey ? `${tabKey}` : 'pureDataSourceList';
  const [commodityCollection,setCommodityCollection] = useState(JSON.parse(sessionStorage.getItem(`${newSelectionField}`)) || [])
  let requestedFrame;
  const rootRef = useRef(null);

  /**
   * 根据initTabLength初始化个数多tab
   * */
  const initMuiltipleData = () => {
    if (initTabLength > 1 & commodityCollection.length <= 0) { //只有在初始化时候需要
      let _tabs = [];
      for (var i = 1; i <= initTabLength; i++) {
        _tabs.push({});
      }
      setCommodityCollection(_tabs);
    }
  }

  useEffect(()=>{
    initMuiltipleData();
    return ()=>{
      if (requestedFrame !== undefined) {
        cancelAnimationFrame(requestedFrame)
      }
    }
  },[])

  // 卡片上移
  const cardMoveUp = (index)=>{
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${newSelectionField}`)) || [])
    let newCommodityItem = newCommodityCollection[index - 1];
    newCommodityCollection[index - 1] = newCommodityCollection[index]
    newCommodityCollection[index] = newCommodityItem
    setCommodityCollection(newCommodityCollection)
    sessionStorage.setItem(`${newSelectionField}`,JSON.stringify(newCommodityCollection))
  }

  // 卡片下移
  const cardMoveDown = (index)=>{
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${newSelectionField}`)) || [])
    let newCommodityItem = newCommodityCollection[index + 1];
    newCommodityCollection[index + 1] = newCommodityCollection[index]
    newCommodityCollection[index] = newCommodityItem
    setCommodityCollection(newCommodityCollection)
    sessionStorage.setItem(`${newSelectionField}`,JSON.stringify(newCommodityCollection))
  }

  // 卡片删除
  const cardDelete = (index)=>{
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${newSelectionField}`)) || [])
    delete newCommodityCollection[index]
    newCommodityCollection = newCommodityCollection.filter(i => i)
    setCommodityCollection(newCommodityCollection)
    sessionStorage.setItem(`${newSelectionField}`,JSON.stringify(newCommodityCollection))
  }

  // 卡片新增
  const cardAdd = ()=>{
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${newSelectionField}`)) || [])
    if (maxTabLength && newCommodityCollection.length == maxTabLength) {
      Message.error(`最多设置${maxTabLength}个`)
    }else{
      newCommodityCollection.push({})
      setCommodityCollection(newCommodityCollection)
      sessionStorage.setItem(`${newSelectionField}`,JSON.stringify(newCommodityCollection))
    }
  }

  // 自定义表单onchange
  const inputOnChange = (key,value,index) => {
    let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${newSelectionField}`)) || [])
    newCommodityCollection[index][key] = value
    setCommodityCollection(newCommodityCollection)
    sessionStorage.setItem(`${newSelectionField}`,JSON.stringify(newCommodityCollection))
  }

  /**
   * 拖动卡片
   * @param {当前卡片id} id
   * @param {移动到卡片id} moveId
   */
  const moveCard = (id, moveId) => {
    if (!requestedFrame && id !=moveId && moveId != -1) {
      requestedFrame = requestAnimationFrame(()=>{
        let newCommodityCollection = deepCopy(JSON.parse(sessionStorage.getItem(`${newSelectionField}`)) || [])
        let exchangeMap;
        exchangeMap = newCommodityCollection[moveId]
        newCommodityCollection[moveId] = newCommodityCollection[id]
        newCommodityCollection[id] = exchangeMap
        if (id > moveId) {
          for (let index = moveId + 1; index <= id; index++) {
            let exchangeMapTemporary;
            exchangeMapTemporary = newCommodityCollection[index]
            newCommodityCollection[index] = exchangeMap
            exchangeMap = exchangeMapTemporary
          }
        }else{
          for (let index = moveId -1 ; index >= id; index--) {
            let exchangeMapTemporary;
            exchangeMapTemporary = newCommodityCollection[index]
            newCommodityCollection[index] = exchangeMap
            exchangeMap = exchangeMapTemporary
          }
        }
        setCommodityCollection(newCommodityCollection)
        sessionStorage.setItem(`${newSelectionField}`,JSON.stringify(newCommodityCollection))
        requestedFrame = undefined
      })
    }
  }

  const [{isOver}, drop] = useDrop({
      accept:'card',
      drop:({id}) => {
        return {id}
      },
      hover:(props,monitor)=>{
        const {id, ref, ref2, commodityLength} = props;
        deleteClassListClassName()
        document.getElementsByClassName('right-part')[0].style.overflowY = 'hidden'
        let buttonRef = ref.current.getBoundingClientRect()
        let cardRef = ref2.current.getBoundingClientRect()
        let translation = monitor.getDifferenceFromInitialOffset()
        const buttonToCard = buttonRef.y - cardRef.y
        let translationTop = 0
        let element = document.getElementsByClassName("tabCard")
        if(translation.y < 0) {
          translationTop = tabMapList[id].start + ( buttonToCard + translation.y)
          tabMapList.map((item,index)=>{
            if (index != id) {
              if (index == 0) {
                if (translationTop < item.middle) {
                  element[index].classList.add('drop-over-upward')
                  moverId = 0
                }else if(translationTop >= item.middle && translationTop < item.end){
                  element[index].classList.add('drop-over-downward')
                  moverId = index + 1
                }
              }else{
                if (translationTop < item.middle && translationTop >= tabMapList[index - 1].end) {
                  element[index].classList.add('drop-over-upward')
                  moverId = index
                }else if(translationTop >= item.middle && translationTop < item.end){
                  element[index].classList.add('drop-over-downward')
                  moverId = index + 1
                }
              }
            }
          })
        }else{
          translationTop = tabMapList[id].start + translation.y + buttonToCard
          tabMapList.map((item,index)=>{
            if (index != id) {
              if (index == commodityLength - 1) {
                if (translationTop >= item.middle) {
                  element[index].classList.add('drop-over-downward')
                  moverId = commodityLength - 1
                }else if(translationTop < item.middle && translationTop > item.start){
                  element[index].classList.add('drop-over-upward')
                  moverId = index - 1
                }
              }else{
                if (translationTop >= item.middle && translationTop < tabMapList[index + 1].start) {
                  element[index].classList.add('drop-over-downward')
                  moverId = index
                }else if(translationTop < item.middle && translationTop > item.start){
                  element[index].classList.add('drop-over-upward')
                  moverId = index - 1
                }
              }
            }
          })
        }
      },
      collect: monitor => ({
        isOver: !!monitor.isOver(),
      }),
  })
  drop(rootRef)

  return <div ref={rootRef} className='multiple-tab-father'>
    <Row className='multiple-tab'>
      <Col span={6} style={{width:'200px',maxWidth: '200px'}}>
        <div className='multiple-tab-title'>{nameSetting}</div>
      </Col>
      <Col span={18}>
        <div className='multiple-tab-context-dragger'>
          {commodityCollection.map((item,index)=>{
            return <MultipleSupplyCardItem
              id={index}
              commodityLength={commodityCollection.length}
              item={item}
              index={index}
              oversupply={props.oversupply}
              resourceId={props.resourceId}
              cardMoveUp={cardMoveUp}
              cardMoveDown={cardMoveDown}
              cardDelete={cardDelete}
              inputOnChange={inputOnChange}
              moveCard={moveCard}
              formData={props.formData}
            ></MultipleSupplyCardItem>
          })}
        </div>
        <Button text onClick={()=>cardAdd()}>新增</Button>
      </Col>
    </Row>
  </div>
}
function deleteClassListClassName(){
  let tabCardList = document.getElementsByClassName("tabCard")
  let map;
  for (let index = 0; index < tabCardList.length; index++) {
    map = {}
    tabCardList[index].classList.remove('drop-over-downward')
    tabCardList[index].classList.remove('drop-over-upward')
    map.height = tabCardList[index].clientHeight
    if (index == 0) {
      map.start = 0
      map.middle = tabCardList[index].clientHeight / 2
      map.end = tabCardList[index].clientHeight + 20
      tabMapList[index] = map
    }else{
      map.start = tabMapList[index - 1].end;
      map.middle = map.start + tabCardList[index].clientHeight / 2
      map.end = map.start + tabCardList[index].clientHeight + 20
      tabMapList[index] = map
    }
  }
}
function MultipleSupplyCardItem(props){
  const {id, item, index, commodityLength, cardMoveUp, cardMoveDown, cardDelete, inputOnChange, oversupply, resourceId, moveCard} = props
  const { name = "", field = "",fieldRequired , maxFieldLength, hideTabName = false, fieldType = 'string', defaultFieldValue} = oversupply;
  const ref = useRef(null)
  const ref2 = useRef(null)
  useEffect(() => {
    if (!item[field] && fieldType === 'number') {
      inputOnChange(field,defaultFieldValue,index)
    }
  }, []);
  const [{ opacity, isDragging }, connectDrag, preview] = useDrag({
    item:{ type: "card", id, ref, ref2, commodityLength},
    collect: (monitor) => ({
      opacity: monitor.isDragging() ? 0.4 : 1,
      isDragging: !!monitor.isDragging(),
    }),
    end:(props, monitor) => {
      // 批量去除类名
      deleteClassListClassName()
      document.getElementsByClassName('right-part')[0].style.overflowY = 'scroll'
      moveCard(id,moverId)
    }
  })
  connectDrag(ref)
  preview(ref2)

  return <div ref={ref2} style={{width: 580,opacity:opacity}} id={`tabCard-${index}`} className={`tabCard`}>
    <Card free style={{ width: '100%' }} key={index}>
      <Card.Header
        title={<i className="order-num">{index + 1}</i>}
        extra={<>
          {index != 0 && <Button text onClick={()=>cardMoveUp(index)}>上移</Button>}
          {index != commodityLength -1 && <Button text onClick={()=>cardMoveDown(index)}>下移</Button>}
          {<Button text onClick={()=>cardDelete(index)}>删除</Button>}
        </>}
      />
      <Card.Content>
      <div className='dragger' ref={ref}></div>
      {(!hideTabName && field)&&<Form.Item
          {...formItemLayoutChannel}
          label={name+":"}
          requiredMessage={name+"不能为空"}
          required={typeof fieldRequired == 'undefined' ? true : fieldRequired}
          hasFeedback
        >
          {fieldType === 'number'?<NumberPicker name={field} value={item[field]} onChange={(e)=>inputOnChange(field,e,index)} min={1} max={maxFieldLength?maxFieldLength:6}></NumberPicker>:<Input name={field} value={item[field]} onChange={(e)=>inputOnChange(field,e,index)} placeholder={"请输入"+name} maxLength={maxFieldLength?maxFieldLength:6}></Input>}
        </Form.Item>}
        <CommodityDataSource commodityIndex={index} oversupply={oversupply} resourceId={resourceId} formData={props.formData}></CommodityDataSource>
      </Card.Content>
    </Card>
  </div>

}

/**
* schemeForm多tab
 * */
function MultipleSchemaComp(props){
  const { multipleSchema = {} } = props;
  const [refresh, setRefresh] = useState(false);
  let _initData = {};
  let max = 3;
  if (JSON.stringify(multipleSchema) != "{}") {
    _initData = multipleSchema.initData;
    max = multipleSchema.max;
  }

  let _multipleSchemaData = [];
  if(!sessionStorage.getItem('multipleSchemaData')){
    _multipleSchemaData = [deepCopy(_initData)];
    sessionStorage.setItem("multipleSchemaData",JSON.stringify(_multipleSchemaData));
    // setMultipleSchemaData(deepCopy(_initData));
  }else{
    _multipleSchemaData = JSON.parse(sessionStorage.getItem('multipleSchemaData'));
    // setMultipleSchemaData(deepCopy(JSON.parse(sessionStorage.getItem('multipleSchemaData'))));
  }
  const [multipleSchemaData, setMultipleSchemaData] = useState(_multipleSchemaData);

  useEffect(() => {
    refresh && setTimeout(() => setRefresh(false))
  }, [refresh])

  const onFormItemChange = (index, itemData) => {
    let _multipleSchemaData = deepCopy(JSON.parse(sessionStorage.getItem('multipleSchemaData')) || []);
    for(var o in itemData){
      _multipleSchemaData[index][o] = itemData[o];
    }
    // console.log('itemData', itemData, _multipleSchemaData);
    setMultipleSchemaData(_multipleSchemaData);
    sessionStorage.setItem("multipleSchemaData",JSON.stringify(_multipleSchemaData));
  }

  const addItem = () =>{
    let _item = deepCopy(_initData);
    let _multipleSchemaData = deepCopy(JSON.parse(sessionStorage.getItem('multipleSchemaData')) || [])
    if (multipleSchemaData.length < 1 + max) {
      _multipleSchemaData.push(_item);
      sessionStorage.setItem("multipleSchemaData",JSON.stringify(_multipleSchemaData));
      setMultipleSchemaData(_multipleSchemaData);
    } else {
      Message.warning(`最多配置${max}个`);
    }
  }

  const cardDelete = (index) =>{
    let multipleSchemaData = deepCopy(JSON.parse(sessionStorage.getItem('multipleSchemaData')) || [])
    if(multipleSchemaData.length>1) {
      multipleSchemaData.splice(index,1);
      setMultipleSchemaData(multipleSchemaData);
      sessionStorage.setItem('multipleSchemaData', JSON.stringify(multipleSchemaData));
      setRefresh(true);
    }else{
      Message.warning("最少1个");
    }
  }

  // const onUploadChange = (index,info) =>{
  //   console.log(info);
  //   // this.saveBaseConfig("brandBaPingUrl", info);
  //   // this.setState({brandBaPingUrl: info}, () => {
  //   //   console.log(this.state)
  //   // });
  //   let _multipleSchemaData = deepCopy(JSON.parse(sessionStorage.getItem('multipleSchemaData')) || []);
  //   _multipleSchemaData[index]['logoImgUrl'] = info;
  //   // console.log('itemData', itemData, _multipleSchemaData);
  //   setMultipleSchemaData(_multipleSchemaData);
  //   sessionStorage.setItem("multipleSchemaData",JSON.stringify(_multipleSchemaData));
  // }
  //
  // const onInputChange = (index,value) => {
  //   let _multipleSchemaData = deepCopy(JSON.parse(sessionStorage.getItem('multipleSchemaData')) || []);
  //   _multipleSchemaData[index]['word'] = value;
  //   // console.log('itemData', itemData, _multipleSchemaData);
  //   setMultipleSchemaData(_multipleSchemaData);
  //   sessionStorage.setItem("multipleSchemaData",JSON.stringify(_multipleSchemaData));
  // }

  // let options = {
  //   format: "uri",
  //   type: "string",
  //   validate: {
  //     accept: "png,jpeg,jpg,apng,gif",
  //     maxSize: 500,
  //     width: 750
  //   }
  // }

  console.log('multipleSchemaData',multipleSchemaData);
  console.log(JSON.parse(sessionStorage.getItem('multipleSchemaData')));
  return <Row className={'multiple-schema'}>
    <Col span={6} style={{width:'200px',maxWidth: '200px'}}>
      <div className='multiple-schema-title'>{multipleSchema.titleName || ''}：</div>
    </Col>
    <Col span={18}>
      {multipleSchemaData.map((item,index) => {
          return <div className={`multiple-schema-item`}>
            <div>
              {/*{index != 0 && <Button text onClick={() => cardMoveUpOrDown(index, 1)}>上移</Button>}*/}
              {/*{index != multipleSchemaData.length - 1 &&*/}
              {/*<Button text onClick={() => cardMoveUpOrDown(index, 2)}>下移</Button>}*/}
              {multipleSchemaData.length != 1 && <Button type={'primary'} className={'btn-del'} text onClick={() => cardDelete(index)}>删除</Button>}
            </div>
            {/*<ImgUploadWidget options={options} value={v.logoImgUrl} schema={schema} onChange={(info)=>onUploadChange(index,info)}/>*/}
            {/*<Input value={v.word} onChange={(value)=>onInputChange(index,value)}/>*/}
            {/*<span className='multiple-schema-index'>{1 + index}</span>*/}
            {(JSON.stringify(item) != "{}" && Boolean(multipleSchema) && !refresh) && <SchemaForm className={'alsc-form-tab'} {...formItemLayout} formData={item} schema={multipleSchema}
                        onChange={(itemData) => onFormItemChange(index, itemData)}/>}
          </div>
      })}
      <Button text type={'primary'} onClick={()=>addItem()}>新增</Button>
    </Col>
  </Row>
}


// 下面这个注释用来快速折叠代码
//#region ResourceEditExtra

class PutInfoShop extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      poolTypeConfig: props.poolTypeConfig,
      shopList:[],
      putPoolIds:[]
    }
  }

  componentDidMount() {
    // this.getCity();
  }

  componentWillReceiveProps(newProps){
    this.setPutPoolIds();
  }

  setPutPoolIds = () =>{
    let ext_pool_ids = sessionStorage.getItem("ext_pool_ids") || '';
    let ext_pool_group_label = sessionStorage.getItem("ext_pool_group_label") || "";
    let newPoolIdsArray = ext_pool_ids.split(",") || [];
    let newPoolIds = [];
    if (ext_pool_ids != '') {
      newPoolIdsArray.map((v) => {
        console.log(typeof parseInt(v));
        newPoolIds.push(parseInt(v));
      })
    }
    this.setState({
      putPoolIds: newPoolIds,
      shopList: ext_pool_group_label ? JSON.parse(ext_pool_group_label) : []
    });
  }

  checkDataSources = (rule, value, callback) => {
    if (value.length > 5) {
      callback("选品集ID输入最多5个");
    } else {
      callback();
    }
  };

  onPoolChange = (value, actionType, item) => {
    let {shopList} = this.state;
    if (value.length > 5) {
      Message.warning("选品集ID输入最多5个");
    }else{
      if (actionType == "itemClick" || actionType == "tag") {
        sessionStorage.setItem("ext_pool_ids", value.join(","));
        let ext_pool_set = sessionStorage.getItem("ext_pool_set")?JSON.parse(sessionStorage.getItem("ext_pool_set")):[];
        let ext_pool_group_label = [];
        if (ext_pool_set && ext_pool_set.length > 0) {
          ext_pool_set.map((v) => {
            if (value.includes(v.value)) {
              ext_pool_group_label.push(v);
            }
          })
        }
        // this.setLocalPoolIdsSet(ext_pool_group_label, "ext_pool_group_label");
        sessionStorage.setItem("ext_pool_group_label", JSON.stringify(ext_pool_group_label));
        this.setPutPoolIds();
      }
    }
  };

  removeDuplicate = (arr) => {
    var hash = {};
    let result = arr.reduce(function (item, next) {
      hash[next.value] ? "" : (hash[next.value] = true && item.push(next));
      return item;
    }, []);
    return result;
  };

  setLocalPoolIdsSet = (dataSource,key) => {
    const LocalPoolIdsSet = sessionStorage.getItem(key)
      ? JSON.parse(sessionStorage.getItem(key))
      : [];
    let wholePoolIdsSet = LocalPoolIdsSet.concat(dataSource);
    const newWholePoolIdsSet = this.removeDuplicate(wholePoolIdsSet);
    sessionStorage.setItem(key, JSON.stringify(newWholePoolIdsSet));
  };

  onSearch = (keyword) => {
    let {poolTypeConfig} = this.state;
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if (keyword) {
        getPoolInfoSurge({ searchKey: keyword,poolType:poolTypeConfig}).then((data) => {
          const dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
            newPlatformFlag:item.newPlatformFlag
          }));
          this.setState({ shopList: dataSource },()=>{
            this.setLocalPoolIdsSet(dataSource,'ext_pool_set');
          });
        });
      } else {
        this.setState({ shopList: [] });
        sessionStorage.setItem("ext_pool_set", JSON.stringify([]));
      }
    }, 800);
  };

  render() {
    let {shopList, poolTypeConfig, putPoolIds} = this.state;
    return (
      <Form.Item
        {...formItemLayoutChannel}
        label={`投放${poolTypeConfig == 1 ? '商品' : '门店'}：`}
        requiredMessage={`选${poolTypeConfig == 1 ? '品' : '店'}集ID不能为空`}
        validator={this.checkDataSources}
        hasFeedback
        className='newputin'
      >
        <Select
          mode="multiple"
          showSearch
          placeholder={`请输入选${poolTypeConfig == 1 ? '品' : '店'}集ID，支持输入最多5个`}
          name="poolIds"
          value={putPoolIds ? putPoolIds : []}
          onChange={(value, actionType) => this.onPoolChange(value, actionType)}
          onSearch={this.onSearch}
          dataSource={shopList}
          style={{ width: "100%" }}
        />
      </Form.Item>
    )
  }
}

class RetailPutIn extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      cityDataSource:[],
      dataConfig: (sessionStorage.getItem("putInInfo") && JSON.parse(sessionStorage.getItem("putInInfo")).dataResourceDTOList) ? JSON.parse(sessionStorage.getItem("putInInfo")) : {
        cityCode: [],
        citys:[],
        dataResourceDTOList:[{
          dataResourceList: '',
          poolIdType: '1',
          dataResourceType: "1"
        },{
          dataResourceList: '',
          poolIdType: '',
          dataResourceType: "2"
        }],
      }
    }
  }

  componentDidMount() {
    this.getCity();
  }

  componentWillReceiveProps(newProps){
    this.setState({
      dataConfig: (sessionStorage.getItem("putInInfo") && JSON.parse(sessionStorage.getItem("putInInfo")).dataResourceDTOList) ? JSON.parse(sessionStorage.getItem("putInInfo")) : {
        cityCode: [],
        citys:[],
        dataResourceDTOList:[{
          dataResourceList: '',
          poolIdType: '1',
          dataResourceType: "1"
        },{
          dataResourceList: '',
          poolIdType: '',
          dataResourceType: "2"
        }],
      }
    })
  }

  getCity = async () => {
    try {
      const res = await cityInfoDistrictCode();
      this.setState({
        cityDataSource:res
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  handleChange = (value, data, extra) => {
    let {dataConfig} = this.state;
    dataConfig.citys = [];
    let newCitys = [];
    data.map((v) => {
      dataConfig.citys.push({
        value: v.value,
        label: v.label,
        level: (v.pos.split("-").length - 1)
      })
      console.log(v);
      if(v.children){
        let newArr = v.children.map(o=>o.value);
        newCitys = newCitys.concat(newArr);
      }else{
        newCitys = newCitys.concat([v.value]);
      }
    });
    dataConfig.cityCode = newCitys;
    // dataConfig.citys = value;
    this.setState({dataConfig},()=>{
      console.log(this.state.dataConfig);
      sessionStorage.setItem("putInInfo",JSON.stringify(this.state.dataConfig));
    });
  }

  changePoolIdOrType = (value, dataResourceType,key) => {
    let {dataConfig} = this.state;
    let index = dataConfig.dataResourceDTOList.findIndex((value) => {
      return value.dataResourceType == dataResourceType;
    })
    dataConfig.dataResourceDTOList[index][key] = value;
    this.setState({dataConfig},()=>{
      console.log(this.state.dataConfig);
      sessionStorage.setItem("putInInfo",JSON.stringify(this.state.dataConfig));
    })
  }



  render() {
    let {cityDataSource,dataConfig} = this.state;
    let {cityCode,citys,dataResourceDTOList} = dataConfig;
    let selectedCitysValue = (citys && citys.length > 0) ? citys.map(v => v.value) : [];
    return (
      <div>
        <Form.Item label="投放城市：" {...formItemLayoutChannel}>
          <CascaderSelect style={{ width: '302px' }} value={selectedCitysValue} multiple dataSource={cityDataSource} onChange={this.handleChange} />
        </Form.Item>
        <Form.Item label="数据源：" {...formItemLayoutChannel}>
          {(dataResourceDTOList && dataResourceDTOList.length>0) && dataResourceDTOList.map((v)=>{
            return <div style={{marginBottom:'10px'}}>
              <label>{resourceTypeMap[v.dataResourceType]}</label>
              {v.dataResourceType == '1' && <span style={{marginLeft:'10px'}}>选品类型： <Select dataSource={poolTypeSet} value={v.poolIdType} onChange={(value) => this.changePoolIdOrType(value, v.dataResourceType,'poolIdType')}/></span>}
              <span style={{marginLeft:'10px'}}><span style={{color:'red'}}>*</span>选品ID： <Input value={v.dataResourceList} placeholder='必填,英文逗号隔开,最多10个' onChange={(value) => this.changePoolIdOrType(value, v.dataResourceType,'dataResourceList')}/></span></div>
          })}
        </Form.Item>
      </div>
    )
  }
}

class CategoryComps extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      resourceId:props.resourceId,
      skipUrl: centerCreateUrl[window.configEnv],
      linkAlgorithmPolicyOptions: [],
      resourceType: props.resourceType,
      categoryCascadeOptions:[],
      isNewGold: props.resourceType == '143' || false,
      // allCategory:[],
      linkAlgorithmPolicy: sessionStorage.getItem("linkAlgorithmPolicy") || '',
      searchLabelList: sessionStorage.getItem("searchLabelList") ? JSON.parse(sessionStorage.getItem("searchLabelList")) : [
        {
          label: "",
          selectedGoodsCategoryList:[],
          goodsCategoryList: []
        }
      ],
      isCategoryCenter: (props.isCategoryCenter) || false,
      linkAlgorithmCenterOptions: [],  //品类中心
      linkAlgorithmCenter: sessionStorage.getItem("linkAlgorithmCenter") || '',
    }
  }

  componentDidMount() {
    if(!this.state.isNewGold) {
      this.getSkuCategories();
    }
    if (this.state.isCategoryCenter) {
      this.getAllQueryAlgorithmStrategyCenterList();
    }
  }

  componentWillReceiveProps(newProps) {
    let {isNewGold, categoryCascadeOptions, isCategoryCenter, linkAlgorithmCenterOptions} = this.state;
    this.setState({
      resourceType: newProps.resourceType,
      isNewGold: newProps.resourceType == '143' || false,
      linkAlgorithmPolicy: sessionStorage.getItem("linkAlgorithmPolicy") || '',
      searchLabelList: sessionStorage.getItem("searchLabelList") ? JSON.parse(sessionStorage.getItem("searchLabelList")) : [
        {
          label: "",
          selectedGoodsCategoryList: [],
          goodsCategoryList: []
        }
      ],
      isCategoryCenter: (newProps.isCategoryCenter) || false,
      linkAlgorithmCenterOptions: linkAlgorithmCenterOptions,  //品类中心
      linkAlgorithmCenter: sessionStorage.getItem("linkAlgorithmCenter") || '',
    },()=>{
      if (isNewGold && categoryCascadeOptions.length == 0) {
        this.getAllCategoryKingKongLinkAlgorithmPolicy();
        this.getAllFastSearchWordLinkAlgorithmPolicy();
      }
      if (isCategoryCenter && linkAlgorithmCenterOptions.length == 0) {
        this.getAllQueryAlgorithmStrategyCenterList();
      }
    })
  }

  async getAllCategoryKingKongLinkAlgorithmPolicy() {
    try {
      let resp = await queryAllCategoryKingKongLinkAlgorithmPolicy();
      this.setState({linkAlgorithmPolicyOptions:resp});
      console.log(resp);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  // 关联算法策略从品类中心获取
  async getAllQueryAlgorithmStrategyCenterList() {
    try {
      let resp = await queryAlgorithmStrategyCenterList();
      this.setState({linkAlgorithmCenterOptions:resp});
      console.log(resp);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getAllFastSearchWordLinkAlgorithmPolicy() {
    try {
      let resp = await queryAllFastSearchWordLinkAlgorithmPolicy();
      this.setState({categoryCascadeOptions:resp});
      console.log(resp);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getSkuCategories() {
    try {
      let resp = await api.ali.getCategorySku(false).then(api.onRequestSuccess);
      this.setState({
        categoryCascadeOptions: resp
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  changeLabel = (value,index) =>{
    let {searchLabelList} = this.state;
    searchLabelList[index].label = value;
    this.setState({searchLabelList},()=>{
      sessionStorage.setItem("searchLabelList",JSON.stringify(this.state.searchLabelList));
    });
  }

  changeLinkAlgorithmPolicy = (value) => {
    this.setState({
      linkAlgorithmPolicy: value
    }, () => {
      sessionStorage.setItem("linkAlgorithmPolicy", value);
    })
  }

  changeLinkAlgorithmCenter = (value) => {
    this.setState({
      linkAlgorithmCenter: value
    }, () => {
      sessionStorage.setItem("linkAlgorithmCenter", value);
    })
  }

  changeCategory = (value, data, extra, index) => {
    let {isNewGold, searchLabelList} = this.state;
    if (!isNewGold) {
      if (value.length <= 4) {
        const haveSelected = data.map(item => {
          const {value, label, pos} = item;
          return {
            value,
            label,
            level: pos.split('-').length - 1
          }
        })
        searchLabelList[index].goodsCategoryList = haveSelected
        searchLabelList[index].selectedGoodsCategoryList = value
        console.log(searchLabelList);
        this.setState({searchLabelList}, () => {
          sessionStorage.setItem("searchLabelList", JSON.stringify(this.state.searchLabelList));
        });
      } else {
        Message.warning("一个前台类目最多关联4个后台类目");
      }
    } else {
      const {value, label, pos} = data;
      const haveSelected = {
        value,
        label,
        level: pos.split('-').length - 1
      };
      searchLabelList[index].goodsCategoryList = [];
      searchLabelList[index].selectedGoodsCategoryList = [];
      searchLabelList[index].goodsCategoryList.push(haveSelected);
      searchLabelList[index].selectedGoodsCategoryList.push(value);
      console.log(searchLabelList);
      this.setState({searchLabelList}, () => {
        sessionStorage.setItem("searchLabelList", JSON.stringify(this.state.searchLabelList));
      });
    }
  }

  delCategory = (index) =>{
    let {searchLabelList} = this.state;
    if(searchLabelList.length>1) {
      searchLabelList.splice(index,1);
    }else{
      Message.warning("最少1个");
    }
    this.setState({searchLabelList},()=>{
      sessionStorage.setItem("searchLabelList",JSON.stringify(this.state.searchLabelList));
    });
  }

  addCategory = () =>{
    let {searchLabelList,isNewGold} = this.state;
    let message = '最多配置8个类目';
    let maxLength = 8;
    if (isNewGold) {
      maxLength = 10;
      message = '最多配置10个商品词';
    }
    let item = {
      label: "",
      goodsCategoryList: []
    }
    if (searchLabelList.length < maxLength) {
      searchLabelList.push(item);
      this.setState({searchLabelList}, () => {
        sessionStorage.setItem("searchLabelList", JSON.stringify(this.state.searchLabelList));
      });
    } else {
      Message.warning(message);
    }
  }

  render() {
    let { searchLabelList, categoryCascadeOptions,isNewGold, linkAlgorithmPolicyOptions, linkAlgorithmPolicy,  linkAlgorithmCenterOptions, linkAlgorithmCenter, isCategoryCenter, skipUrl, resourceId } = this.state;
    let maxLength = (isNewGold ? 5 : 4);
    let isRequired = (resourceId != '100072001' && resourceId != '72001' && resourceId != '100072005' && resourceId != '72005' && resourceId != '100007046' && resourceId != '7046');
    if(isCategoryCenter){
      return <Form.Item required hasFeedback label="关联算法策略:" {...formItemLayoutChannel} className='newputin'>
        <CascaderSelect placeholder='请选择' multiple={false} dataSource={linkAlgorithmCenterOptions} style={{width: '300px', marginRight: '5px'}}
                        value={linkAlgorithmCenter} onChange={(value) => this.changeLinkAlgorithmCenter(value)}/>
        <a href={skipUrl} target="_blank">去创建</a>
      </Form.Item>
    }else{
      return (
        <>
          {isNewGold && <Form.Item required={isRequired} hasFeedback={isRequired} label="关联算法策略:" {...formItemLayoutChannel}>
            <CascaderSelect placeholder='请选择' multiple={false} dataSource={linkAlgorithmPolicyOptions} style={{width: '300px', marginRight: '5px'}}
                            value={linkAlgorithmPolicy} onChange={(value) => this.changeLinkAlgorithmPolicy(value)}/>
          </Form.Item>}
          <Form.Item label={`${isNewGold ? '商品词' : '所属类目'}:`} {...formItemLayoutChannel}>
            {(searchLabelList && searchLabelList.length > 0) && searchLabelList.map((v, index) => {
              // let selectedGoodsCategoryList = (v.goodsCategoryList && v.goodsCategoryList.length > 0) ? v.goodsCategoryList.map(v => v.value) : [];
              return <div style={{marginBottom: '10px'}}>
                <Input value={v.label} placeholder={`请输入${isNewGold ? '商品词' : '前台类目'},最多${maxLength}个字`} style={{width: '170px',marginRight: '5px'}}
                       maxLength={maxLength} onChange={(value) => this.changeLabel(value, index)}/>
                {categoryCascadeOptions.length > 0 &&
                <CascaderSelect placeholder={`${isNewGold ? '请选择关联的算法策略' : '请选择后台类目,最多4个'}`} multiple={!isNewGold}
                                value={v.selectedGoodsCategoryList} style={{width: '260px', marginRight: '5px'}}
                                dataSource={categoryCascadeOptions}
                                onChange={(value, data, extra) => this.changeCategory(value, data, extra, index)}/>}
                <Button type={'primary'} style={{marginRight: '5px'}} onClick={() => this.delCategory(index)}>－</Button>
                {index == 0 && <Button type={'primary'} onClick={this.addCategory}>＋</Button>}
              </div>
            })}
          </Form.Item>
        </>
      )
    }

  }
}

class SignComps extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      resourceId:this.props.resourceId,
      categoryCascadeOptions:[],
      signRedBagList: sessionStorage.getItem("signRedBagList") ? JSON.parse(sessionStorage.getItem("signRedBagList")) : this.ctrlSignList()
    }
    sessionStorage.setItem("signRedBagList",JSON.stringify(this.state.signRedBagList));
  }

  componentDidMount() {
    // this.ctrlSignList();
  }

  ctrlSignList = () =>{
    let result = [];
    for(let o = 1; o <= 7; o++){
      const hasRedBagGroup = [3,7];
      if(hasRedBagGroup.includes(o)){
        result.push({
          redBagAttr: '',
          appointRightId: '',
          // appointBrandId: '',
          signDay: o,
        })
      }else{
        result.push({
          appointRightId: '',
          // appointBrandId: '',
          signDay: o,
        })
      }
    }
    return result;
  }

  componentWillReceiveProps(newProps) {
    this.setState({
      signRedBagList: sessionStorage.getItem("signRedBagList") ? JSON.parse(sessionStorage.getItem("signRedBagList")) : this.ctrlSignList()
    },()=>{
      sessionStorage.setItem("signRedBagList",JSON.stringify(this.state.signRedBagList));
    })
  }

  changeSign = (value, key, index) =>{
    let {signRedBagList} = this.state;
    signRedBagList[index][key] = value;
    this.setState({signRedBagList},()=>{
      sessionStorage.setItem("signRedBagList",JSON.stringify(this.state.signRedBagList));
    });
  }

  render() {
    let {signRedBagList,resourceId} = this.state;
    if(resourceId=='100071002' || resourceId=='71002'){
      return (
        <Form.Item {...formItemLayoutChannel} style={{marginLeft:'100px'}} validate={this.state.signRedBagList}>
          {(signRedBagList && signRedBagList.length>0) && signRedBagList.map((v,index)=>{
            return <div style={{marginBottom:'10px'}}>
              <p style={{color:'#666666'}}>签到{v.signDay}天</p>
              <label style={{margin:'0 5px 0 40px'}}>权益池ID</label>
              <NumberPicker value={v.appointRightId} placeholder='指定权益池ID' style={{width:'120px'}} onChange={(value) => this.changeSign(value, 'appointRightId',index)} />
              {v.hasOwnProperty('redBagAttr') && <>
                <label style={{margin:'0 2px 0 2px'}}>红包属性</label>
                <NumberPicker value={v.redBagAttr} style={{width: '60px'}} onChange={(value) => this.changeSign(value, 'redBagAttr', index)} min="0" max="9999" />
              </>}
            </div>
          })}
        </Form.Item>
      )
    }else{
      return (
        <Form.Item {...formItemLayoutChannel} style={{marginLeft:'100px'}}>
          {(signRedBagList && signRedBagList.length>0) && signRedBagList.map((v,index)=>{
            return <div style={{marginBottom:'10px'}}>
              <p style={{color:'#666666'}}>签到{v.signDay}天</p>
              <label  className="rights" style={{margin:'0 5px 0 40px'}}>权益ID</label>
              <NumberPicker value={v.appointRightId} placeholder='指定权益ID' style={{width:'105px'}} onChange={(value) => this.changeSign(value, 'appointRightId',index)} />
              {/*<label style={{margin:'0 5px 0 5px'}}>品牌ID</label>*/}
              {/*<NumberPicker value={v.appointBrandId} placeholder='指定品牌ID' style={{width:'105px'}} onChange={(value) => this.changeSign(value, 'appointBrandId',index)} />*/}
              {v.hasOwnProperty('redBagAttr') && <>
                <label style={{margin:'0 2px 0 2px'}}>红包属性</label>
                <Input value={v.redBagAttr} style={{width: '60px'}} onChange={(value) => this.changeSign(value, 'redBagAttr', index)}/>
              </>}
            </div>
          })}
        </Form.Item>
      )
    }
  }
}

class SignEntryComps extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      fieldGroup:[
        {
          key:"noSignInCopyList",title:"未签到"
        },
        {
          key:"signInCopyList",title:"签到"
        }
      ],
      signGroup:{
        noSignInCopyList: this.ctrlNoSignInCopyList('noSignInCopyList'),
        signInCopyList: this.ctrlNoSignInCopyList('signInCopyList'),
      }
    }
  }

  componentDidMount() {
  }

  ctrlNoSignInCopyList = (key) =>{
    return sessionStorage.getItem(key) ? JSON.parse(sessionStorage.getItem(key)) : [
      {
        signContent: '',
        sort: '',
        typeFaceColor: ''
      }
    ]
  }

  componentWillReceiveProps(newProps) {
    this.setState({
      signGroup:{
        noSignInCopyList: this.ctrlNoSignInCopyList('noSignInCopyList'),
        signInCopyList: this.ctrlNoSignInCopyList('signInCopyList'),
      }
    })
  }

  changeSign = (value, index,field, key) => {
    let {signGroup, fieldGroup} = this.state;
    signGroup[field][index][key] = value;
    signGroup[field] = signGroup[field].map((v,i)=>{
        v.sort = 1+i;
        return v;
    })
    this.setState({
      signGroup
    }, () => {
      sessionStorage.setItem(field, JSON.stringify(this.state.signGroup[field]));
    })
  }

  delItem = (field,index) =>{
    let {signGroup} = this.state;
    if(signGroup[field].length>1) {
      signGroup[field].splice(index,1);
      signGroup[field] = signGroup[field].map((v,i)=>{
        v.sort = 1+i;
        return v;
      })
    }else{
      Message.warning("最少1个");
    }
    this.setState({signGroup},()=>{
      sessionStorage.setItem(field,JSON.stringify(this.state.signGroup[field]));
    });
  }

  addItem = (key) =>{
    let {signGroup,fieldGroup} = this.state;
    let item = {
      signContent: '',
      sort: '',
      typeFaceColor: ''
    }
    if (signGroup[key].length < 5) {
      signGroup[key].push(item);
      this.setState({signGroup}, () => {
        sessionStorage.setItem(key, JSON.stringify(this.state.signGroup[key]));
      });
    } else {
      Message.warning("最多配置5个");
    }
  }

  render() {
    let {noSignInCopyList,signInCopyList,fieldGroup,signGroup} = this.state;
    return (
      <>
        {fieldGroup.map((o) => {
          let title = o.title;
          let item = signGroup[o.key];
          return <Form.Item label={`${title}文案：`} {...formItemLayoutChannel}>
            {(item && item.length > 0) && item.map((v, index) => {
              console.log(v);
              return <div style={{marginBottom: '10px'}}>
                  <Input value={v.signContent} placeholder={`请输入${title}文案`} style={{marginRight: '5px',width:'160px'}}
                         onChange={(value) => this.changeSign(value, index,o.key, 'signContent')}/>
                  <label>颜色：</label>
                  <Input value={v.typeFaceColor} placeholder='请输入色值，例如ECECEC' style={{marginRight: '5px',width:'100px'}}
                         onChange={(value) => this.changeSign(value, index,o.key,  'typeFaceColor')}/>
                  <Button type={'primary'} style={{marginRight: '5px'}} onClick={() => this.delItem(o.key,index)}>－</Button>
                  {index == 0 && <Button type={'primary'} onClick={()=>this.addItem(o.key)}>＋</Button>}
              </div>
            })}
          </Form.Item>
        })
        }
      </>
    )
  }
}
class AoiList extends React.Component {
  constructor(props) {
    super(props);
    const ext_aoi_codes = sessionStorage.getItem("ext_aoi_codes");
    this.state = {
      ext_aoi_codes: ext_aoi_codes ? JSON.parse(ext_aoi_codes) : [],
      channelListMap:[],
    }
  }
  componentDidMount() {
    this.getAoiList();
  }

  getAoiList = () => {
    api.queryAoiList().then((result) => {
      this.setState({
        aoiListMap: JSON.parse(JSON.stringify(result).replace(/"subChannels"/g,'"children"'))
      })
    }).catch(api.onRequestError);
  }

  changAoi = (aoi) => {
    this.setState( {ext_aoi_codes: aoi},()=>{
      sessionStorage.setItem("ext_aoi_codes",JSON.stringify(this.state.ext_aoi_codes));
    })
  }

  render()
  {
    return (
      <Form className='newputin'>
        <Form.Item label="投放AOI类型:" {...formItemLayoutChannel}>
          <CascaderSelect
            style={{ width: '302px' }}
            name="ext_aoi_codes"
            multiple
            value={this.state.ext_aoi_codes}
            dataSource={this.state.aoiListMap}
            onChange={(value) => this.changAoi(value)}
          />
        </Form.Item>
      </Form>
    )
  }
}
function isHiddenMultilineIdText(text,form) {
  if (typeof text == 'string') {
    let indexsigh = text.indexOf('!');
    let indexEqual = text.indexOf('=');
    let lastIndexEqual = text.lastIndexOf('=');
    let name = indexsigh > 0 ? text.substring(0,indexsigh).trim() : text.substring(0,indexEqual).trim();
    let value = text.substring(lastIndexEqual+1).trim()
    return indexsigh > 0 ? form[name] != value : (indexEqual > 0 ? form[name] == value : true);
  }else if (typeof text == 'boolean') {
    return text;
  }else{
    return false;
  }
}
class MultilineIdText extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showMultilineIdText:this.props.showMultilineIdText || {},
      multilineIdText : JSON.parse(sessionStorage.getItem(`${this.props.showMultilineIdText.key}`)) || ''
    }
  }

  changMultilineIdText = (text) => {
    this.setState( {multilineIdText: text},()=>{
      sessionStorage.setItem(`${this.props.showMultilineIdText.key}`,JSON.stringify(text));
    })
  }

  render()
  {
    const { name, maxLength, key, placeholder, hidden = '', required = false } = this.state.showMultilineIdText;
    const isHidden = isHiddenMultilineIdText(hidden,this.props.formData)
    return (isHidden && <Form className='newputin'>
      <Form.Item label={`${name}:`} {...formItemLayoutChannel} required={required}>
        <Input.TextArea
          style={{ width: '302px' }}
          name={`${key}`}
          placeholder={`请输入${placeholder || name},多个${placeholder || name}使用英文逗号隔开,至多${maxLength}个`}
          onChange={(value) => this.changMultilineIdText(value)}
          value={this.state.multilineIdText}
        ></Input.TextArea>
      </Form.Item>
    </Form>)
  }
}
class ShopMainCategory extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      shopMainCategoryDataSource:[],
      shop_main_category:JSON.parse(sessionStorage.getItem('shop_main_category')) || [], //list map
      shopMajorCategoryList:JSON.parse(sessionStorage.getItem('shopMajorCategoryList')) || [], //list string
      showMainCategory: this.props.showMainCategory || {}
    }
  }
  componentDidMount() {
    this.getMainCategory();
  }

  getMainCategory = () => {
    getStoreMajorCategory().then((result) => {
      this.setState({
        shopMainCategoryDataSource: result.map((item)=>{
          return {
            label:item.name,
            value:item.code
          }
        })
      })
    }).catch(api.onRequestError);
  }

  changMainCategory = (newShopMainCategory,data) => {
    const newData = data.map(item => {
      const {value, label, pos} = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    this.setState( {shop_main_category: newData,shopMajorCategoryList: newShopMainCategory},()=>{
      sessionStorage.setItem("shop_main_category",JSON.stringify(this.state.shop_main_category));
      sessionStorage.setItem("shopMajorCategoryList",JSON.stringify(this.state.shopMajorCategoryList));
    })
  }

  render()
  {
    const { required = false } = this.state.showMainCategory || {};
    return (
      <Form className='newputin'>
        <Form.Item label="商家主营类目:" {...formItemLayoutChannel} required={required}>
          <CascaderSelect
            showSearch
            style={{ width: '302px' }}
            name="shopMainCategory"
            multiple
            value={this.state.shopMajorCategoryList}
            dataSource={this.state.shopMainCategoryDataSource}
            onChange={(value,item) => this.changMainCategory(value,item)}
          />
        </Form.Item>
      </Form>
    )
  }
}
class AdvertiseLayer extends React.Component { //逛品牌霸屏广告位
  constructor(props) {
    super(props);
    let {scheduleInfo} = props;
    let adFloat = (scheduleInfo && scheduleInfo.adFloat) ? scheduleInfo.adFloat:'2';
    this.saveBaseConfig("adFloat", adFloat);
    this.state = {
      visible: false,
      adFloat, //1:是  2：否
      materialType: '', //1：视频，2：图片
      brandBaPingUrl: '',
      viewId: '',
      type: '',
      videoListVisible: false,
      videoVisible: false,
      viewItem: '', //预览的视频
      selectList: [],
      videoList:[],
      scheduleInfo: props.scheduleInfo,
      mode: 1, //1：列表模式  2：选中模式
      page:1,
      size:10,
      query:'',
      total:0
    }
  }

  componentDidMount() {
    this.getViewList();
  }

  componentWillReceiveProps(newProps) {
    let advertiseGroup = sessionStorage.getItem("advertiseGroup") ? JSON.parse(sessionStorage.getItem("advertiseGroup")) : '';
    if(advertiseGroup) {
      this.setState({
        advertiseGroup,
        adFloat: advertiseGroup.adFloat,
        materialType: advertiseGroup.materialType,
        brandBaPingUrl: advertiseGroup.brandBaPingUrl,
        viewId: advertiseGroup.viewId,
        selectList: advertiseGroup.selectViewList || [],
        mode: (advertiseGroup && advertiseGroup.selectViewList && advertiseGroup.selectViewList.length > 0) ? 2 : 1,
        videoListVisible: true
      }, () => {
        console.log(this.state.advertiseGroup);
      })
    }
  }

  saveBaseConfig = (key, value) => {
    let advertiseGroup = sessionStorage.getItem("advertiseGroup") ? JSON.parse(sessionStorage.getItem("advertiseGroup")) : {};
    advertiseGroup[key] = value;
    sessionStorage.setItem("advertiseGroup", JSON.stringify(advertiseGroup));
  }

  getViewList = async () => {
    let {page,size,query,total,videoList} = this.state;
    let req = {
      page,
      size,
      query
    }
    videoList = [];
    total = 0;
    try {
      let res = await queryViewList(req);
      videoList = res.data?res.data:[];
      total = res.total;
    } catch (error) {
      // api.onRequestError(error)
    }
    this.setState({
      videoList,
      total,
    },()=>{
      console.log(this.state);
    })
  }

  onCheckChange = (v) => {
    let {type} = this.state;
    if (!v) type = '';
    let value = v ? '1' : '2';
    this.saveBaseConfig("adFloat", value);
    this.setState({adFloat: value, type});
  }

  onSelectChange = (v) => {
    this.saveBaseConfig("materialType", v);
    this.saveBaseConfig("brandBaPingUrl", '');
    this.saveBaseConfig("viewId", '');
    let advertiseGroup = sessionStorage.getItem("advertiseGroup") ? JSON.parse(sessionStorage.getItem("advertiseGroup")) : '';
    if (v == '2' && advertiseGroup.selectViewList && advertiseGroup.selectViewList.length > 0) {
      delete advertiseGroup.selectViewList;
      this.setState({selectViewList: []})
      sessionStorage.setItem("advertiseGroup", JSON.stringify(advertiseGroup));
    }
    this.setState({materialType: v, brandBaPingUrl: '', viewId: ''})
  }

  uploadChange = (info) => {
    console.log(info);
    this.saveBaseConfig("brandBaPingUrl", info);
    this.setState({brandBaPingUrl: info}, () => {
      console.log(this.state)
    });
  }

  onSearch = (v) =>{
    this.setState({query: v},()=>{
      this.getViewList();
    });
  }

  playVideo = (item) => {
    this.setState({videoVisible: true, viewItem: item});
  }

  selectVideo = (item) => {
    let {selectList, mode, brandBaPingUrl, viewId} = this.state;
    if (mode == 2) { //更换
      mode = 1;
    } else { //选择
      selectList = [];
      selectList[0] = item;
      brandBaPingUrl = item.transcodeUrl;
      viewId = item.id;
      mode = 2;
    }
    this.saveBaseConfig("brandBaPingUrl", brandBaPingUrl);
    this.saveBaseConfig("viewId", viewId);
    this.saveBaseConfig("selectViewList", selectList);
    this.setState({selectList, mode,brandBaPingUrl,viewId},()=>{
      console.log(this.state);
    })
  }

  onPriorityChange = (v) =>{
    this.setState({
      priority:v
    },()=>{
      this.saveBaseConfig("priority", v);
    })
  }

  onPageChange = (value) =>{
    console.log(value);
    this.setState({
      page:value
    },()=>{
      this.getViewList();
    })
  }

  render() {
    let { adFloat, materialType, brandBaPingUrl, viewItem, videoListVisible, videoVisible, selectList, mode,videoList,priority,page,size,query,total} = this.state;
    let radioDs = [{label: '视频', value: '1'}, {label: '图片', value: '2'}];
    let options = {
      format: "uri",
      type: "string",
      validate: {
        accept: "png,jpeg,jpg,apng,gif",
        height: 1500,
        maxSize: 500,
        width: 990
      }
    }
    let schema = {}
    let tmpList = (mode == 2) ? Object.assign(selectList) : Object.assign(videoList);
    console.log(tmpList);
    const PLAY_IMG = require('../../../../images/icon-play.png');
    return (
      <>
        <Form.Item label="增加广告位浮层：" {...formItemLayoutChannel2} className="item-advertise">
          <Switch autoWidth style={{marginTop:'10px'}} checked={(adFloat==1)} checkedChildren="是" unCheckedChildren="否" onChange={this.onCheckChange} />
          {(adFloat=='1') && <Radio.Group style={{position:'relative',top:'-10px',marginLeft:'5px'}} name="radio" value={materialType} dataSource={radioDs} onChange={this.onSelectChange}></Radio.Group>}
          {(adFloat=='1') && <div>
            {(materialType == '1' && !videoListVisible) && <Button type='normal' onClick={()=>this.setState({videoListVisible:true})}>选择视频素材</Button>}
            {(materialType == '1' && videoListVisible) && <div className="video-list">
              <Button text onClick={()=>this.setState({videoListVisible:false})}>收起</Button>
              <Search key="4" type={'secondary'} placeholder={'查询视频名称'} onSearch={this.onSearch} style={{width: "400px"}} />
              <div className='inner'>
                {tmpList.length>0 && tmpList.map((v) => {
                  let curRow = (selectList && selectList.length > 0 && selectList[0].id == v.id);
                  return <Row>
                    <Col span='4'>
                      <div className="bg-video" onClick={()=>this.playVideo(v)}>
                        <img src={v.headUrl} className='head' />
                        <img src={PLAY_IMG} className='icon' />
                        {/*<video src={'https://media.w3.org/2010/05/sintel/trailer_hd.mp4'} poster="https://video-react.js.org/assets/poster.png" width='100%' height='100%' controls='controls' />*/}
                      </div>
                    </Col>
                    <Col span='16'>
                      <p className="title">{v.mediaName}</p>
                      <p className="position">{v.brandName}</p>
                    </Col>
                    <Col span='4'>
                      <Button className={`btn-video ${curRow ? 'current' : ''}`} onClick={() => this.selectVideo(v)}>{(mode == 2) ? '更换' : (curRow ? '已选' : '选择')}</Button>
                    </Col>
                  </Row>
                }) }
                {tmpList.length==0 && <div className="data-empty">暂无数据</div>}
                {mode==1 && <Pagination style={{marginTop:'10px'}} current={page} total={total} type="simple" onChange={this.onPageChange}/> }
              </div>
              <Dialog
                title="视频预览"
                className="video-dialog"
                visible={videoVisible}
                style={{width:'392px'}}
                footer={false}
                onClose={() => this.setState({videoVisible: false})}
              >
                <video src={viewItem.transcodeUrl} poster={viewItem.headUrl}  width='392' height='auto' controls='false'/>
              </Dialog>
            </div>}
            {materialType == '2' && <ImgUploadWidget options={options} value={brandBaPingUrl} schema={schema} onChange={this.uploadChange}/>}
          </div>}
        </Form.Item>
        {adFloat == '2' && <Form.Item label="权重：" {...formItemLayoutChannel2}>
          <NumberPicker placeholder='取值优先级：数值＞时间' max='999999' name='priority' style={{width:'150px'}} value={priority} onChange={this.onPriorityChange} />
        </Form.Item>}
      </>
    )
  }
}

class Step2 extends React.Component {
  constructor(props) {
    super(props);
    debugLF('Step2 constructor', { props });
  }

  componentDidUpdate() {
    debugLF('Step2 did update');
  }

  componentWillUnmount() {
    debugLF('Step2 will unmount');
  }

  render() {
    debugLF('Step render', this.props);

    const { resourceType } = this.props;
    return (
      <CreatePutInPage resourceType={resourceType}/>
    )
  }
}

//#endregion

export const LogTimePutInPage = logTimeComponent(MarketEdit, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MarketEditPage = permissionAccess(LogTimePutInPage, async (params) => {
  return await validateAclResource(params);
})
