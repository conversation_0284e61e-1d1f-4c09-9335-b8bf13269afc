import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button, CascaderSelect,
  Form,
  Input, Message, Select, Switch,
  Tab,
} from '@alife/next'
import {ACLPermission} from '@/containers//PutInPage/request';
import React from 'react'
import {Link} from 'react-router-dom'
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {PageBase} from '@/containers/base';
import {permissionAccess} from '@/components/PermissionAccess';
import DetailConfig from './detailConfig'
import moment from 'moment';
import * as qs from 'query-string';
import './viewConfig.scss';
import {
  pageMap,showQrCodeMap
} from '../common';
import * as api from '@/utils/api';
import {
  queryPosConfig,
  validateAclResource,
  submitScheduleCheck
} from '../request';
import {TimeRangePicker} from "@/components/TimeRangePicker";
import QRCode from "qrcode.react";
import {getAllDeliveryChannel, getCityList, onRequestError} from "@/utils/api";
import {getUserTagGroupIdsList} from "@/containers/PutInPage/request";
import {versionGroupSet} from "@/containers/PutInPage/constants";
import {composeSelector, composeSelectorGrid} from "@/components/CascaderSelect";
import {formLayout} from "@/containers/PutInPage/CreatePutInPage/constants";
function fetchCity() {
  return getCityList().then((res) => res).catch(onRequestError)
}
let CitySelector = composeSelector(fetchCity);
let GridSelector = composeSelectorGrid(fetchCity);

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 7},
  wrapperCol: {
    span: 11
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

class NewPutInfo extends React.Component {
  constructor(props) {
    super(props);

    const putInInfo = sessionStorage.getItem("newPutInInfo")
      ? JSON.parse(sessionStorage.getItem("newPutInInfo"))
      : '';
    this.state = {
      putInInfo,
      cityList:[],
      citys:putInInfo ? putInInfo.citys : [],
      selectedCitys:putInInfo ? putInInfo.selectedCitys : [],
      griddings:[],
      showGrid:(props.resourceName == "每日聚划算"),
      resourceName:props.resourceName,
      pageId: props.pageId,
      resourceId: props.resourceId,
      channelListMap:[],
      channelList: putInInfo ? putInInfo.channelList : [],
      userTagGroupIdsListSet:[],
      userTagGroupIdsList:putInInfo?putInInfo.userTagGroupIdsList:[],
      channelListValue:[],
      channelVersionList:putInInfo ? putInInfo.channelVersionList: {
        allVersion: true,
        channel: "",
        versionGroup: {
          relationType: "and",
          groupList: [],
          versionList: [
            {
              operateType: "",
              value: "",
            },
          ],
        },
      },
    }

    this.cityList = [];
  }

  initCityList = () =>{
    const putInInfo = sessionStorage.getItem("newPutInInfo")
      ? JSON.parse(sessionStorage.getItem("newPutInInfo"))
      : '';
    if (putInInfo && putInInfo.griddings && putInInfo.griddings.length) {
      putInInfo.griddings.forEach((item) => {
        if (item.parentValue && !this.cityList.includes(item.parentValue)) {
          this.cityList.push(item.parentValue);
        }
      });
    }
  }

  initField = () =>{
    const putInInfo = sessionStorage.getItem("newPutInInfo")
      ? JSON.parse(sessionStorage.getItem("newPutInInfo"))
      : '';
    this.setState({
      putInInfo,
      citys:putInInfo ? putInInfo.citys : [],
      selectedCitys:putInInfo ? putInInfo.selectedCitys : [],
      channelList: (putInInfo && putInInfo.channelList) ? putInInfo.channelList : [],
      userTagGroupIdsList: (putInInfo && putInInfo.userTagGroupIdsList) ? putInInfo.userTagGroupIdsList : [],
      channelVersionList: (putInInfo && putInInfo.channelVersionList) ? putInInfo.channelVersionList : {
        allVersion: true,
        channel: "",
        versionGroup: {
          relationType: "and",
          groupList: [],
          versionList: [
            {
              operateType: "",
              value: "",
            },
          ],
        },
      },
    })
  }

  componentDidMount() {
    this.queryResourceDeliveryChannel();
    this.initCityList();
  }

  componentWillReceiveProps(newProps) {
    console.log(newProps);
    this.initCityList();
    this.initField();
  }

  saveBaseConfig = (key, value) => {
    // let advertiseGroup = sessionStorage.getItem("advertiseGroup") ? JSON.parse(sessionStorage.getItem("advertiseGroup")) : {};
    // advertiseGroup[key] = value;
    // sessionStorage.setItem("advertiseGroup", JSON.stringify(advertiseGroup));
  }


  queryResourceDeliveryChannel = () => {
    getAllDeliveryChannel()
      .then((result) => {
        let { channelList } = result;
        this.setState({
          channelListMap: JSON.parse(JSON.stringify(channelList).replace(/"subChannels"/g,'"children"'))
        })
      })
      .catch(api.onRequestError);
  };


  changeDeliveryChannel = (checkedList) => {
    this.setState({channelList:checkedList})
    this.saveLocalField("channelList", checkedList);
    // this.props.onFirstPageChange("channelList", checkedList);
  };

  removeDuplicate = (arr) => {
    var hash = {};
    let result = arr.reduce(function (item, next) {
      hash[next.value] ? "" : (hash[next.value] = true && item.push(next));
      return item;
    }, []);
    return result;
  };


  onSearchUserTagGroupIdsList = (keyword) => {
    if (this.searchGroupTimeout) {
      clearTimeout(this.searchGroupTimeout);
    }
    this.searchGroupTimeout = setTimeout(() => {
      if (keyword) {
        getUserTagGroupIdsList({ groupId: keyword }).then((data) => {
          let { userTagGroupIdsListSet } = this.state;
          const dataSource = data.map((item) => ({
            label: item.groupName,
            value: item.groupId,
          }));
          this.setState({
            userTagGroupIdsListSet:this.removeDuplicate([...userTagGroupIdsListSet, ...dataSource])
          })
        });
      } else {
        this.setState({ poolIdsSet: [] });
        sessionStorage.setItem("poolIdsSet", JSON.stringify([]));
      }
    }, 800);
  };

  onChangeUserTagGroupIdsList = (value, actionType) => {
    if (actionType == "itemClick" || actionType == "tag") {
      this.setState({
        userTagGroupIdsList: value
      })
      this.saveLocalField("userTagGroupIdsList", value);
    }

  };

  saveLocalField = (name, value) => {
    let fields = [
      "channelVersionList",
      "channelList",
      "channelLabel",
      "citys",
      "userTagGroupIdsList",
      "selectedCitys",
      "griddings",
      "peopleType",
      "peopleOrderType",
      'eleCategoryFirList',
      'eleCategorySecList'
    ];
    if (fields.includes(name)) {
      let putInInfo = sessionStorage.getItem("newPutInInfo")
        ? JSON.parse(sessionStorage.getItem("newPutInInfo"))
        : {};
      putInInfo[name] = value;
      if (name == "channelList") {
        if (
          value.includes("*") ||
          value.includes("android") ||
          value.includes("ios")
        ) {
          putInInfo.channelVersionList = this.state.channelVersionList;
          let newChannelList = value.filter((v) =>
            ["*", "android", "ios"].includes(v)
          );
          putInInfo.channelVersionList.channel = newChannelList.join(",");
        } else {
          delete putInInfo.channelVersionList;
        }
      }
      // this.field.setValue(name, value);
      sessionStorage.setItem("newPutInInfo", JSON.stringify(putInInfo));
    }
  };

  changeAllVersion = (value) => {
    let { channelVersionList } = this.state;
    channelVersionList.allVersion = value;
    this.ctrlChannelVersionList(channelVersionList);
  };

  ctrlChannelVersionList = (value) => {
    let putInInfo = sessionStorage.getItem("newPutInInfo")
      ? JSON.parse(sessionStorage.getItem("newPutInInfo"))
      : {};
    if (putInInfo.channelList) {
      let newChannelList = putInInfo.channelList.filter((v) =>
        ["*", "android", "ios"].includes(v)
      );
      value.channel = newChannelList.join(",");
    }
    this.setState({ value }, () => {
      this.saveLocalField("channelVersionList", this.state.channelVersionList);
    });
  };

  changeVersion = (key, e, index) => {
    let { channelVersionList } = this.state;
    let value = e.target ? e.taret.value : e;
    channelVersionList.versionGroup.versionList[index][key] = value;
    this.ctrlChannelVersionList(channelVersionList);
  };

  addVersion = () => {
    let { channelVersionList } = this.state;
    let item = {
      operateType: "",
      value: "",
    };
    if (channelVersionList.versionGroup.versionList.length < 3) {
      channelVersionList.versionGroup.versionList.push(item);
      this.setState({ channelVersionList }, () => {
        this.ctrlChannelVersionList(channelVersionList);
      });
    } else {
      Message.warning("最多3个");
    }
  };

  delVersion = (index) => {
    let { channelVersionList } = this.state;
    if (channelVersionList.versionGroup.versionList.length > 1) {
      channelVersionList.versionGroup.versionList.splice(index, 1);
    } else {
      Message.warning("最少1个");
    }
    this.ctrlChannelVersionList(channelVersionList);
  };

  //获取selectedCitys&griddings
  getSelectedGriddings = (data, extra) => {
    let { showGrid } = this.state;
    const [selectedCitys, griddings] = this.updateSelectedGriddings(data, extra);
    this.saveLocalField("selectedCitys", selectedCitys);
    this.saveLocalField("citys", data);
    this.saveLocalField("griddings", griddings);
    this.setState({
      selectedCitys,
      griddings,
      citys:data
    })
  };

  //获取selectedCitys
  getSelectedCitys = (data) => {
    let newCitys = data.map(item => {
      const { value, label, pos } = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    const selectedCitys = this.updateSelectedCitys(data);
    this.saveLocalField("citys", newCitys);
    this.saveLocalField("selectedCitys", selectedCitys);
    this.setState({selectedCitys})
    this.setState({citys: newCitys})
  };

  //处理citys, 更新selectedCitys&griddings
  updateSelectedGriddings = (data, extra) => {
    const { indeterminateData = [] } = extra || {};
    const province = [];
    const citys = [];
    const griddings = [];
    data.forEach((item) => {
      let { value, label, children, pos, level } = item;
      if (!+value && label == "直营城市") {
        citys.push(item);
      } else if (level == 3 || (!level && pos.split("-").length == 4)) {
        griddings.push(item);
      } else if (level == 2 || (!level && pos.split("-").length == 3)) {
        citys.push(item);
      } else if (level == 1 || (!level && pos.split("-").length == 2)) {
        province.push(item);
      }
    });
    const extraCitys = indeterminateData.filter(
      (item) => item.pos.split("-").length == 3
    );

    return [
      province
        .reduce((provinceCitys, item) => provinceCitys.concat(item.children), [])
        .concat(citys, extraCitys)
        .map((item) => +item.value),
      griddings.map(({ value, label }) => ({
        value: +value.split("_")[1],
        name: label,
      })),
    ];
  }

  //处理citys, 更新selectedCitys
  updateSelectedCitys = (data) => {
    const province = data.filter((item) => !!item.children);
    const citys = data.filter((item) => !item.children);

    return province
      .reduce((provinceCitys, item) => provinceCitys.concat(item.children), [])
      .concat(citys)
      .map((item) => +item.value);
  }

  render() {

    // let showVersion = false;
    let {channelList, channelListMap,userTagGroupIdsListSet,userTagGroupIdsList,channelListValue,channelVersionList,resourceName,cityList,showGrid,selectedCitys,griddings} = this.state;
    let showVersion =
      (channelList && channelList.length > 0)
        ? channelList.includes("*") ||
        channelList.includes("android") ||
        channelList.includes("ios")
        : false;
    return (
      <Form {...formLayout} className='newputin'>
        <Form.Item label="投放渠道：" {...formItemLayout}>
          <CascaderSelect
            name="channelList"
            multiple
            value={channelList}
            dataSource={channelListMap}
            onChange={(value) => this.changeDeliveryChannel(value)}
          />
        </Form.Item>
        {showVersion && (
          <FormItem label="投放版本号(仅针对app):" {...formItemLayout}>
            <Switch
              style={{ width: "130px" }}
              checked={channelVersionList.allVersion}
              checkedChildren="默认全版本"
              unCheckedChildren="不默认全版本"
              onChange={this.changeAllVersion}
            />
            {!channelVersionList.allVersion &&
            channelVersionList.versionGroup &&
            channelVersionList.versionGroup.versionList.map(
              (item, index) => {
                return (
                  <div style={{ width: "600px", marginBottom: "10px" }}>
                    <Select
                      placeholder="请选择"
                      style={{ width: "200px", marginRight: "5px" }}
                      name="operateType"
                      value={item.operateType}
                      dataSource={versionGroupSet}
                      onChange={(value) =>
                        this.changeVersion("operateType", value, index)
                      }
                    />
                    <Input
                      name="versionValue"
                      style={{ marginRight: "5px" }}
                      placeholder="请输入版本号(比如9.1,9.1.1)"
                      value={item.value}
                      onChange={(e) =>
                        this.changeVersion("value", e, index)
                      }
                    />
                    <Button
                      type={"primary"}
                      style={{ marginRight: "5px" }}
                      onClick={() => this.delVersion(index)}
                    >
                      -
                    </Button>
                    {index == 0 && (
                      <Button type={"primary"} onClick={this.addVersion}>
                        and
                      </Button>
                    )}
                  </div>
                );
              }
            )}
          </FormItem>
        )}
        <Form.Item label="投放城市" {...formItemLayout}>
          {showGrid || cityList.length ? (
            <GridSelector
              name="citys"
              expandTriggerType="click"
              cityList={this.cityList}
              value={this.state.citys}
              getSelectData={this.getSelectedGriddings}
            />
          ) : (
            <CitySelector
              name="citys"
              value={this.state.citys}
              getSelectData={this.getSelectedCitys}
            />
          )}
        </Form.Item>
        <Form.Item
          className="user-tap-group-ids-wrap"
          label="投放人群："
          hasFeedback
          {...formItemLayout}
          validator={this.checkUserTagGroupIdsList}
        >
          <Select
            mode="multiple"
            showSearch
            placeholder="请输入用户群组ID，支持输入最多5个"
            name="userTagGroupIdsList"
            valueRender={this.renderUserTagGroupIdsList}
            value={userTagGroupIdsList ? userTagGroupIdsList : []}
            onChange={(value, actionType) =>
              this.onChangeUserTagGroupIdsList(value, actionType)
            }
            onSearch={this.onSearchUserTagGroupIdsList}
            dataSource={userTagGroupIdsListSet}
            style={{ width: "100%" }}
          />
          <a
            className="to-create"
            href="https://boreas.kunlun.alibaba-inc.com/page/crm/index.html?from=header-v2#/crm/crowedSelect"
            target="_blank"
          >
            去创建
          </a>
        </Form.Item>
      </Form>
    )
  }
}

export const LogTimeResourcePutInSet = logTimeComponent(NewPutInfo, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const NewPutInfoPage = permissionAccess(LogTimeResourcePutInSet, async (activityId) => {
  return await ACLPermission(activityId);
})
