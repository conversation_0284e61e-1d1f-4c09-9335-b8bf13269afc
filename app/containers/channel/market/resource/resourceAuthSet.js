import React from 'react';

import {PageWrapper} from '@/components/PageWrapper';
import {Breadcrumb, Form, Field, Grid, Message, Select, Radio, Dialog} from '@alife/next';
import {PageBase} from '@/containers/base';
import {onRequestError} from '@/utils/api';
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import {queryUserByKeyWord, createAclUser, queryAclUser, deleteAclUser, reviewHandle, queryReview, getResource} from '../request';
// import {pageMap} from '../common'
import './resourcePutInSet.scss'
const FormItem = Form.Item;
const {Row, Col} = Grid;
const RadioGroup = Radio.Group;
const resourcePutInSetItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 11
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
}

const formLayout = {
  style: {
    margin: '20px auto'
  },
}

class ResourceAuthSet extends PageBase {
  constructor(props) {
    super(props)
    let {pageId = '', resourceId = '', resourceName = ''} = this.query;
    const {pathname=''} = this.location;
    this.state = {
      pageId, // 页面id
      marketPageBreadInfo: {}, //商超频道页面包屑信息
      curPageBreadInfo: {}, //当前页页面包屑信息
      resourceId, // 资源位id
      resourceName, // 资源位名称
      checkAll: false,
      // userDataSource:[],
      manageDataSource:[],
      authDataSource:[],
      auditDataSource:[],
      defaultManageValue:[],
      defaultAuthValue:[],
      defaultAuditValue:[],
      popupManageVisible:false,
      popupAuthVisible:false,
      popupAuditVisible:false,
      reviewOpen:0,
      uedAuditOpen:0,
      uedAuditDataSource:[],
      defaultUedAuditOpen:[],
      popupUedAuditVisible:false,
      contentGroupReview:0,
      contentGroupReviewDataSource:[],
      defaultContentGroupReviewValue:[],
      popupContentGroupReviewVisible:false,
      dialogOpen:false,
      pageUrl: pathname.substring(0, pathname.lastIndexOf('/'))
    }

    this.field = new Field(this, {
      values: {...this.state.defaultValue}
    });
  }

  componentDidMount() {
    this.getActUser();
    this.getAllReviewState()
  }
  // 获取全部状态
  getAllReviewState=()=>{
    let {pageId,resourceId} = this.state;
    let requestDTO = {
      pageId,
      resourceId,
    }
    getResource({...requestDTO}).then((data) =>{
        this.setState({reviewOpen: data.reviewOpen || 0 });
        this.setState({uedAuditOpen: data.uedReviewOpen || 0});
        this.setState({contentGroupReview: data.contentReviewOpen || 0});
    }).catch(onRequestError)
  }

  getActUser = () => {

    let {pageId,resourceId} = this.state;
    let queryAclUserRequest = {
      pageId,
      resourceId,
    }
    this.setState({manageDataSource:[]})
    queryAclUser({...queryAclUserRequest}).then((data) => {
      let manageDataSource = [], authDataSource = [], auditDataSource = [], defaultManageValue = [],
        defaultAuthValue = [], defaultAuditValue = [],uedAuditDataSource = [],defaultUedAuditOpen = [],
        contentGroupReviewDataSource = [],defaultContentGroupReviewValue = [];
      data.map((v) => {
        let item = {
          value: v.empId,
          label: `${v.userName}_${v.empId}`,
          record: v
        };
        if (v.role === 1) {
          manageDataSource.push(item);
          defaultManageValue.push(item);
        } else if (v.role === 0) {
          authDataSource.push(item);
          defaultAuthValue.push(item);
        } else if (v.role === 2) {
          auditDataSource.push(item);
          defaultAuditValue.push(item);
        }else if (v.role === 3) {
          uedAuditDataSource.push(item);
          defaultUedAuditOpen.push(item);
        }else if (v.role === 4) {
          contentGroupReviewDataSource.push(item);
          defaultContentGroupReviewValue.push(item);
        }
      });
      this.setState({manageDataSource, authDataSource,defaultManageValue,defaultAuthValue,auditDataSource,defaultAuditValue,uedAuditDataSource,defaultUedAuditOpen,contentGroupReviewDataSource,defaultContentGroupReviewValue});
    }).catch(onRequestError)
  }

  addAcl = (value, actionType, item, role) => {
    let {resourceId, pageId} = this.state;
    if(value.length==0) {
      Message.error('用户已存在，请不要重复添加');
      return
    }
    if (actionType == 'itemClick' && value.length>0) {
      let single = item[item.length - 1].record;
      let {userId, empId} = single;
      let createAclUserRequest = {
        pageId,
        resourceId,
        role,
        userId,
        empId,
        userName: single.lastName || single.userName
      }
      createAclUser({...createAclUserRequest}).then((data) => {
        // Message.success('添加权限成功')
        this.searchTimeout = setTimeout(() => {
          this.getActUser();
          if(role===1) {
            this.setState({popupManageVisible: false})
          }else if(role==0){
            this.setState({popupAuthVisible: false})
          }else if(role==2){
            this.setState({popupAuditVisible: false})
          }else if(role==3){
            this.setState({popupUedAuditVisible: false})
          }else if(role==4){
            this.setState({popupContentGroupReviewVisible: false})
          }
        }, 200);
      }).catch(onRequestError)
    }
  }

  deleteAcl = (item,role,last) =>{
    if (last=="last"&&this.state.defaultAuditValue.length==1) {
      Message.error("审核流管控开启需至少选择1个排期配置审核对象,如果真的想把最后一个换掉请先增加一个！")
    }else{
      let {resourceId, pageId} = this.state;
      let deleteAclUserRequest = {
        pageId,
        resourceId,
        role,
        userId:item.record.userId
      }
      deleteAclUser({...deleteAclUserRequest}).then((data) => {
        // Message.success('删除权限成功')
        this.getActUser();
      }).catch(onRequestError)
    }

  }

  onSearch = (keyword,role) =>{ //通过关键字查用户
    // var reg = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]+$");
    // if(keyword!="" && !reg.test(keyword)){
    //   Message.error('不能输入特殊字符');
    //   return;
    // }
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if(keyword){
        var reg = new RegExp('^[A-Za-z0-9\u4e00-\u9fa5]+$');
        if(keyword!='' && !reg.test(keyword)){
          Message.error('不能输入特殊字符');
          return;
        }
        queryUserByKeyWord(keyword).then((data) => {
          const dataSource = data.map(v => ({
            value: v.empId,
            label: `${v.lastName}_${v.empId}`,
            record:v
          }));
          if (role === 1) {
            this.setState({
              popupManageVisible: true,
              manageDataSource: dataSource
            })
          } else if (role === 0) {
            this.setState({
              popupAuthVisible: true,
              authDataSource: dataSource
            })
          } else if (role === 2) {
            this.setState({
              popupAuditVisible: true,
              auditDataSource: dataSource
            })
          } else if (role === 3) {
            this.setState({
              popupUedAuditVisible: true,
              uedAuditDataSource: dataSource
            })
          } else if (role === 4) {
            this.setState({
              popupContentGroupReviewVisible: true,
              contentGroupReviewDataSource: dataSource
            })
          }
          // this.setState({manageDataSource:dataSource,authDataSource:dataSource});
        }).catch(onRequestError)
      }else{
        if(role===1) {
          this.setState({popupManageVisible: false})
        }else if(role===0){
          this.setState({popupAuthVisible: false})
        }else if(role===2){
          this.setState({popupAuditVisible: false})
        }else if(role===3){
          this.setState({popupUedAuditVisible: false})
        }else if(role===4){
          this.setState({popupContentGroupReviewVisible: false})
        }
      }
    }, 800);
  }

  // 商品集ID检验
  checkDataSources = (rule, value, callback) => {
    if (value.length>3) {
      callback('选品集ID输入最多10个');
    } else {
      callback();
    }
  }

  closePop =() => {
    this.setState({
      popupManageVisible:false,
      popupAuthVisible:false,
      popupAuditVisible:false,
      popupUedAuditVisible:false,
      popupContentGroupReviewVisible:false
    })
  }


  // v：开还是关，reviewType：1、2、3，分别对应审核流，UED，内容组
  changeReview = (v,reviewType) =>{
    if(v==1){
      this.setReview(v,reviewType);
    }else{
      Dialog.confirm({
        title: '是否关闭',
        onOk: () => this.setReview(v,reviewType)
      });
    }
  }

  // v：开还是关，reviewType：1、2、3，分别对应审核流，UED，内容组
  setReview = (v,reviewType) =>{
    let {resourceId, pageId} = this.state;
    let reviewRequestDTO = {
      pageId,
      resourceId,
      reviewOpen: v,
      reviewType
    }
    reviewHandle({...reviewRequestDTO}).then((data) => {
      console.log(data);
      Message.success(`${v == 1 ? '开启' : '关闭'}成功`);
      if (v==0&&reviewType==1) {
        setTimeout(() => {
          // 审核流关闭时将弹窗设为初始状态
          this.setState({dialogOpen:false})
        }, 1000);
      }else if (v==1&&reviewType==1){
        this.setState({dialogOpen:true})
      }
      // 每次关闭之后查询一下当前存在的权限，清空state，防止关闭之后再次打开产生的废数据
      if(v==0){
        this.getActUser()
        this.getAllReviewState()
      }
      this.getReview(reviewType);
    }).catch(onRequestError)
    }


  getReview = (reviewType) =>{
    let {resourceId, pageId} = this.state;
    let reviewRequestDTO = {
      pageId,
      resourceId,
      reviewType
    }
    queryReview({...reviewRequestDTO}).then((data) => {
      // Message.success('删除权限成功')
      if (reviewType == 1) {
        this.setState({reviewOpen: data});
      }else if (reviewType == 2) {
        this.setState({uedAuditOpen: data});
      }else if (reviewType == 3) {
        this.setState({contentGroupReview: data});
      }

    }).catch(onRequestError)
  }

  visibleReview(){
    if (this.state.defaultAuditValue<=0) {
      Message.error("审核流管控开启需至少选择1个排期配置审核对象")
    }else{
      this.setState({dialogOpen:false})
    }
  }

  render() {
    let {resourceName,resourceId, marketPageBreadInfo = {}, curPageBreadInfo = {},pageId} = this.state;
    let {
      manageDataSource, defaultManageValue, popupManageVisible,
      authDataSource, defaultAuthValue, popupAuthVisible,
      auditDataSource, defaultAuditValue, popupAuditVisible,
      uedAuditDataSource, defaultUedAuditOpen, popupUedAuditVisible,
      contentGroupReviewDataSource, defaultContentGroupReviewValue, popupContentGroupReviewVisible,
      pageUrl
        } = this.state;
    return (
      <PageBase.Container className="resource-putin-set-page auth-set-page">
        <div className='auth-layer' onClick={this.closePop}></div>
        <div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item>频道管理</Breadcrumb.Item>
            {/*<Breadcrumb.Item><Link to={marketPageBreadInfo.url}>{marketPageBreadInfo.pageName}</Link></Breadcrumb.Item>*/}
            {/*<Breadcrumb.Item><Link to={marketPageBreadInfo.url}>资源位配置</Link></Breadcrumb.Item>*/}
            {/*<Breadcrumb.Item><Link*/}
            {/*  to={curPageBreadInfo.url}>{curPageBreadInfo.pageName}</Link></Breadcrumb.Item>*/}
            <Breadcrumb.Item><Link style={{textDecoration:'underline'}} to={`${pageUrl}/${pageId}?anchorId=${resourceId}`}>{resourceName}</Link></Breadcrumb.Item>
            <Breadcrumb.Item>权限配置</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <PageWrapper title="权限配置">
          {/*<Row className="set-title-wrap">*/}
          {/*<Col span={8} className='set-title'>权限配置</Col>*/}
          {/*</Row>*/}
          <Form className="putin-set-form" {...formLayout} field={this.field}>
            <FormItem label="资源位管理员："  {...resourcePutInSetItemLayout}>
              <Select aria-label="tag mode" placeholder={'请输入员工工号'} mode="tag" value={defaultManageValue} visible={popupManageVisible} onRemove={(item)=>this.deleteAcl(item,1)} hasArrow={false} onChange={(value,actionType,item)=>this.addAcl(value,actionType,item,1)} onSearch={(value)=>this.onSearch(value,1)} dataSource={manageDataSource} style={{width: 400}} />
            </FormItem>
            <FormItem label="授权投放权限：" {...resourcePutInSetItemLayout}>
              <Select aria-label="tag mode"  placeholder={'请输入员工工号'} mode="tag" value={defaultAuthValue} visible={popupAuthVisible} onRemove={(item)=>this.deleteAcl(item,0)} hasArrow={false}  onChange={(value,actionType,item)=>this.addAcl(value,actionType,item,0)} onSearch={(value)=>this.onSearch(value,0)} dataSource={authDataSource}  style={{width: 400}} />
            </FormItem>
            <FormItem label="审核流管控：" {...resourcePutInSetItemLayout}>
              <RadioGroup name="reviewOpen" onChange={(v)=>this.changeReview(v,1)} value={this.state.reviewOpen}>
                <Radio value={1}>开启</Radio>
                <Radio value={0}>关闭</Radio>
              </RadioGroup>
              {/* {this.state.reviewOpen==1 && } */}
            </FormItem>
            {this.state.reviewOpen==1 &&
              <FormItem label="UED审核：" {...resourcePutInSetItemLayout}>
                <RadioGroup name="uedAuditOpen" onChange={(v)=>this.changeReview(v,2)} value={this.state.uedAuditOpen}>
                  <Radio value={1}>开启</Radio>
                  <Radio value={0}>关闭</Radio>
                </RadioGroup>
                {this.state.uedAuditOpen==1 && <Select aria-label="tag mode"  placeholder={'请输入员工工号'} mode="tag" value={defaultUedAuditOpen} visible={popupUedAuditVisible} onRemove={(item)=>this.deleteAcl(item,3)} hasArrow={false}  onChange={(value,actionType,item)=>this.addAcl(value,actionType,item,3)} onSearch={(value)=>this.onSearch(value,3)} dataSource={uedAuditDataSource}  style={{width: 400}} />}
              </FormItem>
            }
            {this.state.reviewOpen==1 &&
              <FormItem label="内容组审核：" {...resourcePutInSetItemLayout}>
                <RadioGroup name="contentGroupReview" onChange={(v)=>this.changeReview(v,3)} value={this.state.contentGroupReview}>
                  <Radio value={1}>开启</Radio>
                  <Radio value={0}>关闭</Radio>
                </RadioGroup>
                {this.state.contentGroupReview==1 && <Select aria-label="tag mode"  placeholder={'请输入员工工号'} mode="tag" value={defaultContentGroupReviewValue} visible={popupContentGroupReviewVisible} onRemove={(item)=>this.deleteAcl(item,4)} hasArrow={false}  onChange={(value,actionType,item)=>this.addAcl(value,actionType,item,4)} onSearch={(value)=>this.onSearch(value,4)} dataSource={contentGroupReviewDataSource}  style={{width: 400}} />}
              </FormItem>
            }
            {this.state.reviewOpen==1 && !this.state.dialogOpen &&
              <FormItem label="排期配置审核：" {...resourcePutInSetItemLayout}>
                <Select aria-label="tag mode"  placeholder={'请输入员工工号'} mode="tag" value={defaultAuditValue} visible={popupAuditVisible} onRemove={(item)=>this.deleteAcl(item,2,"last")} hasArrow={false}  onChange={(value,actionType,item)=>this.addAcl(value,actionType,item,2)} onSearch={(value)=>this.onSearch(value,2)} dataSource={auditDataSource}  style={{width: 400}} />
              </FormItem>
            }
            <Dialog
              title="审核流管控开启需至少选择1个排期配置审核对象，点击确认进行提交，点击取消则关闭审核流管控。"
              visible={this.state.dialogOpen&&this.state.reviewOpen==1}
              hasMask={true}
              closeable={false}
              footerAlign='center'
              onOk={()=>this.visibleReview()}
              onCancel={()=>{this.changeReview(0,1)}}
            >
             <FormItem label="排期配置审核：" {...resourcePutInSetItemLayout}>
                <Select aria-label="tag mode"  placeholder={'请输入员工工号'} mode="tag" value={this.state.defaultAuditValue} visible={this.state.popupAuditVisible} onRemove={(item)=>this.deleteAcl(item,2)} hasArrow={false}  onChange={(value,actionType,item)=>this.addAcl(value,actionType,item,2)} onSearch={(value)=>this.onSearch(value,2)} dataSource={this.state.auditDataSource}  style={{width: 400}} />
              </FormItem>
            </Dialog>
          </Form>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}

export const LogTimeResourcePutInSet = logTimeComponent(ResourceAuthSet, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ResourceAuthSetPage = permissionAccess(LogTimeResourcePutInSet, async (activityId) => {
  return await ACLPermission(activityId);
})
