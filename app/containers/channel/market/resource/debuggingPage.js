import React, { useState, useEffect } from "react";
import { logTimeComponent, goldLog, track } from "@/utils/aplus";
import { ACLPermission } from "@/containers//PutInPage/request";
import { createStructuredSelector } from "reselect";
import { makePermission } from "../../../App/selectors";
import { queryResourceList, queryChannelPage } from "../request";
import cx from "classnames";
import { permissionAccess } from "@/components/PermissionAccess";
import Mtop from "@ali/universal-mtop";
import { PageWrapper } from "@/components/PageWrapper";
import AMapAddress from "./putPreviewPage/components/AMapAddress";
import {getPageMap, pageMap} from "../common";
import { connect } from "react-redux";
import * as api from "@/utils/api";
import * as qs from "query-string";
import {
  Form,
  Input,
  Field,
  Button,
  Select,
  Radio,
  Table,
  Pagination,
  CascaderSelect,
} from "@alife/next";
const FormItem = Form.Item;
import "./debuggingPage.scss";

const ID = {
  goods: "71709",
  shop: "71707",
};

const API = {
  goods: "mtop.alsc.aladdin.newretail.searchMultiActivityIdGoodsList",
  shop: "mtop.alsc.aladdin.newretail.searchShopAndGoodsList",
};

const COLUMN = {
  goods: [
    { title: "商品名称", dataIndex: "name", width: 200, lock: "left" },
    { title: "商品分类", dataIndex: "categoryId", width: 140 },
    { title: "商品条形码", dataIndex: "id", width: 140 },
    { title: "商户名称", dataIndex: "newRetailShopInfo.name", width: 200 },
    { title: "商户ID", dataIndex: "newRetailShopInfo.id", width: 120 },
    { title: "原价", dataIndex: "originalPrice", width: 100 },
    { title: "现价", dataIndex: "price", width: 100 },
    { title: "库存", dataIndex: "stock", width: 100 },
    { title: "最近30天销量", dataIndex: "monthSales", width: 100 },
  ],
  shop: [
    { title: "商户名称", dataIndex: "name", width: 200, lock: "left" },
    { title: "商户ID", dataIndex: "id", width: 120 },
    { title: "距离(m)", dataIndex: "distance", width: 100 },
    { title: "最近30天销量", dataIndex: "recentOrderNumDisplay", width: 100 },
  ],
};

const resultCOLUMN = {
  static: [
    // { title: "排序", dataIndex: "sort", width: 200 },
    { title: "配置ID", dataIndex: "configId", width: 200 },
    { title: "配置名称", dataIndex: "configName", width: 200 },
    { title: "权重", dataIndex: "weight", width: 200 },
  ],
  goods: [
    // { title: "排序", dataIndex: "sort", width: 100, lock: "left" },
    { title: "商品ID", dataIndex: "itemId", width: 100, lock: "left" },
    // { title: "商品条形码", dataIndex: "itemUpc", width: 200 },
    { title: "商品名称", dataIndex: "itemName", width: 200 },
    { title: "商户名称", dataIndex: "shopName", width: 200 },
    { title: "原价", dataIndex: "originalPrice", width: 200 },
    { title: "现价", dataIndex: "price", width: 200 },
    { title: "库存", dataIndex: "stock", width: 200 },
    { title: "最近30天销量", dataIndex: "monthSale", width: 200 },
    { title: "选品集ID", dataIndex: "poolIds", width: 200 },
    { title: "投放活动ID", dataIndex: "activityId", width: 200 },
    { title: "配置ID", dataIndex: "staticStateData.configId", width: 200 },
    { title: "配置名称", dataIndex: "staticStateData.configName", width: 200 },
    { title: "权重", dataIndex: "staticStateData.weight", width: 200 },
  ],
  shop: [
    // { title: "排序", dataIndex: "sort", width: 100, lock: "left" },
    { title: "门店ID", dataIndex: "storeId", width: 100, lock: "left" },
    { title: "店铺名称", dataIndex: "shopName", width: 200, lock: "left" },
    { title: "距离", dataIndex: "distance", width: 200 },
    { title: "最近30天销量", dataIndex: "monthSale", width: 200 },
    { title: "门店池ID", dataIndex: "poolIds", width: 200 },
    { title: "投放活动ID", dataIndex: "activityId", width: 200 },
    { title: "配置ID", dataIndex: "staticStateData.configId", width: 200 },
    { title: "配置名称", dataIndex: "staticStateData.configName", width: 200 },
    { title: "权重", dataIndex: "staticStateData.weight", width: 200 },
  ],
};

function DebuggingPageContainer(props) {
  const { permission = [] } = props;
  const paramsUrl = qs.parse(props.location.search);
  const [debuggingUrl, setUrl] = useState("about:blank");
  const [logContent, setLogContent] = useState({});
  const [traceId, setTraceId] = useState(0);
  let defaultCheckType = paramsUrl.formPutIn ? "2" : "1";

  const [checkType, setCheckType] = useState(paramsUrl.formChannel ? "3" : defaultCheckType);
  const [emptyDataList, setEmptyDataList] = useState([]);
  const formField = Field.useField();
  const [pageMap, setPageMap] = useState([]);
  const [resourceList, setResourceList] = useState([]);
  const [staticStateDataList, setStaticStateDataList] = useState([]);
  const [goodDataList, setGoodDataList] = useState([]);
  const [shopDataList, setShopDataList] = useState([]);
  const [loading, setLoading] = useState(false);

  const checkSearchActivityCounts = async (
    latitude,
    longitude,
    activityIds,
    activityType
  ) => {
    if (!activityIds) {
      setEmptyDataList([
        {
          activityId: ID[activityType],
          isAll: true,
          activityType,
          latitude,
          longitude,
          time: +new Date(),
        },
      ]);
    } else {
      let list = activityIds.toString().split(",");
      setEmptyDataList(
        list.map((item, index) => ({
          activityId: item,
          activityType,
          latitude,
          longitude,
          time: +new Date(),
        }))
      );
    }
  };

  useEffect(() => {
    window.addEventListener("message", (msg) => {
      if (
        msg.origin.indexOf("tb.ele.me") != -1 ||
        msg.origin.indexOf("tmall.com") != -1
      ) {
        // 获取requestId
        const { data } = msg;
        const parsedData = JSON.parse(data);
        const { type } = parsedData;
        const traceList = parsedData.data;
        const urlPrefix = window.configEnv == "prod" ? "" : "pre-";
        // if (traceList && type == "responseAllGot") {
        //   for (let key in traceList) {
        //     if (traceList.hasOwnProperty(key)) {
        //       let traceIds = traceList[key].map(
        //         (traceItem) => traceItem.traceId
        //       );
        //       if (traceIds.length) {
        //         const reqData = {
        //           traceIds: traceIds,
        //         };
        //         fetch(
        //           `https://${urlPrefix}api-newretail-delivery-platform.ele.alibaba-inc.com/api/meetingTools/getSlsLogList`,
        //           {
        //             method: "POST",
        //             credentials: "include",
        //             headers: {
        //               "Content-Type": "application/json",
        //             },
        //             body: JSON.stringify(reqData),
        //           }
        //         ).then((res) => {
        //           res.json().then((data) => {
        //             if (data.code == 200) {
        //               setTraceId(Object.keys(data.data.logList)[0]);
        //               setLogContent({ ...logContent, ...data.data.logList });
        //             }
        //           });
        //         });
        //       }
        //     }
        //   }
        // }
      }
    });
  }, []);

  const getLogs = async () => {
    if(checkType === '1'){
      formField.validateCallback((err, values)=>{
        if(!err){
          const {
            url,
            latitude,
            longitude,
            activityId,
            userId,
            userType,
          } = values;
          const newUrl = `${url.trim()}${
            url.includes("?") ? "&" : "?"
          }nr-debug&latitude=${latitude.trim()}&longitude=${longitude.trim()}&activityId=${activityId.trim()}&userId=${userId}&elemeNewUser=${
            userType === "2"
          }&newRetailNewUser=${userType === "3"}`;
          setUrl(`${newUrl}&${Date.now()}`);
        }
      });
    }
    if(checkType === '2'){
      formField.validateCallback((err, values)=>{
        if(!err){
          const {
            latitude,
            longitude,
            activityId,
            activityType,
          } = values;
          checkSearchActivityCounts(latitude, longitude, activityId, activityType);
        }
      });
    }
    if (checkType === "3") {
      formField.validateCallback((err, values) => {
        if (!err) {
          try {
            setLoading(true);
            queryChannelPage({
              pageId: values.pageId,
              resourceId: values.resourceId,
              longitude: values.longitude,
              latitude: values.latitude,
              userId: values.userId,
              userType: values.userType,
              bizChannel: values.bizChannel,
              version: values.version,
            }).then((res) => {
              setLoading(false);
              setStaticStateDataList(res.staticStateDataList);
              setGoodDataList(res.goodDataList);
              setShopDataList(res.shopDataList);
            });
          } catch (error) {
            setLoading(false);
            console.log(error);
          }
        }
      });
      setLoading(false);
    }
  };

  // const logList = [];
  // for (let logItem in logContent) {
  //   if (logContent.hasOwnProperty(logItem)) {
  //     logList.push({
  //       activityId: logItem,
  //       logs: logContent[logItem],
  //     });
  //   }
  // }

  const premissionChannelManageMenu =
    permission.filter((v) => v.menuName === "channel_manage")[0] || {};

  const listToTree = (list = []) => {
    const loop = (subMenus = []) => {
      return subMenus.map((v) => {
        return {
          label: v.menuTitle,
          value: v.menuUrl,
          children: v.subMenus.length > 0 ? loop(v.subMenus) : [],
        };
      });
    };

    return loop(list) || [];
  };

  const transChannelTree = listToTree(premissionChannelManageMenu.subMenus);

  useEffect(() => {
    getPageMap().then((res) => {
      console.log(res);
      setPageMap(res);
    }).catch(api.onRequestError);
  }, [])

  const getResourceData = (pageId) => {
    let queryResource = {
      page: 1,
      size: 80,
      query: {
        pageId,
      },
    };
    queryResourceList(queryResource)
      .then((result) => {
        setResourceList(
          (result.resourceList || []).map((v) => ({
            label: v.name,
            value: v.resourceId,
          }))
        );
      })
      .catch(api.onRequestError);
  };

  useEffect(() => {
    if (paramsUrl.formPutIn) {
      const putInData = JSON.parse(paramsUrl.formPutIn);
      formField.setValues({
        activityType: putInData.poolType.toString() === "1" ? "goods" : "shop",
        activityId: putInData.id.toString(),
      });
    }
    if(paramsUrl.formChannel){
      const paramsData = JSON.parse(paramsUrl.formChannel);
      // const curPage = pageMap.filter((v) => v.pageId === paramsData.pageId)[0];
      // getResourceData(curPage.pageId);
      formField.setValues({
        pageUrl: paramsData.prePageUrl,
        pageId: paramsData.pageId,
        resourceId: paramsData.resourceId
      });
    }
  }, []);

  return (
    <PageWrapper title="投放预览">
      <div
        className={cx({
          "debugging-page-container": true,
          rowCls: checkType === "1",
        })}
      >
        <Form field={formField} style={{ width: 500 }}>
          <FormItem>
            <Radio.Group
              defaultValue={checkType}
              onChange={(value) => {
                setCheckType(value);
                setStaticStateDataList([]);
                setGoodDataList([]);
                setShopDataList([]);
              }}
            >
              <Radio value="1">会场预览</Radio>
              <Radio value="2">投放活动预览</Radio>
              <Radio value="3">资源位预览</Radio>
            </Radio.Group>
          </FormItem>
          {checkType === "1" && (
            <FormItem label="会场地址" required>
              <Input name="url"></Input>
            </FormItem>
          )}
          {checkType === "2" && (
            <FormItem label="召回类型">
              <Select name="activityType" defaultValue="goods">
                <Select.Option value="goods">商品</Select.Option>
                <Select.Option value="shop">门店</Select.Option>
              </Select>
            </FormItem>
          )}
          {checkType !== "3" && (
            <FormItem required label="投放活动ID">
              <Input name="activityId"></Input>
            </FormItem>
          )}
          {checkType === "3" && (
            <>
              <FormItem hidden>
                <Input name="pageId" />
              </FormItem>
              <FormItem required label="频道">
                <CascaderSelect
                  style={{ width: "500px" }}
                  dataSource={transChannelTree}
                  name="pageUrl"
                  onChange={(value) => {
                    const curPage = pageMap.filter((v) => v.url === value)[0];
                    if (curPage) {
                      getResourceData(curPage.pageId);
                      formField.setValue("pageId", curPage.pageId);
                      formField.setValue('resourceId', '');
                    }
                  }}
                />
              </FormItem>
            </>
          )}
          {checkType === "3" && (
            <FormItem required label="资源位名称">
              <Select
                style={{ width: "500px" }}
                dataSource={resourceList}
                name="resourceId"
              />
            </FormItem>
          )}
          <AMapAddress
            onChange={(value) => {
              formField.setValues({
                longitude: value.lng,
                latitude: value.lat,
              });
            }}
            position={{
              lat: formField.getValue("latitude"),
              lng: formField.getValue("longitude"),
            }}
          />
          <FormItem label="经度" required>
            <Input name="longitude" defaultValue="121.38228"></Input>
          </FormItem>
          <FormItem label="纬度" required>
            <Input name="latitude" defaultValue="31.232975"></Input>
          </FormItem>
          <FormItem label="用户标识">
            <Radio.Group name="userMark">
              <Radio value="1">ID</Radio>
              <Radio value="2">类型</Radio>
            </Radio.Group>
          </FormItem>
          {formField.getValue("userMark") === "1" && (
            <FormItem label="用户ID">
              <Input name="userId"></Input>
            </FormItem>
          )}
          {formField.getValue("userMark") === "2" && (
            <FormItem label="用户类型">
              <Select
                style={{ width: "500px" }}
                name="userType"
                defaultValue="1"
              >
                <Select.Option value="1">未登录</Select.Option>
                <Select.Option value="2">饿了么新</Select.Option>
                <Select.Option value="3">零售新</Select.Option>
                <Select.Option value="4">老客</Select.Option>
              </Select>
            </FormItem>
          )}
          {checkType === "3" && (
            <FormItem label="渠道">
              <Radio.Group
                defaultValue="1"
                name="bizChannel"
                dataSource={[
                  {
                    label: "不限",
                    value: "1",
                  },
                  {
                    label: "饿了么ios",
                    value: "2",
                  },
                  {
                    label: "饿了么android",
                    value: "3",
                  },
                  {
                    label: "支付宝",
                    value: "4",
                  },
                  {
                    label: "手淘",
                    value: "5",
                  },
                  {
                    label: "微信小程序",
                    value: "6",
                  },
                ]}
              />
            </FormItem>
          )}
          {checkType === "3" && (
            <FormItem label="版本">
              <Input style={{ width: "200px" }} name="version" />
            </FormItem>
          )}
          <FormItem label="">
            <Button onClick={getLogs}>确认</Button>
          </FormItem>
        </Form>
        {checkType === "1" && (
          <>
            <div className="preview-window">
              <p>预览界面</p>
              <iframe
                src={debuggingUrl}
                style={{ height: "100%", width: "100%" }}
              />
            </div>
            {/*<div className="ret-log">*/}
            {/*  <p>日志列表</p>*/}
            {/*  {logList.map((logItem) => {*/}
            {/*    return (*/}
            {/*      <div className="log-container">*/}
            {/*        {logItem.logs.map((log, index) => (*/}
            {/*          <div className="log-item">*/}
            {/*            {index + 1} - 时间：{log.time}，内容：{log.content}*/}
            {/*          </div>*/}
            {/*        ))}*/}
            {/*      </div>*/}
            {/*    );*/}
            {/*  })}*/}
            {/*</div>*/}
          </>
        )}
        {checkType === "2" && (
          <>
            <div className="result-list">
              {emptyDataList.map((dataItem, index) => {
                let { activityId, activityType, count, time } = dataItem || {};
                return (
                  <DataTable
                    userId={formField.getValue("userId")}
                    elemeNewUser={formField.getValue("userType") === "2"}
                    newRetailNewUser={formField.getValue("userType") === "3"}
                    key={time + "_" + index}
                    {...dataItem}
                  />
                );
              })}
            </div>
          </>
        )}
        {checkType === "3" && (
          <div>
            <p>静态资源位信息</p>
            <Table
              loading={loading}
              dataSource={staticStateDataList}
              key="configId"
            >
              {resultCOLUMN.static.map((item) => (
                <Table.Column {...item} />
              ))}
            </Table>
            <p>商品信息</p>
            <Table loading={loading} dataSource={goodDataList} key="itemId">
              {resultCOLUMN.goods.map((item) => (
                <Table.Column {...item} />
              ))}
            </Table>
            <p>门店信息</p>
            <Table loading={loading} dataSource={shopDataList} key="storeId">
              {resultCOLUMN.shop.map((item) => (
                <Table.Column {...item} />
              ))}
            </Table>
          </div>
        )}
      </div>
    </PageWrapper>
  );
}

function DataTable(props) {
  const [shopData, setShopData] = useState([]); //商户列表数据
  const [shopLoading, setShopLoading] = useState(false); //商户列表加载状态
  const [shopRankId, setShopRankId] = useState(""); //商户列表当前rankId
  const [shopTotal, setShopTotal] = useState(0); //商户列表可召回总数
  const [goodsData, setGoodsData] = useState([]); //商品列表数据
  const [goodsLoading, setGoodsLoading] = useState(false); //商品列表加载状态
  const [goodsRankId, setGoodsRankId] = useState(""); //商品列表当前rankId
  const [goodsTotal, setGoodsTotal] = useState(0); //商品列表可召回总数
  const {
    activityId,
    activityType,
    latitude,
    longitude,
    isAll,
    userId,
    elemeNewUser,
    newRetailNewUser,
  } = props;

  useEffect(() => {
    getShopData(1);
    if (activityType == "goods") {
      getGoodsData(1);
    }
  }, []);

  const getShopData = async (currentPage) => {
    const ret = await fetch(
      `https://h5.ele.me/restapi/bgs/division/get_division_by_location?latitude=${latitude}&longitude=${longitude}`,
      {
        method: "GET",
        credentials: "include",
      }
    );
    const { city } = await ret.json();

    Mtop.config("prefix", "");
    Mtop.config("mainDomain", "ele.me");
    Mtop.config(
      "subDomain",
      window.configEnv == "prod" ? "shopping" : "ppe-shopping"
    );
    Mtop.config(
      "pageDomain",
      location.hostname.indexOf("ele.me") !== -1 ? "ele.me" : ""
    );
    Mtop.request({
      api: API.shop,
      v: "1.0",
      dataType: "json",
      data: {
        activityId: activityId,
        limit: 20,
        latitude,
        longitude,
        shopRankId,
        cityId: city.id,
        offset: 20 * (currentPage - 1),
        countOnly: false,
        moduleId: "adminTool",
        comeFrom: "adminTool",
        bizChannel: "mobile.default.default",
        debug: window.configEnv !== "prod",
        userId,
        elemeNewUser: elemeNewUser,
        newRetailNewUser: newRetailNewUser,
      },
    })
      .then(({ data }) => {
        let { newRetailShopList: list, meta, totalCount } = data || {};
        if (list && list.length) {
          setShopData(list);
          setShopTotal(totalCount);
          setShopRankId(meta.rankId);
        } else {
          setShopData([]);
          setShopTotal(0);
          setShopRankId("");
        }
        setShopLoading(false);
      })
      .catch(() => {
        setShopData([]);
        setShopTotal(0);
        setShopRankId("");
        setShopLoading(false);
      });
  };

  const getGoodsData = async (currentPage) => {
    const ret = await fetch(
      `https://h5.ele.me/restapi/bgs/division/get_division_by_location?latitude=${latitude}&longitude=${longitude}`,
      {
        method: "GET",
        credentials: "include",
      }
    );
    const { city } = await ret.json();

    Mtop.config("prefix", "");
    Mtop.config("mainDomain", "ele.me");
    Mtop.config(
      "subDomain",
      window.configEnv == "prod" ? "shopping" : "ppe-shopping"
    );
    Mtop.config(
      "pageDomain",
      location.hostname.indexOf("ele.me") !== -1 ? "ele.me" : ""
    );
    Mtop.request({
      api: API.goods,
      v: "1.0",
      dataType: "json",
      data: {
        activityIds: JSON.stringify([activityId]),
        limit: 20,
        goodsRankId,
        latitude,
        longitude,
        cityId: city.id,
        offset: 20 * (currentPage - 1),
        countOnly: false,
        dataModel: "goodsandshop",
        moduleId: "adminTool",
        comeFrom: "adminTool",
        bizChannel: "mobile.default.default",
        debug: window.configEnv !== "prod",
      },
    })
      .then(({ data }) => {
        let { newRetailGoodsList: list, meta, totalCount } = data || {};
        if (list && list.length) {
          setGoodsData(list);
          setGoodsTotal(totalCount);
          setGoodsRankId(meta.rankId);
        } else {
          setGoodsData([]);
          setGoodsTotal(0);
          setGoodsRankId("");
        }
        setGoodsLoading(false);
      })
      .catch(() => {
        setGoodsData([]);
        setGoodsTotal(0);
        setGoodsRankId("");
        setGoodsLoading(false);
      });
  };

  const onShopChange = (currentPage) => {
    setShopLoading(true);
    getShopData(currentPage);
  };

  const onGoodsChange = (currentPage) => {
    setGoodsLoading(true);
    getGoodsData(currentPage);
  };

  return (
    <div className="result-item">
      <p>{`召回结果(${isAll ? "全部供给" : `投放ID:${activityId}`})`}</p>
      <div className="result-num">
        <span>召回商户数：{shopTotal}</span>
        {activityType == "goods" && <span>{`召回商品数：${goodsTotal}`}</span>}
      </div>
      <p>明细数据</p>
      <div>
        <p>商户列表</p>
        <Table dataSource={shopData} loading={shopLoading}>
          {COLUMN.shop.map((item) => (
            <Table.Column {...item} />
          ))}
        </Table>
        <Pagination
          onChange={onShopChange}
          total={shopTotal}
          pageSize={20}
          className="page-wrapper"
        />
      </div>
      {activityType == "goods" && (
        <div>
          <p>商品列表</p>
          <Table dataSource={goodsData} loading={goodsLoading}>
            {COLUMN.goods.map((item) => (
              <Table.Column {...item} />
            ))}
          </Table>
          <Pagination
            onChange={onGoodsChange}
            total={goodsTotal}
            pageSize={20}
            className="page-wrapper"
          />
        </div>
      )}
    </div>
  );
}

export const LogDebuggingPage = logTimeComponent(
  DebuggingPage,
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const ExportedDebuggingPage = permissionAccess(
  LogDebuggingPage,
  async (activityId) => {
    return await ACLPermission(activityId);
  }
);

const DebuggingPage = connect(
  createStructuredSelector({
    permission: makePermission(),
  })
)(DebuggingPageContainer);

export { DebuggingPage };
