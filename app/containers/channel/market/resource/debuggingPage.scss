.debugging-page-container {
  display: flex;
  flex-direction: column;
  &.rowCls {
    flex-direction: row;
  }
  .preview-window {
    margin: 0 10px;
    height: 500px;
    width: 375px;
  }

  .ret-log {
    display: flex;
    flex: 1;
    overflow: auto;
    flex-direction: column;

    .log-container {
      margin-bottom: 10px;
      display: flex;
      flex-direction: column;

      .log-item {
        overflow-wrap: break-word;
        margin-bottom: 10px;
      }
    }
  }

  .result-list {
    margin-left: 10px;
    display: flex;
    flex: 1;
    overflow: auto;
    flex-direction: column;

    .result-item {
      .result-num {
        display: flex;
        flex-direction: column;
      }
    }

    .page-wrapper {
      margin-top: 8px;
    }
  }
}

.page-wrapper {
  margin-top: 8px;
}
