import React, { useState, useEffect } from "react";

import {
  Breadcrumb,
  Form,
  Radio,
  Grid,
  Icon,
  Button,
  Input,
  Select,
  DatePicker,
  Tab,
  Dropdown,
  Menu,
  Divider,
  Message,
  Dialog,
  Balloon,
  CascaderSelect,
} from "@alife/next";
import moment from "moment";
import { PageBase } from "@/containers/base";
import { PageWrapper } from "@/components/PageWrapper";
import { DialogBtn, LinkButton, GoldLogLink } from "@/components/Button";
import { ACLPermission } from "@/containers//PutInPage/request";
import { AccessBtn } from "@/components/Button/AccessBtn";
import { manageList, setTableCol, filterForm } from "@/components/Table";
import { formatTimeStamp } from "@/utils/time";
import { onRequestError, getCityList } from "@/utils/api";
import { logTimeComponent, goldLog, track } from "@/utils/aplus";
import { isAllNumber } from "@/utils/validators";
import { doesFormHasErrors } from "@/utils/formTool";
import { filterData } from "@/utils/filterDataByKey";
import { momentToTimeStamp } from "@/utils/time";
import { withoutCopy } from "@/utils/others";
import { permissionAccess } from "@/components/PermissionAccess";
import { Link } from "react-router-dom";
import {
  queryConfigList,
  updateConfigState,
  copyDelevery,
  validateAclSchedule,
  validateAclResource,
  recallReview,
  queryReviewRecorde,
  queryIsReviewer,
} from "../request";
import {  filtrationAlpha } from "../common";
import "./viewConfigure.scss";
import { LogTimePutInPage } from "@/containers/decoration/activity/edit";
import { getPutInDetail } from "@/containers/PutInPage/request";
import { stateMap } from "@/home/<USER>/common";
import * as api from "@/utils/api";

const FormItem = Form.Item;
const { Row, Col } = Grid;

const timeFormat = "YYYY-MM-DD HH:mm:ss";

const tabGoldLinks = [
  "/selection_kunlun.CHANNEL-MANAGE-view-configure.self-can-view-tab",
  "/selection_kunlun.CHANNEL-MANAGE-view-configure.self-charge-tab",
  "/selection_kunlun.CHANNEL-MANAGE-view-configure.self-applied-tab",
];

export const statusMap = [
  { value: "", name: "全部", color: "#FF9500" },
  { value: 1, name: "编辑中", color: "#CCCCCC" },
  { value: 2, name: "审核中", color: "#CCCCCC" },
  { value: 3, name: "未通过", color: "#FF9500" },
  { value: 5, name: "未开始", color: "#FF9500" },
  { value: 6, name: "进行中", color: "#00CCAA" },
  { value: 7, name: "已结束", color: "#CCCCCC" },
  { value: 8, name: "已下线", color: "#CCCCCC" },
];

export const statusAlphaMap = [
  { value: "", name: "全部", color: "#FF9500" },
  { value: 2, name: "审核中", color: "#CCCCCC" },
  { value: 3, name: "未通过", color: "#FF9500" },
  { value: 5, name: "未开始", color: "#FF9500" },
  { value: 6, name: "进行中", color: "#00CCAA" },
  { value: 7, name: "已结束", color: "#CCCCCC" },
  { value: 8, name: "已下线", color: "#CCCCCC" },
];

const StatusText = ({ ...params }) => {
  const { value, record, pagination = {}, pageId, resourceId } = params;
  const group = statusMap.filter((s) => s.value == value);
  const status = group.length > 0 ? group[0] : "";
  const [nowResult, setNowResult] = useState(0);
  const [rejected, setRejected] = useState({});

  useEffect(() => {
    fetchData();
  }, []);
  const fetchData = () => {
    if (value == 3 && record.reviewInstanceId) {
      let configRequestDTO = {
        page: pagination.page,
        size: pagination.size,
        query: record.reviewInstanceId,
      };
      queryReviewRecorde({ ...configRequestDTO })
        .then((data) => {
          setNowResult(1);
          setRejected(data);
        })
        .catch(onRequestError);
    } else if (value == 2 && record.approvalUrl) {
      let baseQueryRequestDTO = {
        resourceId,
        pageId,
      };
      queryIsReviewer(baseQueryRequestDTO).then((result) => {
        if (result) {
          setNowResult(2);
        }
      });
      if (record.isShowReviewUrl == 1) {
        setNowResult(2);
      }
    }
  };

  if (nowResult == 1) {
    return (
      <>
        <Balloon
          align="r"
          trigger={
            <div
              id="review-status-text-withdraw"
              className="review-status-text"
            >
              <i style={{ backgroundColor: status.color }}></i>
              {status.name}
            </div>
          }
          triggerType={["hover"]}
          children={`驳回理由:${rejected.rejectReason}，驳回人员:${rejected.rejectName}(${rejected.rejectNo})，请尽快修改`}
          popupContainer="review-status-text-withdraw"
          closable={false}
        ></Balloon>
      </>
    );
  } else if (nowResult == 2) {
    return (
      <>
        <Balloon
          align="r"
          trigger={
            <div
              id="review-status-text-withdraw"
              className="review-status-text"
            >
              <i style={{ backgroundColor: status.color }}></i>
              {status.name}
            </div>
          }
          triggerType={["hover"]}
          children={
            <a target="_blank" href={record.approvalUrl}>
              审核详情
            </a>
          }
          popupContainer="review-status-text-withdraw"
          closable={false}
        ></Balloon>
      </>
    );
  } else {
    return (
      <>
        <div className="review-status-text">
          <i style={{ backgroundColor: status.color }}></i>
          {status.name}
        </div>
      </>
    );
  }
};

/**操作列 */
const OptionCol = ({ ...params }) => {
  const {
    record,
    reload,
    tabidx,
    pageId,
    resourceId,
    resourceType,
    posId,
    curPageBreadInfo,
    location = {},
    history,
    showViewData,
    pageUrl
  } = params;
  const { id, status, userOrigin } = record;
  // const { url } = curPageBreadInfo;
  const url = pageUrl;
  const { search = "" } = location;
  const copyDelever = () => {
    // let params = {
    //   pageId,
    //   resourceId,
    //   configId: id,
    //   type: '1',
    // }
    // copyDelevery({...params}).then((data) => {
    //   Message.success('success')
    //   reload()
    // }).catch(onRequestError)
  };

  const operateConfigState = ({ type }) => {
    let params = {
      pageId,
      resourceId,
      locationId: posId,
      configId: id,
      type, // 1删除 2停止
    };
    updateConfigState({ ...params })
      .then((data) => {
        Message.success(`${type == 1 ? "删除" : "停用"}成功`);
        reload();
      })
      .catch(onRequestError);
  };

  const getPermission = async () => {
    let scheduleAclRequest = {
      pageId,
      resourceId,
      locationId: posId,
      configId: id,
    };
    try {
      let data = await validateAclSchedule({ ...scheduleAclRequest });
      return data.redirectUrl;
    } catch (error) {
      onRequestError(error);
    }
  };

  const deleteConfirmMap = {
    1: "确认删除该活动?",
    2: "确认停用该活动?",
  };

  const deleteConfig = (type) => {
    track("clickEvent", [
      `/selection_kunlun.CHANNEL-MANAGE-view-configure.${
        type == 1 ? "delete" : "stop-useing"
      }-config-btn`,
      `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}&configId=${id}`,
    ]);
    Dialog.confirm({
      title: deleteConfirmMap[type],
      onOk: () => operateConfigState({ type }),
    });
  };
  const editConfig = () => {
    if (status === 2) {
      Dialog.show({
        footerAlign: "center",
        footerActions: ["cancel", "ok"],
        closeable: "esc,mask",
        title: "重新编辑会撤回当前审批，编辑后请再次提交，是否继续？",
        isFullScreen: true,
        onOk: () => {
          let configRequestDTO = {
            pageId,
            resourceId,
            locationId: posId,
            configId: id,
          };
          recallReview({ ...configRequestDTO })
            .then((data) => {
              track("clickEvent", [
                "/selection_kunlun.CHANNEL-MANAGE-view-configure.ediit-config-btn",
                `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}&configId=${id}&resourceType=${resourceType}`,
              ]);
              history.replace(`${url}/edit/${id}${search}`);
              reload();
            })
            .catch(onRequestError);
        },
      });
    } else {
      track("clickEvent", [
        "/selection_kunlun.CHANNEL-MANAGE-view-configure.ediit-config-btn",
        `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}&configId=${id}&resourceType=${resourceType}`,
      ]);
      history.replace(`${url}/edit/${id}${search}`);
    }
  };

  const recall = () => {
    Dialog.confirm({
      title: "是否撤回",
      onOk: () => operateRecall(),
    });
  };

  const operateRecall = () => {
    let configRequestDTO = {
      pageId,
      resourceId,
      locationId: posId,
      configId: id,
    };
    recallReview({ ...configRequestDTO })
      .then((data) => {
        reload();
        history.go(0);
      })
      .catch(onRequestError);
  };

  return (
    <div className="option-col">
      <Link to={`${url}/detailConfig/${id}${search}`}>
        <Button
          type="primary"
          text
          onClick={() =>
            track("clickEvent", [
              "/selection_kunlun.CHANNEL-MANAGE-view-configure.view-config-btn",
              `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}&configId=${id}`,
            ])
          }
        >
          查看
        </Button>
      </Link>
      {filtrationAlpha.includes(resourceId) ? (
        ""
      ) : (
        <>
          {userOrigin != "same_city" && userOrigin != "shen_bing" && (
            <>
              {(status == 1 ||
                status == 2 ||
                status == 5 ||
                status == 6 ||
                status == 3) && (
                <>
                  <Divider direction="ver" />
                  <AccessBtn
                    getPermission={getPermission}
                    btnText={"编辑"}
                    callback={() => {
                      editConfig();
                    }}
                  />
                </>
              )}
            </>
          )}
          {(status == 6 || status == 7) && showViewData == "true" && (
            <>
              <Divider direction="ver" />
              <Button
                text
                onClick={() => {
                  console.log(1);
                  history.replace(
                    `/report/schedule?resourceId=${resourceId}&scheduleId=${id}`
                  );
                }}
              >
                投放数据
              </Button>
            </>
          )}
          {userOrigin != "same_city" && (
            <>
              <Divider direction="ver" />
              <Dropdown
                trigger={
                  <span>
                    更多&nbsp;
                    <Icon type="arrow-down" size="xxs" />
                  </span>
                }
                triggerType={["hover"]}
                cache
              >
                <Menu>
                  <Menu.Item>
                    <div className="menu-item">
                      <AccessBtn
                        getPermission={getPermission}
                        btnText={"复制"}
                        callback={() => {
                          track("clickEvent", [
                            "/selection_kunlun.CHANNEL-MANAGE-view-configure.copy-config-btn",
                            `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}&configId=${id}`,
                          ]);
                          history.replace(`${url}/edit/-${id}${search}`);
                        }}
                      />
                    </div>
                  </Menu.Item>
                  <Menu.Item>
                    <div className="menu-item">
                      <AccessBtn
                        getPermission={getPermission}
                        btnText={"删除"}
                        callback={() => deleteConfig(1)}
                      />
                    </div>
                  </Menu.Item>
                  {status == 6 && (
                    <Menu.Item>
                      <div className="menu-item">
                        <AccessBtn
                          getPermission={getPermission}
                          btnText={"停用"}
                          callback={() => deleteConfig(2)}
                        />
                      </div>
                    </Menu.Item>
                  )}
                  {status == 2 && (
                    <Menu.Item>
                      <div className="menu-item">
                        <AccessBtn
                          getPermission={getPermission}
                          btnText={"撤回"}
                          callback={() => recall()}
                        />
                      </div>
                    </Menu.Item>
                  )}
                  <Menu.Item>
                    <Link to={`${url}/log/${search}`}>
                      <div className="menu-item">
                        <Button
                          type="primary"
                          text
                          onClick={() =>
                            track("clickEvent", [
                              "/selection_kunlun.CHANNEL-MANAGE-view-configure.operate-log-btn",
                              `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}&configId=${id}`,
                            ])
                          }
                        >
                          操作记录
                        </Button>
                      </div>
                    </Link>
                  </Menu.Item>
                </Menu>
              </Dropdown>
            </>
          )}
        </>
      )}
    </div>
  );
};

const defaultColType = { title: "" };

let columns = [
  {
    title: "配置ID",
    dataIndex: "id",
    width: "10%",
    cell: (value) => `ID：${value}`,
  },
  { title: "配置名称", dataIndex: "name", width: "10%" },
  // {title: '投放ID', dataIndex: 'activityId', width: '10%', cell: (value) => (!!value ? `ID：${value}` : '')},
  {
    title: "投放ID",
    dataIndex: "activityId",
    width: "8%",
    cell: (value) => {
      let result = "";
      if (value) {
        let newValue =
          JSON.parse(value).length > 0
            ? JSON.parse(value).filter((v) => v && v.trim())
            : JSON.parse(value);
        result = `${newValue}`;
        // result = <div>
        //   {newValue.map((v) => {
        //     return <a href={`#/putIn/list/detail/${v}`}>{v}</a>
        //   })}
        // </div>
      }
      return <>{result}</>;
    },
  },
  {
    title: "投放时段",
    dataIndex: "startTime",
    width: "15%",
    cell: (value, rowIndex, record) => {
      return (
        <>
          {`${formatTimeStamp(record.startTime, timeFormat)}~`}
          <br />
          {`${formatTimeStamp(record.endTime, timeFormat)}`}
        </>
      );
    },
  },
  { title: "投放城市", dataIndex: "city", width: "8%" },
  {
    title: "状态",
    dataIndex: "status",
    width: "8%",
    cell: function (value, rowIndex, record) {
      return (
        <StatusText
          value={value}
          record={record}
          {...this.props}
          pagination={this.state.pagination}
        />
      );
    },
  },
  { title: "权重", dataIndex: "weight", width: "6%" },
  {
    title: "操作时间",
    dataIndex: "updateTime",
    width: "15%",
    cell: (value, rowIndex, record) =>
      formatTimeStamp(record.updateTime || record.createTime, timeFormat),
  },
  {
    title: "操作人",
    dataIndex: "updateUser",
    width: "6%",
    cell: (value, rowIndex, record) => record.updateUser || record.createUser,
  },
  {
    title: "操作",
    width: "14%",
    cell: function (value, rowIndex, record) {
      return <OptionCol record={record} reload={this.reload} {...this.props}  />;
    },
  },
];

function getCol(type) {
  return { ...defaultColType, ...type };
}

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
  labelAlign: "center",
  labelTextAlign: "center",
};

const formLayout = {
  style: {
    margin: "20px 0px 10px 0px",
  },
};

/**检测活动id格式 */
const checkActivityId = function (rule, value, callback) {
  const errors = isAllNumber(value);
  if (errors.length && value) {
    callback("活动ID格式错误");
  } else {
    callback();
  }
};

function PutInForm({
  field,
  searchData,
  reload,
  curPageBreadInfo,
  location = {},
  pageId,
  resourceId,
  posId,
  pageUrl
}) {
  const isAlpha = filtrationAlpha.includes(resourceId) || false;
  // const { url } = curPageBreadInfo;
  const { search = "" } = location;
  const [cityDataSource, setCityDataSource] = useState([]);
  const isSuperMarket = location.pathname.includes("supermarketChannel");

  const onSearch = () => {
    track("clickEvent", [
      "/selection_kunlun.CHANNEL-MANAGE-view-configure.search-btn",
      `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}`,
    ]);
    field.validate((e) => {
      if (!doesFormHasErrors(e)) {
        searchData();
      }
    });
  };
  useEffect(() => {
    getCity();
  }, []);

  const getCity = async () => {
    try {
      let request = getCityList();
      request.then((resp) => {
        setCityDataSource(resp || []);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };
  return (
    <Form {...formLayout} field={field}>
      <Row>
        <Col>
          <FormItem
            label="配置ID:"
            {...formItemLayout}
            validator={checkActivityId}
          >
            <Input placeholder="请输入配置ID" name="configId" />
          </FormItem>
        </Col>
        <Col span={6}>
          {isAlpha ? (
            <FormItem
              label="活动计划ID:"
              {...formItemLayout}
              validator={checkActivityId}
            >
              <Input
                placeholder="请输入活动计划ID"
                name="promotionActivityId"
              />
            </FormItem>
          ) : (
            <FormItem
              label="投放ID:"
              {...formItemLayout}
              validator={checkActivityId}
            >
              <Input placeholder="请输入活动ID" name="activityId" />
            </FormItem>
          )}
        </Col>
        <Col>
          <FormItem label="配置名称:" {...formItemLayout}>
            <Input placeholder="请输入配置名称" name="name" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="状态:" {...formItemLayout}>
            <Select
              name="status"
              placeholder="请选择状态"
              style={{ width: "100%" }}
            >
              {(isAlpha ? statusAlphaMap : statusMap).map((v) => {
                return (
                  <Select.Option key={v.value} value={v.value}>
                    {v.name}
                  </Select.Option>
                );
              })}
            </Select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={6}>
          <FormItem label="开始时间:" {...formItemLayout}>
            <DatePicker
              style={{ width: "100%" }}
              name="startTime"
              showTime
              placeholder="请选择开始时间"
            />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="结束时间:" {...formItemLayout}>
            <DatePicker
              style={{ width: "100%" }}
              showTime={{ defaultValue: moment("23:59:59", "HH:mm:ss", true) }}
              name="endTime"
              placeholder="请选择结束时间"
            />
          </FormItem>
        </Col>
        {isAlpha ? (
          <Col span={6}>
            <Button
              type="primary"
              style={{ marginLeft: "80px" }}
              onClick={onSearch}
            >
              查询
            </Button>
          </Col>
        ) : (
          <Col span={6}>
            <FormItem label="父关联id:" {...formItemLayout}>
              <Input placeholder="请输入父关联id" name="parentId" />
            </FormItem>
          </Col>
        )}
        <>
          {isSuperMarket && (
            <Col span={6}>
              <FormItem label="投放城市:" {...formItemLayout}>
                <CascaderSelect
                  style={{ width: "200px" }}
                  multiple
                  dataSource={cityDataSource}
                  name={"cityIdList"}
                />
              </FormItem>
            </Col>
          )}
        </>
      </Row>
      {isAlpha ? (
        ""
      ) : (
        <Row>
          <Col span={2}></Col>
          <Col span={22}>
            <FormItem className="ope-btns">
              <Button type="normal" onClick={onSearch}>
                查询
              </Button>
              &nbsp;&nbsp;
              {/*<Button type="normal" onClick={reload}>重置</Button>&nbsp;&nbsp;*/}
              <LinkButton
                to={`${pageUrl}/edit${search}`}
                onClick={() =>
                  track("clickEvent", [
                    "/selection_kunlun.CHANNEL-MANAGE-view-configure.putin-btn",
                    `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}`,
                  ])
                }
              >
                去投放
              </LinkButton>
            </FormItem>
          </Col>
        </Row>
      )}
    </Form>
  );
}

const PutInFilterForm = filterForm(PutInForm, [
  "configId",
  "startTime",
  "name",
  "status",
  "activityId",
  "endTime",
  "parentId",
  "promotionActivityId",
  "cityIdList",
]);
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(
  PutInTable,
  PutInFilterForm,
  async function (search) {
    let { query, page, size } = search;
    let { tabidx, resourceId, posId, pageId } = this.props;
    if (query.hasOwnProperty("cityIdList") && query.cityIdList == "") {
      query.cityIdList = [];
    }
    let params = {
      page,
      size,
      query: {
        ...query,
        pageId,
        resourceId,
        step: 0,
        posId,
        type: tabidx,
      },
    };
    try {
      const data = await queryConfigList({ ...params });
      return data;
    } catch (error) {
      onRequestError(error);
    }
  }
);

class ViewConfigure extends PageBase {
  constructor(props) {
    super(props);
    let {
      pageId = "",
      resourceId = "",
      resourceName = "",
      posId = "",
      posName = "",
      resourceType = "",
      showViewData,
    } = this.query;
    const isAlpha = filtrationAlpha.includes(resourceId);
    if (isAlpha) {
      columns[2].title = "活动计划ID";
      columns[2].dataIndex = "promotionActivityId";
    }
    const {pathname=''} = this.location;
    this.state = {
      isAlpha,
      tabidx: 1, // 1我创建的、0可查看的、2：已申请权限的
      pageId, // 页面id
      resourceType,
      marketPageBreadInfo: {}, //商超频道页面包屑信息
      curPageBreadInfo: {}, //当前页页面包屑信息
      resourceId, // 资源id
      resourceName,
      posId, // 坑位id
      posName, // 坑位名称
      showViewData,
      pageUrl: pathname.substring(0, pathname.lastIndexOf('/'))
    };
  }

  async componentDidMount() {
    let { pageId, resourceId } = this.state;
    const { withPermission } = await this.props.getPermission({
      pageId,
      resourceId,
    });
    if (!withPermission) return;

    this.refs.manageList.fetchData({});
  }

  onTabChange = (tabidx) => {
    let { pageId, resourceId, posId } = this.state;
    track("clickEvent", [
      tabGoldLinks[parseInt(tabidx)],
      `pageId=${pageId}&resourceId=${resourceId}&posId=${posId}`,
    ]);
    this.setState(
      {
        tabidx,
      },
      () => {
        this.refs.manageList.fetchData({ page: 1 });
      }
    );
  };

  render() {
    let {
      tabidx,
      pageId,
      resourceId,
      posId,
      posName,
      marketPageBreadInfo = {},
      curPageBreadInfo = {},
      resourceName,
      resourceType,
      showViewData,
      pageUrl,
    } = this.state;
    return (
      <PageBase.Container className="view-configure-page">
        <div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item>频道管理</Breadcrumb.Item>
            <Breadcrumb.Item>
              <Link to={`${pageUrl}/${pageId}?anchorId=${resourceId}`}>
                {resourceName}
              </Link>
            </Breadcrumb.Item>
            <Breadcrumb.Item>{posName}</Breadcrumb.Item>
            <Breadcrumb.Item>查看配置</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <PageWrapper title="查看配置">
          <ManageList
            ref="manageList"
            tabidx={tabidx}
            pageId={pageId}
            resourceId={resourceId}
            resourceType={resourceType}
            posId={posId}
            showViewData={showViewData}
            curPageBreadInfo={curPageBreadInfo}
            location={this.location}
            history={this.history}
            pageUrl={pageUrl}
          >
            <Tab shape="wrapped" onChange={this.onTabChange} activeKey={tabidx}>
              <Tab.Item title="我创建的" key={1}></Tab.Item>
              {this.state.isAlpha ? (
                ""
              ) : (
                <Tab.Item title="已申请的" key={2}></Tab.Item>
              )}
              <Tab.Item title="可查看的" key={0}></Tab.Item>
            </Tab>
          </ManageList>
        </PageWrapper>
      </PageBase.Container>
    );
  }
}

export const LogTimeViewConfigure = logTimeComponent(
  ViewConfigure,
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const ViewConfigurePage = permissionAccess(
  LogTimeViewConfigure,
  async (params) => {
    return await validateAclResource(params);
  }
);
