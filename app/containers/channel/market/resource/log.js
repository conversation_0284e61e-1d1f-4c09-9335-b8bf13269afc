import React,{useState, useEffect} from 'react'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {FilterForm, FilterItem} from '@/components/filter'
import {Link, withRouter} from 'react-router-dom';
import useTableQuery from '@/components/useTableQuery'
import {parseQuery} from '@/containers/decoration/common';
import {ACLAccess} from '@/components/ACLAccess'
import {Button, Input, Select, Table, Pagination, Tab, Dialog,Breadcrumb} from '@alife/next';
import './index.scss';
import {getQueryString} from "../../../../utils/others";
// import {pageMap} from "../common";
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {getOperationList} from "../request";

function doQuery(params, page = 1, size = 10) {
  const filter = {
    page,
    size,
    query: {
      ...params
    }
  }
  return getOperationList(filter)
    .then((result) => {
      const {rows, total} = result;
      return {
        list: rows,
        total: +total,
      }
    })
}

function MarketLog({history, location, match}) {
  const route = {
    history, location, match
  }
  let newHistory = {
    ...history,
    location: {
      ...location,
      query: parseQuery(route)
    }
  };
  const params = {
    resourceId: getQueryString("resourceId"),
    posId: getQueryString("posId")
  }
  if (match.params.configId) {
    params.configId = match.params.configId;
  }

  const query = useTableQuery(newHistory, doQuery, {...params}, true);
  const {bindInput, setField, filterInput} = query
  // const changeTab = (val) => {
  //   setField({ showType: +val + 1 }, true)
  // }
  useEffect(() => {
    query.confirmInput;
  }, [0])
  const pageId = getQueryString("pageId");
  const posName = getQueryString("posName");
  const resourceId = getQueryString("resourceId");
  const resourceName = getQueryString("resourceName");
  // const curPage =  pageMap.filter(v => v.pageId == pageId)[0];
  const pageUrl = location.pathname.slice(0,-4);
  const pageName = '';
  return (
    <div className="container">
      <div className="title">
        <Breadcrumb>
          <Breadcrumb.Item>频道管理</Breadcrumb.Item>
          <Breadcrumb.Item><Link style={{textDecoration:'underline'}} to={`${pageUrl}/${pageId}?anchorId=${resourceId}`}>{resourceName}</Link></Breadcrumb.Item>
          <Breadcrumb.Item>{posName}</Breadcrumb.Item>
          <Breadcrumb.Item>操作记录</Breadcrumb.Item>
        </Breadcrumb>
      </div>
      <h2 style={{margin:'16px 0 30px 20px'}}>操作记录</h2>
      <QueryResult query={query} />
    </div>
  )
}


function QueryResult ({ query }) {
  return (
    <div style={{width:'900px',margin:'0 auto'}}>
      <Table dataSource={query.result.list} rowProps={(record) => ({ style: +record.yn === 1 ? { opacity: 0.5 } : {} })}>
        <Table.Column title="操作记录ID" dataIndex="id" width='100px'/>
        <Table.Column title="操作内容" dataIndex="handleType" width='200px' />
        <Table.Column title="变更时间" dataIndex="createTime"  width='100px' cell={(value) => formatTimeStamp(value, FORMAT.TIMETwo)}/>
        <Table.Column title="变更人" dataIndex="createUser"  width='100px'/>
      </Table>
      <br />
      <Pagination {...query.pagination} />
    </div>
  )
}

function effectTime (record) {
  return record.effectStartDate + ' - ' + record.effectEndDate
}


export const LogTimePutInPage = logTimeComponent(withRouter(MarketLog), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MarketLogPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})



