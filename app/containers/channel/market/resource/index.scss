.resource-index,.resource {
  background-color: #fafafa;
  .nav-wrapper {
    position: fixed;
    height:40px;
    line-height:40px;
    background-color:#fbfbfb;
    z-index:2;
    width:100%;
    nav{
      margin:15px 0 0 15px;
    }
  }
  .next-breadcrumb .next-breadcrumb-text > a{
    text-decoration: underline;
  }
  .next-tabs-bar {
    position: fixed !important;
    width: 100%;
    z-index:2;
    background-color:#fbfbfb !important;
  }
  .next-tabs-content {
    .next-tabs-tabpane {
      padding-top: 50px;
    }
  }
  & > .body {
    margin-top: 40px;
    .resource-title{
      font-family: PingFangSC-Regular !important;
      font-size: 18px;
      color: #333333;
      margin:15px !important;
    }
    .resource-list {
      .resource-item {
        width: 928px;
        margin: 35px auto;
        &-title {
          font-family: PingFangSC-Semibold;
          font-size: 20px;
          color: #333333;
          margin-bottom: 10px;
          position: relative;
          .btn-putin-panel{
            position:absolute;
            right:0px;
            top:0px;
          }
          .btn-putin{
            color:#333;
            background: #FFFFFF;
            border: 1px solid #CCCCCC;
            border-radius: 4px;
            border-radius: 4px;
            width:80px;
            height:26px;
            line-height:24px;
            padding:0;
            margin-right:5px;
          }
        }
        .resource-row {
          background: #FFFFFF;
          box-shadow: 2px 2px 9px 0 rgba(0, 0, 0, 0.10);
          border-radius: 8px;
          border-radius: 8px;
          padding: 20px;
          overflow: hidden;
          &-left, &-right {
            float: left;
            width: 49%;
            margin-right: 1%;
          }
          &-left {
            border: 1px solid #ECECEC;
            & > .img-exp {
              text-align: center;
              p {
                margin: 0;
                height: 40px;
                line-height: 40px;
                border-bottom: 1px solid #ECECEC;
                font-family: PingFangSC-Medium;
                font-size: 14px;
                color: #333333;
                padding: 0 20px;
                text-align: left;
                a {
                  font-family: PingFangSC-Regular;
                  font-size: 14px;
                  color: #FF7C4D;
                  float: right;
                  cursor: pointer;
                }
              }
              .wrap {
                height: 120px;
                overflow:hidden;
                align-items:flex-start;
                display: -webkit-flex;
                justify-content:center;
                &.center{
                  align-items:center;
                }
              }
            }

          }
          &-right{
            .next-table th .next-table-cell-wrapper{
              padding:9px 16px;
            }
          }
        }
      }
      .load-wrapper{
        text-align:center;
        height:500px;
        margin-top:200px;
        font-weight:20px;
      }
    }
    .resource-body{
      padding-bottom:20px;
    }
  }
}
.reviewButton{
  position: fixed !important;
  bottom: 48%;
  right: 50px;
  background: #b4d145;
}
.resource-index{
  .resource {
    .nav-wrapper{
      display:none;
    }
  }
}

.resource-edit{
  .next-breadcrumb .next-breadcrumb-text > a{
    text-decoration: underline;
  }
  .related-channel{
    .next-form-item-label{
      width:200px;
      min-width:200px;
      flex:0 0 auto;
      overflow: hidden;
    }
  }
  .item-advertise{
    .video-list{
      border:1px solid #EBEBEB;
      padding:12px 20px;

      .inner {
        max-height: 400px;
        overflow-y: auto;
      }
      .data-empty{
        margin:10px;
        height:30px;
      }
      .next-btn-text{
        color: #999999;
        margin-right: 40px;
      }
      .next-row{
        margin-top:15px;
      }
      .bg-video{
        width:56px;
        height:56px;
        background-color: #EBEBEB;
        margin-top: 10px;
        cursor: pointer;
        position: relative;
        .head{
          width:100%;
          height:100%;
        }
        .icon{
          position: absolute;
          bottom:4px;
          right:4px;
          z-index: 100;
          width:16px;
          height:16px;
        }
      }
      .btn-video{
        margin-top: 10px;

        &.current {
          color: #FF7000;
          border: 1px solid #FF7000;
        }
      }
      .title{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #333333;
        width:168px;
        line-height: 20px;
        //overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 4px;
        padding-left: 0;
      }
      .position{
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #999999;
        line-height: 20px;
      }
    }
  }
}


.tooltip-text{
 &>p{
   font-family: PingFangSC-Regular;
   font-size: 12px;
   color: #333333;
 }
}

.page-setting{
  .ops{
      margin:20px 0 0 30px;
    &>button{
      margin-right:10px;
    }
  }
}

.video-dialog{
  &.next-overlay-wrapper .next-overlay-inner{
    border-radius: 4px;
  }
  .next-dialog-body{
    padding:0;
    background-color: #000;
  }
}
.rights::before{
  margin-right: 4px;
  content: "*";
  color: #FF2D4B;
}
.top-background{
  margin-bottom: 4px;
  display: flex;
  position: relative;
  left: 93px;
  .next-form-item-label{
    display: inline-block;
  }
  .next-form-item-control{
    display: inline-block;
    width: 85%;
  }
  & .next-form-item-control{
    display: flex;
  }
  .topImageItem{
    width: 70px;
    margin-right: 12px;
    margin-bottom: 12px;
    display: inline-block;

    .topImageItem-img-border{
      width: 70px;
      height: 70px;
      padding: 5px;
      border-radius: 4px;
      cursor: pointer;
      &.active-true{
        padding: 4px;
        border: 1px solid #FF7000;
        background-color: rgba(255,112,0,0.04);
      }
      .topImageItem-img{
        position: relative;
        width: 60px;
        height: 60px;
        border-radius: 4px;
        background-repeat: no-repeat;
        background-size: 100%;
        .next-radio-wrapper{
          position: absolute;
          bottom: 4px;
          right: 4px;
          line-height: 20px;
        }
      }
    }
    .topImageItem-title{
      line-height: 20px;
    }
  }
}

.commodity-data-source{
  padding: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  // margin-bottom: 4px;
  // display: flex;
  // position: relative;
  // left: 93px;
  // .next-form-item-label{
  // display: inline-block;
  // }
  // .next-form-item-control{
  //   display: inline-block;
  //   width: 85%;
  // }
  // & .next-form-item-control{
  //   display: flex;
  // }
}

.multiple-tab-title{
  text-align: right;
  padding-right: 12px;
}
.multiple-tab{
  padding: 0 0 20px 0;
  // padding-left: 25px;
  .next-btn{
    color: rgb(255, 112, 0) !important;
  }
  .next-card{
    margin-bottom: 18px;
    .next-card-header{
      .next-btn{
        margin-left: 10px;
        color: rgba(255, 112, 0,0.8);
        &:hover{
          color: rgb(255, 112, 0);
        }
      }
    }
    .next-card-content-container{
      padding-bottom: 0;
    }
  }
  .order-num {
    width: 18px;
    height: 18px;
    line-height: 16px;
    text-align: center;
    display: inline-block;
    border: 1px solid #ff7000;
    background-color: #ffffff;
    font-family: AlibabaSans102-Bold;
    font-size: 14px;
    font-style: normal;
    color: #ff7000;
    border-radius: 50%;
    margin-right: 8px;
  }
  .next-card-content-container{
    position: relative;
    .dragger{
      display: inline-block;
      background-image: url('https://img.alicdn.com/imgextra/i4/O1CN01kHbt29255909UHmF5_!!6000000007474-2-tps-48-48.png');
      background-repeat: no-repeat;
      background-size: 100%;
      width: 30px;
      height: 30px;
      position: absolute;
      left: 10px;
      top: 0;
      cursor: move
    }
  }
  .tabCard{
    &.drop-over-downward::after {
      display: inline-block;
      height: 36px;
      line-height: 36px;
      width: 100%;
      margin: -10px 0 5px 2px;
      color: #ff7000;
      border: dashed 2px #ff7000;
      border-radius: 4px;
      content: '移动到此处';
      text-align: center;
      z-index: 100;
      justify-self: flex-end;
    }

    &.drop-over-upward::before {
      display: block;
      height: 36px;
      line-height: 36px;
      width: 100%;
      margin: -10px 0 5px 0px;
      color: #ff7000;
      border: dashed 2px #ff7000;
      border-radius: 4px;
      content: '移动到此处';
      text-align: center;
      z-index: 100;
      animation-name: 'fade';
      animation-duration: 1s;
      justify-self: flex-end;
    }
  }
}
.multiple-schema,.single-schema{
  .multiple-schema-title,.single-schema-title{
    text-align: right;
  }
  .alsc-form-tab{
    border:1px solid #ccc;
    //margin-bottom: 10px;
    //padding:10px;
    padding:25px 10px 0 10px;
  }
  .multiple-schema-item{
    //border: 1px solid #ccc;
    margin-top: 10px;
    position:relative;
    .btn-del{
      position: absolute;
      right:10px;
      top: 0;
    }
    &.multiple-schema-item0{
      //opacity: 0;
      visibility: hidden;
    }
    .multiple-schema-index{
      width: 18px;
      height: 18px;
      line-height: 16px;
      text-align: center;
      display: inline-block;
      background-color: #ffffff;
      font-family: AlibabaSans102-Bold;
      font-size: 14px;
      font-style: normal;
      color: #ff7000;
      border-radius: 50%;
      margin-right: 8px;

    }
    .multiple-schema{
        border: none !important;
    }
  }
}

.single-schema{
  .legend{
    display: none;
  }
  .alsc-form-tab{
    width:600px;
    margin-bottom: 10px;
  }
}

.rank-title{
  margin-left: 120px;
}

.rank-tab {
  margin-left: 200px;
  //padding: 10px;

  & > span {
    padding: 5px;
    border: 1px solid #ccc;
    display: inline-block;
    margin-right: 5px;
    cursor: pointer;

    &.current {
      border: 1px solid #FF7000;
      //background-color: #FF7000;
      color: #FF7000;
    }
  }
}

.rank-group{
  position: relative;
  top: -18px;
}

.rank-item-none {
  display: none;
}

.rank-item {
  display: block;
}
.rank-item,.rank-item-none{
  margin-left: 200px;
  padding: 10px;
  border: 1px solid #ccc;
  margin-bottom: 10px;
  min-width: 810px;
  &-detail{
    margin-left: 10px;
    padding: 10px;
    border: 1px solid #ccc;
    margin-bottom: 10px;
    min-width: 810px;
  }
  .next-form-item-label{
    width: 200px;
    max-width: 200px;
  }
  .tab-type{
    line-height:36px;
  }

  .detail-block {
    border: 1px solid #ccc;
    margin-bottom: 5px;
    padding:5px;
  }
}
.rank-position-group{
  margin-top: 10px;
}
.rank-position{
  line-height: 35px;
}
.rank-schedule{
  margin-bottom: 10px;

  &-detail{
    margin-top: 10px;
  }
}


.dynamic-datasource-form-popup{
  background-color: red;
  .next-cascader-menu-wrapper{
    min-width: 200px !important;
    width: 100% !important;
  }
}
