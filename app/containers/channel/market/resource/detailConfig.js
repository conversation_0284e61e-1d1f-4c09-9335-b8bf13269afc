import {<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>} from '@alife/next'
import React,{useState,useEffect} from 'react'
import './index.scss'
import SimpleTable from '@/components/SimpleTable'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {Redirect, Link, withRouter, Switch} from 'react-router-dom'
import {isArray,isString} from "@/utils/validators";
import {permissionAccess} from '@/components/PermissionAccess';
import QRCode  from 'qrcode.react';
import {PageBase} from '@/containers/base';
import {getEleCategory, getParentConfigList, getConfigList, getScheduleTemplate, queryPosConfig,queryIsReviewer,queryAlgorithmStrategyCenterList} from "../request";
import { getPutInDetail } from '@/containers/PutInPage/request'
import * as api from '@/utils/api';
import * as apiL from "@/adator/api";
import {formatTimeStamp,FORMAT} from '@/utils/time';
import {
  jumpTypeMap,
  deliveryChannelsMap,
  weekMap,
  insertMap,
  skipIconContentTypeMap,
  positionMap,
  pageMap,
  versionDetailGroup,
  resourceTypeMap,
  poolTypeMap,
  showQrCodeMap,
  filtrationAlpha
} from '../common';
import {getQueryString} from "@/utils/others";
import {getTreeName, itemPicQualityScoreSet} from "@/home/<USER>/common";
import {optionMap} from "@/home/<USER>/common/map";
import RankDetail from "@/containers/channel/market/comps/rankDetail";
import PoolOrShopSelectDetails from './PoolOrShopSelectDetails';
import { getWidget } from '@/utils/Form/src/utils';
import widgets from '@/utils/Form/src/components/widgets';
import { env } from '@/config';
const {Row, Col} = Grid;
const PeopleOrderTypeTextMap = {
  '0': '全部',
  '1': '新零售新客',
  '2': '新零售老客',
  '3': '医药新客'
}

const gameTypeMap = {
  "multi_box": "券你省省",
  "sign": "签到",
  "share": "助力"
}

/**
 * 供给来源 hidden 字段判断逻辑
 * @param {boolean | string | undefined} text 传进来的 hidden
 * @param {schema} form 传进来的Schema
 * @returns true：展示 false：不展示
 */
function getVisibleItemForForm(text,form) {
  if (typeof text == 'string') {
    let indexsigh = text.indexOf('!');
    let indexEqual = text.indexOf('=');
    let lastIndexEqual = text.lastIndexOf('=');
    let name = indexsigh > 0 ? text.substring(0,indexsigh).trim() : text.substring(0,indexEqual).trim();
    let value = text.substring(lastIndexEqual+1).trim()
    return indexsigh > 0 ? form[name] != value : (indexEqual > 0 ? form[name] == value : true);
  }else if (typeof text == 'boolean') {
    return !text;
  }else if (text == undefined) {
    return true;
  }else{
    return false;
  }
}

const PeopleList = ["47","43",'136'];
const topImgResourceGroup = ['100003049', '3049', '100009039', '9039','100007009','7009', '100250003', '250003'];
export default class DetailConfig extends PageBase{
  constructor(props) {
    super(props);
    // let { pageId = '', resourceId = '', resourceName = '', posId = '', posName = '', resourceType = '' } = this.query;
    let configId = this.params ? this.params.configId : props.configId;
    const {pathname=''} = this.location || {};
    this.state = {
      resourceId: getQueryString("resourceId"),
      resourceName: getQueryString("resourceName"),
      pageId: getQueryString("pageId"),
      posName: getQueryString("posName"),
      posId: getQueryString("posId"),
      configId,
      resourceType: getQueryString("resourceType"),
      scheduleInfo: {},
      putInActivityIdConfig: [],
      retatedData: [],
      isReviewer:false,
      categoryDataSource:[],
      pageUrl: (configId) ? pathname.substring(0, pathname.lastIndexOf('/', pathname.lastIndexOf("/") - 1)) : pathname.substring(0, pathname.lastIndexOf('/'))
    }
  }

  componentDidMount() {
    this.getDetailConfig();
    // this.getSchema();
    this.fetchCategory();
  }
  // 查看是否是审核人
  getqueryIsReviewer = ()=>{
    let {resourceId, pageId} = this.state;
    let baseQueryRequestDTO = {
      resourceId,
      pageId
    }
    queryIsReviewer(baseQueryRequestDTO)
      .then((result) => {
        if(result){
          this.setState({
            isReviewer:true
          })
        }
        // Message.success('获取成功')
      }).catch(api.onRequestError);
  }

  getSchema = () =>{
    let {resourceId, posId} = this.state;
    let baseQueryRequestDTO = {
      resourceId,
      posId
    }
    getScheduleTemplate(baseQueryRequestDTO)
      .then((result) => {
        let scheduleTemplate = JSON.parse(result);
        this.setState({
          scheduleTemplate,
          showDataConfig: scheduleTemplate["showDataConfig"],
          showAddConfig: scheduleTemplate["showAddConfig"],
          showPutIn: scheduleTemplate["showPutIn"],
          retailConfig: scheduleTemplate["retailConfig"],
          showCategory: scheduleTemplate["showCategory"],
          showSupChannel: scheduleTemplate["supChannel"],
          showSign:scheduleTemplate["showSign"],
        isCategoryCenter: scheduleTemplate["isCategoryCenter"] || false,
          poolTypeConfig: scheduleTemplate["poolTypeConfig"] || '',
        }, () => {
          localStorage.setItem("SchemaMap", result);
          if (this.state.isCategoryCenter) {
            this.getAllQueryAlgorithmStrategyCenterList();
          }
          const {scheduleInfo} = this.state
          if (scheduleTemplate.oversupply && scheduleTemplate.oversupply.tabKey && scheduleInfo[scheduleTemplate.oversupply.tabKey] && scheduleInfo[scheduleTemplate.oversupply.tabKey].length > 0) {
            this.fetchSkuCategory()
          }
        });

        // Message.success('获取成功')
      }).catch(api.onRequestError);
  }

  getDetailConfig = () => {
    let {resourceId, posId, configId} = this.state;
    let baseQueryRequestDTO = {
      // resourceId,
      // posId,
      configId
    }
    queryPosConfig(baseQueryRequestDTO)
      .then((result) => {
        let scheduleInfo = JSON.parse(result.scheduleInfo);
        this.setState({
          scheduleInfo,
          resourceId:scheduleInfo.resourceId.toString(),
          posId: scheduleInfo.locationId,
          pageId: scheduleInfo.pageId.toString()
        },()=>{
          if (!filtrationAlpha.includes(resourceId)) {
            this.getSchema();
          }
          if (scheduleInfo.putInActivityId && scheduleInfo.putInActivityId.length > 0) {
            let putInActivityIdSet = scheduleInfo.putInActivityId.filter(v => v != "");
            putInActivityIdSet.map((v) => {
              this.getDetailInfo(v);
            })
          }
          if(scheduleInfo.relatedSup){
            this.getRetatedData();
          }
          if (scheduleInfo.ext_aoi_codes) {
            this.getAoi(scheduleInfo.ext_aoi_codes);
          }
          if (scheduleInfo.pureDataSourceList && scheduleInfo.pureDataSourceList.length > 0) {
            this.fetchSkuCategory()
          }
        })
        this.getqueryIsReviewer();
      }).catch(api.onRequestError);
  }

  async getDetailInfo (activityId) {
    let {scheduleInfo,putInActivityIdConfig} = this.state;
    try {
      const detail = await getPutInDetail(activityId);
      // scheduleInfo.deliveryChannels =  detail.deliveryChannels;
      // scheduleInfo.citys =  detail.citysRep;
      // scheduleInfo.userTagGroupIdsList =  detail.userTagGroupIdsList;
      // scheduleInfo.poolIdsRep =  detail.poolIdsRep;
      let poolIdsRep = '';
      switch(detail.limitType) {
        case 1:
          poolIdsRep = ['门店聚类'];
          break;
        case 2:
          poolIdsRep = ['商品聚类'];
          break;
        default:
          poolIdsRep = detail.poolIdsRep;
          break;
      }
      putInActivityIdConfig.push({
        activityId:activityId,
        deliveryChannelList: detail.deliveryChannelList,
        deliveryChannelLabel: detail.deliveryChannelLabel,
        citys: detail.citysRep,
        griddings: detail.griddingsRep || [],
        userTagGroupIdsList: detail.userTagGroupIdsList,
        poolIdsRep: poolIdsRep,
      })
      this.setState({ scheduleInfo,putInActivityIdConfig },()=>{
        console.log(this.state);
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  getWorkDay = (value) =>{
    if (value) {
      let result = (value.length == 7) ? "整周" : value.map((v) => weekMap[v]).join(",");
      return result
    } else {
      return value;
    }
  }

  getCity = (value) => {
    if (value) {
      let result = value.map((v) => v.label).join(",");
      return result;
    } else {
      return '--';
    }
  }

  traverseChannel = (ret, list) => {
    list.forEach((listItem) => {
      ret.push({
        value: listItem.value,
        label: listItem.label
      });
      if (listItem.children) {
        this.traverseChannel(ret, listItem.children);
      }
    })
  }

  getChannelList = (list) =>{
    const { channelListMap } = JSON.parse(localStorage.getItem("deliveryData"));
    let ret = [];
    let strRet = [];
    if (list.length) {
      this.traverseChannel(ret, channelListMap);
      list.forEach((listItem) => {
        ret.forEach((retItem) => {
          if(listItem === retItem.value) {
            strRet.push(retItem.label);
          }
        })
      })
    }
    return strRet.length > 0 ? strRet.join(',') : '--';

  }

  getChannelLabel = (value) =>{
    let { labelListMap } = JSON.parse(localStorage.getItem("deliveryData"))
    if (value.length) {
      let result = labelListMap.filter((item) => value.indexOf(item.value) >= 0).map((item) => item.label).join(' , ')
      return result;
    } else {
      return '--';
    }
  }

  getRetatedData = () => {
    let {resourceId, pageId,step} = this.state;
    let queryResourceRequestDTO = {
      resourceId,
      pageId,
    }
    if (resourceId == "100025023" || resourceId == "25023") {
      // 此处关联的金刚位排期与之前关联上级配置不同。云主题没有上级，只是虚构关联所以需要按服务端查询。
      queryResourceRequestDTO = {
        page: 1,
        size: 50,
        query: {
          pageId: resourceId == "100025023" ? "100003000" : "3000",
          resourceId: resourceId == "100025023" ? "100003070" : "3070",
          step,
          posId: resourceId == "100025023" ? "944" : "957",
          type: "0",
          status: 6
        },
      };
      getConfigList(queryResourceRequestDTO)
        .then((result) => {
          this.setState({
            retatedData: result.rows.map((item) => {
              return {
                value: "" + item.id,
                label: "(" + item.id + ") " + item.name,
              };
            }),
          });
        })
        .catch(api.onRequestError);
    } else {
      getParentConfigList(queryResourceRequestDTO)
        .then((result) => {
          this.setState(
            {
              retatedData: result,
            },
            () => {
              console.log(this.state);
            }
          );
        })
        .catch(api.onRequestError);
    }
  }


  handleField = (key, info, fieldInfo) => {
    let result;
    let value = info[key];
    if (value || value === 0) {
      switch (key){
        case 'workDay':
          result = this.getWorkDay(value);
          break;
        case 'beginTime':
        case 'endTime':
        case 'redPacketStartTime':
        case 'redPacketEndTime':
          result = formatTimeStamp(value,FORMAT.TIMETwo);
          break;
        case 'jumpType':
          result = jumpTypeMap[value];
          break;
        case 'timeSelection':
          const timeRangesValue = (info['timeRanges'] && info['timeRanges'].length > 0) ? info['timeRanges'].join("，") : '';
          result = (value != '1') ? '全天' : timeRangesValue;
          break;
        case 'activityType':
          if (info["activityIds"]) {
            const activityTypeValue = (info['activityType'] == '1') ? '商品' : '门店';
            result = `${activityTypeValue}:${info["activityIds"]}`;
          } else {
            result = "--";
          }
          break;
        case 'insertInto':
          result = insertMap[parseInt(value)];
          break;
        case 'showShopWindow':
        case 'keyMarkEnable':
          result = value==="0" ? '否' : '是';
          break;
        case 'dynamicOpenEnable':
          result = value==="0" ? '不需要' : '需要';
          break;
        case 'skipIconContentType':
          result = skipIconContentTypeMap[value];
          break;
        case 'acceptMode':
          if(fieldInfo['x-ui-valueLabel']&&fieldInfo['x-ui-valueLabel'][value]) {
            result = fieldInfo['x-ui-valueLabel'][value];
          } else {
            result = value=="0" ? '会场承接页' : '业态金刚内页';
          }
          break;
        case 'position':
          if(fieldInfo['x-ui-valueLabel']&&fieldInfo['x-ui-valueLabel'][value]) {
            result = fieldInfo['x-ui-valueLabel'][value];
          } else {
            result = positionMap[parseInt(value)];
          }
          break;
        case 'shopActivityIds':
        case 'goodsActivityIds':
          result = value.join(',');
          break;
        case 'sceneType':
          result = value == '1' ? '营销卡' : value == '0' ? '商品卡' : '免配卡';
          break;
        case 'gameType':
          // result = value == 'share' ? '助力' : '签到';
          result = gameTypeMap[value];
          break;
        case 'meetingPlaceUrl':
        case 'jumpUrl':
          try {
            let landingUrl;
            if (value) {
              let newJumpUrl = decodeURIComponent(value.trim());
              landingUrl = newJumpUrl.substr(1 + newJumpUrl.indexOf("="));
            }
            result = (
              <div>
                <p>{value}</p>
                <QRCode value={landingUrl} size={120} fgColor="#000000" />
              </div>
            );
          } catch (e) {
            console.error(e);
            return (
              <div>
                <p>{value}</p>
              </div>
            );
          }
          break;
        default:
          if (fieldInfo["type"] === 'atom') {
            // TODO: 支持将 schema 中的配置传入控件
            const Widget = getWidget(
              fieldInfo,
              fieldInfo["x-ui-widget"],
              widgets
            );
            if (!Widget) {
              return <></>;
            }
            console.log('env for atom widget', env)
            return (
              <Widget
                isPreview
                value={value}
                schema={fieldInfo}
                env={env === "ppe" ? "pre" : env}
              />
            );
          }
          if (fieldInfo["x-ui-widget"] == "img-upload" && value != "") { //图片类型，展示图片
            let {width, height} = fieldInfo["x-ui-validate"];
            result = <img src={value} style={{width: '100%', height: 'auto'}}/>;
          } else if (fieldInfo["x-ui-widget"] == "img-upload-new" && value != "") {
            let {url} = JSON.parse(value);
            result = <img src={url} style={{width: '100%', height: 'auto'}}/>;
          } else if (fieldInfo['enum'] && fieldInfo['enumNames']) {
            let enumIndex = fieldInfo['enum'].indexOf(value);
            if (enumIndex > -1) {
              return fieldInfo['enumNames'][enumIndex]
            }else{
              return value
            }
          } else if (fieldInfo['x-ui-valueLabel'] && fieldInfo['x-ui-valueLabel'][value]) {
            return fieldInfo['x-ui-valueLabel'][value];
          } else if (
            (fieldInfo["x-ui-widget"] == "select" ||
              fieldInfo["x-ui-widget"] == "checkboxes"||
              fieldInfo["x-ui-widget"] == "CheckboxesWidget") &&
            fieldInfo["type"] == "array"
          ) {
            //下拉框多选的功能
            return fieldInfo["items"]["enum"]
              .map((v, index) => {
                return (
                  value.includes(v) && fieldInfo["items"]["enumNames"][index]
                );
              })
              .filter((v) => !!v)
              .join(",");
          } else if(fieldInfo['x-ui-widget'] == 'dynamicCascadeSelectWidget'){
            result = value.map((item)=>{
              return <>
              <span>ID:{item.value}</span>&nbsp;&nbsp;
              <span>名称:{item.label}</span>
              <br></br>
            </>
            })
          } else if(fieldInfo['x-ui-widget'] == 'poolOrShopSelectWidget'){
            result = <PoolOrShopSelectDetails value={value} schema={fieldInfo} />
          } else if (fieldInfo["x-ui-widget"] === 'DateTimeWidget') {
            let formateType = FORMAT.TIMETwo
            if (fieldInfo["hideTime"]) {
              formateType = FORMAT.DATE
            }
            result = formatTimeStamp(value, formateType);
          } else  {
            result = value;
          }
          break;
      }
    }else{
      result = '--';
    }
    return result;
  }

  fetchCategory = async () => {
    try {
      let request = getEleCategory;
      let resp = await request();
      this.setState({
        eleCategorylist: resp.data.data
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  getEleCategoryList = (scheduleInfo) => {
    let {eleCategoryFirList, eleCategorySecList} = scheduleInfo;
    let {eleCategorylist} = this.state;
    let eleCategoryListArray = eleCategorySecList.concat(eleCategoryFirList);
    let result;
    if (eleCategoryListArray.length>0 && eleCategorylist.length > 0) {
      result = eleCategoryListArray.map((v) => {
        let labelName = getTreeName(eleCategorylist, v);
        return labelName
      }).join(",");
    }
    return result;
  }

  // 关联算法策略从品类中心获取
  async getAllQueryAlgorithmStrategyCenterList() {
    try {
      let resp = await queryAlgorithmStrategyCenterList();
      this.setState({linkAlgorithmCenterOptions: resp});
    } catch (error) {
      api.onRequestError(error)
    }
  }


  getFieldKeys = () => {
    const { scheduleTemplate, scheduleInfo } = this.state;
    let { properties } = scheduleTemplate.detail;
    let dependencies = scheduleTemplate.detail.dependencies ? JSON.parse(JSON.stringify(scheduleTemplate.detail.dependencies)) : [];
    try{
      Object.keys(dependencies).forEach((dependencyItem) => {
        const oneOf = dependencies[dependencyItem].oneOf;
        if (oneOf) {
          oneOf.forEach((oneItem, index) => {
            if (oneItem['properties'][dependencyItem]['enum'][0] == scheduleInfo[dependencyItem]) {
              // onOf 中的 dependencyItem 会覆盖掉 properties 中的 dependencyItem，导致properties 中的字段丢失影响页面正常展示
              delete dependencies[dependencyItem]['oneOf'][index]['properties'][dependencyItem];
              properties = Object.assign(properties, dependencies[dependencyItem]['oneOf'][index]['properties']);
            }
          })
        }
      })
    } catch(e) {};
    return properties;
  }

  getMultipleSchemaFieldKeys = () =>{
    const {scheduleTemplate, scheduleInfo} = this.state;
    let {detail, multipleSchema = {}} = scheduleTemplate;
    let {properties} = multipleSchema;
    return properties;
  }

  /*获取商品分类数据*/
  fetchSkuCategory = async () => {
    try {
      let request = apiL.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map((dataItem) => {
        dataItem.value = dataItem.value.toString();
        dataItem.children &&
          dataItem.children.map((subItem) => {
            subItem.value = subItem.value.toString();
            subItem.children &&
              subItem.children.map((thirdItem) => {
                thirdItem.value = thirdItem.value.toString();
              });
          });
        return dataItem;
      });
      this.setState({categoryDataSource:dataSource})
    } catch (error) {
      api.onRequestError(error);
    }
  };

  getAoi = (ext_aoi_codes) => {
    let str;
    const recursion = (res, list) => {
      res.forEach(v => {
        list[v.value] = v.label;
        if (v.children && v.children.length) {
          recursion(v.children,list);
        }
      });
    }

    new Promise((resolve, reject) => {
      const list = JSON.parse(localStorage.getItem("aoi_list")) || [];
      if (list.length) {
        resolve(list)
      } else {
        api.queryAoiList().then((result) => {
          recursion(result,list);
          sessionStorage.setItem("aoi_list",JSON.stringify(list));
          resolve(list)
        }).catch(api.onRequestError);
      }
    }).then((res)=>{
      if (res) {
        str = ext_aoi_codes.map((v) => res[v] ? res[v]  : v).join(",")
        this.setState({
          scheduleInfo: {...this.state.scheduleInfo, aoi: str}
        })
      }
    })
  }

  commondiy = (item,oversupply,index,name) => {
    if(Object.keys(item).length > 0){
      let Text = []
      if (item.supplyType == 1) {
        item.shopList.map((comItem)=>{
          if (item.poolIds.includes(comItem.value)) {
            Text.push({
              value:comItem.value,
              label:comItem.label
            })
          }
        })
      } else if (item.supplyType == 4) {
        Text = item.investIdList;
      } else{
        item.itemCat.map((catItem)=>{
          Text.push(catItem)
        })
      }
      let showTab = false
      if (oversupply.field && item[oversupply.field] && item[oversupply.field].length > 0) {
        showTab = true
      }

      let content = (
        <SimpleTable.Item
          label={`${name ? name + "-" : ""}商品类目${
            showTab || oversupply.showSingleOversupply ? `-${index + 1}` : ""
          }`}
        >
          {Text.map((comItem, _index) => {
            return (
              <>{`${comItem.label}${_index != Text.length - 1 ? "、" : ""}`}</>
            );
          })}
          {oversupply.showCornerIcon && item.cornerIconUrl && <img src={item.cornerIconUrl} width={68} height={36} />}
        </SimpleTable.Item>
      );

      if (item.supplyType == 1) {
        content = (
          <SimpleTable.Item
            label={`${name ? name + "-" : ""}选品集ID${
              showTab || oversupply.showSingleOversupply ? `-${index + 1}` : ""
            }`}
          >
            {Text.map((comItem) => {
              return (
                <>
                  <span>ID:{comItem.value}</span>&nbsp;&nbsp;
                  <span>名称:{comItem.label}</span>
                  <br></br>
                </>
              );
            })}
            {oversupply.showCornerIcon && item.cornerIconUrl && <img src={item.cornerIconUrl} width={68} height={36} />}
          </SimpleTable.Item>
        );
      } else if (item.supplyType == 4) {
        content = (
          <SimpleTable.Item
            label={`${name ? name + "-" : ""}招商活动ID${
              showTab || oversupply.showSingleOversupply ? `-${index + 1}` : ""
            }`}
          >
            <>
              <span>ID:{Text.join(",")}</span>
            </>
          </SimpleTable.Item>
        );
      }

      return <>
        {showTab && <SimpleTable.Item label={`${oversupply.name}-${index+1}`}>
          {item[oversupply.field]}
        </SimpleTable.Item>}
        {content}
      </>
    }
  }

  render() {
    let { configId, resourceType, pageId, posName,resourceId, resourceName, scheduleInfo, scheduleTemplate, putInActivityIdConfig, retatedData, linkAlgorithmCenterOptions, poolTypeConfig,pageUrl } = this.state;
    let fieldkey = scheduleTemplate ? this.getFieldKeys() : [];
    let multipleSchemaFieldKey = scheduleTemplate ? this.getMultipleSchemaFieldKeys() : [];
    let newFieldkey = {
      configName:{title:"配置名称:"},
      beginTime:{title:"开始时间:"},
      endTime:{title:"结束时间:"},
      workDay:{title:"星期:"},
      timeSelection:{title:"时段:"},
      promotionActivityId:{title:"活动计划ID:"},
      imageUrl:{title:"图片:","x-ui-widget":"img-upload","x-ui-validate": {width: 264, height: 264, maxSize: 500, accept: 'png,jpeg,jpg,apng,gif'}},
      imageUrlTwo:{title:"图片二:","x-ui-widget":"img-upload","x-ui-validate": {width: 264, height: 264, maxSize: 500, accept: 'png,jpeg,jpg,apng,gif'}},
      iconSelected:{title:"选中图片:","x-ui-widget":"img-upload","x-ui-validate": {width: 264, height: 264, maxSize: 500, accept: 'png,jpeg,jpg,apng,gif'}},
      iconUnselected:{title:"未选中图片:","x-ui-widget":"img-upload","x-ui-validate": {width: 264, height: 264, maxSize: 500, accept: 'png,jpeg,jpg,apng,gif'}},
      priority:{title:"权重:"},
      subTitle:{title:"门店分类:"},
      userOrigin:{title:"用户来源:"}
    }
    let showPutIn = scheduleTemplate ? scheduleTemplate.showPutIn : false;
    let retailConfig = scheduleTemplate ? scheduleTemplate.retailConfig : false;
    let showDataConfig = scheduleTemplate ? scheduleTemplate.showDataConfig : false;
    let showAddConfig = scheduleTemplate ? scheduleTemplate.showAddConfig : false;
    let showConfigGroup = scheduleTemplate ? scheduleTemplate.showConfigGroup : false;
    let showCategory = scheduleTemplate ? scheduleTemplate.showCategory : false;
    let showSignEntry = scheduleTemplate ? scheduleTemplate.showSignEntry : false;
    let showSign = scheduleTemplate ? scheduleTemplate.showSign : false;
    let isCategoryCenter = scheduleTemplate ? scheduleTemplate.isCategoryCenter : false;
    let showAoi = scheduleTemplate ? scheduleTemplate.showAoi : false;
    let showMainCategory = scheduleTemplate ? scheduleTemplate.showMainCategory : false;
    let showMultilineIdText = scheduleTemplate ? scheduleTemplate.showMultilineIdText : false;
    let dynamicDataSourceForm = scheduleTemplate ? scheduleTemplate.dynamicDataSourceForm : false;
    // let curPage = pageMap.filter(v => v.pageId == pageId)[0];
    let oversupply = scheduleTemplate ? scheduleTemplate.oversupply : false;
    let showSingleOversupply = scheduleTemplate ? scheduleTemplate.showSingleOversupply : false;
    let selectionCollection = scheduleTemplate ? scheduleTemplate.selectionCollection : false;
    let relatedSup;
    if (scheduleInfo.relatedSup && retatedData.length > 0) {
      let tmp = retatedData.filter(v => v.value == scheduleInfo.relatedSup);
      relatedSup = (tmp && tmp.length > 0) ? tmp[0].label : "";
    }
    let putInActivityId = (scheduleInfo.putInActivityId) ? scheduleInfo.putInActivityId.filter(v => v != "").join(",") : '';
    let activityIds = (scheduleInfo.activityIds) ? scheduleInfo.activityIds.filter(v => v != "").join(",") : '';
    let peopleOrderTypeText = [];
    let tmpArray = [];
    if (scheduleInfo.peopleOrderType) {
      tmpArray = scheduleInfo.peopleOrderType.sort((a, b) => a - b);
      tmpArray.forEach((typeItem) => {
        peopleOrderTypeText.push(PeopleOrderTypeTextMap[typeItem])
      })
    }
    peopleOrderTypeText = peopleOrderTypeText.join(',');
    let channelVersionList = (scheduleInfo.channelVersionList ? JSON.parse(scheduleInfo.channelVersionList) : '');
    let fieldGroup = [{key: "noSignInCopyList", title: "未签到"},{key: "signInCopyList", title: "签到"}];
    let isViewPage = Boolean(this.props.configId);

    let qrCodeUrl;
    if(pageUrl) {
      let dailyUrl = `https://market.wapa.taobao.com/app/eleme-newretail-c-h5/pg-embed/transit-preview/index.html?page=${showQrCodeMap[pageUrl]}`;
      let ppeUrl = `https://ppe-h5.ele.me/newretail/p/transit-preview/?page=${showQrCodeMap[pageUrl]}`;
      let prodUrl = `https://h5.ele.me/newretail/p/transit-preview/?page=${showQrCodeMap[pageUrl]}`;
      // let ppeGroups = {
      //   "/channelManage/market/getWelfare": "https://ppe-h5.ele.me/newretail/p/transit-preview/?page=welfare",
      //   "/channelManage/market/intraCityCircle": "https://ppe-h5.ele.me/newretail/p/transit-preview/?page=area",
      //   "/channelManage/market/brandWall": "https://ppe-h5.ele.me/newretail/p/transit-preview/?page=brand"
      // }
      // if (ppeGroups[curPage.url]) {
      //   ppeUrl = ppeGroups[curPage.url];
      // }
      let urlSet = {
        'daily': dailyUrl,
        'localdev': dailyUrl,
        'ppe': ppeUrl,
        'prod': prodUrl
      }
      qrCodeUrl = urlSet[window.configEnv];
    }

    let linkAlgorithmPolicyItem, linkAlgorithmPolicyLabel;
    if (isCategoryCenter) {
      linkAlgorithmPolicyItem = (linkAlgorithmCenterOptions && linkAlgorithmCenterOptions.length > 0) ? linkAlgorithmCenterOptions.filter((v) => v.value == scheduleInfo.linkAlgorithmPolicy) : [];
      linkAlgorithmPolicyLabel = (linkAlgorithmPolicyItem && linkAlgorithmPolicyItem.length > 0) ? linkAlgorithmPolicyItem[0].label : '';
    }

    let extPoolGroupLabel = [];
    let extPoolGroupLabelText = "";
    if (poolTypeConfig) {
      extPoolGroupLabel = scheduleInfo.ext_pool_group_label || [];
      let tempLabel = extPoolGroupLabel.map((v) => v.label);
      extPoolGroupLabelText = (tempLabel && tempLabel.length > 0) ? tempLabel.join(",") : '';
    }

    let newDataSourceList;
    if (typeof oversupply == 'object' && typeof oversupply.tabKey != 'undefined') {
      newDataSourceList = scheduleInfo[oversupply.tabKey] || [];
    }else{
      newDataSourceList = scheduleInfo.pureDataSourceList || [];
    }
    
    return (
      <div className="container">
        {(!isViewPage) && <div className="title">
          <Breadcrumb>
            <Breadcrumb.Item>频道管理</Breadcrumb.Item>
            {/*<Breadcrumb.Item>商超频道页</Breadcrumb.Item>*/}
            {/*<Breadcrumb.Item>{curPage.pageName}</Breadcrumb.Item>*/}
            <Breadcrumb.Item><Link style={{textDecoration:'underline'}} to={`${pageUrl}/${pageId}?anchorId=${resourceId}`}>{resourceName}</Link></Breadcrumb.Item>
            <Breadcrumb.Item>{posName}</Breadcrumb.Item>
            <Breadcrumb.Item>去投放</Breadcrumb.Item>
          </Breadcrumb>
        </div>}
        <div className="resource-body">
        {(scheduleInfo.status==2)&&(this.state.isReviewer)&&<Button className="reviewButton" size="large" component="a" href={scheduleInfo.approvalUrl} target="_black">审核</Button>}
          {filtrationAlpha.includes(resourceId)?(Boolean(scheduleInfo)&&<Row>
            <Col offset="3" span="18">
              <SimpleTable
                title={`${isViewPage?'':'基础信息'}`}
              >
                <SimpleTable.Item label='配置ID'>
                  {configId}
                </SimpleTable.Item>
                {(scheduleInfo.status == 2) && <SimpleTable.Item label="排期预览">
                  <QRCode
                    value={qrCodeUrl}
                    size={120}
                    fgColor="#000000"
                  />
                  {/*<p>注：请使用上面填写的用户ID登录饿了么APP，然后扫描上方二维码即可预览投放效果</p>*/}
                  <p>注：使用饿了么APP扫描上方二维码即可预览排期投放效果</p>
                </SimpleTable.Item>}
                {Object.keys(newFieldkey).map((item) => {
                  return <SimpleTable.Item label={newFieldkey[item].title}>
                      {this.handleField(item, scheduleInfo, newFieldkey[item])}
                    </SimpleTable.Item>
                })}
              </SimpleTable>
            </Col>
          </Row>):(Boolean(scheduleInfo && scheduleTemplate) && <Row>
            <Col offset="3" span="18">
              <SimpleTable
                title={`${isViewPage?'':'基础信息'}`}
              >
                <SimpleTable.Item label='配置ID'>
                  {configId}
                </SimpleTable.Item>
                {(scheduleInfo.status == 2) && <SimpleTable.Item label="排期预览">
                  <QRCode
                    value={qrCodeUrl}
                    size={120}
                    fgColor="#000000"
                  />
                  {/*<p>注：请使用上面填写的用户ID登录饿了么APP，然后扫描上方二维码即可预览投放效果</p>*/}
                  <p>注：使用饿了么APP扫描上方二维码即可预览排期投放效果</p>
                </SimpleTable.Item>}
                {Object.keys(fieldkey).map((o) => {
                  if (o != 'timeRanges' && o != 'activityIds' && o != 'configId') {
                    return <SimpleTable.Item label={fieldkey[o].title}>
                      {this.handleField(o, scheduleInfo, fieldkey[o])}
                    </SimpleTable.Item>
                  }
                })}
                {(scheduleInfo.relatedSup && resourceId!='100025023'&&resourceId!='25023')&&<SimpleTable.Item label="关联上级配置">
                  {relatedSup}
                </SimpleTable.Item>}
                {(scheduleInfo.relatedSup && (resourceId=='100025023'||resourceId=='25023'))&&<SimpleTable.Item label="关联业态金刚排期：">
                  {relatedSup}
                </SimpleTable.Item>}
                {(showDataConfig || showConfigGroup) && <SimpleTable.Item label="投放活动ID">
                  {putInActivityId || activityIds}
                </SimpleTable.Item>}
                {(topImgResourceGroup.includes(resourceId)) &&<SimpleTable.Item label="顶部背景图">
                  <img src={scheduleInfo.topImgUrl} style={{width: '100%', height: 'auto'}}/>
                </SimpleTable.Item>}
                {(resourceId == '100051001' || resourceId == '51001') && <SimpleTable.Item label="增加广告位浮层">
                  {scheduleInfo.adFloat == '1' ? <div>
                    {scheduleInfo.materialType == '1' ? <>
                      {scheduleInfo.selectViewList.length > 0 && scheduleInfo.selectViewList.map((v) => {
                        return <>
                          <p>视频：{v.mediaName}</p>
                          <video src={v.transcodeUrl} poster={v.headUrl} width='392' height='auto' controls='false'/>
                        </>
                      })}
                    </> : <>
                      <span>图片</span>
                      <img src={scheduleInfo.brandBaPingUrl} style={{width: '100%', height: 'auto'}}/>
                    </>}
                  </div> : <div>否</div>}
                </SimpleTable.Item>}
                {typeof oversupply == 'object' && newDataSourceList && (newDataSourceList.map((item,index)=>{
                  return this.commondiy(item,{...oversupply,showSingleOversupply},index)
                }))}
                {typeof selectionCollection == 'object' && selectionCollection.length > 0 ?<>{selectionCollection.map((item)=>{
                  return <>{ item.selectionFields && scheduleInfo[item.selectionFields] && scheduleInfo[item.selectionFields].map((newItem,index)=>{
                    if (!getVisibleItemForForm(item.hidden, scheduleInfo )) {
                      return null
                    }
                    return this.commondiy(newItem,{},index,item.selectionName)
                  })}</>
                })}</>:null}
                {scheduleTemplate.rankSchema && <RankDetail rankInfo={scheduleInfo.rankInfo} deliveryPosition={scheduleInfo.deliveryPosition} rankSchema={scheduleTemplate.rankSchema} />}
                {(showPutIn) && <SimpleTable.Item label="投放渠道">
                  {(scheduleInfo.channelList && scheduleInfo.channelList.length > 0) ? this.getChannelList(scheduleInfo.channelList) : '--'}
                </SimpleTable.Item>}
                {((showDataConfig|| showConfigGroup) && showAddConfig) && <SimpleTable.Item label="基础信息-投放渠道">
                  {(scheduleInfo.channelList && scheduleInfo.channelList.length > 0) ? this.getChannelList(scheduleInfo.channelList) : '--'}
                </SimpleTable.Item>}
                {((showDataConfig|| showConfigGroup) && showAddConfig) && <SimpleTable.Item label="基础信息-投放人群">
                  {(scheduleInfo.userTagGroupIdsList && scheduleInfo.userTagGroupIdsList.length > 0) ? scheduleInfo.userTagGroupIdsList.join(",") : '--'}
                </SimpleTable.Item>}
                {(showPutIn) && <SimpleTable.Item label="投放版本">
                  {(channelVersionList.allVersion) ? '全版本' : <div>
                    {channelVersionList.versionGroup && channelVersionList.versionGroup.versionList.map((v)=>{
                      return <p>{versionDetailGroup[v.operateType]}：{v.value}</p>
                    })}
                  </div>}
                </SimpleTable.Item>}
                {((showDataConfig|| showConfigGroup) && showAddConfig) && <SimpleTable.Item label="基础信息-投放版本">
                  {(channelVersionList.allVersion) ? '全版本' : <div>
                    {channelVersionList.versionGroup && channelVersionList.versionGroup.versionList.map((v)=>{
                      return <p>{versionDetailGroup[v.operateType]}：{v.value}</p>
                    })}
                  </div>}
                </SimpleTable.Item>}
                {((showDataConfig|| showConfigGroup) && showAddConfig) && <SimpleTable.Item label="基础信息-投放城市">
                  {(scheduleInfo.citys && scheduleInfo.citys.length > 0) ? this.getCity(scheduleInfo.citys) : '--'}
                </SimpleTable.Item>}
                {(showPutIn) && <SimpleTable.Item label="投放标签">
                  {(scheduleInfo.channelLabel && scheduleInfo.channelLabel.length > 0) ? this.getChannelLabel(scheduleInfo.channelLabel) : '--'}
                </SimpleTable.Item>}
                {(showPutIn) && <SimpleTable.Item label="门店类目">
                  {(scheduleInfo.eleCategoryFirList && scheduleInfo.eleCategoryFirList.length > 0) || (scheduleInfo.eleCategorySecList && scheduleInfo.eleCategorySecList.length > 0) ? this.getEleCategoryList(scheduleInfo) : '--'}
                </SimpleTable.Item>}
                {(showDataConfig) && <SimpleTable.Item label="投放渠道">
                  {(putInActivityIdConfig.length > 0 && putInActivityIdConfig[0].deliveryChannelList && putInActivityIdConfig[0].deliveryChannelList.length > 0) ? this.getChannelList(putInActivityIdConfig[0].deliveryChannelList) : '--'}
                </SimpleTable.Item>}
                {(showDataConfig) && <SimpleTable.Item label="投放标签">
                  {(putInActivityIdConfig.length > 0 && putInActivityIdConfig[0].deliveryChannelLabel && putInActivityIdConfig[0].deliveryChannelLabel.length > 0) ? this.getChannelLabel(putInActivityIdConfig[0].deliveryChannelLabel) : '--'}
                </SimpleTable.Item>}
                {(showPutIn || retailConfig) && <SimpleTable.Item label="投放城市">
                  {(scheduleInfo.citys && scheduleInfo.citys.length > 0) ? this.getCity(scheduleInfo.citys) : '--'}
                </SimpleTable.Item>}
                {poolTypeConfig && <SimpleTable.Item label={`投放${poolTypeConfig == 1 ? '商品' : '门店'}：`}>
                  {extPoolGroupLabelText}
                </SimpleTable.Item>}
                {retailConfig && <SimpleTable.Item label="数据源">
                  {(scheduleInfo.dataResourceDTOList && scheduleInfo.dataResourceDTOList.length > 0) ? <div>
                    {scheduleInfo.dataResourceDTOList.map((v) => {
                      return <p>
                        <span>{resourceTypeMap[v.dataResourceType]}，</span>
                        {v.dataResourceType == '1' && <span>选品类型:{poolTypeMap[v.poolIdType]}，</span>}
                        <span>选品ID:{v.dataResourceList}</span>
                      </p>
                    })}
                  </div> : '--'}
                </SimpleTable.Item>}
                {showCategory && <SimpleTable.Item label="所属类目">
                  {(scheduleInfo.searchLabelList && scheduleInfo.searchLabelList.length > 0) ? <div>
                    {scheduleInfo.searchLabelList.map((v) => {
                      let goodsCategoryListLabel = (isArray(v.goodsCategoryList) ? v.goodsCategoryList.map(o => o.label).join("、") : v.goodsCategoryList.label);
                      return <p>
                        <span>{v.label}，</span>
                        <span>{goodsCategoryListLabel}</span>
                      </p>
                    })}
                  </div> : '--'}
                </SimpleTable.Item>}
                {resourceType == '143' && <SimpleTable.Item label="关联算法策略">
                  {scheduleInfo.linkAlgorithmPolicy && <span>{scheduleInfo.linkAlgorithmPolicy}</span>}
                </SimpleTable.Item>}
                {isCategoryCenter && <SimpleTable.Item label="关联算法策略">
                  {scheduleInfo.linkAlgorithmPolicy && <span>{linkAlgorithmPolicyLabel}</span>}
                </SimpleTable.Item>}
                {showSign && <SimpleTable.Item label="签到">
                  {(scheduleInfo.signRedBagList && scheduleInfo.signRedBagList.length>0) && scheduleInfo.signRedBagList.map((v,index)=>{
                    return <div style={{marginBottom:'10px'}}>
                      <p>签到{v.signDay}天</p>
                      <label  style={{marginLeft:'40px'}}>权益ID：</label>
                      <span>{v.appointRightId}，</span>
                      <label  style={{marginLeft:'5px'}}>品牌ID：</label>
                      <span>{v.appointBrandId}，</span>
                      {v.hasOwnProperty('redBagAttr') && <>
                        <label>，红包属性：</label>
                        <span>{v.redBagAttr}</span>
                      </>}
                    </div>
                  })}
                </SimpleTable.Item>}
                {this.handleField("gameType", scheduleInfo, fieldkey["gameType"])=="签到"&&(resourceId=='100071002' || resourceId=='71002') && <SimpleTable.Item label="签到">
                  {(scheduleInfo.signRedBagList && scheduleInfo.signRedBagList.length>0) && scheduleInfo.signRedBagList.map((v,index)=>{
                    return <div style={{marginBottom:'10px'}}>
                      <p>签到{v.signDay}天</p>
                      <label  style={{marginLeft:'40px'}}>权益池ID：</label>
                      <span>{v.appointRightId}，</span>
                      <label  style={{marginLeft:'5px'}}>品牌ID：</label>
                      <span>{v.appointBrandId}，</span>
                      {v.hasOwnProperty('redBagAttr') && <>
                        <label>，红包属性：</label>
                        <span>{v.redBagAttr}</span>
                      </>}
                    </div>
                  })}
                </SimpleTable.Item>}
                {(scheduleTemplate.multipleSchema && scheduleTemplate.multipleSchema.key) && <SimpleTable.Item label={scheduleTemplate.multipleSchema.titleName}>
                  {scheduleInfo[scheduleTemplate.multipleSchema.key].map((o) => {
                    return <div style={{border: '1px solid #CCC', marginBottom: '5px', padding: '10px'}}>
                      {Object.keys(multipleSchemaFieldKey).map((item) => {
                        return <SimpleTable.Item label={multipleSchemaFieldKey[item].title}>
                          {this.handleField(item, o, multipleSchemaFieldKey[item])}
                        </SimpleTable.Item>
                      })}
                    </div>
                  })}
                </SimpleTable.Item>}
                {showSignEntry && <>
                  {fieldGroup.map((o) => {
                    let title = o.title;
                    let item = scheduleInfo[o.key];
                    return <SimpleTable.Item label={`${title}文案：`}>
                      {(item && item.length > 0) && item.map((v) => {
                        return <div style={{marginBottom: '10px'}}>
                          <label>文案内容：</label>
                          <span>{v.signContent}</span>
                          <label>，颜色：</label>
                          <span>{v.typeFaceColor}</span>
                        </div>
                      })}
                    </SimpleTable.Item>
                  })
                  }
                </>}

                {(showDataConfig) && [<SimpleTable.Item label="投放城市">
                  {(putInActivityIdConfig.length > 0 && (putInActivityIdConfig[0].citys.length > 0 || putInActivityIdConfig[0].griddings.length > 0)) ? this.getCity([...putInActivityIdConfig[0].citys,...putInActivityIdConfig[0].griddings]) : '--'}
                </SimpleTable.Item>, <SimpleTable.Item label="选品集ID">
                  {(putInActivityIdConfig.length > 0 && putInActivityIdConfig[0].poolIdsRep && putInActivityIdConfig[0].poolIdsRep.length > 0) ? putInActivityIdConfig[0].poolIdsRep.join(", ") : '--'}
                </SimpleTable.Item>, <SimpleTable.Item label="投放人群">
                  {(putInActivityIdConfig.length > 0 && putInActivityIdConfig[0].userTagGroupIdsList.length > 0) ? putInActivityIdConfig[0].userTagGroupIdsList : '--'}
                </SimpleTable.Item>]}
                {showConfigGroup && [<SimpleTable.Item label="投放城市">
                  {putInActivityIdConfig.map((v) => {
                    return <p><span>{v.activityId}：</span>{(v.citys && v.citys.length>0) ? this.getCity(v.citys):'--'}</p>
                  })}
                </SimpleTable.Item>, <SimpleTable.Item label="选品集ID">
                  {putInActivityIdConfig.map((v) => {
                    return <p><span>{v.activityId}：</span>{v.poolIdsRep.join(", ")}</p>
                  })}
                </SimpleTable.Item>, <SimpleTable.Item label="投放人群">
                  {putInActivityIdConfig.map((v) => {
                    return <p><span>{v.activityId}：</span>{(v.userTagGroupIdsList && v.userTagGroupIdsList.length>0) ? v.userTagGroupIdsList : '--'}</p>
                  })}
                </SimpleTable.Item>]}
                {(!PeopleList.includes(resourceType) && showPutIn) && <SimpleTable.Item label="投放人群">
                  {(scheduleInfo.userTagGroupIdsList && scheduleInfo.userTagGroupIdsList.length > 0) ? scheduleInfo.userTagGroupIdsList.join(",") : '--'}
                </SimpleTable.Item>}
                {PeopleList.includes(resourceType) && showPutIn && <SimpleTable.Item label="投放人群类型">
                  {scheduleInfo.peopleType && scheduleInfo.peopleType == '1' ? '订单用户身份' : 'MUSE用户标签'}
                </SimpleTable.Item>}
                {peopleOrderTypeText && <SimpleTable.Item label="订单用户类型">
                  {peopleOrderTypeText}
                </SimpleTable.Item>}
                {showAoi && <SimpleTable.Item label="投放AOI类型">
                  {scheduleInfo.aoi ? scheduleInfo.aoi: '--'}
                </SimpleTable.Item>}
                {showMainCategory && <SimpleTable.Item label="商家主营类目">
                  {scheduleInfo.shop_main_category ? scheduleInfo.shop_main_category.map((item)=>{
                    return item.label
                  }).join(): '--'}
                </SimpleTable.Item>}
                {showMultilineIdText && showMultilineIdText.map((item)=>{
                  let _itemText = scheduleInfo[item.key];
                  if (item.parentKey) { // 常买热卖中，加了一层parentKey
                    let parentText = scheduleInfo[item.parentKey];
                    _itemText = (parentText && parentText.length > 0) ? parentText[0][item.key] : [];
                  }
                  return <SimpleTable.Item label={item.name}>
                    {_itemText && _itemText.join()}
                  </SimpleTable.Item>
                })}
                {dynamicDataSourceForm && dynamicDataSourceForm.map((item)=>{
                  let itemText = scheduleInfo[item.fieldName] || [];
                  return <SimpleTable.Item label={item.componentName}>
                    {itemText.length >0 ? itemText.map((nItem)=>{
                      return <p><span>{nItem.label}：</span>{nItem.value}</p>
                    }):"-"}
                  </SimpleTable.Item>
                })}
              </SimpleTable>
            </Col>
          </Row>)}
        </div>
      </div>
    )
  }
}


export const LogTimePutInPage = logTimeComponent(DetailConfig, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const DetailConfigPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})
