import React from 'react';

import {Form, Field, Switch} from '@alife/next';
import DistinctEnableSetBtn from '../comps/distinctEnableSetBtn';
import {PageBase} from '@/containers/base';
import {onRequestError} from '@/utils/api';
import {getPagePutInConfig, savePagePutInConfig} from '../request';
import './putInSet.scss'

const FormItem = Form.Item;

/**rules form settings */
const putInSetItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 11
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
}

const formLayout = {
  style: {
    margin: '20px auto'
  },
}


export default class PutInSet extends PageBase {
  constructor(props) {
    super(props)
    this.state = {
      distinctEnable: false,
      pageId: this.props.pageId,
    }

    this.field = new Field(this, {
      values: {
        distinctEnable: false,
      }
    })

  }

  componentDidMount() {
    this.getPagePutInConfig()

  }

  getPagePutInConfig = () => {
    // 获取去投放信息数据
    let {pageId} = this.state;
    let params = {
      pageId,
    }
    getPagePutInConfig(params).then((data) => {
      let {distinctEnable} = data;
      this.field.setValues({distinctEnable})
    }).catch(onRequestError)

  }

  switchChange = (name, v) => {
    this.field.setValue(name, v);
    // 发请求 保存
    let {pageId} = this.state;
    let params = {
      pageId,
      distinctEnable: Number(this.field.getValue('distinctEnable'))
    }

    savePagePutInConfig(params).then((data) => {
      console.log(data, 'data')
    }).catch(onRequestError)

    // const data = await savePagePutInConfig(params);
  }

  render() {
    let {pageId} = this.state;
    let distinctEnable = this.field.getValue('distinctEnable');

    return (
      <PageBase.Container className="putin-set-page">
        <div className="set-title">投放配置</div>
        <Form className="putin-set-form" {...formLayout} field={this.field}>
          <FormItem label="楼层间去重" {...putInSetItemLayout} >
            <Switch name="distinctEnable" checked={distinctEnable} onChange={(v) => this.switchChange('distinctEnable', v)}/>
            {!!distinctEnable && <DistinctEnableSetBtn pageId={pageId}/>}
          </FormItem>
        </Form>
      </PageBase.Container>
    )
  }
}
