import {Input, Select, Tab, Button, Table, Pagination, Grid, Breadcrumb} from '@alife/next'
import {FilterForm, FilterItem} from '@/components/filter'
import React, {useState, useEffect} from 'react'
import { track } from '@/utils/aplus';
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link,withRouter} from 'react-router-dom'
import useTableQuery from '@/components/useTableQuery'
import MarketResourceList from '@/containers/channel/market/comps/list.js'
import MarketPage from '@/containers/channel/market/resource/page'
// import {fetchList} from '../api'
// import {parseQuery} from '../../common'

import PutInSet from './putInSet'
import './index.scss'
import {resourceMap, getQueryValue, getPageMap, redirectUrl, changeRedirectUrl} from "../common";
import { getUrlQuery } from '@/utils/others';
import {getPermission} from "@/utils/api";
// const {Row, Col} = Grid

const tabGoldLinks = [
  '/selection_kunlun.CHANNEL-MANAGE-index.resource-set-tab',
  '/selection_kunlun.CHANNEL-MANAGE-index.page-set-tab',
  '/selection_kunlun.CHANNEL-MANAGE-index.putin-set-tab',
]

function MarketResource() {
  const pageUrl = location.hash.split("?")[0];
  // const {pageId,pageName,isInside} = currentPage;
  const [tabKey, setTabKey] = useState("0");
  // const pageName='';
  const [currentPage, setCurrentPage] = useState({});
  const [pageMap, setPageMap] = useState([]);
  const _url = location.href.indexOf('?') >= 0 ? location.href.substring(0, location.href.lastIndexOf('?')) : location.href;
  const pageId = _url.substring(1 + _url.lastIndexOf('/'), _url.length);

  const onTabChange = (key) => {
    setTabKey(key);
    track('clickEvent', [tabGoldLinks[key], `pageId=${pageId}`])
  }

  useEffect(() => {
    changeRedirectUrl();

    getPageMap().then((res) => {
      console.log(res);
      setPageMap(res);
      const _currentPage = res.filter(v => pageUrl == '#' + v.url)[0];
      setCurrentPage(_currentPage);
      // const {pageId, pageName, isInside} = _currentPage;
      // setPageId(pageId);
      // setPageName(pageName);
    })

  }, [])

  const tabGroup = {
    "0":"资源位配置",
    "1":"页面配置",
    "2":"投放配置"
  }

  return (
    <div className="resource-index">
      {/*<div className="nav-wrapper">*/}
      {/*  <Breadcrumb>*/}
      {/*    <Breadcrumb.Item>频道管理</Breadcrumb.Item>*/}
      {/*    <Breadcrumb.Item>{pageName}</Breadcrumb.Item>*/}
      {/*    <Breadcrumb.Item>{tabGroup[tabKey]}</Breadcrumb.Item>*/}
      {/*  </Breadcrumb>*/}
      {/*</div>*/}
      <div className='body'>
        <Tab onChange={onTabChange}>
          <Tab.Item title="资源位配置" key="0"><MarketResourceList resourceMap={resourceMap} pageMap={pageMap} pageId={pageId}/></Tab.Item>
          <Tab.Item title="页面配置" key="1"><MarketPage pageId={pageId}/></Tab.Item>
          <Tab.Item title="投放配置" key="2"><PutInSet pageId={pageId}/></Tab.Item>
        </Tab>
      </div>
    </div>
  )
}


export const LogTimePutInPage = logTimeComponent(withRouter(MarketResource), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MarketResourcePage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})



