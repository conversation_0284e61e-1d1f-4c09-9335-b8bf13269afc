import {
  Grid,
  Button,
  Input,
  Field,
  Form,
  Tab,
  Select,
  Dialog,
  Message
} from '@alife/next'

import React from 'react'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {PageBase} from '@/containers/base';
import {permissionAccess} from '@/components/PermissionAccess';
import './index.scss';
import { env } from '@/config';
import * as api from '@/utils/api';
import {getBucUser} from "@/adator/api";
import {setSchema, getScheduleTemplate, rootSendMsg, setMetadataSchema,setResourcePermission} from "../request";
const FormItem = Form.Item;
const { Row, Col } = Grid;
const formItemLayout = {
  labelCol: {
    span: 6
  },
  wrapperCol: {
    span: 14
  }
};

export default class MarketSetSchema extends PageBase{
  constructor(props) {
    super(props);
    this.field = new Field(this, {});
    this.state = {
      tabKey: 1,
      empId: '',
      lastName: '',
      empIdGroup: ['178401', '342681', '177759', "321792", "231380", "322141", "356306", "409739"]
    }
  }

  componentDidMount() {
    this.getUserName();
    console.log(this.field.getValues());
  }

  // 发送钉钉通知
  sendRootMsg = (data = {}, type) => {
    try {
      const { lastName, empId } = this.state;
      const userInfo = `${lastName}(${empId})`;
      let contentTitle = `## Schema 设置成功; Env: ${env}`;
      // * schemeJson: [格式化地址](https://www.bejson.com/?data=${data.content})
      let content = `
    #### 发布人: ${userInfo}; 
    * resourceId: ${data.resourceId}; 
    * locationId: ${data.locationId}; 
    * fileLocation: app/containers/channel/market/resource/setSchema.js; 
    * [查看schema发布记录](https://selection.kunlun.alibaba-inc.com/#/tools/schemaReleaseRecord)`; 
      if (type === "origin") {
        contentTitle = "## Schema 元数据设置成功";
      }

      rootSendMsg({
        msgtype: "markdown", 
        markdown: {
          title: `${userInfo}${contentTitle}`,
          text: contentTitle + content,
        },
      });
    } catch (e) {
      return false;
    }
  }

  setSchema = () =>{
    Dialog.confirm({
      title: `确定设置schema？`,
      onOk: () => {
        let data = this.field.getValues();
        console.log(JSON.parse(data.schemeJson));
        let schemeRequestDTO = {
          resourceId: data.resourceId,
          locationId: data.locationId,
          schemeJson: data.schemeJson
        }
        setSchema(schemeRequestDTO)
          .then((result) => {
            // console.log(result);
            // this.sendRootMsg(data);
            Message.success('设置成功')
          }).catch(api.onRequestError);
      }
    })
  }

  setMetaSchema = () =>{
    Dialog.confirm({
      title: `确定设置schema元数据？`,
      onOk: () => {
        let data = this.field.getValues();
        console.log(JSON.parse(data.schemeJson));
        let schemeRequestDTO = {
          resourceId:data.resourceId,
          locationId:data.locationId,
          schemeJson:data.schemeJson
        }
        setMetadataSchema(schemeRequestDTO)
          .then((result) => {
            console.log(result);
            this.sendRootMsg(data, 'origin');
            Message.success('设置成功')
          }).catch(api.onRequestError);
      }
    })
  }

  getSchema = () =>{
    let data = this.field.getValues();
    let baseQueryRequestDTO = {
      resourceId:data.resourceId,
      posId:data.locationId,
    }
    getScheduleTemplate(baseQueryRequestDTO)
      .then((result) => {
        console.log(result);
        Message.success('获取成功')
        this.field.setValue("schemeJson",result);
      }).catch(api.onRequestError);
  }

  clear = () =>{
    this.field.reset();
  }

  onTabChange = (value) => {
    this.setState({
      tabKey:value
    })
  }

  setPermission = () =>{
    let data = this.field.getValues();
    let query = {
      resourceId:data.resourceId,
      pageId:data.pageId,
      ignorePermission:data.ignorePermission
    }
    setResourcePermission(query)
      .then((result) => {
        console.log(result);
        Message.success('设置权限成功')
      }).catch(api.onRequestError);
  }

  getUserName = () =>{
    try {
      getBucUser().then((resp)=>{
        let {empId, lastName} = resp.data.data;
        this.setState({
          empId,
          lastName,
        })
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  render() {
    let {tabKey,empIdGroup,empId} = this.state;
    if(empIdGroup.includes(empId)) {
      return (
        <div>
          <Tab onChange={this.onTabChange} value={tabKey}>
            <Tab.Item title="设置schema" key={1}/>
            <Tab.Item title="设置权限" key={2}/>
          </Tab>
          <Form {...formItemLayout} field={this.field} style={{padding: '20px'}}>
            <FormItem label="资源位ID:">
              <Input name="resourceId"/>
            </FormItem>
            {tabKey == 1 && [<FormItem label="坑位ID:">
              <Input name="locationId"/>
            </FormItem>,
            <FormItem label="Schema串:">
              <Input.TextArea name="schemeJson" aria-label="auto height" autoHeight={{minRows: 20, maxRows: 30}}/>
            </FormItem>]}
            {tabKey == 2 && [<FormItem label="页面ID:">
              <Input name="pageId"/>
            </FormItem>, <FormItem label="是否需要校验:">
              <Select name="ignorePermission">
                <Select.Option value={0}>是</Select.Option>
                <Select.Option value={1}>否</Select.Option>
              </Select>
            </FormItem>]}
            <Row>
              <Col span={6}/>
              {tabKey == 1 && <Col>
                <Button onClick={this.setSchema}>设置schema</Button>
                {sessionStorage.getItem("showSetOriginSchema") ? (
                  <Button onClick={this.setMetaSchema}>
                    设置schema元数据
                  </Button>
                ) : null}
                <Button onClick={this.getSchema}>获取schema</Button>
                <Button onClick={this.clear}>清空</Button>
              </Col>}
              {tabKey == 2 && <Col>
                <Button onClick={this.setPermission}>设置权限</Button>
                <Button onClick={this.clear}>清空</Button>
              </Col>}
            </Row>
          </Form>
        </div>
      )
    }else{
      return <div style={{textAlign:'center',paddingTop:'200px'}}><p>当前没有权限</p></div>
    }
  }
}

export const LogTimePutInPage = logTimeComponent(MarketSetSchema, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MarketSetSchemaPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})

