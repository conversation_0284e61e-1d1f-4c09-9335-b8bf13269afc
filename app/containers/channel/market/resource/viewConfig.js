import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Button,
  Form,
  Input,
  Tab,
} from '@alife/next'

import React from 'react'
import {Link} from 'react-router-dom'
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {PageBase} from '@/containers/base';
import {permissionAccess} from '@/components/PermissionAccess';
import DetailConfig from './detailConfig'
import moment from 'moment';
import * as qs from 'query-string';
import './viewConfig.scss';
import {
  pageMap,showQrCodeMap
} from '../common';
import * as api from '@/utils/api';
import {
  queryPosConfig,
  validateAclResource,
  submitScheduleCheck
} from '../request';
import {TimeRangePicker} from "@/components/TimeRangePicker";
import QRCode from "qrcode.react";
const FormItem = Form.Item;

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    if(start.isBefore(moment().startOf('day'))) return cb('开始时间不能小于当前时间')
  }
  cb()
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 5
  },
  wrapperCol: {
    span: 19
  }
};

class ViewConfig extends PageBase{
  constructor(props) {
    super(props);
    let {resourceId = '', posId = '', posName, resourceName, resourceType, pageId} = this.query;
    const {pathname=''} = this.location;
    let {configId} = this.params;
    this.state = {
      tabidx: 1,
      resourceId,
      posId,
      posName,
      resourceName,
      resourceType,
      pageId,
      configId,
      pageUrl: (configId) ? pathname.substring(0, pathname.lastIndexOf('/', pathname.lastIndexOf("/") - 1)) : pathname.substring(0, pathname.lastIndexOf('/'))
      // pageUrl: pathname.substring(0, pathname.lastIndexOf('/'))
      // curPage: pageMap.filter(v => v.pageId == pageId)[0]
    }
  }

  componentDidMount() {
    this.getDetailConfig();
  }


  getDetailConfig = () => {
    let {resourceId, posId, configId} = this.state;
    let baseQueryRequestDTO = {
      resourceId,
      posId,
      configId
    }
    queryPosConfig(baseQueryRequestDTO)
      .then((result) => {
        let scheduleInfo = JSON.parse(result.scheduleInfo);
        this.setState({
          scheduleInfo
        })
      }).catch(api.onRequestError);
  }

  onTabChange = (tabidx) => {
    this.setState({
      tabidx
    })
  }

  backEditPage = () =>{
    const {curPage, configId, resourceId, posId, resourceName, posName, pageId, resourceType,pageUrl} = this.state;
    const query = {
      resourceId,
      posId,
      resourceName,
      posName,
      pageId,
      resourceType
    }
    this.history.push(`${pageUrl}/edit/${configId}?${qs.stringify(query)}`)
  }

  submitInfo = () => {
    let {curPage, resourceId, posId, configId, pageId, posName, resourceName, resourceType,pageUrl} = this.state;
    const baseParams = {
      resourceId,
      resourceName,
      posId,
      posName,
      pageId,
    }
    submitScheduleCheck({...baseParams, id: configId})
      .then((result) => {
        console.log(result);
        this.history.push(`${pageUrl}/viewConfigure?${qs.stringify({...baseParams, resourceType})}`)
      }).catch(api.onRequestError);
  }


  render() {
    let { posName,resourceId, resourceName, tabidx,scheduleInfo,curPage,pageUrl,pageId} = this.state;
    let isShowLandPage = (scheduleInfo && scheduleInfo.hasOwnProperty("jumpUrl") && scheduleInfo.jumpUrl!="" );
    let dailyUrl = `https://market.wapa.taobao.com/app/eleme-newretail-c-h5/pg-embed/transit-preview/index.html?page=${showQrCodeMap[pageUrl]}`;
    let ppeUrl = `https://ppe-h5.ele.me/newretail/p/transit-preview/?page=${showQrCodeMap[pageUrl]}`;
    let prodUrl = `https://h5.ele.me/newretail/p/transit-preview/?page=${showQrCodeMap[pageUrl]}`;
    // let ppeGroups = {
    //   "/channelManage/market/getWelfare": "https://ppe-h5.ele.me/newretail/p/transit-preview/?page=welfare",
    //   "/channelManage/market/intraCityCircle": "https://ppe-h5.ele.me/newretail/p/transit-preview/?page=area",
    //   "/channelManage/market/brandWall": "https://ppe-h5.ele.me/newretail/p/transit-preview/?page=brand"
    // }
    // if (ppeGroups[curPage.url]) {
    //   ppeUrl = ppeGroups[curPage.url];
    // }
    let urlSet = {
      'daily': dailyUrl,
      'localdev': dailyUrl,
      'ppe': ppeUrl,
      'prod': prodUrl
    }
    let qrCodeUrl = urlSet[window.configEnv];

    let landingUrl = '';
    if(scheduleInfo && scheduleInfo.jumpUrl){
      let newJumpUrl = decodeURIComponent(scheduleInfo.jumpUrl.trim());
      landingUrl = newJumpUrl.substr(1 + newJumpUrl.indexOf('='));
    }

    return (
      <div className="container resource-view-config">
        <div className="title">
          <Breadcrumb>
            <Breadcrumb.Item>频道管理</Breadcrumb.Item>
            {/*<Breadcrumb.Item>{curPage.pageName}</Breadcrumb.Item>*/}
            <Breadcrumb.Item><Link to={`${pageUrl}/${pageId}?anchorId=${resourceId}`}>{resourceName}</Link></Breadcrumb.Item>
            <Breadcrumb.Item>{posName}</Breadcrumb.Item>
            <Breadcrumb.Item>预览排期</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <div className="body">
          <Tab shape="wrapped" onChange={this.onTabChange} activeKey={this.state.tabidx}>
            <Tab.Item title="排期信息" key={0}></Tab.Item>
            <Tab.Item title="排期预览" key={1}></Tab.Item>
            {isShowLandPage && <Tab.Item title="落地页预览" key={2}></Tab.Item>}
          </Tab>
          {tabidx == '0' && <DetailConfig configId={this.state.configId}/>}
          {tabidx == '1' && <Form {...formItemLayout}>
            {/*<FormItem*/}
            {/*  label="日期"*/}
            {/*  required*/}
            {/*  requiredMessage="请输入时间段"*/}
            {/*  validator={timeRangeValidator}*/}
            {/*  validatorTrigger={['onChange']}>*/}
            {/*  <TimeRangePicker showTime={true} name="effectRange" disabledDate={(date) => date.isBefore(moment().startOf('day'))}></TimeRangePicker>*/}
            {/*</FormItem>*/}
            {/*<FormItem*/}
            {/*  label="手机号"*/}
            {/*  required*/}
            {/*  requiredMessage="请输入手机号">*/}
            {/*  <Input style={{width: '340px'}}/>*/}
            {/*</FormItem>*/}
            {/*<FormItem label=" " colon={false}>*/}
            {/*  <Form.Submit*/}
            {/*    type="primary"*/}
            {/*    validate*/}
            {/*    // onClick={this.savePool}*/}
            {/*    // disabled={isSubmit}*/}
            {/*  >*/}
            {/*    生成二维码*/}
            {/*  </Form.Submit>*/}
            {/*</FormItem>*/}
          </Form>}
          {tabidx == '1' && <div className="result">
            <QRCode
              value= {qrCodeUrl}
              size={120}
              fgColor="#000000"
            />
            {/*<p>注：请使用上面填写的用户ID登录饿了么APP，然后扫描上方二维码即可预览投放效果</p>*/}
            <p>注：使用饿了么APP扫描上方二维码即可预览排期投放效果</p>
            <Button type="default" onClick={this.backEditPage}>返回编辑</Button>
            <Button type="primary" style={{marginLeft: '10px'}} onClick={this.submitInfo}>提交排期</Button>
          </div>}
          {tabidx == '2' && <div className="landing-page">
            <QRCode
              value={landingUrl}
              size={120}
              fgColor="#000000"
            />
            <p>注：请扫描上方二维码即可预览落地页的效果</p>
          </div>}
        </div>
      </div>
    )
  }
}

export const LogTimeViewConfig = logTimeComponent(ViewConfig, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ViewConfigPage = permissionAccess(LogTimeViewConfig, async (params) => {
  return await validateAclResource(params);
})
