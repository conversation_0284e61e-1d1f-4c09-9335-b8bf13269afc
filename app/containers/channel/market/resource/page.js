import {Grid, Button, Tab, Form, Input, Radio,Message} from '@alife/next'
import React,{useState,useEffect} from 'react'
import './index.scss'
import SimpleTable from '@/components/SimpleTable'
import QRCode  from 'qrcode.react';
import PagePreview from '@/components/PagePreview';
import {getPageBaseConfig, editPageStyle} from '../request';
import * as api from '@/utils/api';
import SchemaForm from '@/utils/Form/src';
const {Row, Col} = Grid;
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    fixedSpan:12
  },
  wrapperCol: {
    span: 14
  }
};

function BaseInfo({pageId = '',data}) {
  // const [data, setPageData] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  // useEffect(() => {
  //   getPageData();
  // }, [0])
  // function getPageData() {
  //   getPageBaseConfig({pageId})
  //     .then((result) => {
  //       setPageData(result);
  //     }).catch(api.onRequestError);
  // }
  return (
    <div className="resource-body">
      {data && <Row>
        <Col offset="3" span="18">
          <SimpleTable
            title="基础信息"
          >
            <SimpleTable.Item label="页面ID">
              {data.pageId}
            </SimpleTable.Item>
            <SimpleTable.Item label="页面名称">
              {data.name}
            </SimpleTable.Item>
            <SimpleTable.Item label="页面描述">
              {data.description}
            </SimpleTable.Item>
            <SimpleTable.Item label="管理员">
              {data.adminName}
            </SimpleTable.Item>
            <SimpleTable.Item label="页面链接">
              {data.url}
            </SimpleTable.Item>
            <SimpleTable.Item label="页面实时数据">
              <Button type="primary" text onClick={() => setPreviewVisible(true)}>预览</Button>
              {previewVisible&&
              <PagePreview
                visible={previewVisible}
                title={data.name}
                join={'&'}
                onClose={() => setPreviewVisible(false)}
                previewSrc={`${data.url}#`}
              ></PagePreview>
              }
            </SimpleTable.Item>
            <SimpleTable.Item label="页面二维码">
              <QRCode
                value={data.url}
                size={120}
                fgColor="#000000"
              />
            </SimpleTable.Item>
          </SimpleTable>
        </Col>
      </Row>}
    </div>
  )
}

function AtmosphereSetting({pageId = '',data}) {
  console.log(data);
  const [formData, setFormData] = useState(data);

  const schema = {
    'title': '',
    'type': 'object',
    'properties': {
      'backgroundColor': {
        'title': '背景色：',
        'type': 'string',
        'x-ui-placeholder': '示例：#8913FF'
      },
      'backgroundImageType': {
        'type': 'number',
        'title': '背景图类型:',
        'enum': [1, 2, 3],
        'x-ui-className': 'selection-form',
        'x-ui-valueLabel': {
          1: '纯色',
          2: '整张图片',
          3: '图片平铺'
        },
        'default':'',
        'x-ui-widget': 'radio'
      },
      'backgroundImageUrl': { //todo 图片宽高待确定
        'title': '背景图:',
        'type': 'string',
        'format': 'uri',
        'x-ui-widget': 'img-upload',
        'x-ui-validate': {
          'width': 750,
          'maxSize': 500,
          'accept': 'png,jpeg,jpg,apng'
        }
      },
      'subImageUrl': { //todo 图片宽高待确定
        'title': '磁贴背景图:',
        'type': 'string',
        'format': 'uri',
        'x-ui-widget': 'img-upload',
        'x-ui-validate': {
          'width': 750,
          'height': 464,
          'accept': 'png,jpeg,jpg,apng'
        }
      },
    },
  };

  function onFormChange(formData){
    setFormData(formData)
  }

  function saveSetting() {
    let requestDto = {
      pageId,
      ...formData
    }
    editPageStyle(requestDto)
      .then((result) => {
        // console.log(result);
        Message.success('设置成功')
      }).catch(api.onRequestError);
  }

  return (
    <div className="resource-body" style={{width: '60%',marginTop:'20px'}}>
      <SchemaForm formData={formData} schema={schema} onChange={(formData)=>onFormChange(formData)}/>
      <Button type={'primary'} style={{marginLeft:'20%'}} onClick={()=>saveSetting()}>保存设置</Button>
    </div>
  )
}

export default function MarketPage({pageId = ''}) {
  const [type, setType] = useState(1);
  const [data, setPageData] = useState('');
  const [formData, setFormData] = useState();
  useEffect(() => {
    getPageData();
  }, [0])
  function getPageData() {
    getPageBaseConfig({pageId})
      .then((result) => {
        console.log(result);
        setPageData(result);
        setFormData({
          backgroundImageType:result.backgroundImage,
          backgroundColor:result.backgroundColor,
          backgroundImageUrl:result.backgroundImageUrl,
          subImageUrl: result.subImageUrl
        })

      }).catch(api.onRequestError);
  }
  const btnGroup = [
    {name: '基本信息', type: 1},
    {name: '氛围配置', type: 2},
  ]
  return (
    <div className='page-setting'>
      <div className='ops'>
        {btnGroup.map((v) => {
          return <Button type={type == v.type ? 'primary' : 'default'} onClick={() => setType(v.type)}>{v.name}</Button>
        })}
      </div>
      {type == 1 && <BaseInfo pageId={pageId} data={data} />}
      {type == 2 && <AtmosphereSetting pageId={pageId} data={formData}/>}
    </div>
  )
}




