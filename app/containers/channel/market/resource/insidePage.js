import {Grid,<PERSON>,But<PERSON>,Dialog,Breadcrumb} from '@alife/next'
import {FilterForm, FilterItem} from '@/components/filter'
import React from 'react'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link,withRouter} from 'react-router-dom'
import MarketResourceList from '@/containers/channel/market/comps/list.js'
import useTableQuery from '@/components/useTableQuery'
// import {fetchList} from '../api'
import {benefitMap, pageMap} from '../common'
import './index.scss'
import {promotionACL, updateActivityState} from "../../../decoration/activity/api";
const {Row, Col} = Grid;


export default function MarketResourceInside() {
  const pageUrl = location.hash;
  const currentPage = pageMap.filter(v => pageUrl.includes('#'+v.url))[0];
  const {pageId} = currentPage;
  return (
    <MarketResourceList resourceMap={benefitMap} pageId={pageId} />
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(MarketResourceInside), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MarketResourceInsidePage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})





