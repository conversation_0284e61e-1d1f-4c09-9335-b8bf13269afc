import React from 'react';

import {PageWrapper} from '@/components/PageWrapper';
import { Breadcrumb, Form, Radio, Checkbox, Field, Grid, Icon, Button, Message, NumberPicker, Switch, CascaderSelect, Select} from '@alife/next';
import {DialogBtn} from '@/components/Button';
import {PageBase} from '@/containers/base';
import {getAllDeliveryChannel, onRequestError} from '@/utils/api';
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import {getResource, saveResourcePutInConfig} from '../request';
import {pageMap} from '../common'
import './resourcePutInSet.scss'
import {formItemLayout, rulesSettingItemLayout} from "../../../PutInPage/CreatePutInPage/constants";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const {Row, Col} = Grid;
const Option = Select.Option;

const DeRepeatShopGoodText = () => {
  return (
    <div className="text">
      门店<span className='tip'><Icon type='prompt' size="x"/>按门店id去重</span>
      商品<span className='tip'><Icon type='prompt' size="x"/>按商品item-id去重</span>
    </div>
  )
}

const dataDistinctRadioMap = [
  {
    value: 1,
    label: <DeRepeatShopGoodText/>,
  }
]

// const goodsNameDistinctRadioMap = [
//   {
//     value: 1,
//     label: '是',
//   },
//   {
//     value: 0,
//     label: '否',
//   }
// ]


const componentDistinctRadioMap = [
  {
    value: 1,
    label: '同一组件内所以配置数据依次去重'
  }
]

const resourcePutInSetItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 15
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
}

const formLayout = {
  style: {
    margin: '20px auto'
  },
}

const StrategyList = ["3049", "3064", "3001","9037","9016","9039","7041","7022","7009","25023",
                      "100003049", "100003064", "100003001",,"100009037","100009016","100009039","100007041","100007022","100007009","100025023"];

// 大旋风分磁铁和气泡
const BigStorm = ["3062","7038","9034","8026","100003062","100007038","100009034","100008026","100001003"];
class ResourcePutInSet extends PageBase {
  constructor(props) {
    super(props)
    let {pageId = '', resourceId = '', resourceName = '', resourceType = ''} = this.query;
    this.state = {
      tabidx: 1, // 1我负责的、0可查看的
    }
    const {pathname=''} = this.location;
    this.state = {
      channelListMap: [],
      labelListMap: [],
      defaultValue: {
        channelList: [],
        channelLabel: [],
        dataDistinct: 1,
        componentDistinct: 1,
        goodsNameDistinct:0,
        bigWhirlwindMagnet:1,
        largeCycloneBubble:1
      },
      resourceType,
      pageId, // 页面id
      marketPageBreadInfo: {}, //商超频道页面包屑信息
      curPageBreadInfo: {}, //当前页页面包屑信息
      resourceId, // 资源位id
      resourceName, // 资源位名称
      checkAll: false,
      locationList:[],
      pageUrl: pathname.substring(0, pathname.lastIndexOf('/'))
    }

    this.field = new Field(this, {
      values: {...this.state.defaultValue}
    });
  }

  async componentDidMount() {
    // 获取数据
    await this.getResource();
  }

  handleChannelList = (channelList, deliveryChannelShow) => {
    channelList.forEach(channelItem => {
      if (deliveryChannelShow.indexOf('*') !== -1) {
        channelItem.checkboxDisabled = false;
      } else {
        channelItem.checkboxDisabled = true;
        deliveryChannelShow.forEach(key => {
          const comparedKey = channelItem.value === 'mini_app' ? 'miniapp' : channelItem.value;
          if (comparedKey.indexOf(key) != -1) {
            channelItem.checkboxDisabled = false;
          }
        })
      }
      if (channelItem.subChannels) {
        channelItem.children = JSON.parse(JSON.stringify(channelItem.subChannels));
        this.handleChannelList(channelItem.children, deliveryChannelShow);
      } else {
        channelItem.children = [];
      }
    })
  }

  async getResource () {
    let { resourceId, pageId, locationList} = this.state;
    let params = {
      pageId,
      resourceId
    }
    // 获取渠道列表
    let { channelList: channelListMap, labelList: labelListMap } = await getAllDeliveryChannel();

    getResource({ ...params }).then(({ channelList, channelLabel, deliveryChannel, dataDistinct, goodsNameDistinct, componentDistinct, deliveryChannelShow = '', loadingType, recallSizePre,deliveryStrategy,sortModeList = []}) => {
      deliveryChannelShow = deliveryChannelShow.replace('mini_app', 'miniapp').split(',');

      // 给channelList设置disabled值, 特殊处理一下mini_app，因为只有它和它的下级渠道value不一致，影响判断。
      this.handleChannelList(channelListMap, deliveryChannelShow);
      let curDefaultValue = {
        channelList: channelList ? channelList.split(',') : [], // 投放渠道
        channelLabel: channelLabel ? channelLabel.split(',') : [], // 投放标签
        dataDistinct: dataDistinct || 1,
        componentDistinct: componentDistinct || 1,
        goodsNameDistinct: goodsNameDistinct || 0,
        deliveryChannel: deliveryChannel.split(','),
        recallSizePre,
        deliveryStrategy,
        loadingType
      }
      if (sortModeList && sortModeList.length!=0) {
        sortModeList.map((item)=>{
          // 1是磁贴，2是气泡
          if (item.locationType == "1") {
            curDefaultValue.bigWhirlwindMagnet = item.sortMode
          }else if(item.locationType == "2"){
            curDefaultValue.largeCycloneBubble = item.sortMode
          }
        })
      }
      this.field.setValues({...curDefaultValue})
      this.setState({
        defaultValue: {...curDefaultValue},
        channelListMap,
        labelListMap: labelListMap,
        locationList: locationList || []
      })
    }).catch(onRequestError)
  }

  saveResourcePutInConfig = () => {
    let {resourceId, pageId} = this.state;
    let { channelList, channelLabel,bigWhirlwindMagnet,largeCycloneBubble } = this.field.getValues();
    let tempChannelList = [...channelList];
    let params = {
      pageId,
      resourceId,
      ...this.field.getValues(),
      channelList: String(tempChannelList),
      channelLabel: String(channelLabel),
      deliveryChannel: String(this.field.getValue('deliveryChannel'))
    }
    if (BigStorm.includes(resourceId)) {
      let sortModeList = [{
        locationType:"1",
        sortMode:bigWhirlwindMagnet
      },{
        locationType:"2",
        sortMode:largeCycloneBubble
      }]
      params.sortModeList = sortModeList
      params.deliveryStrategy = 3
    }
    saveResourcePutInConfig({...params}).then(() => {
      Message.success('保存成功')
    }).catch(onRequestError)
  }

  changeDeliveryChannel = (channelList) => {
    this.field.setValue('channelList', channelList);
  }

  cancelEdit = () => {
    this.field.setValues({...this.state.defaultValue});
  }

  submitHandler = () => {
    this.field.validate((error) => {
      if (error === null) {
        // 发请求
        this.saveResourcePutInConfig()
      }
    });
  }

  showMinDisplay = () => {
    const {locationList} = this.state;
    let result = false;
    if (locationList && locationList.length > 0) {
      const minDisplayGroup = locationList.filter(v => v.minDisplay >= 0);
      result = minDisplayGroup.length > 0;
    }
    return result;
  }

  changeGoodsNameDistinct = (checked) =>{
    this.field.setValue("goodsNameDistinct", (checked) ? 1 : 0);
  }

  render() {
    let {pageUrl, channelListMap = [], resourceId, locationList, labelListMap = [], resourceName, marketPageBreadInfo = {}, curPageBreadInfo = {}, resourceType, pageId} = this.state;
    let { channelList, dataDistinct, componentDistinct, goodsNameDistinct, recallSizePre,deliveryStrategy, loadingType,bigWhirlwindMagnet,largeCycloneBubble} = this.field.getValues();
    return (
      <PageBase.Container className="resource-putin-set-page">
        <div className="nav-wrapper">
          <div className="add-breadcrumb">
            <span>频道管理 > </span>
            {/*<Breadcrumb.Item><Link style={{textDecoration:'underline'}} to={`${pageUrl}/${pageId}?anchorId=${resourceId}`}>{resourceName}</Link></Breadcrumb.Item>*/}
            <a href={`#${pageUrl}/${pageId}?anchorId=${resourceId}`}>{resourceName} > </a>
            <span style={{color:'#363636'}}>投放配置</span>
          </div>
          {/*<Breadcrumb>*/}
          {/*  <Breadcrumb.Item>频道管理</Breadcrumb.Item>*/}
          {/*  {curPageBreadInfo.pageId != '200' ? <Breadcrumb.Item><Link*/}
          {/*    to={curPageBreadInfo.url}>{curPageBreadInfo.pageName}</Link></Breadcrumb.Item> : <></>}*/}
          {/*  <Breadcrumb.Item><Link to={marketPageBreadInfo.url}>资源位配置</Link></Breadcrumb.Item>*/}
          {/*  <Breadcrumb.Item>{resourceName}</Breadcrumb.Item>*/}
          {/*  <Breadcrumb.Item>投放配置</Breadcrumb.Item>*/}
          {/*</Breadcrumb>*/}
        </div>
        <PageWrapper title="投放配置">
          <Row className="set-title-wrap">
            <Col span={8} className='set-title'>投放配置</Col>
          </Row>
          <Form className="putin-set-form" {...formLayout} field={this.field}>
            <FormItem label="投放渠道：" {...resourcePutInSetItemLayout} validator={this.checkDeliveryChannel}>
              <CascaderSelect name="channelList" multiple value={channelList} dataSource={channelListMap} onChange={(changeDeliveryChannel) => this.changeDeliveryChannel(changeDeliveryChannel)}/>
            </FormItem>
            {/*<FormItem label="投放标签：" {...resourcePutInSetItemLayout}>*/}
            {/*  <Checkbox.Group name="channelLabel" dataSource={labelListMap}/>*/}
            {/*</FormItem>*/}
            <FormItem label="数据去重：" {...resourcePutInSetItemLayout}>
              <RadioGroup className="data-distinct" name="dataDistinct" value={dataDistinct} dataSource={dataDistinctRadioMap}/>
            </FormItem>
            <FormItem label=" " {...resourcePutInSetItemLayout}>
              <RadioGroup name="componentDistinct" value={componentDistinct} dataSource={componentDistinctRadioMap}/>
            </FormItem>
            <FormItem label="去重能力扩展：" {...resourcePutInSetItemLayout}>
              <FormItem label="商品名称去重" {...rulesSettingItemLayout}>
                <Switch checked={goodsNameDistinct} style={{ 'top': '7px'}} name='goodsNameDistinct' onChange={this.changeGoodsNameDistinct} />
              </FormItem>
              {/*<RadioGroup className="data-distinct" name="goodsNameRepeat" value={goodsNameRepeat} dataSource={goodsNameRepeatRadioMap}/>*/}
            </FormItem>
            {StrategyList.includes(resourceId) && <FormItem label="排序方式" {...resourcePutInSetItemLayout}>
              <Select value={deliveryStrategy} defaultValue={1} onChange={(newVal) => this.field.setValue('deliveryStrategy', newVal)}>
                <Option value={1}>权重排序</Option>
                <Option value={2}>赛马排序</Option>
              </Select>
            </FormItem>}
            {BigStorm.includes(resourceId) && (<>
              <FormItem label="大旋风磁贴" {...resourcePutInSetItemLayout}>
                <Select value={bigWhirlwindMagnet} defaultValue={1} onChange={(newVal) => this.field.setValue('bigWhirlwindMagnet', newVal)}>
                  <Option value={1}>权重排序</Option>
                  <Option value={2}>赛马排序</Option>
                </Select>
              </FormItem>
              <FormItem label="大旋风气泡" {...resourcePutInSetItemLayout}>
              <Select value={largeCycloneBubble} defaultValue={1} onChange={(newVal) => this.field.setValue('largeCycloneBubble', newVal)}>
                <Option value={1}>权重排序</Option>
                <Option value={2}>赛马排序</Option>
              </Select>
            </FormItem>
            </>)}
            {resourceType == 40 && <FormItem label="每次请求数量" {...resourcePutInSetItemLayout}>
              <Select value={recallSizePre} defaultValue={10} onChange={(newVal) => this.field.setValue('recallSizePre', newVal)}>
                <Option value={5}>5</Option>
                <Option value={10}>10</Option>
                <Option value={20}>20</Option>
                <Option value={30}>30</Option>
              </Select>
            </FormItem>}
            {resourceType == 40 && <FormItem label="加载方式" {...resourcePutInSetItemLayout}>
              <Select value={loadingType} defaultValue={2} onChange={(newVal) => this.field.setValue('loadingType', newVal)}>
                <Option value={2}>点击加载</Option>
                <Option value={1}>自动加载</Option>
              </Select>
            </FormItem>}
            <FormItem className="ope-btns" label=" "{...resourcePutInSetItemLayout} >
              <DialogBtn onOk={this.cancelEdit} content={'你所编辑的内容将清空，确认取消吗？'}>
                {/*<Button type="normal">取消</Button>*/}
                <button className="next-btn next-medium next-btn-normal"><span className='next-btn-helper'>取消</span></button>
              </DialogBtn>
              <Form.Submit validate type="primary" onClick={this.submitHandler}>保存</Form.Submit>
            </FormItem>
          </Form>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}

export const LogTimeResourcePutInSet = logTimeComponent(ResourcePutInSet, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ResourcePutInSetPage = permissionAccess(LogTimeResourcePutInSet, async (activityId) => {
  return await ACLPermission(activityId);
})
