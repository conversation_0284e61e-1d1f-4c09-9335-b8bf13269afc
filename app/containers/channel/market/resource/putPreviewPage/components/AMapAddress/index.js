import React, { useState, useEffect, memo } from "react";
import { Map, Marker } from "react-amap";
import { Select } from "@alife/next";
const mapKey = "776b6523308e1f587b85d5d00964f893";

const AMapAddress = ({ onChange, position = {} }) => {
  const createdPlugins = {
    created: () => {
      let auto;
      let placeSearch;
      window.AMap.plugin("AMap.Autocomplete", () => {
        auto = new window.AMap.Autocomplete({
          input: "tipinput",
          pageSize: 10,
          pageIndex: 1,
        });
      });

      window.AMap.plugin("AMap.Geocoder");

      // 创建搜索实例
      window.AMap.plugin("AMap.PlaceSearch", () => {
        placeSearch = new window.AMap.PlaceSearch({
          input: "tipinput",
          pageSize: 10,
          pageIndex: 1,
        });
      });

      window.AMap.event.addListener(auto, "select", (e) => {
        placeSearch.search(e.poi.name);
      });
    },
  };
  const [poiList, setPoiList] = useState([]);
  const [curPosition, setCurPosition] = useState({
    lat: 31.232975,
    lng: 121.38228,
  });
  const [address, setAddress] = useState(
    "上海市普陀区长风新村街道近铁城市广场北座"
  );

  useEffect(() => {
    if (position.lat && position.lng && window.AMap.Geocoder) {
      if (
        curPosition.lat === position.lat &&
        curPosition.lng === position.lng
      ) {
        return;
      }
      const geocoder = new window.AMap.Geocoder();
      geocoder.getAddress(
        [position.lng, position.lat],
        function (status, result) {
          if (status === "complete" && result.regeocode) {
            const address = result.regeocode.formattedAddress;
            setAddress(address);
          }
        }
      );

      setCurPosition(position);
    }
  }, [position]);
  const onSearch = (value) => {
    const place = new window.AMap.PlaceSearch({
      pageSize: 10,
      pageIndex: 1,
    });
    place.search(value, (status, result) => {
      const { info, poiList = {} } = result;
      const { pois = [] } = poiList;
      setPoiList(pois);
    });
  };

  return (
    <div>
      <div>
        <p>定位选择</p>
        <p>
          <Select
            placeholder="请填写定位地址"
            dataSource={poiList.map((v) => ({
              label: v.name,
              value: v.location,
            }))}
            filterLocal={false}
            style={{ width: "500px" }}
            showSearch
            value={address}
            onChange={(value, action, item) => {
              setCurPosition(value);
              onChange(value);
              setAddress(item.label);
            }}
            onSearch={onSearch}
          />
        </p>
      </div>
      <br />
      <div style={{ height: "300px", width: "500px" }}>
        <Map
          events={createdPlugins}
          amapkey={mapKey}
          plugins={["ToolBar", "Scale"]}
          zoom={15}
          center={curPosition}
        >
          <Marker position={curPosition} />
        </Map>
      </div>
    </div>
  );
};

export default memo(AMapAddress);
