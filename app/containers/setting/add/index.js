/**
 *  AddPage
 *
 */

import React from 'react';
import './index.scss';
import { Radio, Input, Button, Message } from '@alife/next';
import { completedGoodsPool, completedStorePool, onRequestError } from '@/utils/api';
import {PageBase} from "@/containers/base";
import * as api from "@/utils/api";
const RadioGroup = Radio.Group;
const typeList = [
  {
    value: 'shop',
    label: '门店id'
  }, {
    value: 'goods',
    label: '商品id'
  }
];
const addList = [
  {
    value: '1',
    label: '是'
  }, {
    value: '0',
    label: '否'
  }
];
export class AddPage extends PageBase {
  constructor(props){
    super(props);

    this.state = {
      poolType: "shop", //选品集类型
      poolId: "", //选品集ID
    }
  }

  handleChange = (value,type) => {
    let options = {};
    options[type] = value;
    this.setState(options);
  }

  async editPool() {
    const {poolType, poolId, nearly7DaysOrders, nearly30DaysOrders, nearly7DaysTurnover, nearly30DaysTurnover, nearly7DaysMinHistoryTransactionPrice, nearly15DaysMinHistoryTransactionPrice} = this.state;
    let data = {
      itemId: poolId,
      nearly7DaysOrders,
      nearly30DaysOrders,
      nearly7DaysTurnover,
      nearly30DaysTurnover,
      nearly7DaysMinHistoryTransactionPrice,
      nearly15DaysMinHistoryTransactionPrice
    }
    let request = (poolType == 'shop') ? (completedStorePool) : (completedGoodsPool);
    await request((poolType == 'shop') ? poolId : data).then((res) => {
      if (res && res == "ok") {
        Message.success('操作成功')
      } else {
        Message.error("操作失败");
      }
    }).catch(onRequestError)
  }

  reset = () =>{
    let { poolType, poolId, nearly7DaysOrders,nearly30DaysOrders,nearly7DaysTurnover,nearly30DaysTurnover,nearly7DaysMinHistoryTransactionPrice,nearly15DaysMinHistoryTransactionPrice} = this.state;
    poolId =  '';
    nearly7DaysOrders = nearly30DaysOrders = nearly7DaysTurnover = nearly30DaysTurnover = nearly7DaysMinHistoryTransactionPrice = nearly15DaysMinHistoryTransactionPrice = '';
    this.setState({
      poolId,
      nearly7DaysOrders,
      nearly30DaysOrders,
      nearly7DaysTurnover,
      nearly30DaysTurnover,
      nearly7DaysMinHistoryTransactionPrice,
      nearly15DaysMinHistoryTransactionPrice
    })
  }

  render(){
    let { poolType, poolId, nearly7DaysOrders,nearly30DaysOrders,nearly7DaysTurnover,nearly30DaysTurnover,nearly7DaysMinHistoryTransactionPrice,nearly15DaysMinHistoryTransactionPrice} = this.state;
    return (
      <div className="edit-page">
        <h3>日常商户&商品基础</h3>
        <div className="edit-item">
          <span className="item-label">类型：</span>
          <RadioGroup dataSource={typeList} value={poolType} onChange={(val)=>this.handleChange(val,"poolType")} />
          <Input placeholder="请输入数字" value={poolId} onChange={(val)=>this.handleChange(val,"poolId")} />
        </div>
        <div className="edit-item">
          <span className="item-label">7天有效订单数：</span>
          <Input placeholder="请输入正整数" value={nearly7DaysOrders} onChange={(val)=>this.handleChange(val,"nearly7DaysOrders")} className="poolid-input"/>
        </div>
        <div className="edit-item">
          <span className="item-label">30天有效订单数：</span>
          <Input placeholder="请输入正整数" value={nearly30DaysOrders} onChange={(val)=>this.handleChange(val,"nearly30DaysOrders")} className="poolid-input"/>
        </div>
        <div className="edit-item">
          <span className="item-label">7天交易额：</span>
          <Input placeholder="请输入正数" value={nearly7DaysTurnover} onChange={(val)=>this.handleChange(val,"nearly7DaysTurnover")} className="poolid-input"/>
        </div>
        <div className="edit-item">
          <span className="item-label">30天交易额：</span>
          <Input placeholder="请输入正数" value={nearly30DaysTurnover} onChange={(val)=>this.handleChange(val,"nearly30DaysTurnover")} className="poolid-input"/>
        </div>
        <div className="edit-item">
          <span className="item-label">是否7天最低价：</span>
          <RadioGroup dataSource={addList} value={nearly7DaysMinHistoryTransactionPrice} onChange={(val)=>this.handleChange(val,"nearly7DaysMinHistoryTransactionPrice")} />
        </div>
        <div className="edit-item">
          <span className="item-label">是否15天最低价：</span>
          <RadioGroup dataSource={addList} value={nearly15DaysMinHistoryTransactionPrice} onChange={(val)=>this.handleChange(val,"nearly15DaysMinHistoryTransactionPrice")} />
        </div>
        {/*<div className="edit-item">
          <span className="item-label">门店ID：</span>
          <Input placeholder="请输入选品集ID" value={shopId} onChange={(val)=>this.handleChange(val,"shopId")} className="shopid-input"/>
        </div>*/}
        <div className="edit-btns">
          <Button type="primary" className="add-btn" onClick={()=>this.editPool()}>保存</Button>
          <Button type="secondary" className="delete-btn" onClick={()=>this.reset()}>取消</Button>
        </div>
      </div>
    )
  }
}
