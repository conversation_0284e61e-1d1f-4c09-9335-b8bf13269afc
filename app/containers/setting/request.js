import { request } from '../../utils/request';
import { putInReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

const requestInstance = request();

export function selectByCondition(params){
  return requestInstance.post('/api/user/pool/limit/selectByCondition', params).then(onRequestSuccess)
}


export function addLimit(params){
  return requestInstance.post('/api/user/pool/limit/add', params).then(onRequestSuccess)
}

export function updateLimit(params){
  return requestInstance.post('/api/user/pool/limit/update', params).then(onRequestSuccess)
}

export function deleteLimit(params){
  return requestInstance.post('/api/user/pool/limit/delete', params).then(onRequestSuccess)
}


