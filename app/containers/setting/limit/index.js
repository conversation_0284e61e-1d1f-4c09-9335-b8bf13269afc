import React, {Fragment} from 'react';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import { Form, Input, Grid, Button, Select, Table, Field, Pagination, Dialog, Message} from '@alife/next';
import {Link} from 'react-router-dom';
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {TimeRangePicker} from '@/components/TimeRangePicker';
import moment from 'moment';
import * as api from '@/utils/api';
import {selectByCondition,addLimit,updateLimit,deleteLimit} from "../request";
const Option = Select.Option;
const FormItem = Form.Item;
const {Row, Col} = Grid;
const formItemLayout = {
  labelCol: {span: 8},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'left',
  labelTextAlign: 'right'
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}
const isDeleteMap = {
  1:'已删',
  0:'未删'
}
const defaultPage = 1;
const defaultPageSize = 10;

const formItemLayoutNew = {
  labelCol: {span: 4},
  wrapperCol: {span: 20}
};
class EditForm extends React.Component {
  constructor(props){
    super(props);
    console.log(props.item);

    this.field = new Field(this, {
      onChange: (name, value) => {
        this.onFieldChange(name, value);
      }
    })
  }

  //初始化数据
  init(props){
    let formData = props.item;
    const fieldValue = Object.keys(formData).reduce((field, key) => {
      return {...field, [key]: formData[key]}
    }, {})

    this.field.setValues(fieldValue);
  }

  componentDidMount() {
    // this.onSearch();
    if(this.props.item){
      this.init(this.props);
    }
  }

  onFieldChange= (name,value) =>{
    this.setState({
      [name]:value
    })
  }

  handleSubmit = () =>{
    // let query
    console.log(this.field.getValues());
    this.submit({
        ...this.field.getValues()
    });
  }

  async submit(query){
    try {
      let request = addLimit;
      let msgText = "新建";
      if(this.props.item){
        request = updateLimit;
        msgText = "编辑";
      }
      const res = await request({...query});
      console.log(res);
      Message.success({content: `${msgText}成功`});
      this.props.onSearch();
      this.props.handleForm(false);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  reset = () =>{
    let filters = [];
    for(var o in this.field.getValues()){
      filters.push(o);
    }
    const blankVal = filters.reduce((query, search) => ({ ...query, [search]:'' }), {});
    this.field.setValues(blankVal);
    this.props.handleForm(false);
  }

  render() {
    return (
      <Form field={this.field}  style={{maxWidth: '500px'}} size={'medium'} {...formItemLayoutNew} >
        <FormItem label="用户ID:">
          <Input name="userId" placeholder="请输入用户ID"/>
        </FormItem>
        <FormItem label="上限值:">
          <Input name="poolLimit" placeholder="请输入上限值"/>
        </FormItem>
        <FormItem label="是否删除:">
          {/*<Input name="isDelete" placeholder="请选择"/>*/}
          <Select name="isDelete" >
            <Option value={1}>已删</Option>
            <Option value={0}>未删</Option>
          </Select>
        </FormItem>
        <FormItem label=" ">
          <Form.Submit type='primary' onClick={this.handleSubmit}>确认</Form.Submit>&nbsp;&nbsp;
          <Button  onClick={this.reset}>取消</Button>
        </FormItem>
      </Form>
    )
  }
}
export class LimitPage extends PageBase {
  constructor(props) {
    super(props);
    let columns = [
      { title: '用户', dataIndex: '1583135941000', width:110, cell: (value) => formatTimeStamp(value, FORMAT.TIMETwo) },
      { title: '用户ID', dataIndex: 'userId', width:90 },
      { title: '用户类型', dataIndex: 'userType', width:90 },
      { title: '上限值', dataIndex: 'poolLimit', width:140},
      { title: '是否删除', dataIndex: 'isDelete', width:140, cell: (value) => isDeleteMap[value]},
      {
        title: '操作', cell: (value, index, record) => {
          return <div className="opbtns">
            <a onClick={()=>this.updateRecord(record)}>编辑</a>
            <a onClick={()=>this.deleteRecord(record)}>删除</a>
          </div>
        },
        width:'10%'
      }
    ];
    this.state = {
      columns,
      page: defaultPage,
      pageSize: defaultPageSize,
      visibleForm: false,
      item: ''
    }


    this.field = new Field(this, {
      onChange: (name, value) => {
        this.onFieldChange(name, value);
      }
    })
  }

  updateRecord = (record) =>{
    this.setState({
      item:{
        userId:record.userId,
        poolLimit:record.poolLimit,
        isDelete:record.isDelete
      },
      visibleForm:true
    })
  }

  async deleteRecord (record){
    Dialog.confirm({
      title: '提示',
      content: '确定删除?',
      onOk: async () => {
        // const {tabidx, selectedRows, basePoolId} = this.state;
        try {
          let query = {
            userId:record.userId
          }
          const res = await deleteLimit({...query});
          console.log(res);
          this.toast({content: '删除成功'});
          this.onSearch();
        } catch (error) {
          api.onRequestError(error)
        }
      }
    });
  }

  componentDidMount() {
    this.onSearch();
  }

  onFieldChange= (name,value) =>{
    this.setState({
      [name]:value
    })
  }

  initTable = () => {
    //接口慢，先初始化
    this.setState({listData: [], isLoading: true});
  }

  async searchData(query) {
    this.initTable();
    const res = await selectByCondition({...query});
    this.setState({
      listData:res.rows,
      total:res.total,
      isLoading:false
    })
  }

  onSearch = () =>{
    let { page, pageSize} = this.state;
    let {userId,isDelete} = this.field.getValues();
    this.setState({isLoading:true});
    let query = ({
      userId,
      isDelete
    })
    this.searchData({page,size:pageSize,query});
  }

  onPageChange = (page) => {
    this.setState({
      page
    },()=>{
      this.onSearch()
    })
  }

  onPageSizeChange = (pageSize) =>{
    let { page } = this.state;
    if (pageSize != this.state.pageSize) {
      page = 1;
    }
    this.setState({
      pageSize,
      page
    },()=>{
      this.onSearch()
    })
  }

  reset = () =>{
    let filters = [];
    for(var o in this.field.getValues()){
      filters.push(o);
    }
    const blankVal = filters.reduce((query, search) => ({ ...query, [search]:'' }), {});
    this.field.setValues(blankVal);
    this.setState({page:defaultPage,pageSize:defaultPageSize},()=>{
      this.onSearch();
    });
  }

  handleForm = (bool) =>{
    this.setState({
      visibleForm:bool,
    })
  }

  showCreate = () =>{
    this.setState({
      visibleForm:true,
      item:''
    })
  }

  render() {
    let { total,columns,page,pageSize, isLoading,item} = this.state;
    return (
      <PageBase.Container className="act-effect">
        <PageWrapper className='filter-list'>
          <Form {...formLayout} field={this.field} style={{marginTop:0}}>
            <Row>
              <Col span={6}>
                <FormItem label="用户ID" {...formItemLayout}>
                  <Input placeholder="请输入页面ID" name="userId"/>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="是否删除" {...formItemLayout}>
                  <Select name="isDelete" >
                    <Option value={1}>已删</Option>
                    <Option value={0}>未删</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span={8}>
                <div style={{marginLeft: "96px"}}>
                  <Button type="primary" onClick={this.onSearch}>查询</Button>&nbsp;&nbsp;
                  <Button type="normal" onClick={this.reset}>重置</Button>&nbsp;&nbsp;
                  <Button type='primary' onClick={()=>this.showCreate()}>新增</Button>
                  <Dialog
                    title="新增"
                    style={{width:'500px'}}
                    visible={this.state.visibleForm}
                    footer={false}
                    onOk={() => this.handleForm(false)}
                    onCancel={() => this.handleForm(false)}
                    onClose={() => this.handleForm(false)}>
                    <EditForm item={item} onSearch={this.onSearch} handleForm={this.handleForm}/>
                  </Dialog>
                </div>
              </Col>
            </Row>
          </Form>
          <div className='table-wrapper'>
            <div className='center-panel'>
              {total && <span className='total'>共{total}条数据结果</span>}
            </div>
            <Table className='manage-list only-bottom-border' loading={isLoading} dataSource={this.state.listData} onSort={this.onSort}>
              {
                columns.map((e, idx) => {
                  return <Table.Column {...e} key={idx} />
                })
              }
            </Table>
            <Pagination
              style={{textAlign: 'right', marginTop: '20px'}}
              popupProps = {{align:'bl tl'}}
              shape="arrow-only"
              current={page} total={total} pageSize={pageSize}
              onChange={this.onPageChange} pageSizeSelector="dropdown" pageSizePosition="end"
              onPageSizeChange={this.onPageSizeChange} />
          </div>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}
