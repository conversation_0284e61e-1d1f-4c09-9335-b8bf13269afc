import React, { useState, useEffect, useCallback } from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { debounce } from "lodash";
import { queryUserByKeyWord } from "@/containers/channel/market/request";
import { onRequestError } from "@/utils/api";
import {
  publishStatusEnum,
  cleanResultEnum,
  dateRender,
} from "@/home/<USER>/common";
import { isAllNumber } from "@/utils/validators";
import * as api from "@/adator/api";
import { promisify } from "@/utils/others";
import "./index.scss";
import Clipboard from "clipboard";
import {
  Grid,
  Input,
  NumberPicker,
  Button,
  Message,
  Form,
  Field,
  Table,
  Dialog,
  Balloon,
  Divider,
  Icon,
  Select,
  Pagination,
} from "@alife/next";
const FormItem = Form.Item;
const { Row, Col } = Grid;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};

const formItemLayout2 = {
  labelCol: { span: 5 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};

export default function PooClearSetting(props) {
  const [tableList, setTableList] = useState([]);
  const [query, setQuery] = useState({ needTotalCount: true }); // 请求数据
  const [loading, setLoading] = useState(false);
  const [pageIndex, setPageIndex] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [visible, setVisible] = useState(false);
  const [roleDataSource, setRoleDataSource] = useState([]); //员工工号
  const [isSuperAdministrator, setIsSuperAdministrator] = useState(null);
  const field = Field.useField({
    onChange: (name, value) => {
      if (name == 'cleanResult') {
        setPageIndex(1)
        setPageSize(10)
      }
      query[name] = value;
      setQuery(query);
    },
  });

  const dailogField = Field.useField({
    onChange: (name, value) => {
      dailogField.setValue(name, value);
    },
  });

  const onSearchUser = useCallback(
    debounce((keyword) => {
      if (keyword) {
        var reg = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]+$");
        if (keyword != "" && !reg.test(keyword)) {
          Message.error("不能输入特殊字符");
          return;
        }
        queryUserByKeyWord(keyword)
          .then((data) => {
            const dataSource = data.map((v) => ({
              value: v.empId,
              label: `${v.lastName}_${v.empId}`,
              record: v,
            }));
            setRoleDataSource(dataSource);
          })
          .catch(onRequestError);
      }
    }, 800),
    [roleDataSource]
  );

  useEffect(() => {
    getUserName();
    getSuperAdministrator();
  }, []);

  useEffect(() => {
    if (isSuperAdministrator !== null) {
      getListUselessPools();
    }
  }, [pageIndex, pageSize]);

  const columns = [
    { title: "池子ID", dataIndex: "poolId", width: "100px" },
    { title: "池子名称", dataIndex: "poolName", width: "130px" },
    { title: "池子清除原因", dataIndex: "cleanReason", width: "130px" },
    {
      title: "创建时间",
      dataIndex: "gmtCreate",
      cell: dateRender,
      width: "130px",
    },
    {
      title: "修改时间",
      dataIndex: "gmtModified",
      cell: dateRender,
      width: "130px",
    },
    {
      title: "处理结果",
      dataIndex: "cleanResult",
      width: "130px",
      cell: (value, index, record) => {
        return (
          <>
            {cleanResultEnum.map((i) => {
              if (i.value == value) {
                return i.label;
              }
            })}
          </>
        );
      },
    },
    { title: "处理结果详情", dataIndex: "cleanResultDesc", width: "130px" },
    { title: "创建人", dataIndex: "empId", width: "130px" },
    {
      title: "操作",
      cell: (value, index, record) => {
        return (
          <div className="opbtns">
            {record.cleanResult != 'DONE' && <Button text={true} onClick={() => onDealWith(record)}>
              处理
            </Button>}
          </div>
        );
      },
      width: "100px",
    },
  ];

  const onDealWith = (record) => {
    let { cleanResult, poolId, cleanResultDesc } = record;
    dailogField.setValue("cleanResult", cleanResult);
    dailogField.setValue("poolId", poolId);
    dailogField.setValue("cleanResultDesc", cleanResultDesc);
    setVisible(true);
  };

  // 查询列表
  const getListUselessPools = async () => {
    if (!loading) {
      setLoading(true);
      try {
        let req = {
          ...query,
          cleanResult: field.getValue("cleanResult"),
          pageIndex,
          pageSize,
        };
        let resp = await api.queryCleanPoolList(req);
        setTableList(resp.data.data.data);
        setTotal(resp.data.data.totalPages);
        setLoading(false);
        // 圈选池子相关指标
      } catch (error) {
        setLoading(false);
        setTableList([]);
        setTotal(0);
        api.onRequestError(error);
      }
    }
  };

  const getUserName = async () => {
    try {
      let resp = await api.getBucUser();
      let { empId, lastName } = resp.data.data;
      setRoleDataSource([
        {
          label: lastName,
          value: empId,
        },
      ]);
      query.empId = empId;
      field.setValue("empId", empId);
      field.setValue("cleanResult", "PENDING");
      setQuery(query);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const getSuperAdministrator = async () => {
    try {
      let resp = await api.queryRoleValidate("zxdt_super_administrator");
      setIsSuperAdministrator(resp == "YES");
      getListUselessPools();
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 处理池子
  const getCleanDeal = async () => {
    try {
      await promisify(dailogField.validate)();
      let resp = await api.getCleanDeal(dailogField.getValues());
      if (resp.data.data.success) {
        Message.success("处理成功");
        dailogField.setValues({});
        setVisible(false);
        getListUselessPools();
      }
    } catch (error) {
      setVisible(false);
      api.onRequestError(error);
    }
  };

  // 重置表单
  const resetPoolList = () => {
    field.reset();
    field.setValues({});
    Object.keys(query).filter((key) => {
      delete query[key];
    });
    setQuery({});
  };

  /**检测id格式 */
  const checkId = function (rule, value, callback) {
    const errors = isAllNumber(value);
    if (errors.length && value) {
      callback("格式错误");
    } else {
      callback();
    }
  };

  return (
    <div className="pool-clear">
      <div className="filter">
        <Form field={field}>
          <div className="top">
            <span className="pool-title">池子清理</span>
          </div>
          <Row>
            <Col span={6}>
              <FormItem label="池子ID" {...formItemLayout} validator={checkId}>
                <NumberPicker name="poolId" type="normal" />
              </FormItem>
            </Col>

            <Col span={6}>
              <FormItem label="状态" {...formItemLayout}>
                <Select
                  name="state"
                  defaultValue={""}
                  dataSource={publishStatusEnum}
                  style={{ width: "100%" }}
                />
              </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label="处理结果" {...formItemLayout}>
                <Select
                  name="cleanResult"
                  defaultValue={""}
                  dataSource={cleanResultEnum}
                  style={{ width: "100%" }}
                />
              </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label="创建人" {...formItemLayout}>
                <Select
                  key={`empId_${isSuperAdministrator}`}
                  showSearch
                  hasClear
                  disabled={!isSuperAdministrator}
                  name="empId"
                  filterLocal={false}
                  hasArrow={false}
                  onSearch={(value) => {
                    onSearchUser(value);
                  }}
                  dataSource={roleDataSource}
                  style={{ width: "100%" }}
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col
              span={24}
              style={{
                marginBottom: "20px",
                display: "flex",
                justifyContent: "end",
              }}
            >
              <Button
                type="secondary"
                onClick={getListUselessPools}
                style={{ marginLeft: "95px" }}
              >
                查询
              </Button>
              <Button className="btn_reset" onClick={resetPoolList}>
                重置
              </Button>
            </Col>
          </Row>
        </Form>
      </div>

      <div className="table-panel">
        <Table
          dataSource={tableList}
          loading={loading}
          hasBorder={false}
          primaryKey="poolId"
        >
          {columns.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
        <Pagination
          onChange={(e) => {
            setPageIndex(e);
          }}
          onPageSizeChange={(e) => {
            setPageSize(e);
          }}
          total={total}
          current={pageIndex}
          pageSize={pageSize}
          pageSizeSelector="dropdown"
          pageSizeList={[10, 20]}
          popupProps={{ align: "bl tl" }}
          style={{ float: "right", marginTop: "10px" }}
        />
      </div>
      <Dialog
        visible={visible}
        onOk={()=>getCleanDeal()}
        onCancel={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
      >
        <div className="deal-content">
          <Form field={dailogField}>
            <FormItem required label="处理方式" {...formItemLayout2}>
              <Select
                name="cleanResult"
                defaultValue={""}
                dataSource={cleanResultEnum}
                style={{ width: "100%" }}
              />
            </FormItem>
            <FormItem required label="备注" {...formItemLayout2}>
              <Input.TextArea
                aria-label="auto height"
                name="cleanResultDesc"
                style={{ width: "100%" }}
              />
            </FormItem>
          </Form>
        </div>
      </Dialog>
    </div>
  );
}

export const LogTimePooClearSetting = logTimeComponent(
  withRouter(PooClearSetting),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const PooClearSettingPage = permissionAccess(LogTimePooClearSetting);
