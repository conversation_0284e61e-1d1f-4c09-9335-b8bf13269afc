 /**
 *  AddPage
 *
 */

import React from 'react';
import './index.scss';
import {Radio, Input, Button, Message, Form, Field} from '@alife/next';
import { extensionMuti, onRequestError } from '@/utils/api';
import {PageBase} from "@/containers/base";
import * as api from "@/utils/api";
import {getPoolDetailByPoolId} from "@/adator/api";
import {TimeRangePicker} from "@/components/TimeRangePicker";
import moment from "moment";
 import {getQueryString, promisify} from "@/utils/others";
const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
}

const formLayout = {
  style: {
    margin: '20px auto'
  },
}

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    // if(start.isBefore(moment().startOf('day'))) return cb('开始时间不能小于当前时间')
    if (end.clone().subtract(4, 'month').isAfter(start.clone())) return cb('时间段不能超过4个月')
  }
  cb()
}

export class EditTimePage extends PageBase {
  constructor(props){
    super(props);
    this.state = {
      // defaultTime: [moment(), moment().add(1, 'months').set({hour: 23, minute: 59, second: 59})],
      poolId: this.params.poolId || '',
      flag:getQueryString("flag")
      // effectAt:moment().valueOf(),
      // expireAt:moment().add(1, 'months').set({hour: 23, minute: 59, second: 59}).valueOf(),
    }

    this.field = new Field(this, {
      onChange: (name, value) => {
        this.setState({
          effectAt: value[0].valueOf(),
          expireAt: value[1].valueOf(),
        })
      }
    });
  }

  componentDidMount() {
    let {flag} = this.state;
    if (flag == 1) {
      this.getPoolDetail();
    } else {
      this.getOldPoolDetail()
    }
  }

  async getOldPoolDetail() {
    const {poolId,baseInfo,params} = this.state;
    try {
      const request = api.ali.getPoolDetail;
      let resp = await request({id:poolId});
      this.setState({
        effectAt: resp.effectAt,
        expireAt: resp.expireAt
      })
      this.field.setValue('effectRange',[moment(resp.effectAt),moment(resp.expireAt)])
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getPoolDetail() {
    const {poolId,baseInfo,params} = this.state;
    try {
      const request = getPoolDetailByPoolId;
      let resp = await request(poolId);
      this.setState({
        effectAt: resp.data.effectAt,
        expireAt: resp.data.expireAt
      })
      this.field.setValue('effectRange',[moment(resp.data.effectAt),moment(resp.data.expireAt)])
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async editPool() {
    const {effectAt, expireAt,poolId} = this.state;
    await (promisify(this.field.validate)());
    let saveItemPoolReq = {
      poolId,
      effectAt,
      expireAt,
    }
    try {
      let request = (extensionMuti);
      const result = await request((saveItemPoolReq));
      if (result.success) {
        Message.success('操作成功');
      } else {
        Message.warning(result.errMessage);
      }
    } catch (error) {
      api.onRequestError(error)
    }
  }

  reset = () =>{

  }

  render(){
    let {defaultTime} = this.state;
    return (
      <div className="edit-page">
        <h3>选品池延期</h3>
        <Form  {...formLayout} field={this.field}>
          <FormItem
            label="有效期："
            {...formItemLayout}
            required
            requiredMessage="请输入时间段"
            validator={timeRangeValidator}
            validatorTrigger={['onChange']}
          >
            <TimeRangePicker showTime={true} name="effectRange" disabledDate={(date) => date.isBefore(moment().startOf('day'))}></TimeRangePicker>
          </FormItem>
        </Form>
        <div className="edit-btns">
          <Button type="primary"  onClick={()=>this.editPool()}>保存</Button>
          <Button type="secondary" style={{marginLeft:'10px'}} onClick={()=>this.reset()}>取消</Button>
        </div>
      </div>
    )
  }
}
