.selection {
  padding: 12px;
  > div {
    background-color: #fff;
    padding: 20px;
  }
  .selection-filter {
    padding-bottom: 0;
    margin-bottom: 10px;
    h3 {
      font-weight: bolder;
    }
    .next-select {
      width: 80%;
    }
  }
  .selection-context {
    .balloon {
      display: flex;
      flex-direction: column;
    }
    .extend-text {
      display: flex;
      flex-direction: row;
      .lable {
        font-weight: bolder;
      }
    }
    .opbtns {
      .next-btn {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        font-weight: bolder;
        color: #333333;
        &:hover {
          color: #ff7000;
        }
      }
    }
  }
}
.filter-item-field {
  .next-select {
    width: 100%;
  }
  textarea {
    min-height: 80px;
    word-break: break-all;
  }
  .next-dialog-body {
    overflow: scroll;
  }
}
