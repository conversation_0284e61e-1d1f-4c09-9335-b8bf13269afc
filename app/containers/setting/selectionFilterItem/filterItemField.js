import React, { useState, useEffect } from "react";
import {
  Form,
  Field,
  Input,
  Select,
  CascaderSelect,
  NumberPicker,
  Grid,
  Radio,
} from "@alife/next";
import {
  TypeOfOperation,
  grouping,
  validateOperation,
  dataSource,
  ruleDataSource,
} from "./index";
const FormItem = Form.Item;
const { Row, Col } = Grid;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};

export default function FilterItemField(props) {
  const { isEdit, filterField, sceneRuleSourceList } = props;

  useEffect(() => {
    initData();
  }, []);

  const initData = () => {};

  return (
    <div className="filter-item-field">
      <Form field={filterField}>
        <FormItem
          label="是否为测试指标:"
          {...formItemLayout}
          required
          requiredMessage="测试指标必选"
        >
          <RadioGroup
            name="isTest"
            value={filterField.getValue("isTest") || ""}
          >
            <Radio value="0">正常</Radio>
            <Radio value="1">测试</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem
          label="底池名称:"
          {...formItemLayout}
          required
          requiredMessage="底池名称必选"
        >
          <CascaderSelect
            disabled={isEdit || false}
            hasClear
            showSearch
            multiple='true'
            name="ruleSource"
            placeholder="请选择池子名称"
            dataSource={sceneRuleSourceList}
            value={filterField.getValue("ruleSource") || ""}
          ></CascaderSelect>
        </FormItem>
        <FormItem
          label="序号:"
          {...formItemLayout}
          required
          requiredMessage="序号必填"
        >
          <NumberPicker
            hasClear
            name="ruleSort"
            value={filterField.getValue("ruleSort") || null}
          />
        </FormItem>
        <FormItem
          label="筛选项名称:"
          {...formItemLayout}
          required
          requiredMessage="筛选项名称必填"
        >
          <Input
            value={filterField.getValue("filterFieldLabel") || ""}
            hasClear
            name="filterFieldLabel"
            placeholder="请输入筛选项名称"
          />
        </FormItem>
        <FormItem
          label="筛选项Id"
          {...formItemLayout}
          required
          requiredMessage="筛选项Id必填"
        >
          <Input
            hasClear
            name="filterFieldId"
            placeholder="请输入筛选项Id"
            value={filterField.getValue("filterFieldId") || ""}
          />
        </FormItem>

        <FormItem
          label="操作类型"
          {...formItemLayout}
          required
          requiredMessage="操作类型必填"
        >
          <Select
            hasClear
            name="filterFieldComponentType"
            placeholder="请选择操作类型"
            value={filterField.getValue("filterFieldComponentType") || ""}
            dataSource={Object.keys(TypeOfOperation).map((item) => {
              return {
                label: TypeOfOperation[item],
                value: item,
                disabled: ["picStandard", "selectSearch"].includes(item),
              };
            })}
          ></Select>
        </FormItem>

        <FormItem
          label="操作符"
          {...formItemLayout}
          required
          requiredMessage="操作符必填"
        >
          <NumberPicker
            hasClear
            name="operator"
            value={filterField.getValue("operator") || null}
          />
        </FormItem>
        <FormItem
          label="分组"
          {...formItemLayout}
          required
          requiredMessage="分组必填"
        >
          <Select
            hasClear
            name="filterFieldIdGroup"
            value={filterField.getValue("filterFieldIdGroup") || ""}
            placeholder="请选择分组"
            dataSource={Object.keys(grouping).map((item) => {
              return {
                label: grouping[item],
                value: item,
                disabled: false,
              };
            })}
          ></Select>
        </FormItem>
        <FormItem label="校验函数名称" {...formItemLayout}>
          <Select
            hasClear
            name="validatorValue"
            placeholder="请选择校验函数名称"
            value={filterField.getValue("validatorValue") || ""}
            dataSource={Object.keys(validateOperation).map((item) => {
              return {
                label: validateOperation[item],
                value: item,
                disabled: false,
              };
            })}
          ></Select>
        </FormItem>
        <FormItem
          label="规则类型"
          {...formItemLayout}
          required
          requiredMessage="数据源类型必填"
        >
          <Select
            hasClear
            name="filterFieldType"
            value={filterField.getValue("filterFieldType")}
            placeholder="请选择规则类型"
            dataSource={Object.keys(ruleDataSource).map((item) => {
              return {
                label: ruleDataSource[item],
                value: item,
                disabled: false,
              };
            })}
          ></Select>
        </FormItem>
        <FormItem
          label="数据源类型"
          {...formItemLayout}
          required
          requiredMessage="数据源类型必填"
        >
          <Select
            hasClear
            name="filterFieldDataType"
            value={filterField.getValue("filterFieldDataType")}
            placeholder="请选择数据源类型"
            dataSource={Object.keys(dataSource).map((item) => {
              return {
                label: dataSource[item],
                value: item,
                disabled: false,
              };
            })}
          ></Select>
        </FormItem>
        {filterField.getValue("filterFieldDataType") > 0 && (
          <FormItem
            label="数据源"
            {...formItemLayout}
            required
            requiredMessage="数据源必填"
          >
            <Input.TextArea
              hasClear
              name="filterFieldData"
              value={filterField.getValue("filterFieldData") || ""}
              autoHeight={{ minRows: 1, maxRows: 6 }}
              placeholder="请输入数据源，静态数据源例如：[{label:'否',value:'0'},{label:'是',value:'1'}]、动态数据源例如（需为get请求）：/api/v2/*"
              maxLength={32767}
              hasLimitHint
            />
          </FormItem>
        )}
        {filterField.getValue("filterFieldDataType") > 0 && (
          <FormItem
            label="云鼎数据源"
            {...formItemLayout}
          >
            <Input.TextArea
              hasClear
              name="filterFieldData4YunDing"
              value={filterField.getValue("filterFieldData4YunDing") || ""}
              autoHeight={{ minRows: 1, maxRows: 6 }}
              placeholder="请输入云鼎接口apiKey"
              maxLength={32767}
              hasLimitHint
            />
          </FormItem>
        )}
        <FormItem label="数值区间拓展" {...formItemLayout}>
          <Input.TextArea
            hasClear
            name="filterFieldExtend"
            value={filterField.getValue("filterFieldExtend") || ""}
            autoHeight={{ minRows: 2, maxRows: 6 }}
            placeholder="请输入数值区间拓展，例如{min:0,max:100,unitNode:'%',step:0.01,precision:1},max:最大值,min:最小值,unitNode:后缀,step:步长,precision:保留小数点后几位"
            maxLength={32767}
            hasLimitHint
          />
        </FormItem>
        <FormItem label="筛选项描述" {...formItemLayout}>
          <Input.TextArea
            name="filterFieldDesc"
            value={filterField.getValue("filterFieldDesc") || ""}
            placeholder="请输入筛选项描述"
            maxLength={32767}
            hasLimitHint
          />
        </FormItem>
        <FormItem label="初始值" {...formItemLayout}>
          <Input.TextArea
            name="filterFieldValue"
            value={filterField.getValue("filterFieldValue") || ""}
            placeholder="请输入筛选项初始值"
          />
        </FormItem>
      </Form>
    </div>
  );
}
