import React, { useState, useEffect } from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import * as api from "@/adator/api";
import ReactJson from "react-json-view";
import FilterItemField from "./filterItemField";
import { promisify } from "@/utils/others";
import "./index.scss";
import Clipboard from "clipboard";
import {
  Grid,
  Button,
  Message,
  Form,
  Field,
  Table,
  Dialog,
  Balloon,
  Divider,
} from "@alife/next";
import { deepCopy } from "@/home/<USER>/common";
import { Select } from "@alife/next";
import { getFilterFieldPolicy } from "@/selection";

const FormItem = Form.Item;
const { Row, Col } = Grid;

export const TypeOfOperation = {
  arrayInput: "文本框",
  radio: "单选框",
  checkbox: "复选框",
  multipleSelect: "下拉多选",
  cascaderSelect: "下拉级联多选",
  picStandard: "牛皮癣图片专用",
  rangeNumberInput: "数值区间",
  selectSearch: "搜索查询多选（所属品牌专用）",
  selectSearchWithApi: "下拉搜索项(自定义搜索接口)",
  batchInputOrUploadOfFiles: "批量输入或上传文件",
  mktConditions: '定制组件-年货玩法配置',
  cpvSelect: getFilterFieldPolicy('cpvSelect').getDisplayName()
};

export const grouping = {
  required: "通用必填项",
  baseInfo: "通用基本信息",
  skuStore: "通用门店信息",
  skuMarket: "通用营销信息",
  skuTrade: "品交易信息",
  scene: "品场景信息",
  basicattributes: "门店基本属性",
  browsedeal: "门店浏览和交易信息",
  marketing: "门店营销信息",
  superMarket: "全能超市"
};

export const validateOperation = {
  idCommaSeperatedRequired: "ID校验(0-100)",
  upcIdCommaSeperated: "upc校验(0-100)",
  nameSeperatedRequired: "名称校验(0-30)",
};

export const dataSource = {
  0: "无",
  1: "静态数据源",
  2: "动态数据源",
};

export const ruleDataSource = {
  0: "品规则",
  1: "专家场景规则",
  2: "店规则",
};

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: {
    span: 18,
  },
  style: {
    width: "100%",
  },
};

export default function SelectionFilterItem(props) {
  const [tableList, setTableList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sceneRuleSourceList, setSceneRuleSourceList] = useState([]);

  const field = Field.useField({
    onChange: (name, value) => {},
  });

  const filterField = Field.useField({
    onChange: (name, value) => {
      if (name == "filterFieldDataType" && value == 0) {
        filterField.setValue("filterFieldData", "");
      }
      filterField.setValue(name, value);
    },
  });

  const [isEdit, setIsEdit] = useState(false);
  const [visible, setVisible] = useState(false);

  // 查询列表
  const searchPoolList = async () => {
    if (!loading) {
      setLoading(true);
      try {
        if (field.getValue("ruleSource")) {
          let resp = await api.getRuleListByRuleSource([field.getValue("ruleSource")]);
          setTableList(resp.data.data.data);
          setLoading(false);
        }
        // 圈选池子相关指标
      } catch (error) {
        setLoading(false);
        setTableList([]);
        api.onRequestError(error);
      }
    }
  };

  // 展示数据源
  const viewFilterFieldData = (value) => {
    Dialog.show({
      v2: "true",
      title: " ",
      content: (
        <ReactJson
          src={JSON.parse(value)}
          name="JSON"
          style={{ width: "80vw", height: "80vh" }}
          displayDataTypes={false}
        ></ReactJson>
      ),
      onOk: () => {},
      footerActions: ["cancel"],
    });
  };

  // 数据评分类拓展字段展示
  const extendMap = (value) => {
    let opbtns = [];
    let opbtnsElement;
    if (value) {
      const newValue = deepCopy(JSON.parse(value) || {});
      Object.keys(newValue).forEach((item) => {
        switch (item) {
          case "max":
            opbtns.push(
              <div className="extend-text">
                <div className="lable">最大值：</div>
                <div className="value">{newValue[item]}</div>
              </div>
            );
            break;
          case "min":
            opbtns.push(
              <div className="extend-text">
                <div className="lable">最小值：</div>
                <div className="value">{newValue[item]}</div>
              </div>
            );
            break;
          case "unitNode":
            opbtns.push(
              <div className="extend-text">
                <div className="lable">后缀：</div>
                <div className="value">{newValue[item]}</div>
              </div>
            );
            break;
          case "precision":
            opbtns.push(
              <div className="extend-text">
                <div className="lable">小数位：</div>
                <div className="value">{newValue[item]}</div>
              </div>
            );
            break;
          case "step":
            opbtns.push(
              <div className="extend-text">
                <div className="lable">步长：</div>
                <div className="value">{newValue[item]}</div>
              </div>
            );
            break;
          default:
            break;
        }
      });
      opbtnsElement = opbtns.map((item) => {
        return item;
      });
    } else {
      opbtnsElement = <>无</>;
    }
    return opbtnsElement;
  };

  // 修改筛选项
  const updateFilterTag = (record) => {
    setIsEdit(true);
    filterField.setValues({
      isTest: "1",
      ...record,
      ruleSource: [field.getValue("ruleSource")],
    });
    setVisible(true);
  };

  // 新增筛选项
  const addFilterTag = () => {
    setIsEdit(false);
    filterField.setValues({
      isTest: "1",
      ruleSource: [field.getValue("ruleSource")],
    });
    setVisible(true);
  };

  // 操作筛选项
  const operationFilterTag = async () => {
    await promisify(filterField.validate)();
    try {
      const { ruleSource, ...others } = filterField.getValues();
      let requestParams = {
        ruleSourceList: ruleSource ? ruleSource : field.getValue("ruleSource"),
        manualRuleDTO: {
          ruleCode: "",
          filterFieldId: "",
          filterFieldLabel: "",
          operator: "",
          filterFieldIdGroup: "",
          filterFieldComponentType: "",
          validatorValue: "",
          filterFieldType: "",
          isDelete: "",
          gmtCreate: "",
          gmtModified: "",
          filterFieldDataType: "",
          filterFieldValue: "",
          filterFieldExtend: "",
          filterFieldData: "",
          filterFieldDesc: "",
          isTest: "",
          ruleSort: "",
          ...others,
          filterFieldKey: others.filterFieldId || "",
        },
      };
      if (isEdit) {
        //修改
        let resp = await api.updateRuleByRuleSource(requestParams);
        if (resp.data.data.success) {
          Message.success("修改成功");
          setVisible(false);
          filterField.setValues({});
          searchPoolList();
        } else {
          Message.success(resp.data.data.errMessage);
        }
      } else {
        //新增
        requestParams.manualRuleDTO.ruleCode = "";
        let resp = await api.addRuleForRuleSource(requestParams);
        if (resp.data.data.success) {
          Message.success("添加成功");
          setVisible(false);
          filterField.setValues({});
          searchPoolList();
        } else {
          Message.success(resp.data.data.errMessage);
        }
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 删除筛选项
  const deleteFilterTag = (record) => {
    Dialog.show({
      v2: "true",
      title: "注意",
      content: "确定要删除当前筛选项吗?",
      onOk: async () => {
        try {
          let params = {
            ruleSource: field.getValue("ruleSource"),
            ruleCode: record.ruleCode,
          };
          let resp = await api.delRuleByRuleSource(params);
          if (resp.data.data.success) {
            Message.success("删除成功");
            filterField.setValues({});
            searchPoolList();
            setLoading(false);
          } else {
            Message.success(resp.data.data.errMessage);
          }
        } catch (error) {
          api.onRequestError(error);
        }
      },
      footerActions: ["ok", "cancel"],
    });
  };

  useEffect(() => {
    (async () => {
      await getSceneRuleSource();
    })();
  }, []);

  // 获取场景列表
  const getSceneRuleSource = async () => {
    try {
      let resp = await api.getSceneRuleSourceList();
      setSceneRuleSourceList(
        resp.data.data.data.map((item) => {
          return {
            label: item.sceneName,
            value: item.ruleSource,
          };
        })
      );
      field.setValue("ruleSource", resp.data.data.data[0].ruleSource);
      searchPoolList();
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const columns = [
    { title: "序号", dataIndex: "ruleSort", width: "60px",lock: "left"},
    { title: "筛选项名称", dataIndex: "filterFieldLabel", width: "160px",lock: "left"},
    {
      title: "筛选项Id",
      dataIndex: "filterFieldId",
      width: "160px",
      cell: (value, index, record) => {
        return <div>{value}</div>;
      },
    },
    {
      title: "操作类型",
      dataIndex: "filterFieldComponentType",
      width: "160px",
      cell: (value, index, record) => {
        return <div>{TypeOfOperation[value]}</div>;
      },
    },
    {
      title: "操作符",
      dataIndex: "operator",
      width: "100px",
      cell: (value, index, record) => {
        return <div>{value}</div>;
      },
    },
    {
      title: "分组",
      dataIndex: "filterFieldIdGroup",
      width: "120px",
      cell: (value, index, record) => {
        return <div>{grouping[value]}</div>;
      },
    },

    {
      title: "校验函数名称",
      dataIndex: "validatorValue",
      width: "160px",
      cell: (value, index, record) => {
        return (
          <div>
            {validateOperation[value] ? validateOperation[value] : "无"}
          </div>
        );
      },
    },
    {
      title: "数据源类型",
      dataIndex: "filterFieldDataType",
      width: "160px",
      cell: (value, index, record) => {
        return <div>{dataSource[value]}</div>;
      },
    },
    {
      title: "数据源",
      dataIndex: "filterFieldData",
      width: "160px",
      cell: (value, index, record) => {
        const clipboard = new Clipboard(`#copy`);
        clipboard.on("success", function (e) {
          console.log(e);
          Message.success("复制成功");
        });
        clipboard.on("error", function (e) {
          console.log(e);
        });
        if (record.filterFieldDataType == 1) {
          return (
            <div>
              <Button
                text
                onClick={() => {
                  viewFilterFieldData(value);
                }}
                style={{
                  color: "#F65100",
                }}
              >
                点击查看
              </Button>
            </div>
          );
        } else if (record.filterFieldDataType == 2) {
          return (
            <Balloon.Tooltip
              align="t"
              trigger={
                <div
                  style={{ cursor: "pointer" }}
                  className="btn-copy"
                  id="copy"
                  data-clipboard-text={value}
                >
                  {value.slice(0, 16)}...
                </div>
              }
            >
              {value}
            </Balloon.Tooltip>
          );
        } else {
          return "无";
        }
      },
    },
    {
      title: "评分拓展",
      dataIndex: "filterFieldExtend",
      width: "120px",
      cell: (value, index, record) => {
        return <div className="balloon">{extendMap(value)}</div>;
      },
    },
    {
      title: "筛选项描述",
      dataIndex: "filterFieldDesc",
      width: "160px",
      cell: (value, index, record) => {
        return <>{value ? value : "无"}</>;
      },
    },
    {
      title: "初始值",
      dataIndex: "filterFieldValue",
      width: "100px",
      cell: (value, index, record) => {
        return <div>{value ? value : "无"}</div>;
      },
    },

    {
      title: "操作",
      lock: "right",
      width: "160px",
      cell: (value, index, record) => {
        return (
          <div className="opbtns">
            <Button text onClick={() => updateFilterTag(record)}>
              修改
            </Button>
            <Divider direction="ver" />
            <Button text onClick={() => deleteFilterTag(record)}>
              删除
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="selection">
      <div className="selection-filter">
        <Form className="filter" field={field}>
          <Row warp="true">
            <h3>选品集筛选项设置</h3>
          </Row>
          <br></br>
          <Row warp="true">
            <Col span={8}>
              <FormItem label="池子名称:" {...formItemLayout}>
                <Select
                  hasClear
                  name="ruleSource"
                  placeholder="请选择池子名称"
                  dataSource={sceneRuleSourceList}
                  onChange={(e, d) => {
                    e && searchPoolList();
                  }}
                ></Select>
              </FormItem>
            </Col>
            <Col span={16} style={{ display: "flex", justifyContent: "end" }}>
              <Button type="primary" onClick={addFilterTag}>
                新增
              </Button>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="selection-context">
        <Table
          dataSource={tableList}
          loading={loading}
          hasBorder={false}
          primaryKey="goodsId"
        >
          {columns.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
      </div>
      <Dialog
        v2="true"
        title=" "
        visible={visible}
        autoFocus
        onOk={() => {
          operationFilterTag();
        }}
        onCancel={() => {
          setVisible(false);
          filterField.setValues({});
        }}
        onClose={() => {
          setVisible(false);
          filterField.setValues({});
        }}
        footerActions={["ok", "cancel"]}
        children={
          <FilterItemField
            filterField={filterField}
            isEdit={isEdit}
            sceneRuleSourceList={sceneRuleSourceList}
          ></FilterItemField>
        }
        style={{ width: "580px",maxHeight: '800px',overflowY:'scroll' }}
      ></Dialog>
    </div>
  );
}

export const LogTimeSelectionFilterItem = logTimeComponent(
  withRouter(SelectionFilterItem),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const SelectionFilterItemPage = permissionAccess(
  LogTimeSelectionFilterItem
);
