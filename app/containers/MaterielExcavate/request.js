import { sceneReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'


export function reportScene(params) {
  return sceneReq.post('/api/scene/write/reportScene', params).then(onRequestSuccess)
}

export function deleteScene(params) {
  return sceneReq.post('/api/scene/write/deleteScene', params).then(onRequestSuccess)
}

export function listScene(params) {
  return sceneReq.post('/api/scene/read/listScene', params).then(onRequestSuccess)
}

export function listViewableScene(params) {
  return sceneReq.post('/api/scene/read/listViewableScene', params).then(onRequestSuccess)
}

export function listSceneDetail(params) {
  return sceneReq.post('/api/scene/read/listSceneDetail', params).then(onRequestSuccess)
}

export function deleteSceneItem(params) {
  return sceneReq.post('/api/scene/write/deleteSceneItem', params).then(onRequestSuccess)
}
