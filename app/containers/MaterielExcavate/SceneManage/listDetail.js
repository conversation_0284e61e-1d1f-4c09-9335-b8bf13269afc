import React, { Component } from 'react';

import {
  Form,
  Grid,
  Button,
  Input,
  Field,
  Table,
  Message,
  MenuButton,
  Pagination,
  Dialog
} from '@alife/next';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import {DialogBtn, LinkButton, GoldLogLink} from '@/components/Button';
import {ACLPermission} from "@/containers//PutInPage/request";
import {AccessBtn} from '@/components/Button/AccessBtn';
import {manageList, setTableCol, filterForm} from '@/components/Table';
import {formatTimeStamp} from '@/utils/time';
import {onRequestError} from '@/utils/api'
import * as validators from '@/utils/validators';
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {doesFormHasErrors} from '@/utils/formTool';
import {filterData} from '@/utils/filterDataByKey';
import {momentToTimeStamp} from '@/utils/time';
import {withoutCopy} from '@/utils/others';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import { listSceneDetail,deleteSceneItem } from '../request';
import './index.scss'
import {LogTimePutInPage} from "@/containers/decoration/activity/edit";
import PreviewImage from '@/components/PreviewImage';
const DEFAULT_GOODS_IMG = require('../../../images/default-goods-pic.png');
const FormItem = Form.Item;
const {Row, Col} = Grid;
const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

const formItemLayout = {
  labelCol: {span:6},
  wrapperCol: {
    span: 17
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'left'
};

export default class ListDetail extends Component {
  constructor(props) {
    super(props)
    this.state = {
      searchForm: {},
      goodList:[],
      itemIdList:[],
      pageNo:1,
      pageSize:10,
      total:0
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        const {searchForm} = this.state;
        searchForm[name] = value;
        this.setState({
          searchForm
        }, ()=> {
          console.log(this.state);
        })
      }
    })
  }

  componentDidMount(){
    this.search();
  }

  search = () =>{
    let self = this;
    this.field.validate((e) => {
      if(!doesFormHasErrors(e)){
        self.setState({pageNo: 1}, () => {
          self.getDetail();
        })
      }
    })
  }

  getDetail = () =>{
    const {data} = this.props;
    const {searchForm, pageSize, pageNo} = this.state;
    let query = {
      sceneName:data.name,
      searchItemName:searchForm.searchItemName,
      searchItemIdList: searchForm.searchItemIdList ? searchForm.searchItemIdList.split(",") : [],
      pageNo,
      pageSize
    }
    listSceneDetail({...query}).then((data) => {
      this.setState({
        goodList:data.rows,
        total:data.total
      })
    }).catch(onRequestError)
  }

  reload = () => {
    const f = this.field.getValues();
    for (var o in f) {
      this.field.setValue(o, '');
    }
    this.setState({searchForm: {}, pageNo: 1}, () => {
      this.getDetail()
    });
  }

  onSelectChange = (...args) =>{
    this.setState({
      itemIdList: args[0]
    });
  }

  delItem = () =>{
    const {data} = this.props;
    const {itemIdList} = this.state;
    let cmd = {
      sceneName:data.name,
      itemIdList
    }
    let self = this;
    Dialog.confirm({
      title: '是否删除',
      onOk: () => {
        deleteSceneItem({...cmd}).then((data) => {
          console.log(data);
          Message.success('删除成功');
          self.getDetail();
        }).catch(onRequestError)
      }
    });
  }

  delSingle = (record) => {
    this.setState({
      itemIdList: [record.itemId]
    }, () => this.delItem());
  }

  onChange = (currentPage) => {
    this.setState({pageNo: currentPage}, () => {
      this.getDetail();
    })
  }

  checkItemId = (rule, value, callback) =>{
    if(value && value.split(',').length > 100){
      callback(`输入超过最大长度100个`)
    }else if(value && (!(/^(\d+)(,\s*\d+)*$/).test(value))){
      callback('输入格式不合法')
    }else{
      callback();
    }
  }

  render() {
    // console.log(this.props.data);
    let {data} = this.props;
    const opt = (value, index, record) => {
      return <a href="javascript:;" onClick={()=>this.delSingle(record)}>删除</a>;
    };
    let {total} = this.state;
    return (
      <div>
        <Row>
          <Col>场景词:{data.name}</Col>
          {data.explainWords && <Col>同义词/上下位词:{data.explainWords}</Col>}
        </Row>
        <Form {...formLayout} field={this.field}>
          <Row>
            <Col span={8}>
              <FormItem label="商品ID:" {...formItemLayout} validator={this.checkItemId}>
                <Input placeholder="英文逗号隔开,最多100个" name="searchItemIdList" style={{marginRight:'10px'}}/>
              </FormItem>
            </Col>
            <Col span={10}>
              <FormItem label="商品名称:" {...formItemLayout}>
                <Input placeholder="请输入商品名称" name="searchItemName"/>
              </FormItem>
            </Col>
            <Col span={6}>
              &nbsp;&nbsp;<Button type="primary" onClick={this.search}>查询</Button>
              &nbsp;&nbsp;<Button type="normal" onClick={this.reload}>重置</Button>
            </Col>
          </Row>
        </Form>
        <Button type="primary" onClick={this.delItem} style={{marginBottom:'5px'}}>批量删除</Button>
        <Table dataSource={this.state.goodList} primaryKey='itemId' rowSelection={{
          onChange: this.onSelectChange,
          columnProps: () => {
            return {
              lock: 'left',
              width: 50,
              align: 'center'
            };
          },
        }}>
          <Table.Column title="商品ID" dataIndex="itemId" width='100px'/>
          <Table.Column title="商品名称" dataIndex="itemName" width='100px' />
          <Table.Column title="商品图片" dataIndex="itemPicUrl" width='74px' cell={ (imgsrc, index, record) => <PreviewImage src={imgsrc || DEFAULT_GOODS_IMG} title={record.itemName}/> } />
          <Table.Column title="门店名称" dataIndex="storeName" width='200px'/>
          <Table.Column title="操作"   width='60px' cell={opt}/>
        </Table>
        <Pagination onChange={this.onChange} current={this.state.pageNo} totalRender={total => `总共: ${total}个商品`}   type="simple" total={total} style={{marginTop:'10px'}}/>
      </div>
    )
  }
}
