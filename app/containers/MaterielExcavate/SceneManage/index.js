import React from 'react';

import {
  Form,
  Grid,
  Button,
  Input,
  Select,
  Tab,
  Field,
  Divider,
  Dialog
} from '@alife/next';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import {DialogBtn, LinkButton, GoldLogLink} from '@/components/Button';
import {ACLPermission} from "@/containers//PutInPage/request";
import {AccessBtn} from '@/components/Button/AccessBtn';
import {manageList, setTableCol, filterForm} from '@/components/Table';
import {formatTimeStamp} from '@/utils/time';
import {doesFormHasErrors} from '@/utils/formTool';
import {onRequestError} from '@/utils/api'
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {isAllNumber} from '@/utils/validators';
import {filterData} from '@/utils/filterDataByKey';
import {momentToTimeStamp} from '@/utils/time';
import {withoutCopy} from '@/utils/others';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import { reportScene,listScene,listViewableScene,deleteScene } from '../request';
import ListDetail from './listDetail';


import './index.scss'
import {LogTimePutInPage} from "@/containers/decoration/activity/edit";
import {getPutInDetail} from "@/containers/PutInPage/request";

const FormItem = Form.Item;
const {Row, Col} = Grid;

const timeFormat = 'YYYY-MM-DD HH:mm:ss';

export const onlineStatusMap = [
  {value: "", name: "全部"},
  {value: "已提报", name: "已提报", color: "#FF9500"},
  {value: "生产中", name: "生产中", color: "#00CCAA"},
  {value: "生产完成", name: "生产完成", color: "#CCCCCC"},
  {value: "生产失败", name: "生产失败", color: "#CCCCCC"},
]

/**操作列 */
const OptionCol = ({...params}) => {
  const {record, reload, tabidx,seekDetail} = params;
  const operateScene = () => {
    let cmd = {
      name:record.name,
    }
    deleteScene({...cmd}).then((data) => {
      console.log(data);
      reload()
    }).catch(onRequestError)
  }

  const deleteSceneWord = () => {
    Dialog.confirm({
      title: '删除场景词',
      onOk: () => operateScene(),
    });
  }
  return (
    <div className="option-col">
      <Button type="primary" disabled={record.state == 1 || record.state == 2} text
              onClick={() => seekDetail(record)}>查看</Button>
      {tabidx==1 && [<Divider direction="ver"/>,
      <Button type="primary" text onClick={()=>deleteSceneWord()}>删除</Button>]}
    </div>
  )
}

const defaultColType = {title: ''}

const columns = [
  {title: '场景词', dataIndex: 'name', width: '20%', cell: (value) => `${value}`},
  {title: '同义词/上下位词', dataIndex: 'explainWords', width: '20%'},
  {title: '状态', dataIndex: 'stateDesc', width: '20%'},
  {
    title: '操作', width: '22%', cell: function (value, rowIndex, record) {
      return <OptionCol record={record} reload={this.reload} {...this.props}/>
    }
  }
]

function getCol(type) {
  return {...defaultColType, ...type}
}

const formItemLayout = {
  labelCol: {span: 8},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

function PutInForm({ field, searchData, reload}) {
    const onSearch = () => {
      field.validate((e) => {
        if(!doesFormHasErrors(e)){
          searchData()
        }
      })
    }
    return (
      <Form {...formLayout} field={field}>
        <Row>
          <Col span={6}>
            <FormItem label="场景词:" {...formItemLayout}>
              <Input placeholder="请输入场景词" name="searchName" />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem label="状态:" {...formItemLayout}>
                <Select name="searchStateDesc" placeholder="请选择状态" style={{ width: '200px' }}>
                {
                    onlineStatusMap.map((v) => {
                    return <Select.Option key={v.value} value={v.value}>{v.name}</Select.Option>
                    })
                }
                </Select>
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem  {...formItemLayout}>
              <Button type="primary" onClick={onSearch}>查询</Button>&nbsp;&nbsp;
              <Button type="normal" onClick={reload}>重置</Button>&nbsp;&nbsp;
            </FormItem>
          </Col>
        </Row>
      </Form>
    )
}

const PutInFilterForm = filterForm(PutInForm, ['searchName', 'searchStateDesc'])
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(PutInTable, PutInFilterForm,
  async function (search) {
    let {query, page, size} = search;
    let {tabidx} = this.props;
    let sceneListQry = {
      pageNo: page,
      pageSize: size,
      ...search.query,
    }
    try {
      let request = (tabidx == 1) ? listScene : listViewableScene;
      const data = await request({...sceneListQry});
      return data;
    } catch(error) {
      onRequestError(error)
    }
  }
)

class Index extends PageBase {
  constructor(props) {
    super(props)
    this.state = {
      tabidx: 1, // 1我创建的、0可查看的、2：已申请权限的
      createFormVisible: false,
      detailFormVisible: false,
      createForm: {}
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        const {createForm} = this.state;
        createForm[name] = value;
        this.setState({
          createForm
        }, () => {
          console.log(this.state);
        })
      }
    })
  }

  async componentDidMount() {
    this.refs.manageList.fetchData({});
  }

  onTabChange = (tabidx) => {
    this.setState({
      tabidx,
    }, () => {
        this.refs.manageList.fetchData({page:1});
    })
  }

  createScene = () => {
    this.setState({createFormVisible: true})
  }

  submitScene = () =>{
    this.field.validate((e) => {
      if (!doesFormHasErrors(e)) {
        const {createForm} = this.state;
        let cmd = {
          name:createForm.name,
          explainWords:createForm.explainWords,
        }
        reportScene({...cmd}).then((data) => {
          console.log(data);
          this.setState({createFormVisible: false, createForm: {}})
          this.refs.manageList.fetchData({page: 1});
        }).catch(onRequestError)
      }
    });
  }

  seekDetail = (record) =>{
    this.setState({detailFormVisible: true, detailData: record})
  }

  render() {
    let {tabidx} = this.state;

    return (
      <PageBase.Container className="scene-manage-page">
        <PageWrapper title={<div><span>场景词提报</span><Button type="primary" style={{float:'right',marginRight:'25px'}} onClick={this.createScene}>提报场景词</Button></div>}>
          <ManageList
            ref="manageList"
            tabidx={tabidx}
            seekDetail={this.seekDetail}
          >
            <Tab shape="wrapped" onChange={this.onTabChange} activeKey={tabidx}>
              <Tab.Item title="我创建的" key={1}></Tab.Item>
              <Tab.Item title="可查看的" key={0}></Tab.Item>
            </Tab>
            <Dialog title="提报场景词"
                    style={{width:'606px'}}
                    visible={this.state.createFormVisible}
                    okProps={{children: '提交'}}
                    onOk={this.submitScene}
                    onCancel={()=>this.setState({createFormVisible: false})}
                    onClose={()=>this.setState({createFormVisible: false})}
              >
              {this.state.createFormVisible && <Form {...formLayout} field={this.field}>
                <Row>
                  <Col span={20}>
                    <FormItem required label="场景词:" {...formItemLayout} requiredMessage={"场景词必填"}>
                      <Input placeholder="请输入场景词" name="name"/>
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span={20}>
                    <FormItem label="同义词/上下位词:" {...formItemLayout}>
                      <Input placeholder="请输入同义词/上下位词" name="explainWords"/>
                    </FormItem>
                  </Col>
                </Row>
              </Form>}
            </Dialog>
            <Dialog title="查看场景商品明细"
                    style={{width: '786px'}}
                    isFullScreen={true}
                    visible={this.state.detailFormVisible}
                    okProps={{children: '关闭'}}
                    onOk={()=>this.setState({detailFormVisible: false})}
                    onCancel={()=>this.setState({detailFormVisible: false})}
                    onClose={()=>this.setState({detailFormVisible: false})}
            >
              <ListDetail data={this.state.detailData}/>
            </Dialog>
          </ManageList>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}


export const SceneManagePage = logTimeComponent(Index, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

// export const SceneManagePage = permissionAccess(SceneManage, async (params) => {
//   return await validateAclResource(params);
// })
