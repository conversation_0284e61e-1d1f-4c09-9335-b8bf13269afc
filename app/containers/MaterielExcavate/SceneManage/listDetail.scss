.scene-manage-page {
    padding: 20px 0;

    .nav-wrapper {
        margin-left: 20px;
    }
    .next-breadcrumb .next-breadcrumb-text > a{
      text-decoration: underline;
    }
    .set-title-wrap {

        .set-title {
            margin-left: -14px;
            margin-bottom: 20px;
            padding-right: 20px;
            text-align: right;
            font-size: 16px;
            color: #333333;
        }

    }

    .c-page-wrapper__content {
        padding-top: 0;

        .next-form-item {
            margin-bottom: 20px;

            .next-form-item-label {
                color: #333;
            }

            &.ope-btns {
                //margin-left: -58px;

                .next-btn {
                    padding:0 22px;
                }
            }
        }

        .manage-list {
            margin-top: -1px !important;

            .online-status-text {
                display: inline-flex;
                justify-content: center;
                align-items: center;

                i {
                    display: inline-block;
                    width: 6px;
                    height: 6px;
                    margin-right: 8px;
                    border-radius: 6px;
                }
            }

            .option-col {

                a, span {
                    margin:0;
                }

                .next-btn {
                    height: auto;
                }
            }
        }

    }

}
