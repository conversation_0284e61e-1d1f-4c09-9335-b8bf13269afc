import debugFn from "debug";
import React from "react";
import NotFoundPage from "@/containers/NotFoundPage/Loadable";
import EditPage from "@/containers/EditPage/Loadable";
import * as CMP from "@/containers/PoolPage";
import {
  PutInListPage,
  CreatePutInPage,
  PutInDetailPage,
} from "@/containers/PutInPage";
import {
  PutInActEffectPage,
  VenueEffectPage,
  ActivityRankListPage,
} from "@/containers/ActivityEffectPage";
import {
  ActListPage,
  ActDetailPage,
  ActEditPage,
} from "@/containers/decoration/activity";
import {
  MaterialListPage,
  MaterialDetailPage,
  MaterialEditPage,
} from "@/containers/decoration/material";
import {
  MarketResourcePage,
  MarketEditPage,
  MarketLogPage,
  ResourcePutInSetPage,
  ViewConfigurePage,
  MarketSetSchemaPage,
  DetailConfigPage,
  /* MarketResourceInsidePage,*/ ResourceAuthSetPage,
  DebuggingPage,
  ViewConfigPage,
} from "@/containers/channel/market";
import { MaterialLibraryListPage } from "@/containers/materialLibrary";
import { SceneManagePage } from "@/containers/MaterielExcavate";
import {
  ScenePolicyPage,
  ScenePolicyPutInPage,
} from "@/containers/PolicyPlatform";
import {
  LimitPage,
  AddPage,
  EditTimePage,
  SelectionFilterItemPage,
  PooClearSettingPage,
} from "@/containers/setting";
import { ResourceReportPage, ScheduleReportPage } from "@/containers/report";
import * as CP from "@/home/<USER>";
import { SetSchema } from "@/home/<USER>/setSchema";
import { RankManagePage } from "@/home/<USER>/rankManage/index";
import * as SuperMarket from "@/home/<USER>";
import * as Activity from "@/home/<USER>";
// import * as Rank from '@/home/<USER>';
import * as RankV2 from "@/home/<USER>";
import * as PageSetUp from "@/home/<USER>";
import * as HM from "@/home";
import * as QP from "@/home/<USER>";
import * as P2 from "@/home/<USER>";
import * as PermissionApply from "@/home/<USER>";
import * as ResourceInspector from "@/home/<USER>";
import { Route, Redirect } from "react-router-dom";

const debug = debugFn("selection:routes");

function routeNode({ path, component, title, children = [], ...rest }) {
  return {
    path,
    component,
    title,
    children,
    ...rest,
  };
}

// const commonPageGroup = [ "/channelManage/market/index", "/channelManage/market/category", "/channelManage/market/gold","/channelManage/market/newgold", "/channelManage/market/benefit","/channelManage/market/deliveryFreeCard", "/channelManage/market/welfareCentre","/channelManage/market/channelgold",
//   "/channelManage/green/index", , "/channelManage/green/benefit", "/channelManage/fruit/index", "/channelManage/fruit/benefit", "/channelManage/flower/index", "/channelManage/flower/benefit",
//   "/channelManage/medicinemall/index","/channelManage/medicine/index", "/channelManage/ele/index", "/channelManage/ele/applet", "/channelManage/ele/seasonalgoods", "/channelManage/market/special", "/channelManage/fruit/special",
//   "/channelManage/green/special", "/channelManage/flower/special", "/channelManage/ele/special", "/channelManage/medicine/special", "/channelManage/order/detail", "/channelManage/ordering/index", "/channelManage/deliveryFreeCard/index",
//   "/channelManage/aliPayCard/index","/channelManage/aliPayCard/ssu","/tempchannelManage/market/shopListCard","/channelManage/market/newbornZone","/channelManage/green/welfareCentre","/channelManage/fruit/welfareCentre","/channelManage/market/intraCityCircle","/channelManage/market/intraCityCircleInside",
//   "/channelManage/market/brandWall","/channelManage/ele/goodStuff","/channelManage/ele/index_new","/channelManage/ele/goodGoldPage","/channelManage/green/newbornZone","/channelManage/fruit/newbornZone","/channelManage/market/getWelfare","/channelManage/shop/detail","/channelManage/marketAged/index","/channelManage/marketAged/special","/channelManage/marketAged/newgold"
//   ,"/channelManage/ordersuccess/index","/channelManage/ele/universal_supermarket","/channelManage/ele/hot_sell","/channelManage/ele/sales_promotion","/channelManage/medicine/alipay_program", "/channelManage/market/newDeliveryFreeCard",
//   "/supermarketChannel/manage", "/channelManage/supermarketChannelNew/manage","/channelManage/supermarketChannelNew/diamondInnerSheet", "/channelManage/superdrug/index","/channelManage/medicine/alipay_aggregation_program","/channelManage/market/new", "/channelManage/market/newgold2","/channelManage/supermarketChannelNew/shop","/channelManage/supermarketChannelNew/map",
//   "/channelManage/supermarketChannelNew/nightMarket", "/channelManage/XCategoryChannel/index","/channelManage/XDiamondLiner/index","/channelManage/supermarketChannelNew/tradeBistro","/channelManage/supermarketChannelNew/tradeBistroTwo","/channelManage/supermarketChannelNew/SignAndGetMoney",
//   "/channelManage/school/index","/channelManage/onecent/index"
// ];
//
// const channelManageChildrenMenu = [];
//
// commonPageGroup.map((v)=>{
//   channelManageChildrenMenu.push({
//     path: `${v}/edit/:configId?`,
//     component: MarketEditPage
//   },{
//     path: `${v}/log/:configId?`,
//     component: MarketLogPage
//   },{
//     path: `${v}/resourcePutInSet`,
//     component: ResourcePutInSetPage,
//   },{
//     path: `${v}/resourceAuthSet`,
//     component: ResourceAuthSetPage,
//   },{
//     path: `${v}/viewConfigure`,
//     component: ViewConfigurePage,
//   },{
//     path: `${v}/viewConfig/:configId?`,
//     component: ViewConfigPage,
//   },{
//     path:`${v}/detailConfig/:configId?`,
//     component: DetailConfigPage,
//   });
// });

function generateChannelManageSubPages(prefix) {
  return [
    {
      path: `${prefix}/edit/:configId?`,
      component: MarketEditPage,
    },
    {
      path: `${prefix}/log/:configId?`,
      component: MarketLogPage,
    },
    {
      path: `${prefix}/resourcePutInSet`,
      component: ResourcePutInSetPage,
    },
    {
      path: `${prefix}/resourceAuthSet`,
      component: ResourceAuthSetPage,
    },
    {
      path: `${prefix}/viewConfigure`,
      component: ViewConfigurePage,
    },
    {
      path: `${prefix}/viewConfig/:configId?`,
      component: ViewConfigPage,
    },
    {
      path: `${prefix}/detailConfig/:configId?`,
      component: DetailConfigPage,
    },
    {
      path: `${prefix}/setSchema`,
      component: SetSchema,
    },
  ];
}

const ctrlCommonPageGroup = (permissions) => {
  let _commonPageGroup = [];
  let channelManageGroup = permissions.filter(
    (v) =>
      v.menuName === "channel_manage" || v.menuName === "supermarket_channel",
  );
  let list =
    channelManageGroup &&
    channelManageGroup.length > 0 &&
    channelManageGroup[0].subMenus
      ? channelManageGroup.map((o) => o.subMenus)
      : [];
  let _list = [].concat(...list);
  // _subMenu 指的类似'天天特价内页'的三级节点
  const _subMenu = _list.map((v) => v.subMenus);
  // parentSubMenu 指的类似'订单详情'的二级节点
  const parentSubMenu = _list.map(
    ({ menuName, menuDescription, menuTitle, menuUrl }) => ({
      menuName,
      menuDescription,
      menuTitle,
      menuUrl,
    }),
  );
  // const newSubMenu = [].concat(..._subMenu).map(({menuName,menuDescription, menuTitle, menuUrl}) => ({menuName,menuDescription, menuTitle, menuUrl:`${menuUrl}/${menuDescription}`}));
  // console.log('list22',list,[].concat(...list),_subMenu,parentSubMenu);
  const _flatChildrenNode = parentSubMenu.concat(..._subMenu);
  _commonPageGroup = _flatChildrenNode.map((o) => o.menuUrl);
  const _channelManageChildrenMenu = [];
  _commonPageGroup.map((v) => {
    generateChannelManageSubPages(v).forEach((p) => {
      _channelManageChildrenMenu.push(p);
    });
  });
  console.log("_channelManageChildrenMenu", _channelManageChildrenMenu);
  return {
    _channelManageChildrenMenu,
    _flatChildrenNode,
  };
};

//自定义menu信息
let menuMap = {
  "/pool": {
    children: [
      {
        path: "/pool/list/create/:poolType?",
        component: CP.poolCreatePage,
      },
      {
        path: "/pool/list/tagPool/:poolId?",
        component: CP.tagPoolPage,
      },
      {
        path: "/pool/list/selectedStore/:poolId?",
        component: CP.selectedStorePage,
      },
      {
        path: "/pool/list/fileUpload/:poolId?",
        component: CP.fileUploadPage,
      },
      {
        path: "/pool/list/intelligentPool/:poolId?",
        component: CP.IntelligentPoolPage,
      },
      {
        path: "/pool/list/labelPool/:poolId?",
        component: CP.LabelPoolPage,
      },
      {
        path: "/pool/list/detail/:poolId?",
        component: CP.PoolDetailPage,
      },
      {
        path: "/pool/list/fileUploadPage/:poolId?",
        component: CP.fileUploadPage,
      },
      {
        path: "/pool/list/analyse/:poolId?",
        component: CP.analysePage,
      },
      {
        path: "/pool/list/grid/:poolId?",
        component: CP.gridPage,
      },
      {
        path: "/pool/expertPool/create/:scenePoolType/:sceneId?",
        component: CP.expertPoolCreatePage,
      },
      {
        path: "/pool/expertPoolDetail/:expertPoolId/:poolEntryType?",
        component: CP.expertPoolDetail,
      },
    ],
    icon: "leftnav_xuanpin",
  },
  "/commoditypool": {
    children: [
      // {
      //   path: '/commoditypool/list',
      //   component: CP.poolListPage,
      // },
      // {
      //   path: '/commoditypool/creation',
      //   component: CP.poolListPage,
      // },
      // {
      //   path: '/commoditypool/dataSetUpload',
      //   component: CP.poolListPage,
      // },
      {
        path: "/commoditypool/creation/query/:poolId?",
        component: CMP.SelectQueryPoolPage,
      },
      {
        path: "/commoditypool/dataSetUpload/:poolId?",
        component: CMP.DataSetUploadPage,
      },
      {
        path: "/commoditypool/list/progress/:id",
        component: CMP.PoolCreationProgressPage,
      },
      {
        path: "/commoditypool/list/exportProgress",
        component: CMP.PoolExportProgress,
      },
      {
        path: "/commoditypool/list/detail/:id?",
        component: CMP.PoolDetailPage,
      },
    ],
    icon: "leftnav_xuanpin",
  },
  "/storepool": {
    children: [
      // {
      //   path: '/storepool/list',
      //   component: CP.poolListPage,
      // },
      // {
      //   path: '/storepool/creation',
      //   component: CP.poolListPage,
      // },
      // {
      //   path: '/storepool/dataSetUpload',
      //   component: CP.poolListPage,
      // },
      {
        path: "/storepool/creation/query/:poolId?",
        component: CMP.SelectQueryPoolPage,
      },
      {
        path: "/storepool/dataSetUpload/:poolId?",
        component: CMP.DataSetUploadPage,
      },
      {
        path: "/storepool/list/progress/:id",
        component: CMP.PoolCreationProgressPage,
      },
      {
        path: "/storepool/list/exportProgress",
        component: CMP.PoolExportProgress,
      },
      {
        path: "/storepool/list/detail/:id?",
        component: CMP.PoolDetailPage,
      },
    ],
    icon: "leftnav_xuandian",
  },
  "/putIn": {
    children: [
      {
        path: "/putIn/list/create/:activityId",
        component: CreatePutInPage,
      },
      {
        path: "/putIn/list/create",
        component: CreatePutInPage,
      },
      {
        path: "/putIn/list/new/:activityId",
        component: Activity.ActivityCreatePage,
      },
      {
        path: "/putIn/list/new",
        component: Activity.ActivityCreatePage,
      },
      {
        path: "/putIn/list/view/:activityId",
        component: Activity.ActivityViewPage,
      },
      {
        path: "/putIn/list/detail/:activityId",
        component: PutInDetailPage,
      },
    ],
    icon: "leftnav_toufang",
  },
  "/rankManage": {
    children: [
      {
        path: "/rankManage/list/create/:id",
        component: RankV2.rankCreatePage,
      },
      {
        path: "/rankManage/list/create",
        component: RankV2.rankCreatePage,
      },
      {
        path: "/rankManage/list/view/:id",
        component: RankV2.rankViewPage,
      },
      {
        path: "/rankManage/list/schema",
        component: SetSchema,
      },
    ],
    icon: "bangdanguanli",
  },
  // '/activityEffect':{
  //   children:[
  //     {
  //       path: '/putInActivityEffect/list/:activityId',
  //       component: PutInActEffectPage
  //     },
  //     {
  //       path: '/putInActivityEffect/list',
  //       component: PutInActEffectPage
  //     },
  //     {
  //       path: '/venueEffect/list',
  //       component: VenueEffectPage
  //     },
  //     {
  //       path: '/activityRankList',
  //       component: ActivityRankListPage
  //     }
  //   ],
  //   icon: 'leftnav_shuju'
  // },
  "/decorationActivity": {
    children: [
      {
        path: "/decorationActivity/list/detail/:id",
        component: ActDetailPage,
      },
      {
        path: "/decorationActivity/list/edit/:id",
        component: ActEditPage,
      },
      {
        path: "/materialCenter/list/detail/:id",
        component: MaterialDetailPage,
      },
      {
        path: "/materialCenter/list/edit/:id",
        component: MaterialEditPage,
      },
    ],
    icon: "leftnav-zhuangxiu",
  },
  "/channelManage": {
    children: [
      // ...channelManageChildrenMenu,
      // {
      //   path: '/channelManage/market/setSchema',
      //   component: MarketSetSchemaPage,
      // },
      // {
      //   path: '/channelManage/market/debuggingPage',
      //   component: DebuggingPage,
      // }
    ],
    icon: "pindaoguanli",
  },
  "/report": {
    children: [],
    icon: "leftnav_huodongxiaoguo",
  },
  "/materielExcavate": {
    children: [],
    icon: "menu-solid",
  },
  "/policyPlatform": {
    children: [],
    icon: "leftnav_yingxiao",
  },
  "/materialLibrary": {
    children: [],
    icon: "sucaiku",
  },
  "/tempchannelManage": {
    //临时的频道
    children: [],
    icon: "pindaoguanli",
  },
  // '/channelManage/market/debuggingPage': { //投前预览
  //   children: [],
  //   icon: 'pindaoguanli'
  // },
  "/setting": {
    //内部使用的设置页
    children: [
      {
        path: "/setting/editTimePage/:poolId",
        component: EditTimePage,
      },
    ],
    icon: "set",
  },
  "/supermarketChannel": {
    //全能超市2.0
    children: [],
    icon: "pindaoguanli",
  },
  "/supermarketChannelNew": {
    //全能超市3.0
    children: [],
    icon: "shoppingcart",
  },
  "/pageSetUp": {
    //搭建
    children: [],
    icon: "leftnav_dajian",
  },
  "/qualityProduct": {
    // 优品流量投资
    children: [],
    icon: "thumb",
  },
};

// 权限列表渲染map
let moziSubMenuMap = {
  //列表入口
  create_pool_list: {
    path: "/pool/list",
    component: CP.poolListPage,
    // routeProps: {
    //   exact: true
    // }
  },
  expert_pool_management: {
    path: "/expertPool/list",
    component: CP.expertPoolListPage,
  },
  create_sku_pool_menu: {
    path: "/commoditypool/creation",
    component: CMP.SelectBasePoolPage,
    routeProps: {
      exact: true,
    },
  },
  // 列表入口
  manage_sku_pool_menu: {
    path: "/commoditypool/list",
    component: CMP.SkuListPage,
  },
  commodity_data_set_upload: {
    path: "/commoditypool/dataSetUpload",
    component: CMP.DataSetUploadPage,
  },
  // 门店创建入口
  create_store_pool_menu: {
    path: "/storepool/creation",
    component: CMP.SelectBasePoolPage,
    routeProps: {
      exact: true,
    },
  },
  // 门店列表接口
  manage_store_pool_menu: {
    path: "/storepool/list",
    component: CMP.StoreListPage,
  },
  //店-数据集上传
  store_data_set_upload: {
    path: "/storepool/dataSetUpload",
    component: CMP.DataSetUploadPage,
  },
  // 投放列表
  manage_put_in_menu: {
    path: "/putIn/list",
    component: PutInListPage,
  },
  //投放活动效果
  put_in_activity_effect_menu: {
    path: "/putInActivityEffect/list",
    component: PutInActEffectPage,
  },
  venue_effect_menu: {
    path: "/venueEffect/list",
    component: VenueEffectPage,
  },
  activity_rank_list_menu: {
    path: "/activityRankList",
    component: ActivityRankListPage,
  },
  //大促氛围
  decoration_activity_list: {
    path: "/decorationActivity/list",
    component: ActListPage,
  },
  material_center: {
    path: "/materialCenter/list",
    component: MaterialListPage,
  },
  rank_manage_list: {
    path: "/rankManage/list",
    component: RankManagePage,
    // routeProps: {
    //   exact: true
    // }
  },
  // 'market_channel_aged':{
  //   path: '/channelManage/marketAged',
  // },
  // 'market_channel_aged_index':{
  //   path: '/channelManage/marketAged/index',
  //   component: MarketResourcePage,
  // },
  // 'special_inside_page_aged':{
  //   path: '/channelManage/marketAged/special',
  //   component: MarketResourcePage,
  // },
  // 'new_gold_inner_aged':{
  //   path: '/channelManage/marketAged/newgold',
  //   component: MarketResourcePage,
  // },
  // 'market_channel':{
  //   path: '/channelManage/market',
  // },
  // 'market_channel_index':{
  //   path: '/channelManage/market/index',
  //   component: MarketResourcePage,
  // },
  // 'x_category_channel':{
  //   path: '/channelManage/XCategoryChannel',
  // },
  // 'x_category_channel_index':{
  //   path: '/channelManage/XCategoryChannel/index',
  //   component: MarketResourcePage,
  // },
  // 'x_diamond_liner_index':{
  //   path: '/channelManage/XDiamondLiner/index',
  //   component: MarketResourcePage,
  // },
  // 逛品牌
  // 'market_get_welfare':{
  //   path: '/channelManage/market/getWelfare',
  //   component: MarketResourcePage,
  // },
  // 'category_inside_page':{
  //   path: '/channelManage/market/category',
  //   component: MarketResourcePage, //MarketResourceInsidePage
  // },
  // 'special_inside_page':{
  //   path: '/channelManage/market/special',
  //   component: MarketResourcePage, //MarketResourceInsidePage
  // },
  // 'gold_inside_page':{
  //   path: '/channelManage/market/gold',
  //   component: MarketResourcePage,
  // },
  // 'new_gold_inner':{
  //   path: '/channelManage/market/newgold',
  //   component: MarketResourcePage,
  // },
  // 'channel_gold_inner':{
  //   path: '/channelManage/market/channelgold',
  //   component: MarketResourcePage,
  // },
  // 'shop_list_card_inner':{
  //   path: '/tempchannelManage/market/shopListCard',
  //   component: MarketResourcePage,
  // },
  // 'benefit_inside_page':{
  //   path: '/channelManage/market/benefit',
  //   component: MarketResourcePage,
  // },
  // 'delivery_free_card_inner':{ // 免配卡内页
  //   path: '/channelManage/market/deliveryFreeCard',
  //   component: MarketResourcePage,
  // },
  // 'new_delivery_free_card_inner':{ // 新免配卡内页
  //   path: '/channelManage/market/newDeliveryFreeCard',
  //   component: MarketResourcePage,
  // },
  // 'welfare_centre':{
  //   path: '/channelManage/market/welfareCentre',
  //   component: MarketResourcePage,
  // },
  // 'newborn_zone_inner':{ // 新人专区内页
  //   path: '/channelManage/market/newbornZone',
  //   component: MarketResourcePage,
  // },
  // 'intra_city_circle':{ // 同城圈频道页
  //   path: '/channelManage/market/intraCityCircle',
  //   component: MarketResourcePage,
  // },
  // 'intra_city_circle_inside':{ // 同城圈金刚球内页
  //   path: '/channelManage/market/intraCityCircleInside',
  //   component: MarketResourcePage,
  // },
  // 'brand_wall_inner':{ // 品牌内页
  //   path: '/channelManage/market/brandWall',
  //   component: MarketResourcePage,
  // },
  // 'green_channel':{
  //   path: '/channelManage/green',
  // },
  // 'green_channel_index':{
  //   path: '/channelManage/green/index',
  //   component: MarketResourcePage,
  // },
  // 'green_inside_page':{
  //   path: '/channelManage/green/special',
  //   component: MarketResourcePage, //MarketResourceInsidePage
  // },
  // 'green_welfare_centre':{
  //   path: '/channelManage/green/welfareCentre',
  //   component: MarketResourcePage,
  // },
  // 'green_newborn_zone_inner':{
  //   path: '/channelManage/green/newbornZone',
  //   component: MarketResourcePage,
  // },
  // 'benefit_inside_green_page':{
  //   path: '/channelManage/green/benefit',
  //   component: MarketResourcePage,
  // },
  // 'fruit_channel':{
  //   path: '/channelManage/fruit',
  // },
  // 'fruit_channel_index':{
  //   path: '/channelManage/fruit/index',
  //   component: MarketResourcePage,
  // },
  // 'fruit_inside_page':{
  //   path: '/channelManage/fruit/special',
  //   component: MarketResourcePage, //MarketResourceInsidePage
  // },
  // 'fruit_welfare_centre':{
  //   path: '/channelManage/fruit/welfareCentre',
  //   component: MarketResourcePage,
  // },
  // 'fruit_newborn_zone_inner':{
  //   path: '/channelManage/fruit/newbornZone',
  //   component: MarketResourcePage,
  // },
  // 'benefit_inside_fruit_page':{
  //   path: '/channelManage/fruit/benefit',
  //   component: MarketResourcePage,
  // },
  // 'flower_channel':{
  //   path: '/channelManage/flower',
  // },
  // 'flower_channel_index':{
  //   path: '/channelManage/flower/index',
  //   component: MarketResourcePage,
  // },
  // 'flower_inside_page':{
  //   path: '/channelManage/flower/special',
  //   component: MarketResourcePage, //MarketResourceInsidePage
  // },
  // 'benefit_inside_flower_page':{
  //   path: '/channelManage/flower/benefit',
  //   component: MarketResourcePage,
  // },
  // 'medicine_channel':{
  //   path: '/channelManage/medicine',
  // },
  // 'medicine_channel_index':{
  //   path: '/channelManage/medicine/index',
  //   component: MarketResourcePage,
  // },
  // 'medicine_channel_alipay_program':{
  //   path: '/channelManage/medicine/alipay_program',
  //   component: MarketResourcePage,
  // },
  // 'alipay_aggregation_program':{
  //   path: '/channelManage/medicine/alipay_aggregation_program',
  //   component: MarketResourcePage,
  // },
  // 'medicine_mall':{
  //   path: '/channelManage/medicinemall/index',
  //   component: MarketResourcePage,
  // },
  // 'superdrug':{
  //   path: '/channelManage/superdrug/index',
  //   component: MarketResourcePage,
  // },
  // 'ele_channel':{
  //   path: '/channelManage/ele',
  // },
  // 'ele_channel_index':{
  //   path: '/channelManage/ele/index',
  //   component: MarketResourcePage,
  // },
  // 'ele_channel_index_new': {
  //   path: '/channelManage/ele/index_new',
  //   component: MarketResourcePage,
  // },
  // 'universal_supermarket': {
  //   path: '/channelManage/ele/universal_supermarket',
  //   component: MarketResourcePage,
  // },
  // 'special_offer_applet': {
  //   path: '/channelManage/ele/applet',
  //   component: MarketResourcePage,
  // },
  // 'special_offer_hot_sell': {
  //   path: '/channelManage/ele/hot_sell',
  //   component: MarketResourcePage,
  // },
  // 'special_offer_sales_promotion': {
  //   path: '/channelManage/ele/sales_promotion',
  //   component: MarketResourcePage,
  // },
  // 'order_success_menu': {//下单成功页
  //   path: '/channelManage/ordersuccess/index',
  //   component: MarketResourcePage,
  // },
  // 'ele_inside_page':{
  //   path: '/channelManage/ele/special',
  //   component: MarketResourcePage,
  // },
  // 'seasonalgoods_channel':{
  //   path: '/channelManage/ele/seasonalgoods',
  //   component: MarketResourcePage,
  // },
  // 'good_stuff_inner':{ // 好货正当时
  //   path: '/channelManage/ele/goodStuff',
  //   component: MarketResourcePage,
  // },
  // 'good_gold_inner':{ // 品类金刚内页页面
  //   path: '/channelManage/ele/goodGoldPage',
  //   component: MarketResourcePage,
  // },
  material_library: {
    path: "/materialLibrary",
  },
  scene_manage: {
    path: "/materielExcavate/sceneManage",
    component: SceneManagePage,
  },
  scene_policy: {
    path: "/policyPlatform/scenePolicy",
    component: ScenePolicyPage,
  },
  scene_policy_putIn: {
    path: "/policyPlatform/scenePolicyPutIn",
    component: ScenePolicyPutInPage,
  },
  seasonal_library: {
    path: "/materialLibrary/seasonal",
    component: MaterialLibraryListPage,
  },
  setting_pool: {
    path: "/setting/limitPage",
    component: LimitPage,
  },
  add_pool: {
    path: "/setting/addPage",
    component: AddPage,
  },
  setting_update_ids: {
    path: "/setting/editPage",
    component: EditPage,
  },
  selection_filter_item: {
    path: "/setting/selectionFilterItem",
    component: SelectionFilterItemPage,
  },
  selection_pool_clear: {
    path: "/setting/poolClearing",
    component: PooClearSettingPage,
  },
  channel_supermarketnew_gallery_scene: {
    path: "/channelManage/gallery/sceneManage",
    component: HM.SceneManageListPage,
    routeProps: {
      exact: true,
    },
  },
  supermarket_channel_big_store: {
    path: "/channelManage/supermarketChannelBigStore/sceneManage",
    component: HM.BigStoreManegePage,
  },
  // 'one-cent-purchase': { // 一分购外投
  //   path: '/channelManage/onecent/index',
  //   component: MarketResourcePage,
  // },
  // 'school_market_page': { // 校园超市页
  //   path: '/channelManage/school/index',
  //   component: MarketResourcePage,
  // },
  // 'order_detail_page': { // 订单详情页
  //   path: '/channelManage/order/detail',
  //   component: MarketResourcePage,
  // },
  // 'shop_detail_page': { // 店内大促专区
  //   path: '/channelManage/shop/detail',
  //   component: MarketResourcePage,
  // },
  // 'order_page': { // 提单页
  //   path: '/channelManage/ordering/index',
  //   component: MarketResourcePage,
  // },
  // 'medicine_inside_page':{
  //   path: '/channelManage/medicine/special',
  //   component: MarketResourcePage,
  // },
  // 'alipay_channel':{
  //   path: '/channelManage/aliPayCard',
  // },
  // 'alipay_card':{
  //   path: '/channelManage/aliPayCard/index',
  //   component: MarketResourcePage,
  // },
  debugPage: {
    path: "/channelManage/market/debuggingPage",
    component: DebuggingPage,
  },
  // 'alipay_ssu':{
  //   path: '/channelManage/aliPayCard/ssu',
  //   component: MarketResourcePage,
  // },
  resource_report: {
    path: "/report/resource",
    component: ResourceReportPage,
  },
  schedule_report: {
    path: "/report/schedule",
    component: ScheduleReportPage,
  },
  supermarket_channel_audit: {
    path: "/supermarketChannel/audit",
    component: SuperMarket.SuperMarketChannelAudit,
  },
  // 'supermarket_manage':{
  //   path: '/supermarketChannel/manage',
  //   component: MarketResourcePage,
  // },
  supermarket_shop_manage: {
    path: "/supermarketChannel/shopManage",
    component: SuperMarket.SuperMarketShopManage,
  },
  // 'market_new_channel_index':{
  //   path: '/channelManage/market/new',
  //   component: MarketResourcePage,
  // },
  // 'channel_gold_new_inner':{
  //   path: '/channelManage/market/newgold2',
  //   component: MarketResourcePage,
  // },
  // 'supermarket_channel_three':{
  //   path: '/channelManage/supermarketChannelNew',
  // },
  // 'channel_supermarketnew_manage':{
  //   path: '/channelManage/supermarketChannelNew/manage',
  //   component: MarketResourcePage,
  // },
  // 'channel_supermarketnew_bistro':{
  //   path: '/channelManage/supermarketChannelNew/tradeBistro',
  //   component: MarketResourcePage,
  // },
  // 'sign_and_get_money':{
  //   path: '/channelManage/supermarketChannelNew/SignAndGetMoney',
  //   component: MarketResourcePage,
  // },
  // 'channel_supermarketnew_bistro_two':{
  //   path: '/channelManage/supermarketChannelNew/tradeBistroTwo',
  //   component: MarketResourcePage,
  // },
  // 'channel_supermarketnew_shop':{
  //   path: '/channelManage/supermarketChannelNew/shop',
  //   component: MarketResourcePage,
  // },
  // 'channel_supermarketnew_map':{
  //   path: '/channelManage/supermarketChannelNew/map',
  //   component: MarketResourcePage,
  // },
  // 'channel_supermarketnew_insidepage':{
  //   path: '/channelManage/supermarketChannelNew/diamondInnerSheet',
  //   component: MarketResourcePage,
  // },
  // 'channel_supermarketnew_nightpage':{
  //   path: '/channelManage/supermarketChannelNew/nightMarket',
  //   component: MarketResourcePage,
  // },
  page_set_up_marketing_venue: {
    path: "/pageSetUp/marketingVenue",
    component: PageSetUp.PageSetUp,
  },
  goodsManage: {
    path: "/qualityProduct/goodsManage",
    component: QP.QualityGoodsManage,
  },
  goodsDelivery: {
    path: "/qualityProduct/goodsDelivery",
    component: QP.QualityGoodsDelivery,
  },
};

//base routes
const baseRoutes = [
  {
    path: "/",
    render() {
      return <Redirect to="/pool/list" />;
    },
    routeProps: {
      exact: true,
    },
  },
  {
    path: "/permissionApply/deny",
    render() {
      return <PermissionApply.DenyPage />;
    },
    routeProps: {
      exact: true,
    },
  },
  {
    path: "/permissionApply",
    render() {
      return <PermissionApply.Page />;
    },
    routeProps: {
      exact: true,
    },
  },
 
  {
    path: "/tools/SchemaReleaseRecord",
    component: HM.SchemaReleaseRecordPage,
  },
  {
    path: "/commoditypool",
    children: [
      {
        path: "/commoditypool/creation/query/:poolId?",
        component: CMP.SelectQueryPoolPage,
      },
      {
        path: "/commoditypool/dataSetUpload/:poolId?",
        component: CMP.DataSetUploadPage,
      },
      {
        path: "/commoditypool/list/progress/:id",
        component: CMP.PoolCreationProgressPage,
      },
      {
        path: "/commoditypool/list/exportProgress",
        component: CMP.PoolExportProgress,
      },
      {
        path: "/commoditypool/list/detail/:id?",
        component: CMP.PoolDetailPage,
      },
    ],
  },
  {
    path: "/storepool",
    children: [
      {
        path: "/storepool/creation/query/:poolId?",
        component: CMP.SelectQueryPoolPage,
      },
      {
        path: "/storepool/dataSetUpload/:poolId?",
        component: CMP.DataSetUploadPage,
      },
      {
        path: "/storepool/list/progress/:id",
        component: CMP.PoolCreationProgressPage,
      },
      {
        path: "/storepool/list/exportProgress",
        component: CMP.PoolExportProgress,
      },
      {
        path: "/storepool/list/detail/:id?",
        component: CMP.PoolDetailPage,
      },
    ],
  },
  {
    path: "/storepoolInvite",
    children: [
      {
        path: "/storepoolInvite/creation/query/:poolId?",
        component: CMP.SelectQueryPoolPage,
      },
      {
        path: "/storepoolInvite/fileUpload/:poolId?",
        component: CMP.FileUploadPage,
      },
      {
        path: "/storepoolInvite/list/progress/:id",
        component: CMP.PoolCreationProgressPageInvite,
      },
      {
        path: "/storepoolInvite/list/exportProgress",
        component: CMP.PoolExportProgressInvite,
      },
      {
        path: "/storepoolInvite/list/detail/:id?",
        component: CMP.PoolDetailPageInvite,
      },
      {
        routeProps: {
          exact: true,
        },
        path: "/storepoolInvite/list",
        component: CMP.StoreListPageInvite,
      },
      {
        routeProps: {
          exact: true,
        },
        path: "/storepoolInvite/creation",
        component: CMP.SelectBasePoolPageInvite,
      },
    ],
  },
];

if (window.configEnv === "ppe") {
  baseRoutes.push({
    path: "/resource-inspector",
    render() {
      return <ResourceInspector.Page />;
    },
    routeProps: { exact: true },
  });
}

function fillChildrenMenu(children) {
  return children.map((route) => {
    const { menuTitle, menuName, subMenus, menuId, hasPermission } = route;
    if (!moziSubMenuMap[menuName]) return {};
    const { component, path, routeProps } = moziSubMenuMap[menuName];

    return {
      hasPermission,
      originMenuId: menuId,
      routeProps,
      title: menuTitle,
      path,
      component,
      children: subMenus && fillChildrenMenu(subMenus),
    };
  });
}

/**根据permission填充routes */
function fillRouterByPermission(permissions, extraRoutes) {
  // console.log(permissions);
  if (!permissions.length) return baseRoutes;

  // 更新频道管理的菜单
  const { _channelManageChildrenMenu, _flatChildrenNode } =
    ctrlCommonPageGroup(permissions);
  menuMap["/channelManage"].children = [
    {
      path: "/channelManage/market/setSchema",
      component: MarketSetSchemaPage,
    },
    {
      path: "/channelManage/market/debuggingPage",
      component: DebuggingPage,
    },
    {
      path: "/channelManage/gallery/sceneManage",
      component: HM.SceneManageListPage,
      routeProps: {
        exact: true,
      },
    },
    {
      path: "/channelManage/gallery/sceneManage/create/:id",
      component: HM.SceneCreatePage,
    },
    {
      path: "/channelManage/gallery/sceneManage/create",
      component: HM.SceneCreatePage,
    },
    {
      path: "/channelManage/gallery/sceneManage/view/:id",
      component: HM.SceneViewPage,
    },
    {
      path: "/channelManage/gallery/sceneManage/schema",
      component: SetSchema,
    },
    {
      path: "/channelManage/supermarketChannelBigStore/sceneManage",
      component: HM.BigStoreManegePage,
      routeProps: {
        exact: true,
      },
    },
    {
      path: "/channelManage/supermarketChannelBigStore/sceneManage/create/:id",
      component: HM.BigStoreSceneCreatePage,
    },
    {
      path: "/channelManage/supermarketChannelBigStore/sceneManage/create",
      component: HM.BigStoreSceneCreatePage,
    },
    {
      path: "/channelManage/supermarketChannelBigStore/sceneManage/view/:id",
      component: HM.BigStoreSceneViewPage,
    },
    ..._channelManageChildrenMenu,
  ];
  _flatChildrenNode.map((o) => {
    // console.log(!isNaN(parseFloat(o.menuDescription)), o.menuUrl );
    //只有menuDescription是-1，就不走MarketResourcePage链路
    let pageId =
      window.configEnv === "prod"
        ? parseInt(o.menuDescription) - 100000000
        : o.menuDescription;
    let isChannelNumber = !isNaN(parseFloat(o.menuDescription));
    let menuUrl = isChannelNumber ? `${o.menuUrl}/${pageId}` : o.menuUrl;
    if (o.menuDescription !== "-1") {
      moziSubMenuMap[o.menuName] = {
        path: menuUrl,
        component: MarketResourcePage,
      };
    }
  });
  console.log(
    "_flatChildrenNode",
    _flatChildrenNode,
    moziSubMenuMap,
    menuMap,
    menuMap["/channelManage"].children,
  );
  let resultRoutes = baseRoutes.concat(
    permissions.map((per) => {
      const { menuTitle, menuUrl, subMenus, menuId, hasPermission } = per;
      if (!menuMap[menuUrl]) {
        console.log(`${per.menuTitle}${menuUrl}`); //配了ACL权限，route没配，去掉权限即可
        debug(`${per.menuTitle}${menuUrl} no config in local menuMap`);
      }
      const ret = {
        originMenuId: menuId,
        title: menuTitle,
        path: menuUrl,
        icon: menuMap[menuUrl] ? menuMap[menuUrl]["icon"] : "", //判断下，以免又icon不存在报错
        hasPermission,
        children: menuMap[menuUrl]
          ? menuMap[menuUrl]["children"].concat(fillChildrenMenu(subMenus))
          : "",
      };

      try {
        if (
          window.configEnv === "ppe" &&
          menuUrl === "/channelManage" &&
          Array.isArray(ret.children) &&
          ret.children.length
        ) {
          generateChannelManageSubPages(
            "/channelManage/__universal_page__",
          ).forEach((p) => {
            ret.children.push(p);
          });
          ret.children.push({
            path: "/channelManage/__universal_page__/:pageId",
            component: MarketResourcePage,
          });
        }
      } catch (e) {
        console.error(e);
      }

      return ret;
    }),
  );
  if (extraRoutes?.length) {
    resultRoutes = resultRoutes.concat(extraRoutes);
  }
  resultRoutes = resultRoutes.concat({
    path: "",
    render() {
      return <NotFoundPage />;
    },
  });
  return resultRoutes;
}

/**每一个节点都用routeNode包裹 */
function getRouteNode(routes) {
  if (!routes) return;
  return routes.map((item) => {
    const children = getRouteNode(item.children);

    return routeNode({ ...item, children });
  });
}

export function createReactRoutes(permission) {
  const extraRoutes = [
    localStorage.getItem("__selection_pooling_v2_inject__") === "true"
      ? {
          // 测试版新选品选商子应用
          path: "/selection/poolPage/poolList",
          component: P2.PoolPage,
        }
      : null,
  ].filter(Boolean);
  const routes = getRouteNode(fillRouterByPermission(permission, extraRoutes));
  let idGen = newIdGen();
  const ret = createRoutes(routes, idGen);
  debug("final page routes", ret);
  return ret;
}

function createRoutes(routes, idGen) {
  return routes.reduce((p, c) => [...p, ...createOneBranch(c, idGen)], []);
}

function createOneBranch(route, idGen) {
  if (route) {
    return [createRoute(route, idGen)]
      .filter(Boolean)
      .concat(createRoutes(route.children, idGen));
  } else return [];
}

function createRoute(route, idGen) {
  const { path, component, routeProps, render } = route;
  if (path != null && (component || render)) {
    return React.createElement(Route, {
      component,
      render,
      path,
      ...routeProps,
      key: idGen(),
    });
  } else {
    return null;
  }
}

export function createMenuData(permission) {
  const routes = getRouteNode(fillRouterByPermission(permission));
  let idGen = newIdGen(1);
  const ret = createMenus(routes, 0, idGen);
  debug("final menu data", ret);
  return ret;
}

function createMenus(routes, pid, idGen) {
  return routes
    .map((e) => {
      return createMenuItem(e, pid, idGen);
    })
    .filter(Boolean);
}

function createMenuItem(route, pid, idGen) {
  const { path, title, children, icon, menuUrl, originMenuId, hasPermission } =
    route;
  let leaf = !children.length;

  if (!title) return null;

  let node = {
    hasPermission,
    originMenuId,
    menuId: idGen(),
    menuTitle: title,
    menuParentId: pid,
    leaf,
    menuTitleEN: icon,
  };

  node.menuUrl = menuUrl || path;
  if (!leaf) {
    node.subMenus = createMenus(children, node.menuParentId, idGen);
  }
  return node;
}

function newIdGen(start = 0) {
  let _id = start;
  return function () {
    return _id++;
  };
}
