.putin-list {
  .manage-list {
    margin-top: 0 !important;
  }
  .next-table.only-bottom-border{
    margin-top: 0 !important;
  }
  .next-tabs {
    margin-top: 20px;
  }
  .btn-seek {
    position: relative;
    top: 1px;
  }
  table {
    td {
      font-family: "PingFang SC";
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      letter-spacing: 0;
      line-height: 20px;
      &:first-child {
        .next-table-cell-wrapper {
          display: flex;
        }
      }
    }
  }
  .tabItem-ID {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 8px;
  }
  .tabItem-ID-row-left {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .tabItem-ID-icon {
    width: 20px;
    height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    font-family: "PingFangSC";
    font-weight: 500;
    font-size: 12px;
    color: #ffffff;
    line-height: 12px;
    border-radius: 50%;
    background-color: rgba(68, 148, 249, 1);
    &.shop {
      background-color: rgba(12, 193, 193, 1);
    }
    &.scene {
      background-color: rgb(204, 73, 61);
    }
    &.emall {
      background-color: hotpink;
    }
    &.card {
      background-color: darkgreen;
    }
  }
  .tabItem-ID-name a {
    font-family: "PingFang SC";
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #333333;
    margin: 0;
    &:hover {
      color: #FF7C4D;
    }
    i {
      margin-left: 3px;
    }
  }

  .tabItem-ID-id {
    font-family: "PingFang SC";
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
  }
  .btn-copy {
    font-size: 12px;
    color: #999999 !important;
  }
  .tabItem-operation {
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
    a,span {
      margin: 0 !important;
      color: #333333 !important;
      &:hover {
        color: #FF7C4D !important;
      }
    }
    > * + *{
      margin-left: 17px;
      position: relative;
      &::before{
        content: "";
        width: 1px;
        height: 14px;
        background-color: #ebebeb;
        position: absolute;
        left: -8px;
      }
    }
  }
  .poolIds {
    cursor: pointer;
    &:hover {
      color: #FF7C4D;
    }
  }
}
