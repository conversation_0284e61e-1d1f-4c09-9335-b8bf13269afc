/**
 * PutInListPage
 * 投放平台
 */

import React, { Fragment, useState, useRef, useEffect } from "react";
import {
  Form,
  Input,
  Grid,
  Button,
  Tab,
  Dialog,
  Message,
  Dropdown,
  Icon,
  Menu,
  Select,
} from "@alife/next";
import { manageList, setTableCol, filterForm } from "@/components/Table";
import { LinkButton } from "@/components/Button";
import { PageWrapper } from "@/components/PageWrapper";
import { formatTimeStamp, FORMAT } from "@/utils/time";
import { DialogBtn, GoldLogLink, AccessBtn } from "@/components/Button";
import {
  getPutInList,
  delPutIn,
  ACLPermission,
  getPoolIdsGroup,
} from "../request";
import { onRequestError } from "@/utils/api";
import { isAllNumber } from "@/utils/validators";
import { doesFormHasErrors } from "@/utils/formTool";
import { getPoolList } from "@/adator/api/pool";
import { track } from "@/utils/aplus";
import { pooTypeRadioRules, ShowCollectionPoolId } from "../constants";
import Clipboard from "clipboard";
import "./style.scss";

const FormItem = Form.Item;
const Option = Select.Option;
const { Row, Col } = Grid;

const skipDetail = async (id, poolType) => {
  try {
    let res = await getPoolList({
      pageIndex: 1,
      pageSize: 10,
      poolId: id,
      rangeType: 0,
    });
    if (res.data.length > 0) {
      let { newPlatformFlag, sourcePoolId, basePoolName, poolType, state } =
        res.data[0];
      if (state != "25") {
        if (newPlatformFlag === 0) {
          let dataType = parseInt(sourcePoolId) >= 20001 ? 1 : 2;
          location.href = `#/${
            poolType == 1 ? "commoditypool" : "storepool"
          }/creation/query/${id}?basePoolId=${sourcePoolId}&basePoolName=${basePoolName}&dataType=${dataType}`;
        } else {
          location.href = `#/pool/list/${
            poolType == 1 ? "tagPool" : "selectedStore"
          }/${id}`;
        }
      } else {
        Message.warning("池子已下线");
      }
    } else {
      Message.warning("池子不存在");
    }
  } catch (error) {
    onRequestError(error);
  }
};

/**常规池id列 */
const PoolIdsCol = ({ record }) => {
  const { poolIds, limitType, poolType } = record;
  let poolIdsReq = "---";
  if (!limitType) {
    // poolIdsReq = poolIds;
    poolIdsReq = poolIds
      ? JSON.parse(poolIds).map((v) => {
          return (
            <a
              href="javascript:void(0)"
              onClick={() => skipDetail(v, record.poolType)}
            >
              {v}
            </a>
          );
        })
      : "";
  } else if (limitType == 1 || limitType == 2) {
    poolIdsReq = pooTypeRadioRules.filter((item) => item.value == poolType)[0]
      .label;
  }
  return <Fragment>{poolIdsReq}</Fragment>;
};
/**加权id列 */
const WeightPoolIdsCol = ({ record }) => {
  const { weightPoolIds, limitType, poolType } = record;
  let weightPoolIdsReq = "---";
  if (!limitType) {
    // weightPoolIdsReq = weightPoolIds;
    weightPoolIdsReq = weightPoolIds
      ? JSON.parse(weightPoolIds).map((v) => {
          return (
            <a
              href="javascript:void(0)"
              onClick={() => skipDetail(v, record.poolType)}
            >
              {v}
            </a>
          );
        })
      : "";
  } else if (limitType == 1 || limitType == 2) {
    weightPoolIdsReq = pooTypeRadioRules.filter(
      (item) => item.value == poolType
    )[0].label;
  }
  return <Fragment>{weightPoolIdsReq}</Fragment>;
};
/**操作列 */
const OptionCol = ({ record, reload }) => {
  const { id } = record;

  const onOk = () => {
    delPutIn(id)
      .then(() => {
        reload();
      })
      .catch(onRequestError);
  };

  const getPermission = async () => {
    try {
      let data = await ACLPermission(id);
      return data.redirectUrl;
    } catch (error) {
      onRequestError(error);
    }
  };

  const deletePutIn = () => {
    track("clickEvent", [
      "/selection_kunlun.PUTIN-MANAGE-putin-list.delete-putin-btn",
      `putInActivityId=${id}`,
    ]);
    Dialog.confirm({
      title: "确认删除该活动？",
      onOk: () => onOk(),
    });
  };
  // let {activityName, startTime, endTime} = record;
  // startTime = startTime ? formatTimeStamp(startTime, FORMAT.DATE).toString(): ''
  // endTime = endTime ? formatTimeStamp(endTime, FORMAT.DATE).toString() : '';
  // const timePeriod = [
  //   {
  //     startTime,
  //     endTime
  //   }
  // ]
  // let pageSet = {
  //   "localdev":'https://market.wapa.taobao.com/app/op_fe_selection/o2o-data-analysis-admin/index.html',
  //   'daily':'https://market.wapa.taobao.com/app/op_fe_selection/o2o-data-analysis-admin/index.html',
  //   "ppe":'https://pre-kunlun.alibaba-inc.com/dataAnalysis',
  //   "prod":'https://kunlun.alibaba-inc.com/dataAnalysis'
  // }
  // let domain = pageSet[window.configEnv];
  return (
    <Fragment>
      <GoldLogLink
        to={`/putIn/list/view/${id}`}
        goldParams={[
          "/selection_kunlun.PUTIN-MANAGE-putin-list.view-putin-btn",
          `putInActivityId=${id}`,
        ]}
      >
        <span className="btn-seek">查看</span>
      </GoldLogLink>
      <AccessBtn
        getPermission={getPermission}
        btnText="预览"
        callback={() => {
          location.href = `#/channelManage/market/debuggingPage?formPutIn=${encodeURIComponent(
            JSON.stringify({
              id: record.id,
              poolType: record.poolType,
            })
          )}`;
        }}
      />
      <AccessBtn
        getPermission={getPermission}
        btnText={"编辑"}
        callback={() => {
          track("clickEvent", [
            "/selection_kunlun.PUTIN-MANAGE-putin-list.edit-putin-btn",
            `putInActivityId=${id}`,
          ]);
          location.href = `#/putIn/list/new/${id}`;
        }}
      />
      <Dropdown
        trigger={
          <div>
            更多&nbsp;
            <Icon type="arrow-down" size="xxs" />
          </div>
        }
        triggerType={["hover"]}
        cache
      >
        <Menu>
          <Menu.Item>
            <div className="menu-item">
              <AccessBtn
                getPermission={getPermission}
                btnText={"复制"}
                callback={() => {
                  track("clickEvent", [
                    "/selection_kunlun.PUTIN-MANAGE-putin-list.copy-putin-btn",
                    `putInActivityId=${id}`,
                  ]);
                  location.href = `#/putIn/list/new/-${id}`;
                }}
              />
            </div>
          </Menu.Item>
          <Menu.Item>
            <div className="menu-item">
              <AccessBtn
                getPermission={getPermission}
                btnText={"删除"}
                callback={() => deletePutIn()}
              />
            </div>
          </Menu.Item>
        </Menu>
      </Dropdown>
      {/* <a target='_blank'
        href={`${domain}#/actEffect/list/detail?activityId=${id}&putinActivityName=${activityName}&timePeriod=${JSON.stringify(timePeriod)}`}>
          <span className='btn-seek'>活动效果</span>
          </a> */}
    </Fragment>
  );
};

const defaultColType = { title: "" };

const iconMap = {
  0: { text: '品', className: '' },
  1: { text: '品', className: '' },
  2: { text: '品', className: '' },
  3: { text: '店', className: 'shop' },
  6: { text: '场', className: 'scene' },
  7: { text: '卡', className: 'card' },
  8: { text: '大', className: 'emall' },
  10: { text: '券', className: '' }, // 行业券包
};

const getIconStyle = (value) => {
  return iconMap[value] || { text: '品', className: '' };
};

const columns = [
  {
    title: "名称/ID",
    dataIndex: "id",
    lock: "left",
    width: "320px",
    cell: (value, rowIndex, record) => {
      const {limitType} = record;
      const iconStyle = getIconStyle(limitType);
      return (
        <div className="tabItem-ID">
          <div className="tabItem-ID-row-left">
            {/*{record["poolType"] == 1 ? (*/}
            {/*  <div className="tabItem-ID-icon">品</div>*/}
            {/*) :record["poolType"] == 2? (*/}
            {/*  <div className="tabItem-ID-icon shop">店</div>*/}
            {/*):(*/}
            {/*  <div className="tabItem-ID-icon scene">场</div>*/}
            {/*)}*/}
            <div className={`tabItem-ID-icon ${iconStyle['className']} `}>{iconStyle['text']}</div>
          </div>
          <div>
            <div className="tabItem-ID-name">
              <GoldLogLink
                to={`/putIn/list/view/${value}`}
                goldParams={[
                  "/selection_kunlun.PUTIN-MANAGE-putin-list.view-putin-btn",
                  `putInActivityId=${value}`,
                ]}
              >
                {record["activityName"]}
                <Icon
                  type="arrow-right"
                  size="xxs"
                  style={{ marginTop: "1px" }}
                />
              </GoldLogLink>
            </div>
            <div className="tabItem-ID-id">
              ID: {value}{" "}
              <span className="btn-copy" data-clipboard-text={value}>
                复制
              </span>{" "}
            </div>
          </div>
        </div>
      );
    },
  },
  // { title: "活动名称", dataIndex: "activityName", width: "10%" },
  {
    title: "加权池ID",
    dataIndex: "weightPoolIds",
    width: "160px",
    cell: function (value, rowIndex, record) {
      // return <WeightPoolIdsCol record={record}/>
      return ShowCollectionPoolId(1, record, true);
    },
  },
  {
    title: "常规池ID",
    dataIndex: "poolIds",
    width: "160px",
    cell: function (value, rowIndex, record) {
      // return <PoolIdsCol record={record}/>
      return ShowCollectionPoolId(2, record, true);
    },
  },
  {
    title: "创建渠道",
    dataIndex: "userOrigin",
    width: "120px",
    cell: function (value, rowIndex, record) {
      let text = " -- ";
      switch (value) {
        case "delivery-platform":
          text = "投放平台";
          break;
        case "ele-newretail-welkin-admin":
          text = "兵马俑";
          break;
      }
      return <div>{text}</div>;
    },
  },
  {
    title: "活动时间",
    dataIndex: "startTime",
    cell: function (value, rowIndex, record) {
      let { startTime, endTime } = record;
      startTime = startTime ? formatTimeStamp(startTime) : "";
      endTime = endTime ? formatTimeStamp(endTime) : "";
      return (
        <div className="tabItem-time">
          <div>起: {startTime}</div>
          <div>止: {endTime}</div>
        </div>
      );
    },
    width: "200px",
  },
  // {
  //   title: "维度",
  //   dataIndex: "poolType",
  //   width: "6%",
  //   cell: (value) => (value == 1 ? "商品" : "门店"),
  // },
  { title: "创建人", dataIndex: "createUser", width: "80px" },
  {
    title: "操作时间",
    dataIndex: "updateAt",
    cell: (value) => formatTimeStamp(value),
    width: "170px",
  },

  {
    title: "操作",
    lock: "right",
    width: "220px",
    cell: function (value, rowIndex, record) {
      return (
        <div className="tabItem-operation">
          <OptionCol record={record} reload={this.reload} />
        </div>
      );
    },
  },
];

function getCol(type) {
  return { ...defaultColType, ...type };
}

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
  labelAlign: "left",
  labelTextAlign: "left",
};

const formLayout = {
  style: {
    margin: "20px 0px 10px 0px",
  },
};

/**检测活动id格式 */
const checkActivityId = function (rule, value, callback) {
  const errors = isAllNumber(value);
  if (errors.length && value) {
    callback("活动ID格式错误");
  } else {
    callback();
  }
};

function PutInForm({ field, searchData, reload }) {
  const onSearch = () => {
    track("clickEvent", [
      "/selection_kunlun.PUTIN-MANAGE-putin-list.search-btn",
    ]);
    field.validate((e) => {
      if (!doesFormHasErrors(e)) {
        searchData();
      }
    });
  };

  return (
    <Form {...formLayout} field={field}>
      <Row>
        <Col>
          <FormItem
            label="活动ID"
            {...formItemLayout}
            validator={checkActivityId}
          >
            <Input placeholder="请输入活动ID" name="activityId" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="活动名称" {...formItemLayout}>
            <Input placeholder="请输入活动名称" name="activityName" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="创建人" {...formItemLayout}>
            <Input placeholder="请输入创建人" name="createrName" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="创建渠道" {...formItemLayout}>
            <Select
              placeholder="请选择创建渠道"
              name="userOrigin"
              defaultValue="delivery-platform"
            >
              <Option value="delivery-platform">投放平台</Option>
              <Option value="ele-newretail-welkin-admin">兵马俑</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <Button type="primary" onClick={onSearch}>
            查询
          </Button>
          &nbsp;&nbsp;
          <Button type="normal" onClick={reload}>
            重置
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

const PutInFilterForm = filterForm(PutInForm, [
  "activityId",
  "activityName",
  "createrName",
  "userOrigin",
]);
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(
  PutInTable,
  PutInFilterForm,
  async (search, reservedParams) => {
    let { query } = search;
    if (JSON.stringify(query) == "{}") {
      query.userOrigin = "delivery-platform";
    }
    let { activityId } = query;
    if (activityId) {
      query = { ...query, activityId: +activityId };
    }
    const listData = await getPutInList({ ...search, query }, reservedParams);
    return listData;
  }
);

export const PutInListPage = function () {
  track("setSpm", ["12964525"]);

  const [onlyMine, setOnlyMine] = useState(1);
  const manageListRef = useRef(null);

  const onTabChange = (v) => {
    let tabValue = parseInt(v);
    setOnlyMine(tabValue);
    manageListRef.current.fetchData({ page: 1 }, tabValue);
  };

  useEffect(() => {
    const clipboard = new Clipboard(".btn-copy");
    clipboard.on("success", function (e) {
      console.log(e);
      Message.success("复制成功");
    });
    return () => {
      clipboard.destroy();
    };
  }, [onlyMine]);

  return (
    <PageWrapper title="投放活动管理" className="putin-list">
      <ManageList ref={manageListRef} onlyMine={onlyMine}>
        <LinkButton
          to={"/PutIn/list/new"}
          onClick={() => {
            track("clickEvent", [
              "/selection_kunlun.PUTIN-MANAGE-putin-list.create-putin-btn",
            ]);
          }}
        >
          新建投放活动
        </LinkButton>
        <Tab shape="wrapped" onChange={onTabChange} activeKey={onlyMine}>
          <Tab.Item title="我创建的" key={1}></Tab.Item>
          <Tab.Item title="已申请的" key={2}></Tab.Item>
          <Tab.Item title="可查看的" key={3}></Tab.Item>
        </Tab>
      </ManageList>
    </PageWrapper>
  );
};
