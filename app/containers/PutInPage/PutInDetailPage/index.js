import React from 'react';
import { Breadcrumb } from '@alife/next';
import { PageWrapper } from '@/components/PageWrapper';
import { PageBase } from '@/containers/base';
import { DetailList } from '../DetailList';
import { defaultDetail } from '../constants'
import { getPutInDetail } from '../request'
import { onRequestError, queryStoreMajorCategory, queryEatUnionTag } from '@/utils/api';
import { track } from '@/utils/aplus';
import { Link } from 'react-router-dom';
import './index.scss';

export class PutInDetailPage extends PageBase {
  constructor(props) {
    super(props);

    this.state = {
      detail: { ...defaultDetail }
    }
  }
  // 获取门店经营类型
  getQueryStoreMajorCategory() {
    return queryStoreMajorCategory().then((res) => {
      return res
    }).catch(onRequestError)
  }
  fetchQueryEatUnionTag() {
    return queryEatUnionTag().then((res) => res).catch(onRequestError)
  }

  async componentDidMount() {
    //打点
    track('setSpm', [12964533]);

    const { activityId } = this.params;
    try {
      const detail = await getPutInDetail(activityId);
      const storeMajorCategory = await this.getQueryStoreMajorCategory()
      const eatUnionTag = await this.fetchQueryEatUnionTag()
      const newData = {
        ...detail,
      }
      newData.recallRulesRep = {
        ...newData.recallRulesRep,
        shopMajorCategoryList: storeMajorCategory,
        eatUnionTagList: eatUnionTag
      }
      console.log("newData",newData);
      this.setState({
        detail: newData
      })
    } catch (error) {
      onRequestError(error)
    }
  }

  render() {
    const { detail } = this.state;

    return (
      <PageBase.Container className="putin-detailpage">
        <div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item link="javascript:void(0);">投放</Breadcrumb.Item>
            <Breadcrumb.Item link="javascript:void(0);"><Link to={`/putIn/list`}>活动管理</Link></Breadcrumb.Item>
            <Breadcrumb.Item>查看</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <PageWrapper title={detail.activityName} className="detail-title" withBorder={true} secondTitle={`ID: ${detail.id}`}>
          <DetailList detail={detail} />
        </PageWrapper>
      </PageBase.Container>
    )
  }
}
