import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';

import injectReducer from '@/utils/injectReducer';
import injectSaga from '@/utils/injectSaga';
import { PutInPage } from './PutInPage'
import { detailReducer, templateReducer} from './reducer'
import saga from './saga';
import { updateFirst, updateRecallRules, updateRepeatRules, updateSortRules, updateDistributeRules, updateFilterRules, initData, getPoolType, setPoolType, blankData, updateTemplate, updateSecondPage, blankTemplate } from './actions'
import { selectDataByKey, getRulesTemplate } from './selectors'
import { makeCities, makeDeliveryData } from '../../App/selectors'

const mapDispatchToProps = (dispatch) => ({
  onFirstPageChange: (name, value) => {
    dispatch(updateFirst(name, value))
  },
  updateRecallRules: (name, value) => {
    dispatch(updateRecallRules(name, value))
  },
  updateRepeatRules: (name, value) => {
    dispatch(updateRepeatRules(name, value))
  },
  updateSortRules:(name, value) => {
    dispatch(updateSortRules(name, value))
  },
  updateFilterRules:(name, value) => {
    dispatch(updateFilterRules(name, value))
  },
  updateDistributeRules:(name, value) => {
    dispatch(updateDistributeRules(name, value))
  },
  blankData:() => {
    dispatch(blankData())
  },
  blankTemplate: () => {
    dispatch(blankTemplate())
  },
  init:(activityId) => {
    dispatch(initData(activityId))
  },
  getPoolType: (poolId) => {
    dispatch(getPoolType(poolId))
  },
  setPoolType: (poolType) => {
    dispatch(setPoolType(poolType))
  },
  updateTemplate:(name, value) => {
    dispatch(updateTemplate(name, value))
  },
  updateSecondPage:(secondPage) => {
    dispatch(updateSecondPage(secondPage))
  }
})

const mapStateToProps = createStructuredSelector({
  data: selectDataByKey('firstPage'),
  rules: selectDataByKey('secondPage'),
  poolType: selectDataByKey('poolType'),
  rulesTemplate: getRulesTemplate(),
  cities: makeCities(),
  deliveryData: makeDeliveryData()
})

const withConnect = connect(mapStateToProps, mapDispatchToProps)
const putInFormReducer = injectReducer({ key:'putInForm', reducer: detailReducer });
const rulesTemplateReducer = injectReducer({ key:'rulesTemplate', reducer: templateReducer });
const withSaga = injectSaga({key:'putInForm', saga})

export const CreatePutInPage = compose(rulesTemplateReducer, putInFormReducer, withSaga, withConnect)(PutInPage)
