import debugFn from 'debug';
import React from "react";
import moment from "moment";
import { PageWrapper } from "@/components/PageWrapper";
import { Step, Breadcrumb, Overlay, Icon, Message } from "@alife/next";
import { FillDataSource } from "./FillDataSource";
import { RulesSetting } from "./RulesSetting/index";
import { PageBase } from "@/containers/base";
import { createPutIn, creatTemplateRules, ACLPermission } from "../request";
import { onRequestError } from "@/utils/api";
import { logTimeComponent, goldLog, track } from "@/utils/aplus";
import { filterData } from "@/utils/filterDataByKey";
import { momentToTimeStamp } from "@/utils/time";
import { withoutCopy, getQueryString, replaceParamVal } from "@/utils/others";
import { isNull, isUndefined } from "@/utils/validators";
import { permissionAccess } from "@/components/PermissionAccess";
import { Link } from "react-router-dom";
import { exlog } from '@/containers/App/exlog';

import "./style.scss";
import { createDelivery, saveDelivery } from "../../channel/market/request";
import {pageMap, showQrCodeMap} from "../../channel/market/common";
// import { SchemaMap } from "../../channel/market/config";
// import {uniq} from "../../../utils/others";

const debug = debugFn('selection:putin:PutinPage');
const StepItem = Step.Item;
//
// console.log('pageUrl',pageUrl);

/**格式化首页信息 */
function formalFirstPage(firstData) {
  const getByKey = filterData(firstData);
  return getByKey(
    [
      "activityTime",
      "channelList",
      "channelLabel",
      "checkAllValue",
      "limitType",
      "poolIds",
      'weightPoolIds',
      'commonCollection',
      'weightCollection',
      "activityName",
      "citys",
      "selectedCitys",
      "griddings",
      "userTagGroupIdsList",
      "isRecommend",
      "categoryType",
      "categoryList",
      "isCatComp",
      "riskCompLevel",
      "mainCates",
    ],
    (data) => {
      const {
        activityTime,
        limitType,
        channelList,
        channelLabel,
        poolIds,
        weightPoolIds,
        commonCollection,
        weightCollection,
        activityName,
        citys,
        selectedCitys,
        griddings,
        userTagGroupIdsList,
        isRecommend,
        categoryType,
        categoryList,
        isCatComp,
        riskCompLevel,
        mainCates,
      } = data;
      const [start, end] = activityTime;

      let itemCatList = [];
      let rejectItemCatList = [];
      if (isRecommend) {
        switch (categoryType) {
          case 0:
            itemCatList = categoryList;
            break;
          case 1:
            rejectItemCatList = categoryList;
            break;
        }
      }
      return {
        // ...firstData,
        activityTime: firstData.activityTime,
        channelList,
        channelLabel,
        limitType,
        poolIds: poolIds ? poolIds : [],
        weightPoolIds: weightPoolIds ? weightPoolIds : [],
        commonCollection,
        weightCollection,
        startTime: momentToTimeStamp(start),
        endTime: momentToTimeStamp(end),
        activityName,
        citys: citys.filter((item) => !item.level || item.level < 3),
        selectedCitys,
        griddings,
        userTagGroupIdsList,
        isRecommend,
        itemCat: itemCatList,
        rejectItemCat: rejectItemCatList,
        isCatComp,
        riskCompLevel,
        mainCates,
      };
    }
  );
}

/**格式化召回信息 */
function formalRecall(recallRules) {
  const getRecallByKey = filterData(recallRules);
  return getRecallByKey(
    [
      "storeDistance",
      'shopScore',
      "storeTypeRep",
      'shopMajorCategory',
      'eatUnionTag',
      'itemBrandList',
      "shopBrands",
      "goodsOriginalPrice",
      "goodsPresentPrice",
      "skuDiscount",
      "goodsCategory",
      "isNationalDelivery",
      "goodsActivitiesType",
    ],
    (data) => {
      const {
        storeDistance,
        shopScore,
        storeTypeRep,
        goodsOriginalPrice,
        goodsPresentPrice,
        // skuDiscount,
        goodsCategory,
        isNationalDelivery = false,
        goodsActivitiesType,
      } = data;
      return {
        ...recallRules,
        isNationalDelivery,
        storeDistance: storeDistance.filter(
          (item) => !isNull(item) && !isUndefined(item)
        ),
        shopScore: shopScore.filter(
          (item) => !isNull(item) && !isUndefined(item)
        ),
        // 字段配置
        storeType: storeTypeRep.map((item) => item.value),
        // goodsOriginalPrice: goodsOriginalPrice ? goodsOriginalPrice.filter(item => !isNull(item) && !isUndefined(item)) : [],
        // goodsPresentPrice: goodsOriginalPrice ? goodsPresentPrice.filter(item => !isNull(item) && !isUndefined(item)): [],
        // skuDiscount: skuDiscount ? skuDiscount.filter(item => !isNull(item) && !isUndefined(item)) : [],
        goodsCategory: goodsCategory || [],
        // goodsActivitiesType: goodsActivitiesType || [],
      };
    }
  );
}

/**格式化配送规则 */
function formDeliveryRules(deliveryRules) {
  const getRecallByKey = filterData(deliveryRules);
  console.log(getRecallByKey("distributionFullDecrementAmount"));
  return getRecallByKey("distributionFee", (distributionFee) => {
    let distributionFullDecrementAmountValue = getRecallByKey(
      "distributionFullDecrementAmount"
    )
      ? getRecallByKey("distributionFullDecrementAmount")
      : [];
    return {
      ...deliveryRules,
      distributionFullDecrementAmount: distributionFullDecrementAmountValue.filter(
        (item) => !isNull(item) && !isUndefined(item)
      ),
      distributionFee: distributionFee.filter(
        (item) => !isNull(item) && !isUndefined(item)
      ),
    };
  });
}

/**格式化排序规则 */
function formSortRules(rules, poolType) {
  const { skuSortRules, storeSortRules } = rules;

  if (poolType == 1) {
    return { storeSortRules: 0, skuSortRules };
  } else {
    return { skuSortRules: 0, storeSortRules };
  }
}

/**格式化第二页数据 */
function formSecondPage(rules, poolType) {
  const recallRules = formalRecall(rules.recallRules);
  const sortRules = formSortRules(rules, poolType);
  const deliveryRules = formDeliveryRules(rules.deliveryRules);
  if (rules.sortRules && rules.sortRules.discountRankFactor !== 3) {
    delete rules.sortRules.goodsCategoryList;
  }
  if (
    rules.filterRules &&
    rules.filterRules.priceFilter &&
    !rules.filterRules.priceFilter[0] &&
    !rules.filterRules.priceFilter[1]
  ) {
    rules.filterRules.priceFilter = [];
  }
  return {
    ...rules,
    recallRules,
    deliveryRules,
    ...sortRules,
  };
}

function requireSubmit(params, needMeassage) {
  const {
    poolIds,
    limitType,
    putinRulesReq: { storeSortRules, skuSortRules },
  } = params;

  if (poolIds.length == 0 && limitType != 1 && limitType != 2) {
    //选门店聚类的不用提示
    if (needMeassage) {
      this.toast({
        type: "warning",
        title: "注意",
        content: "选品集Id不能为空",
      });
    }
    return false;
  }

  if (skuSortRules == 0 && storeSortRules == 0) {
    if (needMeassage) {
      this.toast({
        type: "warning",
        title: "注意",
        content: "投放规则中排序必填",
      });
    }
    return false;
  }
  return true;
}

/**before submit */
function beforeSubmit(params) {
  const {
    putinRulesReq: {
      recallRules: { storeDistance, shopScore, itemBrandList = [], shopBrands = ''},
      filterRules: { priceFilter },
      deliveryRules: { distributionFee },
    },
  } = params;
  if (storeDistance.length == 1) {
    this.toast({
      type: "warning",
      title: "注意",
      content: "请填写完整店铺距离范围",
    });
    return false;
  }
  if (shopScore.length == 1) {
    this.toast({
      type: "warning",
      title: "注意",
      content: "请填写完整店铺评分",
    });
    return false;
  }


  if (distributionFee.length == 1) {
    this.toast({
      type: "warning",
      title: "注意",
      content: "请填写完整配送费范围",
    });
    return false;
  }

  if (priceFilter && priceFilter.filter((v) => !v).length > 0) {
    this.toast({
      type: "warning",
      title: "注意",
      content: "请填写完整商品活动价格",
    });
    return false;
  }
  if(itemBrandList && itemBrandList.length > 5){
    this.toast({
      type: "warning",
      title: "注意",
      content: "商品所属品牌最多输入五个",
    });
    return false;
  }

  if(shopBrands && shopBrands.length > 0){
    if(shopBrands && shopBrands.split(',').length > 5){
      this.toast({
        type: "warning",
        title: "注意",
        content: "门店所属品牌最多输入五个",
      });
      return false;
    }else if(shopBrands && shopBrands.indexOf("，")>0){
      this.toast({
        type: "warning",
        title: "注意",
        content: "门店所属品牌输入不能有中文逗号",
      });
      return false;
    }else if(shopBrands && shopBrands.indexOf(" ")!=-1){
      this.toast({
        type: "warning",
        title: "注意",
        content: "门店所属品牌输入不能有空格",
      });
      return false;
    }else if(shopBrands && shopBrands.indexOf("\n")>0){
      this.toast({
        type: "warning",
        title: "注意",
        content: "门店所属品牌输入不能有回车",
      });
      return false;
    }
  }
  return true;
}

class PutInPageBase extends PageBase {
  constructor(props) {
    super(props);
    let isChannel = location.href.includes("channelManage") || location.href.includes("supermarketChannel") || location.href.includes("setting/setSchema");
    let resourceName = getQueryString("resourceName");
    let showGrid = resourceName == "每日聚划算";
    this.state = {
      currentStep: 0,
      submitLoading: false,
      isChannel,
      showGrid,
      ruleSettingVisible: false,
      detailData: "",
    };
    this.activityId = withoutCopy(this.params.activityId, (value) => {
      if (value) this.isEdit = true;
    });
  }

  /**初始化编辑数据 */
  async componentDidMount() {
    track("setSpm", [12964530]);

    // 获取所有投放渠道、投放标签
    this.setDeliveryData();

    if (this.isEdit) {
      const { withPermission } = await this.props.getPermission(
        this.activityId
      );
      if (!withPermission) return;
    }

    if (this.activityId) {
      await this.props.init(this.activityId);
    } else {
      //频道页
      let step = sessionStorage.getItem("step");
      let activityId = JSON.parse(sessionStorage.getItem("putInActivityId"));
      if (activityId && activityId[step - 1]) {
        this.props.init(activityId[step - 1]);
      }
    }
    localStorage.removeItem("poolIdsSet");
  }

  handleChannelList = (channelList, deliveryChannelShow) => {
    channelList.forEach((channelItem) => {
      if (deliveryChannelShow.indexOf("*") !== -1) {
        channelItem.checkboxDisabled = false;
      } else {
        channelItem.checkboxDisabled = true;
        deliveryChannelShow.forEach((key) => {
          const comparedKey =
            channelItem.value === "mini_app" ? "miniapp" : channelItem.value;
          if (comparedKey.indexOf(key) != -1) {
            channelItem.checkboxDisabled = false;
          }
        });
      }
      if (channelItem.subChannels) {
        channelItem.children = JSON.parse(
          JSON.stringify(channelItem.subChannels)
        );
        this.handleChannelList(channelItem.children, deliveryChannelShow);
      } else {
        channelItem.children = [];
      }
    });
  };

  setDeliveryData() {
    let { channelListMap, labelListMap } = this.props.deliveryData;
    this.props.onFirstPageChange("channelListMap", channelListMap);
    this.props.onFirstPageChange("labelListMap", labelListMap);
  }

  componentWillUnmount() {
    this.props.blankData();
    this.props.blankTemplate();
  }

  onStepChange = (currentStep) => {
    this.setState({ currentStep });
  };

  /**ACL 权限认证 */
  // async getACL(activityId) {
  //   const resp = await ACLPermission(activityId);
  //   if (resp && resp.rediectUrl) { //error case
  //     window.location = resp.rediectUrl
  //   }
  // }

  /**完成按钮点击事件 */
  onSubmit = (isPublish = false) => {
    const { isChannel } = this.state;
    if (!isChannel) {
      //非频道页的发布投放活动
      const { poolType } = this.props;
      const rules = this.props.rules.toJS();
      let reqData = this.props.data.toJS();

      if (!reqData.channelList) {
        reqData.channelList = reqData.deliveryChannelList
          ? reqData.deliveryChannelList.slice(0)
          : [];
      }
      if (!reqData.channelLabel) {
        reqData.channelLabel = reqData.deliveryChannelLabel
          ? reqData.deliveryChannelLabel.slice(0)
          : [];
      }
      const data = formalFirstPage(reqData);
      let putinRulesReq = formSecondPage(rules, poolType);
      if (poolType == 2) { //店的投放活动
        putinRulesReq.repeatRules.storeRepeatCommodityNum = 4;
      }
      const { activityId } = this.params;
      let message = "创建成功!";

      const params = {
        ...data,
        putinRulesReq,
        poolType,
      };

      if (!beforeSubmit.apply(this, [params])) return;

      withoutCopy(activityId, (value) => {
        params["activityId"] = value;
        message = "编辑成功!";
      });

      this.setState({ submitLoading: true });
      createPutIn(params)
        .then(() => {
          if (!this.isEdit) {
            this.props.goldLogTimeConsume();
          }
        })
        .then(() => {
          this.history.push("/putIn/list");
          this.toast({
            type: "success",
            title: "成功",
            content: message,
          });
        })
        .catch(onRequestError)
        .finally(() => this.setState({ submitLoading: false }));
    } else {
      //频道页的发布投放活动
      this.submitChannel(isPublish);
    }
  };

  /**完成按钮点击事件 */
  submitChannel = (isPublish) => {
    // const {isChannel} = this.state;
    const { poolType, resourceType } = this.props;

    const rules = this.props.rules.toJS();
    let reqData = this.props.data.toJS();
    if (!reqData.channelList) {
      reqData.channelList = reqData.deliveryChannelList
        ? reqData.deliveryChannelList.slice(0)
        : [];
    }
    if (!reqData.channelLabel) {
      reqData.channelLabel = reqData.deliveryChannelLabel
        ? reqData.deliveryChannelLabel.slice(0)
        : [];
    }
    const data = formalFirstPage(reqData);
    const putinRulesReq = formSecondPage(rules, poolType);

    if (
      resourceType == 40 &&
      poolType == 1 &&
      putinRulesReq.skuSortRules != 4 &&
      putinRulesReq.skuSortRules != 5 &&
      putinRulesReq.skuSortRules != 6
    ) {
      return Message.error(
        "支持门店类型的选品集或者商品类型的选品集用门店排序，请自查。"
      );
    }

    if (resourceType == 105) {
      if (poolType != 1) {
        return Message.error(
          "该资源位仅支持商品类型的选品集，请自查。"
        );
      } else {
        putinRulesReq.filterRules.whiteBasePic = true;
      }
    }

    let { activityId } = this.params;

    const params = {
      ...data,
      putinRulesReq,
      poolType,
    };

    // if(isChannel){
    let formData = JSON.parse(sessionStorage.getItem("baseConfig"));
    params.activityTime = [
      moment(formData.beginTime),
      moment(formData.endTime),
    ];
    params.activityName = formData.configName;
    params.startTime = momentToTimeStamp(moment(formData.beginTime));
    params.endTime = momentToTimeStamp(moment(formData.endTime));
    let step = sessionStorage.getItem("step")
      ? sessionStorage.getItem("step")
      : 0;
    let putInActivityId = JSON.parse(sessionStorage.getItem("putInActivityId"));
    let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
    let showDataConfig = SchemaMap.showDataConfig;
    let showConfigGroup = SchemaMap.showConfigGroup;
    let configGroup = SchemaMap.configGroup;
    let newsChannelPoolType;
    let needPutIn;
    if (isPublish) {
      // 发布
      // let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
      if (showDataConfig) {
        activityId = putInActivityId ? putInActivityId[0] : "";
        newsChannelPoolType = SchemaMap ? SchemaMap["newsChannelPoolType"] : "";
        needPutIn = true;
      }
      if (showConfigGroup) {
        activityId = putInActivityId[configGroup.length - 2];
        newsChannelPoolType = SchemaMap
          ? configGroup[step].newsChannelPoolType
          : "";
        needPutIn =
          configGroup && typeof configGroup[step].required != "undefined"
            ? configGroup[step].required
            : true;
      }
    } else {
      //上一步 下一步
      activityId = sessionStorage.getItem("putInActivityId")
        ? JSON.parse(sessionStorage.getItem("putInActivityId"))[step - 1]
        : "";
      newsChannelPoolType =
        showConfigGroup && configGroup
          ? configGroup[step].newsChannelPoolType
          : "";
      needPutIn =
        showConfigGroup &&
        configGroup &&
        typeof configGroup[step].required != "undefined"
          ? configGroup[step].required
          : true;
    }
    // 复制的时候去掉投放活动ID。
    if (sessionStorage.getItem("isCopy")) {
      activityId = "";
    }
    if (newsChannelPoolType) {
      params.newsChannelPoolType = newsChannelPoolType;
    }

    if (!beforeSubmit.apply(this, [params])) return;

    if (
      showConfigGroup &&
      !needPutIn &&
      !requireSubmit.apply(this, [params, false])
    ) {
      //选填的情况
      let putInActivityId = JSON.parse(
        sessionStorage.getItem("putInActivityId")
      );
      let putPoolType = JSON.parse(sessionStorage.getItem("putPoolType"));
      let step = parseInt(sessionStorage.getItem("step"));
      let baseConfig = JSON.parse(sessionStorage.getItem("baseConfig"));
      if (!putInActivityId && sessionStorage.getItem("isCopy")) {
        putInActivityId = [];
        putPoolType = [];
      }
      let stepNum = parseInt(step) - 1;
      if (typeof putInActivityId[stepNum] == "undefined") {
        putInActivityId.push("");
        putPoolType.push("");
      }
      if (params.poolIds.length == 0) {
        //删除选品集id，需要解绑此投放活动id
        putInActivityId.splice(stepNum, 1, "");
        putPoolType.splice(stepNum, 1, "");
      }
      baseConfig.putInActivityId = putInActivityId;
      baseConfig.putPoolType = putPoolType;
      sessionStorage.setItem("baseConfig", JSON.stringify(baseConfig));
      sessionStorage.setItem(
        "putInActivityId",
        JSON.stringify(putInActivityId)
      );
      sessionStorage.setItem("putPoolType", JSON.stringify(putPoolType));
      if (isPublish) {
        this.createConfig();
      } else {
        step = step + 1;
        sessionStorage.setItem("step", step);
        replaceParamVal("step", sessionStorage.getItem("step"));
      }
    } else if (requireSubmit.apply(this, [params, true])) {
      withoutCopy(activityId, (value) => {
        params["activityId"] = value;
      });

      this.setState({ submitLoading: true });
      if ((params.commonCollection && params.commonCollection.length > 0) || (params.weightCollection && params.weightCollection.length > 0)) {  //兼容频道管理创建投放活动，不用commonCollection的数据
        params.commonCollection = null;
      }
      if (params.poolType && params.poolType == 2) { //店的投放活动
        params.putinRulesReq.repeatRules.storeRepeatCommodityNum = 10;
      }
      createPutIn(params)
        .then((data) => {
          if (!this.isEdit) {
            this.props.goldLogTimeConsume();
          }
          let putInActivityId = JSON.parse(
            sessionStorage.getItem("putInActivityId")
          );
          let putPoolType = JSON.parse(sessionStorage.getItem("putPoolType"));
          let baseConfig = JSON.parse(sessionStorage.getItem("baseConfig"));
          if (!putInActivityId) {
            putInActivityId = [];
            putPoolType = [];
          }
          let stepNum = parseInt(step) - 1;
          if (
            !putInActivityId.includes(data) &&
            putInActivityId[stepNum] != ""
          ) {
            putInActivityId[stepNum] = data;
            putPoolType.push(params.poolType);
          }
          if (putInActivityId[stepNum] == "") {
            putInActivityId.splice(stepNum, 1, data);
            putPoolType.splice(stepNum, 1, params.poolType);
          }
          if (putPoolType[stepNum] != params.poolType) {
            //切换选品集类型
            putPoolType.splice(stepNum, 1, params.poolType);
          }
          baseConfig.putInActivityId = putInActivityId;
          baseConfig.putPoolType = putPoolType;
          sessionStorage.setItem("baseConfig", JSON.stringify(baseConfig));
          sessionStorage.setItem(
            "putInActivityId",
            JSON.stringify(putInActivityId)
          );
          sessionStorage.setItem("putPoolType", JSON.stringify(putPoolType));
        })
        .then(() => {
          if (isPublish) {
            this.createConfig();
          } else {
            let step = parseInt(sessionStorage.getItem("step"));
            step = step + 1;
            sessionStorage.setItem("step", step);
            replaceParamVal("step", sessionStorage.getItem("step"));
          }
        })
        .catch((err) => {
          Message.show({
            type: "warning",
            content: err.msg || "网络请求出错请稍后再试",
          });
          this.setState({ submitLoading: false });
        });
      // }).catch(onRequestError).finally(() => {
      //   if (!isPublish) { //非发布状态，去掉置灰
      //     this.setState({submitLoading: false})
      //   }
      // })
    }
  };

  /**保存为规则模版 */
  onSubmitTemplate = async ({ templateName, id }) => {
    const { poolType } = this.props;
    const rules = this.props.rules.toJS();
    const putinRulesReq = formSecondPage(rules, poolType);

    let message = "创建成功!";

    const params = {
      putinRulesReq,
      templateRuleName: templateName,
      templateType: poolType,
    };

    if (!beforeSubmit.apply(this, [params])) return false;

    if (id) {
      params["id"] = id;
      message = "更新成功！";
    }

    return creatTemplateRules(params)
      .then(() => {
        this.toast({
          type: "success",
          title: "成功",
          content: message,
        });
      })
      .catch(onRequestError);
  };

  createConfig = () => {
    this.setState({ submitLoading: true });
    let pageId = getQueryString("pageId");
    let resourceId = getQueryString("resourceId");
    let locationId = getQueryString("posId");
    let posName = getQueryString("posName");
    let resourceName = getQueryString("resourceName");
    let resourceType = getQueryString("resourceType");
    let sceneWords = sessionStorage.getItem("sceneWords")
      ? sessionStorage.getItem("sceneWords")
      : "";
    let relatedSup = sessionStorage.getItem("relatedSup")
      ? sessionStorage.getItem("relatedSup")
      : "";
    let baseConfigObj = JSON.parse(sessionStorage.getItem("baseConfig"));
    // if(baseConfigObj.redPackText){
    //   baseConfigObj.redPacketTitleLeft = baseConfigObj.redPackText.split("-")[0];
    //   baseConfigObj.redPacketTitleRight = baseConfigObj.redPackText.split("-")[1];
    // }
    if (sceneWords) {
      baseConfigObj.sceneWords = sceneWords;
    }
    if (relatedSup) {
      baseConfigObj.relatedSup = relatedSup;
    }

    if(baseConfigObj.jumpUrl && baseConfigObj.jumpUrl!=""){
      baseConfigObj.jumpUrl = baseConfigObj.jumpUrl.trim();
    }

    let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
    let configGroup = SchemaMap.configGroup;
    let showConfigGroup = SchemaMap.showConfigGroup;
    let showDataConfig = SchemaMap.showDataConfig;
    let putInActivityId = JSON.parse(sessionStorage.getItem("putInActivityId"));
    let putPoolType = JSON.parse(sessionStorage.getItem("putPoolType"));
    if (showConfigGroup) {
      baseConfigObj.dataConfigList = [];
      configGroup.map((v, i) => {
        if (v.type == 2 && putInActivityId[i - 1] != "") {
          baseConfigObj.dataConfigList.push({
            activityId: putInActivityId[i - 1],
            lockStatus: v.lockStatus,
            locationRange: v.locationRange,
            dataType: v.dataType,
            stepIndex: i - 1,
            poolType: putPoolType[i - 1],
          });
        }
      });
    }
    if (showDataConfig) {
      //数据配置的时候也需要传poolType给后端
      baseConfigObj.poolType = putPoolType[0];
    }
    sessionStorage.setItem("baseConfig", JSON.stringify(baseConfigObj));
    let baseConfig = sessionStorage.getItem("baseConfig");

    let newPutInInfo = {};
    let showAddConfig = SchemaMap.showAddConfig;
    if((showDataConfig || showConfigGroup) && showAddConfig){
      newPutInInfo = JSON.parse(sessionStorage.getItem('newPutInInfo'));
    }
    let deliveryInfoObj = {
      ...newPutInInfo,
      ...(JSON.parse(baseConfig)),
    }

    if(deliveryInfoObj.channelVersionList && newPutInInfo.channelVersionList){
      deliveryInfoObj.channelVersionList = JSON.parse(JSON.stringify(newPutInInfo.channelVersionList));
    }
    if(deliveryInfoObj.channelVersionList && !(deliveryInfoObj.channelList.includes("*") || deliveryInfoObj.channelList.includes("android") || deliveryInfoObj.channelList.includes("ios"))){
      delete deliveryInfoObj.channelVersionList;
    }
    const ext_aoi_codes = JSON.parse(sessionStorage.getItem('ext_aoi_codes'));
    if (ext_aoi_codes) {
      deliveryInfoObj.ext_aoi_codes = ext_aoi_codes;
    }

    let deliveryRequestDTO = {
      pageId,
      resourceId,
      locationId,
      deliveryInfo: JSON.stringify(deliveryInfoObj),
    };




    let baseConfigObject = JSON.parse(baseConfig);
    let isCopy = sessionStorage.getItem("isCopy");
    if (baseConfigObject.configId && !isCopy) {
      deliveryRequestDTO.id = baseConfigObject.configId;
    }
    if (relatedSup) {
      deliveryRequestDTO.parentId = relatedSup;
    }
    const pageUrl = baseConfigObject.configId
      ? location.href.substring(
          0,
          location.href.lastIndexOf("/", location.href.lastIndexOf("/") - 1)
        )
      : location.href.substring(0, location.href.lastIndexOf("/"));
    // const pageUrl = pageMap.filter((v) => v.pageId == pageId)[0].url;
    if(!showQrCodeMap[pageUrl]){
      createDelivery(deliveryRequestDTO)
        .then(() => {
          this.toast({
            type: "success",
            title: "成功",
            content: "创建成功",
          });
          sessionStorage.clear();
          // const pageUrl = pageMap.filter((v) => v.pageId == pageId)[0].url;
          location.href = `${pageUrl}/viewConfigure?pageId=${pageId}&posName=${posName}&posId=${locationId}&resourceId=${resourceId}&resourceName=${resourceName}&resourceType=${resourceType}`;
        })
        .catch(onRequestError)
        .finally(() => this.setState({ submitLoading: false }));
    }else{
      saveDelivery(deliveryRequestDTO)
        .then((data) => {
          console.log(data);
          this.toast({
            type: "success",
            title: "成功",
            content: "创建成功",
          });
          sessionStorage.clear();
          // const pageUrl = pageMap.filter((v) => v.pageId == pageId)[0].url;
          location.href = `${pageUrl}/viewConfig/${data}?pageId=${pageId}&posName=${posName}&posId=${locationId}&resourceId=${resourceId}&resourceName=${resourceName}&resourceType=${resourceType}`;
          // location.href = `#${pageUrl}/viewConfigure?pageId=${pageId}&posName=${posName}&posId=${locationId}&resourceId=${resourceId}&resourceName=${resourceName}&resourceType=${resourceType}`;
        })
        .catch(onRequestError)
        .finally(() => this.setState({ submitLoading: false }));
    }
  };

  showRulesSetting = (ruleSettingVisible) => {
    this.setState({
      ruleSettingVisible,
    });
  };

  render() {
    const {
      currentStep,
      submitLoading,
      isChannel,
      showGrid,
      ruleSettingVisible,
    } = this.state;
    const {
      onFirstPageChange,
      updateSecondPage,
      updateRecallRules,
      updateRepeatRules,
      updateDistributeRules,
      updateFilterRules,
      updateTemplate,
      updateSortRules,
      getPoolType,
      setPoolType,
      data,
      rules,
      rulesTemplate,
      poolType,
      cities,
      resourceType,
    } = this.props;

    debug('PutInPageBase render', 'this.props', this.props, 'this.state', this.state, 'this.isEdit', this.isEdit);

    let editlable = this.isEdit ? "编辑" : "新建";
    // console.log(this.props.data.toJS(), 'this.props.data');

    if (!isChannel) {
      try {
        exlog.logCustomEvent({
          code: "legacy-branches",
          message: "PutinPage 非资源位配置场景",
        });
      } catch(e) {
        console.error(e)
      }

      return (
        <PageBase.Container className="putinpage-new">
          <div className="nav-wrapper">
            <Breadcrumb>
              <Breadcrumb.Item link="javascript:void(0);">投放</Breadcrumb.Item>
              <Breadcrumb.Item link="javascript:void(0);">
                <Link to={"/putIn/list"}>活动管理</Link>
              </Breadcrumb.Item>
              <Breadcrumb.Item>{editlable}</Breadcrumb.Item>
            </Breadcrumb>
          </div>
          <PageWrapper title={`${editlable}投放活动`}>
            <Step current={currentStep} shape="circle" className="putIn-step">
              <StepItem title="填写数据" />
              <StepItem title="设置投放规则" />
            </Step>
            {currentStep === 0 && (
              <FillDataSource
                onFirstPageChange={onFirstPageChange}
                data={data}
                isChannel={isChannel}
                showGrid={showGrid}
                step={0}
                onStepChange={this.onStepChange}
                getPoolType={getPoolType}
                setPoolType={setPoolType}
                cities={cities}
                poolType={poolType}
              />
            )}
            {currentStep === 1 && (
              <RulesSetting
                submitLoading={submitLoading}
                updateRecallRules={updateRecallRules}
                updateSecondPage={updateSecondPage}
                updateRepeatRules={updateRepeatRules}
                updateSortRules={updateSortRules}
                updateDistributeRules={updateDistributeRules}
                updateFilterRules={updateFilterRules}
                updateTemplate={updateTemplate}
                onSubmit={this.onSubmit}
                onSubmitTemplate={this.onSubmitTemplate}
                rules={rules}
                data={data}
                rulesTemplate={rulesTemplate}
                poolType={poolType}
                step={1}
                onStepChange={this.onStepChange}
                isEdit={this.isEdit}
              />
            )}
          </PageWrapper>
        </PageBase.Container>
      );
    } else {
      return (
        <PageBase.Container className="channel putinpage-new">
          <PageWrapper>
            <FillDataSource
              submitLoading={submitLoading}
              onFirstPageChange={onFirstPageChange}
              data={data}
              isChannel={isChannel}
              showGrid={showGrid}
              step={0}
              onStepChange={this.onStepChange}
              getPoolType={getPoolType}
              setPoolType={setPoolType}
              cities={cities}
              onSubmit={this.onSubmit}
              showRulesSetting={this.showRulesSetting}
              poolType={poolType}
              resourceType={resourceType}
            />
            <Overlay visible={ruleSettingVisible} align="cc cc" hasMask>
              <div className="template-overlay">
                <Icon
                  type="close"
                  className="template-overlay-close"
                  size="small"
                  onClick={() => this.showRulesSetting(false)}
                />
                <RulesSetting
                  isChannel={isChannel}
                  showRulesSetting={this.showRulesSetting}
                  submitLoading={submitLoading}
                  updateRecallRules={updateRecallRules}
                  updateSecondPage={updateSecondPage}
                  updateRepeatRules={updateRepeatRules}
                  updateSortRules={updateSortRules}
                  updateDistributeRules={updateDistributeRules}
                  updateFilterRules={updateFilterRules}
                  updateTemplate={updateTemplate}
                  onSubmit={this.onSubmit}
                  onSubmitTemplate={this.onSubmitTemplate}
                  rules={rules}
                  rulesTemplate={rulesTemplate}
                  poolType={poolType}
                  step={1}
                  onStepChange={this.onStepChange}
                  resourceType={resourceType}
                  isEdit={this.isEdit}
                />
              </div>
            </Overlay>
          </PageWrapper>
        </PageBase.Container>
      );
    }
  }
}

export const LogTimePutInPage = logTimeComponent(
  PutInPageBase,
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const PutInPage = permissionAccess(
  LogTimePutInPage,
  async (activityId) => {
    return await ACLPermission(activityId);
  }
);
