import { takeLatest, call, put, select } from 'redux-saga/effects';
import { INIT_DATA, GET_POOL_TYPE } from './constants';
import { dataLoaded, setPoolType } from './actions';
import { getPoolIds, selectDataByKey,getWeightPoolIds } from './selectors';
import { getPoolType, onRequestError } from '@/utils/api'
import { makeCities, makeDeliveryData } from '@/containers/App/selectors'
import { getPutInDetail } from '../request'
import { formDetail } from '../constants';
/** saga worker */

function formSelectCities(cities, data) {
  const { firstPage: { selectedCitys } } =  data;

  return selectedCitys.reduce((values, id) => {
    const filterCity =  cities.filter((item) => item.value == id)[0]

    if(!filterCity) return values.concat(+id)

    if (filterCity.children){
      return values.concat(filterCity.children.map((child) => +child.value))
    }
  }, [])
}

/**
 * 获取详情初始数据的 request/respose handler
 */

export function* getInit({activityId}) {
  try {
    const detailData = yield call(getPutInDetail, activityId);
    const data = formDetail(detailData);
    const { firstPage } = data;

    const cities = yield select(makeCities());
    const deliveryData = yield select(makeDeliveryData());

    const selectedCitys = formSelectCities(cities, data);
    yield put(dataLoaded({ ...data, firstPage:{ ...firstPage, selectedCitys, ...deliveryData } }))

  } catch(error) {
    onRequestError(error)
  }
}

/**
 * 根据poolIds获取 维度类型
 */
export function* getConfigType() {
  const poolIds = yield select(getPoolIds());
  const rules = yield select(selectDataByKey('secondPage'));
  const storeSortRules = rules.get('storeSortRules');
  const skuSortRules = rules.get('skuSortRules');
  let params = [];
  if (poolIds && poolIds.length > 0) {
    poolIds.map((v) => {
      params.push({poolId: v})
    })
  }

  const weightPoolIds = yield select(getWeightPoolIds());
  if (weightPoolIds && weightPoolIds.length > 0) {
    weightPoolIds.map((v) => {
      params.push({poolId: v})
    })
  }

  // const params = [{ 'poolId': +poolIds }]
  if (params && params.length > 0) {
    try {
      const type = yield call(getPoolType, params);
      yield put(setPoolType({type, storeSortRules, skuSortRules}))

    } catch (error) {
      onRequestError(error)
    }
  }
  // const data = yield select(selectDataByKey('firstPage'));
  // const limitType = data.get('limitType');
  // const rules = yield select(selectDataByKey('secondPage'));
  // const storeSortRules = rules.get('storeSortRules');
  // const skuSortRules = rules.get('skuSortRules');
  // if(!limitType) {
  //   const poolIds = yield select(getPoolIds()) || [];
  //   let params = [];
  //   poolIds.map((v) => {
  //     params.push({poolId: v})
  //   })
  //   // const params = [{ 'poolId': +poolIds }]
  //   try {
  //     const type = yield call(getPoolType, params);
  //     yield put(setPoolType({type, storeSortRules, skuSortRules}))

  //   } catch(error) {
  //     onRequestError(error)
  //   }
  // }else if(limitType == 1){
  //   const poolType = yield select(selectDataByKey('poolType'));
  //   yield put(setPoolType({type: poolType, storeSortRules, skuSortRules}))
  // }
}

/**
 *
 */

/**
  * Root saga manages watcher lifecycle
  */
export default function* config() {
  yield takeLatest(INIT_DATA, getInit);
  yield takeLatest(GET_POOL_TYPE, getConfigType);
}



