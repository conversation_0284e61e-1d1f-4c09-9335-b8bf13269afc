import React from 'react';
import {Form, Field, Switch, Radio, Checkbox, CascaderSelect, NumberPicker} from '@alife/next';
import { Tip } from '@/components/Tip';
import { rulesSettingItemLayout, formLayout } from '../constants';
import { psoriasisPicFilterRules,  bodyAreaRatioRules } from '../../constants';
import { SettingBlock } from '@/components/BlockWrapper';
import {getActivityType} from "../../request";
import * as api from '@/utils/api';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const CheckboxGroup = Checkbox.Group;

const filterFormItemLayout = {
  ...rulesSettingItemLayout,
  labelCol: { span: 7 },
  wrapperCol: {
    span: 17
  }
}

export class FilterRules extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      showPic:false,
      activityTypeDataSource:[]
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        if(name=='minPriceFilter'){
          this.props.updateFilterRules("priceFilter", [value,this.field.getValue("maxPriceFilter")]);
        }else if(name=='maxPriceFilter'){
          this.props.updateFilterRules("priceFilter", [this.field.getValue("minPriceFilter"),value]);
        }else {
          this.props.updateFilterRules(name, value);
        }
      }
    });
  }

  componentWillReceiveProps(newProps){
    const formData = newProps.rules.toJS();
    this.field.setValues(formData);
  }

  componentDidMount() {
    const formData = this.props.rules.toJS();
    if (formData.priceFilter && formData.priceFilter.length>0) {
      this.field.setValue("minPriceFilter", formData.priceFilter[0]);
      this.field.setValue("maxPriceFilter", formData.priceFilter[1]);
    }
    this.field.setValues(formData);
    this.getActivityTypeList();
  }

  onPicChange = (value) =>{
    this.setState({
      showPic:value
    })
  }

  getActivityTypeList = async() =>{
    return getActivityType().then((data) => {
      console.log(data)
      this.setState({activityTypeDataSource:data.data});
    }).catch(api.onRequestError)
  }

  render() {
    const { getValue, id } = this.field;
    const { showPic } = this.state;
    const lockWhite = this.props.resourceType == 105;//大旋风资源位锁定白底图
    return (
      <SettingBlock title='过滤规则'>
        <Form field={ this.field } {...formLayout}>
          <FormItem label="商品总库存告罄过滤" { ...filterFormItemLayout } >
            <Switch checked={getValue('stockExhausted')} style={{ 'top': '7px'}} disabled/>
          </FormItem>
          <FormItem label="商品活动库存告罄过滤" { ...filterFormItemLayout } >
            <Switch checked={getValue('activityStockExhusted')} style={{ 'top': '7px'}} name='activityStockExhusted' disabled/>
            <Tip type='prompt' text='品牌招商池暂不支持，全量池开启该项数据会被过滤' />
          </FormItem>
          <FormItem label="无图商品过滤" { ...filterFormItemLayout } >
            <Switch checked={getValue('goodsWithoutPic')} style={{ 'top': '7px'}} disabled/>
          </FormItem>
          <FormItem label="无品牌商品过滤" { ...filterFormItemLayout } >
            <Switch checked={getValue('brandGoodsFilter')} name='brandGoodsFilter' style={{ 'top': '7px'}}/>
          </FormItem>
          <FormItem label="商品图片质量分过滤" { ...filterFormItemLayout } >
            <Switch checked = { lockWhite || showPic } disabled={lockWhite} onChange={this.onPicChange} style={{ 'top': '7px'}}/>
            <Tip type='prompt' text='默认收起,开启后可选择含牛皮癣图片商品' />
          </FormItem>
          <FormItem label="商品活动价格" { ...rulesSettingItemLayout }>
            <NumberPicker step={0.01} precision={0.01} max={getValue('maxPriceFilter')} name='minPriceFilter'/>
            &nbsp;&nbsp;~&nbsp;&nbsp;
            <NumberPicker step={0.01} precision={0.01} min={getValue('minPriceFilter')} name='maxPriceFilter'/>
            <span>&nbsp;元，最多输入两位小数</span>
          </FormItem>
          <FormItem label="营销活动商品筛选" { ...filterFormItemLayout } >
            <CascaderSelect style={{ width: '302px' }} name="mktActivityTypeList" multiple dataSource={this.state.activityTypeDataSource} />
          </FormItem>
          <FormItem label="营销标签商品筛选" { ...filterFormItemLayout } >
            <CascaderSelect style={{ width: '302px' }} name="mktActivityLabelList" multiple dataSource={[{ label: '爆好价', value: 'hardDiscount' }]} />
          </FormItem>
          <FormItem label="预售商品过滤" { ...filterFormItemLayout } >
            <Switch checked={getValue('presaleGoodsFilter')} name='presaleGoodsFilter' style={{ 'top': '7px'}}/>
            <Tip type='prompt' text='默认过滤预售商品，如需投放预售商品，请关闭此选项' />
          </FormItem>
          <div style={{marginLeft: '130px', display: `${(lockWhite || showPic) ? 'block' : 'none'}`}}>
            <FormItem label="含牛皮癣图片商品" {...filterFormItemLayout}>
              <CheckboxGroup name="goodsFilterWithPsoriasisPic" dataSource={psoriasisPicFilterRules}/>
            </FormItem>
            <FormItem label="是否透明底图" {...filterFormItemLayout} >
              <Switch checked={getValue('transparentBasePic')} style={{'top': '7px'}} name='transparentBasePic'/>
            </FormItem>
            <FormItem label="是否白底图" {...filterFormItemLayout} >
              <Switch checked={lockWhite || getValue('whiteBasePic')} disabled={lockWhite} style={{'top': '7px'}} name='whiteBasePic'/>
            </FormItem>
            <FormItem label="商品主体面积占比" {...filterFormItemLayout}>
              <RadioGroup name="goodsBodyAreaRatio" dataSource={bodyAreaRatioRules}/>
            </FormItem>
          </div>
        </Form>
      </SettingBlock>
    )
  }
}
