/**
 * RulesTemplateList
 * 规则模版列表
 */

import React, { Fragment } from 'react';
import { Form, Grid, Input, Button, Balloon } from '@alife/next';

import { manageList, setTableCol, filterForm } from '@/components/Table';
import { DetailList } from '../../DetailList'
import { getTemplateList } from '../../request';
import { defaultDetail } from '../../constants';
import { formatTimeStamp } from '@/utils/time'
import { onRequestError } from '@/utils/api'

const FormItem = Form.Item;
const { Row, Col } = Grid;

const Use = ({onClick}) => <span onClick={onClick}>使用</span>
const Preview = ({onClick}) => <span onClick={onClick}>预览</span>

const balloonAlign = ['lt', 'l', 'l', 'l','lb']

class OptionBar extends React.Component {
  constructor(props){
    super(props);

    this.state = {
      balloonVisible: false,
    }
  }

  useTemplate = () => {
    this.props.useTemplate();
  }

  render(){
    const preview = <Preview onClick={this.showBallon}/>
    const { index, recode, type } = this.props;

    return (
      <Fragment>
        <Use onClick={this.useTemplate}/>
        <Balloon trigger={preview}
          popupStyle={{maxWidth:770,width:770, height:500}}
          triggerType="click"
          shouldUpdatePosition
          needAdjust
          // align={balloonAlign[index]}
          align={'l'}
        >
          <DetailList className="preview-detail" detail={ {...defaultDetail, ...recode, poolType:type} }/>
        </Balloon>
      </Fragment>
    )
  }
}


const columns = (ctx)=>{
  return [
    { title:'模版名称', dataIndex: 'templateName' },
    { title:'创建人', dataIndex:'createUser' },
    { title:'创建时间', dataIndex:'createAt', cell: (value) => formatTimeStamp(value) },
    { title:'操作', cell:function(value, index, recode){
      const { type } = ctx.props;
      return(<OptionBar index={index} type={type} recode={recode} useTemplate={() => ctx.props.useTemplate(recode)}/>)
    }}
  ]
}

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'left',
  labelTextAlign: 'left'
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

function TemplateFilterForm({ field, searchData }) {
  return (
    <Form field={field} {...formLayout}>
      <Row>
        <Col>
          <FormItem label="模版名称" {...formItemLayout}>
            <Input placeholder="请输入模版名称" name="templateRuleName" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="创建人" {...formItemLayout}>
            <Input placeholder="请输入创建人" name="createrName" />
          </FormItem>
        </Col>
        <Col>
          <Button type="primary" onClick={searchData}>查询</Button>&nbsp;&nbsp;
          <Button type="normal">重置</Button>
        </Col>
      </Row>
    </Form>
  )
}

const TemplateForm = filterForm(TemplateFilterForm, ['templateRuleName', 'createrName'])
const TemplateTable = setTableCol(columns);

export const RulesTemplateList = manageList(TemplateTable, TemplateForm,
  function(data){
    const { query } = data;
    const params = { ...data, query:{ ...query, type: this.props.type } }
    return getTemplateList(params).catch(onRequestError)
  },{
    size:5
  }
)

