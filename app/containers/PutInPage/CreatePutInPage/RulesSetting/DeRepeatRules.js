import React from 'react';
import { Form, Switch, Field, NumberPicker } from '@alife/next';
import { SettingBlock } from '@/components/BlockWrapper';
import { formLayout, rulesSettingItemLayout } from '../constants';
import { Tip } from '@/components/Tip';

const FormItem = Form.Item;

const numberItemLayout = {
  ...rulesSettingItemLayout,
  labelCol: { span: 7 }
}

const Commodity = ({ field }) => {
  const { getValue } = field;
  return (
    <SettingBlock title='去重规则'>
      <Form {...formLayout} field={field}>
        <FormItem label="商品名称去重" { ...rulesSettingItemLayout } >
          <Switch checked={getValue('goodsNameRepeat')} style={{ 'top': '7px'}} name='goodsNameRepeat' />
        </FormItem>
        <FormItem label="商品三级类目去重" { ...rulesSettingItemLayout } >
          <Switch checked={getValue('thirdGoodsCategory')} style={{ 'top': '7px'}} name='thirdGoodsCategory' />
        </FormItem>
        <FormItem label="一个门店下商品数量限制" {...numberItemLayout} required requiredMessage='商品数量限制不能为空'>
          <NumberPicker step={1} precision={0} max={getValue('storeRepeatCommodityNum')} name='minStoreRepeatCommodityNum'/>
          &nbsp;&nbsp;~&nbsp;&nbsp;
          <NumberPicker step={1} precision={0} min={getValue('minStoreRepeatCommodityNum')} name='storeRepeatCommodityNum'/>
        </FormItem>
        <FormItem label="同品牌商品去重" { ...rulesSettingItemLayout } >
          <Switch checked={getValue('repeatWithBrand')} style={{ 'top': '7px'}} name='repeatWithBrand' />
        </FormItem>
        {/* {<FormItem label="商品名称去重" { ...rulesSettingItemLayout }>
          <Switch checked={getValue('goodsNameRepeat')} style={{ 'top': '7px'}} name='goodsNameRepeat' />
        </FormItem>} */}
        <FormItem label="同品牌折叠" { ...rulesSettingItemLayout } >
          <Switch checked={false} style={{ 'top': '7px'}} name='foldingWithBrand' disabled/>
          <Tip type='prompt' text='开启后同一品牌下的其他门店折叠, 暂不生效' />
        </FormItem>
        <FormItem label="同商品去重" { ...rulesSettingItemLayout } >
          <Switch checked={getValue('upcAggregation')} style={{ 'top': '7px'}} name='upcAggregation' />
          <Tip type='prompt' text='通过商品同品归一和UPC识别相同商品' />
        </FormItem>
        {getValue('upcAggregation') && <FormItem label="同一个商品最大可召回数量" {...rulesSettingItemLayout} >
          <NumberPicker
            step={1}
            precision={0}
            max={10}
            min={1}
            name='upcRecallCount'
          />
        </FormItem>}
      </Form>
    </SettingBlock>
  )
}

const Store = ({ field }) => {
  const { getValue } = field;
  return (
    <SettingBlock title='去重规则'>
      <Form {...formLayout} field={field}>
        <FormItem label="同品牌折叠" { ...rulesSettingItemLayout } >
          <Switch checked={getValue('foldingWithBrand')} style={{ 'top': '7px'}} name='foldingWithBrand' />
          <Tip type='prompt' text='开启后同一品牌下的其他门店折叠' />
        </FormItem>
      </Form>
    </SettingBlock>
  )
}

function composeRepeatRules(Component) {
  return class DeRepeatRules extends React.Component {
    constructor(props){
      super(props);

      this.field = new Field(this, { onChange:(name, value) => {
        console.log(name,value);
        this.props.updateRepeatRules(name, value);
      },
      autoUnmount: false
      });
    }

    getField() {
      return this.field;
    }

    renderData(formData){
      const fieldValue = Object.keys(formData).reduce((field, key) => {
        return {...field, [key]:formData[key]}
      }, {})

      this.field.setValues(fieldValue)
    }

    componentWillReceiveProps(newProps){
      const formData = newProps.rules.toJS();
      this.renderData(formData);
    }

    componentDidMount() {
      const formData = this.props.rules.toJS();
      this.renderData(formData);
    }



    render(){
      return (
        <Component field={this.field}/>
      )
    }
  }
}

export const StoreRepeatRules = composeRepeatRules(Store);
export const CommodityRepeatRules = composeRepeatRules(Commodity);
