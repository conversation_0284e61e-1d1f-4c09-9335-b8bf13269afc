import React from 'react';
import { Form, Field, NumberPicker, Checkbox, Input, Select, CascaderSelect } from '@alife/next';

import { rulesSettingItemLayout, formLayout } from '../constants';
import { composeSelector } from '@/components/CascaderSelect';
import { SettingBlock } from '@/components/BlockWrapper';
import { switcher, branch } from '@/utils/switcher';
import { getStoreCategory, onRequestError, ali, queryMarketingType, queryStoreMajorCategory, queryEatUnionTag, queryBrand } from '@/utils/api';
import { openStatusRadio } from '../../constants';
import { Tip } from '@/components/Tip';

const FormItem = Form.Item;

let searchTimeout;

/**获取门店分类函数 */
function fetchStore() {
  return getStoreCategory().then((res) => res).catch(onRequestError)
}
// 获取门店经营类型
function getQueryStoreMajorCategory() {
  return queryStoreMajorCategory().then((res) => {
    return res
  }).catch(onRequestError)
}


function fetchSkuCategory() {
  return ali.getCategorySku(false).then((res) => {
    if (res.status == 200) {
      return res.data.data;
    } else {
      return {}
    }
  }).catch(onRequestError)
}


function fetchQueryEatUnionTag() {
  return queryEatUnionTag().then((res) => res).catch(onRequestError)
}





function fetchMarketingType() {
  return queryMarketingType().then(res => {
    if (res.status == 200) {
      return res.data.data;
    } else {
      return {}
    }
  }).catch(onRequestError)
}


/**门店分类选择组件 */
const StoreSelector = composeSelector(fetchStore)
const SkuCategorySelector = composeSelector(fetchSkuCategory);
// const MarketingTypeSelector = composeSelector(fetchMarketingType);
const StoreCategory = composeSelector(getQueryStoreMajorCategory);
// const QueryEatUnionTagCompose = composeSelector(fetchQueryEatUnionTag)

export class RecallRules extends React.Component {
  constructor(props) {

    super(props);
    this.field = new Field(this, {
      onChange: (name, value) => {
        this.props.updateRecallRules(name, value);
      }
    });
    this.state = {
      foodAlliance: [],
      majorCategory: [],
      commodityBrand: {},
      shopBrands:""
    }
  }

  renderData(formData) {
    const formalField = switcher(
      branch('storeDistance',
        () => {
          const [minStoreDistance, maxStoreDistance] = formData['storeDistance']
          return { minStoreDistance, maxStoreDistance }
        }),
      branch('shopScore',
        () => {
          const [minShopScore, maxShopScore] = formData['shopScore']
          return { minShopScore, maxShopScore }
        }),

      // branch('goodsOriginalPrice',
      //   () => {
      //     const [minGoodsOriginalPrice, maxGoodsOriginalPrice] = formData['goodsOriginalPrice'] || [];
      //     return { minGoodsOriginalPrice, maxGoodsOriginalPrice }
      //   }),
      // branch('goodsPresentPrice',
      //   () => {
      //     const [minGoodsPresentPrice, maxGoodsPresentPrice] = formData['goodsPresentPrice'] || [];
      //     return { minGoodsPresentPrice, maxGoodsPresentPrice }
      //   }),
      // branch('skuDiscount',
      //   () => {
      //     const [minSkuDiscount, maxSkuDiscount] = formData['skuDiscount'] || [];
      //     return { minSkuDiscount, maxSkuDiscount }
      //   }),
      branch(() => true,
        (type) => {
          return { [type]: formData[type] }
        }),
    )

    let fieldValue = Object.keys(formData).reduce((field, key) => {
      return { ...field, ...formalField(key) }
    }, {})
    this.field.setValues(fieldValue);
  }

  componentWillReceiveProps(newProps) {
    const formData = newProps.rules.toJS();

    this.renderData(formData);
  }

  async componentDidMount() {
    const formData = this.props.rules.toJS();
    this.renderData(formData);
    // 除了编辑，如果这两个值都没有，则需要对配送距离进行初始化
    if(!this.props.isEdit){
      if (typeof(this.field.values.minStoreDistance)=="undefined") {
        this.props.updateRecallRules("minStoreDistance",0)
      }
      if (typeof(this.field.values.maxStoreDistance)=="undefined") {
        this.props.updateRecallRules("maxStoreDistance",5000)
      }
    }
    const data = await fetchQueryEatUnionTag().then()
    const foodAlliance = data.map((x) => {
      return {
        value: x.code,
        label: x.name
      }
    })
    const res = await getQueryStoreMajorCategory();
    const majorCategory = res.map((x) => {
      return {
        value: x.code,
        label: x.name
      }
    })

    this.setState({
      foodAlliance,
      majorCategory
    })

  }
  handleChange = (value, actionType, item, key) => {
    this.props.updateRecallRules(key, value);
  }
  // 模块搜索
  handleSearch = (keyword,key)=>{
    let commodityBrand = this.state.commodityBrand;
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      if (keyword) {
        queryBrand({searchKey: keyword}).then((data) => {
          const dataSource = data.map(item => ({
            label: item.brandName, 
            value: item.brandId
          }));
          const originData = commodityBrand[key] ? commodityBrand[key] : [];
          const wholeData = dataSource.concat(originData);
          commodityBrand[key] = Array.from(new Set(wholeData))
          this.setState({commodityBrand:commodityBrand})
        })
      } else {
        this.setState({commodityBrand:this.state.commodityBrand})
      }
    }, 800);
  }
  render() {
    const { getValue, init } = this.field;
    const { poolType } = this.props;
    const { commodityBrand = {} } = this.state;
    // 由于商品品牌没办法进行全部查询，所以要使用服务端透传的字段
    const commodityBrands = {...init('itemBrandListContent')}.value || [];
    let newCommodityBrands = commodityBrands.map((x) => {return {value: x.code,label: x.name}})
    if (commodityBrand["itemBrandList"] && commodityBrand["itemBrandList"].length > 0) {
      newCommodityBrands = [...newCommodityBrands,...commodityBrand["itemBrandList"]]
    }
    return (
      <SettingBlock title='召回规则'>
        <Form field={this.field} {...formLayout}>
          <FormItem label="门店分类" {...rulesSettingItemLayout}>
            <StoreSelector name="storeTypeRep" {...init('storeTypeRep')} />
          </FormItem >
          <FormItem label="店铺距离" {...rulesSettingItemLayout}>
            <NumberPicker defaultValue={0} step={0.1} precision={1} max={getValue('maxStoreDistance') - 1} name='minStoreDistance' />
            &nbsp;&nbsp;~&nbsp;&nbsp;
            <NumberPicker defaultValue={5000} step={0.1} precision={1} min={getValue('minStoreDistance') + 1} name='maxStoreDistance' />
            <span>&nbsp;米，最多输入一位小数，最大支持40km</span>
          </FormItem>
          <FormItem label=" " {...rulesSettingItemLayout}>
            <Checkbox name="isNationalDelivery">召回快递门店</Checkbox>
            <Tip type='prompt' text='开启后召回快递门店，不开启则不召回' />
          </FormItem>
          {/*<FormItem label="最低数量" {...rulesSettingItemLayout}>*/}
          {/*  <NumberPicker defaultValue={1} step={1} precision={0} name='minNum' />*/}
          {/*  <span>&nbsp;个</span>*/}
          {/*</FormItem>*/}
          <FormItem label="营业状态" {...rulesSettingItemLayout}>
            <Checkbox.Group name='openStatusSet'>
              {openStatusRadio.map((item, index) => <Checkbox key={index} value={item.value}>{item.label}</Checkbox>)}
            </Checkbox.Group>
          </FormItem>
          {/* {poolType == 1 && <FormItem label='商品名称' {...rulesSettingItemLayout} maxLength={30} minmaxLengthMessage="不可超过30个字符">
            <Input
              placeholder='请输入商品名称关键词，以逗号分隔'
              name='goodsNameKeyWord'
            />
          </FormItem>} */}
          {poolType == 1 && <FormItem label="商品类目" {...rulesSettingItemLayout}>
            <SkuCategorySelector name="goodsCategory" {...init('goodsCategory')} />
          </FormItem >}
          {/* {poolType == 1 &&<FormItem label="商品活动类型" {...rulesSettingItemLayout}>
            <MarketingTypeSelector name="goodsActivitiesType" {...init('goodsActivitiesType')} />
          </FormItem >}
          {poolType == 1 && <FormItem label="原价区间" {...rulesSettingItemLayout}>
            <NumberPicker step={0.01} precision={2} name='minGoodsOriginalPrice' />
              &nbsp;&nbsp;~&nbsp;&nbsp;
            <NumberPicker step={0.01} precision={2} name='maxGoodsOriginalPrice' />
            <span>&nbsp;元，最多输入两位小数</span>
          </FormItem>}
          {poolType == 1 && <FormItem label="现价区间" {...rulesSettingItemLayout}>
            <NumberPicker step={0.01} precision={2} name='minGoodsPresentPrice' />
              &nbsp;&nbsp;~&nbsp;&nbsp;
            <NumberPicker step={0.01} precision={2} name='maxGoodsPresentPrice' />
            <span>&nbsp;元，最多输入两位小数</span>
          </FormItem>}
          {poolType == 1 && <FormItem label="折扣区间" {...rulesSettingItemLayout}>
            <NumberPicker step={0.01} precision={2} name='minSkuDiscount' />
              &nbsp;&nbsp;~&nbsp;&nbsp;
            <NumberPicker step={0.01} precision={2} name='maxSkuDiscount' />
            <span>&nbsp;折，最多输入两位小数</span>
          </FormItem>} */}
          <FormItem label="商家主营类目" {...rulesSettingItemLayout}>
            <Select
              mode="multiple"
              // defaultValue={["10001"]}
              {...init('shopMajorCategory')}
              onChange={(value, actionType, item) => this.handleChange(value, actionType, item, 'shopMajorCategory')}
              dataSource={this.state.majorCategory}
              style={{ width: 300, marginRight: 8 }}
            />
          </FormItem>
          <FormItem label="店铺评分" {...rulesSettingItemLayout}>
            <NumberPicker step={0.1} precision={1} max={getValue('maxShopScore') - 1} name='minShopScore' />
            &nbsp;&nbsp;-&nbsp;&nbsp;
            <NumberPicker step={0.1} precision={1} min={getValue('minShopScore') + 1} name='maxShopScore' />
            <span>&nbsp;</span>
          </FormItem>
          <FormItem label="吃货联盟标" {...rulesSettingItemLayout}>
            <Select
              mode="multiple"
              {...init('eatUnionTag')}
              onChange={(value, actionType, item) => this.handleChange(value, actionType, item, 'eatUnionTag')}
              dataSource={this.state.foodAlliance}
              style={{ width: 300, marginRight: 8 }}
            />
          </FormItem>
          {poolType == 1 && <FormItem label="商品所属品牌" {...rulesSettingItemLayout}>
            <CascaderSelect
              placeholder="请输入商品所属品牌，支持输入最多5个"
              notFoundContent=""
              showSearch 
              multiple={true}
              {...init('itemBrandList')}
              onChange={(value, actionType, item) => this.handleChange(value, actionType, item, 'itemBrandList')}
              dataSource={newCommodityBrands}
              onSearch={(value) => this.handleSearch(value,'itemBrandList')} style={{ width: 300, marginRight: 8 }}
            />
          </FormItem>}
          <FormItem label="门店所属品牌" {...rulesSettingItemLayout}>
            <Input.TextArea 
              placeholder={"英文逗号隔开，最多5个"}
              {...init('shopBrands')}
              onChange={(value, actionType, item) => {this.handleChange(value, actionType, item, 'shopBrands') }}
            />
          </FormItem>
        </Form>
      </SettingBlock>
    )
  }
}
