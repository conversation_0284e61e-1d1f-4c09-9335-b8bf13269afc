import React from 'react';
import { Form, Field, NumberPicker, Switch } from '@alife/next';

import { rulesSettingItemLayout, formLayout } from '../constants';
import { SettingBlock } from '@/components/BlockWrapper';
import { Tip } from '@/components/Tip';
import { switcher, branch } from '@/utils/switcher';

const FormItem = Form.Item;

export class DistributeRules extends React.Component {
  constructor(props) {
    super(props)

    this.field = new Field(this, {
      onChange: (name, value) => {
        this.props.updateDistributeRules(name, value);
      }
    });
  }

  renderData(formData){
    const formalField = switcher(
      branch('distributionFee',
        () => {
          const [ minDistribution, maxDistribution ] = formData['distributionFee']
          return { minDistribution, maxDistribution }
        }),
      branch('distributionFullDecrementAmount',
        () => {
          const [minThreshold, maxThreshold] = formData['distributionFullDecrementAmount']
          return {minThreshold, maxThreshold}
        }),
      branch(() => true,
        (type) => {
          return { [type]: formData[type] }
        }),
    )

    const fieldValue = Object.keys(formData).reduce((field, key) => {
      return {...field, ...formalField(key)}
    }, {})

    this.field.setValues(fieldValue);
  }

  componentWillReceiveProps(newProps){
    const formData = newProps.rules.toJS();
    this.renderData(formData);
  }

  componentDidMount(){
    const formData = this.props.rules.toJS();
    this.renderData(formData);
  }

  render() {
    const { getValue } = this.field;
    return (
      <SettingBlock title='配送规则'>
        <Form field={ this.field } {...formLayout}>
          <FormItem label="配送费" { ...rulesSettingItemLayout }>
            <NumberPicker step={0.1} precision={1} max={getValue('maxDistribution')} name='minDistribution'/>
              &nbsp;&nbsp;~&nbsp;&nbsp;
            <NumberPicker step={0.1} precision={1} min={getValue('minDistribution')} name='maxDistribution'/>
            <span>&nbsp;元，最多输入一位小数</span>
          </FormItem>
          <FormItem label="配送满减门槛金额" { ...rulesSettingItemLayout }>
            <NumberPicker step={0.1} precision={1} max={getValue('maxThreshold')} name='minThreshold'/>
            &nbsp;&nbsp;~&nbsp;&nbsp;
            <NumberPicker step={0.1} precision={1} min={getValue('minThreshold')} name='maxThreshold'/>
            <span>&nbsp;元，最多输入一位小数</span>
          </FormItem>
          <FormItem label="蜂鸟专送" { ...rulesSettingItemLayout } >
            <Switch checked={getValue('hummingBird')} style={{ 'top': '7px'}} name='hummingBird' />
            <Tip type='prompt' text='开启后默认蜂鸟专送' />
          </FormItem>
          <FormItem label="准时达" { ...rulesSettingItemLayout } >
            <Switch checked={getValue('onTime')} style={{ 'top': '7px'}} name='onTime' />
            <Tip type='prompt' text='开启后默认准时达' />
          </FormItem>
          {/* <FormItem label="免配店铺" { ...rulesSettingItemLayout } >
            <Switch checked={getValue('freeDeliveryStore')} style={{ 'top': '7px'}} name='freeDeliveryStore' />
          </FormItem> */}
        </Form>
      </SettingBlock>
    )
  }
}
