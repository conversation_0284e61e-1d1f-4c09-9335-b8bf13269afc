import React from 'react';
import { Radio, Field, Switch, Message, Select } from '@alife/next';
import { SettingBlock } from '@/components/BlockWrapper';
import { commodityRankRules,  storeRankRules , discountRankFactorRules, scatterCategory } from '../../constants';
import { Tip } from '@/components/Tip';
import { composeSelector } from '@/components/CascaderSelect';
import { onRequestError, ali } from '@/utils/api';
const Option = Select.Option;
import {actRankChartEum} from "@/containers/ActivityEffectPage/common";

const RadioGroup = Radio.Group;

function fetchSpecialSkuCategory() {
  return ali.getCategorySku(false).then((res) => {
    if (res.status == 200) {
      // 只能选二级类目
      let categoryList = res.data.data.slice(0);
      categoryList.forEach(firstLevel => {
        firstLevel.checkboxDisabled = true;
        if(firstLevel.children) {
          firstLevel.children.forEach(secondLevel => {
            if(secondLevel.children) {
              secondLevel.children.forEach(thirdLevel => {
                thirdLevel.checkboxDisabled = true;
              })
            }
          })
        }
      })
      return categoryList;
    } else {
      return {}
    }
  }).catch(onRequestError)
}
// 只能选二级类目
const SpecialSkuCategorySelector = composeSelector(fetchSpecialSkuCategory);

function composeRankRules(Component) {
  return class RankRules extends React.Component {
    constructor(props) {
      super(props);
      this.state = {
        sortRules: {}
      }

      this.field = new Field(this, {
        onChange: (name, value) => {
          let sortRules = JSON.parse(JSON.stringify(this.state.sortRules));
          if (name == 'discountRankFactor' || name == 'categoryScatter' || name == 'shopWeightingType') {
            sortRules[name] = +value;
            if (name == 'discountRankFactor' && value == 1) {
              sortRules.categoryScatter = 1;
              this.props.updateSortRules('sortRules', sortRules);
            } else {
              this.props.updateSortRules('sortRules', sortRules);
            }
            this.setState({sortRules});
          } else if (name == 'skuSortRules') {
            let discountRankFactorValue;
            let categoryScatterValue;
            // 当更换的内容是4569时需要置空UPC
            let upcScatterValue;
            switch (value) {
              case 1:
                upcScatterValue = 1;
                break;
              case 2:
                upcScatterValue = 1;
                break;
              case 8:
              case 10:
                // categoryScatterValue = this.state.sortRules.categoryScatter,
                categoryScatterValue = 1;
                discountRankFactorValue = 0;
                upcScatterValue = 1;
                break;
              case 3:
                // discountRankFactorValue = this.state.sortRules.discountRankFactor;
                discountRankFactorValue = 0;
                categoryScatterValue = 1;
                upcScatterValue = 1;
                break;
              case 4:
                upcScatterValue = 0;
                break;
              case 5:
                upcScatterValue = 1;
                break;
              case 6:
                upcScatterValue = 0;
                discountRankFactorValue = 0;
                categoryScatterValue = 0;
                break;
              case 9:
                upcScatterValue = 0;
                break;
              case 11:
                categoryScatterValue = 1;
                upcScatterValue = 1;
                break;
              case 12:
                categoryScatterValue = 1;
                upcScatterValue = 1;
                break;
              default:
                break;
            }
            sortRules.discountRankFactor =  discountRankFactorValue;
            sortRules.categoryScatter = categoryScatterValue;
            sortRules.upcScatter = upcScatterValue;
            this.props.updateSortRules('sortRules', sortRules);
            this.props.updateSortRules(name, value);
            this.setState({sortRules});
          }else if(name=='storeSortRules'){
            let shopWeightingTypeValue;
            switch (value) {
              case 4:
                shopWeightingTypeValue = this.state.sortRules.shopWeightingType;
                break;
              case 5:
              case 6:
                shopWeightingTypeValue = 0;
                break;
              default:
                break;
            }
            sortRules.shopWeightingType = shopWeightingTypeValue;
            this.props.updateSortRules('sortRules',sortRules);
            this.props.updateSortRules(name, value);
          } else if (name == 'goodsCategoryList') {
            if(value.length > 20) {
              sortRules.goodsCategoryList = value.slice(0, value.length-1);
              Message.show({
                type: 'warning',
                content: '只能选择20个二级类目'
              });
            } else {
              sortRules.goodsCategoryList = value;
            }
            this.props.updateSortRules('sortRules', sortRules);
          }else if (name == 'upcScatter') {
            sortRules.upcScatter = value ? 1 : 0;
            this.props.updateSortRules('sortRules', sortRules);
            this.setState({sortRules});
          }else if (name == 'catScatterLevel') {
            sortRules.catScatterLevel = value
            this.props.updateSortRules('sortRules', sortRules);
            this.setState({sortRules});
          }else {
            this.props.updateSortRules(name, value);
          }
        }
      });
    }

    renderData(formData){
      this.field.setValues(formData);
      for (var o in formData.sortRules) {
        this.field.setValue(o, formData.sortRules[o] ? formData.sortRules[o] : 0)
      }
    }

    componentWillReceiveProps(newProps){
      const formData = newProps.value;
      this.renderData(formData)
    }

    componentDidMount(){
      const formData = this.props.value;
      this.setState({
        sortRules: formData.sortRules
      });
      this.renderData(formData)
    }

    render(){
      return (
        <Component field={this.field} />
      )
    }
  }
}



export const Commodity = ({ field }) => {
  const { init } = field;
  let skuSortRulesValue = init('skuSortRules').value;
  let categoryScatterValue = false;
  let upcScatterValue = false;
  let catScatterLevelValue =  '';
  // let brandShopScatterValue = false;
  // let levelCategoryScatterValue = false;
  /**
   * 每次更改排序规则之后要根据当前选择的内容进行修改。
   * 如：商品--一开始选择的是商品销量排序，但切换成了门店距离排序（不支持UPC打散和商品品类打散）则需要把他们置空
   */
  if(init('sortRules').value) { //后续换成单选框，更改此种方式
    categoryScatterValue = (init('sortRules').value.categoryScatter == 1);
    upcScatterValue = (init('sortRules').value.upcScatter == 1);
    catScatterLevelValue = (init('sortRules').value.catScatterLevel) ? init('sortRules').value.catScatterLevel : '';
    // brandShopScatterValue = (init('sortRules').value.brandShopScatter == 1);
    // levelCategoryScatterValue = (init('sortRules').value.levelCategoryScatter == 1);
  }
  const discountRankFactor = field.getValue('discountRankFactor');
  let upcScatterDisabled = [4,9,5,6].includes(skuSortRulesValue);
  let categoryScatterDisabled = [1,2,11,12].includes(skuSortRulesValue);
  let showCatScatterLevel = [1,2,11,12,3,10].includes(skuSortRulesValue);
  return (
    <SettingBlock title="排序规则" className='rank-rules-bg'>
      <div className='item'>
        <span>基础规则 </span>
        {/*<RadioGroup dataSource={commodityRankRules} {...init('skuSortRules')} name='skuSortRules'/>*/}
        <RadioGroup {...init('skuSortRules')} name='skuSortRules'>
          {commodityRankRules.map((v) => {
            if (v.value==7){
              return <Radio value={v.value} disabled>{v.label}<Tip type='prompt' text='该排序只对时令好货场景生效，非相关运营勿选' /></Radio>;
            } else {
              return <Radio value={v.value}>{v.label}</Radio>
            }
          })}
        </RadioGroup>
      </div>
      <div className='item'><span>优惠排序因子 </span>
        <RadioGroup {...init('discountRankFactor')} name='discountRankFactor'>
          {discountRankFactorRules.map((v) => {
            let disabled =  (v.value==1 && !(skuSortRulesValue ==3) || v.value==2 && !(skuSortRulesValue == 4));
            return <Radio value={v.value} disabled={disabled}>{v.label}</Radio>
          })}
        </RadioGroup>
      </div>
      <div className='item'>
        <span>商品品类打散 </span>
        <Switch checked={categoryScatterValue} {...init('categoryScatter')} name='categoryScatter' disabled={!categoryScatterDisabled}/>
        {(showCatScatterLevel && categoryScatterValue) && <Select name="catScatterLevel" value={catScatterLevelValue} {...init('catScatterLevel')} style={{marginLeft: '5px'}}>
          {scatterCategory.map((v) => {
            return <Option value={v.value}>{v.label}</Option>
          })}
        </Select>}
      </div>
      {/* <div className='item'><span>同品牌店铺打散 </span><Switch checked={brandShopScatterValue} {...init('brandShopScatter')} name='brandShopScatter' /></div>
      <div className='item'><span>三级类目打散 </span><Switch checked={levelCategoryScatterValue} {...init('levelCategoryScatter')} name='levelCategoryScatter' /></div> */}
      {discountRankFactor === 3 && <div className='item'><span>商品类目</span>
        <SpecialSkuCategorySelector name="goodsCategoryList" {...init('goodsCategoryList')} />
      </div >}
      <div className="item">
        <span>UPC打散</span>
        <Switch checked={upcScatterValue} disabled={upcScatterDisabled} {...init('upcScatter')} name='upcScatter'/>
      </div>
    </SettingBlock>
  )
}

export const Store = ({ field }) => {
  const { init } = field;
  let storeSortRulesValue = init('storeSortRules').value;
  let shopWeightingTypeValue = false;
  if(init('sortRules').value) { //后续换成单选框，更改此种方式
    shopWeightingTypeValue = (init('sortRules').value.shopWeightingType == 1);
  }
  return (
    <SettingBlock title="排序规则" className='rank-rules-bg'>
      <div className='item'><span>基础规则 </span><RadioGroup dataSource={storeRankRules} {...init('storeSortRules')} name='storeSortRules' /></div>
      <div className='item'><span>优惠排序因子 </span><Switch checked={shopWeightingTypeValue} {...init('shopWeightingType')}  name='shopWeightingType' disabled={!(storeSortRulesValue==4||storeSortRulesValue==12)}/></div>
    </SettingBlock>
  )
}

export const CommodityRankRules = composeRankRules(Commodity);
export const StoreRankRules = composeRankRules(Store);
