@import '../../../../styles/common.scss';

.putIn-rules-setting{
  width: 870px;
  margin: 20px auto;

  .setting-block {
    border: 1px solid #EBEBEB;
    padding: 20px 30px;
    color: $font_main_light;

    .block-item {
      padding: 10px 0px 32px 0px;
    }
  }

  .rank-rules-bg {
    background-color: white;
    .next-radio-group .next-radio-label  {
      color: $font_main_light !important;
    }
    .item{
      display:flex;
      margin-bottom:10px;
      &>.next-radio-group{
        width:490px;
        &>.next-radio-wrapper{
          float: left;
          margin-right:20px;
        }
      }
      &>span{
        width:140px;
        display:block;
      }
    }
  }

  .button-bar {
    margin-top: 20px;
  }
}

.template-overlay {
  background-color: white;
  padding: 10px 20px 20px 20px;
  width: 900px;
  height: 500px;

  .template-overlay-close {
    float: right;
    width: 10px;
  }
}

.template-name {
  width: 100% !important;
}

.next-balloon {
  max-width: 600px;
}
