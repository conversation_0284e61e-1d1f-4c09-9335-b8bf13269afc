import React, { Fragment } from 'react';
import { Grid, Button, Overlay, Icon, Input, Dialog, Form, Field } from '@alife/next';

import { PageSection } from '@/components/PageWrapper'
import { DialogBtn } from '@/components/Button';
import { RecallRules } from './RecallRules';
import { FilterRules } from './FilterRules';
import { CommodityRepeatRules, StoreRepeatRules } from './DeRepeatRules';
import { CommodityRankRules, StoreRankRules } from './RankRules';
import { doesFormHasErrors } from '@/utils/formTool';
import { RulesTemplateList } from './RulesTemplateList';
import { DistributeRules } from './DistributeRules';
import { track } from '@/utils/aplus';
import { formSecond } from '../../constants';

import './style.scss';
const { Row } = Grid;
const FormItem  = Form.Item;

/** Overlay 样式设置 */
const dialogProps = {
  title:"设置模版名称",
  autoFocus: true,
  cancelProps: { 'aria-label': '取消' },
  okProps: { 'aria-label': '确认' },
  style:{width: '400px'}
}

/** Dialog 样式设置 */
const templateNameInput = {
  placeholder: "请输入模版名称",
  maxLength: 30,
  rows:2,
  hasLimitHint:true
}


export class RulesSetting extends React.Component {
  constructor(props){
    super(props);

    this.state = {
      templateVisible: false,
      setVisible: false
    }

    this.field = new Field(this);
  }
  /**上一步按钮操作 */
  onLastChange = () => {

    this.props.onStepChange(this.props.step - 1);
  }

  /**完成按钮点击事件 */
  onSubmit = () => {
    track('clickEvent', ['/selection_kunlun.PUTIN-MANAGE-create-putin-activity.submit-btn'])
    this.repeatRulesForm.getField().validate((errors) => {
      if(!doesFormHasErrors(errors)){
        this.props.onSubmit();
        // console.log(this.props.rules.toJS());
      }
    })
  }

  /**使用投放规则 */
  onOpenTemplate = () => {
    //goldlog: PUTIN-NEW
    track('clickEvent', ['/selection_kunlun.PUTIN-NEW.putIn-new-useTemplate'])

    this.setState({
      templateVisible: true,
    })
  }

  /**关闭规则模版弹层 */
  onCloseTemplate = () => {

    this.setState({
      templateVisible: false,
    })
  }

  switchSetDialog = (isOpen) => {
    this.setState({ setVisible: isOpen })
  }
  /**
   * 保存为规则模版
   */
  saveTemplate = () => {
    //goldlog: PUTIN-NEW
    track('clickEvent', ['/selection_kunlun.PUTIN-NEW.putIn-new-saveTemplate'])

    this.switchSetDialog(true);
  }

  /**
   * 确认模版名字
   */
  onSubmitTemplate = async () => {
    const templateName = this.field.getValue('templateName')
    await this.props.onSubmitTemplate({templateName})

    this.switchSetDialog(false);
  }

  /**
   * 更新模版
   */
  onUpdateTemplate = async () => {
    const templateId = this.props.rulesTemplate.toJS()['id']
    const { templateName } = this.props.rulesTemplate.toJS()

    await this.props.onSubmitTemplate( {id: templateId, templateName} )
  }

  /**
   * 取消设置模版
   */
  onCancelTemplate = () => {
    this.switchSetDialog(false);
  }

  /**
   * 使用规则模版
   */
  useTemplate = (recode) => {
    const { id, templateName } = recode;
    const { updateTemplate, rules } = this.props;
    const defaultData  = rules.toJS();
    const secondPage = formSecond(recode);
    updateTemplate('id', id);
    updateTemplate('templateName', templateName);
    this.props.updateSecondPage({ ...defaultData, ...secondPage });
    this.onCloseTemplate();
  }

  render() {
    const {
      updateRecallRules,
      updateRepeatRules,
      rules,
      updateSortRules,
      poolType,
      updateDistributeRules,
      updateFilterRules,
      rulesTemplate,
      submitLoading,
      data,
      resourceType,
      isEdit
    } = this.props;
    const {templateVisible, setVisible} = this.state;
    const template = rulesTemplate.toJS()
    const { templateName } = template
    const templateId = template['id'];
    return (
      <Fragment>
        <PageSection title='投放规则' className='putIn-rules-setting'>
          <section className="setting-block">
            <Row className="block-item">
              <Button onClick={this.onOpenTemplate}>使用投放规则</Button>
            </Row>
            <RecallRules
              updateRecallRules={updateRecallRules}
              rules={rules.get('recallRules')}
              poolType={poolType}
              isEdit={isEdit}
            />
            <DistributeRules
              updateDistributeRules={updateDistributeRules}
              rules={rules.get('deliveryRules')}
            />
            { poolType == 1 &&
              <FilterRules
                updateFilterRules={updateFilterRules}
                rules={rules.get('filterRules')}
                resourceType={resourceType}
              />
            }

            { poolType == 1 ?
              <CommodityRepeatRules
                updateRepeatRules={updateRepeatRules}
                rules={rules.get('repeatRules')}
                ref={(ref) => this.repeatRulesForm = ref}
              /> :
              <StoreRepeatRules
                updateRepeatRules={updateRepeatRules}
                rules={rules.get('repeatRules')}
                ref={(ref) => this.repeatRulesForm = ref}
              />
            }
            {poolType == 1 ?
              <CommodityRankRules updateSortRules={updateSortRules} value={{
                skuSortRules: rules.get('skuSortRules'),
                sortRules: JSON.parse(JSON.stringify(rules.get('sortRules')))
              }}/> :
              <StoreRankRules updateSortRules={updateSortRules} value={{
                storeSortRules: rules.get('storeSortRules'),
                sortRules: JSON.parse(JSON.stringify(rules.get('sortRules')))
              }}/>
            }
          </section>
          <section className="setting-block">
            <Button onClick={this.saveTemplate}>保存为规则模版</Button>&nbsp;&nbsp;
            {!!templateId &&
              <DialogBtn onOk={this.onUpdateTemplate} content={`确定要更新名称为${templateName}的模版?`}>
                <Button>更新模版</Button>
              </DialogBtn>
            }
          </section>
          <section className="button-bar">
            {!this.props.isChannel ? [<Button onClick={this.onLastChange} style={{marginRight:'10px'}}>上一步</Button>,
              <Button type='primary' onClick={this.onSubmit} loading={submitLoading}>完成</Button>]
              : <Button type='primary' onClick={() => this.props.showRulesSetting(false)} loading={submitLoading}>保存</Button>}
          </section>
        </PageSection>
        <Overlay visible={templateVisible} align="cc cc" hasMask >
          <div className="template-overlay">
            <Icon type="close" className="template-overlay-close" size="small" onClick={this.onCloseTemplate} />
            <RulesTemplateList type={poolType} useTemplate={this.useTemplate}/>
          </div>
        </Overlay>
        <Dialog
          {...dialogProps}
          visible={setVisible}
          onOk={this.onSubmitTemplate}
          onCancel={this.onCancelTemplate}
          onClose={this.onCancelTemplate}>
          <Form field={this.field}>
            <FormItem><Input.TextArea className="template-name" {...templateNameInput} name='templateName'/></FormItem>
          </Form>
        </Dialog>
      </Fragment>
    )
  }
}
