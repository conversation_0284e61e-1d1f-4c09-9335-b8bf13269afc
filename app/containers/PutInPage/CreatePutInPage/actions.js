/**
 * PutIn Actions
 */

import { 
  UPDATE_FIRST_FIELDS, 
  UPDATE_SECOND_FIELDS,
  UPDATE_RECALL_RULES, 
  UPDATE_REPEAT_RULES, 
  UPDATE_SORT_RULES,
  INIT_DATA,
  BLANK_DATA,
  BLANK_TEMPLATE,
  DATA_LOADED,
  SET_POOL_TYPE,
  GET_POOL_TYPE,
  UPDATE_DISTRIBUTE_RULES,
  UPDATE_FILTER_RULES,
  UPDATE_TEMPLATE
} from './constants';

/**
 * use template
 */
export function updateSecondPage(secondPage){
  return {
    type: UPDATE_SECOND_FIELDS,
    secondPage
  }
}

export function blankTemplate(){
  return {
    type:BLANK_TEMPLATE
  }
}

/**
 * init page
 * @param {string} name 
 * @param {string} value 
 */
export function initData(activityId){
  return {
    type: INIT_DATA,
    activityId
  }
}

export function dataLoaded(initState){
  return {
    type: DATA_LOADED,
    initState
  }
}

/**
 * blank data
 */
export function blankData(){
  return {
    type: BLANK_DATA
  }
}

/**
 * get config type
 */
export function getPoolType() {
  return {
    type: GET_POOL_TYPE,
  }
}

/**
 * set config type commodity or store 
 * @param {number} configType 
 */
export function setPoolType({type, storeSortRules, skuSortRules}) {
  return {
    type: SET_POOL_TYPE,
    poolType: type,
    storeSortRules,
    skuSortRules
  }
}

/**
 * 
 * @param {*} type 
 */
export function updateTemplate(name, value){
  return {
    type: UPDATE_TEMPLATE,
    [name]: value
  }
}


/**
 * update first page data
 * @param  {string} name
 * @param {string} value 
 */

function updateByType(type){
  return function(name, value) {
    return {
      type,
      [name]: value
    }
  }
}

export function updateFirst(name, value){

  return updateByType(UPDATE_FIRST_FIELDS)(name, value)
}

/**
 * update recall data
 */
export function updateRecallRules(name, value){
  return updateByType(UPDATE_RECALL_RULES)(name, value)
}

/**
 * update deRepeat rules
 */
export function updateRepeatRules(name, value){
  return updateByType(UPDATE_REPEAT_RULES)(name, value)
}

/**
 * update sort rules
 */
export function updateSortRules(name, value){
  return updateByType(UPDATE_SORT_RULES)(name, value)
}

/**
 * update distribute rules
 */
export function updateDistributeRules(name, value) {
  return updateByType(UPDATE_DISTRIBUTE_RULES)(name, value)
}

export function updateFilterRules(name, value) {
  return updateByType(UPDATE_FILTER_RULES)(name, value)
}
