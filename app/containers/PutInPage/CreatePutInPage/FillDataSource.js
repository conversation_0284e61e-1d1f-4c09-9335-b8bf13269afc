import React from "react";

import {
  Form,
  Input,
  Field,
  Select,
  Checkbox,
  Button,
  Overlay,
  Radio,
  CascaderSelect,
  Dialog,
  Switch,
  Message,
} from "@alifd/next";
import './newCollection.scss';
// import '@alifd/next/dist/next.css';
import * as validators from "@/utils/validators";
import  moment from 'moment';
import { Redirect, Link, withRouter } from "react-router-dom";
import {
  composeSelector,
  composeSelectorGrid,
} from "@/components/CascaderSelect";
import { CheckBoxWithAll } from "@/components/CheckBoxWithAll";
import { doesFormHasErrors } from "@/utils/formTool";
import { track } from "@/utils/aplus";
import { formItemLayout, formLayout, formItemLayoutChannel } from "./constants";
import { momentToTimeStamp } from "@/utils/time";
import { getQueryString, replaceParamVal } from "@/utils/others";
import { TimeRangePicker } from "@/components/TimeRangePicker";
import { getPoolIdsGroup, getUserTagGroupIdsList,getPoolList,getInvestData } from "../request";
import {
  deliveryChannelsRadio,
  limitTypeSelect,
  pooTypeRadioRules,
  limitTypeGoodSelect,
  versionGroupSet,
} from "../constants";
import { SchemaMap } from "../../channel/market/config";
import { fromJS } from "immutable";
import * as api from "@/utils/api";
import * as napi from "@/adator/api";
import { pageMap, showQrCodeMap } from "../../channel/market/common";
import {
  queryPosConfig,
  getResourceDeliveryChannel, getEleCategory,
} from "../../channel/market/request";
import { SupplySource } from '@ali/luna-rmc';
import {
  getStoreCategory,
  onRequestError,
  ali,
  queryMarketingType,
  queryMarketActivityType
} from "@/utils/api";
const FormItem = Form.Item;
const { isArrayWithBlank } = validators;
let CitySelector = composeSelector([]);
let GridSelector = composeSelectorGrid([]);
// import jsonp from 'jsonp';

const RadioGroup = Radio.Group;

const testActivityTime = isArrayWithBlank;

const PeopleList = ["43","136"];

// const showShopList = ['155']; //单纯需要门店类目的

// const cateList = ["733","919","920"];

//处理citys, 更新selectedCitys
function updateSelectedCitys(data) {
  const province = data.filter((item) => !!item.children);
  const citys = data.filter((item) => !item.children);

  return province
    .reduce((provinceCitys, item) => provinceCitys.concat(item.children), [])
    .concat(citys)
    .map((item) => +item.value);
}

function fetchSkuCategory() {
  return ali
    .getCategorySku(false)
    .then((res) => {
      if (res.status == 200) {
        // 商品类目下发的value是number，但是在cascadeSelector里返回的选中项都是string，因此这里需要统一一下格式，不然编辑的时候会找不到对应的项。
        const dataSource = res.data.data.map(dataItem => {
          dataItem.value = dataItem.value.toString();
          dataItem.children && dataItem.children.map(subItem => {
            subItem.value = subItem.value.toString();
            subItem.children && subItem.children.map(thirdItem => {
              thirdItem.value = thirdItem.value.toString();
            })
          })
          return dataItem;
        });
        return dataSource;
      } else {
        return {};
      }
    })
    .catch(onRequestError);
}
const SkuCategorySelector = composeSelector(fetchSkuCategory);

function fetchMainCategory() {
  return ali
    .getNewAllStoreMainCategory()
    .then((res) => {
      if (res.status == 200) {
        return res.data.data;
      } else {
        return {};
      }
    })
    .catch(onRequestError);
}
const MainCategorySelector = composeSelector(fetchMainCategory);

//处理citys, 更新selectedCitys&griddings
function updateSelectedGriddings(data, extra) {
  const { indeterminateData = [] } = extra || {};
  const province = [];
  const citys = [];
  const griddings = [];
  data.forEach((item) => {
    let { value, label, children, pos, level } = item;
    if (!+value && label == "直营城市") {
      citys.push(item);
    } else if (level == 3 || (!level && pos.split("-").length == 4)) {
      griddings.push(item);
    } else if (level == 2 || (!level && pos.split("-").length == 3)) {
      citys.push(item);
    } else if (level == 1 || (!level && pos.split("-").length == 2)) {
      province.push(item);
    }
  });
  const extraCitys = indeterminateData.filter(
    (item) => item.pos.split("-").length == 3
  );

  return [
    province
      .reduce((provinceCitys, item) => provinceCitys.concat(item.children), [])
      .concat(citys, extraCitys)
      .map((item) => +item.value),
    griddings.map(({ value, label }) => ({
      value: +value.split("_")[1],
      name: label,
    })),
  ];
}

export class FillDataSource extends React.Component {
  static defaultProps = {
    onStepChange: () => {},
  };

  constructor(props) {
    super(props);

    // const pageItem = pageMap.filter(
    //   (v) => v.pageId == getQueryString("pageId")
    // );
    // const pageUrl = pageItem.length > 0 ? pageItem[0].url : "";
    const pageUrl = location.href.substring(0, location.href.lastIndexOf('/'));
    this.state = {
      newFormData:{},
      cityIds: [],
      // 常规池
      poolIdsSet: [],
      poolIds: [],
      // 加权池
      weightPoolIdsSet: [],
      weightPoolIds: [],
      isActivityEdit:props.isActivityEdit,
      isEdit: location.href.indexOf("/edit/") != -1,
      channelVersionList: {
        allVersion: true,
        channel: "",
        versionGroup: {
          relationType: "and",
          groupList: [],
          versionList: [
            {
              operateType: "",
              value: "",
            },
          ],
        },
      },
      showQrCode: showQrCodeMap[pageUrl],
    };

    this.field = new Field(this, {
      onChange: (name, value) => {
        if (props.isChannel) {
          this.saveLocalField(name, value);
        }
        // if (name == "limitType") {
        //   switch (value) {
        //     case 1:
        //       this.props.setPoolType({type: 2});
        //       this.props.onFirstPageChange("poolType", 2);
        //       break;
        //     case 2:
        //       this.props.setPoolType({type: 1});
        //       this.props.onFirstPageChange("poolType", 1);
        //       break;
        //   }
        // }
        if (name == "channelList") {
          let {channelVersionList} = this.state;
          if (
            value.includes("*") ||
            value.includes("android") ||
            value.includes("ios")
          ) {
            let newChannelList = value.filter((v) =>
              ["*", "android", "ios"].includes(v)
            );
            channelVersionList.channel = newChannelList.join(",");
            this.setState({channelVersionList});
          }
        }
        if (name == 'peopleType') {
          let v = (value == '1' ? ['0'] : '');
          this.saveLocalField('peopleOrderType', v);
          this.props.onFirstPageChange('peopleOrderType', v);
        }
        this.props.onFirstPageChange(name, value);
      },
    });

    this.hasCity = false;
    if (
      (props.cities.toJS && props.cities.toJS().length) ||
      (props.cities && props.cities.length)
    ) {
      CitySelector = composeSelector(props.cities);
      GridSelector = composeSelectorGrid(props.cities);
      this.hasCity = true;
    }

    this.cityList = [];


  }

  getSchemaMapByKey = (key) => {
    let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
    const result = SchemaMap ? SchemaMap[key] : false;
    return result;
  };

  // getShowPutIn = () =>{
  //   let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
  //   const showPutIn = SchemaMap ? SchemaMap["showPutIn"] : false;
  //   return showPutIn;
  // }
  //
  // getShowDataConfig = () =>{
  //   let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
  //   const showDataConfig = SchemaMap ? SchemaMap["showDataConfig"] : false;
  //   return showDataConfig;
  // }
  //
  // getConfigGroup = () => {
  //   let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
  //   const configGroup = SchemaMap ? SchemaMap.configGroup : [];
  //   return configGroup
  // }

  // getShowConfigGroup = () =>{
  //   let SchemaMap = JSON.parse(sessionStorage.getItem("SchemaMap"));
  //   const showConfigGroup = SchemaMap ? SchemaMap.showConfigGroup : [];
  //   return showConfigGroup
  // }

  //获取selectedCitys&griddings
  getSelectedGriddings = (data, extra) => {
    let { showGrid } = this.props;
    const [selectedCitys, griddings] = updateSelectedGriddings(data, extra);
    if (this.props.isChannel) {
      this.saveLocalField("selectedCitys", selectedCitys);
      this.saveLocalField("griddings", griddings);
    }
    this.props.onFirstPageChange("selectedCitys", selectedCitys);
    this.props.onFirstPageChange("griddings", griddings);
  };

  //获取selectedCitys
  getSelectedCitys = (data) => {
    const selectedCitys = updateSelectedCitys(data);
    if (this.props.isChannel) {
      this.saveLocalField("selectedCitys", selectedCitys);
    }
    this.props.onFirstPageChange("selectedCitys", selectedCitys);
  };

  getDetailData = () => {
    if (sessionStorage.getItem("configId")) {
      let baseQueryRequestDTO = {
        resourceId: getQueryString("resourceId"),
        posId: getQueryString("posId"),
        configId: sessionStorage.getItem("configId"),
      };
      queryPosConfig(baseQueryRequestDTO)
        .then((result) => {
          let scheduleInfo = JSON.parse(result.scheduleInfo);
          let {eleCategoryFirList,eleCategorySecList} = scheduleInfo;
          // let scheduleTemplate = JSON.parse(result.scheduleTemplate);
          let putInInfo = {
            channelList: scheduleInfo.channelList,
            channelLabel: scheduleInfo.channelLabel,
            citys: scheduleInfo.citys,
            selectedCitys: scheduleInfo.selectedCitys,
            griddings: scheduleInfo.griddings,
            userTagGroupIdsList: scheduleInfo.userTagGroupIdsList
              ? scheduleInfo.userTagGroupIdsList
              : [],
            peopleType: scheduleInfo.peopleType,
            peopleOrderType: scheduleInfo.peopleOrderType,
          };
          if (scheduleInfo.channelVersionList) {
            putInInfo.channelVersionList = JSON.parse(
              scheduleInfo.channelVersionList
            );
            this.setState({
              channelVersionList: JSON.parse(scheduleInfo.channelVersionList),
            });
          }
          if(scheduleInfo.eleCategoryFirList || scheduleInfo.eleCategorySecList){
            putInInfo.eleCategoryFirList = scheduleInfo.eleCategoryFirList;
            putInInfo.eleCategorySecList = scheduleInfo.eleCategorySecList;
            this.setState({
              eleCategoryArray: scheduleInfo.eleCategoryFirList.concat(scheduleInfo.eleCategorySecList),
            },()=>{
              console.log(this.state.eleCategoryArray);
            });
          }
          sessionStorage.setItem("putInInfo", JSON.stringify(putInInfo));
          this.init(this.props);
        })
        .catch(api.onRequestError);
    }
  };

  //初始化数据
  init(props) {
    let formData = props.data.toJS();
    if (props.isChannel && this.getSchemaMapByKey("showPutIn")) {
      //第二种情况，不生成投放活动id，只有投放城市、投放人群、投放渠道
      formData = JSON.parse(sessionStorage.getItem("putInInfo"));
    }
    if (formData && formData.griddings && formData.griddings.length) {
      formData.griddings.forEach((item) => {
        if (item.parentValue && !this.cityList.includes(item.parentValue)) {
          this.cityList.push(item.parentValue);
        }
      });
    }
    if (
      !this.props.isChannel ||
      (this.props.isChannel &&
        (this.getSchemaMapByKey("showDataConfig") ||
          this.getSchemaMapByKey("showConfigGroup")))
    ) {
      this.initPoolIdsSet(formData);
    }
    if (!this.state.lastName) {
      this.getUserName();
    }
    if (formData) {
      let {newLimitType} = this.state;
      if(formData.limitType){
        newLimitType = formData.limitType;
      }
      this.setState({newFormData:formData,newLimitType},()=>{
        console.log("newFormData",this.state.newFormData);
      });

      const fieldValue = Object.keys(formData).reduce((field, key) => {
        return { ...field, [key]: formData[key] };
      }, {});

      this.field.setValues(fieldValue);

      let defaultPoolId = [];
      if (!this.state.isEdit && getQueryString("poolId") && formData.poolIds == "") { //新建页面，默认
        let poolIdString = getQueryString("poolId") ? getQueryString("poolId") : '';
        defaultPoolId.push(poolIdString);
        this.field.setValue("poolIds",defaultPoolId);
        this.props.onFirstPageChange("poolIds",defaultPoolId);
      }
    }
  }

  initPoolIdsSet = (formData) => {
    let poolIdsSet = [];
    let weightPoolIdsSet = [];
    if (formData && formData.poolIdAndContentRep) {
      formData.poolIdAndContentRep.map((v) => {
        poolIdsSet.push({
          label: v.poolContent,
          value: v.poolId,
        });

      });
      this.setLocalPoolIdsSet(poolIdsSet);
    }
    if (formData && formData.weightPoolIdAndContentRep) {
      formData.weightPoolIdAndContentRep.map((v) => {
        weightPoolIdsSet.push({
          label: v.poolContent,
          value: v.poolId,
        });

      });
      this.setLocalWeightPoolIdsSet(weightPoolIdsSet);
    }
  };

  componentWillReceiveProps(newProps) {
    this.init(newProps);
    if (!this.hasCity && newProps.cities.length) {
      CitySelector = composeSelector(newProps.cities);
      GridSelector = composeSelectorGrid(newProps.cities);
      this.hasCity = true;
    }
  }


  getUserName = () => {
    try {
      let request = napi.getBucUser;
      request().then((resp) => {
        this.setState({lastName: resp.data.data.lastName});
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  componentDidMount() {
    this.init(this.props);
    this.getDetailData();

    if (this.props.isChannel) {
      // 获取频道页可勾选的渠道key值
      this.queryResourceDeliveryChannel();
    }

    this.fetchCategory();
    // this.searchMarketingTypeList();
  }

  handleChannelList = (channelList, deliveryChannelShow) => {
    channelList.forEach((channelItem) => {
      if (deliveryChannelShow.indexOf("*") !== -1) {
        channelItem.checkboxDisabled = false;
      } else {
        channelItem.checkboxDisabled = true;
        deliveryChannelShow.forEach((key) => {
          const comparedKey =
            channelItem.value === "mini_app" ? "miniapp" : channelItem.value;
          if (comparedKey.indexOf(key) != -1) {
            channelItem.checkboxDisabled = false;
          }
        });
      }
      if (channelItem.subChannels) {
        channelItem.children = JSON.parse(
          JSON.stringify(channelItem.subChannels)
        );
        this.handleChannelList(channelItem.children, deliveryChannelShow);
      } else {
        channelItem.children = [];
      }
    });
  };

  queryResourceDeliveryChannel = () => {
    let baseQueryRequestDTO = {
      pageId: getQueryString("pageId"),
      resourceId: getQueryString("resourceId"),
    };
    getResourceDeliveryChannel(baseQueryRequestDTO)
      .then((result) => {
        let { channelListMap } = this.props.data.toJS();
        const deliveryChannelShow = result.join(",");
        this.handleChannelList(channelListMap, deliveryChannelShow);
        this.props.onFirstPageChange("channelListMap", channelListMap);
      })
      .catch(api.onRequestError);
  };

  // 商品集ID检验
  checkDataSources = (rule, value, callback) => {
    if (value.length > 15) {
      callback("选品集ID输入最多15个");
    } else {
      callback();
    }
  };
  // // 商品集ID检验
  checkDataSourcess = (rule, value, callback) => {
    if (value.length > 20) {
      callback("选品集ID输入最多20个");
    } else {
      callback();
    }
  };
  // 加权池检验
  weightCheckDataSources = (rule, value, callback) => {
    if (value.length > 5) {
      callback("选品集ID输入最多5个");
    } else {
      callback();
    }
  };

  // 投放人群检验
  checkUserTagGroupIdsList = (rule, value, callback) => {
    if (value.length > 5) {
      callback("用户群组ID输入最多5个");
    } else {
      callback();
    }
  };

  // 活动时间的校验
  checkActivityTime = (rule, value, callback) => {
    value = value.map((time) => momentToTimeStamp(time));

    const errors = testActivityTime(value);
    if (errors.length) {
      callback("请选填完整的活动时间段！");
    } else {
      callback();
    }
  };

  //下一步点击操作
  onNextStep = () => {
    track("clickEvent", [
      "/selection_kunlun.PUTIN-MANAGE-create-putin-activity.next-step-btn",
    ]);
    let {limitType, categoryList, isRecommend, poolType, commonCollection} = this.props.data.toJSON();
    if (poolType == 1) {
      this.props.setPoolType({type: 1});
    }

    //校验常规池必填
    if(limitType==0 || limitType==3) {
      if (commonCollection && commonCollection.length > 0) {
        let filterGroup = commonCollection.filter((v) => ((v.dataSourceType == '1' || v.dataSourceType == '2' || v.dataSourceType == '3') && v.idList.length > 0) || (v.dataSourceType == '4' && v.marketActivityIds != ''));
        if (filterGroup.length == 0 ) {
          return Message.error("常规池必填");
        }
      } else {
        return Message.error("常规池必填");
      }
    }

    // 检测categoryList
    if (isRecommend) {
      if (!categoryList.length) {
        return Message.error("商品类目必填");
      } else {
        let totalLevel1 = 0;
        let totalLevel2 = 0;
        let totalLevel3 = 0;
        categoryList.forEach(categoryItem => {
          switch(categoryItem.level) {
            case 1:
              totalLevel1 += 1;
              if(totalLevel1 > 100) {
                return Message.error('一级类目数不能大于100个');
              }
              break;
            case 2:
              totalLevel2 += 1;
              if(totalLevel2 > 100) {
                return Message.error('二级类目数不能大于100个');
              }
              break;
            case 3:
              totalLevel3 += 1;
              if(totalLevel3 > 100) {
                return Message.error('三级类目数不能大于100个');
              }
              break;
          }
        })
      }
    }
    this.field.validate((e) => {
      if (!doesFormHasErrors(e)) {
        limitType == 0 && this.props.getPoolType();
        this.props.onStepChange(this.props.step + 1);
      }
    });
  };

  changeDeliveryChannel = (checkedList) => {
    this.props.onFirstPageChange("channelList", checkedList);
  };

  removeDuplicate = (arr) => {
    var hash = {};
    let result = arr.reduce(function (item, next) {
      hash[next.value] ? "" : (hash[next.value] = true && item.push(next));
      return item;
    }, []);
    return result;
  };

  setLocalPoolIdsSet = (dataSource) => {
    const LocalPoolIdsSet = sessionStorage.getItem("poolIdsSet")
      ? JSON.parse(sessionStorage.getItem("poolIdsSet"))
      : [];
    let wholePoolIdsSet = LocalPoolIdsSet.concat(dataSource);
    const newWholePoolIdsSet = this.removeDuplicate(wholePoolIdsSet);
    sessionStorage.setItem("poolIdsSet", JSON.stringify(newWholePoolIdsSet));
  };
  setLocalWeightPoolIdsSet = (dataSource) => {
    const LocalPoolIdsSet = sessionStorage.getItem("weightPoolIdsSet")
      ? JSON.parse(sessionStorage.getItem("weightPoolIdsSet"))
      : [];
    let wholePoolIdsSet = LocalPoolIdsSet.concat(dataSource);
    const newWholePoolIdsSet = this.removeDuplicate(wholePoolIdsSet);
    sessionStorage.setItem(
      "weightPoolIdsSet",
      JSON.stringify(newWholePoolIdsSet)
    );
  };
  onChange = (value, actionType) => {
    if (actionType == "itemClick") {
      this.props.onFirstPageChange("poolIds", value);
      this.props.getPoolType();
    }
  };

  onWeightChange = (value, actionType) => {
    if (actionType == "itemClick") {
      this.props.onFirstPageChange("weightPoolIds", value);
      this.props.getPoolType();
    }
  };

  onChangeUserTagGroupIdsList = (value, actionType) => {
    if (actionType == "itemClick") {
      this.props.onFirstPageChange("userTagGroupIdsList", value);
      if (this.props.isChannel) {
        this.saveLocalField("userTagGroupIdsList", value);
      }
    }
  };

  onSearchUserTagGroupIdsList = (keyword) => {
    if (this.searchGroupTimeout) {
      clearTimeout(this.searchGroupTimeout);
    }
    this.searchGroupTimeout = setTimeout(() => {
      if (keyword) {
        getUserTagGroupIdsList({ groupId: keyword }).then((data) => {
          let { userTagGroupIdsListSet } = this.props.data.toJS();
          const dataSource = data.map((item) => ({
            label: item.groupName,
            value: item.groupId,
          }));
          this.props.onFirstPageChange(
            "userTagGroupIdsListSet",
            this.removeDuplicate([...userTagGroupIdsListSet, ...dataSource])
          );
        });
      } else {
        this.setState({ poolIdsSet: [] });
        sessionStorage.setItem("poolIdsSet", JSON.stringify([]));
      }
    }, 800);
  };

  onSearch = (keyword) => {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if (keyword) {
        getPoolIdsGroup({ poolContent: keyword }).then((data) => {
          const dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
          }));
          this.setState({ poolIdsSet: dataSource }, () => {
            this.setLocalPoolIdsSet(dataSource);
          });
        });
      } else {
        this.setState({ poolIdsSet: [] });
        sessionStorage.setItem("poolIdsSet", JSON.stringify([]));
      }
    }, 800);
  };
  onWeightSearch = (keyword) => {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if (keyword) {
        getPoolIdsGroup({ poolContent: keyword }).then((data) => {
          const dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
          }));
          this.setState({ weightPoolIdsSet: dataSource }, () => {
            this.setLocalWeightPoolIdsSet(dataSource);
          });
        });
      } else {
        this.setState({ weightPoolIdsSet: [] });
        sessionStorage.setItem("weightPoolIdsSet", JSON.stringify([]));
      }
    }, 800);
  };

  onSearchCategory = (keyword) => {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if (keyword) {
        getPoolIdsGroup({ poolContent: keyword }).then((data) => {
          const dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
          }));
          this.setState({ poolIdsSet: dataSource }, () => {
            this.setLocalPoolIdsSet(dataSource);
          });
        });
      } else {
        this.setState({ poolIdsSet: [] });
        sessionStorage.setItem("poolIdsSet", JSON.stringify([]));
      }
    }, 800);
  };

  renderUserTagGroupIdsList = (v) => {
    return `${v.label}(${v.value})`;
  };

  saveLocalField = (name, value) => {
    const { isChannel } = this.props;
    let showPutIn = this.getSchemaMapByKey("showPutIn");
    let fields = [
      "channelVersionList",
      "channelList",
      "channelLabel",
      "citys",
      "userTagGroupIdsList",
      "selectedCitys",
      "griddings",
      "peopleType",
      "peopleOrderType",
      'eleCategoryFirList',
      'eleCategorySecList'
    ];
    if (isChannel && showPutIn && fields.includes(name)) {
      let putInInfo = sessionStorage.getItem("putInInfo")
        ? JSON.parse(sessionStorage.getItem("putInInfo"))
        : {};
      putInInfo[name] = value;
      if (name == "channelList") {
        if (
          value.includes("*") ||
          value.includes("android") ||
          value.includes("ios")
        ) {
          putInInfo.channelVersionList = this.state.channelVersionList;
          let newChannelList = value.filter((v) =>
            ["*", "android", "ios"].includes(v)
          );
          putInInfo.channelVersionList.channel = newChannelList.join(",");
        } else {
          delete putInInfo.channelVersionList;
        }
      }
      this.field.setValue(name, value);
      sessionStorage.setItem("putInInfo", JSON.stringify(putInInfo));
    }
  };

  onChangeStep = (type) => {
    let self = this;
    let step = parseInt(sessionStorage.getItem("step"));
    if (type == 1) {
      //上一步
      step = step - 1 < 0 ? 0 : step - 1;
      sessionStorage.setItem("step", step);
      replaceParamVal("step", step);
    }
    if (type == 2) {
      //下一步
      sessionStorage.removeItem("poolIdsSet"); //先清掉这个值
      this.onValidate(function () {
        self.props.onSubmit();
      });
    }
  };

  handleSetting = () => {
    if (this.field.getValue("limitType") == 3) { // 为了兼容频道管理里面新增的指定选店集
      this.props.setPoolType({type: 2})
    } else {
      this.props.getPoolType();
    }
    this.props.showRulesSetting(true);
  };

  onSubmit = (type) => {
    let self = this;
    let isShowDialog =
      sessionStorage.getItem("reviewOpen") == 1 &&
      (sessionStorage.getItem("status") == 5 ||
        sessionStorage.getItem("status") == 6);
    const isCopy = sessionStorage.getItem("isCopy") || false;
    let { showQrCode } = this.state;
    if (!showQrCode) {
      if (isShowDialog && !isCopy) {
        Dialog.confirm({
          title: "提示",
          content: "保存后，原先活动失效，会重新发起审核流程，请确认？",
          onOk: () =>
            this.onValidate(function () {
              self.props.onSubmit(true);
            }),
        });
      } else {
        this.onValidate(function () {
          self.props.onSubmit(true);
        });
      }
    } else {
      this.onValidate(function () {
        self.props.onSubmit(true);
      });
    }
  };

  getValidate = () => {
    let showDataConfig = this.getSchemaMapByKey("showDataConfig");
    let configGroup = this.getSchemaMapByKey("configGroup");
    let showConfigGroup = this.getSchemaMapByKey("showConfigGroup");
    let step = sessionStorage.getItem("step")
      ? sessionStorage.getItem("step")
      : 0;
    let needPutIn =
      showConfigGroup && configGroup ? configGroup[step].required : true;
    if (showDataConfig || (showConfigGroup && needPutIn)) {
      //需要校验
      return true;
    }
    if (showConfigGroup && !needPutIn) {
      return false;
    }
  };

  onValidate = (callback) => {
    //通过配置，判断是否需要校验
    // let showDataConfig = this.getSchemaMapByKey("showDataConfig");
    // let configGroup = this.getSchemaMapByKey("configGroup");
    // let showConfigGroup = this.getSchemaMapByKey("showConfigGroup");
    // let step = sessionStorage.getItem("step") ? sessionStorage.getItem("step") : 0;
    // let needPutIn = (showConfigGroup && configGroup) ? configGroup[step].required : true;
    if (this.getValidate()) {
      this.field.validate((e) => {
        if (!doesFormHasErrors(e)) {
          callback();
        }
      });
    }
    if (!this.getValidate()) {
      callback();
    }
  };

  ctrlChannelVersionList = (value) => {
    let putInInfo = sessionStorage.getItem("putInInfo")
      ? JSON.parse(sessionStorage.getItem("putInInfo"))
      : {};
    if (putInInfo.channelList) {
      let newChannelList = putInInfo.channelList.filter((v) =>
        ["*", "android", "ios"].includes(v)
      );
      value.channel = newChannelList.join(",");
    }
    // if (!value.allVersion && value.versionGroup.versionList.length > 0) {
    //   value.versionGroup.versionList.map((v) => {
    //     if (v.operateType == '' || v.value == '') {
    //       Message.warning("版本条件必填");
    //     }
    //   })
    // }
    this.setState({ value }, () => {
      this.saveLocalField("channelVersionList", this.state.channelVersionList);
      // this.props.onFirstPageChange("channelVersionList", this.state.channelVersionList);
    });
  };

  changeAllVersion = (value) => {
    let { channelVersionList } = this.state;
    channelVersionList.allVersion = value;
    this.ctrlChannelVersionList(channelVersionList);
  };

  changeVersion = (key, e, index) => {
    let { channelVersionList } = this.state;
    let value = e.target ? e.taret.value : e;
    channelVersionList.versionGroup.versionList[index][key] = value;
    this.ctrlChannelVersionList(channelVersionList);
  };

  addVersion = () => {
    let { channelVersionList } = this.state;
    let item = {
      operateType: "",
      value: "",
    };
    if (channelVersionList.versionGroup.versionList.length < 3) {
      channelVersionList.versionGroup.versionList.push(item);
      this.setState({ channelVersionList }, () => {
        this.ctrlChannelVersionList(channelVersionList);
      });
    } else {
      Message.warning("最多3个");
    }
  };

  delVersion = (index) => {
    let { channelVersionList } = this.state;
    if (channelVersionList.versionGroup.versionList.length > 1) {
      channelVersionList.versionGroup.versionList.splice(index, 1);
    } else {
      Message.warning("最少1个");
    }
    this.ctrlChannelVersionList(channelVersionList);
  };

  fetchCategory = async () => {
    try {
      let request = getEleCategory;
      let resp = await request();
      this.setState({
        eleCategorylist: resp.data.data
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  changeEleCategory = (value,data) =>{
    let eleCategoryFirListArray = []
    let eleCategorySecListArray = [];
    let {eleCategoryArray} = this.state;
    data.map((v) => {
      let length = v.pos.split("-").length;
      if (length == 2) {
        eleCategoryFirListArray.push(v.value);
      } else if (length == 3) {
        eleCategorySecListArray.push(v.value);
      }
    })
    this.setState({eleCategoryArray:value});
    this.saveLocalField("eleCategoryFirList", eleCategoryFirListArray);
    this.saveLocalField("eleCategorySecList", eleCategorySecListArray);
  }

  searchMarketingTypeList = () => {
    let {newFormData} = this.state;
    let itemStoreType = newFormData.poolType || 1;
    try {
      return queryMarketActivityType(itemStoreType)
        .then((res) => {
          if (res && res.length > 0) {
            return Promise.resolve(res);
          } else {
            Message.warning(res.msgInfo || '系统繁忙');
          }
        })
    } catch (error) {
      Message.warning(error || '系统繁忙');
    }
  }

  searchRequest = (formData, type) => {
    let {lastName, newFormData} = this.state;
    if (type == '1') {
      try {
        return getPoolList({
          // createUser: lastName,
          rangeType: 0,
          itemStoreType: newFormData.poolType || 0,
          ...formData
        })
          .then((res) => {
            if (res.success) {
              let data = [];
              if (res.model && res.model.length > 0) {
                data = res.model.map(item => {
                  item.submit = {
                    label: item.poolName,
                    value: item.poolId,
                    newPlatformFlag: item.newPlatformFlag,
                    createMode: item.createMode,
                    poolType: item.poolType
                  };
                  return item;
                });
              }
              return Promise.resolve({
                data,
                total: res.totalCount || 0
              });
            } else {
              Message.warning(res.msgInfo || '系统繁忙');
            }
          })
      } catch (error) {
        Message.warning(error || '系统繁忙');
      }
    } else {
      try {
        return getInvestData({
          // type: '单活动',
          itemStoreType: newFormData.poolType || 0,
          // createUser: lastName,
          ...formData
        })
          .then((res) => {
            if (res.success) {
              let data = [];
              if (res.model && res.model.length > 0) {
                data = res.model.map(item => {
                  item.submit = {
                    label: item.investName,
                    value: item.investId,
                  };
                  return item;
                });
              }
              return Promise.resolve({
                data,
                total: res.totalCount || 0
              });
            } else {
              Message.warning(res.msgInfo || '系统繁忙');
            }
          })
      } catch (error) {
        Message.warning(error || '系统繁忙');
      }
    }
  }



  onSupplyChange = (dataSource) =>{
    if(dataSource.hasOwnProperty("commonCollection")){
      this.props.onFirstPageChange('commonCollection', dataSource.commonCollection);
    }
    if(dataSource.hasOwnProperty("weightCollection")){
      this.props.onFirstPageChange('weightCollection', dataSource.weightCollection);
    }
  }

  onSupplyChangePoolType = (limitType) =>{
    let newPoolType;
    console.log(limitType);
    switch (limitType) {
      case 0:
      case 2:
        this.props.setPoolType({type: 1});
        newPoolType = 1;
        this.props.onFirstPageChange("poolType", 1);
        break;
      case 1:
      case 3:
        this.props.setPoolType({type: 2});
        newPoolType = 2;
        this.props.onFirstPageChange("poolType", 2);
        break;
    }
    this.setState({newLimitType:limitType, newPoolType});
    this.props.onFirstPageChange("limitType",limitType);
  }

  /**
   * @param time
   * 把开始时间和结束时间的时分秒转换一下
   */
  changeTime = (time) => {
    const { startDate, endDate } = time;
    return [
      moment(startDate).set({
        hour: 0,
        minute: 0,
        second: 0,
        millisecond: 0,
      }),
      moment(endDate).set({
        hour: 23,
        minute: 59,
        second: 59,
        millisecond: 0,
      }),
    ];
  };

  /**
   * @param type 1 本周 2 本月 3 未来7天 4 未来30天
   */
  setDate = (type) => {
    let startDate = moment();
    let endDate;
    switch (type) {
      case 1:
        endDate = moment().weekday(7);
        break;
      case 2:
        endDate = moment().endOf('month');
        break;
      case 3:
        endDate = moment().add('days', 7);
        break;
      case 4:
        endDate = moment().add('days', 30);
        break;
    }
    let result = this.changeTime({
      startDate,
      endDate,
    });
    this.props.onFirstPageChange('activityTime',result);
  };

  render() {
    // const { CitySelector } = this.state;
    const { init } = this.field;
    let { poolType, data, resourceType, showGrid } = this.props;
    const { isEdit, eleCategorylist, eleCategoryArray, newFormData} = this.state;
    let posId = getQueryString("posId");
    // categoryType 商品分类是指定还是剔除。
    let {
      userTagGroupIdsListSet,
      limitType,
      peopleType,
      peopleOrderType,
      channelListMap = [],
      labelListMap = [],
      isRecommend,
      categoryType,
      categoryList,
      isCatComp,
      riskCompLevel,
      mainCates,
    } = data.toJS();
    const putInInfo = sessionStorage.getItem("putInInfo")
      ? JSON.parse(sessionStorage.getItem("putInInfo"))
      : {};
    // if (!isEdit && !peopleType) {
    //   peopleType = "1";
    //   putInInfo.peopleType = '1';
    //   sessionStorage.setItem("putInInfo", JSON.stringify(putInInfo));
    // }
    let { deliveryChannelList, deliveryChannelLabel } = this.field.getValues();
    if (!deliveryChannelList) {
      deliveryChannelList = putInInfo.channelList || [];
    }
    if (!deliveryChannelLabel) {
      deliveryChannelLabel = putInInfo.channelLabel || [];
    }
    // 常规池设置
    const poolIdsSet =
      this.state.poolIdsSet.length > 0
        ? this.state.poolIdsSet
        : JSON.parse(sessionStorage.getItem("poolIdsSet"));
    const poolIdsValue = this.field.getValue("poolIds");
    // 加权池设置
    const weightPoolIdsSet =
      this.state.weightPoolIdsSet.length > 0
        ? this.state.weightPoolIdsSet
        : JSON.parse(sessionStorage.getItem("weightPoolIdsSet"));
    const weightPoolIdsValue = this.field.getValue("weightPoolIds");

    const userTagGroupIdsListValue = this.field.getValue("userTagGroupIdsList");
    const { isChannel } = this.props;
    const showDataConfigObj = [
      { title: "基础配置", type: 1 },
      { title: "非锁定配置", type: 2 },
      { title: "锁定配置", type: 2 },
    ];
    const step = sessionStorage.getItem("step");
    let { channelList, channelLabel } = data.toJS();
    if (!channelList) {
      channelList = deliveryChannelList;
    }
    if (!channelLabel) {
      channelLabel = deliveryChannelLabel;
    }
    let showFormData = (newFormData.commonCollection && newFormData.commonCollection.length > 0) || (newFormData.weightCollection && newFormData.weightCollection.length > 0);
    const limitTypeSet = [
      {
        value: 0,
        label: "指定商品池",
      },
      {
        value: 3,
        label: "指定门店池",
      },
      {
        value: 1,
        label: "不指定，门店聚类",
      },
      {
        value: 2,
        label: "不指定，商品聚类",
      }
    ];
    /**
     * 日期组件下面的按钮list
     */
    const dateGroup = [
      {
        label: '本周',
        type: 1,
      },
      {
        label: '本月',
        type: 2,
      },
      {
        label: '未来7天',
        type: 3,
      },
      {
        label: '未来30天',
        type: 4,
      },
    ];
    if (!isChannel) {
      return (
        <Form {...formLayout} field={this.field}>
          <FormItem
            label="活动名称"
            {...formItemLayout}
            required
            requiredMessage="活动名称不能为空"
            maxLength={30}
            minmaxLengthMessage="不可超过30个字符"
          >
            <Input placeholder="请输入活动名称" name="activityName" />
          </FormItem>
          <FormItem
            required
            label="活动时间"
            hasFeedback
            {...formItemLayout}
            validator={this.checkActivityTime}
          >
            <TimeRangePicker
              showTime={{ format: "HH:mm:ss" }}
              format="YYYY-MM-DD"
              name="activityTime"
              hasClear={true}
            />
            {dateGroup.map(item => {
              return (
                <Button
                  className="btn-time-group"
                  key={item.type}
                  size="small"
                  onClick={() => this.setDate(item.type)}
                >
                  {item.label}
                </Button>
              );
            })}
          </FormItem>
          <FormItem label="投放渠道" {...formItemLayout}>
            <CascaderSelect
              name="channelList"
              multiple
              value={channelList}
              dataSource={channelListMap}
              onChange={(value) => this.changeDeliveryChannel(value)}
            />
          </FormItem>
          <FormItem label="投放标签" {...formItemLayout}>
            <Checkbox.Group
              name="channelLabel"
              value={channelLabel}
              dataSource={labelListMap}
            />
          </FormItem>
          <FormItem label="投放城市" {...formItemLayout}>
            {showGrid || this.cityList.length ? (
              <GridSelector
                name="citys"
                expandTriggerType="click"
                cityList={this.cityList}
                {...init("citys")}
                getSelectData={this.getSelectedGriddings}
              />
            ) : (
              <CitySelector
                name="citys"
                {...init("citys")}
                getSelectData={this.getSelectedCitys}
              />
            )}
          </FormItem>
          <FormItem
            className="user-tap-group-ids-wrap"
            label="投放人群"
            hasFeedback
            {...formItemLayout}
            validator={this.checkUserTagGroupIdsList}
          >
            <Select
              mode="multiple"
              showSearch
              placeholder="请输入用户群组ID，支持输入最多5个"
              name="userTagGroupIdsList"
              valueRender={this.renderUserTagGroupIdsList}
              value={userTagGroupIdsListValue ? userTagGroupIdsListValue : []}
              onChange={(value, actionType) =>
                this.onChangeUserTagGroupIdsList(value, actionType)
              }
              onSearch={this.onSearchUserTagGroupIdsList}
              dataSource={userTagGroupIdsListSet}
              style={{ width: "100%" }}
            />
            <a
              className="to-create"
              href="https://boreas.kunlun.alibaba-inc.com/page/crm/index.html?from=header-v2#/crm/crowedSelect"
              target="_blank"
            >
              去创建
            </a>
          </FormItem>
          <SupplySource
            typeKey={'limitType'}
            poolType={(newFormData && (newFormData.limitType || newFormData.limitType==0)) ? newFormData.limitType : 3}
            showSupplySourcePool={limitType === 0 || limitType === 3}
            supplyDataSource={{
              commonCollection: newFormData.commonCollection || [],
              weightCollection: newFormData.weightCollection || []
            }}
            poolTypeMap={limitTypeSet}
            onSupplyChange={this.onSupplyChange}
            onSupplyChangePoolType={this.onSupplyChangePoolType}
            searchRequest={this.searchRequest}
            searchMarketingTypeList={this.searchMarketingTypeList}
            weightIdsMax={60}
            commonIdsMax={60}
          />
          {/*{!showFormData && <SupplySource*/}
          {/*  typeKey={'limitType'}*/}
          {/*  poolTypeMap={limitTypeSet}*/}
          {/*  poolType={(newFormData && (newFormData.limitType || newFormData.limitType==0)) ? newFormData.limitType : 3}*/}
          {/*  showSupplySourcePool={limitType === 0 || limitType === 3}*/}
          {/*  onSupplyChange={this.onSupplyChange}*/}
          {/*  onSupplyChangePoolType={this.onSupplyChangePoolType}*/}
          {/*  searchRequest={this.searchRequest}*/}
          {/*  weightIdsMax={5}*/}
          {/*  commonIdsMax={15}*/}
          {/*/>}*/}
          {/*<FormItem*/}
          {/*  required*/}
          {/*  label="选品集ID"*/}
          {/*  hasFeedback*/}
          {/*  {...formItemLayout}*/}
          {/*  requiredMessage="选品集ID不能为空"*/}
          {/*  validator={this.checkDataSources}*/}
          {/*>*/}
          {/*  <Select*/}
          {/*    placeholder="请选择状态"*/}
          {/*    value={limitType}*/}
          {/*    style={{ width: "100%" }}*/}
          {/*    name="limitType"*/}
          {/*    dataSource={limitTypeSelect}*/}
          {/*  />*/}
          {/*</FormItem>*/}
          {/*{limitType == 0 && (*/}
          {/*  <FormItem*/}
          {/*    label="加权池"*/}
          {/*    hasFeedback*/}
          {/*    {...formItemLayout}*/}
          {/*    validator={this.weightCheckDataSources}*/}
          {/*  >*/}
          {/*    <Select*/}
          {/*      mode="multiple"*/}
          {/*      showSearch*/}
          {/*      placeholder="请输入选品集ID，支持输入最多5个"*/}
          {/*      name="weightPoolIds"*/}
          {/*      value={weightPoolIdsValue ? weightPoolIdsValue : []}*/}
          {/*      onChange={(value, actionType) =>*/}
          {/*        this.onWeightChange(value, actionType)*/}
          {/*      }*/}
          {/*      onSearch={this.onWeightSearch}*/}
          {/*      dataSource={weightPoolIdsSet}*/}
          {/*      style={{ width: "100%" }}*/}
          {/*    />*/}
          {/*  </FormItem>*/}
          {/*)}*/}
          {/*{limitType == 0 && (*/}
          {/*  <FormItem*/}
          {/*    required*/}
          {/*    label="常规池"*/}
          {/*    hasFeedback*/}
          {/*    {...formItemLayout}*/}
          {/*    requiredMessage="选品集ID不能为空"*/}
          {/*    validator={this.checkDataSources}*/}
          {/*  >*/}
          {/*    <Select*/}
          {/*      mode="multiple"*/}
          {/*      showSearch*/}
          {/*      placeholder="请输入，支持输入最多15个"*/}
          {/*      name="poolIds"*/}
          {/*      value={poolIdsValue ? poolIdsValue : []}*/}
          {/*      onChange={(value, actionType) =>*/}
          {/*        this.onChange(value, actionType)*/}
          {/*      }*/}
          {/*      onSearch={this.onSearch}*/}
          {/*      dataSource={poolIdsSet}*/}
          {/*      style={{ width: "100%" }}*/}
          {/*    />*/}
          {/*  </FormItem>*/}
          {/*)}*/}
          {(limitType === 0 || limitType === 2) && (
            <FormItem label="是否开启算法精品池推荐" {...formItemLayout}>
              <RadioGroup name="isRecommend" value={isRecommend}>
                <Radio value={0}>否</Radio>
                <Radio value={1}>是</Radio>
              </RadioGroup>
            </FormItem>
          )}
          {(limitType === 0 || limitType === 2) && isRecommend === 1 && (
            <FormItem label=" " {...formItemLayout}>
              <RadioGroup name="categoryType" value={categoryType}>
                <Radio value={0}>指定商品分类</Radio>
                <Radio value={1}>剔除商品分类</Radio>
              </RadioGroup>
            </FormItem>
          )}
          {(limitType === 0 || limitType === 2) && isRecommend == 1 && (
            <FormItem
              required
              label="商品类目"
              {...formItemLayout}
              requiredMessage="商品类目不能为空"
            >
              <SkuCategorySelector
                name="categoryList"
                {...init("categoryList")}
              />
            </FormItem>
          )}
          {(limitType === 0 || limitType === 2) && isRecommend === 1 && (
            <FormItem label="类目挂靠商品准确性" {...formItemLayout}>
              <RadioGroup name="isCatComp" value={isCatComp}>
                <Radio value={0}>全部</Radio>
                <Radio value={1}>合格</Radio>
              </RadioGroup>
            </FormItem>
          )}
          {(limitType === 0 || limitType === 2) && isRecommend === 1 && (
            <FormItem label="风控合规数据" {...formItemLayout}>
              <RadioGroup name="riskCompLevel" value={riskCompLevel}>
                <Radio value={1}>普通等级</Radio>
                <Radio value={2}>严格等级</Radio>
              </RadioGroup>
            </FormItem>
          )}
          {(limitType === 0 || limitType === 2) && isRecommend == 1 && (
            <FormItem
              label="门店主营类目(新)"
              hasFeedback
              {...formItemLayout}
              requiredMessage="门店主营类目不能为空"
            >
              <MainCategorySelector
                name="mainCates"
                {...init("mainCates")}
              />
            </FormItem>
          )}
          <FormItem label={<span />} {...formItemLayout}>
            <Form.Submit type="primary" validate onClick={this.onNextStep}>
              下一步
            </Form.Submit>
          </FormItem>
        </Form>
      );
    } else {
      //频道页
      let showPutIn = this.getSchemaMapByKey("showPutIn");
      let showDataConfig = this.getSchemaMapByKey("showDataConfig");
      let configGroup = this.getSchemaMapByKey("configGroup");
      let showConfigGroup = this.getSchemaMapByKey("showConfigGroup");
      let showShopList = this.getSchemaMapByKey("showShopList");
      let putInActivityId = sessionStorage.getItem("putInActivityId")
        ? JSON.parse(sessionStorage.getItem("putInActivityId"))
        : "";
      let step = sessionStorage.getItem("step");
      let limitTypeSelectGroup = limitTypeSelect.concat([{
        value: 3,
        label: "指定选店集",
      }])

      if (
        (showDataConfig &&
          this.getSchemaMapByKey("newsChannelPoolType") == 1) ||
        (showConfigGroup &&
          configGroup[parseInt(step)].newsChannelPoolType == 1)
      ) {
        limitTypeSelectGroup = limitTypeGoodSelect;
      }
      if (!peopleOrderType) {
        peopleOrderType = putInInfo.peopleOrderType;
      }
      if (!peopleType) {
        peopleType = putInInfo.peopleType;
      }
      let peopleOrderTypeSource = [
        {
          value: '0',
          label: "全部",
        },
        {
          value: 1,
          label: "新零售新客",
        },
        {
          value: 2,
          label: "新零售老客",
        },
        {
          value: 3,
          label: "医药新客",
        },
      ];
      let channelListValue = this.field.getValue("channelList")
        ? this.field.getValue("channelList")
        : [];
      let { channelVersionList } = this.state;
      let showVersion =
        channelListValue.length > 0
          ? channelListValue.includes("*") ||
            channelListValue.includes("android") ||
            channelListValue.includes("ios")
          : false;
      // let showVersion = channelListValue.length > 0 ? ["ios", "android", "*"].sort().toString().includes(channelListValue.sort().toString()) : false;
      return (
        <Form {...formLayout} field={this.field} className="putin-data">
          {!showPutIn && (
            <FormItem label="投放活动ID：" {...formItemLayoutChannel}>
              <p style={{ color: "#ccc" }}>
                {putInActivityId ? putInActivityId[step - 1] : "保存后自动生成"}
              </p>
            </FormItem>
          )}
          {step && (
            <FormItem label="投放渠道：" {...formItemLayout}>
              <CascaderSelect
                name="channelList"
                multiple
                value={channelList}
                dataSource={channelListMap}
                onChange={(value) => this.changeDeliveryChannel(value)}
              />
            </FormItem>
          )}
          {step && (
            <FormItem label="投放标签：" {...formItemLayout}>
              <Checkbox.Group
                name="channelLabel"
                value={channelLabel}
                dataSource={labelListMap}
              />
            </FormItem>
          )}
          {!step && (
            <FormItem label="投放渠道：" {...formItemLayout}>
              <CascaderSelect
                name="channelList"
                multiple
                {...init("channelList")}
                dataSource={channelListMap}
              />
            </FormItem>
          )}
          {showPutIn && showVersion && (
            <FormItem label="投放版本号(仅针对app):" {...formItemLayout}>
              <Switch
                style={{ width: "130px" }}
                checked={channelVersionList.allVersion}
                checkedChildren="默认全版本"
                unCheckedChildren="不默认全版本"
                onChange={this.changeAllVersion}
              />
              {!channelVersionList.allVersion &&
                channelVersionList.versionGroup &&
                channelVersionList.versionGroup.versionList.map(
                  (item, index) => {
                    return (
                      <div style={{ width: "600px", marginBottom: "10px" }}>
                        <Select
                          placeholder="请选择"
                          style={{ width: "200px", marginRight: "5px" }}
                          name="operateType"
                          value={item.operateType}
                          dataSource={versionGroupSet}
                          onChange={(value) =>
                            this.changeVersion("operateType", value, index)
                          }
                        />
                        <Input
                          name="versionValue"
                          style={{ marginRight: "5px" }}
                          placeholder="请输入版本号(比如9.1,9.1.1)"
                          value={item.value}
                          onChange={(e) =>
                            this.changeVersion("value", e, index)
                          }
                        />
                        <Button
                          type={"primary"}
                          style={{ marginRight: "5px" }}
                          onClick={() => this.delVersion(index)}
                        >
                          -
                        </Button>
                        {index == 0 && (
                          <Button type={"primary"} onClick={this.addVersion}>
                            and
                          </Button>
                        )}
                      </div>
                    );
                  }
                )}
            </FormItem>
          )}
          {/*{!step && (*/}
          {/*  <FormItem label="投放标签:" {...formItemLayout}>*/}
          {/*    <Checkbox.Group*/}
          {/*      name="channelLabel"*/}
          {/*      {...init("channelLabel")}*/}
          {/*      dataSource={labelListMap}*/}
          {/*    />*/}
          {/*  </FormItem>*/}
          {/*)}*/}
          <FormItem label="投放城市：" {...formItemLayoutChannel}>
            {showGrid ? (
              <GridSelector
                name="citys"
                expandTriggerType="click"
                cityList={this.cityList}
                {...init("citys")}
                getSelectData={this.getSelectedGriddings}
              />
            ) : (
              <CitySelector
                name="citys"
                {...init("citys")}
                getSelectData={this.getSelectedCitys}
              />
            )}
          </FormItem>
          {(!PeopleList.includes(resourceType)) && (
            <FormItem
              className="user-tap-group-ids-wrap"
              label="投放人群："
              hasFeedback
              {...formItemLayoutChannel}
              validator={this.checkUserTagGroupIdsList}
            >
              <Select
                mode="multiple"
                showSearch
                placeholder="请输入用户群组ID，支持输入最多5个"
                name="userTagGroupIdsList"
                valueRender={this.renderUserTagGroupIdsList}
                value={userTagGroupIdsListValue ? userTagGroupIdsListValue : []}
                onChange={(value, actionType) =>
                  this.onChangeUserTagGroupIdsList(value, actionType)
                }
                onSearch={this.onSearchUserTagGroupIdsList}
                dataSource={userTagGroupIdsListSet}
                style={{ width: "100%" }}
              />
              <a
                className="to-create"
                href="https://boreas.kunlun.alibaba-inc.com/page/crm/index.html?from=header-v2#/crm/crowedSelect"
                target="_blank"
              >
                去创建
              </a>
            </FormItem>
          )}
          {PeopleList.includes(resourceType) && (
            <FormItem label="投放人群类型" {...formItemLayout}>
              <RadioGroup name="peopleType" value={peopleType}>
                <Radio value="1">订单用户身份</Radio>
                <Radio value="2">MUSE用户标签</Radio>
              </RadioGroup>
            </FormItem>
          )}
          {PeopleList.includes(resourceType) && peopleType == "1" && (
            <FormItem label="订单用户类型" {...formItemLayout}>
              <Checkbox.Group
                name="peopleOrderType"
                value={peopleOrderType}
                dataSource={peopleOrderTypeSource}
              />
            </FormItem>
          )}
          {PeopleList.includes(resourceType) && peopleType == "2" && (
            <FormItem
              className="user-tap-group-ids-wrap"
              label="MUSE人群标签"
              hasFeedback
              {...formItemLayoutChannel}
              validator={this.checkUserTagGroupIdsList}
            >
              <Select
                mode="multiple"
                showSearch
                placeholder="请输入用户群组ID，支持输入最多5个"
                name="userTagGroupIdsList"
                valueRender={this.renderUserTagGroupIdsList}
                value={userTagGroupIdsListValue ? userTagGroupIdsListValue : []}
                onChange={(value, actionType) =>
                  this.onChangeUserTagGroupIdsList(value, actionType)
                }
                onSearch={this.onSearchUserTagGroupIdsList}
                dataSource={userTagGroupIdsListSet}
                style={{ width: "100%" }}
              />
              <a
                className="to-create"
                href="https://boreas.kunlun.alibaba-inc.com/page/crm/index.html?from=header-v2#/crm/crowedSelect"
                target="_blank"
              >
                去创建
              </a>
            </FormItem>
          )}
          {((PeopleList.includes(resourceType) && eleCategorylist && eleCategorylist.length > 0) || (showShopList)) && (
            <FormItem label="门店类目" {...formItemLayout}>
              <CascaderSelect
                expandTriggerType={'hover'}
                autoFocus
                value={eleCategoryArray}
                dataSource={eleCategorylist}
                multiple={true} onChange={(value,data)=>this.changeEleCategory(value,data)}/>
            </FormItem>
          )}
          {!showPutIn && [
            <FormItem
              required={this.getValidate()}
              label="选品集ID"
              hasFeedback
              {...formItemLayout}
              requiredMessage="选品集ID不能为空"
              validator={this.checkDataSources}
            >
              <Select
                placeholder="请选择状态"
                value={limitType}
                style={{ width: "100%" }}
                name="limitType"
                dataSource={limitTypeSelectGroup}
              />
            </FormItem>,
            (limitType == 0 || limitType == 3) && (
              <FormItem
                label="选品集ID"
                required={this.getValidate()}
                hasFeedback
                {...formItemLayoutChannel}
                requiredMessage="选品集ID不能为空"
                validator={this.checkDataSourcess}
              >
                <Select
                  mode="multiple"
                  showSearch
                  placeholder="请输入选品集ID，支持输入最多20个"
                  name="poolIds"
                  value={poolIdsValue ? poolIdsValue : []}
                  onChange={(value, actionType) =>
                    this.onChange(value, actionType)
                  }
                  onSearch={this.onSearch}
                  dataSource={poolIdsSet}
                  style={{ width: "100%" }}
                />
                <a
                  className="to-create"
                  href="#/commoditypool/creation"
                  target="_blank"
                >
                  去创建
                </a>
              </FormItem>
            ),
            limitType == 1 && (
              <FormItem label=" " {...formItemLayout}>
                <RadioGroup name="poolType">
                  {pooTypeRadioRules.map((v) => {
                    return <Radio value={v.value}>{v.label}</Radio>;
                  })}
                </RadioGroup>
              </FormItem>
            ),
            <Form.Item
              {...formItemLayoutChannel}
              required={this.getValidate()}
              hasFeedback
              label="投放规则"
            >
              <Button
                size="small"
                style={{ marginRight: "10px" }}
                onClick={() => this.handleSetting()}
              >
                设置规则
              </Button>
            </Form.Item>,
            <FormItem label={<span />} {...formItemLayoutChannel}>
              <Button
                type="default"
                onClick={() => this.onChangeStep(1)}
                style={{ marginRight: "10px" }}
              >
                上一步
              </Button>
              {showConfigGroup && parseInt(step) < configGroup.length - 1 && (
                <Form.Submit
                  type="primary"
                  validate
                  disabled={this.props.submitLoading}
                  onClick={() => this.onChangeStep(2)}
                  style={{ marginRight: "10px" }}
                >
                  下一步
                </Form.Submit>
              )}
              {showDataConfig && (
                <Form.Submit
                  type="primary"
                  validate
                  disabled={this.props.submitLoading}
                  onClick={() => this.onSubmit()}
                >
                  {`${this.state.showQrCode ? "保存" : "发布"} `}
                </Form.Submit>
              )}
              {showConfigGroup && parseInt(step) == configGroup.length - 1 && (
                <Form.Submit
                  type="primary"
                  validate
                  disabled={this.props.submitLoading}
                  onClick={() => this.onSubmit()}
                >
                  {`${this.state.showQrCode ? "保存" : "发布"} `}
                </Form.Submit>
              )}
            </FormItem>,
          ]}
        </Form>
      );
    }
  }
}
