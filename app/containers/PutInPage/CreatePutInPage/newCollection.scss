
.next-overlay-wrapper .next-overlay-inner {
  z-index: 1001; 
}

.next-overlay-wrapper .next-overlay-backdrop {
  position: fixed;
  z-index: 1001;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  transition: opacity 300ms ease;
  opacity: 0; }

.next-overlay-wrapper.opened .next-overlay-backdrop {
  opacity: 1; }

.next-dialog[dir=rtl] {
  text-align: right; }
.next-dialog[dir=rtl] .next-dialog-footer.next-align-left {
  text-align: right; }
.next-dialog[dir=rtl] .next-dialog-footer.next-align-center {
  text-align: center; }
.next-dialog[dir=rtl] .next-dialog-footer.next-align-right {
  text-align: left; }
.next-dialog[dir=rtl] .next-dialog-btn + .next-dialog-btn {
  margin-right: 4px;
  margin-left: 0; }
.next-dialog[dir=rtl] .next-dialog-close {
  left: 20px;
  right: auto; }

.next-dialog {
  box-sizing: border-box;
  position: fixed;
  z-index: 1001;
  background: #FFFFFF;
  border: 1px solid #EBEBEB;
  border-radius: 2px;
  box-shadow: 0px 2px 10px 0px rgba(12, 13, 16, 0.08);
  text-align: left;
  overflow: hidden;
  animation-duration: 300ms;
  animation-timing-function: ease-in-out;
  max-width: 90%;
  /* 让用户自己设置 */
  /* &.next-closeable &-header, */
  /* &.next-closeable &-body, { */
  /*     padding-right: $dialog-part-padding-right-closeable; */
  /* } */ }
.next-dialog *,
.next-dialog *:before,
.next-dialog *:after {
  box-sizing: border-box; }
.next-dialog-header {
  padding: 20px 20px 12px 20px;
  border-bottom: 0 solid transparent;
  font-size: 16px;
  background: transparent;
  color: #363636; }
.next-dialog-body {
  padding: 20px 20px 12px 20px;
  font-size: 14px;
  color: #6B6B6B; }
.next-dialog-footer {
  padding: 20px 20px 20px 20px;
  border-top: 0 solid transparent;
  background: transparent; }
.next-dialog-footer.next-align-left {
  text-align: left; }
.next-dialog-footer.next-align-center {
  text-align: center; }
.next-dialog-footer.next-align-right {
  text-align: right; }
.next-dialog-footer-fixed-height {
  position: absolute;
  width: 100%;
  bottom: 0; }
.next-dialog-btn + .next-dialog-btn {
  margin-left: 8px; }
.next-dialog-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 16px;
  height: 16px;
  color: #6B6B6B;
  cursor: pointer; }
.next-dialog-close:link, .next-dialog-close:visited {
  height: 16px;
  color: #6B6B6B; }
.next-dialog-close:hover {
  background: transparent;
  color: #363636; }
.next-dialog-close .next-dialog-close-icon.next-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -6px;
  margin-left: -6px;
  width: 12px;
  height: 12px;
  line-height: 12px; }
.next-dialog-close .next-dialog-close-icon.next-icon:before {
  width: 12px;
  height: 12px;
  font-size: 12px;
  line-height: 12px; }
.next-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  padding: 40px;
  overflow: auto;
  text-align: center;
  box-sizing: border-box; }
.next-dialog-container:before {
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 100%;
  content: ''; }
.next-dialog-container .next-dialog {
  display: inline-block;
  position: relative;
  vertical-align: middle; }
.next-dialog-quick .next-dialog-body {
  padding: 20px 20px 20px 20px; }
.next-dialog .next-dialog-message.next-message {
  min-width: 300px;
  padding: 0; }

.next-dialog-close:link, .next-dialog-close:visited {
  height: 16px;
  color: #999999;
}
.next-dialog-close:hover {
  background: transparent;
  color: #333333;
}
.next-dialog-close .next-dialog-close-icon.next-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: calc(0px - 12px / 2);
  margin-left: calc(0px - 12px / 2);
  width: 12px;
  height: 12px;
  line-height: 1em;
}
.next-dialog-close .next-dialog-close-icon.next-icon:before {
  width: 12px;
  height: 12px;
  font-size: 12px;
  line-height: 1em;
}
.next-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
  padding: 40px;
  overflow: auto;
  text-align: center;
  box-sizing: border-box;
}
.next-dialog-container:before {
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 100%;
  content: "";
}
.next-dialog-container .next-dialog {
  display: inline-block;
  position: relative;
  vertical-align: middle;
}
.next-dialog-quick .next-dialog-body {
  padding: 20px 20px 20px 20px;
}
.next-dialog .next-dialog-message.next-message {
  min-width: calc(100px * 3);
  padding: 0;
}

.next-dialog-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow: auto;
}
.next-dialog-inner-wrapper {
  display: flex;
  position: relative;
  top: 100px;
  pointer-events: none;
  padding-bottom: 24px;
}
.next-dialog-v2 {
  pointer-events: auto;
  margin: 0 auto;
}
.next-dialog-v2 .next-dialog-header {
  word-break: break-word;
  padding-right: calc(12px * 2 + 16px);
}
.next-dialog-v2 .next-dialog-body {
  padding-right: calc(12px * 2 + 16px);
}
.next-dialog-v2 .next-dialog-header + .next-dialog-body {
  padding: 20px 20px 20px 20px;
}
.next-dialog-v2 .next-dialog-header + .next-dialog-body-no-footer {
  margin-bottom: 0px;
}
.next-dialog.next-dialog-v2 {
  position: relative;
}
.next-dialog-centered {
  text-align: center;
}
.next-dialog-centered::before {
  display: inline-block;
  width: 0;
  height: 100%;
  vertical-align: middle;
  content: "";
}
.next-dialog-centered .next-dialog-v2 {
  margin: 40px 0;
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}

.btn-time-group{
  margin: 10px 10px 0 0;
}
