/**
 * putInReducer
 * 投放设置页面
 */

import {fromJS} from 'immutable';

import {
  UPDATE_FIRST_FIELDS,
  UPDATE_SECOND_FIELDS,
  UPDATE_RECALL_RULES,
  UPDATE_REPEAT_RULES,
  UPDATE_SORT_RULES,
  DATA_LOADED,
  BLANK_DATA,
  BLANK_TEMPLATE,
  SET_POOL_TYPE,
  UPDATE_DISTRIBUTE_RULES,
  UPDATE_FILTER_RULES,
  UPDATE_TEMPLATE
} from './constants';

import { combineReducer } from '@/utils/combineReducer';
import { switcher, branch  } from '@/utils/switcher';
import { defaultDetail, formDetail, defaultRulesTemplate } from '../constants';
import { updateStoreByKey } from '@/utils/others'
/**编辑 state */
const initialState = fromJS(formDetail(defaultDetail));

/**模版 state */
const rulesTemplateState = fromJS(defaultRulesTemplate)

/**
 *reducers
 */

/**初始化 编辑 页面数据 */
function initPutInDetail(state, action) {
  const { initState } = action;
  return fromJS(initState);
}

/**清空 数据 */
function blankData() {
  return fromJS(formDetail(defaultDetail))
}

/** 更新第一页数据 */

const formalFirstPage = function(state, value){
  return switcher(
    // branch('activityTime', (key) => state.setIn(['firstPage', key], List(value))),
    branch(() => true, (key) => state.setIn(['firstPage', key], value))
  )
}

function updateFirstPage(state, action){
  const updateByKey = updateStoreByKey(state, action);

  return updateByKey((state, action, key) => {
    const setValByKey = formalFirstPage(state, action[key]);
    return setValByKey(key);
  })
}

/**更新数组函数 */
const updateList = function(keys){
  return function (state, value){
    return function(index) {
      return state.updateIn(keys, list => list.set(index, value));
    }
  }
}

/**
 * 更新recallRules数据
 * @param {Object} state
 * @param {String} action
 */
const updateDistance = updateList(['secondPage', 'recallRules', 'storeDistance']);
const updateGoodsOriginalPrice = updateList(['secondPage', 'recallRules', 'goodsOriginalPrice']);
const updateGoodsPresentPrice = updateList(['secondPage', 'recallRules', 'goodsPresentPrice']);
const updateSkuDiscount = updateList(['secondPage', 'recallRules', 'skuDiscount']);
const updateShopScore = updateList(['secondPage', 'recallRules', 'shopScore']);


const formalRecall = function(state, value){
  const setDistance = updateDistance(state, value);
  const setShopScore  = updateShopScore(state, value);
  const setGoodsOriginalPrice = updateGoodsOriginalPrice(state, value);
  const setGoodsPresentPrice = updateGoodsPresentPrice(state, value);
  const setSkuDiscount = updateSkuDiscount(state, value);
  return switcher(
    branch('minStoreDistance', () => setDistance(0)),
    branch('maxStoreDistance', () => setDistance(1)),
    // 拆分店铺评分 【1.11， 2.22】 => 1.11  2.22
    branch('minShopScore', () => setShopScore(0)),
    branch('maxShopScore', () => setShopScore(1)),
    branch('minGoodsOriginalPrice', () => setGoodsOriginalPrice(0)),
    branch('maxGoodsOriginalPrice', () => setGoodsOriginalPrice(1)),
    branch('minGoodsPresentPrice', () => setGoodsPresentPrice(0)),
    branch('maxGoodsPresentPrice', () => setGoodsPresentPrice(1)),
    branch('minSkuDiscount', () => setSkuDiscount(0)),
    branch('maxSkuDiscount', () => setSkuDiscount(1)),
    branch(() => true, (type) => state.setIn(['secondPage', 'recallRules', type], value))
  )
}

function updateRecallRules(state, action){
  const updateByKey = updateStoreByKey(state, action);
  return updateByKey((state, action, key) => {
    const setValByKey = formalRecall(state, action[key]);
    return setValByKey(key);
  })
}

/**
 * 更新配送规则
 * @param {*} state
 * @param {*} action
 */
const updateDistribution = updateList(['secondPage', 'deliveryRules', 'distributionFee']);
const updateDistributionFull = updateList(['secondPage', 'deliveryRules', "distributionFullDecrementAmount"]);

const formalDistribute = function(state, value){
  const setDistribution = updateDistribution(state, value);
  const setDistributionFull = updateDistributionFull(state, value);

  return switcher(
    branch('minDistribution', () => setDistribution(0)),
    branch('maxDistribution', () => setDistribution(1)),
    branch('minThreshold', () => setDistributionFull(0)),
    branch('maxThreshold', () => setDistributionFull(1)),
    branch(() => true, (type) => state.setIn(['secondPage', 'deliveryRules', type], value))
  )
}

function updateDistributeRules(state, action){
  const updateByKey = updateStoreByKey(state, action);

  return updateByKey((state, action, key) => {
    const setValByKey = formalDistribute(state, action[key]);
    return setValByKey(key);
  })
}

/**
 * 更新过滤规则
 * @param {*} state
 * @param {*} action
 */

function updateFilterRules(state, action){
  const updateByKey = updateStoreByKey(state, action);
  return updateByKey((state, action, key) => state.setIn(['secondPage', 'filterRules', key], action[key]))
}

/**
 * 更新repeatRules数据
 * @param {Object} state
 * @param {String} action
 */
function updateRepeatRules(state, action){
  const updateByKey = updateStoreByKey(state, action);

  return updateByKey((state, action, key) => state.setIn(['secondPage', 'repeatRules', key], action[key]))
}

/**
 * 更新排序类型数据
 */
function updateSortRules(state, action){
  const updateByKey = updateStoreByKey(state, action);
  return updateByKey((state, action, key) => {
    if (key != 'sortRules') {
      // 排序类型如果存在，则不赋初始值，报错直接赋初始值
      try{
        // 判断是初始化还是手动更改
        if (state.get('secondPage').get('skuSortRules') && action.type != 'update_sort_rules') {
          return state.setIn(['secondPage', key],state.get('secondPage').get('skuSortRules'))
        }else{
          return state.setIn(['secondPage', key], +action[key])
        }
      }catch{
        return state.setIn(['secondPage', key], action[key])
      }
    } else {
      return state.setIn(['secondPage', key], action[key])
    }
  })
}

/**存储 poolIds*/
function updatePoolType(state, action) {
  const { poolType, storeSortRules = 0, skuSortRules = 0 } = action;
  console.log(action, 'action')
  let newState = state
    .set('poolType', poolType)
    .updateIn(['secondPage','filterRules','stockExhausted'], () => poolType == 1 ? true : false)

  /**
   * poolType: 1商品  2门店
   * 对商品和门店规则进行初始化
   */
  if(poolType == 1 && skuSortRules == 0) {
    /**
     * skuSortRules -- 基础规则 商品算法排序-点击率最高
     * categoryScatter -- 商品品类打散
     * upcScatter -- UPC打散
     */
    newState = updateSortRules(newState, { type:'', skuSortRules: 3,
      sortRules: {categoryScatter: 1, upcScatter: 1, catScatterLevel: 2}
    })
  } else if(poolType == 2 && storeSortRules == 0) {
    newState = updateSortRules(newState, { type:'', storeSortRules:4 })
  }

  return newState;
}

/**选择模版，更新第二页面 */
function updateSecondPage(state, action) {
  const { secondPage } = action;
  return state.set('secondPage', fromJS(secondPage));
}

/**edit rules reducer */
export const detailReducer = combineReducer(initialState, {
  [UPDATE_FIRST_FIELDS]: updateFirstPage,
  [UPDATE_SECOND_FIELDS]: updateSecondPage,
  [UPDATE_RECALL_RULES]: updateRecallRules,
  [UPDATE_REPEAT_RULES]: updateRepeatRules,
  [UPDATE_SORT_RULES]: updateSortRules,
  [UPDATE_DISTRIBUTE_RULES]: updateDistributeRules,
  [UPDATE_FILTER_RULES]: updateFilterRules,
  [BLANK_DATA]: blankData,
  [DATA_LOADED]: initPutInDetail,
  [SET_POOL_TYPE]: updatePoolType
})

/**
 * rules template
 */

/**更新模版信息 */
function updateTemplate(state, action) {
  const key = Object.keys(action)[1];
  return state.set(key, action[key])
}

/**清空模版 */
function blankTemplate() {
  return fromJS(defaultRulesTemplate)
}

/**template reducer */
export const templateReducer = combineReducer(rulesTemplateState, {
  [UPDATE_TEMPLATE]: updateTemplate,
  [BLANK_TEMPLATE]: blankTemplate
})


