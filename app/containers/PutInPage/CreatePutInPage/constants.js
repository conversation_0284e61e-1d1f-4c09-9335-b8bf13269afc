/**action types */
const UPDATE_FIRST_FIELDS = 'update_first_fields';
const UPDATE_SECOND_FIELDS = 'update_second_fields';
const UPDATE_RECALL_RULES = 'update_recall_rules';
const UPDATE_REPEAT_RULES = 'update_repeat_rules';
const UPDATE_SORT_RULES = 'update_sort_rules';
const UPDATE_DISTRIBUTE_RULES = 'update_distribute_rules';
const UPDATE_FILTER_RULES = 'update_filter_rules';
const UPDATE_TEMPLATE = 'update_template';

const DATA_LOADED = 'data_loaded';
const INIT_DATA = 'init_data';
const BLANK_DATA = 'blank_data';
const BLANK_TEMPLATE = 'blank_template';
const SET_POOL_TYPE = 'set_pool_type';
const GET_POOL_TYPE = 'get_pool_type';



/** form settings */
const formItemLayout = {
  labelCol: { span: 7},
  wrapperCol: {
    span: 11
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const formItemLayoutChannel = {
  labelCol: { span: 6},
  wrapperCol: {
    span: 18
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};


const formLayout = {
  style: {
    margin: '20px 0px'
  },
}

/**rules form settings */
const rulesSettingItemLayout = {
  ...formItemLayout,
  labelCol: { span: 7 },
  wrapperCol: {
    span: 15
  }
}

export {
  UPDATE_FIRST_FIELDS,
  UPDATE_SECOND_FIELDS,
  UPDATE_RECALL_RULES,
  UPDATE_REPEAT_RULES,
  UPDATE_SORT_RULES,
  UPDATE_TEMPLATE,
  INIT_DATA,
  BLANK_DATA,
  BLANK_TEMPLATE,
  DATA_LOADED,
  SET_POOL_TYPE,
  GET_POOL_TYPE,
  UPDATE_DISTRIBUTE_RULES,
  UPDATE_FILTER_RULES,
  formItemLayout,
  formLayout,
  formItemLayoutChannel,
  rulesSettingItemLayout
}
