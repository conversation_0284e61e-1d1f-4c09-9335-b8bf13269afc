import { createSelector } from 'reselect';

const selectPutIn = (state) => state.get('putInForm')
const selectRulesTemplate = (state) => state.get('rulesTemplate')

// /**获取第一页数据 */
// const selectFormData = () => createSelector(
//   selectPutIn,
//   (formData) => formData.get('firstPage')
// )

// /**获取第二页数据 */
// const selectRules = () => createSelector(
//   selectPutIn,
//   (formData) => formData.get('secondPage')
// )

/**获取 第一层级数据 */
const selectDataByKey = (key) => createSelector(
  selectPutIn,
  (formData) => formData.get(key)
)

/**获取poolIds */
const getPoolIds = () => createSelector(
  selectPutIn,
  (formData) => formData.getIn(['firstPage', 'poolIds'])
)

/**获取weightPoolIds */
const getWeightPoolIds = () => createSelector(
  selectPutIn,
  (formData) => formData.getIn(['firstPage', 'weightPoolIds'])
)

/**获取 rules template */
const getRulesTemplate = () => createSelector(
  selectRulesTemplate,
  (template) => template
)

export {
  selectPutIn,
  selectDataByKey,
  getPoolIds,
  getWeightPoolIds,
  getRulesTemplate,
  selectRulesTemplate
}

