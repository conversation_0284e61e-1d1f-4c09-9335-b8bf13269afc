import React, {Fragment} from "react";
import clz from "classnames";
import { Message } from '@alife/next';
import { filterData } from "@/utils/filterDataByKey";
import { PageSection } from "@/components/PageWrapper";
import {
  psoriasisPicFilterRules,
  bodyAreaRatioRules,
  commodityRankRules,
  storeRankRules,
  openStatusRadio,
  pooTypeRadioRules, ShowCollectionPoolId,
} from "../constants";
import { TableHeader, HeadLeftTable } from "./Table";
import { fillBlank } from "@/utils/others";
import { formatTimeStamp } from "@/utils/time";

import "./index.scss";
import { reject } from "core-js/fn/promise";
import {Button} from "@alife/next";
import {getPoolList} from "@/adator/api";
import {onRequestError} from "@/utils/api";


const skipDetail = async (id,poolType) =>{
  try {
    let res = await getPoolList({
      pageIndex: 1,
      pageSize: 10,
      poolId: id,
      rangeType: 0
    });
    if(res.data.length>0) {
      let {newPlatformFlag, sourcePoolId, basePoolName, poolType, state} = res.data[0];
      if (state != '25') {
        if (newPlatformFlag === 0) {
          let dataType = (parseInt(sourcePoolId) >= 20001) ? 1 : 2;
          location.href = `#/${poolType == 1 ? 'commoditypool' : 'storepool'}/list/detail/${id}?basePoolId=${sourcePoolId}&basePoolName=${basePoolName}&dataType=${dataType}`;
        } else {
          location.href = `#/pool/list/detail/${id}?isSelectedStore=${poolType == 1 ? false : true}`;
        }
      } else {
        Message.warning('池子已下线');
      }
    }else{
      Message.warning('池子不存在');
    }
  } catch (error) {
    onRequestError(error)
  }
}

/**常规池id列 */
const DetailPoolIdsCol = ({record,ids}) => {
  if (ids && ids.length > 0) {
    let selectonIds = ids.filter((v) => v.dataSourceType == '1');
    let investIds = ids.filter((v) => v.dataSourceType == '2');
    let promoteType = ids.filter((v) => v.dataSourceType == '3');
    let promoteIds = ids.filter((v) => v.dataSourceType == '4');
    console.log(promoteIds);
    let showSelection = selectonIds.length > 0 && selectonIds[0].idList && selectonIds[0].idList.length > 0;
    let showInvest = (investIds.length > 0 && investIds[0].idList && investIds[0].idList.length > 0);
    let showPromoteType = (promoteType.length > 0 && promoteType[0].idList);
    let showPromoteId = (promoteIds.length > 0 && promoteIds[0].marketActivityIds);
    let selectionLabel = (showSelection) && <strong>选品池：</strong>;
    let investLabel = (showInvest) && <strong>招商池：</strong>;
    let promoteIdLabel = (showPromoteId) && <strong>营销活动ID：</strong>;
    let promoteTypeLabel = (showPromoteType) && <strong>营销活动类型：</strong>;
    let idsReqs1 = showSelection ? selectonIds[0].idList.map((v,i) => {
      return <a className="group—id" href='javascript:void(0)'
                onClick={() => skipDetail(v.value, record.poolType)}>{i!=0?',':''}{v.value}</a>
    }) : '';
    let idsReqs2 = showInvest ? investIds[0].idList.map((v,i) => {
      return <a className="group—id"
                href={`https://nr.ele.me/op-fe/kunlun-zs-landing/#/landing-page/activity-detail-from-alpha?activityId=${v.value}`}
                target={'_blank'}>{i!=0?',':''}{v.value}</a>
    }) : '';
    let idsReqs3 = showPromoteType ? promoteType[0].idList.map((v) => v.label).join(","):'';
    let idsReqs4 = showPromoteId ? <span>{promoteIds[0].marketActivityIds}</span> : '';
    return (
      <Fragment>{selectionLabel}{idsReqs1}{showInvest && <br/>}{investLabel}{idsReqs2}{showPromoteType && <br/>}{promoteTypeLabel}{idsReqs3}{showPromoteId && <br/>}{promoteIdLabel}{idsReqs4}
      </Fragment>);
  }else{
    return <Fragment>---</Fragment>;
  }
}

const dataSourceCols = [
  { title: "投放渠道", dataIndex: "deliveryChannelList" },
  { title: "投放标签", dataIndex: "deliveryChannelLabel" },
  { title: "投放城市", dataIndex: "citysRep" },
  { title: "投放人群", dataIndex: "userTagGroupIdsList" },
  {
    title: "加权池ID", dataIndex: "weightCollection", cell: (value, record) => {
      return <DetailPoolIdsCol record={record} ids={value} />
      // return ShowCollectionPoolId(record,'1');
    }
  },
  {
    title: "常规池ID", dataIndex: "commonCollection", cell: (value, record) => {
      return <DetailPoolIdsCol record={record} ids={value} />
      // return <DetailPoolIdsCol record={record} ids={value} />
    },
  },
  { title: "是否开启算法精品池推荐", dataIndex: "isRecommend" },
  { title: "商品分类方式", dataIndex: "categoryType" },
  { title: "商品类目", dataIndex: "categoryList" },
  { title: "类目挂靠商品准确性", dataIndex: "isCatComp" },
  { title: "风控合规数据", dataIndex: "riskCompLevel" },
  { title: "门店主营类目", dataIndex: "mainCates" },
  { title: "活动时间", dataIndex: "activityTime" },
];

// 查看页面
const recallRulesCols = [
  { title: "门店分类", dataIndex: "storeType" },
  { title: "店铺距离", dataIndex: "storeDistance" },
  { title: "店铺评分", dataIndex: "shopScore" },
  { title: "召回快递门店", dataIndex: "isNationalDelivery" },
  // { title: "最低数量", dataIndex: "minNum" },
  { title: "门店营业状态", dataIndex: "openStatusSet" },
  { title: "吃货联盟", dataIndex: "eatUnionTag" },
  { title: "商品所属品牌", dataIndex: "itemBrandList" },
  { title: "门店所属品牌", dataIndex: "shopBrands" },
  { title: "商家主营类目", dataIndex: "shopMajorCategory" },
];

const deliveryRulesCols = [
  { title: "配送费用", dataIndex: "distributionFee" },
  { title: "配送满减门槛金额", dataIndex: "distributionFullDecrementAmount" },
  { title: "蜂鸟专送", dataIndex: "hummingBird" },
  { title: "准时达", dataIndex: "onTime" },
  // { title: "店铺免配", dataIndex: "freeDeliveryStore" },
];

const filterRulesCols = [
  { title: "商品总库存告罄过滤", dataIndex: "stockExhausted" },
  { title: "无图商品过滤", dataIndex: "goodsWithoutPic" },
  { title: "无品牌商品过滤", dataIndex: "brandGoodsFilter" },
  { title: "含牛皮癣图片商品", dataIndex: "goodsFilterWithPsoriasisPic" },
  { title: "营销活动商品筛选", dataIndex: "mktActivityTypeList" },
  { title: "预售商品过滤", dataIndex: "presaleGoodsFilter" },
  { title: "商品活动价格", dataIndex: "priceFilter" },
  { title: "是否透明底图", dataIndex: "transparentBasePic" },
  { title: "是否白底图", dataIndex: "whiteBasePic" },
  { title: "商品主体面积占比", dataIndex: "goodsBodyAreaRatio" },
];

const commodityDeRepeatCol = [
  { title: "门店去重", dataIndex: "repeatRules" },
  { title: "商品三级类目去重", dataIndex: "thirdGoodsCategory" },
  { title: "商品名称去重", dataIndex: "goodsNameRepeat" },
  { title: "一个门店内最多透出商品数量", dataIndex: "storeRepeat" },
  { title: "同品牌商品去重", dataIndex: "repeatWithBrand" },
  { title: "同品牌折叠", dataIndex: "foldingWithBrand" },
  { title: "UPC聚合", dataIndex: "upcAggregation" },
  { title: "同一个UPC最大可召回数量", dataIndex: "upcRecallCount" },
];

const storeDeRepeatCol = [
  { title: "同品牌折叠", dataIndex: "foldingWithBrand" },
];

const typeMap = {
  1: "商品优惠加权",
  2: "门店优惠加权",
  3: "品类加权",
};

const filterSkuSortRulesCols = [
  { title: "优惠排序因子", dataIndex: "discountRankFactor" },
  { title: "商品品类打散", dataIndex: "categoryScatter" },
  { title: "UPC打散", dataIndex: "upcScatter" },
  { title: "类目", dataIndex: "catScatterLevel" },
  // { title:'同品牌店铺打散', dataIndex: 'brandShopScatter'},
  // { title: '三级类目打散', dataIndex: 'levelCategoryScatter'}
];

const filterStoreSortRulesCols = [
  { title: "优惠排序因子", dataIndex: "shopWeightingType" },
  // { title: '同品牌店铺打散', dataIndex: 'brandShopScatter' },
  // { title: '三级类目打散', dataIndex: 'levelCategoryScatter' }
];

/**匹配channelList  */
const handleChannelList = (channelListMap, channelList) => {
  channelListMap.forEach((channelItem) => {
    channelList[channelItem.value] = channelItem.label;
    if (channelItem.subChannels) {
      handleChannelList(channelItem.subChannels, channelList);
    }
  });
};

/**格式化数据源 */
const formatSource = (data) => {
  const {
    poolType,
    deliveryChannelList,
    deliveryChannelLabel,
    weightPoolIdsRep,
    commonCollection,
    weightCollection,
    poolIdsRep,
    limitType,
    citysRep,
    griddingsRep,
    startTime,
    endTime,
    userTagGroupIdsList,
    isCatComp,
    riskCompLevel,
    isRecommend,
    categoryType,
  } = data;
  console.log('formatSource',formatSource);
  let poolIdsRepText = "---";
  let weightPoolIdsRepText = "---";
  let { channelListMap, labelListMap } = JSON.parse(
    localStorage.getItem("deliveryData")
  );
  if (!limitType) {
    poolIdsRepText = fillBlank(poolIdsRep, String(poolIdsRep));
    weightPoolIdsRepText = fillBlank(weightPoolIdsRep, String(weightPoolIdsRep));
  } else if (limitType == 1 || limitType == 2) {
    poolIdsRepText = pooTypeRadioRules.filter(
      (item) => item.value == poolType
    )[0].label;
    weightPoolIdsRepText = pooTypeRadioRules.filter(
      (item) => item.value == poolType
    )[0].label;
  }

  let newCitysRep = [];
  if (citysRep && citysRep.length > 0) {
    newCitysRep = [...newCitysRep, ...citysRep];
  }
  if (griddingsRep && griddingsRep.length > 0) {
    newCitysRep = [...newCitysRep, ...griddingsRep];
  }

  const channelList = {};
  handleChannelList(channelListMap, channelList);

  let { itemCatList, rejectItemCatList } = data;
  if (!itemCatList) {
    itemCatList = [];
  }
  if (!rejectItemCatList) {
    rejectItemCatList = [];
  }

  let categoryList = [];
  if (itemCatList.length) {
    categoryList = itemCatList.slice();
  }
  else if (rejectItemCatList.length) {
    categoryList = rejectItemCatList.slice();
  }

  let { mainCates } = data;
  if (!mainCates) {
    mainCates = [];
  }

  return {
    deliveryChannelList: fillBlank(deliveryChannelList, () =>
      deliveryChannelList
        .map((channelItem) => channelList[channelItem])
        .join(" , ")
    ),
    deliveryChannelLabel: fillBlank(deliveryChannelLabel, () =>
      labelListMap
        .filter((item) => deliveryChannelLabel.indexOf(item.value) >= 0)
        .map((item) => item.label)
        .join(" , ")
    ),
    commonCollection,
    weightCollection,
    weightPoolIdsRep: weightPoolIdsRepText,
    poolIdsRep: poolIdsRepText,
    citysRep: fillBlank(newCitysRep, () =>
      newCitysRep.map((item) => item["label"]).join(",")
    ),
    activityTime: `${formatTimeStamp(startTime)} ~ ${formatTimeStamp(endTime)}`,
    userTagGroupIdsList: fillBlank(
      userTagGroupIdsList,
      String(userTagGroupIdsList)
    ),
    isRecommend: isRecommend == 0 ? '否' : '是',
    categoryType: itemCatList.length > 0 ? '指定商品分类' : '剔除商品分类',
    categoryList: categoryList.map(categoryItem => categoryItem.label).join(','),
    isCatComp: isCatComp == 0 ? '全部' : '合格',
    riskCompLevel: riskCompLevel == 1 ? '普通等级' : '严格等级',
    mainCates: mainCates.length > 0 ? mainCates.map(cateItem => cateItem.label).join(',') : '无'
  };
};

/**格式化配送规则 */
const formDeliveryData = (data) => {
  let {
    onTime,
    hummingBird,
    distributionFee,
    // freeDeliveryStore,
    distributionFullDecrementAmount = [],
  } = data;
  if (!distributionFullDecrementAmount) {
    distributionFullDecrementAmount = [];
  }
  const [minFee, maxFee] = distributionFee;
  const [minThreshold, maxThreshold] = distributionFullDecrementAmount;
  return {
    onTime: onTime ? "开启" : "关闭",
    // freeDeliveryStore: freeDeliveryStore ? "开启" : "关闭",
    hummingBird: hummingBird ? "开启" : "关闭",
    distributionFee: fillBlank(distributionFee, `${minFee}~${maxFee}元`),
    distributionFullDecrementAmount: fillBlank(
      distributionFullDecrementAmount,
      `${minThreshold}~${maxThreshold}`
    ),
  };
};

/**格式化召回规则 */
const formatRecall = (data) => {
  const {
    storeDistance,
    shopScore,
    minNum,
    isNationalDelivery,
    status,
    storeTypeRep,
    shopMajorCategory,
    eatUnionTag,
    itemBrandList,
    itemBrandListContent = [],
    shopBrands = "",
    openStatusSet,
    goodsCategory,
    shopMajorCategoryList = [],
    eatUnionTagList = []
  } = data;
  const [minDistance, maxDistance] = storeDistance;
  const [minShopScore, maxShopScore] = shopScore;
  // const [ minStoreDistance, maxStoreDistance ] = distributionFee;


  const shopMajorCategoryReg = (shopMajorCategory && shopMajorCategory.length > 0) ? shopMajorCategory.map((x) => {
    const item = shopMajorCategoryList.find((e) => e.code === x)
    return {
      label: item.name,
      value: item.code
    }
  }) : [];

  const eatUnionTagReg = (eatUnionTag && eatUnionTag.length > 0) ? eatUnionTag.map((x) => {
    const item = eatUnionTagList.find((e) => e.code === x)
    return {
      label: item.name,
      value: item.code
    }
  }) : [];

  const itemBrandListContentReg = (itemBrandListContent && itemBrandListContent.length > 0) ? itemBrandList.map((x) => {
    const item = itemBrandListContent.find((e) => e.code === x)
    return {
      label: item.name,
      value: item.code
    }
  }) : [];

  const recallData = {
    ...data,
    shopBrands,
    storeType: fillBlank(storeTypeRep, () =>
      storeTypeRep.map((item) => item["label"]).join(",")
    ),

    shopMajorCategory: fillBlank(shopMajorCategoryReg, () =>
      shopMajorCategoryReg.map((item) => item["label"]).join(",")
    ),
    eatUnionTag: fillBlank(eatUnionTagReg, () =>
      eatUnionTagReg.map((item) => item["label"]).join(",")
    ),

    itemBrandList:fillBlank(itemBrandListContentReg, () =>
     itemBrandListContentReg.map((item) => item["label"]).join(", ")
    ),

    storeDistance: fillBlank(storeDistance, `${minDistance}~${maxDistance}米`),
    shopScore: fillBlank(shopScore, `${minShopScore}~${maxShopScore}分`),
    minNum: fillBlank(minNum, `${+minNum}个`),
    status: status ? "开启" : "关闭",
    isNationalDelivery: isNationalDelivery ? '开启' : '关闭',
    openStatusSet: fillBlank(openStatusSet, () =>
      openStatusRadio
        .filter((item) => openStatusSet.indexOf(item.value) >= 0)
        .map((item) => item.label)
        .join(" , ")
    ),
  };
  if (goodsCategory) {
    recallData.goodsCategory = goodsCategory
      .map((categoryItem) => categoryItem.label)
      .join(", ");
  }
  // if (goodsActivitiesType) {
  //   recallData.goodsActivitiesType = goodsActivitiesType.map((activityItem) => activityItem.label).join(', ');
  // }
  // if (goodsOriginalPrice) {
  //   recallData.goodsOriginalPrice = goodsOriginalPrice.join(' - ') + ' 元';
  // }
  // if (goodsPresentPrice) {
  //   recallData.goodsPresentPrice = goodsPresentPrice.join(' - ') + ' 元';
  // }
  // if (skuDiscount) {
  //   recallData.skuDiscount = skuDiscount.join(' - ') + ' 元';
  // }
  return recallData;
};

/**格式化 过滤规则*/
const formatFilter = (data) => {
  const {
    stockExhausted,
    activityStockExhusted,
    goodsWithoutPic,
    brandGoodsFilter,
    presaleGoodsFilter,
    mktActivityTypeList,
    mktActivityTypeRepList,
    goodsFilterWithPsoriasisPic,
    transparentBasePic,
    whiteBasePic,
    goodsBodyAreaRatio,
    priceFilter,
  } = data;
  return {
    stockExhausted: stockExhausted ? "已开启" : "已关闭",
    activityStockExhusted: activityStockExhusted ? "已开启" : "已关闭",
    goodsWithoutPic: goodsWithoutPic ? "已开启" : "已关闭",
    brandGoodsFilter: brandGoodsFilter ? "已开启" : "已关闭",
    presaleGoodsFilter: presaleGoodsFilter ? "已开启" : "已关闭",
    goodsFilterWithPsoriasisPic:
      goodsFilterWithPsoriasisPic == "" ||
        goodsFilterWithPsoriasisPic.length == 0
        ? "---"
        : psoriasisPicFilterRules
          .filter((item) => goodsFilterWithPsoriasisPic.includes(item.value))
          .map((item) => item.label)
          .join(" , "),
    transparentBasePic: transparentBasePic ? "已开启" : "已关闭",
    whiteBasePic: whiteBasePic ? "已开启" : "已关闭",
    goodsBodyAreaRatio:
      goodsBodyAreaRatio == null
        ? "---"
        : bodyAreaRatioRules
          .filter((item) => item.value == goodsBodyAreaRatio)
          .map((item) => item.label)
          .join(" , "),
    mktActivityTypeList:
      mktActivityTypeRepList && mktActivityTypeRepList.length > 0
        ? mktActivityTypeRepList.map((v) => v.label).join("，")
        : "",
    priceFilter:
      priceFilter && priceFilter.length > 1
        ? `${priceFilter[0]}~${priceFilter[1]}元`
        : "",
  };
};

/**格式化 过滤规则*/
const formatSortRuleFilter = (data) => {
  if (data) {
    const {
      discountRankFactor,
      categoryScatter,
      upcScatter,
      shopWeightingType,
      goodsCategoryList,
    } = data;
    return {
      discountRankFactor: `${typeMap[discountRankFactor] || "已关闭"}`,
      categoryScatter: categoryScatter == 1 ? "已开启" : "已关闭",
      shopWeightingType: shopWeightingType == 1 ? "已开启" : "已关闭",
      upcScatter: upcScatter == 1 ? "已开启" : "已关闭",
      goodsCategoryList: goodsCategoryList
        ? goodsCategoryList.map((categoryItem) => categoryItem.label).join(", ")
        : "无",
      // brandShopScatter: (brandShopScatter == 1) ? '已开启' : '已关闭',
      // levelCategoryScatter: (levelCategoryScatter == 1) ? '已开启' : '已关闭'
    };
  }
};

/**格式化 重复规则 */
const formDeRepeatData = (data) => {
  const {
    storeRepeat,
    goodsNameRepeat,
    storeRepeatCommodityNum,
    minStoreRepeatCommodityNum,
    foldingWithBrand,
    thirdGoodsCategory,
    upcAggregation,
    repeatWithBrand,
    upcRecallCount
  } = data;

  return {
    repeatRules: storeRepeat
      ? `开启：一个门店下商品数量限制${minStoreRepeatCommodityNum}-${storeRepeatCommodityNum}个`
      : "关闭",
    thirdGoodsCategory: thirdGoodsCategory? "开启":"关闭",
    goodsNameRepeat: goodsNameRepeat ? "开启" : "关闭",
    storeRepeat: storeRepeat ? "开启" : "关闭",
    foldingWithBrand: foldingWithBrand ? "开启" : "关闭",
    upcAggregation: upcAggregation ? "开启" : "关闭",
    repeatWithBrand: repeatWithBrand ? "开启" : "关闭",
    upcRecallCount: upcAggregation ? upcRecallCount : '无'
  };
};

/**格式化排序规则 */
const formRankRules = (data, type) => {
  const rulesType = {
    1: "skuSortRules",
    2: "storeSortRules",
  };

  const renderRules = {
    1: commodityRankRules,
    2: storeRankRules,
  };

  const rulesVal = data[rulesType[type]];
  const rankRules = renderRules[type].filter((item) => item.value == rulesVal);
  return {
    rankRules: fillBlank(rankRules, () => rankRules[0]["label"]),
  };
};

/**
 * 0  commodity
 * 1  store
 * */
export const DetailList = ({ className, detail = {} }) => {

  /**模块数据处理 */

  console.log("detail",detail);
  const getDataBy = filterData(detail);
  const type = detail.poolType;
  const withDataSource = className !== "preview-detail";
  const sourceData = withDataSource
    ? getDataBy(
      [
        "poolType",
        "poolIdsRep",
        'commonCollection',
        'weightCollection',
        "weightPoolIdsRep",
        "limitType",
        "deliveryChannelList",
        "deliveryChannelLabel",
        "citysRep",
        "griddingsRep",
        "startTime",
        "endTime",
        "userTagGroupIdsList",
        "isCatComp",
        "categoryList",
        "riskCompLevel",
        "mainCates",
        "categoryType",
        "isRecommend",
        "itemCatList",
        "rejectItemCatList"
      ],
      formatSource
    )
    : "";
  console.log("sourceData",sourceData);
  let recallData = getDataBy("recallRulesRep", formatRecall);

  const filter = getDataBy("filterRulesRep", formatFilter);

  const DeRepeatData = getDataBy("repeatRulesRep", formDeRepeatData);

  const RankData = getDataBy(["storeSortRules", "skuSortRules"], (data) => {
    return formRankRules(data, type);
  });

  const deliveryData = getDataBy("deliveryRulesRep", formDeliveryData);

  const DeRepeatCol = {
    1: commodityDeRepeatCol,
    2: storeDeRepeatCol,
  };

  const sortRulesData = getDataBy("sortRules", formatSortRuleFilter);

  let newFilterSkuSortRulesCols = filterSkuSortRulesCols.slice();
  let recallRulesColsNew = recallRulesCols.slice(0);
  if (detail && detail.sortRules && detail.sortRules.discountRankFactor == 3) {
    newFilterSkuSortRulesCols.push({
      title: "商品品类",
      dataIndex: "goodsCategoryList",
    });
    if (type == 1) {
      recallRulesColsNew = recallRulesColsNew.concat([
        {
          title: "商品类目",
          dataIndex: "goodsCategory",
        },
      ]);
      // },{
      //   title: '商品名称',
      //   dataIndex: 'goodsNameKeyWord',
      // },{
      //   title: '商品活动类型',
      //   dataIndex: 'goodsActivitiesType'
      // }, {
      //   title: '原价区间',
      //   dataIndex: 'goodsOriginalPrice'
      // }, {
      //   title: '现价区间',
      //   dataIndex: 'goodsPresentPrice'
      // }, {
      //   title: '折扣区间',
      //   dataIndex: 'skuDiscount'
      // }])
    }
  }
  return (
    <div className={clz("putIn-detail", className)}>
      {withDataSource && (
        <PageSection title="数据源" className="detail-section">
          <HeadLeftTable
            thBackgroud={true}
            columns={dataSourceCols}
            dataSource={sourceData}
          />
        </PageSection>
      )}
      <PageSection title="投放规则" className="detail-section">
        <HeadLeftTable
          columns={recallRulesColsNew}
          dataSource={recallData}
          tableHeader={<TableHeader text="召回规则" />}
        />
        <HeadLeftTable
          columns={deliveryRulesCols}
          dataSource={deliveryData}
          tableHeader={<TableHeader text="配送规则" />}
        />
        <HeadLeftTable
          columns={filterRulesCols}
          dataSource={filter}
          tableHeader={<TableHeader text="过滤规则" />}
        />
        <HeadLeftTable
          columns={DeRepeatCol[type]}
          dataSource={DeRepeatData}
          tableHeader={<TableHeader text="去重规则" />}
        />
        <HeadLeftTable
          columns={
            detail.sortRules
              ? detail.skuSortRules
                ? newFilterSkuSortRulesCols
                : filterStoreSortRulesCols
              : []
          }
          dataSource={detail.sortRules ? sortRulesData : []}
          tableHeader={
            <TableHeader text={`排序规则:${RankData["rankRules"]}`} />
          }
        />
      </PageSection>
    </div>
  );
};
