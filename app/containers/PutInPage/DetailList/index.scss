@import '../../../styles/common.scss';

@mixin border-no-bottom($color) {
  border-top: 1px solid  $color;
  border-left: 1px solid $color;
  border-right: 1px solid $color;
}

.putIn-detail {
  .detail-section {
    h3 {
      color: $font_main_height;
      margin: 30px 0px 15px 0px;
    }

    width: 50%;
    min-width: 700px;
    margin: 0px auto;
  }

  .left-head-table{
    width: 100%;
    @include border-no-bottom($line_common);
    color: $font_main_light;
    margin-bottom: 15px;

    tr td {
      border-bottom: 1px solid $line_common;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
    }

    td:nth-child(1) {
      width:22%;
      font-weight: 500;
      // color: $font_main_light;
      border-right: 1px solid $line_common;
      // background-color: $fill_light;
    }

    .thBackgroud {
      background-color: $fill_light;
      color: $font_main_height;
    }
  }

  .table-header {
    color: $font_main_height;
    padding-left: 10px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    font-weight: 500;
    background-color: $fill_light;
    @include border-no-bottom($line_common);
  }
  .tdContent{
    word-wrap:break-word;
    word-break:normal;
    max-width:496px;
  }
}

//预览时的样式
.preview-detail{
  max-height: 460px;
  overflow-y: scroll;
  overflow-x: hidden;
}
