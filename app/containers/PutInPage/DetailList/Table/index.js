import React, { Fragment } from 'react';
import clz from 'classnames';

export const Tr = ({ title, value, thBackgroud }) => {
  return (
    <tr>
      <td className={clz({thBackgroud})}>{title}</td>
      <td><div className="tdContent">{value}</div></td>
    </tr>
  )
}

export const TableHeader = ({ text }) => (
  <div className="table-header">{text}</div>
)

export const HeadLeftTable = ({columns = [], dataSource, tableHeader = null, markCell = false, thBackgroud = false}) => {
  return (
    <Fragment>
      { tableHeader }
      <table className="left-head-table" cellspacing="0px">
        { !!columns.length && columns.map((col) => {
          const {title, dataIndex, cell} = col;
          let cellValue = '';
          if(cell){
            cellValue = cell(dataSource[dataIndex],dataSource);
          }
          return <Tr title={title} value={(!cell)?dataSource[dataIndex]:cellValue} thBackgroud={thBackgroud}/>
        }) }
      </table>
    </Fragment>
  )
}
