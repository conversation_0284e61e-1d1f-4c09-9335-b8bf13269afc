import { filterData } from "@/utils/filterDataByKey";
import moment from "moment";
import { switchNullTo, switchUndefinedTo } from "@/utils/others";
import { getPoolList } from "@/adator/api";
import { Message, Balloon } from "@alife/next";
import { onRequestError } from "@/utils/api";
import React, { Fragment } from "react";

/** 选品集ID 投放方式选择 */
export const limitTypeSelect = [
  {
    value: 0,
    label: "指定选品集",
  },
  {
    value: 1,
    label: "不限制，门店聚类",
  },
  {
    value: 2,
    label: "不限制，商品聚类",
  },
];

export const limitTypeGoodSelect = [
  {
    value: 0,
    label: "指定选品集",
  },
];

/*投放版本*/
export const versionGroupSet = [
  {
    value: "in",
    label: "包含(支持多选,分割)",
  },
  {
    value: "not_in",
    label: "不包含(支持多选,分割)",
  },
  {
    value: "gt",
    label: "大于",
  },
  {
    value: "lt",
    label: "小于",
  },
  {
    value: "ge",
    label: "大于等于",
  },
  {
    value: "le",
    label: "小于等于",
  },
];

/** 选品集ID 不限制 单选按钮 */
export const pooTypeRadioRules = [
  {
    value: 1,
    label: "门店聚类",
  },
  {
    value: 2,
    label: "商品聚类",
  },
];

/** 含牛皮癣图片商品过滤map 1*/
export const psoriasisPicFilterRules = [
  {
    value: 2,
    label: "低质量图片",
  },
  {
    value: 1,
    label: "中等质量图片",
  },
  {
    value: 0,
    label: "高质量图片",
  },
];

/** 商品主体面积占比map 1*/
export const bodyAreaRatioRules = [
  {
    value: 0,
    label: "低质量图片",
  },
  {
    value: 1,
    label: "高质量图片",
  },
];
/** 投放渠道 */
// export const deliveryChannelsRadio = [
//   {
//     value:0,
//     label:'全部'
//   },{
//     value:7,
//     label:'N元购'
//   }
// ]

/** 商品排序map 1*/
export const commodityRankRules = [
  {
    value: 4,
    label: "门店算法排序-点击率最高",
  },
  {
    value: 9,
    label: "门店算法排序-转化率最高",
  },
  {
    value: 5,
    label: "门店距离排序",
  },
  {
    value: 6,
    label: "门店销量排序",
  },
  {
    value: 3,
    label: "商品算法排序-点击率最高",
  },
  {
    value: 10,
    label: "商品算法排序-转化率最高",
  },
  {
    value: 1,
    label: "商品销量排序",
  },
  {
    value: 2,
    label: "商品折扣排序",
  },
  {
    value: 7,
    label: "推荐同店同场景品",
  },
  {
    value: 8,
    label: "营销活动排序",
  },
  {
    value: 11,
    label: "商品CPS排序",
  },
  {
    value: 12,
    label: "商家CPS排序",
  },
];

/** 优惠排序因子 map 2*/
export const discountRankFactorRules = [
  // {
  //   value:1,
  //   label:'商品优惠加权'
  // },
  // {
  //   value:2,
  //   label:'门店优惠加权'
  // },
  {
    value: 3,
    label: "品类加权",
  },
];
//
// /** 商品品类打散 map 3*/
// export const categoryScatterRules = [
//   {
//     value:1,
//     label:'商品品类打散'
//   }
// ]

/** 门店排序map 2*/
export const storeRankRules = [
  {
    value: 4,
    label: "门店算法排序-点击率最高",
  },
  {
    value: 9,
    label: "门店算法排序-转化率最高",
  },
  {
    value: 5,
    label: "门店距离排序",
  },
  {
    value: 6,
    label: "门店销量排序",
  },
  {
    value: 12,
    label: "商家CPS排序",
  },
  // 免配卡内页需求，先注掉
  // }, {
  //   value: 8,
  //   label: '门店配送费排序'
  // }
];

/** 门店营业状态 */
export const openStatusRadio = [
  {
    value: 1,
    label: "预定中",
  },
  {
    value: 5,
    label: "营业中",
  },
  {
    value: 4,
    label: "即将休息",
  },
  {
    value: 0,
    label: "休息中",
  },
];

export const scatterCategory = [
  { value: 1, label: "一级类目" },
  { value: 2, label: "二级类目" },
  { value: 3, label: "三级类目" },
];

/**default detail */
export const defaultDetail = {
  id: "",
  poolType: 1,
  activityName: "",
  channelListMap: [],
  labelListMap: [],
  checkAllValue: "",
  deliveryChannelList: [], // 投放渠道（投放活动用）
  deliveryChannelLabel: [], // 投放标签（投放活动用）
  channelList: [], // 投放渠道（频道页用）
  channelLabel: [], // 投放标签（频道页用）
  citysRep: [],
  // selectedCitys:[],
  userTagGroupIdsList: [],
  userTagGroupIdsListSet: [],
  limitType: 0,
  poolIdsRep: "",
  weightPoolIdsRep: "",
  startTime: "",
  endTime: "",
  filterRulesRep: {
    stockExhausted: false,
    activityStockExhusted: false,
    goodsWithoutPic: true,
    brandGoodsFilter: false,
    presaleGoodsFilter: true,
    goodsFilterWithPsoriasisPic: [],
    transparentBasePic: false,
    whiteBasePic: false,
    goodsBodyAreaRatio: "",
    mktActivityTypeList: [],
  },
  // 召回规则
  recallRulesRep: {
    storeDistance: [],
    storeTypeRep: [],
    shopMajorCategory: [],
    // 店铺评分
    shopScore: [],
    eatUnionTag: [],
    storeType: [],
    minNum: 0,
    openStatusSet: [5],
    goodsCategory: [],
    isNationalDelivery: false,
    itemBrandList: [],
    shopBrands: "",
    // goodsNameKeyWord: '',
    // goodsActivitiesType: [],
    // goodsOriginalPrice: [],
    // goodsPresentPrice: [],
    // skuDiscount: []
  },
  repeatRulesRep: {
    goodsNameRepeat: true,
    storeRepeat: true,
    minStoreRepeatCommodityNum: 1,
    thirdGoodsCategory: false,
    storeRepeatCommodityNum: 3,
    foldingWithBrand: false,
    upcAggregation: true,
    upcRecallCount: 3,
  },
  deliveryRulesRep: {
    distributionFee: [],
    distributionFullDecrementAmount: [],
    hummingBird: false,
    onTime: false,
    // freeDeliveryStore: false,
  },
  sortRules: {
    discountRankFactor: 0,
    upcScatter: 0,
    categoryScatter: 0,
    shopWeightingType: 0,
    catScatterLevel: 2,
    // brandShopScatter: 0,
    // levelCategoryScatter: 0,
    goodsCategoryList: [],
  },
  storeSortRules: 0,
  skuSortRules: 0,
};

export const defaultRulesTemplate = {
  id: "",
  templateName: "",
};

// 格式化详情数据

const firstPageKeys = [
  "poolType",
  "activityName",
  "channelListMap",
  "labelListMap",
  "checkAllValue",
  "deliveryChannelList",
  "deliveryChannelLabel",
  "channelList",
  "channelLabel",
  "citysRep",
  "griddingsRep",
  "poolIdsRep",
  "weightPoolIdsRep",
  "commonCollection",
  "weightCollection",
  "limitType",
  "startTime",
  "endTime",
  "poolIdAndContentRep",
  "weightPoolIdAndContentRep",
  "userTagGroupIdsList",
  "userTagGroupIdsListSet",
  "isRecommend",
  "categoryType",
  "categoryList",
  "itemCatList",
  "rejectItemCatList",
  "isCatComp",
  "riskCompLevel",
  "mainCates",
];
const secondPageKeys = [
  "recallRulesRep",
  "filterRulesRep",
  "repeatRulesRep",
  "deliveryRulesRep",
  "storeSortRules",
  "skuSortRules",
  "sortRules",
];
const recallSectionKeys = [
  "storeDistance",
  "storeTypeRep",
  "shopMajorCategory",
  "shopScore",
  "eatUnionTag",
  "itemBrandList",
  "itemBrandListContent",
  "shopBrands",
  "minNum",
  "openStatusSet",
  "goodsNameKeyWord",
  "goodsCategory",
  "goodsActivitiesType",
  "goodsOriginalPrice",
  "goodsPresentPrice",
  "skuDiscount",
  "isNationalDelivery",
];

/**格式化 第二页 */
export function formSecond(data) {
  const {
    recallRulesRep,
    filterRulesRep,
    repeatRulesRep,
    deliveryRulesRep,
    storeSortRules,
    skuSortRules,
    sortRules,
  } = data;

  const pickRecallByKey = filterData(recallRulesRep);
  const recallRules = pickRecallByKey(recallSectionKeys, (data) => {
    // 需要转换一下goodsActivitiesType的格式。传给后端的是[xxx,xxx]，需要转换成[{value:xxx},{value:xxx}]
    let newGoodsActivitiesType = [];
    data.goodsActivitiesType &&
      data.goodsActivitiesType.forEach((typeItem) => {
        if (Object.prototype.toString.call(typeItem) == "[object String]") {
          newGoodsActivitiesType.push({
            value: typeItem,
          });
        }
      });
    if (newGoodsActivitiesType.length) {
      data.goodsActivitiesType = newGoodsActivitiesType;
    }
    const storeTypeRep = switchNullTo(data.storeTypeRep, []);
    const shopMajorCategory = switchNullTo(data.shopMajorCategory, []);
    const eatUnionTag = switchNullTo(data.eatUnionTag, []);
    const openStatusSet = switchNullTo(data.openStatusSet, []);
    const itemBrandListContent = switchNullTo(data.itemBrandListContent, []);
    return {
      ...data,
      storeTypeRep,
      shopMajorCategory,
      eatUnionTag,
      openStatusSet,
      itemBrandListContent,
    };
  });

  const pickDeliveryByKey = filterData(deliveryRulesRep);
  const deliveryRules = pickDeliveryByKey(
    [
      "distributionFee",
      "hummingBird",
      "onTime",
      "distributionFullDecrementAmount",
      // "freeDeliveryStore"
    ],
    (data) => {
      const distributionFullDecrementAmount = switchNullTo(
        data.distributionFullDecrementAmount,
        []
      );
      return { ...data, distributionFullDecrementAmount };
    }
  );
  return {
    recallRules,
    filterRules: filterRulesRep,
    repeatRules: repeatRulesRep,
    deliveryRules: deliveryRules,
    storeSortRules,
    skuSortRules,
    sortRules,
  };
}

/**格式化 第一页 */
function formFirst(data) {
  const {
    poolType,
    activityName,
    channelListMap,
    labelListMap,
    checkAllValue,
    deliveryChannelList,
    deliveryChannelLabel,
    channelList,
    channelLabel,
    citysRep,
    griddingsRep,
    poolIdsRep,
    weightPoolIdsRep,
    weightCollection,
    commonCollection,
    limitType,
    startTime,
    endTime,
    poolIdAndContentRep,
    weightPoolIdAndContentRep,
    userTagGroupIdsList,
    userTagGroupIdsListSet,
    isRecommend,
    itemCatList = [],
    rejectItemCatList = [],
    isCatComp = 0,
    riskCompLevel = 1,
    mainCates,
  } = data;
  let { categoryType = 0, categoryList = [] } = data;
  if (itemCatList && itemCatList.length) {
    categoryList = itemCatList;
    categoryType = 0;
  } else if (rejectItemCatList && rejectItemCatList.length) {
    categoryList = rejectItemCatList;
    categoryType = 1;
  }
  const citys = switchNullTo(citysRep, []);
  const griddings = switchNullTo(griddingsRep, []) || [];
  const parentList = [];
  griddings.forEach((item) => {
    if (item.parentValue && !parentList.includes(item.parentValue)) {
      parentList.push(item.parentValue);
    }
  });

  return {
    poolType,
    activityName,
    channelListMap,
    labelListMap,
    checkAllValue,
    deliveryChannelList,
    deliveryChannelLabel,
    channelList,
    channelLabel,
    weightCollection,
    commonCollection,
    activityTime: [moment(startTime), moment(endTime)],
    poolIds: poolIdsRep, //String(poolIdsRep)
    weightPoolIds: weightPoolIdsRep, //String(poolIdsRep)
    limitType,
    poolIdAndContentRep: poolIdAndContentRep,
    weightPoolIdAndContentRep: weightPoolIdAndContentRep,
    citys: citys.concat(
      griddings.map((item) => ({
        label: item.label,
        value: "grid_" + item.value,
        level: 3,
      }))
    ),
    griddings,
    selectedCitys: parentList.concat(citys.map((item) => item.value)),
    userTagGroupIdsList: switchNullTo(userTagGroupIdsList, []),
    userTagGroupIdsListSet: switchUndefinedTo(userTagGroupIdsListSet, []),
    isRecommend,
    categoryList,
    categoryType,
    itemCatList,
    rejectItemCatList,
    isCatComp,
    riskCompLevel,
    mainCates,
  };
}

export function formDetail(data) {
  const pickByKey = filterData(data);
  const firstPage = pickByKey(firstPageKeys, formFirst);
  const secondPage = pickByKey(secondPageKeys, formSecond);
  const poolType = pickByKey("poolType");
  return {
    firstPage,
    secondPage,
    poolType,
  };
}

const skipDetail = async (id, poolType) => {
  try {
    let res = await getPoolList({
      pageIndex: 1,
      pageSize: 10,
      poolId: id,
      rangeType: 0,
    });
    if (res.data.length > 0) {
      let { newPlatformFlag, sourcePoolId, basePoolName, poolType, state } =
        res.data[0];
      if (state != "25") {
        if (newPlatformFlag === 0) {
          let dataType = parseInt(sourcePoolId) >= 20001 ? 1 : 2;
          location.href = `#/${
            poolType == 1 ? "commoditypool" : "storepool"
          }/list/detail/${id}?basePoolId=${sourcePoolId}&basePoolName=${basePoolName}&dataType=${dataType}`;
        } else {
          location.href = `#/pool/list/detail/${id}?isSelectedStore=${
            poolType == 1 ? false : true
          }`;
        }
      } else {
        Message.warning("池子已下线");
      }
    } else {
      Message.warning("池子不存在");
    }
  } catch (error) {
    onRequestError(error);
  }
};

/**
 * @param keyType  区分加权池还是常规池 1：加权池 2：常规池
 * @param record  整条数据
 * @param type  区分是列表还是详情  1：详情页  2：列表页
 * @returns {JSX.Element}
 */
export function showOldPoolId(keyType, record, type) {
  let idsReqs = "---";
  let ids;
  if (type == 1) {
    //详情页
    ids = keyType == 2 ? record.poolIdsRep : record.weightPoolIdsRep;
    if (ids.length > 0) {
      let idsArray = ids.split(",");
      idsReqs = idsArray.map((v) => {
        return (
          <a
            style={{ marginRight: "5px" }}
            href="javascript:void(0)"
            onClick={() => skipDetail(v, record.poolType)}
          >
            {v}
          </a>
        );
      });
    }
  } else {
    //列表页
    const { poolIds, limitType, poolType } = record;
    ids = keyType == 2 ? record.poolIds : record.weightPoolIds;
    if (limitType == 0 || limitType == 3) {
      idsReqs = ids
        ? JSON.parse(poolIds).map((v) => {
            return (
              <a
                href="javascript:void(0)"
                onClick={() => skipDetail(v, record.poolType)}
              >
                {v}
              </a>
            );
          })
        : "";
    } else if (limitType == 1 || limitType == 2) {
      idsReqs = pooTypeRadioRules.filter((item) => item.value == poolType)[0]
        .label;
    }
  }
  return <Fragment>{idsReqs}</Fragment>;
}

export function ShowCollectionPoolId(keyType, record) {
  let { commonCollection = [], weightCollection = [] } = record;
  if (
    (commonCollection && commonCollection.length > 0) ||
    (weightCollection && weightCollection.length > 0)
  ) {
    let ids = keyType == 2 ? commonCollection : weightCollection;
    let selectonIds = ids.filter((v) => v.dataSourceType == "1");
    let investIds = ids.filter((v) => v.dataSourceType == "2");
    let promoteType = ids.filter((v) => v.dataSourceType == "3");
    let promoteIds = ids.filter((v) => v.dataSourceType == "4");
    let upcIds = ids.filter((v) => v.dataSourceType == "5");
    let sceneIds = ids.filter((v) => v.dataSourceType == "6");

    const Selection = () => {
      return (
        <div style={{ display: "flex" }}>
          <div>选品池: </div>
          <Balloon
            trigger={<div className="poolIds">
              {selectonIds[0].idList.length + "个"}
            </div>}
            popupStyle={{ maxWidth: 200, width: 200 }}
            triggerType="click"
            shouldUpdatePosition
            needAdjust
            align={'l'}
          >
            <div
              className="preview-detail"
              style={{
                maxHeight: '200px',
                overflowY: 'scroll',
                overflowX: 'hidden',
              }}
            >
              {selectonIds[0].idList.map((v, k) => {
                return (
                  <a
                    className="group—id"
                    href="javascript:void(0)"
                    onClick={() => skipDetail(v.value, record.poolType)}
                  >
                    {k > 0 ? ',': ''}
                    {" " + v.value + " "}
                  </a>
                );
              })}
            </div>
          </Balloon>
        </div>
      );
    };

    const Invest = () => {
      return (
        <div style={{ display: "flex" }}>
          <div>招商池: </div>
          <Balloon
            trigger={<div className="poolIds">
              {investIds[0].idList.length + "个"}
            </div>}
            popupStyle={{ maxWidth: 200, width: 200}}
            triggerType="click"
            shouldUpdatePosition
            needAdjust
            align={'l'}
          >
            <div
              className="preview-detail"
              style={{
                maxHeight: '200px',
                overflowY: 'scroll',
                overflowX: 'hidden',
              }}
            >
              {investIds[0].idList.map((v,k) => {
                return (
                  <a
                    className="group—id"
                    href={`https://nr.ele.me/op-fe/kunlun-zs-landing/#/landing-page/activity-detail-from-alpha?activityId=${v.value}`}
                    target={"_blank"}
                  >
                    {k > 0 ? ',': ''}
                    {" " + v.value + " "}
                  </a>
                );
              })}
            </div>
          </Balloon>
        </div>
      );
    };

    const Scene = () => {
      return (
        <div style={{ display: "flex" }}>
          <div>场景池: </div>
          <Balloon
            trigger={<div className="poolIds">
              {sceneIds[0].idList.length + "个"}
            </div>}
            popupStyle={{ maxWidth: 200, width: 200 }}
            triggerType="click"
            shouldUpdatePosition
            needAdjust
            align={'l'}
          >
            <div
              className="preview-detail"
              style={{
                maxHeight: '200px',
                overflowY: 'scroll',
                overflowX: 'hidden',
              }}
            >
              {sceneIds[0].idList.map((v, k) => {
                return (
                  <a
                    className="group—id"
                    href="javascript:void(0)"
                    onClick={() => {
                      location.href = `#//channelManage/gallery/view/${v.value}`
                    }}
                  >
                    {k > 0 ? ',': ''}
                    {" " + v.value + " "}
                  </a>
                );
              })}
            </div>
          </Balloon>
        </div>
      );
    };

    const PromoteId = () => {
      return (
        <div style={{ display: "flex" }}>
          <div>营销活动ID: </div>
          <Balloon
            trigger={<div className="poolIds">
              {(promoteIds[0].marketActivityIds).split(",").length + "个"}
            </div>}
            popupStyle={{ maxWidth: 200, width: 200, height: 100 }}
            triggerType="click"
            shouldUpdatePosition
            needAdjust
            align={'l'}
          >
            <div
              className="preview-detail"
              style={{
                maxHeight: '200px',
                overflowY: 'scroll',
                overflowX: 'hidden',
              }}
            >
              {promoteIds[0].marketActivityIds}
            </div>
          </Balloon>
        </div>
      );
    };

    const PromoteType = () => {
      return (
        <div style={{ display: "flex" }}>
          <div>营销活动类型: </div>
          <Balloon
            trigger={<div className="poolIds">
              {promoteType[0].idList.length + "个"}
            </div>}
            popupStyle={{ maxWidth: 200, width: 200, height: 100 }}
            triggerType="click"
            shouldUpdatePosition
            needAdjust
            align={'l'}
          >
            <div
              className="preview-detail"
              style={{
                maxHeight: '200px',
                overflowY: 'scroll',
                overflowX: 'hidden',
              }}
            >
              {promoteType[0].idList.map((v) => v.label).join(",")}
            </div>
          </Balloon>
        </div>
      );
    };

    const UPCEle = () => {
      return (
        <div style={{ display: "flex" }}>
          <div>商品条形码: </div>
          <Balloon
            trigger={<div className="poolIds">
              {upcIds[0].idList.length + "个"}
            </div>}
            popupStyle={{ maxWidth: 200, width: 200, height: 100 }}
            triggerType="click"
            shouldUpdatePosition
            needAdjust
            align={'l'}
          >
            <div
              className="preview-detail"
              style={{
                maxHeight: '200px',
                overflowY: 'scroll',
                overflowX: 'hidden',
              }}
            >
              {upcIds[0].idList.map((v) => v.value).join(",")}
            </div>
          </Balloon>
        </div>
      );
    };

    const ShowContent = () => {
      const showSelection =
        selectonIds.length > 0 &&
        selectonIds[0].idList &&
        selectonIds[0].idList.length > 0;
      const showInvest =
        investIds.length > 0 &&
        investIds[0].idList &&
        investIds[0].idList.length > 0;
      const showScene =
        sceneIds.length > 0 &&
        sceneIds[0].idList &&
        sceneIds[0].idList.length > 0;
      const showPromoteId =
        promoteIds.length > 0 && promoteIds[0].marketActivityIds;
      const showPromoteType = promoteType.length > 0 && promoteType[0].idList;
      const showUPC = upcIds.length > 0 && upcIds[0].idList;
      if (showSelection || showInvest || showPromoteId || showPromoteType || showUPC || showScene) {
        return (
          <Fragment>
            {showSelection && Selection()}
            {showInvest && Invest()}
            {showPromoteId && PromoteId()}
            {showPromoteType && PromoteType()}
            {showUPC && UPCEle()}
            {showScene && Scene()}
          </Fragment>
        );
      } else {
        return <Fragment>--</Fragment>;
      }
    };

    return <ShowContent />;
  } else {
    return <Fragment>--</Fragment>;
  }
}

export function ShowCollectionPoolId22(keyType, record) {
  let {commonCollection = [], weightCollection = []} = record;
  if ((commonCollection && commonCollection.length > 0) || (weightCollection && weightCollection.length > 0)) {
    let ids = (keyType == 2) ? commonCollection : weightCollection;
    let selectonIds = ids.filter((v) => v.dataSourceType == '1');
    let investIds = ids.filter((v) => v.dataSourceType == '2');
    let promoteType = ids.filter((v) => v.dataSourceType == '3');
    let promoteIds = ids.filter((v) => v.dataSourceType == '4');
    let showSelection = selectonIds.length > 0 && selectonIds[0].idList && selectonIds[0].idList.length > 0;
    let showInvest = (investIds.length > 0 && investIds[0].idList && investIds[0].idList.length > 0);
    let showPromoteType = (promoteType.length > 0 && promoteType[0].idList);
    let showPromoteId = (promoteIds.length > 0 && promoteIds[0].marketActivityIds);
    let selectionLabel =  (showSelection) && <strong>选品池：</strong>;
    let investLabel =  (showInvest) && <strong>招商池：</strong>;
    let promoteIdLabel =  (showPromoteId) && <strong>营销活动ID：</strong>;
    let promoteTypeLabel =  (showPromoteType) && <strong>营销活动类型：</strong>;
    let idsReqs1 = showSelection ? selectonIds[0].idList.map((v) => {
      return <a className="group—id" href='javascript:void(0)' onClick={() => skipDetail(v.value, record.poolType)}>{v.value}</a>
    }) : '';
    let idsReqs2 = showInvest ? investIds[0].idList.map((v) => {
      return <a className="group—id" href={`https://nr.ele.me/op-fe/kunlun-zs-landing/#/landing-page/activity-detail-from-alpha?activityId=${v.value}`} target={'_blank'}>{v.value}</a>
    }) : '';
    let idsReqs3 = showPromoteType ? promoteType[0].idList.map((v) => v.label).join(","):'';
    let idsReqs4 = showPromoteId ? promoteIds[0].marketActivityIds : '';
    return (<Fragment>{selectionLabel}{idsReqs1}<br/>{investLabel}{idsReqs2}<br/>{promoteTypeLabel}{idsReqs3}<br/>{promoteIdLabel}{idsReqs4}</Fragment>);
  }
}
