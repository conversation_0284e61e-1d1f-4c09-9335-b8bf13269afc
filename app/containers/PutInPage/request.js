import { putInReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

/**投放活动管理列表 */
export function getPutInList(params,onlyMine) {  // onlyMine 1.'我创建的',2.已申请的,3.可查看的
  let request = '/api/putInActivity/list';
  if (onlyMine == 2) {
    request = '/api/putInActivity/applyList';
  } else if (onlyMine == 3) {
    request = '/api/putInActivity/viewableList';
  }
  return putInReq.post(request, params).then(onRequestSuccess)
}

/**创建投放活动 */
export function createPutIn(params) {
  return putInReq.post('/api/putInActivity/addOrUpdate', params).then(onRequestSuccess)
}

/**按群群组id搜索人群标签**/
export function getUserTagGroupIdsList(params) {
  return putInReq.get(`/api/putInActivity/queryRegionGroupByKeyword/${params.groupId}`).then(onRequestSuccess)
}

/**按名称或者活动id或者创建人的名字搜索活动集**/
export function getPoolIdsGroup(params) {
  return putInReq.post('/api/putInActivity/getPoolInfoByPoolNameOrPoolIdOrCreatorName', params).then(onRequestSuccess)
}

/**获取投放活动详情 */
export function getPutInDetail(activityId) {
  return putInReq.get(`/api/putInActivity/info/${activityId}`).then(onRequestSuccess)
}

/**删除活动 */
export function delPutIn(activityId){
  return putInReq.get(`/api/putInActivity/del/${activityId}`).then(onRequestSuccess)
}

/**保存为规则模版函数 */
export function creatTemplateRules(params){
  return putInReq.post('/api/putIn/templateRules/addOrUpdate', params).then(onRequestSuccess)
}

/**获取所有的模版列表 */
export function getTemplateList(params){
  return putInReq.post('/api/putIn/templateRules/list', params).then(onRequestSuccess)
}

/**ACL权限认证 */
export function ACLPermission(activityId){
  return putInReq.post(`/api/acl/validate/${activityId}`).then(onRequestSuccess)
}

/**获得买赠list */
export function getActivityType(){
  return putInReq.get('/api/putInActivity/queryActivityType').then(onRequestSuccess)
}


/**获得选品集数据 */
export function getPoolList(params){
  return putInReq.post('/api/putInActivity/queryPoolList',params).then(onRequestSuccess)
}

/**获得选品集数据 */
export function getInvestData(params){
  return putInReq.post('/api/putInActivity/queryInvestList',params).then(onRequestSuccess)
}
