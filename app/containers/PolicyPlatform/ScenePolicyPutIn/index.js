import React from 'react';

import {
  Form,
  Grid,
  Button,
  Input,
  Tab,
  Field,
  Message,
  Dialog
} from '@alife/next';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import {DialogBtn, LinkButton, GoldLogLink} from '@/components/Button';
import {ACLPermission} from "@/containers//PutInPage/request";
import {AccessBtn} from '@/components/Button/AccessBtn';
import {manageList, setTableCol, filterForm} from '@/components/Table';
import {formatTimeStamp} from '@/utils/time';
import {onRequestError} from '@/utils/api'
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {isAllNumber} from '@/utils/validators';
import {doesFormHasErrors} from '@/utils/formTool';
import {filterData} from '@/utils/filterDataByKey';
import {momentToTimeStamp} from '@/utils/time';
import {withoutCopy} from '@/utils/others';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import { listPutin, listViewablePutin, addPutin, deletePutin } from '../request';
import {pageMap} from '../common'
import './index.scss'
import {LogTimePutInPage} from "@/containers/decoration/activity/edit";
const FormItem = Form.Item;
const {Row, Col} = Grid;

const timeFormat = 'YYYY-MM-DD HH:mm:ss';

export const onlineStatusMap = [
  {value: "", name: "全部"},
  {value: "1", name: "未开始", color: "#FF9500"},
  {value: "2", name: "进行中", color: "#00CCAA"},
  {value: "3", name: "已下线", color: "#CCCCCC"},
  {value: "4", name: "已结束", color: "#CCCCCC"},
]

/**操作列 */
const OptionCol = ({...params}) => {
  const {record, reload, tabidx} = params;
  const operatePutIn = () => {
    let cmd = {
      putinId:record.putinId,
    }
    deletePutin({...cmd}).then((data) => {
      console.log(data);
      Message.success('删除成功');
      reload()
    }).catch(onRequestError)
  }

  const deletePutIn = (record) => {
    Dialog.confirm({
      title: '删除场景策略投放',
      onOk: () => operatePutIn(record),
    });
  }

  return (
    <div className="option-col">
      <Button disabled={tabidx != 1} type="primary" text onClick={() => deletePutIn(record)}>删除</Button>
    </div>
  )
}

const defaultColType = {title: ''}

const columns = [
  {title: '场景Code', dataIndex: 'sceneCode', width: '20%', cell: (value) => `${value}`},
  {title: '千网策略ID', dataIndex: 'outerStrategyId', width: '20%'},
  {title: '创建人', dataIndex: 'createUserName', width: '20%'},
  {title: '创建时间', dataIndex: 'createTime', width: '20%'},
  {
    title: '操作', width: '22%', cell: function (value, rowIndex, record) {
      return <OptionCol record={record} reload={this.reload} {...this.props}/>
    }
  }
]

function getCol(type) {
  return {...defaultColType, ...type}
}

const formItemLayout = {
  labelCol: {span: 8},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

function PutInForm({ field, searchData, reload }) {
    const onSearch = () => {
      field.validate((e) => {
        if(!doesFormHasErrors(e)){
          searchData()
        }
      })
    }
    return (
      <Form {...formLayout} field={field}>
        <Row>
          <Col span={6}>
            <FormItem label="场景Code:" {...formItemLayout}>
              <Input placeholder="请输入场景Code" name="searchSceneCode" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="策略ID:" {...formItemLayout}>
              <Input placeholder="请输入策略ID" name="searchOuterStrategyId" />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem  {...formItemLayout}>
              <Button type="primary" onClick={onSearch} style={{marginLeft:'10px'}}>查询</Button>&nbsp;&nbsp;
              <Button type="normal" onClick={reload}>重置</Button>&nbsp;&nbsp;
            </FormItem>
          </Col>
        </Row>
      </Form>
    )
}

const PutInFilterForm = filterForm(PutInForm, ['searchSceneCode', 'searchOuterStrategyId'])
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(PutInTable, PutInFilterForm,
  async function (search) {
    let {query, page, size} = search;
    let {tabidx} = this.props;
    let params = {
      pageNo: page,
      pageSize: size,
      ...search.query
    }
    try {
      let request = (tabidx == 1) ? listPutin : listViewablePutin;
      const data = await request({...params});
      return data;
    } catch(error) {
      onRequestError(error)
    }
  }
)

class Index extends PageBase {
  constructor(props) {
    super(props)
    this.state = {
      tabidx: 1, // 1我创建的、0可查看的、2：已申请权限的
      createFormVisible: false,
      createForm: {}
    }

    this.field = new Field(this, {
      onChange: (name, value) => {
        const {createForm} = this.state;
        createForm[name] = value;
        this.setState({
          createForm
        }, () => {
          console.log(this.state);
        })
      }
    })
  }

  async componentDidMount() {
    this.refs.manageList.fetchData({});
  }

  onTabChange = (tabidx) => {
    this.setState({
        tabidx,
    }, () => {
        this.refs.manageList.fetchData({page:1});
    })
  }


  submitScene = () =>{
    const {createForm} = this.state;
    let cmd = {
      sceneCode:createForm.sceneCode,
    }
    addPutin({...cmd}).then((data) => {
      this.setState({createFormVisible: false})
      Message.success('创建成功');
      this.refs.manageList.fetchData({page:1});
    }).catch(onRequestError)
  }

  createScene = () => {
    this.setState({createFormVisible: true})
  }

  render() {
    let {tabidx} = this.state;

    return (
      <PageBase.Container className="scene-manage-page">
        <PageWrapper title={<div><span>场景策略投放</span><Button type="primary" style={{float:'right',marginRight:'25px'}} onClick={this.createScene}>新建策略ID</Button></div>}>
          <ManageList
            ref="manageList"
            tabidx={tabidx}
          >
            <Tab shape="wrapped" onChange={this.onTabChange} activeKey={tabidx}>
              <Tab.Item title="我创建的" key={1}></Tab.Item>
              <Tab.Item title="可查看的" key={0}></Tab.Item>
            </Tab>
            <Dialog title="新建千网策略ID"
                    style={{width:'606px'}}
                    visible={this.state.createFormVisible}
                    okProps={{children: '提交'}}
                    onOk={this.submitScene}
                    onCancel={()=>this.setState({createFormVisible: false})}
                    onClose={()=>this.setState({createFormVisible: false})}
            >
              <Form {...formLayout} field={this.field}>
                <Row>
                  <Col span={20}>
                    <FormItem label="场景code:" {...formItemLayout}>
                      <Input placeholder="请输入场景code" name="sceneCode"/>
                    </FormItem>
                  </Col>
                </Row>
              </Form>
            </Dialog>
          </ManageList>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}


export const ScenePolicyPutInPage = logTimeComponent(Index, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})
//
// export const ScenePolicyPutInPage = permissionAccess(ScenePolicyPutIn, async (params) => {
//   return await validateAclResource(params);
// })
