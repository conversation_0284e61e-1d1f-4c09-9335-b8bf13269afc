import React, { Component } from 'react';

import {
  Form,
  Grid,
  Field,
} from '@alife/next';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import {DialogBtn, LinkButton, GoldLogLink} from '@/components/Button';
import {ACLPermission} from "@/containers//PutInPage/request";
import {AccessBtn} from '@/components/Button/AccessBtn';
import {manageList, setTableCol, filterForm} from '@/components/Table';
import {formatTimeStamp} from '@/utils/time';
import {onRequestError} from '@/utils/api'
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {isAllNumber} from '@/utils/validators';
import {doesFormHasErrors} from '@/utils/formTool';
import {filterData} from '@/utils/filterDataByKey';
import {momentToTimeStamp} from '@/utils/time';
import {withoutCopy} from '@/utils/others';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import SimpleTable from '@/components/SimpleTable'
import {
  reportScene, listScene,getMaterialStrategyDetail
} from '../request';
import './index.scss'
import {LogTimePutInPage} from "@/containers/decoration/activity/edit";
const FormItem = Form.Item;
const {Row, Col} = Grid;
const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

export default class ListDetail extends Component {
  constructor(props) {
    super(props)
    this.state = {
      createFormVisible: false,
      createForm: {},
      detailData:props.data,
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        const {createForm} = this.state;
        createForm[name] = value;
        this.setState({
          createForm
        }, ()=> {
          console.log(this.state);
        })
      }
    })
  }

  componentDidMount() {
    this.getDetail();
  }

  async getDetail(){
    let {detailData} = this.state;
    console.log(detailData);
    let query = {
      sceneCode:detailData.sceneCode
    }
    try {
      const data = await getMaterialStrategyDetail({...query});
      console.log(data);
      this.setState({data});
    } catch(error) {
      onRequestError(error)
    }
  }

  render() {
    console.log(this.props.data);
    let {data} = this.state;
    return (
      <Form {...formLayout} field={this.field}>
        <Row>
          <Col span="24">
            {data && <SimpleTable
            >
              <SimpleTable.Item label="场景词">
                {data.materialNames}
              </SimpleTable.Item>
              <SimpleTable.Item label="门店类目">
                {data.viewRule.storeCateNameList.join(",")}
              </SimpleTable.Item>
              <SimpleTable.Item label="主推商品类目">
                {data.viewRule.itemMainRecommendCateNameList.join(",")}
              </SimpleTable.Item>
              <SimpleTable.Item label="商品关键词">
                {data.viewRule.itemKeywordList}
              </SimpleTable.Item>
              <SimpleTable.Item label="补充商品类目">
                {data.viewRule.itemSupplyCateNameList.join(",")}
              </SimpleTable.Item>
              <SimpleTable.Item label="提权选品集id">
                {data.viewRule.addWeightPoolId&&data.viewRule.addWeightPoolId.join(",")}
              </SimpleTable.Item>
              <SimpleTable.Item label="人群圈选">
                {data.viewRule.crowdContainCrowdList.join(",")}
              </SimpleTable.Item>
            </SimpleTable>
            }
          </Col>
        </Row>
      </Form>
    )
  }
}
