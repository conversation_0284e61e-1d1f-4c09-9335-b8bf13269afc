import React from 'react';

import {
  Form,
  Grid,
  Button,
  Input,
  Select,
  Tab,
  Divider,
  Message,
  Dialog
} from '@alife/next';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import {DialogBtn, LinkButton, GoldLogLink} from '@/components/Button';
import {ACLPermission} from "@/containers//PutInPage/request";
import {AccessBtn} from '@/components/Button/AccessBtn';
import {manageList, setTableCol, filterForm} from '@/components/Table';
import {formatTimeStamp} from '@/utils/time';
import {onRequestError} from '@/utils/api'
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {isAllNumber} from '@/utils/validators';
import {doesFormHasErrors} from '@/utils/formTool';
import {filterData} from '@/utils/filterDataByKey';
import {momentToTimeStamp} from '@/utils/time';
import {withoutCopy} from '@/utils/others';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import { listStrategy,listViewableStrategy,offlineStrategy} from '../request';
import ListCreate from './listCreate';
import ListDetail from './listDetail';
import './index.scss'
import {LogTimePutInPage} from "@/containers/decoration/activity/edit";

const FormItem = Form.Item;
const {Row, Col} = Grid;

const timeFormat = 'YYYY-MM-DD HH:mm:ss';

export const onlineStatusMap = [
  {value: "", name: "全部"},
  {value: "处理中", name: "处理中", color: "#FF9500"},
  {value: "已上线", name: "已上线", color: "#00CCAA"},
  {value: "已下线", name: "已下线", color: "#CCCCCC"},
]

/**操作列 */
const OptionCol = ({...params}) => {
  const {record, reload, tabidx, seekDetail} = params;
  const {id} = record;

  const operateScene = () => {
    let cmd = {
      sceneCode:record.sceneCode,
    }
    offlineStrategy({...cmd}).then((data) => {
      console.log(data);
      Message.success('下线成功');
      reload()
    }).catch(onRequestError)
  }

  const offlineScene = (record) => {
    Dialog.confirm({
      title: '下线场景策略',
      onOk: () => operateScene(record),
    });
  }
  return (
    <div className="option-col">
      <Button type="primary" text onClick={() => seekDetail(record)}>查看</Button>
      {(tabidx==1 && record.state != 1 && record.state != 4) && [<Divider direction="ver"/>,
      <Button type="primary" text onClick={() => offlineScene(record)}>下线</Button>]}
    </div>
  )
}

const defaultColType = {title: ''}

const columns = [
  // {title: '策略名称', dataIndex: 'strategyName', width: '20%', cell: (value) => `${value}`},
  {title: '场景Code', dataIndex: 'sceneCode', width: '20%', cell: (value) => `${value}`},
  {title: '场景词', dataIndex: 'materialNames', width: '20%'},
  {title: '状态', dataIndex: 'stateDesc', width: '20%'},
  {title: '创建人', dataIndex: 'createUserName', width: '20%'},
  {
    title: '操作', width: '22%', cell: function (value, rowIndex, record) {
      return <OptionCol record={record} reload={this.reload} {...this.props}/>
    }
  }
]

function getCol(type) {
  return {...defaultColType, ...type}
}

const formItemLayout = {
  labelCol: {span: 8},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const formItemLayout2 = {
  labelCol: {span: 6},
  wrapperCol: {
    span: 18
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

function PutInForm({ field, searchData, reload }) {
    const onSearch = () => {
      field.validate((e) => {
        if(!doesFormHasErrors(e)){
          searchData()
        }
      })
    }
    return (
      <Form {...formLayout} field={field}>
        <Row>
          <Col span={6}>
            <FormItem label="场景词:" {...formItemLayout}>
              <Input placeholder="请输入策略名称" name="searchMaterialName" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="场景Code:" {...formItemLayout}>
              <Input placeholder="请输入场景Code" name="searchSceneCode" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态:" {...formItemLayout2}>
                <Select name="searchStateDesc" placeholder="请选择状态" style={{ width: '200px' }}>
                {
                    onlineStatusMap.map((v) => {
                    return <Select.Option key={v.value} value={v.value}>{v.name}</Select.Option>
                    })
                }
                </Select>
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem>
              <Button type="primary" onClick={onSearch}>查询</Button>&nbsp;&nbsp;
              <Button type="normal" onClick={reload}>重置</Button>&nbsp;&nbsp;
            </FormItem>
          </Col>
        </Row>
      </Form>
    )
}

const PutInFilterForm = filterForm(PutInForm, ['searchMaterialName', 'searchSceneCode', 'searchStateDesc'])
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(PutInTable, PutInFilterForm,
  async function (search) {
    let {query, page, size} = search;
    let {tabidx} = this.props;
    let params = {
      pageNo: page,
      pageSize: size,
      ...search.query
    }
    try {
      let request = (tabidx == 1) ? listStrategy : listViewableStrategy;
      const data = await request({...params});
      return data;
    } catch(error) {
      onRequestError(error)
    }
  }
)

class Index extends PageBase {
  constructor(props) {
    super(props)
    this.state = {
      tabidx: 1, // 1我创建的、0可查看的、2：已申请权限的
      createFormVisible: false,
      detailFormVisible:false,
      detailData:''
    }
  }

  async componentDidMount() {
    this.refs.manageList.fetchData({});
  }

  onTabChange = (tabidx) => {
    this.setState({
        tabidx,
    }, () => {
        this.refs.manageList.fetchData({page:1});
    })
  }

  createScene = () => {
    this.setState({createFormVisible: true})
  }

  seekDetail = (record) =>{
    this.setState({detailFormVisible: true, detailData: record})
  }

  submitCreate = () =>{
    this.refs.listCreate.submitCreate();
  }

  render() {
    let {tabidx} = this.state;

    return (
      <PageBase.Container className="scene-manage-page">
        <PageWrapper title={<div><span>场景策略</span><Button type="primary" style={{float:'right',marginRight:'25px'}} onClick={this.createScene}>新建场景策略</Button></div>}>
          <ManageList
            ref="manageList"
            tabidx={tabidx}
            seekDetail={this.seekDetail}
          >
            <Tab shape="wrapped" onChange={this.onTabChange} activeKey={tabidx}>
              <Tab.Item title="我创建的" key={1}></Tab.Item>
              <Tab.Item title="可查看的" key={0}></Tab.Item>
            </Tab>
            <Dialog title="新建场景策略"
                    style={{width:'606px'}}
                    isFullScreen={true}
                    visible={this.state.createFormVisible}
                    okProps={{children: '提交'}}
                    onOk={()=>this.submitCreate()}
                    onCancel={()=>this.setState({createFormVisible: false})}
                    onClose={()=>this.setState({createFormVisible: false})}
            >
              <ListCreate ref='listCreate' reload={()=>this.refs.manageList.fetchData({page:1})} showCreate={(bool) => this.setState({createFormVisible: bool})}/>
            </Dialog>
            <Dialog title="查看场景策略"
                    style={{width:'606px'}}
                    isFullScreen={true}
                    visible={this.state.detailFormVisible}
                    okProps={{children: '关闭'}}
                    onOk={()=>this.setState({detailFormVisible: false})}
                    onCancel={()=>this.setState({detailFormVisible: false})}
                    onClose={()=>this.setState({detailFormVisible: false})}
            >
              <ListDetail data={this.state.detailData} />
            </Dialog>
          </ManageList>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}


export const ScenePolicyPage = logTimeComponent(Index, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})
