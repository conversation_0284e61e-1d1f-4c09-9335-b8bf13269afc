import React, {Component} from 'react';

import {
  Form,
  Grid,
  Input,
  Select,
  Field,
  CascaderSelect
} from '@alife/next';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import {DialogBtn, LinkButton, GoldLogLink} from '@/components/Button';
import {ACLPermission} from "@/containers//PutInPage/request";
import {searchPickPoolVaguely} from "../request";
import {AccessBtn} from '@/components/Button/AccessBtn';
import {manageList, setTableCol, filterForm} from '@/components/Table';
import {formatTimeStamp} from '@/utils/time';
import {onRequestError} from '@/utils/api'
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {isAllNumber} from '@/utils/validators';
import {doesFormHasErrors} from '@/utils/formTool';
import {filterData} from '@/utils/filterDataByKey';
import {momentToTimeStamp} from '@/utils/time';
import {withoutCopy} from '@/utils/others';
import * as api from '@/utils/api';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link} from 'react-router-dom';
import {
  SearchRecentSceneByText,
  listItemCategoryList,
  listStoreCategoryList,
  listSelectableCrowdList,
  addStrategy,
} from '../request';
import './index.scss'
import {LogTimePutInPage} from "@/containers/decoration/activity/edit";

const FormItem = Form.Item;
const {Row, Col} = Grid;
const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

const formItemLayout = {
  labelCol: {span: 8},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

export default class ListCreate extends Component {
  constructor(props) {
    super(props)
    this.state = {
      createFormVisible: false,
      createForm: {},
      sceneGroup: [],
      itemCategoryGroup:[],
      storeCategoryGroup:[],
      crowdGroup:[],
      selectStore:[],
      mainSelectItem:[],
      supplySelectItem:[],
      mainRecommendCate:[],
      RecommendCate:[],
      supplyCate:[],
      cateLevel:[],
      containCrowdList:[],
      itemKeywordList:[],
      addWeightPoolId:[],
      poolIdsSet:[],
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        const {createForm} = this.state;
        createForm[name] = value;
        this.setState({
          createForm
        }, () => {
          console.log(this.state);
        })
      }
    })
  }


  componentDidMount() {
    this.getItemCategoryList();
    this.getStoreCategoryList();
    this.getSelectableCrowdList();
  }

  ctrlGroup = (data) =>{
    let result = [];
    if(data) {
      data.map((v) => {
        result.push({
          value: v.value,
          label: v.label,
          level: (v.pos.split("-").length - 1)
        })
      })
    }
    return result;
  }

  changeStoreCategory = (value, data) => {
    this.setState({selectStore: value, cateLevel: this.ctrlGroup(data)},()=>{console.log(this.state)});
  }

  changeItemCategory = (value, data) => {
    this.setState({mainSelectItem: value, mainRecommendCate: this.ctrlGroup(data)},()=>{console.log(this.state)});
  }

  changeSupplyItemCategory = (value, data) => {
    this.setState({supplySelectItem: value, supplyCate: this.ctrlGroup(data)},()=>{console.log(this.state)});
  }

  changeCrowdList = (value) => {
    this.setState({containCrowdList: value });
  }

  changeItemKeywordList = (value) =>{
    this.setState({itemKeywordList: [value]});
  }

  changeAddWeightPoolId = (value) =>{
    this.setState({addWeightPoolId: value});
  }

  onSearch = (keyword) => {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if (keyword) {
        SearchRecentSceneByText({text: keyword}).then((data) => {
          console.log(data);
          const dataSource = data.rows.map(item => ({
            label: item.name, value: item.name
          }));
          this.setState({sceneGroup: dataSource})
        })
      } else {
        this.setState({sceneGroup: []});
      }
    }, 800);
  }

  onChange = (value, actionType) => {
    if (actionType == 'itemClick') {
      console.log(value);
      this.setState({
        materialNameList:value
      })
    }
  }

  async getItemCategoryList() {
    try {
      let resp = await listItemCategoryList();
      this.setState({itemCategoryGroup:resp.rows});
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getStoreCategoryList() {
    try {
      let resp = await listStoreCategoryList();
      this.setState({storeCategoryGroup:resp.rows});
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getSelectableCrowdList() {
    try {
      let resp = await listSelectableCrowdList();
      console.log(resp);
      this.setState({crowdGroup:resp.rows});
    } catch (error) {
      api.onRequestError(error)
    }
  }

  removeEmptyProperty = (obj) => {
    var object = obj;
    for (var i in object) {
      var value = object[i];
      if (typeof value === 'object') {
        if (Array.isArray(value)) {
          if (value.length == 0) {
            delete object[i];
            continue;
          }
        }
        this.removeEmptyProperty(value);
      } else {
        if (value === '' || value === null || value === undefined) {
          delete object[i];
        }
      }
    }
    return object;
  }

  async submitCreate() {
    let self = this;
    this.field.validate(async(e) => {
      if (!doesFormHasErrors(e)) {
        let {materialNameList, mainRecommendCate, supplyCate, itemKeywordList, cateLevel, containCrowdList, addWeightPoolId} = self.state;
        console.log(mainRecommendCate.filter(v => v.level == 1).map(v => v.value));
        try { //.map(v => v.value)
          let cmd = {
            materialNameList,
            materialType: 1,
            itemRule: {
              mainRecommendCate1Id: mainRecommendCate.filter(v => v.level == 1).map(v => v.value),
              mainRecommendCate2Id: mainRecommendCate.filter(v => v.level == 2).map(v => v.value),
              mainRecommendCate3Id: mainRecommendCate.filter(v => v.level == 3).map(v => v.value),
              supplyCate1Id: supplyCate.filter(v => v.level == 1).map(v => v.value),
              supplyCate2Id: supplyCate.filter(v => v.level == 2).map(v => v.value),
              supplyCate3Id: supplyCate.filter(v => v.level == 3).map(v => v.value),
              itemKeywordList,
              addWeightPoolId
            },
            storeRule: {
              cateLevel1Id: cateLevel.filter(v => v.level == 1).map(v => v.value),
              cateLevel2Id: cateLevel.filter(v => v.level == 2).map(v => v.value),
            },
            crowdRule: {
              containCrowdList
            }
          }
          let resp = await addStrategy({...cmd});
          self.props.showCreate(false);
          self.props.reload();
          // this.setState({crowdGroup:resp.rows});
        } catch (error) {
          api.onRequestError(error)
        }
      }
    })
  }

  // 商品集ID检验
  checkDataSources = (rule, value, callback) => {
    if (value.length > 10) {
      callback('选品集ID输入最多10个');
    } else {
      callback();
    }
  }

  onPoolIdSearch = (keyword) =>{
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if(keyword){
        searchPickPoolVaguely({poolContent: keyword}).then((data) => {
          let list = data.rows || [];
          const dataSource = list.map(item => ({
            label:item.poolContent, value:item.poolId
          }));
          this.setState({poolIdsSet:dataSource})
        })
      }else{
        this.setState({poolIdsSet:[]});
      }
    }, 800);
  }

  removeDuplicate = (arr) =>{
    var hash = {};
    let result = arr.reduce(function(item, next) {
      hash[next.value] ? '' : hash[next.value] = true && item.push(next);
      return item
    }, []);
    return result;
  }

  render() {
    const t = {
      showSearch: true,
      multiple: true,
      expandTriggerType: 'hover',
      hasClear: true,
      useVirtual: true,
    }
    let {sceneGroup,itemCategoryGroup,storeCategoryGroup,crowdGroup,selectStore,mainSelectItem,supplySelectItem,containCrowdList,itemKeywordList,addWeightPoolId,poolIdsSet} = this.state;
    return (
      <Form {...formLayout} field={this.field} className="list-create">
        <Row>
          <Col span={20}>
            <FormItem required label="场景词:" {...formItemLayout}    requiredMessage={"场景词必填"}>
              <Select mode="multiple" showSearch placeholder="请选择场景词" name='materialNameList'
                      onChange={(value, actionType) => this.onChange(value, actionType)} onSearch={this.onSearch}
                      dataSource={sceneGroup} style={{width: '100%'}}/>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span={20}>
            <h3>门店圈选</h3>
            <FormItem label="门店类目:" {...formItemLayout} required   requiredMessage={"门店类目必填"}>
              <CascaderSelect style={{width: '100%'}} name='cateLevel'  dataSource={storeCategoryGroup} {...t} onChange={(v,d) => this.changeStoreCategory(v,d)} value={selectStore} />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span={20}>
            <h3>商品圈选</h3>
            <FormItem required label="主推商品类目:" {...formItemLayout} requiredMessage={"主推商品类目必填"}>
              <CascaderSelect style={{width: '100%'}} name='mainRecommendCate'  dataSource={itemCategoryGroup} {...t} onChange={(v,d) => this.changeItemCategory(v,d)} value={mainSelectItem} />
            </FormItem>
            <FormItem label="商品关键词:" {...formItemLayout} requiredMessage={"商品关键词必填"}>
              <Input onChange={(v)=>this.changeItemKeywordList(v)}  placeholder="请输入商品关键词" value={itemKeywordList[0]}  name='itemKeywordList' />
            </FormItem>
            <FormItem label="补充商品类目:" {...formItemLayout}  requiredMessage={"补充商品类目必填"}>
              <CascaderSelect style={{width: '100%'}}  name='supplyCate'dataSource={itemCategoryGroup} {...t} onChange={(v,d) => this.changeSupplyItemCategory(v,d)} value={supplySelectItem} />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span={20}>
            <h3>人群圈选</h3>
            <FormItem label="选择人群:" {...formItemLayout}>
              <Select  mode="multiple"  style={{width: '100%'}}  onChange={(v)=>this.changeCrowdList(v)} value={containCrowdList}>
                {crowdGroup.map((v)=>{
                  return <Select.Option value={v.crowdName}>{v.crowdName}</Select.Option>
                })}
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span={20}>
            <h3>商品提权</h3>
            <FormItem label="提权选品集id:" hasFeedback {...formItemLayout} validator={this.checkDataSources}>
              <Select mode="multiple" showSearch placeholder="请输入选品集ID，支持输入最多10个" name='addWeightPoolId' value={addWeightPoolId}  onChange={(v)=>this.changeAddWeightPoolId(v)} onSearch={this.onPoolIdSearch}  dataSource={poolIdsSet} style={{width:'100%'}} />
            </FormItem>
          </Col>
        </Row>
      </Form>
    )
  }
}
