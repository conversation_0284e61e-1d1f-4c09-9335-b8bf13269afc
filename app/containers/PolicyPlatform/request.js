import { sceneReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

export function listStrategy(params) {
  return sceneReq.post('/api/strategy/read/listStrategy', params).then(onRequestSuccess)
}

export function listViewableStrategy(params) {
  return sceneReq.post('/api/strategy/read/listViewableStrategy', params).then(onRequestSuccess)
}

export function SearchRecentSceneByText(params) {
  return sceneReq.post('/api/scene/read/searchRecentSceneByText', params).then(onRequestSuccess)
}

export function listItemCategoryList() {
  return sceneReq.get('/api/common/read/listItemCategoryList').then(onRequestSuccess)
}

export function listStoreCategoryList() {
  return sceneReq.get('/api/common/read/listStoreCategoryList').then(onRequestSuccess)
}

export function listSelectableCrowdList() {
  return sceneReq.post('/api/strategy/read/listSelectableCrowdList').then(onRequestSuccess)
}

export function addStrategy(params) {
  return sceneReq.post('/api/strategy/write/addStrategy',params).then(onRequestSuccess)
}

export function getMaterialStrategyDetail(params) {
  return sceneReq.post('/api/strategy/read/getMaterialStrategyDetail',params).then(onRequestSuccess)
}

export function offlineStrategy(params) {
  return sceneReq.post('/api/strategy/write/offlineStrategy',params).then(onRequestSuccess)
}

export function listPutin(params) {
  return sceneReq.post('/api/putin/read/listPutin',params).then(onRequestSuccess)
}

export function listViewablePutin(params) {
  return sceneReq.post('/api/putin/read/listViewablePutin',params).then(onRequestSuccess)
}

export function addPutin(params) {
  return sceneReq.post('/api/putin/write/addPutin',params).then(onRequestSuccess)
}

export function deletePutin(params) {
  return sceneReq.post('/api/putin/write/deletePutin',params).then(onRequestSuccess)
}


/**资源位配置-查看配置 获取配置信息*/
export function queryConfigList(params) {
  return putInReq.post('/api/deliverymanage/queryConfigList', params).then(onRequestSuccess)
}

/**更新配置状态 1删除、2停用**/
export function updateConfigState(params) {
  return putInReq.post('/api/deliverymanage/updateConfigState', params).then(onRequestSuccess)
}

/**复制配置信息 **/
export function copyDelevery(params) {
  return putInReq.post('/api/deliverymanage/copyDelivery', params).then(onRequestSuccess)
}

/**资源位配置-投放配置页 获取单条资源信息**/
export function getResource(params) {
  return putInReq.get(`/api/pageManage/getResource/${params.pageId}/${params.resourceId}`).then(onRequestSuccess)
}

/**修改资源位投放配置 **/
export function saveResourcePutInConfig(params) {
  return putInReq.post('/api/pageManage/saveResourcePutInConfig', params).then(onRequestSuccess)
}

/**获取商超频道页-投放配置信息 **/
export function getPagePutInConfig(params) {
  return putInReq.get(`/api/pageManage/getPagePutInConfig/${params.pageId}`).then(onRequestSuccess)
}

/**保存资源位投放配置 **/
export function savePagePutInConfig(params) {
  return putInReq.post('/api/pageManage/savePagePutInConfig', params).then(onRequestSuccess)
}

/**获取楼层去重设置弹窗去重信息 **/
export function getDistinctEnableConfig(params) {
  return putInReq.get(`/api/pageManage/getDistinctEnableConfig/${params.pageId}`).then(onRequestSuccess)
}

/**获取楼层去重设置弹窗资源位列表 **/
export function queryDistinctResourceList(params) {
  return putInReq.get(`/api/pageManage/queryDistinctResourceList/${params.pageId}`).then(onRequestSuccess)
}

/**资源位列表**/
export function queryResourceList(params) {
  return putInReq.post('/api/pageManage/queryResourceList', params).then(onRequestSuccess)
}

/**保存楼层去重设置信息 **/
export function saveDistinctEnableConfig(params) {
  return putInReq.post('/api/pageManage/saveDistinctEnableConfig', params).then(onRequestSuccess)
}

/**去投放**/
export function createDelivery(params) {
  return putInReq.post('/api/deliverymanage/createDelivery', params).then(onRequestSuccess)
}

/**获取页面配置信息**/
export function getPageBaseConfig(params) {
  return putInReq.get(`/api/pageManage/getPageBaseConfig/${params.pageId}`).then(onRequestSuccess)
}

/**查看投放配置信息**/
export function queryPosConfig(params) {
  return putInReq.post('/api/deliverymanage/queryPosConfig',params).then(onRequestSuccess)
}

/**获得操作记录**/
export function getOperationList(params) {
  return putInReq.post('/api/operation/getOperationList',params).then(onRequestSuccess)
}

/**获得操作记录**/
export function getParentConfigList(params) {
  return putInReq.post('/api/deliverymanage/getParentConfigList',params).then(onRequestSuccess)
}

/**设置schema**/
export function setSchema(params) {
  return putInReq.post('/api/deliverymanage/setSchema',params).then(onRequestSuccess)
}

/**设置元数据schema**/
export function setMetadataSchema(params) {
  return putInReq.post('/api/deliverymanage/setMetadataSchema',params).then(onRequestSuccess)
}

/**设置资源位是否需要校验**/
export function setResourcePermission(params) {
  let {pageId,resourceId,ignorePermission} = params;
  return putInReq.get(`/api/acl/channel/setResourcePermission/${pageId}/${resourceId}/${ignorePermission}`).then(onRequestSuccess)
}


/**获取schema**/
export function getScheduleTemplate(params) {
  return putInReq.post('/api/deliverymanage/getScheduleTemplate',params).then(onRequestSuccess)
}

/**氛围设置**/
export function editPageStyle(params) {
  return putInReq.post('/api/pageManage/editPageStyle',params).then(onRequestSuccess)
}

/**氛围设置**/
export function getSceneWord() {
  return putInReq.get('/api/deliverymanage/getSceneWord',).then(onRequestSuccess)
}

/**通过关键字模糊匹配查询用户**/
export function queryUserByKeyWord(userKeyWord) {
  return putInReq.get(`/api/acl/channel/queryUserByKeyWord/${userKeyWord}`).then(onRequestSuccess)
}

/**给用户添加资源位权限**/
export function createAclUser(params) {
  return putInReq.post('/api/acl/channel/createAclUser',params).then(onRequestSuccess)
}

/**给用户删除权限**/
export function deleteAclUser(params) {
  return putInReq.post('/api/acl/channel/deleteAclUser',params).then(onRequestSuccess)
}

/**给用户查询权限**/
export function queryAclUser(params) {
  return putInReq.post('/api/acl/channel/queryAclUser',params).then(onRequestSuccess)
}

/**资源位权限校验**/
export function validateAclResource(params) {
  return putInReq.post('/api/acl/channel/validateAclResource',params).then(onRequestSuccess)
}

/**配置权限校验**/
export function validateAclSchedule(params) {
  return putInReq.post('/api/acl/channel/validateAclSchedule', params).then(onRequestSuccess)
}

/**通过活动ID获取活动类型**/
export function queryActivityPoolType(params) {
  return putInReq.post('/api/putInActivity/queryActivityPoolType', params).then(onRequestSuccess)
}

/**按名称或者活动id或者创建人的名字搜索活动集**/
export function searchPickPoolVaguely(params) {
  return sceneReq.post('/api/common/read/searchPickPoolVaguely', params).then(onRequestSuccess)
}
