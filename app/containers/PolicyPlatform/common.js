import React from 'react';
import moment from 'moment';
import {formatTimeStamp, FORMAT} from '@/utils/time';

export const getQueryValue = function (queryName) {
  let url = window.location.href;
  if (url.split("?").length > 0) {
    var index = url.lastIndexOf("?");
    var query = decodeURI(url.slice(index + 1, url.length));
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      if (pair[0] == queryName) {
        return pair[1];
      }
    }
  }
  return null;
}

export const resourceMap = [
  {
    name: "养成品类",
    key: 'category',
    resourceId: '2001',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    adminName:'张三',
    locationList: [
      {id:'1',name: '养成品类(1-10)'},
      {id:'2',name: '养成品类'}
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1qX8atlr0gK0jSZFnwu2RRXXa.png'
  },
  {
    name: "Banner", key: 'banner', resourceId: '2002',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    adminName:'张三',
    locationList: [
      {id:'3',name: 'banner(1-5)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1bSZ7s7Y2gK0jSZFgwu35OFXa.png'
  },
  {
    name: "金刚位", key: 'diamond_position', resourceId: '2003',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'4',name: '金刚位(1-12)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1yog_s4D1gK0jSZFywu3iOVXa.png'
  },
  {
    name: "大促入口", key: 'promotion', resourceId: '4',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'1041',name: '大促入口(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1Xm76sVP7gK0jSZFjwu35aXXa.png'
  },
  {
    name: "真实惠-商超", key: 'benefit_market', resourceId: '5',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'1051',name: '标题'},
      {id:'1052',name: '商品'},
      {id:'1053',name: '门店'},
      {id:'1054',name: '品牌'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1sWo2sWL7gK0jSZFBwu0ZZpXa.png'
  },
  {
    name: "场景卡片", key: 'scene_card', resourceId: '6',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'1061',name: '场景卡片'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1hkA7s.T1gK0jSZFhwu1AtVXa.png'
  },
  {
    name: "商品瀑布流", key: 'good_list', resourceId: '7',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'1071',name: '商品瀑布流(1-10)'},
      {id:'1072',name: '商品瀑布流(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1LQM6sVP7gK0jSZFjwu35aXXa.png'
  },
  {
    name: "大牌驾到", key: 'major_suit', resourceId: '8',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'1081',name: '大牌驾到(1-5)'},
      {id:'1082',name: '大牌驾到(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1M8U6sVP7gK0jSZFjwu35aXXa.png'
  },
  {
    name: "简单入口", key: 'simple_entry', resourceId: '9',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'1091',name: '简单入口(1-9)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1QJ.5sVY7gK0jSZKzwu1ikpXa.png'
  },
  {
    name: "安心菜场", key: 'safe_market', resourceId: '10',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10101',name: '安心菜场(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1DIg5sVY7gK0jSZKzwu1ikpXa.png'
  },
  {
    name: "超值低价", key: 'low_price', resourceId: '11',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10111',name: '超值低价(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1lD.8s5_1gK0jSZFqwu3paXXa.png'
  },
  {
    name: "优选商家", key: 'preferred_merchant', resourceId: '12',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10121',name: '优选商家(1-2)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1IoI_sYj1gK0jSZFOwu37GpXa.png'
  },
  {
    name: "真实惠", key: 'benefit', resourceId: '13',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10131',name: '真实惠(标题)'},
      {id:'10132',name: '真实惠(1)'},
      {id:'10133',name: '真实惠(兜底)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1nDA5s.H1gK0jSZSywu0tlpXa.png'
  },
  {
    name: "红包雨", key: 'red_enveloped_rain', resourceId: '14',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10141',name: '红包雨(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1OmKysubviK0jSZFNwu1ApXXa.png'
  },
  {
    name: "浮窗", key: '', resourceId: '15',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10151',name: '浮窗(1)'},
    ],
    imageUrl: ''
  },
  {
    name: "大促氛围", key: '', resourceId: '16',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10161',name: '氛围(1)'},
    ],
    imageUrl: ''
  },
  {
    name: "大促入口-医药", key: '', resourceId: '17',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10171',name: 'banner(1-5)'},
    ],
    imageUrl: ''
  },
  {
    name: "简单入口-医药", key: '', resourceId: '18',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10181',name: '简单入口(1)'},
    ],
    imageUrl: ''
  },
]

export const categoryMap = [
  {
    name: "banner", key: 'banner', resourceId: '21',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10211',name: 'banner(1-5)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1f5tpteH2gK0jSZJnwu1T1FXa.png'
  },
  {
    name: "二级tab", key: 'second_tab', resourceId: '22',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10221',name: '二级tab(1-10)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1l0hmtkY2gK0jSZFgwu35OFXa.png'
  },
]

export const goldMap = [
  {
    name: "banner", key: 'banner', resourceId: '31',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10311',name: 'banner(1-5)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1bSZ7s7Y2gK0jSZFgwu35OFXa.png'
  },
  {
    name: "二级tab", key: 'second_tab', resourceId: '32',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10321',name: '二级tab(1-10)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1l0hmtkY2gK0jSZFgwu35OFXa.png'
  },
]

export const benefitMap = [
  {
    name: "领红包", key: 'get_red_packets', resourceId: '41',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10411',name: '红包(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1C98ltlv0gK0jSZKbwu2K2FXa.png'
  },
  {
    name: "超级爆品", key: 'super_explosive', resourceId: '42',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10421',name: '超级爆品(1)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB19_4mtoT1gK0jSZFhwu1AtVXa.png'
  },
  {
    name: "分类tab", key: 'category_tab', resourceId: '43',
    createTime:'2019.12.24',
    anchor:'J_1102519047',
    spmc:'1102519047',
    versionNo:'5.0.1',
    versionId:'276195',
    description:'模块(饿了么-新零售)】门店新客',
    locationList: [
      {id:'10431',name: '分类tab(1-8)'},
    ],
    imageUrl: 'https://gtms04.alicdn.com/tps/i4/TB1iy8qtkT2gK0jSZFkwu3IQFXa.png'
  },
]

let dailyPage = [
    {pageId: '200', pageName: '商超频道页', url: '/channelManage/market/index', isInside: false},
    {pageId: '300', pageName: '养成品类内页', url: '/channelManage/market/category', isInside: true},
    {pageId: '400', pageName: '金刚位内页', url: '/channelManage/market/gold', isInside: true},
    {pageId: '500', pageName: '真实惠内页', url: '/channelManage/market/benefit', isInside: true},
    {pageId: '600', pageName: '买菜频道页', url: '/channelManage/green/index', isInside: false},
    {pageId: '700', pageName: '水果频道页', url: '/channelManage/fruit/index', isInside: false},
    {pageId: '800', pageName: '鲜花频道页', url: '/channelManage/flower/index', isInside: false},
    {pageId: '21', pageName: '医药频道页', url: '/channelManage/medicine/index', isInside: false},
    {pageId: '1000', pageName: '饿了么首页', url: '/channelManage/ele/index', isInside: false},
    {pageId: '1100', pageName: '时令好货内页', url: '/channelManage/ele/seasonalgoods', isInside: true},
  ];
let ppePage = [
  {pageId: '100067000', pageName: '商超频道页(适老版)', url: '/channelManage/marketAged/index', isInside: false},
  {pageId: '100069000', pageName: '天天特价内页', url: '/channelManage/marketAged/special', isInside: true},
  {pageId: '100003000', pageName: '商超频道页', url: '/channelManage/market/index', isInside: false},
  {pageId: '100004000', pageName: '养成品类内页', url: '/channelManage/market/category', isInside: true},
  {pageId: '100029000', pageName: '天天特价内页', url: '/channelManage/market/special', isInside: true},
  {pageId: '100005000', pageName: '金刚位内页', url: '/channelManage/market/gold', isInside: true},
  {pageId: '100006000', pageName: '真实惠内页', url: '/channelManage/market/benefit', isInside: true},
  {pageId: '100007000', pageName: '买菜频道页', url: '/channelManage/green/index', isInside: false},
  {pageId: '100024000', pageName: '真实惠内页', url: '/channelManage/green/benefit', isInside: true},
  {pageId: '100033000', pageName: '天天特价内页', url: '/channelManage/green/special', isInside: true},
  {pageId: '100009000', pageName: '水果频道页', url: '/channelManage/fruit/index', isInside: false},
  {pageId: '100031000', pageName: '天天特价内页', url: '/channelManage/fruit/special', isInside: true},
  {pageId: '100023000', pageName: '真实惠内页', url: '/channelManage/fruit/benefit', isInside: true},
  {pageId: '100008000', pageName: '鲜花频道页', url: '/channelManage/flower/index', isInside: false},
  {pageId: '100028000', pageName: '真实惠内页', url: '/channelManage/flower/benefit', isInside: true},
  {pageId: '100032000', pageName: '天天特价内页', url: '/channelManage/flower/special', isInside: true},
  {pageId: '100021000', pageName: '医药频道页', url: '/channelManage/medicine/index', isInside: false},
  // {pageId: '100025000', pageName: '饿了么首页', url: '/channelManage/ele/index', isInside: false},
  {pageId: '100025000', pageName: '饿了么首页(新)', url: '/channelManage/ele/index_new', isInside: false},
  {pageId: '100034000', pageName: '天天特价内页', url: '/channelManage/ele/special', isInside: false},
  {pageId: '100026000', pageName: '时令好货内页', url: '/channelManage/ele/seasonalgoods', isInside: false},
];
let prodPage = [
  {pageId: '67000', pageName: '商超频道页(适老版)', url: '/channelManage/marketAged/index', isInside: false},
  {pageId: '69000', pageName: '天天特价内页', url: '/channelManage/marketAged/special', isInside: true},
  {pageId: '3000', pageName: '商超频道页', url: '/channelManage/market/index', isInside: false},
  {pageId: '4000', pageName: '养成品类内页', url: '/channelManage/market/category', isInside: true},
  {pageId: '29000', pageName: '天天特价内页', url: '/channelManage/market/special', isInside: true},
  {pageId: '5000', pageName: '金刚位内页', url: '/channelManage/market/gold', isInside: true},
  {pageId: '6000', pageName: '真实惠内页', url: '/channelManage/market/benefit', isInside: true},
  {pageId: '7000', pageName: '买菜频道页', url: '/channelManage/green/index', isInside: false},
  {pageId: '24000', pageName: '真实惠内页', url: '/channelManage/green/benefit', isInside: true},
  {pageId: '33000', pageName: '天天特价内页', url: '/channelManage/green/special', isInside: true},
  {pageId: '9000', pageName: '水果频道页', url: '/channelManage/fruit/index', isInside: false},
  {pageId: '31000', pageName: '天天特价内页', url: '/channelManage/fruit/special', isInside: true},
  {pageId: '23000', pageName: '真实惠内页', url: '/channelManage/fruit/benefit', isInside: true},
  {pageId: '8000', pageName: '鲜花频道页', url: '/channelManage/flower/index', isInside: false},
  {pageId: '28000', pageName: '真实惠内页', url: '/channelManage/flower/benefit', isInside: true},
  {pageId: '32000', pageName: '天天特价内页', url: '/channelManage/flower/special', isInside: true},
  {pageId: '21000', pageName: '医药频道页', url: '/channelManage/medicine/index', isInside: false},
  // {pageId: '1000', pageName: '饿了么首页', url: '/channelManage/ele/index', isInside: false},
  {pageId: '25000', pageName: '饿了么首页(新)', url: '/channelManage/ele/index_new', isInside: false},
  {pageId: '34000', pageName: '天天特价内页', url: '/channelManage/ele/special', isInside: false},
  {pageId: '1100', pageName: '时令好货内页', url: '/channelManage/ele/seasonalgoods', isInside: false},
];
let pageSet = {
  'daily': JSON.parse(JSON.stringify(dailyPage)),
  'localdev': JSON.parse(JSON.stringify(dailyPage)),
  'ppe': ppePage,
  'prod': prodPage
}
export var pageMap = pageSet[window.configEnv];

// export var pageMap = (location.href.indexOf('pre-kunlun.alibaba-inc.com') >= 0) ? [
//   {pageId: '100003000', pageName: '商超频道页', url: '/channelManage/market/index', isInside: false},
//   {pageId: '100004000', pageName: '养成品类内页', url: '/channelManage/market/category', isInside: true},
//   {pageId: '100005000', pageName: '金刚位内页', url: '/channelManage/market/gold', isInside: true},
//   {pageId: '100006000', pageName: '真实惠内页', url: '/channelManage/market/benefit', isInside: true},
//   {pageId: '100007000', pageName: '买菜频道页', url: '/channelManage/green/index', isInside: false},
//   {pageId: '100024000', pageName: '真实惠内页', url: '/channelManage/green/benefit', isInside: true},
//   {pageId: '100009000', pageName: '水果频道页', url: '/channelManage/fruit/index', isInside: false},
//   {pageId: '100023000', pageName: '真实惠内页', url: '/channelManage/fruit/benefit', isInside: true},
//   {pageId: '100008000', pageName: '鲜花频道页', url: '/channelManage/flower/index', isInside: false},
//   {pageId: '100021000', pageName: '医药频道页', url: '/channelManage/medicine/index', isInside: false},
//   {pageId: '1000', pageName: '饿了么首页', url: '/channelManage/ele/index', isInside: false},
//   {pageId: '1100', pageName: '时令好货内页', url: '/channelManage/ele/seasonalgoods', isInside: true},
// ] : [
//   {pageId: '200', pageName: '商超频道页', url: '/channelManage/market/index', isInside: false},
//   {pageId: '300', pageName: '养成品类内页', url: '/channelManage/market/category', isInside: true},
//   {pageId: '400', pageName: '金刚位内页', url: '/channelManage/market/gold', isInside: true},
//   {pageId: '500', pageName: '真实惠内页', url: '/channelManage/market/benefit', isInside: true},
//   {pageId: '600', pageName: '买菜频道页', url: '/channelManage/green/index', isInside: false},
//   {pageId: '700', pageName: '水果频道页', url: '/channelManage/fruit/index', isInside: false},
//   {pageId: '800', pageName: '鲜花频道页', url: '/channelManage/flower/index', isInside: false},
//   {pageId: '21', pageName: '医药频道页', url: '/channelManage/medicine/index', isInside: false},
//   {pageId: '1000', pageName: '饿了么首页', url: '/channelManage/ele/index', isInside: false},
//   {pageId: '1100', pageName: '时令好货内页', url: '/channelManage/ele/seasonalgoods', isInside: true},
// ]

export const jumpTypeMap = {
  1: '内页',
  2: 'h5链接',
  3: '门店',
  4: '搜索页'
}

export const deliveryChannelsMap = {
  0:'全部',
  1:'支付宝生活节',
  2:'饿了么APP',
  3:'支付宝',
  4:'手淘',
  5:'口碑',
  6:'微信',
  7:'N元购'
}

export const weekMap = {
  1:'周一',
  2:'周二',
  3:'周三',
  4:'周四',
  5:'周五',
  6:'周六',
  7:'周日',
}

export const insertMap = {
  1:'养成品类',
  2:'Banner',
  3:'二级Tab',
  4:'金刚位',
  5:'大促入口',
  6:'真实惠-商超',
  7:'场景卡片',
  8:'商品瀑布流',
  9:'大牌驾到',
  10:'简单入口',
  11:'安心菜场',
  12:'超值低价',
  13:'优选商家',
  14:'真实惠',
  15:'红包雨',
  16:'领红包',
  17:'超级爆品',
  18:'分类Tab',
  19:'大促入口-医药',
  20:'简单入口-医药',
  21:'大促氛围',
  22:'浮窗',
  23:'品牌会场',
  24:'大促入口-买菜',
  25:'时令好货-首页入口',
  26:'分享',
  27:'时令好货-承接组件',
  28:'时令好货-红包模块',
  29:'场景分类',
  30:'养成品内页-Banner',
  31:'养成品内页-二级Tab',
  32:'Banner-医药',
  33:'TAB',
  34:'搜索框',
  35:'热搜词',
  36:'天天特价',
  37:'人工榜单'
}

export const skipIconContentTypeMap = {
  1:'红包文案',
  2:'普通文案'
}


export const positionMap = {
  1:'F1',
  2:'F2',
  3:'F3',
  4:'F4',
  5:'F5',
  6:'F6',
  7:'F7',
  8:'F8',
  9:'F9'
}
