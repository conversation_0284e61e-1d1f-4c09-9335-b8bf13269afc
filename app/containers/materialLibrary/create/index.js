
import React from 'react';
import { Form, Radio, Field, Icon, Button, Table, Dialog, Pagination, Message, Upload, Input } from '@alife/next';
import { PageBase } from '@/containers/base';
import * as validators from '@/utils/validators';
import { onRequestError }  from '@/utils/api';
import { putInReq } from '@/utils/request'
import { checkExistMaterial, createMaterial } from '../request';
import './index.scss'

const FormItem = Form.Item;

const formItemLayout = {
    labelCol: { 
        span: 4, 
    },
    wrapperCol: {
        span: 20
    },
    labelAlign: 'center',
    labelTextAlign: 'center'
}

const formLayout = {
    style: {
      margin: '20px auto'
    },
  }

export class CreateMaterialLibrary extends PageBase {
    constructor(props) {
      super(props)
      this.state = {
        skuImgValues: [],
        visible: false,
        validateInfo: {
          width: 654,
          height: 376,
          error: false,
        }
      }

      this.field = new Field(this, {
        values: { 
          skuIdList: '',
          skuDesc: '',
        }
      })
    }

    componentDidMount () {
        
    }

    switchSetDialog = (isOpen) => {
        this.setState({ visible: isOpen })
    }

    openDialog = () => {
      this.switchSetDialog(true);
     
    }

    beforeUpload = (file) => {
        const { validateInfo: v } = this.state;
        if (!v) return true;
        const reader = new FileReader();
        reader.onload = () => {
          const img = new Image();
          img.onload = () => {
            const { size, type } = file;
            const { width, height } = img;
            if (v.width && Math.abs(v.width - width) > 1) return Message.error(`图片宽度需为${v.width}px，目前宽度${width}px`);
            if (v.height && Math.abs(v.height - height) > 1) return Message.error(`图片高度需为${v.height}px，目前高度${height}px`);
            this.upLoadPic(reader.result, file.name);
          };
          img.src = reader.result;
        };
        reader.readAsDataURL(file);
        return false;
    }
    
    upLoadPic = (base64, fileName) => {
        base64 = base64.replace(/^data\:.*?;base64,/, '');
        return putInReq.post('/api/pic/uploadPic', {
          userId: '1',
          base64,
          name: fileName
        }).then((res) => {
          if(res.status === 200 && res.data.code === '200') {
            let skuImgValues = [ ...this.state.skuImgValues, res.data.data ];
            let validateInfo = { ...this.state.validateInfo, error: skuImgValues.length == 0 ? true : false };
            this.setState({
              skuImgValues,
              validateInfo,
            });
            Message.show('上传成功');
          } else {
            Message.error(`上传失败，${res.error && (res.error.message || '未知错误')}`);
          }
        }).catch((res) => {
          Message.error(`上传失败，${res.error && (res.error.message || '未知错误')}`);
        })
    }

    // 删除图片
    onFileRemove = (info) => {
      console.log(info, 'onFileRemove')
      let skuImgValues = this.state.skuImgValues.filter(url => url != info.url);
      let validateInfo = { ...this.state.validateInfo, error: skuImgValues.length == 0 ? true : false };
      this.setState({
        skuImgValues,
        validateInfo,
      })
    }

    // onFileChange = (info) => {
      // console.log(info, 'info')
      // let skuImgValues = info.filter(file => file.url);
      // let validateInfo = { ...this.state.validateInfo, error: skuImgValues.length == 0 ? true : false };
      // this.setState({
      //   skuImgValues,
      //   validateInfo,
      // })
    // }

    // 检查id是否存在
    doCheckExistMaterial = () => {
      let params = {
        ...this.field.getValues(),
        skuImgBase64Image: this.state.skuImgValues,
        skuIdList: this.field.getValue('skuIdList').split(/[,\s]+/gm).filter(e => !!e).map(e => +e.trim())
      }
      checkExistMaterial({...params}).then((data) => { 
        if(data.length > 0) {
          Dialog.confirm({
            title: '是否覆盖这些id？',
            content: String(data),
            onOk: () => this.doCreateMaterial({ ...params }),
            onCancel: () => this.doCreateMaterial({ ...params, existSkuIdList: data }),
          });
        } else {
          this.doCreateMaterial(params)
        }
      }).catch(onRequestError)
    }

    // 创建素材
    doCreateMaterial = (params) => {
      createMaterial({...params}).then((data) => { 
        this.switchSetDialog(false);
        this.props.reload();
        Message.success('创建成功')
        this.setState({ skuImgValues: [] })
      }).catch(onRequestError)
    }

    confirm = () => {
      let { skuImgValues, validateInfo } = this.state;
      this.field.validate((error, values) => {
        this.setState({
          validateInfo: { ...validateInfo, error: skuImgValues.length == 0 ? true : false }
        })
        if (error === null && skuImgValues.length > 0) {
          this.doCheckExistMaterial();
        }
      });
    }

    render() {
      let { skuImgValues = [], validateInfo } = this.state;
      const filteredSkuImgValuesValue = skuImgValues
        .filter((value) => !!value)
        .map((url) => {
          return { downloadURL: url, imgURL: url, url };
        });

      return (
        <div className="create-matirial-library-btn">
            <Button className="go-set-btn" type="primary" onClick={this.openDialog}>新建素材</Button>
            <Dialog
                title="新建素材"
                className="create-matirial-librar-dialog"
                style={{ width: 820 }}
                visible={this.state.visible}
                onClose={() => this.switchSetDialog(false)}
                footer={[
                    <Button onClick={() => this.switchSetDialog(false)}>取消</Button>, 
                    <Button 
                        type="primary" 
                        onClick={this.confirm}
                    >确定</Button>
                ]}
            >
                <div className="dialog-content">
                    <Form className="" {...formLayout} field={this.field}>
                      <FormItem label="商品ID：" {...formItemLayout}>
                        <Input.TextArea
                          name="skuIdList"
                          autoHeight={{minRows: 7}}
                          placeholder={'输入多个可用英文逗号、空格、回车隔开'}
                          {...this.field.init('skuIdList', {
                            rules: [{
                              required: true,
                              message: '请输入ID信息'
                            }, {
                              validator: validators.map(validators.createRegexMatchMiddleware(/^(\d+)([,\s]+\d+)*$/gm, '输入格式不合法'), (_a, b)=> ({value: b.trim()})),
                              trigger: ['onBlur']
                            }]
                          })}
                        />
                      </FormItem>
                      <FormItem label="商品图片素材：" {...formItemLayout}>
                        <Upload.Card
                          name="skuImgBase64Image"
                          accept="image/png, image/jpg, image/jpeg, image/gif, image/bmp"
                          limit={5}
                          beforeUpload={this.beforeUpload}
                          onRemove={this.onFileRemove}
                          // onChange={this.onFileChange}
                          value={filteredSkuImgValuesValue}
                        />
                        <div className={`tip ${validateInfo.error ? 'error' : ''}`}>至少上传1张尺寸为654x376的图片</div>
                      </FormItem>
                      <FormItem label="商品描述：" {...formItemLayout}>
                        <Input.TextArea
                          name="skuDesc"
                          autoHeight={{minRows: 3}}
                          placeholder={'请输入商品描述'}
                          maxLength={100}
                          {...this.field.init('skuDesc', {
                            rules: [{
                              required: true,
                              message: '请输入商品描述'
                            }]
                          })}
                        />
                      </FormItem>
                    </Form>
                </div>
            </Dialog>
        </div>
        
      )
    }
  }
