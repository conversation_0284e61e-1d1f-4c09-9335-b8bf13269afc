/**
 * PutInListPage
 * 投放平台
 */

import React, { Fragment } from 'react';
import { PageBase } from '@/containers/base';
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link,withRouter} from 'react-router-dom'
import { Form, Input, Grid, Button, Breadcrumb, Dialog } from '@alife/next';
import { manageList, setTableCol, filterForm } from '@/components/Table';
import { LinkButton } from '@/components/Button';
import { PageWrapper } from '@/components/PageWrapper';
import { formatTimeStamp } from '@/utils/time';
import { DialogBtn, GoldLogLink } from '@/components/Button';
// import { getPutInList, delPutIn } from '../request';
import { onRequestError } from '@/utils/api'
import { isAllNumber } from '@/utils/validators';
import { doesFormHasErrors } from '@/utils/formTool';
import { track } from '@/utils/aplus';
import './index.scss' 
import { queryMaterial, deleteMaterial } from '../request';
import { CreateMaterialLibrary } from '../create'

const FormItem = Form.Item;
const { Row, Col } = Grid;

const PreviewImage = ({ url }) => {
  const previewDialog = () => {
    Dialog.show({
      title: '图片预览',
      footer:false,
      content: (
        <div style={{ textAlign: 'center' }}>
          <img src={url} style={{ maxWidth: 700, maxHeight: 400 }}/>
        </div>
      ),
      cancelProps: { style: { display: 'none' } },
    })
  }
  return (
    <img className="preview-img" src={url} onClick={previewDialog}/>
  )
}

/**操作列 */
const OptionCol = ({ record, reload }) => {
  const { skuId } = record;

  const onOk = () => {
    deleteMaterial(skuId).then(() => {
      reload()
    }).catch(onRequestError)
  }

  return (
    <Fragment>
      <DialogBtn
        onOk={onOk}
        content='确认删除该活动?'
      >删除</DialogBtn>
    </Fragment>
  )
}

const defaultColType = { title: '' }

const columns = [
  { title: '序号', dataIndex: 'id', width:'10%' },
  { title: '商品id', dataIndex: 'skuId', width:'18%' },
  // { title: '商品名称', dataIndex: 'skuName', width:'15%'},
  // { title: '门店id', dataIndex: 'storeId', width:'8%' },
  // { title: '门店名称', dataIndex: 'storeName', width:'15%' },
  { title: '商品图片素材', dataIndex: 'imageUrlList', width:'30%', cell: (value) => value.map((url) => <PreviewImage url={url}/>)},
  { title: '商品描述', dataIndex: 'skuDesc', width:'20%' },
  { title:'操作', width:'15%', cell:function(value, rowIndex, record){
    return <OptionCol record={record} reload={this.reload}/>
  }}
]

function getCol(type) {
  return { ...defaultColType, ...type }
}

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: {
      span: 20
  },
  style: {
      width: '100%'
  },
  labelAlign: 'left',
  labelTextAlign: 'left'
};


const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

/**检测id格式 */
const checkId = function(rule, value, callback){
  const errors = isAllNumber(value);
  if(errors.length && value){
    callback('格式错误')
  } else {
    callback();
  }
}

function PutInForm({ field, searchData, reload }) {
  const onSearch = () => {
    field.validate((e) => {
      if(!doesFormHasErrors(e)){
        searchData()
      }
    })
  }

  return (
    <Form {...formLayout} field={field}>
      <Row>
        <Col span={6}>
          <FormItem label="商品ID" {...formItemLayout} validator={checkId}>
            <Input placeholder="请输入商品ID" name="skuId" />
          </FormItem>
        </Col>
        {/* <Col>
          <FormItem label="商品名称" {...formItemLayout}>
            <Input placeholder="请输入商品名称" name="skuName" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="门店id" {...formItemLayout} validator={checkId}>
            <Input placeholder="请输入门店id" name="storeId" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="门店名称" {...formItemLayout}>
            <Input placeholder="请输入门店名称" name="storeName" />
          </FormItem>
        </Col> */}
        <Col>
          <Button type="primary" onClick={onSearch} style={{ marginLeft: 20 }}>查询</Button>&nbsp;&nbsp;
        </Col>
      </Row>
    </Form>
  )
}

const PutInFilterForm = filterForm(PutInForm, ['skuId', 'skuName', 'storeId', 'storeName'])
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(PutInTable, PutInFilterForm,
  async (search) => {
    let { query } = search;
    let { activityId } = query;
    if(activityId){
      query = { ...query, activityId:+activityId }
    }
    const listData = await queryMaterial({...search, query});
    return listData
  })
  
class MaterialLibraryList extends PageBase {
  constructor(props) {
    super(props)
    
  }

  reload = () => {
    this.refs.manageList.reload();
  }

  render() {
    return (
      <PageBase.Container className="material-library-list" >
        <div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item >运营平台</Breadcrumb.Item>
            <Breadcrumb.Item >素材库</Breadcrumb.Item>
            <Breadcrumb.Item>时令好货素材库</Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <PageWrapper title="时令好货素材库">
          <ManageList ref="manageList">
            <CreateMaterialLibrary reload={this.reload}/>
          </ManageList>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}

export const LogTimeMaterialLibraryListPage = logTimeComponent(withRouter(MaterialLibraryList), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MaterialLibraryListPage = permissionAccess(LogTimeMaterialLibraryListPage, async (activityId) => {
  return await ACLPermission(activityId);
})



