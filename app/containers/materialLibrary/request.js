import { putInReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

/**获取素材列表*/
export function queryMaterial(params) {
  return putInReq.post('/api/material/manage/queryMaterial', params).then(onRequestSuccess)
}

/**删除素材**/
export function deleteMaterial(materialId) {
  return putInReq.get(`/api/material/manage/deleteMaterial/${materialId}`).then(onRequestSuccess)
}

/**检查素材是否存在*/
export function checkExistMaterial(params) {
  return putInReq.post('/api/material/manage/checkExistMaterial', params).then(onRequestSuccess)
}

/**新建素材*/
export function createMaterial(params) {
  return putInReq.post('/api/material/manage/createMaterial', params).then(onRequestSuccess)
}
