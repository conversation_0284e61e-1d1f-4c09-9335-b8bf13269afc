
@import '../../../styles/common.scss';

.g-add-detail-table-content {
  max-width: 80vw;
}

.CommodityPoolDetailPage{
  padding: 20px 0;

  .img-wrapper {
    width: 60px; height: 60px;
    position: relative;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
    }

    &:hover {
      .bar {
        transform: translateY(0);
      }
    }

    .bar {
      transform: translateY(100%);
      transition: all 300ms ease-in-out;
      cursor: pointer;
      text-align: center;
      position: absolute;
      width: 100%;
      height: 20px;
      bottom: 0;
      background-color: rgba(0,0,0,0.40);
      color: white;
      font-size: 12px;
    }
  }

  .btns {
    //display: inline-block;
    margin-left: 8px;
    button {
      margin:0 4px;
    }
  }
  .nav-wrapper {
    padding: 0 20px;
  }

  .panel {
    background-color: white;
    margin-top: 20px;
    padding: 20px;
  }

  .table-panel {
    .btns button {
      margin-right: 1em;
    }
  }

  .title-total{
    font-size: 16px;
    color: #999999;
    line-height: 16px;
    margin-bottom: 20px;
  }

  .tabs {
    margin: 30px 0;
  }

  .title{
    font-size: 20px;
    color: #333333;
    line-height: 20px;
    margin-bottom: 10px;

    button {
      font-size: 14px;
      color: #999999;
    }
  }

  .pagination {
    margin: 20px 0;
    text-align: right;
  }

  .query {
    //display: flex;
    align-items: center;
    width: 100%;
    form{
      width:100%;
      display:flex;
      flex-wrap: wrap;
    }
    label {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #666666;
      line-height:33px;
      margin:0 .5em;
      min-width: 75px;
      display: inline-block;
    }

    .next-input {
      flex: 1;
    }

    .next-form-item {
      margin-top: 10px;
      margin-bottom: 0;
      display:flex;
      &:not(:last-child){
        margin-right:10px;
      }
      .CascaderSelect{
        .select-delete-filling{
          top:15px;
          right:25px;
        }
      }
    }
    .btns {
      //min-width:11em;
      //margin:10px 0 0 93px;
      > button {
        margin-right: .5em;
      }
    }
  }
}

.next-table.add-search-table {
  max-height: 300px;
  overflow: scroll;
}


.g-preview-dialog {
  .next-dialog-body {
    padding: 0;
  }
}
.g-imgpreview-content {
  width: 400px;
  max-height: 420px;
  padding: 0 20px 20px 20px;

  .title {
    margin: 10px 0;
    color: #333333;
    font-size: 16px;
  }

  .img-wrapper {
    width: 360px; max-height: 360px;
    img {
      object-fit: cover;
      width: 100%;
      max-height: 100%;
    }
  }
}

.add-dialog{
  width: 370px;
  .batchIds-textArea{
    width: 100% !important;
    color: $font_main_height;

    padding: 11px 12px;
  }

  .next-input.next-input-textarea.batchIds-textArea textarea {
    line-height: 22px;
  }

  .next-dialog-header {
    padding: 0px 24px;
    .add-dialog-title {
      .first {
        font-size: 17px;
      }

      .second {
        font-size: 11px;
        font-weight: 100;
        color: $font_tip;
        line-height: 13px;
      }
      margin-bottom: -10px;
    }
  }
}
