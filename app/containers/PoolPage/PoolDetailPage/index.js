import React, { Fragment } from 'react';
import { Table, Input, Button, Breadcrumb, Pa<PERSON>ation, Tab, Dialog, Field, Form, Radio, Select } from '@alife/next';
import {
  StoreColumns,
  CommodityColumns,
  FailStoreColumns,
  FailCommodityColumns,
  CommodityTransformColumns,
  NewDetailCommodityColumns,
  NewDetailStoreColumns, NewStoreColumns
} from '@/containers/PoolPage/SelectQueryPoolPage';
import * as validators from '@/utils/validators';
import { PLACEHODLERS, PAGE_SIZE } from '@/constants';
import { PoolPageBase } from '../common';
import { doesFormHasErrors } from '@/utils/formTool';
import * as api from '@/utils/api';
import * as napi from '@/adator/api';
import { Link } from 'react-router-dom';
import { track } from '@/utils/aplus';
import PreviewImage from '@/components/PreviewImage';
import { CascaderSelectWithClear } from '@/components/CascaderSelect';
import { getValueByUrlKey,deepClone } from '@/utils/others';
import './style.scss';
import {actMap} from "../SelectQueryPoolPage/common";
import {ACLAccess} from '@/components/ACLAccess'

const FormItem = Form.Item
const DEFAULT_GOODS_IMG = require('../../../images/default-goods-pic.png');

export class PoolDetailPage extends PoolPageBase {
  batchInputField = new Field(this)
  queryInputField = new Field(this)

  constructor(props) {
    super(props)

    this.state = {
      resultItemIds:'', //商品id
      upcIds: '', // 商品条形码
      ids:'', //门店池ID
      goodsNameKeyWord: '', // 商品名称
      brandName: '', // 品牌名称
      itemStatus:"",
      goodsActivitiesType: [], // 营销活动
      shopNames: '', // 门店名称
      shopIds: '', //门店ID
      isLoading: false,
      // title: this.query.name,
      disableModify: this.query.disableModify,
      // poolState: this.query.poolState,
      // basePoolId: this.query.basePoolId,
      // editTitle: this.query.name,
      isDialogDeleteVisible: false,
      isDialogBatchAddInputVisible: false,
      isDialogPreviewVisible: false,
      selectedRows: [],
      rows: [],
      pagination: {
        page: 1,
        total: 0,
        size: PAGE_SIZE
      },
      tabidx: this.query.basePoolId == 40003 ? 1 : 0,
      isShow: localStorage.getItem(`publishBtnShow_${this.isStore ? 'store' : 'commodity'}_${this.id}`) || false,
      marketingTypeOptions: [],
      itemStatusOptions: [],
      createMode: this.query.createMode
    }
  }

  componentDidMount() {
    this.getQueryFromData();
  }

  initDetail = () =>{
    this.load({});

    // 打点
    if (this.isStore) {
      track('setSpm', ['12964434'])
    } else {
      track('setSpm', ['12964422'])
    }

    this.getPoolState();
    this.getMarketingTypes();
    this.getItemStatusOptions();
  }

  /**
   * 从接口里面读这些数据，取代url里面的query string
   * */
  getQueryFromData = () => {
    let self = this;
    try {
      napi.getPoolDetailByPoolId(this.id)
        .then((resp) => {
          if (resp.success) {
            let {sourcePoolId, createMode, poolName, state} = resp.data;
            self.setState({
              basePoolId: sourcePoolId,
              createMode,
              title: poolName,
              editTitle: poolName,
              poolState: state
            },()=>{
              console.log(this.state);
              this.initDetail();
            })
          }else{
            Message.error(resp.errMessage)
          }
        })
    } catch (error) {
      napi.onRequestError(error)
    }
  }

  getNoTranform = (basePoolId) => { //不是品转店 (30004，30006 淘内也支持店了)
    return basePoolId != 10013;
  }

  async getPoolState() {
    try {
      if (!this.state.poolState) {
        let request = (this.isAli) ? api.ali.getPoolDetail : api.getPoolDetail;
        let {poolState} =  await request({isStore: this.isStore && this.getNoTranform(this.state.basePoolId), id: this.id});
        this.updateState(() => ({poolState}));
      }
    } catch (error) {
      api.onRequestError(error)
    }
  }

  get id() {
    return this.params.id
  }

  get isAli(){
    return (getValueByUrlKey('dataType') == 1);
  }

  get dataType() {
    return getValueByUrlKey('dataType');
  }

  onQuery = () => {
    this.queryInputField.validate((e) => {
      if(!doesFormHasErrors(e)){
        track('clickEvent', ['/selection_kunlun.MANAGE-POOL-view-pool.search-btn'])
        // track('clickEvent', ['/selection_kunlun.POOL-VIEW.pool-view-search'])
        this.load({
          page: 1
        })
      }
    });
  }

  //pagination change时间
  onPageChange = (page) => {
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-view-pool.page-change', `poolId=${this.id}&poolType=${this.isStore ? 2 : 1}`])
    this.load({page})
  }

  onPageSizeChange = (size) => {
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-view-pool.page-size-change', `poolId=${this.id}&poolType=${this.isStore ? 2 : 1}`])
    this.load({ page: 1, size })
  }

  onReset = () => {
    this.updateState(s => {
      s.upcIds = '';
      s.ids = '';
      s.goodsNameKeyWord = '';
      s.goodsActivitiesType = [];
      s.brandName = '';
      s.shopNames = '';
      s.shopIds = '';
      s.resultItemIds = '';
      s.itemStatus = '';
    })
    this.load({
      page: 1
    })
  }

  onBatchDelete = () => {
    const self = this;
    let {tabidx} = this.state;
    this.judgeAccess(this.id,function () {
      // track('clickEvent', ['/selection_kunlun.POOL-VIEW.pool-view-bundleDelete'])
      track('clickEvent', ['/selection_kunlun.MANAGE-POOL-view-pool.batch-delete', `poolId=${self.id}&poolType=${self.isStore ? 2 : 1}`])

      Dialog.confirm({
        title: '提示',
        content: `确定批量${tabidx==2?'取消':''}删除这些数据么?`,
        onOk: async () => {
          const {tabidx, selectedRows, basePoolId} = self.state;
          try {
            let request = (self.isAli) ? api.ali.batchDeleteItemsInPool : api.batchDeleteItemsInPool;
            await request({
              isStore: self.isStore && self.getNoTranform(basePoolId),
              id: self.id,
              itemIds: selectedRows,
              operateTab: +tabidx,
              isInvite:self.isInvite
            });

            self.updateState(state => state.selectedRows = [])
            self.toast({content: '操作成功'});
            self.load({});
            if (self.state.tabidx != 0) {
              self.onQuery({
                page: self.state.pagination.page
              })
            }
          } catch (error) {
            api.onRequestError(error)
          }
        }
      });
    });
  }

  onTabChange = (tabidx) => {
    const tabGoldLinks = [
      '/selection_kunlun.MANAGE-POOL-view-pool.inital-select-tab',
      '/selection_kunlun.MANAGE-POOL-view-pool.add-tab',
      '/selection_kunlun.MANAGE-POOL-view-pool.delete-tab',
    ]
    track('clickEvent', [tabGoldLinks[tabidx], `poolId=${this.id}&poolType=${this.isStore ? 2 : 1}`])

    this.updateState(s => {
      s.tabidx = tabidx
      this.selectedRows = []
    })
    this.load({page: 1})
  }

  async load({page, size, query}) {
    const { pagination, tabidx, basePoolId, brandName, shopNames, shopIds, resultItemIds, itemStatusOptions, itemStatus } = this.state;
    page = page || pagination.page
    size = size || pagination.size
    const {upcIds, ids, goodsNameKeyWord, goodsActivitiesType} = this.state;
    let useCommaSeperated = validators.upcIdCommaSeperated;
    let validatorField = upcIds;
    if (this.isStore && this.getNoTranform(basePoolId)) {
      useCommaSeperated = validators.idCommaSeperated;
      validatorField = ids;
    }
    useCommaSeperated(undefined, validatorField, async (errMsg) => {
      if (errMsg) return this.toast({content: this.labelText('{}池格式错误'), type: 'error'})
      if(this.isAli){
        query = query || {
          resultUpcIds: upcIds.split(',').map(e => e.trim()).filter(e => !!e),
          resultItemNames: goodsNameKeyWord.split(',').map(e => e.trim()).filter(e => !!e),
          resultItemIds: resultItemIds.split(',').map(e => e.trim()).filter(e => !!e),
          resultStoreNames: shopNames.split(',').map(e => e.trim()).filter(e => !!e),
          resultStoreIds: shopIds.split(',').map(e => e.trim()).filter(e => !!e),
          operateType: +tabidx,
          pagination: {
            page, size
          }
        }
      }else{
        query = query || {
          upcIds: upcIds.split(',').map(e => e.trim()).filter(e => !!e),
          goodsNameKeyWord: goodsNameKeyWord.split(',').map(e => e.trim()).filter(e => !!e),
          goodsActivitiesType,
          shopNames: shopNames.split(',').map(e => e.trim()).filter(e => !!e),
          shopIds: shopIds.split(',').map(e => e.trim()).filter(e => !!e),
          operateType: +tabidx,
          pagination: {
            page, size
          }
        }
      }

      if (itemStatus != "") {
        query.itemStatus = itemStatus
      }

      if(this.isStore && this.getNoTranform(basePoolId)){
        query.ids = ids.split(',').map(e => e.trim()).filter(e => !!e);
        query.brandName = brandName.split(',').map(e => e.trim()).filter(e => !!e);
      }
      try {
        this.setState({isLoading: true})
        let request = (this.isAli) ? api.ali.getDetail : api.getDetail;
        let {rows, total} = await request({
          id: this.id,
          isStore: this.isStore && this.getNoTranform(basePoolId),
          query,
          basePoolId: this.state.basePoolId,
          isInvite:this.isInvite
        }).then(api.onRequestSuccess)

        const updateDate = {
          rows,
          pagination: {total, size, page}
        }
        this.updateState(() => updateDate)
      } catch (error) {
        api.onRequestError(error)
      } finally {
        this.setState({isLoading: false})
      }
    })
  }

  onRowSelectionChange = (selectedRows) => {
    this.updateState(() => ({selectedRows}))
  }

  onModifyTitle = async (action) => {
    switch (action) {
      case 'toModify':
        return this.updateState(() => ({isEdit: true}))
      case 'cancel':
        return this.updateState(() => ({isEdit: false}))
      case 'save':
        try {
          const {editTitle} = this.state;
          await api.modifyPoolTitle(this.id, {poolName: editTitle})
          this.toastSuccess()
          this.setState({
            isEdit: false,
            title: editTitle
          })
          let query = {
            ...this.query,
            name: editTitle
          }
          this.history.replace(`${this.location.pathname}?${this.encodeQuery(query)}`)
        } catch (error) {
          api.onRequestError(error)
        }
        break;
      default:
        throw new Error('invalid action')
    }
  }

  onDialogBatchInputCancel = () => {
    this.updateState(() => ({isDialogBatchAddInputVisible: false}))
  }

  onBatchAddInputCancel = () => {
    this.updateState(() => ({isDialogBatchAddInputVisible: false}))
  }

  onBachAdd = () => {
    const self = this;
    this.judgeAccess(this.id,function () {
      track('clickEvent', ['/selection_kunlun.MANAGE-POOL-view-pool.add-btn', `poolId=${self.id}&poolType=${self.isStore ? 2 : 1}`])
      // track('clickEvent', ['/selection_kunlun.POOL-VIEW.pool-view-bundleAdd'])
      self.updateState(() => ({isDialogBatchAddInputVisible: true}))
      self.onTabChange(1);
    });
  }

  judgeAccess = async(poolId,callback) =>{
    let resp = await api.permission.checkPool(poolId);
    let {rediectUrl} = resp.data.data;
    this.setState({
      rediectUrl
    },()=>{
      if(rediectUrl){
        Dialog.confirm({
          title: '申请权限',
          footer: false,
          content: [<ACLAccess rediectUrl={rediectUrl}/>],
        })
      }else{
        callback();
      }
    });
  }

  // onBatchAddInputConfirm = () => {
  //   this.batchInputField.validate(async (errors, values) => {
  //     if (errors) return
  //     else {
  //       let itemIds = values.batchIds.split(/[,\s]+/gm).map(e => e.trim())
  //       let request = this.isStore && this.state.basePoolId != 10013 ? api.getTargetStoreByIds : api.getTargetSkuByIds
  //       try {
  //         let resp = await request(itemIds).then(api.onRequestSuccess)
  //         this.onBatchAddInputCancel()
  //         this.openBatchAddTableConfirm(resp.rows, itemIds)
  //       } catch (error) {
  //         api.onRequestError(error)
  //       }
  //     }
  //   })
  // }

  onBatchAddInputConfirm = () => {
    this.batchInputField.validate(async (errors, values) => {
      if (errors) return
      else {
        let itemIds = values.batchIds.split(/[,\s]+/gm).map(e => e.trim());
        try {
          this.onBatchAddInputCancel()
          this.openBatchAddTableConfirm(itemIds, values.dataFlag)
        } catch (error) {
          api.onRequestError(error)
        }
      }
    })
  }

  labelText(str,text) {
    return str.replace(`{}`, text || (this.isStore && this.getNoTranform(this.state.basePoolId) ? '门店' : '商品') )
  }

  toastSuccess() {
    this.toast('操作成功')
  }

  // openBatchAddTableConfirm(rows, itemIds) {
  //   return async () => {
  //     try {
  //       await api.addItemsToPool({
  //         id: this.id,
  //         isStore: this.isStore && this.state.basePoolId != 10013,
  //         itemIds,
  //         operateTab: this.tabidx
  //       }).then(api.onRequestSuccess);
  //       this.toastSuccess();
  //       localStorage.setItem(`publishBtnShow_${this.isStore ? 'store' : 'commodity'}_${this.id}`, 1);
  //       this.updateState(() => ({isShow: true}));
  //     } catch (error) {
  //       api.onRequestError(error)
  //     }
  //     if (this.state.tabidx == 1) {
  //       this.reload()
  //     }
  //   },
  //   Dialog.show({
  //     title: this.labelText('添加{}列表'),
  //     content: createTable({rows, isStore: this.isStore, basePoolId:this.state.basePoolId}),
  //     onOk: async () => {
  //       try {
  //         await api.addItemsToPool({
  //           id: this.id,
  //           isStore: this.isStore && this.state.basePoolId != 10013,
  //           itemIds,
  //           operateTab: this.tabidx
  //         }).then(api.onRequestSuccess);
  //         this.toastSuccess();
  //         localStorage.setItem(`publishBtnShow_${this.isStore ? 'store' : 'commodity'}_${this.id}`, 1);
  //         this.updateState(() => ({isShow: true}));
  //       } catch (error) {
  //         api.onRequestError(error)
  //       }
  //       if (this.state.tabidx == 1) {
  //         this.reload()
  //       }
  //     },
  //     // okProps={children: '完成'}
  //   })
  //
  //   function createTable({rows, isStore, basePoolId}) {
  //     let tableColumns = [];
  //     if (basePoolId==10013) {
  //       tableColumns = CommodityTransformColumns;
  //     } else if (isStore) {
  //       tableColumns = StoreColumns;
  //     } else {
  //       tableColumns = CommodityColumns;
  //     }
  //
  //     return (
  //       <div className="g-add-detail-table-content">
  //         <Table dataSource={rows} className='add-search-table' hasBorder={false}>
  //           {
  //             tableColumns.map((e, idx) => <Table.Column {...e} key={idx}/>)
  //           }
  //         </Table>
  //       </div>
  //     )
  //   }
  // }
  async openBatchAddTableConfirm(itemIds, dataFlag) {
    try {
      let request = (this.isAli)?(api.ali.addItemsToPool):(api.addItemsToPool);
      await request({
        id: this.id,
        isStore: this.isStore && this.getNoTranform(this.state.basePoolId),
        itemIds,
        dataFlag,
        operateTab: this.state.tabidx,
        isInvite: this.isInvite
      }).then(api.onRequestSuccess);
      this.toastSuccess();
      if (!this.isAli) { //淘内的池子添加新门店或者商品以后不显示发布按钮
        localStorage.setItem(`publishBtnShow_${this.isStore ? 'store' : 'commodity'}_${this.id}`, 1);
        this.updateState(() => ({isShow: true}));
      }
    } catch (error) {
      api.onRequestError(error)
    }
    if (this.state.tabidx == 1) {
      this.reload()
    }
  }

  reload(page = this.state.pagination.page) {
    this.load({page})
  }

  onPublish = async () => {
    const self = this;
    this.judgeAccess(this.id, async function () {
      track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-publish'])
      try {
        let request = (self.isAli) ? (api.ali.publishPool) : (api.publishPool);
        await request({isStore: self.isStore, id: self.id});
        let query = {
          name: self.query.name,
          basePoolId: self.state.basePoolId,
          dataType: self.dataType
        }
        localStorage.removeItem(`publishBtnShow_${self.isStore ? 'store' : 'commodity'}_${self.id}`);
        self.updateState(() => ({isShow: false}));
        self.history.push(`/${self.isStore ? 'store' : 'commodity'}pool${self.isInvite?"Invite":""}/list/progress/${self.id}?${self.encodeQuery(query)}`)
      } catch (error) {
        api.onRequestError(error)
      }
    });
  }

  async getMarketingTypes() {
    try {
      let resp = await api.queryMarketingType().then(api.onRequestSuccess)
      this.updateState(() => ({marketingTypeOptions: resp}))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getItemStatusOptions() {
    try {
      let resp = await api.ali.getItemStatusOptions().then(api.onRequestSuccess)
      this.updateState(() => ({itemStatusOptions: resp}))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  createColumn = (tabidx) =>{
    let result;
    if (tabidx==3) {
      result = this.isStore ? FailStoreColumns : FailCommodityColumns;
    } else if (!this.getNoTranform(this.basePoolId)) {
      result = CommodityTransformColumns;
    }else if([30004,30006,31004].includes(+this.basePoolId)){ //原本是用这个!this.getNoTranform(this.basePoolId)，但30004,30006输入的跟品保持，输出跟店保持一致
      result = NewDetailStoreColumns;
    }else if(this.isStore) {
      result = deepClone(this.isAli ? NewDetailStoreColumns : StoreColumns);
    }else {
      result = deepClone(this.isAli ? NewDetailCommodityColumns : CommodityColumns);
    }
    return result;
  }

  render() {
    const { pagination, rows, title, editTitle, upcIds, ids, goodsNameKeyWord, goodsActivitiesType, brandName, shopNames, shopIds, isLoading, isEdit, selectedRows, isDialogBatchAddInputVisible, tabidx, disableModify, poolState, basePoolId, isShow, resultItemIds, itemStatus, itemStatusOptions, createMode } = this.state;
    let tableProps = {
      rowSelection: {
        onChange: this.onRowSelectionChange,
        selectedRowKeys: selectedRows
      }
    }
    const t = {
      showSearch: true,
      multiple: true,
      expandTriggerType: 'hover',
      hasClear: true,
      useVirtual: true,
    }
    let navText = "";
    if (!this.getNoTranform(basePoolId)) {
      navText = "门店"
    }
    const isExcelType = (createMode == 'CREATE_EXCEL' || createMode == '文件上传' || createMode=='CREATE_RULE');
    // const isExcelType = (createMode == 'CREATE_EXCEL');
    return (
      <div className="page CommodityPoolDetailPage">
        <div className="nav-wrapper">
          <Breadcrumb>
            {this.isInvite?
              <Breadcrumb.Item><Link to={`/${this.isStore ? 'store' : 'commodity'}poolInvite/list`}>{this.labelText('管理{}池',navText)}</Link></Breadcrumb.Item>:
              [
                <Breadcrumb.Item>{ this.isStore ? '选店' : '选品' }</Breadcrumb.Item>,
                <Breadcrumb.Item><Link to={`/${this.isStore ? 'store' : 'commodity'}pool/list`}>{this.labelText('管理{}池',navText)}</Link></Breadcrumb.Item>
              ]
            }
            <Breadcrumb.Item> {this.labelText('查看{}池',navText)} </Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <Panel>
          <div className="title">
            {
              !isEdit ? (
                <Fragment>
                  <span>{title}</span> <Button size="small" text={true} onClick={() => this.onModifyTitle('toModify')}>修改</Button>
                </Fragment>
              ) : (
                <Fragment>
                  <Input onChange={(v) => this.updateState(() => ({ editTitle: v }))} value={editTitle} />
                  <div className="btns">
                    <Button size="small" text={true} onClick={() => this.onModifyTitle('cancel')}>取消</Button>
                    <Button size="small" type="primary" text={true} onClick={() => this.onModifyTitle('save')}>保存</Button>
                  </div>
                </Fragment>)
            }
          </div>
          <div className="query">
            <Form inline field={this.queryInputField}>
              {!this.isStore || (!this.getNoTranform(basePoolId)) ?
                [<FormItem label={this.labelText('{}条形码')} validator={validators.createMaxCommaItem(150)} validatorTrigger={['onChange']} >
                  <Input placeholder={PLACEHODLERS.commaSeperated} name='upcIds'  onChange={(v) => this.updateState(() => ({ upcIds: v }))} value={upcIds} />
                </FormItem>,
                <FormItem label={this.labelText('{}名称')} validator={validators.createMaxCommaItem(30)} validatorTrigger={['onChange']}>
                  <Input placeholder='请输入，多个英文逗号隔开' name='goodsNameKeyWord' onChange={(v) => this.updateState(() => ({ goodsNameKeyWord: v }))} value={goodsNameKeyWord} />
                </FormItem>,
                <FormItem label='品牌名称' validator={validators.createMaxCommaItem(30)} validatorTrigger={['onChange']} style={{display:'none'}}>
                  <Input placeholder='请输入，多个英文逗号隔开' name='brandName' onChange={(v) => this.updateState(() => ({ brandName: v }))} value={brandName} />
                </FormItem>,
                <FormItem label="商品状态">
                  <Select placeholder="请选择商品状态" name='itemStatus' mode='single' dataSource={itemStatusOptions} hasClear={true} onChange={(v) => this.updateState(() => ({ itemStatus: v }))} value={itemStatus} />
                </FormItem>,
                <div>
                  {!this.isAli ? <FormItem label='营销活动'>
                    <CascaderSelectWithClear style={{width:'150px'}} dataSource={this.state.marketingTypeOptions} {...t} onChange={(v) => this.updateState(() => ({goodsActivitiesType: v}))} value={goodsActivitiesType}/>
                  </FormItem>:<FormItem label="商品ID">
                    <Input placeholder={PLACEHODLERS.commaSeperated} name='resultItemIds'  onChange={(v) => this.updateState(() => ({ resultItemIds: v }))} value={resultItemIds} />
                  </FormItem>}
                </div>,
                <br/>,
                <FormItem label="门店名称">
                  <Input placeholder={PLACEHODLERS.commaSeperated} name='shopNames'  onChange={(v) => this.updateState(() => ({ shopNames: v }))} value={shopNames} />
                </FormItem>,
                <FormItem label="门店ID">
                  <Input placeholder={PLACEHODLERS.commaSeperated} name='shopIds'  onChange={(v) => this.updateState(() => ({ shopIds: v }))} value={shopIds} />
                </FormItem>]:
                [<FormItem label={this.labelText('{}ID')}>
                  <Input placeholder={PLACEHODLERS.commaSeperated} name='ids'  onChange={(v) => this.updateState(() => ({ ids: v }))} value={ids} />
                </FormItem>,
                <FormItem label="品牌名称">
                  <Input placeholder={PLACEHODLERS.commaSeperated} name='brandName'  onChange={(v) => this.updateState(() => ({ brandName: v }))} value={brandName} />
                </FormItem>,
                <FormItem label="门店名称">
                  <Input placeholder={PLACEHODLERS.commaSeperated} name='shopNames'  onChange={(v) => this.updateState(() => ({ shopNames: v }))} value={shopNames} />
                </FormItem>]}
              <FormItem>
                <div className="btns">
                  <Button type="primary" onClick={this.onQuery} disabled={tabidx==3}>查询</Button>
                  <Button onClick={this.onReset} disabled={tabidx==3}>重置</Button>
                </div>
              </FormItem>
            </Form>
          </div>
        </Panel>
        <Panel className="table-panel">
          <div className="btns">
            {(poolState!='25') && ((this.isStore && basePoolId != 10010 && basePoolId != 30003 && this.getNoTranform(basePoolId)) || (!this.isStore && (basePoolId == 10001  || basePoolId == 20001) ) || isExcelType) && <Button onClick={this.onBachAdd} type="primary" disabled={disableModify}>{this.labelText('添加{}')}</Button>}
            {((poolState!='25' && basePoolId!='40001') || (isExcelType && tabidx==0)) && <Button onClick={this.onBatchDelete} disabled={!selectedRows.length}>批量删除</Button>}
            {/*{(createMode == 'CREATE_EXCEL' && tabidx==2) && <Button onClick={this.onBatchDelete} disabled={!selectedRows.length}>批量取消删除</Button>}*/}
            {((poolState == '0' || poolState == '1' || poolState == '15' || poolState == '10') && isShow) && <Button onClick={this.onPublish}  type="primary" >发布</Button>}
          </div>
          <Tab className="tabs" shape="wrapped" onClick={this.onTabChange} activeKey={tabidx}>
            {basePoolId!=40003&&<Tab.Item title={this.labelText('初选{}')} key={0}></Tab.Item>}
            { ((this.isStore && basePoolId != 10010 && basePoolId != 30003 && this.getNoTranform(basePoolId)) || (!this.isStore && (basePoolId == 10001 || basePoolId == 20001 || basePoolId == 40003)) || isExcelType) && <Tab.Item title={this.labelText('添加{}')} key={1}></Tab.Item>}
            {((basePoolId != '40001') || isExcelType) && <Tab.Item title={this.labelText('删除{}')} key={2}></Tab.Item>}
            {this.isAli && <Tab.Item title="失败记录" key={3}></Tab.Item>}
          </Tab>
          <div className="title-total">{this.labelText('查询{}结果')}(共{pagination.total}个)</div>
          <div className="table-wrapper">
            <Table dataSource={rows} loading={isLoading} {...tableProps} hasBorder={false} primaryKey={`${this.isStore && this.getNoTranform(basePoolId) ? 'id' : 'goodsId'}`}>
              {
                (this.createColumn(tabidx)).map((e, idx) => {
                  if (e.dataIndex == 'goodsPic'){
                    e = {
                      ...e,
                      cell: (_v, _idx, data)=> {
                        return <PreviewImage src={data.goodsPic || DEFAULT_GOODS_IMG} title={data.goodsName} />
                      },
                    }
                    delete e.dataIndex
                  }
                  return <Table.Column {...e} key={idx} />
                })
              }
            </Table>
          </div>
          <div className="pagination">
            <Pagination
              popupProps = {{align:'bl tl'}}
              pageSizeList={[20, 50, 100]}
              current={pagination.page}
              total={pagination.total}
              pageSize={pagination.size}
              onChange={this.onPageChange}
              pageSizeSelector="dropdown"
              pageSizePosition="end"
              onPageSizeChange={this.onPageSizeChange} />
          </div>
        </Panel>

        <Dialog
          className="add-dialog"
          height="400px"
          title={<div className="add-dialog-title"><p className="first">{this.labelText('添加{}')}</p></div>}
          visible={isDialogBatchAddInputVisible}
          onOk={this.onBatchAddInputConfirm}
          onCancel={this.onBatchAddInputCancel}
          onClose={this.onBatchAddInputCancel}
          okProps={{
            children: '继续'
          }}>
          {this.isAli && <Radio.Group {...this.batchInputField.init('dataFlag', {initValue: true})}>
            {this.isStore && <Radio value={false}>{this.labelText('按ele{}id')}</Radio>}
            <Radio value={true}>{this.labelText('按淘系{}id')}</Radio>
          </Radio.Group>}
          <p className="second">{this.labelText('请输入{}ID')}</p>
          <Input.TextArea
            className="batchIds-textArea"
            autoHeight={{minRows: 7}}
            placeholder={`输入多个可用英文逗号、空格、回车隔开${this.isStore ? ',一次性可添加2000家店铺，最多可添加50000家店铺' : ''}`}
            {...this.batchInputField.init('batchIds', {
              rules: [{
                required: true,
                message: '请输入ID信息'
              }, {
                validator: validators.map(validators.createRegexMatchMiddleware(/^(\d+)([,\s]+\d+)*$/gm, '输入格式不合法'), (_a, b)=> ({value: b.trim()})),
                trigger: ['onBlur']
              }]
            })} />
          {this.batchInputField.getError('batchIds') ?
            <div style={{ color: 'red' }}>{this.batchInputField.getError('batchIds').join(',')}</div> : ''}
        </Dialog>
      </div>
    )

  }
}

function Panel({ children, className }) {
  return (
    <div className={`panel ${className || ''}`}>
      {children}
    </div>
  )
}

export class PoolDetailPageInvite extends PoolDetailPage {
  get isInvite() {
    return true;
  }
}
