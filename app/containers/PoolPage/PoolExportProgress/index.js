import React from 'react';
import { Step, Icon, Button, Progress, Breadcrumb } from '@alife/next';
import { Link } from 'react-router-dom';
import { ProgressView } from '@/components/ProgressView';
import { PoolPageBase } from '../common';
import * as api from '@/utils/api';

import './style.scss';

const Header = function({ state }){
  let step = state === 'success' ? 2 : 1

  return (
    <div className="step-wraper">
      <Step current={step} shape="circle">
        {
          [
            {title:'选择', content:''},
            {title:'导出数据', content:''},
            {title:'完成', content:''}
          ].map((item, index) => <Step.Item key={index} title={item['title']} content={item['content']} />)
        }
      </Step>
    </div>
  )
}

const textRender = percent => {
  if (percent === 100) {
    return <Icon type="select" size="medium" />;
  }
  return `${percent}%`;
};

const ProgressBar = ({state, percent}) => {
  let t = null
  switch (state) {
    case 'error':
      t = <Icon size="large" type="delete-filling" />
      break;
    case 'success':
      t = <Icon size="large" type="success-filling" />
      break;

    default:
      t = <Progress percent={percent} shape="circle" size="small" state="normal" className="custom-progress" textRender={textRender} />
  }

  return <div className={`export-pg type-${state || ''}`}> {t} </div>
}

const ExportSuccess = ({onSuccess}) => {
  return <>
    <div className="title">导出成功</div>
    <Button type="primary" onClick={onSuccess}>查看导出数据</Button>
  </>
}

const ExportError = ({ msg = '导出失败，再重新导出' }) => {
  return <>
    <div className="title">导出失败</div>
    <p>{msg}</p>
  </>
}

const ExportProgressing = () => {
  return <>
    <div className="title">导出中，请等待……</div>
  </>
}

const InfoBar = ({state, data}) => {
  return (
    <div className="export-info">
      { state === 'success' &&  <ExportSuccess onSuccess={() => {
        window.open(data.filePath);
      }}/>}
      { state === 'error' &&  <ExportError/>}
      { state === 'progressing' &&  <ExportProgressing /> }
    </div>
  )
}

export class PoolExportProgress extends PoolPageBase {
  render() {
    let PoolExportProgress = ProgressView({
      title: '导出进度查看',
      Header,
      ProgressBar,
      InfoBar,
      Breadcrumb,
      Link,
      from: this.isInvite ? "invite" : "",
      getProgress: () => {
        const { data } = this.query;
        return api.getExportProgress(data).then((data) => {
          const { progress, ...other } = data;
          return { percent: progress, ...other }
        })
      }
    })
    return <PoolExportProgress />
  }
}

export class PoolExportProgressInvite extends PoolExportProgress {
  get isInvite() {
    return true;
  }
}
