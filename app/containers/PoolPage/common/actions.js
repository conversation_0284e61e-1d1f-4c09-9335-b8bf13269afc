
import { UPDATE_QUERY_FIELD, INIT_QUERY_FIELD, UPDATE_OUTER_FIELD, RESET_STEP2_FORM, UPDATE_BASE_POOL_ID, RESET_QUERY_FIELD, SET_EDIT_DETAIL, RESET_STEP1_FORM, FETCH_EDIT_DETAIL, GO_STEP_2, INIT_QUERY_META } from './constants';

export function resetQueryField({key}) {
  return {
    type: RESET_QUERY_FIELD,
    payload: {
      key
    }
  };
}

export function updateQueryField(payload) {
  return {
    type: UPDATE_QUERY_FIELD,
    payload
  };
}

export function initQueryField(payload) {
  return {
    type: INIT_QUERY_FIELD,
    payload
  }
}



export function resetStep1Form() {
  return {
    type: RESET_STEP1_FORM,
  };
}

export function resetStep2Form() {
  return {
    type: RESET_STEP2_FORM,
  };
}

export function updateOuterField(payload) {
  return {
    type: UPDATE_OUTER_FIELD,
    payload
  };
}

export function updateBasePoolId(payload) {
  return {
    type: UPDATE_BASE_POOL_ID,
    payload
  };
}


export function setRuleDetail(payload){
  return {
    type: SET_EDIT_DETAIL,
    payload
  };
}


export function fetchPoolDetail(payload){
  return {
    type: FETCH_EDIT_DETAIL,
    payload
  };
}

export function goStep2(payload){
  return {
    type: GO_STEP_2,
    payload
  };
}

export function initQueryMeta(payload) {
  return {
    type: INIT_QUERY_META,
    payload
  }
}