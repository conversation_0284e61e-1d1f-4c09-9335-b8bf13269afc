
import React from 'react';
import { Step } from '@alife/next';
import { PageBase } from '@/containers/base';

import * as actions from './actions';
import * as constants from './constants';
import * as reducer from './reducer';
import * as selectors from './selectors';

export function Steps({ current, middleText }) {

  return (
    <div className="step-wraper">
      <Step current={current} shape="circle">
        {[['选择基础池', ''], [middleText, ''], ['完成', '']].map((item, index) => <Step.Item key={index} title={item[0]} content={item[1]} />)}
      </Step>
    </div>
  )
}


export class PoolPageBase extends PageBase {
  static Container = PageBase.Container

  get isStore() {

    return this.location.pathname.startsWith('/storepool')
  }
}

export {
  actions, constants, reducer, selectors
}
