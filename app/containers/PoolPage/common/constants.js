
export const REDUCER_KEY = 'commodity_creation'
export const UPDATE_QUERY_FIELD = 'UPDATE_QUERY_FIELD'
export const INIT_QUERY_FIELD = 'INIT_QUERY_FIELD'
export const RESET_STEP1_FORM = 'RESET_STEP1_FORM'
export const RESET_STEP2_FORM = 'RESET_STEP2_FORM'
export const RESET_QUERY_FIELD = 'RESET_QUERY_FIELD'
export const UPDATE_OUTER_FIELD = 'UPDATE_OUTER_FIELD'
export const UPDATE_BASE_POOL_ID = 'UPDATE_BASE_POOL_ID'
export const SET_EDIT_DETAIL = 'SET_EDIT_DETAIL'
export const FETCH_EDIT_DETAIL = 'FETCH_EDIT_DETAIL'
export const UPDATE_QUERY_FORM = 'UPDATE_QUERY_FORM'
export const GO_STEP_2 = 'GO_STEP_2'
export const INIT_QUERY_META = 'INIT_QUERY_META'

export const pagetype = {
  COMMODITY: 'commodity',
  STORE: 'store'
}
