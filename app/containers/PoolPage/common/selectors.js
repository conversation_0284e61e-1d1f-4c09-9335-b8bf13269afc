import { createSelector } from 'reselect';
import { REDUCER_KEY } from './constants';

const pageRootSelector = (state) => state.get(REDUCER_KEY);

const makeQueryFieldSelector = () => createSelector(
  pageRootSelector,
  (pageRootState) => pageRootState.get('createQueryForm')
);

const makeInitQueryField = () => createSelector(
  pageRootSelector,
  (pageRootState) => pageRootState.get('initQueryForm')
)

const makeOuterQueryForm = ()=>createSelector(
  pageRootSelector, (pageRootState)=> pageRootState.get('outerForm')
)

const makeBasePoolIdSelector = () => createSelector(
  pageRootSelector,
  (pageRootState) => pageRootState.get('basePoolId')
);

const makeStepSelector = () => createSelector(
  pageRootSelector,
  (pageRootState) => pageRootState.get('step')
)

const makePoolId = () => createSelector(
  pageRootSelector,
  (pageRootState) => pageRootState.get('poolId')
)

export {
  pageRootSelector,
  makeQueryFieldSelector,
  makeInitQueryField,
  makeOuterQueryForm,
  makeBasePoolIdSelector,
  makeStepSelector,
  makePoolId
};
