
import { fromJS } from 'immutable';
import { deepClone } from '@/utils/others';
import * as constants from './constants';

const initialState = fromJS({
  poolId: '',
  basePoolId: '',
  step: 0,
  createQueryForm: {},
  initQueryForm:{},
  outerForm: {},
});


export function rootReducer(state = initialState, action) {
  const { type, payload } = action;
  switch (type) {

    case constants.RESET_STEP1_FORM:
      return state.set('basePoolId', '')
    case constants.RESET_STEP2_FORM:
      return state.set('createQueryForm', {})
        .set('outerForm', {})
        .set('poolId', '')
    case constants.RESET_QUERY_FIELD:
      if (!payload || !payload.key) {
        return state.set('createQueryForm', {})
      } else {
        let [tabkey, formkey] = payload.key.split('.')
        return state.update('createQueryForm', qs => {
          return fromJS(qs).deleteIn([tabkey, formkey]).toJS()
        })
      }
    case constants.UPDATE_BASE_POOL_ID:
      return state.set('basePoolId', payload);

    case constants.UPDATE_QUERY_FIELD:
      return state.set('createQueryForm', deepClone(payload));

    case constants.UPDATE_OUTER_FIELD:
      return state.set('outerForm', deepClone(payload));

    case constants.INIT_QUERY_FIELD:
      return state.set('initQueryForm', deepClone(payload));

    default:
      return state;
  }
}
