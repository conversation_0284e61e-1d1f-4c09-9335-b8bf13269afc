import moment from 'moment';
import { omit } from 'lodash';
import { isString } from '@/utils/validators';

export function humanizeQueryForm({queryForm, labelMap, isStore}) {
  let results = []
  const {store, sku, others} = labelMap;
  queryForm = omit(queryForm, ['sort', 'basePoolId', 'basePoolID'])
  let formItemLabelMap = isStore ? store : sku

  for (let tabkey of Object.keys(queryForm)) {
    let tabvalue = queryForm[tabkey]
    if (!tabvalue) continue
    for (let formkey of Object.keys(tabvalue)) {
      let value = tabvalue[formkey]
      let formItemLabel = formItemLabelMap[formkey];
      if (value != null && formItemLabel) {
        let labelValue = '';
        if (isString(formItemLabel)) {
          labelValue = formItemLabel;
        } else if (formItemLabel.props) {
          labelValue = formItemLabel.props.children[0];
        } else if (formItemLabel.length > 0) {
          labelValue = formItemLabel[0]
        }
        let item = {
          formkey,
          key: `${tabkey}.${formkey}`,
          label: labelValue,
          value: format(value, formkey, others)
        }
        item.value && results.push(item)
      }
    }
  }
  return results

  function format(value, formkey, others) {
    return doFormat(value)

    function doFormat(value){
      switch (true) {
        case value == null:
          return ''
        case Array.isArray(value):
          if (!value.length) return ''
          return value.map(e => doFormat(e,)).join(', ')
        case typeof value === 'object':
          if (value instanceof moment || value.constructor === Date) {
            return value.toLocaleString()
          } else if (value.start !== undefined || value.end !== undefined) {
            return `${doFormat(value.start)}-${doFormat(value.end)}`
          } else if (value.value != null || value.level == null){
            return doFormat(value.value)
          } else {
            throw new Error('not supported')
          }
        case typeof value === 'string' || typeof value === 'number': {
          let formatter = others[formkey]
          if (!formatter) return value + ''
          if (formatter.valueMap) return formatter.valueMap[value+'']
          if (formatter.format) return formatter.format(value)
          break;
        }
        default:
          throw new Error('not supported')
      }
    }
  }
}

export const itemPicQualityScoreSet = [
  {key: '1', field: 'psoriasisLabel', group: ["0", "1", "2"], value: []},
  {key: '2', field: 'transparentLabel', group: ["1"], value: []},
  {key: '3', field: 'wbLabel', group: ["1"], value: []},
  {key: '4', field: 'mainRatioLabel', group: ["0", "1"], value: []},
]

export function dealQualityScoreToArray(data) {
  let result = [];
  for (const o in data) {
    const fieldMap = itemPicQualityScoreSet.map(v => v.field);
    const curObj = itemPicQualityScoreSet.filter((v) => v.field == o);
    if (fieldMap.includes(o) && data[o].length == curObj[0].group.length) result.push({value: curObj[0].key, level: 1});
    if (data[o].length > 0 && data[o].length != curObj[0].group.length) {
      data[o].map((v) => {
        result.push({
          value: `${curObj[0].key}0${v}`,
          level: 2
        })
      })
    }
  }
  return result;
}

export function dealQualityScoreToObject(data) {
  // console.log(data);
  let qualityScoreData = data;
  let result = {};
  qualityScoreData.map((v) => {
    if (v.value < 10) {
      let obj = itemPicQualityScoreSet.filter((o) => o.key == v.value)[0];
      result[obj.field] = obj.group;
    } else {
      let p = parseInt(v.value / 100).toString();
      let t = parseInt(v.value % 10).toString();
      let obj = itemPicQualityScoreSet.filter((o) => o.key == p)[0];
      if(typeof result[obj.field] == 'undefined'){
        result[obj.field] = [];
      }
      result[obj.field].push(t);
    }
  })
  // console.log(result);
  return result;
}

export const actMap = {
  2: '商品直降',
  8: '品类满减',
  16: '品类满折',
  32: '商品特价',
  64: '商品折扣',
  128: '商品买赠',
}

export const activitiesSource = [
  {
    label: '买一赠一',
    value: '{"activityType":7,"childType":1,"conditionType":1,"conditions":1,"discountType":4,"discount":1}'
  },
  {
    label: '第二件特价',
    value: '{"activityType":8,"childType":1,"conditionType":3,"conditions":2}'
  }
]

export const shopPZLMStringSourceStore = [
  {
    label: '吃货联盟',
    value: 'PDCHLM'
  },
  {
    label: '果蔬商超吃货联盟',
    value: 'PDGSCHLM'
  },
  {
    label: '业态红包-超市',
    value: 'YTHBCS'
  },
  {
    label: '业态红包-便利店',
    value: 'YTHBBLD'
  },
  {
    label: '业态红包-水果',
    value: 'YTHBSG'
  },
  {
    label: '业态红包-生鲜',
    value: 'YTHBSX'
  },
  {
    label: '业态红包-零食',
    value: 'YTHBLS'
  },
  {
    label: '业态红包-酒水',
    value: 'YTHBJS'
  },
  {
    label: '业态红包-美妆个护',
    value: 'YTHBMZGH'
  },
  {
    label: '业态红包-日用百货',
    value: 'YTHBRYBH'
  },
  {
    label: '业态红包-母婴',
    value: 'YTHBMY'
  },
  {
    label: '业态红包-宠物',
    value: 'YTHBCW'
  },
  {
    label: '业态红包-鲜花绿植',
    value: 'YTHBXHLZ'
  },
  {
    label: '业态红包-超市便利',
    value: 'YTHBCHBL'
  },
  {
    label: '业态红包-水果生鲜',
    value: 'YTHBSGSX'
  },
  {
    label: '业态红包-零食酒水',
    value: 'YTHBLSJS'
  },
  {
    label: '业态红包-美妆百货',
    value: 'YTHBMZBH'
  }
]

export const alipayRelIdSourceCommodity = [
  {
    label: '支付宝ssu',
    value: 'ALIPAY_MIDDLE_PAGE'
  }
]

export const alipayRelIdSourceStore = [
  {
    label: '支付宝ssu',
    value: 'ALIPAY_MIDDLE_PAGE'
  }
]

export const storeAttributesSourceStore = [
  {
    label: '罗盘附店',
    value: 'LP_ATTACH'
  },
  {
    label: '散店',
    value: 'INDIVIDUAL'
  }
]

export const shopComprehensiveScoreDs = [
  {
    label: 'L1',
    value: 'L1'
  },
  {
    label: 'L2',
    value: 'L2'
  },
  {
    label: 'L3',
    value: 'L3'
  },
  {
    label: 'L4',
    value: 'L4'
  },
  {
    label: 'L5',
    value: 'L5'
  },
]
