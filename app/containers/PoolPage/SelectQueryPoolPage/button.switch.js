import React from 'react';
import { Button, Icon } from '@alife/next';

export class ButtonSwitch extends React.PureComponent {
  render() {
    const { options, value, onChange } = this.props
    return (
      <Button.Group>
        {
          options.map((item, idx) => {
            const { label, value: optionValue } = item;
            const isChecked = value && value.field === optionValue.field
            const withContext = ((optionValue, isChecked) => {
              return () => {
                onChange({
                  field: optionValue.field,
                  order: isChecked ? (value.order === 'asc' ? 'des' : 'asc') : 'des'
                })
              }
            })(optionValue, isChecked)

            let icon
            if (isChecked) {
              let type = value.order === 'asc' ? 'ascending' : 'descending'
              icon = <Icon type={type} size="x" style={{ color: '#fe7b55' }} />
            }
            return (
              <Button
                key={idx}
                type={isChecked ? 'secondary' : 'normal'}
                onClick={withContext}>
                {label}{icon}
              </Button>
            )
          })
        }
      </Button.Group>
    )
  }
}
