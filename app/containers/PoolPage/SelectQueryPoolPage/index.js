import './style.scss'

import React from 'react';
import { PageWrapper, PageSection } from '@/components/PageWrapper';
import { Link } from 'react-router-dom';
import {
  Form,
  Input,
  Tab,
  Table,
  Pagination,
  Button,
  Field,
  Breadcrumb,
  Icon,
  Checkbox,
  Radio,
  Dialog,
  Balloon
} from '@alife/next';
import moment from 'moment';
import {ACLAccess} from '@/components/ACLAccess'
import { connectRedux } from './decorator';
import { generate, createStoreMeta, createCommodityMeta, createAliCommodityMeta, collectLabelMap } from './queryfield';
import * as api from '@/utils/api';
import { promisify, deepClone, flatten } from '@/utils/others';
import { Steps, PoolPageBase } from '../common';
import { ButtonSwitch } from './button.switch';
import {  humanizeQueryForm, actMap, dealQualityScoreToObject, dealQualityScoreToArray } from './common';
import { StatusLabel } from '@/components/Label'
import { configTagGoldLog, track, goldLog } from '@/utils/aplus';
import { filterData } from '@/utils/filterDataByKey'
import { basepoolIds, PAGE_SIZE } from '@/constants';
import PreviewImage from '@/components/PreviewImage';
import { TimeRangePicker } from '@/components/TimeRangePicker';
import { permissionAccess }  from '@/components/PermissionAccess';
import { alipayRelIdSourceCommodity } from './common';


const FormItem = Form.Item;
const DEFAULT_GOODS_IMG = require('../../../images/default-goods-pic.png');
const CheckGroup = Checkbox.Group;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    fixedSpan: 5
  },
  wrapperCol: {
    span: 19
  }
};

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    if(start.isBefore(moment().startOf('day'))) return cb('开始时间不能小于当前时间')
    if (end.clone().subtract(4, 'month').isAfter(start.clone())) return cb('时间段不能超过4个月')
  }
  cb()
}


function goldLogTimeConsume(isStore){
  let start = localStorage.getItem('config_start')
  if (!start || Number.isNaN(+start)) return

  const time = (Date.now() - (+start)) / 1000;
  if(isStore){
    goldLog(['/selection_kunlun.CONFIG-TIME.config-time-store', `time=${time}`])
  } else {
    goldLog(['/selection_kunlun.CONFIG-TIME.config-time-commodity', `time=${time}`])
  }
}
/**
 * 统计配置指标: CONFIG-INDICES
 */
function goldLogTag(data, isStore){
  const getByKey = filterData(data['poolRule']);

  getByKey(() => Object.keys(data['poolRule']).filter(key => key !== 'basePoolId'), (pickedData) => {
    let log = '/selection_kunlun.CONFIG-INDICES.config-indices-commodity'
    if (isStore) {
      pickedData = { ...pickedData, sort: null }
      log = '/selection_kunlun.CONFIG-INDICES.config-indices-store'
    }
    configTagGoldLog(pickedData, log);
  })
}

const groupActivityIdsTypesOptions = [ //标准
  { value: 'marketing', label: '营销活动ID', mode: 'input'},
  { value: 'brand', label: '品牌活动ID', mode: 'input'},
  { value: 'invite', label: '招商活动ID', mode: 'input'},
  { value: 'mktType', label: '营销活动类型', mode: 'select'}
]

@connectRedux
export class BaseSelectQueryPoolPage extends PoolPageBase {
  constructor(props) {
    super(props)
    this.state = {
      pagination: {
        page: 1,
        size: PAGE_SIZE,
        total: 0
      },
      isLoading: false,
      rows: [],
      categoryCascadeOptions: [],
      outSynPlatforms: [],
      storeCascadeOptions: [],
      cityCascadeOptions: [],
      marketingTypeOptions: [],
      newMarketingTypeOptions: [],
      crowdAttributes: [],
      skuSceneInfoOptions: [],
      itemStatusOptions: [],
      manageorgListOptions: [],
      bizLineListOptions: [],
      errorMap: null,
      isSaving: false,
      isPublishing: false,
      showPublish: false,
      defaultTime:[moment(), moment().add(1, 'months').set({ hour: 23, minute: 59, second: 59})]
    }
    // let newQueryForm;
    // if(props.queryForm.activityIdsTypes){
    //   newQueryForm = {
    //     ...props.queryForm,
    //   };
    //   newQueryForm.groupActivitiesType.activityIdsTypes = props.queryForm.activityIdsTypes;
    //   newQueryForm.groupActivitiesType.activityIdsTypesValue = props.queryForm.activityIdsTypesValue;
    // }
    this.queryField = new Field(this, {
      parseName: true,
      values: props.queryForm,
      onChange: () => {
        this.props.onUpdateQueryForm(this.queryField.getValues())
      }
    });

    this.outerField = new Field(this, {
      values: props.outerForm,
      onChange: () => {
        this.props.onUpdateOuterForm(this.outerField.getValues())
      }
    })
    this.isInvite = this.props.match.path === '/storepoolInvite/creation/query/:poolId?';
  }

  componentWillReceiveProps(nextProps) {
    let { initQueryForm } = this.props;
    let { queryForm, outerForm } = nextProps;
    queryForm = queryForm && queryForm.toJS ? queryForm.toJS() : queryForm
    initQueryForm = initQueryForm && initQueryForm.toJS ? initQueryForm.toJS() : initQueryForm

    if(queryForm.baseInfo && queryForm.baseInfo.itemPicQualityScore && !(queryForm.baseInfo.itemPicQualityScore instanceof Array)){ // 恶心的商品质量分
      queryForm.baseInfo.itemPicQualityScore = dealQualityScoreToArray(queryForm.baseInfo.itemPicQualityScore);
    }
    if (queryForm.baseInfo && queryForm.baseInfo.activityIdsTypes) { //后面权益池合并会重新改造
      queryForm.baseInfo.groupActivitiesType = {
        activityIdsTypes: queryForm.baseInfo.activityIdsTypes,
        activityIdsTypesValue: queryForm.baseInfo.activityIdsTypesValue
      }
      delete queryForm.baseInfo.activityIdsTypes;
      delete queryForm.baseInfo.activityIdsTypesValue;
    }
    // const showPublish = !isEqual(removeNullOfObj(queryForm), removeNullOfObj(initQueryForm)) ;
    const showPublish = true;
    this.updateState(() => ({ showPublish }))
    // console.log("outerForm",outerForm);
    this.queryField.setValues(queryForm);
    this.outerField.setValues(outerForm);
  }

  /**
   * when initial state username is not null, submit the form to load repos
   */
  async componentDidMount() {
    this.props.initQueryMeta(this.query.basePoolId);
    if (this.isEdit) {
      const {withPermission} = await this.props.getPermission(this.poolId);
      if(!withPermission) return

      if(!this.isAli) {
        this.toast({
          type: 'warning',
          title:'提示',
          content: '正在编辑的是老链路池子，请尽快将其迁移到新链路的池子【淘内池】。'
        })
      }
      // is edit mode
      // 理应来说, 我们应该控制这个位置的异步流程, 从代码层面上保证执行顺序,
      // 但这样目前看比较麻烦, 就但从理解来看, 执行顺序也是可以保证的.
      // 也就是说获得detail之后, 后面的代码才能真正被执行,
      this.props.fetchPoolDetail({
        id: this.poolId,
        isStore: this.isStore,
        dataType: this.dataType
      })
    }

    await this.getSynPoolPlatformList();
    await this.getNewAllStoreMainCategory()
    await this.getStoreMainCategories()
    await this.getSkuCategories()
    await this.getCityOptions()
    await this.getStoreOptions()
    await this.getMarketingTypes()
    await this.getNewMarketingTypes()
    await this.getCrowdAttributes()
    await this.getManageorg()
    await this.getBizLine()
    await this.getSceneInfo()
    await this.getItemStatusOptions()

    this.meta = this.createMeta();
    this.labelMap = collectLabelMap(this.meta);

    this.setState({})
    setTimeout(() => {
      // the following line can not work
      // this.queryField.setValues(this.props.queryForm);
      const { queryForm } = this.props;
      setValue.call(this, queryForm)
      function setValue(queryForm) {
        if (!queryForm) return
        for (let tabkey of Object.keys(queryForm)) {
          let tab = queryForm[tabkey]
          if (!tab) continue

          for (let formkey of Object.keys(tab)) {
            let formvalue = tab[formkey];
            if (formvalue) this.queryField.setValue(`${tabkey}.${formkey}`, formvalue)
          }
        }

      }
    }, 3000)
  }

  handleQuery = () => {
    //goldlog: POOL-NEW
    track('clickEvent', ['/selection_kunlun.POOL-NEW.pool-new-search'])

    this.load({page: 1})
  }

  hasAnyVisibleErrorHint(){
    let activeTabContent = document.querySelector('.next-tabs-tabpane.active')
    let errorNode = activeTabContent.querySelector('.query-form .next-form-item-help')
    return !!errorNode
  }

  async getSkuCategories() {
    try {
      let resp = await api.ali.getCategorySku(this.isInvite).then(api.onRequestSuccess)
      this.updateState(() => ({ categoryCascadeOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getSceneInfo() {
    try {
      let resp = await api.ali.getSceneInfo({id:this.basePoolId}).then(api.onRequestSuccess);
      this.updateState(() => ({ skuSceneInfoOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getCityOptions() {
    try {
      let resp = await api.getCities().then(api.onRequestSuccess)
      this.updateState(() => ({ cityCascadeOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getSynPoolPlatformList() {
    try {
      // let param = this.isStore ? 2 : 1;
      let param = this.basePoolId;
      let resp = await api.getSynPoolPlatformList(param).then(api.onRequestSuccess)
      resp.forEach((respItem) => {
        respItem.value = respItem.platformCode
      })
      this.updateState(() => ({ outSynPlatforms: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getNewAllStoreMainCategory() { //获取新增获取类目接口
    try {
      let resp = await api.ali.getNewAllStoreMainCategory().then(api.onRequestSuccess)
      this.updateState(() => ({ newStoreMainCascadeOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getStoreMainCategories() {
    try {
      let resp = await api.getStoreMainCatetories().then(api.onRequestSuccess)
      this.updateState(() => ({ storeMainCascadeOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getStoreOptions() {
    try {
      let resp = await api.ali.getCategoryStore(this.isInvite).then(api.onRequestSuccess)
      this.updateState(() => ({ storeCascadeOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getMarketingTypes() {
    try {
      let resp;
      if(this.isAli){
        resp = await api.ali.queryMarketingType({id:this.basePoolId}).then(api.onRequestSuccess);
        if(this.basePoolId=='20011' || this.basePoolId=='21011'){
          this.getGroupActivityIdsTypesOptions(resp);
        }
      }else{
        resp = await api.queryMarketingType().then(api.onRequestSuccess);
      }
      this.updateState(() => ({ marketingTypeOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getNewMarketingTypes() {
    try {
      let resp = await api.ali.queryNewMarketingType().then(api.onRequestSuccess);
      this.updateState(() => ({ newMarketingTypeOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getCrowdAttributes() {
    try {
      let resp = await api.getCrowdAttributes().then(api.onRequestSuccess)
      this.updateState(() => ({ crowdAttributes: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getManageorg() {
    try {
      let resp = await api.getManageorg().then(api.onRequestSuccess)
      this.updateState(() => ({ manageorgListOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getBizLine() {
    try {
      let resp = await api.getBizLine().then(api.onRequestSuccess)
      this.updateState(() => ({ bizLineListOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getItemStatusOptions() {
    try {
      let resp = await api.ali.getItemStatusOptions().then(api.onRequestSuccess)
      this.updateState(() => ({ itemStatusOptions: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getGroupActivityIdsTypesOptions(resp) {
    let newGroupActivityIdsTypesOptions = groupActivityIdsTypesOptions.map(v => {
      let item = {...v}
      if (v.value == 'mktType') {
        item.ds = resp;
      }
      return item;
    });
    if (this.basePoolId == '21011') {
      newGroupActivityIdsTypesOptions = newGroupActivityIdsTypesOptions.filter(v => v.value != 'brand');
    }
    this.setState({groupActivityIdsTypesOptions:newGroupActivityIdsTypesOptions});
  }

  createMeta() {
    let { cityCascadeOptions, storeCascadeOptions, categoryCascadeOptions, storeMainCascadeOptions, newStoreMainCascadeOptions, marketingTypeOptions,newMarketingTypeOptions, crowdAttributes, skuSceneInfoOptions,itemStatusOptions,groupActivityIdsTypesOptions: _groupActivityIdsTypesOptions, manageorgListOptions, bizLineListOptions } = this.state
    const { basePoolId } = this;
    let filtering = true
    if (this.isStore && this.getNoTranform(basePoolId)) {
      return createStoreMeta({ basePoolId, filtering }, { cityCascadeOptions, storeCascadeOptions, storeMainCascadeOptions, newStoreMainCascadeOptions, crowdAttributes, storeActivitiesTypeOptions: marketingTypeOptions, manageorgListOptions, bizLineListOptions })
    } else {
      if (!this.isAli) {
        return createCommodityMeta(
          {
            basePoolId, filtering
          }, {
            marketingTypeOptions,
            categoryCascadeOptions,
            storeCascadeOptions,
            cityCascadeOptions,
            storeMainCascadeOptions,
            newStoreMainCascadeOptions,
            itemStatusOptions,
            manageorgListOptions,
            bizLineListOptions
          })
      } else {
        return createAliCommodityMeta(
          {
            basePoolId, filtering
          }, {
            marketingTypeOptions,
            newMarketingTypeOptions,
            categoryCascadeOptions,
            storeCascadeOptions,
            cityCascadeOptions,
            storeMainCascadeOptions,
            newStoreMainCascadeOptions,
            skuSceneInfoOptions,
            itemStatusOptions,
            groupActivityIdsTypesOptions: _groupActivityIdsTypesOptions,
            manageorgListOptions,
            bizLineListOptions
          })
      }
    }
  }

  // dealQualityScoreToObject = (data) =>{
  //   let qualityScoreData = data;
  //   let result = {};
  //   let mapSet = [
  //     {key: 1, field: 'psoriasisLabel', group: ["0", "1", "2"], value: []},
  //     {key: 2, field: 'transparentLabel', group: ["1"], value: []},
  //     {key: 3, field: 'wbLabel', group: ["1"], value: []},
  //     {key: 4, field: 'mainRatioLabel', group: ["0", "1"], value: []},
  //   ]
  //   qualityScoreData.map((v) => {
  //     if (v.value < 10) {
  //       let obj = mapSet.filter((o) => o.key == v.value)[0];
  //       result[obj.field] = obj.group;
  //     } else {
  //       let p = parseInt(v.value / 100);
  //       let t = parseInt(v.value % 10);
  //       let obj = mapSet.filter((o) => o.key == p)[0];
  //       obj.value.push(t);
  //       result[obj.field] = obj.value;
  //     }
  //   })
  //   return result;
  // }

  async load({ page = 1, size, query } = {}) {
    let [error, queryForm] = await this.getQueryFormValues()
    if (error) return
    query = query || queryForm
    const { pagination } = this.state;
    size = size || pagination.size
    page = page || pagination.page


    let isStoreGroup = (this.isStore && this.getNoTranform(this.basePoolId));
    let request = (this.isAli) ? (isStoreGroup ? api.ali.getStoreList : api.ali.getSkuList) : (isStoreGroup ? api.getStoreList : api.getSkuList);
    // if (this.basePoolId == 30004 && this.isStore && this.isAli) { //淘内品转店，调店的接口（之前淘外没支持，现在淘内支持了）
    //   request = api.ali.getStoreList
    // }
    // let request = this.isStore && this.basePoolId!=10013 ? api.getStoreList : api.getSkuList
    if(query.baseInfo && query.baseInfo.labelGroupIds && query.baseInfo.labelGroupIds.length) {
      query.baseInfo.labelGroupIds = query.baseInfo.labelGroupIds.map(idItem =>{
        if (idItem.indexOf('_') !== -1) {
          return idItem;
        } else {
          return isStoreGroup ? 'S_'+idItem : 'I_'+idItem;
        }
      });
    };
    query = unifyFrontEndModel(query)
    if(!query.baseInfo && !this.isStore){
      query.baseInfo = {};
    }
    if (query.baseInfo && query.baseInfo.isDirect == -1) {
      delete query.baseInfo.isDirect;
    }
    if(query.baseInfo && query.baseInfo.groupActivitiesType){
      query.baseInfo.activityIdsTypes = query.baseInfo.groupActivitiesType.activityIdsTypes;
      query.baseInfo.activityIdsTypesValue = query.baseInfo.groupActivitiesType.activityIdsTypesValue;
      delete query.baseInfo.groupActivitiesType;
      delete query.activityIdsTypesValue;
    }
    if (query.itemStoreInfo && query.itemStoreInfo.isDirect == -1) {
      delete query.itemStoreInfo.isDirect;
    }
    if (query.itemTradeInfo && query.itemTradeInfo.nearly7DaysMinHistoryTransactionPrice == -1) {
      delete query.itemTradeInfo.nearly7DaysMinHistoryTransactionPrice;
    }
    if (query.itemTradeInfo && query.itemTradeInfo.nearly15DaysMinHistoryTransactionPrice == -1) {
      delete query.itemTradeInfo.nearly15DaysMinHistoryTransactionPrice;
    }
    // if (query.marketInfo && query.marketInfo.goodsPerfectActivitiesType) {
    //   query.marketInfo.goodsPerfectActivitiesType = JSON.parse(query.marketInfo.goodsPerfectActivitiesType);
    // }
    let {activityIds, brandActivityIds, inviteIds, mainInviteIds} = query.baseInfo;
    let idMaps = [activityIds && activityIds.length > 0, brandActivityIds && brandActivityIds.length > 0, (inviteIds && inviteIds.length > 0) || (mainInviteIds && mainInviteIds.length > 0)];
    let showIds = idMaps.filter(v => Boolean(v) == true);
    if (this.basePoolId == '20012' && showIds.length > 1) {
      this.toast({
        type: 'error',
        content: '营销活动ID、品牌活动ID、招商活动ID互斥，每次只能填写一种活动类型ID'
      })
      return;
    }
    this.setState({ isLoading: true });
    try {
      let resp = await request({
        page: page,
        size: size,
        query: {
          basePoolId: this.basePoolId,
          ...query,
        }
      },this.isInvite).then(api.onRequestSuccess)

      this.setState({
        pagination: {
          page,
          size,
          total: resp.total,
        },
        rows: resp.rows,
        errorMap: resp.errorMap
      })
    } catch (e) {
      api.onRequestError(e)
    } finally {
      this.setState({ isLoading: false })
    }
  }

  onPageChange = (page) => this.load({ page })

  onPageSizeChange = (size) => this.load({ size })

  resetFormField = (key) => {
    //goldlog: POOL-NEW
    track('clickEvent', ['/selection_kunlun.POOL-NEW.pool-new-reset'])

    // fusion 有bug, 不知道是不是因为级联了 parseName: true 这个选项才导致的,
    //  所以才出此下策实在是不想弄了
    if (!key) {
      this.queryField.reset()
    } else {
      this.queryField.reset(key)
    }
    this.props.resetQueryForm({ key })
  }

  get basePoolId() {
    return this.query.basePoolId
  }

  get basePoolName() {
    return this.query.basePoolName
  }

  get dataType() {
    return this.query.dataType
  }

  get isAli() {
    return (this.query.dataType == 1)
  }

  get poolId() {
    return this.params.poolId
  }

  get isEdit(){
    return !!this.poolId
  }

  getNoTranform = (basePoolId) => { //不是品转店
    return ![10013,30004,30006,31004].includes(+basePoolId);
  }

  async getQueryFormValues(){
    try {
      let values = await (promisify(this.queryField.validate)());
      if (values.baseInfo && values.baseInfo.itemPicQualityScore && values.baseInfo.itemPicQualityScore instanceof Array) { //商品质量分 恶心的处理
        values.baseInfo.itemPicQualityScore = dealQualityScoreToObject(values.baseInfo.itemPicQualityScore);
      }
      return [null, values]
    } catch (error) {
      if (!this.hasAnyVisibleErrorHint()) {
        let firstError = error[Object.keys(error)[0]].errors[0]
        this.toast({
          type: 'error',
          content: firstError
        })
      }
      return [error, null]
    }
  }

  /**保存请求 */
  onSaveReq = async (poolId, outerForm, isInvite) => {
    let isStoreGroup = (this.isStore && this.getNoTranform(this.basePoolId));
    let request = (this.isAli) ? (isStoreGroup ? api.ali.saveAsStorePool : api.ali.saveAsSkuPool):(isStoreGroup ? api.saveAsStorePool : api.saveAsSkuPool);
    // let request = this.isStore && this.basePoolId!=10013 ? api.saveAsStorePool : api.saveAsSkuPool
    let queryForm
    try {
      let [queryFormError, queryFormValues] = await this.getQueryFormValues();
      // 选择“不限”的，都不传给后端。
      if (queryFormValues.baseInfo && queryFormValues.baseInfo.isDirect == -1) {
        delete queryFormValues.baseInfo.isDirect;
      }
      if(queryFormValues.baseInfo && queryFormValues.baseInfo.groupActivitiesType){ //todo 看是否需要干掉
        queryFormValues.baseInfo = {
          ...queryFormValues.baseInfo,
          ...queryFormValues.baseInfo.groupActivitiesType
        }
        delete queryFormValues.baseInfo.groupActivitiesType;
        delete queryFormValues.activityIdsTypesValue;
      }
      if (queryFormValues.itemStoreInfo && queryFormValues.itemStoreInfo.isDirect == -1) {
        delete queryFormValues.itemStoreInfo.isDirect;
      }
      if (queryFormValues.itemTradeInfo && queryFormValues.itemTradeInfo.nearly7DaysMinHistoryTransactionPrice == -1) {
        delete queryFormValues.itemTradeInfo.nearly7DaysMinHistoryTransactionPrice;
      }
      if (queryFormValues.itemTradeInfo && queryFormValues.itemTradeInfo.nearly15DaysMinHistoryTransactionPrice == -1) {
        delete queryFormValues.itemTradeInfo.nearly15DaysMinHistoryTransactionPrice;
      }
      // if (queryFormValues.marketInfo && queryFormValues.marketInfo.goodsPerfectActivitiesType) {
      //   queryFormValues.marketInfo.goodsPerfectActivitiesType = JSON.parse(queryFormValues.marketInfo.goodsPerfectActivitiesType);
      // }
      if(queryFormValues.baseInfo && queryFormValues.baseInfo.labelGroupIds && queryFormValues.baseInfo.labelGroupIds.length) {
        queryFormValues.baseInfo.labelGroupIds = queryFormValues.baseInfo.labelGroupIds.map(idItem =>{
          if (idItem.indexOf('_') !== -1) {
            return idItem;
          } else {
            return isStoreGroup ? 'S_'+idItem : 'I_'+idItem;
          }
        });
      };
      queryForm = queryFormValues
      if (queryFormError) {
        const error = new Error();
        error.message = 'queryForm error';
        throw error;
      }
    } catch (_error) {
      throw _error
    }

    let data
    let poolName = outerForm.name
    let common = {
      poolName,
      effectAt: outerForm.effectRange[0].valueOf(),
      expireAt: outerForm.effectRange[1].valueOf(),
    }
    if (!this.query.isCopy || this.query.isCopy == 'false') {
      common.poolId = poolId;
    }
    // if (this.isStore) {
    //   data = {
    //     ...common,
    //     poolRule: {
    //       basePoolId: this.basePoolId,
    //       ...queryForm
    //     }
    //   }
    // } else {
      common.outSynPlatforms = outerForm.outSynPlatforms;
      if(!(this.isEdit && outerForm.refreshMode=='1')) {
        common.refreshMode = outerForm.refreshMode;
      }
      common.poolResultLimit = outerForm.poolResultLimit ? outerForm.poolResultLimit : '';
      data = {
        ...common,
        poolRule: {
          basePoolId: this.basePoolId,
          ...queryForm
        }
      }
    // }
    // filter out empty array item
    data = unifyFrontEndModel(data)
    let id = await request(data,isInvite).then(api.onRequestSuccess)
    // 统计配置指标: CONFIG-INDICES
    goldLogTag(data, this.isStore);

    return id;
  }

  onSave = async () => {
    //goldlog: POOL-NEW
    track('clickEvent', ['/selection_kunlun.POOL-NEW.pool-new-save'])
    const outerForm = await (promisify(this.outerField.validate)());
    await (promisify(this.queryField.validate)());
    try {
      this.updateState(() => ({ isSaving: true }))
      const id = await this.onSaveReq(this.poolId, outerForm,this.isInvite);

      // 打点 CONFIG-TIME
      if (!this.isEdit) {
        goldLogTimeConsume(this.isStore)
      }

      this.toast('操作成功')
      // :todo, 需要后端同步接口， 前端嵌入这样的逻辑感觉后面会出问题
      let query = {
        basePoolId: this.basePoolId,
        name: outerForm.name,
        canModify: this.isStore || (!this.isStore && this.poolId == basepoolIds.ALL),
        dataType:this.dataType
      }
      this.history.push(`/${this.isStore ? 'store' : 'commodity'}pool${this.isInvite?'Invite':''}/list/detail/${id}?${this.encodeQuery(query)}`)
    } catch (error) {
      if(error.message === 'queryForm error'){
        console.error(error.message);
      } else {
        api.onRequestError(error)
      }

    } finally {
      this.updateState(() => ({ isSaving: false }))
    }
  }

  /**发布 */
  onPublish = async () => {
    //goldlog: POOL-NEW
    track('clickEvent', ['/selection_kunlun.POOL-NEW.pool-new-publish'])
    const outerForm = await (promisify(this.outerField.validate)());

    try {
      this.updateState(() => ({ isPublishing: true }))
      const { name } = outerForm;
      const id = await this.onSaveReq(this.poolId, outerForm,this.isInvite);
      let request = (this.isAli) ? (api.ali.publishPool) : (api.publishPool);
      await request({ isStore: this.isStore, id });

      this.history.push(`/${this.isStore? 'store' : 'commodity'}pool${this.isInvite?'Invite':''}/list/progress/${id}?${this.encodeQuery({name, basePoolId: this.basePoolId, dataType:this.dataType })}`)
    } catch (error) {
      api.onRequestError(error)
    } finally {
      this.updateState(() => ({ isPublishing: false }))
    }
  }

  /**上一步 */
  updateStep = () => {
    //goldlog: POOL-NEW
    track('clickEvent', ['/selection_kunlun.POOL-NEW.pool-new-last'])

    this.history.push(`/${this.isStore? 'store' : 'commodity'}pool${this.isInvite?'Invite':''}/creation`)
  }

  renderSortButton() {
    let props = this.queryField.init('sort', {
      props: {
        onChange: v => {
          this.queryField.setValue('sort', v)
          this.load()
        }
      }
    })

    if (this.isStore && this.getNoTranform(this.basePoolId)) {
      return <ButtonSwitch
        {...props}
        options={[
          { label: '门店7d销量', value: { field: 'd7StoreSalesNum', order: 'des' } },
        ]}></ButtonSwitch>
    }
    let hasPriceSet = ['10002', '10003', '10005', '10007', '10011'];
    let isShowP = (this.isStore || (hasPriceSet.includes(this.basePoolId)));
    return <ButtonSwitch
      {...props}
      options={[
        { label: '商品7d销量', value: { field: 'd7ValidOrder', order: 'des' } },
        ...(isShowP ? [{ label: '商品现价', value: { field: 'presentPrice', order: 'des' } }] : [])
      ]}></ButtonSwitch>
  }

  // flatten = (arr) => {
  //   let res = arr.reduce((pre, cur) => {
  //     if (cur.children && cur.children.length > 0) {
  //       return pre.concat(cur).concat(cur.children);
  //     } else {
  //       return pre.concat(cur)
  //     }
  //   }, []);
  //   return this.changeToObject(res);
  // }
  //
  // changeToObject = (arr) =>{
  //   let obj = {};
  //   arr.map((v) => {
  //     obj[v.value] = v.label;
  //   })
  //   return obj;
  // }

  // dealQualityScoreToArray = (data) =>{
  //   let mapSet = [
  //     {key: 1, field: 'psoriasisLabel', group: [0, 1, 2], value: []},
  //     {key: 2, field: 'transparentLabel', group: [1], value: []},
  //     {key: 3, field: 'wbLabel', group: [1], value: []},
  //     {key: 4, field: 'mainRatioLabel', group: [0, 1], value: []},
  //   ]
  //   let result = [];
  //   for(const o in data){
  //     const fieldMap = mapSet.map(v=>v.field);
  //     const curObj = mapSet.filter((v)=>v.field==o);
  //     if (fieldMap.includes(o) && data[o].length==curObj[0].group.length) result.push({value: curObj[0].key, level: 1});
  //     if (data[o].length > 0 && data[o].length!=curObj[0].group.length) {
  //       data[o].map((v) => {
  //         result.push({
  //           value: `${curObj[0].key}0${v}`,
  //           level: 2
  //         })
  //       })
  //     }
  //   }
  //   return result;
  // }

  renderGroupActivitiesTypeTag = (queryForm) =>{
    let tagsOthers,label,value;
    const {groupActivityIdsTypesOptions: _groupActivityIdsTypesOptions} = this.state;
    if(queryForm.baseInfo && queryForm.baseInfo.groupActivitiesType && queryForm.baseInfo.groupActivitiesType.activityIdsTypesValue.length>0){
      let groupActivitiesType = queryForm.baseInfo.groupActivitiesType;
      let {activityIdsTypes, activityIdsTypesValue} = groupActivitiesType;
      const currenOptions = _groupActivityIdsTypesOptions ? _groupActivityIdsTypesOptions.filter(v => v.value === activityIdsTypes)[0] : [];
      const mktTypeOption = _groupActivityIdsTypesOptions ? _groupActivityIdsTypesOptions.filter(v => v.value === 'mktType')[0].ds : [];
      label = currenOptions.label;
      value = (activityIdsTypes != "mktType") ? activityIdsTypesValue.join(",") : activityIdsTypesValue.map(v => (mktTypeOption && mktTypeOption.length > 0) ? mktTypeOption.filter(m => m.value == v)[0].label : '').join(",");
      tagsOthers =  <p size="medium" className="tagbody"><span className="taglabel">{label}：</span> <span className="tagvalue">{value}</span><Icon type="close" size="xxs" className='tagclose' onClick={() => this.resetFormField('baseInfo.groupActivitiesType')}/></p>
    }
    return tagsOthers;
  }

  renderSelectedItems() {
    const { queryMeta, queryForm, basePoolId } = this.props;
    const { marketingTypeOptions, skuSceneInfoOptions, newMarketingTypeOptions, bizLineListOptions, manageorgListOptions  } = this.state;
    if (!queryMeta) return null

    if (this.isAli && marketingTypeOptions.length > 0) { //入淘的商品类型
      queryMeta.labelMap.others.goodsActivitiesType.valueMap = flatten(marketingTypeOptions);
    }

    if (this.isAli && queryMeta.labelMap.others.activityTypes && newMarketingTypeOptions.length > 0) { //入淘的商品类型
      queryMeta.labelMap.others.activityTypes.valueMap = flatten(newMarketingTypeOptions);
    }

    if (bizLineListOptions.length > 0) { // 业务线回显，注：通用的都在 global 中
      queryMeta.labelMap.others.bizLineLv3CodeV2 =  {
        valueMap: flatten(bizLineListOptions)
      }
      // queryMeta.labelMap.others.bizLineLv3Code =  {
      //   valueMap: flatten(bizLineListOptions)
      // }
    }

    if (manageorgListOptions.length > 0) { // 经营归属回显 注：通用的都在 global 中
      queryMeta.labelMap.others.manageOrgLv2CodeV2 =  {
        valueMap: flatten(manageorgListOptions)
      }
      // queryMeta.labelMap.others.manageOrgLv2Code =  {
      //   valueMap: flatten(manageorgListOptions)
      // }
    }
    
    if (this.isAli && skuSceneInfoOptions.length > 0) { //场景属性
      skuSceneInfoOptions.forEach(({sceneList, sceneTypeName, sceneTypeKey})=>{
        queryMeta.labelMap.sku[sceneTypeKey] = sceneTypeName;
        queryMeta.labelMap.others[sceneTypeKey] = {
          valueMap: flatten(sceneList)
        }
      })
    }
    if (!(this.isStore && this.getNoTranform(basePoolId))) { //店和品的支付宝场景指标重名了，需要特殊处理一下
      queryMeta.labelMap.others.alipayRelId.valueMap = flatten(alipayRelIdSourceCommodity);
    }
    let tags = humanizeQueryForm({ queryForm, labelMap: queryMeta.labelMap, isStore: this.isStore && this.getNoTranform(basePoolId)}).map(({ formkey, key, label, value }) => {
      if (formkey == 'isActivityPriceLessThanNearly7DayMinPrice' && basePoolId != "40001" && basePoolId != "20012") {
        return null;
      } else {
        return <p size="medium" key={formkey} className="tagbody"><span className="taglabel">{label}：</span> <span className="tagvalue">{value}</span><Icon type="close" size="xxs" className='tagclose' onClick={() => this.resetFormField(key)}/></p>
      }
    })
    if (!tags.length) return null
    return <PageSection title="已选条件" className="selected-items">
      <div className="tag-group">
        {this.renderGroupActivitiesTypeTag(queryForm)}{tags}
      </div>
    </PageSection>
  }

  createColumn = () =>{
    let result;
    if (this.basePoolId == 10013) {  //原本是用这个!this.getNoTranform(this.basePoolId)，但30004,30006输入的跟品保持，输出跟店保持一致
      result = CommodityTransformColumns;
    } else if([30004,30006,31004].includes(+this.basePoolId)){
      result = NewCommodityColumns;
    }else if (this.isStore) {
      result = deepClone(this.isAli ? NewStoreColumns : StoreColumns);
    } else {
      result = deepClone(this.isAli ? NewCommodityColumns : CommodityColumns);
    }
    return result;
  }


  judgeAccess = async(permissionType,value,callback) =>{
    let resp = await api.validateComponentsPermission({"permissionType":permissionType,"permissionValue":value}).then(api.onRequestSuccess);
    let {rediectUrl} = resp;
    this.setState({
      rediectUrl
    }, () => {
      if(this.state.rediectUrl){
        Dialog.confirm({
          title: '申请权限',
          footer: false,
          content: [<ACLAccess rediectUrl={this.state.rediectUrl}/>],
        })
        if (permissionType == 'ACTION' || permissionType == 'ACTION_DCDB') { //适用场景
          let originGroup = this.outerField.getValue('outSynPlatforms');
          let newGroup = originGroup.filter(v => v != value);
          this.outerField.setValue('outSynPlatforms', newGroup);
        }else if(permissionType == "ACTION_MILLION"){ //结果集上限
          this.outerField.setValue('poolResultLimit', '');
        }
        this.props.onUpdateOuterForm(this.outerField.getValues());
      }else{
        callback(value);
      }
    });
  }

  changeScene = async(group,e) =>{
    // console.log(group, e.target.value);removeNullOfObj
    if (group.includes(e.target.value) && e.target.value != 'kbzk') {
      let scenePermissionType = "ACTION";
      if(e.target.value == 'dcdb') scenePermissionType= 'ACTION_DCDB';
      this.judgeAccess(scenePermissionType,e.target.value, function (value) {
        console.log( `${value}有权限`);
      });
    }
  }

  changePoolLimit = async(value) =>{
    let self = this;
    if(value!='0') {
      this.judgeAccess("ACTION_MILLION", value, function (_value) {
        self.outerField.setValue('poolResultLimit', _value);
        self.props.onUpdateOuterForm(self.outerField.getValues());
      });
    }else{
      self.outerField.setValue('poolResultLimit', value);
      self.props.onUpdateOuterForm(self.outerField.getValues());
    }
  }

  render() {
    const { pagination, rows, isLoading, errorMap, showPublish, outSynPlatforms, defaultTime } = this.state;
    const { isEdit } = this;
    let labelText = this.isStore ? '门店池名称' : '商品池名称'
    const middleStepText = this.isStore ? '创建门店池' : '创建商品池';
    const basePoolNameText = this.basePoolName ? `(${this.basePoolName})` : '';
    const title = `${isEdit ? '编辑' : '创建'}${this.isStore ? '门店' : '商品'}池  ${basePoolNameText}`;
    // let tableColumns = [];
    // if (!this.getNoTranform(this.basePoolId)) {
    //   tableColumns = CommodityTransformColumns;
    // } else if (this.isStore) {
    //   tableColumns = StoreColumns;
    // } else {
    //   tableColumns = CommodityColumns;
    // }
    // const poolResultLimitMap = [{
    //   label: '100w结果集（池子总数量数限制300个）',
    //   value: '1000000'
    // }, {
    //   label: '200w结果集（池子总数量数限制100个）',
    //   value: '2000000'
    // }]
    const poolResultLimitMap = [{
      label: '100w结果集（池子总数量数限制300个）',
      value: '1000000'
    }];
    const poolLimitSet = ["20001", "20002", "20004", "20005", "20006", "20007", "20008", "20010", "20012","20011","21011"];
    const  refreshModeGroup= [
      {label: '每日增量更新(只增不减)', value: 11},
      {label: '每日全量更新(有增有减)', value: 12}
    ]
    return (
      <PoolPageBase.Container className="page-CreateCommodityPoolPage">
        {this.isInvite&&<div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item><Link to={`/${this.isStore ? 'store' : 'commodity'}poolInvite/list`}>{`管理${this.isStore ? '门店' : '商品'}池`}</Link></Breadcrumb.Item>
            <Breadcrumb.Item> {title} </Breadcrumb.Item>
          </Breadcrumb>
        </div>}
        <PageWrapper title={title}>
          <Steps current={1} middleText={middleStepText}/>
          <PageSection title={labelText} className="outer-form">
            <Form style={{ width: '70%' }} {...formItemLayout} field={this.outerField}>
              <FormItem
                label={`${labelText}: `}
                required
                requiredTrigger="onBlur"
                requiredMessage={`请输入${labelText}, 最多不超过15个字`}>
                <Input htmlType="text" name="name" placeholder="" maxLength={15} />
              </FormItem>
              <FormItem
                label="时间段"
                required
                requiredMessage="请输入时间段"
                validator={timeRangeValidator}
                validatorTrigger={['onChange']}>
                <TimeRangePicker defaultValue={defaultTime} showTime={true} name="effectRange" disabledDate={(date) => date.isBefore(moment().startOf('day'))}></TimeRangePicker>
              </FormItem>
              <FormItem label="更新机制" required asterisk={false}  requiredMessage="更新机制必填" {...formItemLayout}>
                <Radio.Group disabled={isEdit} value={this.outerField.getValue("refreshMode") ? this.outerField.getValue("refreshMode") : 11} dataSource={refreshModeGroup} name="refreshMode"/>
                <Balloon.Tooltip
                  trigger={<span className="help">?</span>}>只有当主站对超会运费满减进行招商时，才选择每日全量更新(有增有减)</Balloon.Tooltip>

              </FormItem>
              {(this.basePoolId != '40001' && (outSynPlatforms && outSynPlatforms.length>0)) && <FormItem
                label="适用场景"
                placeholder="请选择适用场景"
                requiredMessage="请选择适用场景">
                <CheckGroup name="outSynPlatforms" dataSource={outSynPlatforms} onChange={this.changeScene} style={{ width: '150px' }}/>
              </FormItem>}
              {(poolLimitSet.includes(this.basePoolId)) && <FormItem
                label={<>结果集上限<Balloon.Tooltip trigger={<span className="help">?</span>} >只有创建时可选</Balloon.Tooltip></>}
                placeholder="请选择结果集上限"
                requiredMessage="请选择结果集上限">
                <RadioGroup name="poolResultLimit" disabled={this.isEdit} dataSource={poolResultLimitMap} onChange={this.changePoolLimit} />
              </FormItem>}
            </Form>
          </PageSection>

          <Form {...formItemLayout} size="medium" field={this.queryField} >
            <PageSection title="筛选区域" className="query-form">
              {
                this.meta && <Tab shape="wrapped">
                  {generate.call(this, this.meta,this.queryField)}
                </Tab>
              }
              {
                this.renderSelectedItems()
              }
              <div className="buttons">
                <Button type="primary" onClick={this.handleQuery}>查询</Button>
                <Button className="Reset" onClick={() => this.resetFormField()}>重置</Button>
              </div>
            </PageSection>

          </Form>
          <PageSection title={
            <div className="sort-bar">
              <span className="title">查询结果共{pagination.total}个</span>
              {this.renderSortButton()}
            </div>
          }>
            {
              this.meta && <QueryErrors errorMap={errorMap}/>
            }
            <Table dataSource={rows} loading={isLoading} hasBorder={false} >
              {
                (this.createColumn()).map((c, idx) => {
                  return <Table.Column {...c} key={idx} />
                })
              }
            </Table>
            <div className="pagination">
              <Pagination
                popupProps = {{align:'bl tl'}}
                pageSizeList={[20, 50, 100]}
                current={pagination.page}
                total={pagination.total}
                pageSize={pagination.size}
                onChange={this.onPageChange}
                pageSizeSelector="dropdown"
                pageSizePosition="end"
                onPageSizeChange={this.onPageSizeChange} />
            </div>
          </PageSection>

          <div className="buttons bottom">
            <Button type="primary" onClick={this.onSave} disabled={this.state.isSaving} loading={this.state.isSaving}>保存</Button>
            {
              showPublish && <Button type="primary" onClick={this.onPublish} disabled={this.state.isPublishing} loading={this.state.isPublishing}>发布</Button>
            }
            <Button onClick={this.updateStep}>上一步</Button>
          </div>
        </PageWrapper>
      </PoolPageBase.Container>
    )
  }
}

export const SelectQueryPoolPage = permissionAccess(BaseSelectQueryPoolPage, async (id) => {
  return await api.permission.checkPool(id).then(api.onRequestSuccess);
})


function QueryErrors({ errorMap }) {
  if (!errorMap) return null
  let content = Object.keys(errorMap).map((key, idx) => {
    return <div className="row" key={idx}><span className="err-msg">{key}: <span className="red">{errorMap[key].join(',')}</span></span><span> 等ID无效，其他ID已成功输入。</span></div>
  })

  return <div className="error-bar">
    {content}
  </div>
}

const stateMap = {
  0: '下架',
  1: '上架',
  2: '删除',
}

const stateIcon = {
  0: 'error',
  1: 'success',
  2: 'failed'
}

const typeMap = {
  0: '非标',
  1: '标品',
}

const filterValue = (value) => value || '--';

export const CommodityColumns = [ //淘外的
  { title: '商品名称', dataIndex: 'goodsName', lock: 'left', width: 140 },
  { title: '商品ID', dataIndex: 'goodsId', width: 200 },
  { title: '商品条形码', dataIndex: 'goodsUPCId', width: 200 },
  { title: '商品图片', dataIndex: 'goodsPic', width: 100, cell: (imgsrc, index, record) => {
    return <PreviewImage src={imgsrc || DEFAULT_GOODS_IMG} title={record.goodsName}/> }
  },
  { title: '商品原价', dataIndex: 'goodsOriginPrice', width: 100 },
  { title: '商品现价', dataIndex: 'goodsPresentPrice', width: 100 },
  { title: '库存', dataIndex: 'goodsNumber', width: 140 },
  // { title: "状态", dataIndex: "goodsState", width: 100, cell: (state) =>  <StatusLabel text={stateMap[state]} type={stateIcon[state]}/>  },
  { title: '状态', dataIndex: 'goodsState', width: 100, cell: (state) => renderNewStateMap(state)   },
  { title: '销量', dataIndex: 'goodsSales', width: 100 },
  { title: '重量', dataIndex: 'weight', width: 100 },
  { title: '营销活动', dataIndex: 'marketingActivities', width: 100, cell: (act) => actMap[act] },
  // { title: "商品类型", dataIndex: "goodsType", width: 100, cell:(type)=>  `${typeMap[type] || ''}`},
  { title: '门店名称', dataIndex: 'storeName', width: 150 },
  { title: 'ELE门店ID', dataIndex: 'eleStoreId', width: 150 },
  { title: '昆仑门店ID', dataIndex: 'kunlunStoreId', width: 150 },
]

export const StoreColumns = [  //淘外的
  { title: '门店名称', dataIndex: 'storeName', lock: 'left', width: 120 },
  { title: '标签', dataIndex: 'storeLabel', cell: (value) => storeLabelMap[value], width: 100 },
  { title: '昆仑门店id', dataIndex: 'kunlunStoreId', width: 200 },
  { title: 'ele门店id', dataIndex: 'eleStoreId', width: 200 },
  { title: '供应商ID', dataIndex: 'supplierId', width: 200 },
  { title: '所属品牌', dataIndex: 'brand', width: 90},
  { title: '营业状态', dataIndex: 'businessStatus', cell: (value) => renderBusiness(value), width: 90 },
  { title: '创建时间', dataIndex: 'createTime', width: 200 },
  { title: '门店品类', dataIndex: 'storeCategory', width: 90 },
  { title: '店铺评分', dataIndex: 'storeScore', width: 90 },
  { title: '7d营业额', dataIndex: 'sevenDayTurnover', width: 100 },
  { title: '7d订单量', dataIndex: 'sevenDayOrderNum', width: 100 },
  { title: '城市', dataIndex: 'city', width: 90 },
]

export const NewCommodityColumns = [ //淘内-创建商品池-查询列表
  { title: "商品名称", dataIndex: "goodsName", lock: 'left', width: 140, cell: (name, index, record) => {
      let style = record.filterKeyword ? { color: "red", "text-decoration": "line-through" } : {};
      return <span style={style}>{name}</span>
    }
  },
  { title: "商品ID", dataIndex: "goodsId", width: 200 },
  { title: "商品条形码", dataIndex: "goodsUPCId", width: 200 },
  { title: "活动id", dataIndex: "activityId", width: 200, cell: (name, index, record) => {
      return <span>{name ? name : '-'}</span>
    }
  },
  { title: "商品图片", dataIndex: "goodsPic", width: 100, cell: (imgsrc, index, record) => {
    return <PreviewImage src={imgsrc || DEFAULT_GOODS_IMG} title={record.goodsName}/> }
  },
  { title: '商品原价', dataIndex: 'goodsOriginPrice', width: 100 },
  { title: '商品活动价', dataIndex: 'goodsPresentPrice', width: 120 },
  { title: 'C端现价', dataIndex: 'goods2CPresentPrice', width: 120 },
  { title: '库存', dataIndex: 'goodsNumber', width: 140 },
  { title: '状态', dataIndex: 'goodsState', width: 100, cell: (state) => renderNewStateMap(state)  },
  {title: '门店名称', dataIndex: 'storeName', width: 150},
  { title: 'ELE门店ID', dataIndex: 'eleStoreId', width: 120 },
  {title: '淘内门店ID', dataIndex: 'storeId', width: 150},
]

export const NewStoreColumns = [ //淘内-创建门店池-查询列表
  { title: '门店名称', dataIndex: 'storeName', lock: 'left', width: 120 },
  { title: '标签', dataIndex: 'storeLabel', cell: (value) => storeLabelMap[value], width: 120 },
  { title: 'ELE门店ID', dataIndex: 'eleStoreId', width: 120 },
  { title: '淘内门店ID', dataIndex: 'id', width: 200 },
  { title: '淘系卖家ID', dataIndex: 'sellerId', width: 200 },
  { title: '所属品牌', dataIndex: 'brand', width: 90},
  { title: '营业状态', dataIndex: 'businessStatus', cell: (value) => renderBusiness(value), width: 90 },
]


export const NewDetailCommodityColumns = [ //淘内-商品池详情-查询结果
  { title: "商品名称", dataIndex: "goodsName", lock: 'left', width: 140, cell: (name, index, record) => {
      let style = record.filterKeyword ? { color: "red", "text-decoration": "line-through" } : {};
      return <span style={style}>{name}</span>
    }
  },
  { title: "商品ID", dataIndex: "goodsId", width: 200 },
  { title: "商品条形码", dataIndex: "goodsUPCId", width: 200 },
  { title: "活动id", dataIndex: "activityId", width: 200, cell: (name, index, record) => {
      return <span>{name ? name : '-'}</span>
    }
  },
  { title: "商品图片", dataIndex: "goodsPic", width: 100, cell: (imgsrc, index, record) => {
      return <PreviewImage src={imgsrc || DEFAULT_GOODS_IMG} title={record.goodsName}/> }
  },
  { title: '商品原价', dataIndex: 'goodsOriginPrice', width: 100 },
  { title: '商品活动价', dataIndex: 'goodsPresentPrice', width: 120 },
  { title: 'C端现价', dataIndex: 'goods2CPresentPrice', width: 120 },
  { title: '库存', dataIndex: 'goodsNumber', width: 140 },
  { title: '状态', dataIndex: 'goodsState', width: 100, cell: (state) => renderNewStateMap(state)  },
  { title: '销量', dataIndex: 'goodsSales', width: 100 },
  // { title: "重量", dataIndex: "weight", width: 100 },
  // { title: "商品类型", dataIndex: "goodsType", width: 100, cell:(type)=>  `${typeMap[type] || ''}`},
  { title: '门店名称', dataIndex: 'storeName', width: 150 },
  { title: 'ELE门店ID', dataIndex: 'eleStoreId', width: 120 },
  { title: '淘内门店ID', dataIndex: 'storeId', width: 150 },
  { title: '淘系卖家ID', dataIndex: 'sellerId', width: 150 },
]

export const NewDetailStoreColumns = [ //淘内-门店池详情-查询结果
  { title: '门店名称', dataIndex: 'storeName', lock: 'left', width: 120 }, //门店池详情-查看列表-
  { title: '标签', dataIndex: 'storeLabel', cell: (value) => storeLabelMap[value], width: 100 },
  { title: 'ELE门店ID', dataIndex: 'eleStoreId', width: 120 },
  { title: '淘内门店ID', dataIndex: 'id', width: 200 },
  { title: '淘系卖家ID', dataIndex: 'sellerId', width: 200 },
  { title: '所属品牌', dataIndex: 'brand', width: 90},
  { title: '营业状态', dataIndex: 'businessStatus', cell: (value) => renderBusiness(value), width: 90 },
  { title: '创建时间', dataIndex: 'createTime', width: 200 },
  { title: '门店品类', dataIndex: 'storeCategory', width: 90 },
  { title: '店铺评分', dataIndex: 'storeScore', width: 90 },
  { title: '7d营业额', dataIndex: 'sevenDayTurnover', width: 100 },
  { title: '7d订单量', dataIndex: 'sevenDayOrderNum', width: 100 },
  { title: '城市', dataIndex: 'city', width: 90 },
]

export const CommodityTransformColumns = [
  { title: '门店名称', dataIndex: 'storeName', width: 150 },
  { title: 'ELE门店ID', dataIndex: 'eleStoreId', width: 150 },
  { title: '昆仑门店ID', dataIndex: 'kunlunStoreId', width: 150 },
  { title: '商品名称', dataIndex: 'goodsName', lock: 'left', width: 140 },
  { title: '商品ID', dataIndex: 'goodsId', width: 200 },
  { title: '商品条形码', dataIndex: 'goodsUPCId', width: 200 },
  { title: '商品图片', dataIndex: 'goodsPic', width: 100, cell: (imgsrc, index, record) => {
    return <PreviewImage src={imgsrc || DEFAULT_GOODS_IMG} title={record.goodsName}/> }
  },
  { title: '商品原价', dataIndex: 'goodsOriginPrice', width: 100 },
  { title: '商品现价', dataIndex: 'goodsPresentPrice', width: 100 },
  { title: '库存', dataIndex: 'goodsNumber', width: 140 },
  { title: '状态', dataIndex: 'goodsState', width: 100, cell: (state) =>  <StatusLabel text={stateMap[state]} type={stateIcon[state]}/>  },
  { title: '销量', dataIndex: 'goodsSales', width: 100 },
  { title: '重量', dataIndex: 'weight', width: 100 },
  { title: '营销活动', dataIndex: 'marketingActivities', width: 100, cell: (act) => actMap[act] },
  { title: '商品类型', dataIndex: 'goodsType', width: 100, cell:(type)=>  `${typeMap[type] || ''}`},
]

export const FailStoreColumns = [ //失败列表-门店
  { title: '门店名称', dataIndex: 'storeName', width: 120, cell: (v) => `${filterValue(v)}`},
  { title: '淘内门店ID', dataIndex: 'id', width: 120, cell: (v) => `${filterValue(v)}`},
  { title: '饿了么门店ID', dataIndex: 'eleStoreId', width: 120, cell: (v) => `${filterValue(v)}`},
  { title: '操作时间', dataIndex: 'createdTime', width: 120, cell: (v) => `${filterValue(v)}`},
  { title: '失败记录原因', dataIndex: 'errorMsg', width: 200, cell: (v) => `${filterValue(v)}`}
]

export const FailCommodityColumns = [ //失败列表-商品
  {title: '商品名称', dataIndex: 'goodsName', width: 120, cell: (v) => `${filterValue(v)}`},
  {title: '商品ID', dataIndex: 'goodsId', width: 120, cell: (v) => `${filterValue(v)}`},
  {title: '商品条形码', dataIndex: 'goodsUPCId', width: 120, cell: (v) => `${filterValue(v)}`},
  {title: '门店名称', dataIndex: 'storeName', width: 200, cell: (v) => `${filterValue(v)}`},
  {title: '淘内门店ID', dataIndex: 'storeId', width: 120, cell: (v) => `${filterValue(v)}`},
  {title: '操作时间', dataIndex: 'createdTime', width: 120, cell: (v) => `${filterValue(v)}`},
  {title: '失败记录原因', dataIndex: 'errorMsg', width: 120, cell: (v) => `${filterValue(v)}`},
]

const storeLabelMap = {
  9: '全国KA',
  10: '城市龙头',
  11: '其他连锁',
  12: '散店',
}

function renderNewStateMap(value) {
  let text;
  let icon;
  switch (value){
    case 1:
      text = '上架';
      icon = 'success';
      break;
    case 0:
      text = '下架';
      icon = 'error';
      break;
    default:
      break;
  }
  // switch (value){
  //   case 0:
  //   case 1:
  //     text = '正常';
  //     icon = 'success';
  //     break;
  //   case -1:
  //     text = '用户删除';
  //     icon = 'warning';
  //     break;
  //   case -2:
  //     text = '用户下架';
  //     icon = 'error';
  //     break;
  //   case -3:
  //     text = '小二下架';
  //     icon = 'error';
  //     break;
  //   case -4:
  //     text = '小二删除';
  //     icon = 'warning';
  //     break;
  //   case -5:
  //     text = '从未上架';
  //     icon = '';
  //     break;
  //   case -9:
  //     text = 'CC';
  //     icon = '';
  //     break;
  //   default:
  //     break;
  // }
  return <StatusLabel text={text} type={icon}/>;
}


function renderBusiness(value) {
  const businessMenu = {
    1: '营业中',
    3: '休息中',
    9: '暂停营业',
    500: '百度外卖侧异常'
  }

  const businessType = {
    1: 'success',
    3: 'warning',
    9: 'error'
  }

  return <StatusLabel text={businessMenu[value]} type={businessType[value]} />
}


function unifyFrontEndModel(object) {

  switch (true) {
    case object == null: return object
    case Array.isArray(object): return object.map((item) => typeof item === 'string' ? item.trim() : item).filter(e => (e === 0) || !!e)
    case typeof object === 'object':
      if (object instanceof moment) {
        return object.valueOf()
      } else if (object.constructor === Date) {
        return object.getTime()
      } else if (object.start || object.end) {
        return object
      } else {
        return Object.keys(object).reduce((p, c) => {
          p[c] = unifyFrontEndModel(object[c])
          return p
        }, {})
      }
    default:
      return object
  }
}
