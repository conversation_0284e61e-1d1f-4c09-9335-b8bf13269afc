import { put, takeLatest } from 'redux-saga/effects';
import { INIT_QUERY_META, FETCH_EDIT_DETAIL } from '@/containers/PoolPage/common/constants';
import { updateOuterField, updateQueryField, updateBasePoolId, initQueryField } from '@/containers/PoolPage/common/actions';
import { createCommodityMeta, createStoreMeta, collectLabelMap, collectDataSources } from '@/containers/PoolPage/SelectQueryPoolPage/queryfield';
import { toastMessage } from '@/containers/App/actions';
import  * as api from '@/utils/api';
import * as actions from '@/containers/App/actions';
import moment from 'moment';
import { formatTimeStamp } from '@/utils/time';


export function* fetchPoolDetail({payload}) {
  const {isStore, id , dataType} = payload;
  try {
    let request = (dataType == 1) ? api.ali.getPoolDetail : api.getPoolDetail;
    const detail = yield request({isStore, id});
    if(detail.basePoolId === '30005') {
      if (detail.filterRules.baseInfo && detail.filterRules.baseInfo.isDirect == undefined) {
        detail.filterRules.baseInfo.isDirect = -1;
      }
    }
    if(detail.basePoolId === '30006') {
      if (detail.filterRules.itemStoreInfo && detail.filterRules.itemStoreInfo.isDirect == undefined) {
        detail.filterRules.itemStoreInfo.isDirect = -1;
      }
      if (detail.filterRules.itemTradeInfo && detail.filterRules.itemTradeInfo.nearly7DaysMinHistoryTransactionPrice == undefined) {
        detail.filterRules.itemTradeInfo.nearly7DaysMinHistoryTransactionPrice = -1;
      }
      if (detail.filterRules.itemTradeInfo && detail.filterRules.itemTradeInfo.nearly15DaysMinHistoryTransactionPrice == undefined) {
        detail.filterRules.itemTradeInfo.nearly15DaysMinHistoryTransactionPrice = -1;
      }
    }
    let outerForm = {
      name: detail.poolName,
      refreshMode: detail.refreshMode,
      effectRange: [moment(detail.effectAt), moment(detail.expireAt)],
      outSynPlatforms: detail.outSynPlatforms,
      poolResultLimit: detail.poolResultLimit ? detail.poolResultLimit.toString() : ''
    }
    yield put(initQueryField(detail.filterRules))
    yield put(updateBasePoolId(detail.basePoolId))
    yield put(updateOuterField(outerForm))
    yield put(updateQueryField(detail.filterRules))
  } catch (error) {
    yield put(toastMessage({content: '获取数据失败', error}))
  }
}

// export function *checkPermission(payload){
//   const { id } = payload;
//   let resp = yield api.permission.checkPool(id).then(api.onRequestSuccess)
//   if (resp && resp.rediectUrl) { //error case
//     window.location = resp.rediectUrl
//   } else {
//     yield *fetchPoolDetail({payload})
//   }
// }

export function *rootSaga({payload}){
  // yield *checkPermission(payload)
  yield *fetchPoolDetail({payload})
}

function walk(nodes, cb) {
  if (!nodes) return
  for (let node of nodes) {
    cb(node)
    walk(node.children, cb)
  }
}

function casCadeOptionToLabelMap(options) {
  let result = {}
  walk(options, (node) => {
    result[node.value] = node.label
  })
  return result
}

export function* initQueryMeta({ payload }) {
  const storeMainCascadeOptions = yield api.getStoreMainCatetories().then(api.onRequestSuccess)
  const categoryCascadeOptions = yield api.ali.getCategorySku().then(api.onRequestSuccess)
  const cityCascadeOptions = yield api.getCities().then(api.onRequestSuccess)
  const storeCascadeOptions = yield api.ali.getCategoryStore().then(api.onRequestSuccess)
  const newStoreMainCascadeOptions = yield api.ali.getNewAllStoreMainCategory().then(api.onRequestSuccess)
  const marketingTypeOptions = yield api.queryMarketingType().then(api.onRequestSuccess)
  const storeActivitiesTypeOptions = yield api.ali.queryMarketingType({id:payload}).then(api.onRequestSuccess)
  const newMarketingTypeOptions = yield api.queryNewMarketingType().then(api.onRequestSuccess)
  const crowdAttributes = yield api.getCrowdAttributes().then(api.onRequestSuccess)
  const itemStatusOptions = yield api.ali.getItemStatusOptions().then(api.onRequestSuccess)
  let filtering = false
  let meta = {
    sku: createCommodityMeta(
      { basePoolId: payload, filtering }, {
        marketingTypeOptions,
        newMarketingTypeOptions,
        categoryCascadeOptions,
        storeCascadeOptions,
        newStoreMainCascadeOptions,
        cityCascadeOptions,
        storeMainCascadeOptions,
        itemStatusOptions
      }
    ),
    store: createStoreMeta({ basePoolId: payload, filtering }, {
      cityCascadeOptions,
      storeCascadeOptions,
      storeMainCascadeOptions,
      newStoreMainCascadeOptions,
      crowdAttributes,
      storeActivitiesTypeOptions
    }),
  }
  let ds = collectDataSources(meta.sku).concat(collectDataSources(meta.store));
  let ot = ds.reduce((p, c) => {
    const { fieldKey, dataSource } = c;
    p[fieldKey] = {
      valueMap: casCadeOptionToLabelMap(dataSource)
    }
    return p
  }, {})

  let labelMap = {
    sku: collectLabelMap(meta.sku),
    store: collectLabelMap(meta.store),
    others: {
      ...ot,
      createTime: {
        format: (value) => {
          if (!value) return ''
          return formatTimeStamp(value)
        }
      }
    }
  }
  yield put(actions.setQueryMeta({
    meta, labelMap
  }))
}

/**
 * Root saga manages watcher lifecycle
 */
export function* saga() {
  yield takeLatest(FETCH_EDIT_DETAIL, rootSaga);
  yield takeLatest(INIT_QUERY_META, initQueryMeta);
}
