import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import injectSaga from '@/utils/injectSaga';
import { saga } from './saga';
import { actions, selectors, constants, reducer } from '../common';
import { makeSelectQueryMeta } from '@/containers/App/selectors';
import injectReducer from '@/utils/injectReducer';

const mapDispatchToProps = (dispatch) => ({
  initQueryMeta: (formData) => dispatch(actions.initQueryMeta(formData)),
  onUpdateQueryForm: (formData) => dispatch(actions.updateQueryField(formData)),
  onUpdateOuterForm: formData=> dispatch(actions.updateOuterField(formData)),
  resetQueryForm: payload=> dispatch(actions.resetQueryField(payload)),
  fetchPoolDetail: (payload) => dispatch(actions.fetchPoolDetail(payload)),
});

const mapStateToProps = createStructuredSelector({
  queryForm: selectors.makeQueryFieldSelector(),
  initQueryForm: selectors.makeInitQueryField(),
  outerForm: selectors.makeOuterQueryForm(),
  basePoolId: selectors.makeBasePoolIdSelector(),
  queryMeta: makeSelectQueryMeta()
});

const withConnect = connect(mapStateToProps, mapDispatchToProps);
const withSaga = injectSaga({ key: 'query-pool', saga });
const withReducer = injectReducer({ key: constants.REDUCER_KEY, reducer: reducer.rootReducer });

export const connectRedux = compose(withSaga, withReducer, withConnect)
