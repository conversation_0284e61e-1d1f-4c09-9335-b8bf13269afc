import React from 'react';
import {Input, NumberPicker, Checkbox, Radio, Select, Form} from '@alife/next';
import { pick, cloneDeep,isArray } from 'lodash';
import moment from 'moment';
import { TimeRangePicker } from '@/components/TimeRangePicker';
import { CascaderSelectWithClear } from '@/components/CascaderSelect';
import {isString,isAllNumber} from "@/utils/validators";
import {getValueByUrlKey} from '@/utils/others';

const FormItem = Form.Item;

export class DateRangePicker extends React.PureComponent {
  onChange = (value)=>{
    let changed = {
      start: value[0] ? value[0].valueOf() : '',
      end: value[1] ? value[1].valueOf() : ''
    }
    this.props.onChange(changed)
  }

  render(){
    const {value} = this.props;
    let lvalue = value ? [value.start? moment(value.start): null, value.end ? moment(value.end): null] : []
    return <TimeRangePicker {...this.props} showTime={true} value={lvalue} onChange={this.onChange}></TimeRangePicker>
  }
}


export class LevelCascade extends React.PureComponent {
  constructor(props){
    super(props)
    this.state = {
      dataSource: []
    }
  }

  onChange = (value, data)=>{
    if(!value) return;
    let result
    if (Array.isArray(data)){
      result = data.map(mapF)
    } else {
      result = mapF(data)
    }
    this.props.onChange(result)

    function mapF(_data){
      return {
        value: +_data.value,
        level: _data.level
      }
    }
  }

  mapV(v){
    if (!v) return v
    if (Array.isArray(v)){
      return v.map(e => +e.value)
    } else {
      return +v.value
    }
  }

  static getDerivedStateFromProps(props, state){
    if (props.dataSource !== state.dataSource && props.dataSource){
      addLevel(props.dataSource)
      return {
        dataSource: props.dataSource
      }
    }
    return null

    function addLevel(dataSource){
      if (!Array.isArray(dataSource)) return
      return f(dataSource, 1)

      function f(arr, lvl = 1){
        arr.forEach(node=> {
          node.level = lvl
          if (Array.isArray(node.children)){
            f(node.children, lvl+1)
          }
        })
      }
    }
  }

  render(){
    let lvalue = this.mapV(this.props.value)
    const {dataSource} = this.state;
    return <CascaderSelectWithClear {...this.props} dataSource={cloneDeep(dataSource)} value={lvalue} onChange={this.onChange}></CascaderSelectWithClear>
  }
}

export class GroupInputAndSelect extends React.PureComponent{
  constructor(props){
    super(props);
    this.state = {
      dataSource: props.dataSource,
      mode: props.value ? (props.dataSource ? props.dataSource.filter(v => v.value == props.value.activityIdsTypes)[0].mode : '') : 'input',
      value: props.value
    }
  }

  onChange = (v,actionType,item)=>{
    let a = v ? v : null;
    let {value} = this.state;
    value.activityIdsTypes = a;
    value.activityIdsTypesValue = [];
    this.props.onChange(value);
    this.setState({
      mode: item.mode,
    })
  }

  valueChange = (v) =>{
    let {value} = this.state;
    value.activityIdsTypesValue = v ? (isString(v)) ? v.split(',') : v : [];
    this.props.onChange(value);
    this.setState({value});
  }

  componentWillReceiveProps(nextProps) {
    let {value} = this.state;
    value.activityIdsTypesValue = (nextProps.value ? nextProps.value.activityIdsTypesValue : []);
    this.setState({value});
  }


  render() {
    const {mode, value} = this.state;
    const {dataSource, required,validator} = this.props;
    let activityIdsTypes = (value ? value.activityIdsTypes : '');
    let activityIdsTypesValue = (value ? value.activityIdsTypesValue : []);
    const ds = (dataSource && dataSource.length > 0) ? dataSource.filter(v => v.mode == 'select')[0].ds : [];
    const activityIdsTypesLabel = (dataSource && dataSource.length > 0) ?  dataSource.filter(v => v.value == activityIdsTypes)[0].label:'';
    const disabled = isAllNumber(location.hash.split("?")[0].split("/").slice(-1)[0]).length == 0 || getValueByUrlKey("isCopy");
    return <div className="group-activities-types">
      <FormItem required={required} requiredMessage={`请输入${activityIdsTypesLabel}`} validator={validator} validatorTrigger="onBlur">
      {required && <span className="red-mark">*</span>}
      <Select  {...this.props} style={{width:'130px'}} disabled={disabled} value={activityIdsTypes} onChange={(_value, actionType, item) => this.onChange(_value, actionType, item)} />
        {mode == 'input' ?
          <Input name="activityIdsTypesValue" value={activityIdsTypesValue} onChange={this.valueChange} placeholder="英文逗号隔开，最多100个" /> :
          <Select name="activityIdsTypesValue" style={{width:'240px'}} mode='multiple' value={activityIdsTypesValue} dataSource={ds} onChange={this.valueChange}/>}
      </FormItem>
    </div>
  }
}

export class CommaSeperatedInput extends React.PureComponent {
  onChange = (value)=>{
    let a = value ? value.split(',') : null;
    this.props.onChange(a)
  }

  render(){
    const {value} = this.props;
    let lvalue = (value || []).join(',')
    return <Input {...this.props} value={lvalue} onChange={this.onChange}></Input>
  }
}

export class RangeNumberInput extends React.Component {
  static defaultProps = {
    startProps: {},
    endProps: {},
    unitNode: null,
    after:''
  }

  constructor(props){
    super(props)
    this.state = {
      lvalue: undefined,
      rvalue: undefined
    }
  }

  static getDerivedStateFromProps(props, state){
    const {value} = props
    let { lvalue, rvalue } = state;

    if (value){
      if (value.start != lvalue){
        lvalue = value.start
      }
      if (value.end != rvalue){
        rvalue = value.end
      }
      return {
        lvalue, rvalue,
      }
    } else {
      return {lvalue: undefined, rvalue: undefined}
    }
  }

  onStartChange = (value)=>{
    const changed = {
      start: value,
      end: this.state.rvalue
    }
    this.props.onChange(changed)
  }

  onEndChange = (value)=>{
    const changed = {
      start: this.state.lvalue,
      end: value,
    }
    this.props.onChange(changed)
  }

  render() {
    const {id, unitNode, min, max, className, after, precision, step, ...rest} = this.props;
    const {lvalue, rvalue} = this.state;
    let rsp = pick(rest, ['precision', 'step'])

    return (
      <div id={id} className={`range-number-input ${className||''}`} style={{'whiteSpace': 'nowrap'}}>
        <NumberPicker {...rsp} min={min} max={rvalue || max} precision={precision} step={step} onChange={this.onStartChange} value={lvalue}/>-
        <NumberPicker {...rsp} min={lvalue} max={max} precision={precision} step={step} onChange={this.onEndChange} value={rvalue}/>{unitNode}
        {after}
      </div>
    )
  }
}


