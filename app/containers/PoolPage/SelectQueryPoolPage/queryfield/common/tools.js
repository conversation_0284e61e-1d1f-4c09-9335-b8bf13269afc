import { Input, Select, NumberPicker, DatePicker, Radio, Checkbox } from '@alife/next';
import { pickBy } from 'lodash';
import {  PLACEHODLERS, basepoolIds } from '@/constants';
import * as validators from '@/utils/validators';
import { DateRangePicker, CommaSeperatedInput, LevelCascade, RangeNumberInput, GroupInputAndSelect} from './components';
import { CascaderSelectWithClear } from '@/components/CascaderSelect';
import { poolFiltersConfig } from '../filtersConfig'

export { validators, PLACEHODLERS }

export function multiSelect({keys, dataSource, single, hasClear, defaultValue}) {
  let obj = {
    mode: single ? 'single' : 'multiple',
    hasClear: !!hasClear,
    dataSource: dataSource || createEnumDataSource(keys),
  };
  if(defaultValue) obj.defaultValue = defaultValue;
  return createSelect(obj);
}

export function singleSelect({ keys }) {
  let ds = createEnumDataSource(keys).map(e => {
    if (e.label == '全部' || e.label == '不限') {
      e.value = ''
    }
    return e
  })

  return createSelect({
    dataSource: ds
  })
}


export function createRadioGroup({dataSource, defaultValue = ''}){
  if (typeof dataSource === 'string'){
    dataSource = createEnumDataSource(dataSource)
  }

  return {
    component: Radio.Group,
    props: {
      dataSource,
      defaultValue
    }
  }
}

export function createCheckboxGroup({dataSource}) {
  if (typeof dataSource === 'string') {
    dataSource = createEnumDataSource(dataSource)
  }

  return {
    component: Checkbox.Group,
    props: {
      dataSource
    }
  }
}

export function createSelect({ dataSource, mode, hasClear, defaultValue }) {
  return {
    component: Select,
    props: {
      dataSource,
      mode,
      hasClear,
      defaultValue
    }
  }
}

export function createInput(props) {
  return {
    component: Input,
    props: {
      ...props
    }
  }
}

export function createCommaArrayInput({placeholder = PLACEHODLERS.commaSeperated} = {}){
  return {
    component: CommaSeperatedInput,
    props: {
      placeholder
    }
  }
}

// export function createNumberRangeInput(props) {
//   let { min, max, precision, step } = props || {};
//   return {
//     component: RangeNumberInput,
//     props: {
//       ...props,
//       min: min || 0,
//       max: max || Infinity,
//       precision: precision || 1,
//       step: step || 1
//     }
//   }
// }

export function createNumberRangeInput({ min = 0, max = Infinity, precision = 1, step = 1, after = '', unitNode = '' } = {}) {
  return {
    component: RangeNumberInput,
    props: {
      min,
      max,
      precision,
      step,
      after,
      unitNode
    }
  }
}

// export function createNumberRangeInput(props) {
//   let { min, max, precision, step } = props || {};
//   return {
//     component: RangeNumberInput,
//     props: {
//       ...props,
//       min: min || 0,
//       max: max || Infinity,
//       precision: precision || 1,
//       step: step || 1
//     }
//   }
// }

export function createEnumDataSource(keys) {
  if (typeof keys === 'string') keys = keys.split(/[、,，]/)
  return keys.map(e => ({ label: e, value: e }))
}


export function createNumberPicker({ min = 0, max = 1, step = 1, precision } = {}) {
  return {
    component: NumberPicker,
    props: {
      min,
      step,
      precision,
      max,
    }
  }
}

export function createDatepicker(){
  return {
    component: DatePicker,
    props: {

    }
  }
}

export function createDateRangePicker(){
  return {
    component: DateRangePicker, // customized DateRangePicker
    props: {
      // can not be deleted
    }
  }
}

function createBaseCascader({component, props}){
  return {
    component,
    props: {
      showSearch: true,
      multiple: true,
      expandTriggerType: 'hover',
      hasClear: true,
      useVirtual: true,
      ...props,
    }
  }
}

export function createCascaderSelect({dataSource,disabled=false,dataSourceName=''}){
  return createBaseCascader({
    component: CascaderSelectWithClear,
    props: {
      dataSource,
      disabled,
      dataSourceName
    }
  })
}

export function createLevelCascadeSelect({dataSource}){
  return createBaseCascader({
    component: LevelCascade,
    props: {
      dataSource,
    }
  })
}

export function createGroupInputAndSelect({validator,dataSource,required,defaultValue}){
  return {
    component: GroupInputAndSelect,
    props: {
      validator,
      dataSource,
      required,
      defaultValue
    }
  }
}

export function formItemProps(meta) {
  let { label, required, validator } = meta;
  label = typeof label === 'string' ? label.trim().toLocaleUpperCase() : label;
  label = typeof label === 'object' ? label.props.children : label;
  let a = {
    ...meta,
    label,
    ...(required ? {
      requiredMessage: `请输入${label}`,
      requiredTrigger: 'onBlur'
    } : null),
    ...(validator ? {
      validatorTrigger: ['onBlur']
    } : null)
  };
  // console.log(a);
  return a;
}

export function processMeta(meta, {basePoolId}, type){
  let poolConfig = poolFiltersConfig[basePoolId] || {};
  let filterConfig = poolConfig[type] || [];
  if(filterConfig.length == 0) return meta;
  meta.fields = pickBy(meta.fields, (f, key)=>{
    return filterConfig.indexOf(key) !== -1
  })
  return meta
}

export const renderPlaceHolder = (type, label = '', max = 100) => {
  const obj = {
    commaSeperated: `英文逗号隔开，最多${max}个`,
    requireInput: `请输入${label}, 必填`,
    requireSelection: `请选择${label}`
  }
  return obj[type]
}
