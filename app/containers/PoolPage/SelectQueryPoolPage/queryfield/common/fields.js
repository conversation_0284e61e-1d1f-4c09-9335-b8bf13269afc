import { formItemProps, multiSelect, createNumberRangeInput } from './tools';

export const bussinessRegion = {
  props: {
    ...formItemProps({
      label: '业务区域',
    })
  },
  children: {
    ...multiSelect({ keys: '华东战区、华南战区、华北战区、中西战区' })
  }
}

export const skuNum = {
  props: {
    ...formItemProps({
      label: 'SKU数量',
    })
  },
  children: {
    ...createNumberRangeInput({ precision: 0 })
  }
}

export const bussinessLine = {
  props: {
    ...formItemProps({
      label: '业务线',
    })
  },
  children: {
    ...multiSelect({ keys: '快消业务部、零售KA管理部、散店业务部、生鲜菜场业务部、小连锁、医药健康业务部' })
  }
}

export const newBuFlags = {
  props: {
    ...formItemProps({
      label: '新业务线',
    })
  },
  children: {
    ...multiSelect({ keys: '快消业务部、泛零售业务部、散店业务部、水果业务部、大健康业务部、生鲜菜场业务部、其他餐饮' })
  }
}


let serviceTagDS = [
  // {
  //   label: '健康门店',
  //   value: 'HEALTHY_S_TAG'
  // },
  {
    label: '新店',
    value: 'NEW_SHOP_S_TAG'
  },
  {
    label: '品牌',
    value: 'BRAND_S_TAG'
  },
  {
    label: '预定',
    value: 'BOOK_S_TAG'
  },
  {
    label: '无忧退',
    value: 'REFUND_S_TAG'
  },
  {
    label: '开发票',
    value: 'RECEIPT_S_TAG'
  },
  {
    label: '坏品包退',
    value: 'BADPROD_S_TAG'
  },
  {
    label: '极速退款',
    value: 'QREFUND_S_TAG'
  },
  {
    label: '融化必赔',
    value: 'MELTMP_S_TAG'
  },
  {
    label: '冬季保温',
    value: 'WARM_S_TAG'
  },
  // {
  //   label: '慢必赔',
  //   value: 'SLOW_S_TAG'
  // },
]


export const newServiceTag = {
  props: {
    ...formItemProps({
      label: '服务标',
    })
  },
  children: {
    ...multiSelect({dataSource: serviceTagDS})
  }
}

export const storeScore = {
  props: {
    ...formItemProps({
      label: '店铺评分',
    })
  },
  children: {
    ...createNumberRangeInput()
  }
}

export const initialDeliveryPrice = {
  props: {
    ...formItemProps({
      label: '起送价',
    })
  },
  children: {
    ...createNumberRangeInput({ unitNode: '元' })
  }
}

export const nearly7DaysBusinessHours = {
  props: {
    ...formItemProps({
      label: '近7天营业时长',
    })
  },
  children: {
    ...createNumberRangeInput({ max: 24 * 7, unitNode: '小时' })
  }
}
