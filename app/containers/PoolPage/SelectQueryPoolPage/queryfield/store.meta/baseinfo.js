import React from 'react'
import { Balloon } from '@alife/next';

import { createCascaderSelect, validators, formItemProps, createDateRangePicker, renderPlaceHolder, createLevelCascadeSelect, createCommaArrayInput, createInput, createNumberRangeInput, processMeta, createRadioGroup, multiSelect } from '../common/tools';
import { nearly7DaysBusinessHours, skuNum, newServiceTag, storeScore, bussinessLine, newBuFlags, bussinessRegion, initialDeliveryPrice } from '../common/fields';
import {alipayRelIdSourceStore, shopComprehensiveScoreDs, storeAttributesSourceStore} from '../../common';

export const ShopScoreTip = function () {
  return (
    <Balloon.Tooltip trigger={<span className="help">?</span>} >
      通过计算商户门店运营11个指标得出的衡量商户品质的综合评分，分值越高，该商户的下单转化及用户留存表现越好。范围0-100。
    </Balloon.Tooltip>
  )
}

export const BDTip = function () {
  return (
    <Balloon.Tooltip trigger={<span className="help">?</span>} >
      销售组织架构的签约人，仅限于BD
    </Balloon.Tooltip>
  )
}

export const GroupTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    若已有标签群组不能满足需求，可在“数据洞察-标签管理”中重新创建群组
  </Balloon.Tooltip>
)

export const IsRiskCompTip = function () {
  return (
    <Balloon.Tooltip trigger={<span className="help">?</span>} >
      普通等级是针对一般活动限制风险商家15-30天不允许参与活动，限制的商家较少，捞出的商家较多，严格等级是针对特殊活动限制风险商家60天不允许参与活动，限制的商家较多，捞出的商家较少
    </Balloon.Tooltip>
  )
}

export const StoreAttributesTip = function () {
  return (
    <Balloon.Tooltip trigger={<span className="help">?</span>} >
      多选条件，选项间取交集筛选
    </Balloon.Tooltip>
  )
}

export function baseInfo({
  storeMainCascadeOptions,
  newStoreMainCascadeOptions,
  storeCascadeOptions,
  cityCascadeOptions,
  basePoolId,
  filtering,
  manageorgListOptions,
  bizLineListOptions,
}) {
  let isDirectDataSource = { dataSource: [{ label: '不限', value: -1 }, { label: '否', value: 0 }, { label: '是', value: 1 }], defaultValue: -1 };
  if (basePoolId && basePoolId !== '30005' && basePoolId !== '30006') {
    isDirectDataSource = { dataSource: [{ label: '否', value: 0 }, { label: '是', value: 1 }] };
  }
  let isDirect = {
    props: {
      labelCol: { fixedSpan: 7, align: 'center' },
      wrapperCol: { fixedSpan: 8, align: 'center' },
      ...formItemProps({
        label: '是否直营商户',
      }),
      // required: true,
    },
    children: {
      ...createRadioGroup(isDirectDataSource)
    }
  }
  if (basePoolId && basePoolId !== '30005' && basePoolId !== '30006') {
    isDirect.props.required = false;
  }
  let meta = {
    tabName: '基本属性',
    fields: {
      eleStoreId: {
        props: {
          ...formItemProps({
            label: 'ele门店ID	',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({ placeholder: renderPlaceHolder('commaSeperated') })
        }
      },
      activityIds: {
        props: {
          ...formItemProps({
            label: '营销活动id',
            required: true,
            validator: validators.joinArray(validators.chain([validators.idCommaSeperatedRequired, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      newRetailStoreId: {
        props: {
          ...formItemProps({
            label: '新零售门店id',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({ placeholder: renderPlaceHolder('commaSeperated') })
        }
      },
      storeId: {
        props: {
          ...formItemProps({
            label: '淘内门店ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({ placeholder: renderPlaceHolder('commaSeperated') })
        }
      },
      goodsId: { //不一定其他地方会用，目前只在品转店里面（因为是门店池，但是筛选条件是品），只为了把管理门店池列表中把筛选规则里面的商品ID显示出来
        props: {
          ...formItemProps({
            label: '商品ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
        // basePoolId: skuPool.ALL,
      },
      mainInviteIds: {
        props: {
          ...formItemProps({
            label: '主招商活动id',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      inviteIds: {
        props: {
          ...formItemProps({
            label: '子招商活动id',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      storeName: {
        props: {
          ...formItemProps({
            label: '门店名称',
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({ placeholder: renderPlaceHolder('commaSeperated') })
        }
      },
      storeCategoryId: {
        props: {
          ...formItemProps({
            label: '门店类目',
            required: basePoolId == "10004",
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: storeCascadeOptions })
        }
      },
      newMainCategoryId: {
        props: {
          ...formItemProps({
            label: '门店主营类目(新)',
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: newStoreMainCascadeOptions })
        }
      },
      supplierId: {
        props: {
          ...formItemProps({
            label: '供应商ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({ placeholder: renderPlaceHolder('commaSeperated') })
        }
      },
      supplierIds: { // supplierId 为老字段，现在已不用，现在只有招商这俩池子再用新的. 老字段先留存，兼容
        props: {
          ...formItemProps({
            label: '供应商ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      sellerId: {
        props: {
          ...formItemProps({
            label: '淘系卖家ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({ placeholder: renderPlaceHolder('commaSeperated') })
        }
      },
      brandName: {
        props: {
          ...formItemProps({
            label: '所属品牌',
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({ placeholder: renderPlaceHolder('commaSeperated') }),
        }
      },
      mainCategoryId: {
        props: {
          ...formItemProps({
            label: '门店主营类目(旧)',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: storeMainCascadeOptions, disabled: true})
        }
      },
      businessArea: {
        ...bussinessRegion
      },
      businessLine: {
        ...bussinessLine
      },
      newBuFlags: {
        ...newBuFlags
      },
      city: {
        props: {
          ...formItemProps({
            label: '所在城市',
          })
        },
        children: {
          ...createLevelCascadeSelect({ dataSource: cityCascadeOptions })
        }
      },
      storeScore: {
        ...storeScore
      },
      newServiceTag: {
        ...newServiceTag
      },
      skuNum: {
        ...skuNum
      },
      initialDeliveryPrice: {
        ...initialDeliveryPrice
      },
      nearly7DaysBusinessHours: {
        ...nearly7DaysBusinessHours
      },
      createTime: {
        props: {
          ...formItemProps({
            label: '创建时间',
          })
        },
        children: {
          ...createDateRangePicker()
        }
      },
      shopModelScore: {
        props: {
          ...formItemProps({
            label: <>商户模型评分<ShopScoreTip /></>,
          }),
        },
        children: {
          ...createNumberRangeInput({ max: 100, min: 0, precision: 0.01, step: 0.01 })
        }
      },
      signatory: {
        props: {
          ...formItemProps({
            label: <>签约人<BDTip /></>,
          }),
        },
        children: {
          ...createInput({ placeholder: '请输入姓名' })
        }
      },
      isDirect,
      businessAttribute: {
        ...bussinessLine
      },
      isCityAgent: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '是否城代商户',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '否', value: 0 }, { label: '是', value: 1 }] })
        }
      },
      deliveryTimelyRate: {
        props: {
          ...formItemProps({
            label: '出货及时率',
          }),
        },
        children: {
          ...createNumberRangeInput({ max: 100, min: 0, step: 1, unitNode: '%' })
        }
      },
      selfFetch: {
        props: {
          ...formItemProps({
            label: '店类型',
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: [{ label: '外卖店', value: '0' }, { label: '自提店', value: '1' }, { label: '外卖自提店', value: '2' }] })
        }
      },
      shopLevel: {
        props: {
          ...formItemProps({
            label: '基建分层',
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: [{ label: '未入门', value: 0 }, { label: '入门', value: 100 }, { label: '健康', value: 200 }, { label: '完美', value: 300 }, { label: '超级', value: 500 }] })
        }
      },
      wholeCityDelivery: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '是否当日/半日/次日达（全城淘）',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '否', value: 0 }, { label: '是', value: 1 }] })
        }
      },
      isRiskComp: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 13, align: 'center' },
          ...formItemProps({
            label: <>风控合规数据<IsRiskCompTip /></>,
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '普通等级', value: 1 }, { label: '严格等级', value: 2 }], defaultValue: 1 })
        }
      },
      alipayRelId: { //支付宝场景指标字段
        props: {
          ...formItemProps({
            label: '支付宝场景指标',
          })
        },
        children: {
          ...multiSelect({ dataSource: alipayRelIdSourceStore, single: true, hasClear: true })
        },
      },
      storeAttributes: { //店铺标示字段
        props: {
          ...formItemProps({
            label: <>店铺标示<StoreAttributesTip /></>,
          })
        },
        children: {
          ...multiSelect({ dataSource: storeAttributesSourceStore })
        },
      },
      isOpenCommissionFee: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '当天是否开启CPS',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '否', value: 0 }, { label: '是', value: 1 }] })
        }
      },
      commissionRate1d: {
        props: {
          ...formItemProps({
            label: 'CPS出佣比例',
          }),
        },
        children: {
          ...createNumberRangeInput({ max: 100, min: 0, step: 1, precision: 0, unitNode: '%' })
        }
      },
      labelGroupIds: {
        props: {
          ...formItemProps({
            label: <>标签群组<GroupTip/></>,
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.optional(validators.createMaxCommaItem(100))])),
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated', '标签群组')
          })
        }
      },
      isPicStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '图片质量是否合格',
          }),
          defaultValue: '',
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 'Y_PIC'}]})
        }
      },
      isPriceStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '价格是否合格',
          }),
          defaultValue: '',
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 'Y_PRICE'}]})
        }
      },
      isCatCompStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '类目是否正确',
          }),
          defaultValue: '',
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '正确', value: 'Y_CAT'}]})
        }
      },
      isUpcNameStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '品名是否合格',
          }),
          defaultValue: '',
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 'Y_UPCNAME'}]})
        }
      },
      shopComprehensiveScore: { //店铺综合分
        props: {
          ...formItemProps({
            label: '商户成长分',
          })
        },
        children: {
          ...multiSelect({dataSource: shopComprehensiveScoreDs})
        }
      },
      lastMonthShopComprehensiveScore:{ //上月店铺综合分
        props: {
          ...formItemProps({
            label: '上月店铺综合分',
          })
        },
        children: {
          ...multiSelect({dataSource: shopComprehensiveScoreDs})
        }
      },
      isSelfDelivery: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '是否自配送',
          }),
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '否', value: '0'}, {label: '是', value: '1'}]})
        }
      },
      serviceFeature: {
        props: {
          ...formItemProps({
            label: '业务属性',
          })
        },
        children: {
          ...multiSelect({
            dataSource: [
              {
                label: '全国KA',
                value: 'SF_9'
              },
              {
                label: '城市龙头',
                value: 'SF_10'
              },
              {
                label: '其他连锁',
                value: 'SF_11'
              },
              {
                label: '散店',
                value: 'SF_12'
              }
            ]
          })
        }
      },
      servicePackage: {
        props: {
          ...formItemProps({
            label: '服务包',
          })
        },
        children: {
          ...multiSelect({
            dataSource: [{  //todo 枚举需要换
              label: '新店',
              value: 'NEW_SHOP_S_TAG'
            }]
          })
        }
      },
      storeLayer: {
        props: {
          ...formItemProps({
            label: '商户分层',
          })
        },
        children: {
          ...createCascaderSelect({
            dataSource: [{
              label: 'KA',
              value: 'KAA'
            },{
              label: 'SMB',
              value: 'SMB'
            },{ 
              value: "CSBLFC_SMB",
              label: "超市便利_SMB"
            }, { 
              value: "CSBLFC_CKA",
              label: "超市便利_CKA"
            }, { 
              value: "CSBLFC_NKA",
              label: "超市便利_NKA"
            }, { 
              value: "CSBLFC_TT",
              label: "超市便利_TT"
            }, {
              value: "CSBLFC_LKA",
              label: "超市便利_LKA"
            }]
          })
        }
      },
      shopMall: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商家是否入驻全能商厦',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 2214674071299 }] })
        }
      },
      shopMall3: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商家是否入驻全能超市3.0',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'EMAlL3' }] })
        }
      },
      wareHouse: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '是否前置仓',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '否', value: "NOT_WARE_HOUSE" }, { label: '是', value: 'WARE_HOUSE' }] })
        }
      },
      tradeStoreLayer: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '行业商户分层',
          }),
        },
        children: {
          ...multiSelect({
            dataSource: [{ label: 'KA直营', value: "KA_DIRECT" }, { label: 'KA代理', value: "KA_NDIRECT" }, { label: 'SMB直营连锁', value: 'SMB_DIRECT_NOTSF12' }, { label: 'SMB直营散店', value: "SMB_DIRECT_SF12" }, { label: 'SMB代理连锁', value: "SMB_NOTDIRECT_NOTSF12" }, { label: 'SMB代理散店', value: 'SMB_NOTDIRECT_SF12' }],
            hasClear: true,
            defaultValue: ""
          })
        }
      },
      hyCategoryNameList: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '行业',
          }),
        },
        
        children: {
          ...multiSelect({
            dataSource: [{ label: '仓店', value: "CSBLYT_仓店" }, { label: '代购店', value: "CSBLYT_代购店" }, { label: '便利店', value: 'CSBLYT_便利店' }, { label: '奶站', value: "CSBLYT_奶站" }, { label: '快递店', value: "CSBLYT_快递店" }, { label: '校园店', value: 'CSBLYT_校园店' }, { label: '水站', value: 'CSBLYT_水站' }, { label: '电商店', value: 'CSBLYT_电商店' }, { label: '茶行', value: 'CSBLYT_茶行' }, { label: '超市', value: 'CSBLYT_超市' }, { label: '食杂店', value: 'CSBLYT_食杂店' }, { label: '饮料冰品', value: 'CSBLYT_饮料冰品' }],
            hasClear: true,
            defaultValue: ""
          })
        }
      },
      bizLineLv3Code: {
        props: {
          ...formItemProps({
            label: '业务线（废弃）',
            hasClear: true,
          }),
          isHidden: true,
        },
        children: {
          ...createCascaderSelect({ dataSource: bizLineListOptions })
        }
      },
      bizLineLv3CodeV2: {
        props: {
          ...formItemProps({
            label: '业务线',
            hasClear: true,
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: bizLineListOptions })
        }
      },
      manageOrgLv2Code: {
        props: {
          ...formItemProps({
            label: '经营归属（废弃）',
            hasClear: true,
          }),
          isHidden: true,
        },
        children: {
          ...createCascaderSelect({ dataSource: manageorgListOptions })
        }
      },
      manageOrgLv2CodeV2: {
        props: {
          ...formItemProps({
            label: '经营归属',
            hasClear: true,
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: manageorgListOptions })
        }
      },
      allowShoppingMoney: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商家是否开通购物金',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'ALLOW_SHOPPING_MONEY' }] })
        }
      },
      shopSkd: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '是否快递店',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'S_KD1' }, { label: '否', value: 'S_KD0' }] })
        }
      },
      shopBrandSonpre: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '是否官旗',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'B_SONPRE' }] })
        }
      },
      itemGroupIdList: {
        props: {
          ...formItemProps({
            label: <>同品归一码</>,
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.optional(validators.createMaxCommaItem(100))])),
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated', '同品归一码')
          })
        }
      },
    }
  }

  if (basePoolId === '30005' || basePoolId === '30006') {
    delete meta.fields.isCityAgent;
  }
  if (filtering) return processMeta(meta, { basePoolId }, 'baseInfo')
  return meta
}
