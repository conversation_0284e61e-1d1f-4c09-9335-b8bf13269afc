import React from 'react'

import { formItemProps, multiSelect} from '../common/tools';

export function crowdAttributeInfo({
  basePoolId,
  filtering,
  crowdAttributes
}){
  let crowdOptions = crowdAttributes?crowdAttributes.map(item=>{
    return {
      label: item.desc,
      value: item.code,
    }
  }):[];

  let meta = {
    tabName: '人群属性',
    fields: {
      crowdAttributes: {
        props: {
          ...formItemProps({
            label: '适用人群',
          })
        },
        children: {
          ...multiSelect({dataSource: crowdOptions})
        }
      }
    }
  }

  return meta
}
