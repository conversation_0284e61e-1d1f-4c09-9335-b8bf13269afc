import React from 'react'
import { Balloon } from '@alife/next';

import { formItemProps, createNumberRangeInput} from '../common/tools';

/** tip component */
const D7BenefitTip = () => {
  return (
    <Balloon.Tooltip trigger={<span className="help">?</span>} >
      总优惠金额=总补贴金额（平台+商户+代理商+品牌+ 其他 ）
    </Balloon.Tooltip>
  )
}

const D7InvalidTip = () => {
  return (
    <Balloon.Tooltip trigger={<span className="help">?</span>} >
      无效订单率=支付成功后取消成功+退款成功/总支付成功的订单
    </Balloon.Tooltip>
  )
}

const D7ComplaintTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    客诉率= 客诉订单量/支付成功订单量
  </Balloon.Tooltip>
)

const D7RefundTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    退款时长=（退消订单/退单）最后一次操作时间-（退消订单/退单）单第一次操作时间
  </Balloon.Tooltip>
)

const D30RetentionTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    留存率=这个月同一门店下单人数/上个月下单人数
  </Balloon.Tooltip>
)

const D30ShopSubsidyTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    店铺补贴力度=30天内店铺补贴金额/毛GMV
  </Balloon.Tooltip>
)

const D30PlatformSubsidyTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    平台补贴力度=30天内平台补贴金额/毛GMV
  </Balloon.Tooltip>
)

/**固定 formItem的一些参数 */
function setFormItem(meta){
  return formItemProps({
    labelCol: { span: 11, align: 'center' },
    ...meta
  })
}

export function storeBrowseAndTradeInfo(){
  return {
    tabName: '浏览和交易信息',
    fields: {
      nearly7DaysOrders: {
        props: {
          ...setFormItem({
            label: '近7d有效订单总量',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly30DaysOrders: {
        props: {
          ...setFormItem({
            label: '近30d有效订单总量',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly7DaysTurnover: {
        props: {
          ...setFormItem({
            label: '近7d交易总额',
          })
        },
        children: {
          ...createNumberRangeInput({after:'元'})
        }
      },
      nearly30DaysTurnover: {
        props: {
          ...setFormItem({
            label: '近30d交易总额',
          })
        },
        children: {
          ...createNumberRangeInput({after:'元'})
        }
      },
      d15SaleAmt: {
        props: {
          ...setFormItem({
            label: '近15D净交易总额',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0, after:'元'})
        }
      },
      nearly7DaysBrowsePV: {
        props: {
          ...setFormItem({
            label: '近7d浏览总PV',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly7DaysBrowseUV: {
        props: {
          ...setFormItem({
            label: '近7d浏览总UV',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly15DaysBrowsePV: {
        props: {
          ...setFormItem({
            label: '近15d浏览总PV',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly15DaysBrowseUV: {
        props: {
          ...setFormItem({
            label: '近15d浏览总UV',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly7DaysOneCustomerPrice: {
        props: {
          ...setFormItem({
            label: '近7d客单价',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, after:'元'})
        }
      },
      nearly15DaysOneCustomerPrice: {
        props: {
          ...setFormItem({
            label: '近15d客单价',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, after:'元'})
        }
      },
      nearly7DaysOrderConversionRate: {
        props: {
          ...setFormItem({
            label: '近7d下单转化率',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, after:'%'})
        }
      },
      nearly15DaysOrderConversionRate: {
        props: {
          ...setFormItem({
            label: '近15D下单转化率',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, after:'%'})
        }
      },
      d7BenefitTotalAmt: {
        props: {
          ...setFormItem({
            label: <>近7D总优惠金额<D7BenefitTip/></>,
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, step: 0.1, after:'元'})
        }
      },
      d30BenefitTotalAmt: {
        props: {
          ...setFormItem({
            label: '近30d总优惠金额',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, step: 0.1, after:'元'})
        }
      },
      // d7InvalidOrderRate: {
      //   props: {
      //     ...setFormItem({
      //       label: <>近7D无效订单率<D7InvalidTip/></>,
      //     })
      //   },
      //   children: {
      //     ...createNumberRangeInput({precision: 1, step: 0.1, after:'%'})
      //   }
      // },
      // d30InvalidOrderRate: {
      //   props: {
      //     ...setFormItem({
      //       label: '近30d无效订单率',
      //     })
      //   },
      //   children: {
      //     ...createNumberRangeInput({precision: 1, step: 0.1, after:'%'})
      //   }
      // },
      d7ComplaintRate: {
        props: {
          ...setFormItem({
            label: <>近7D客诉率<D7ComplaintTip/></>
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, step: 0.1, after:'%'})
        }
      },
      d30ComplaintRate: {
        props: {
          ...setFormItem({
            label: '近30D客诉率',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, step: 0.1, after:'%'})
        }
      },
      d7RefundDur: {
        props: {
          ...setFormItem({
            label: <>近7D平均退款时长<D7RefundTip/></>,
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0, step: 1, after:'分'})
        }
      },
      d30Retention: {
        props: {
          ...setFormItem({
            label: <>近30D留存率<D30RetentionTip/></>,
          })
        },
        children: {
          ...createNumberRangeInput({precision: 1, step: 0.1, after:'%'})
        }
      },
      d30ShopSubsidy: {
        props: {
          ...setFormItem({
            label: <>近30D店铺补贴力度<D30ShopSubsidyTip/></>,
          })
        },
        children: {
          ...createNumberRangeInput({precision: 2, step: 0.01, after:'%'})
        }
      },
      d30PlatformSubsidy: {
        props: {
          ...setFormItem({
            label: <>近30D平台补贴力度<D30PlatformSubsidyTip/></>,
          })
        },
        children: {
          ...createNumberRangeInput({precision: 2, step: 0.01, after:'%'})
        }
      }
    }
  }
}
