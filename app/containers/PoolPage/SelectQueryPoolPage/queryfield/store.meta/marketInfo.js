import React from 'react';
import { Balloon } from '@alife/next';
import {formItemProps, createNumberRangeInput, createRadioGroup, processMeta, multiSelect, createCascaderSelect} from '../common/tools';
import {shopPZLMStringSourceStore} from "../../common";


export function marketInfo({ basePoolId, filtering, storeActivitiesTypeOptions }) {
  let meta = {
    tabName: '营销信息',
    fields: {
      storeMarketRate: {
        props: {
          ...formItemProps({
            label: <>商户自营销率<Balloon.Tooltip trigger={<span className="help">?</span>} >商户自营销率=商户总补贴/GMV*100%</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      newCustomerActivity: {
        props: {
          ...formItemProps({
            label: '是否含新客活动',
          })
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '是', value: 1}, {label: '否', value: 0}]})
        }
      },
      superActivityAmountFunds: {
        props: {
          ...formItemProps({
            label: '超会商家出资金额	',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      firstGearFullReduceThreshold: {
        props: {
          ...formItemProps({
            label: '第一档满减门槛	',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      firstGearFullReduceDiscountPrice: {
        props: {
          ...formItemProps({
            label: '第一档满减优惠金额	',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      businessVoucherThreshold: {
        props: {
          ...formItemProps({
            label: '商家代金券门槛	',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      businessVoucherDiscountPrice: {
        props: {
          ...formItemProps({
            label: '商家代金券优惠金额	',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      businessNewCustomerThreshold: {
        props: {
          ...formItemProps({
            label: '商家新客券门槛	',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      businessNewCustomerDiscountPrice: {
        props: {
          ...formItemProps({
            label: '商家新客券优惠金额',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      inviteStoreCouponActivity: {
        props: {
          ...formItemProps({
            label: '是否店铺代金券活动',
          })
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '是', value: 1}, {label: '否', value: 0}]})
        }
      },
      inviteActivityThreshold: {
        props: {
          ...formItemProps({
            label: '门槛金额',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      inviteStoreMjPrice: { //入淘招商门店池使用的满减门槛金额
        props: {
          ...formItemProps({
            label: '门槛金额',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      inviteActivityDiscountPrice: {
        props: {
          ...formItemProps({
            label: '优惠金额',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      inviteStoreCouponAmt: { //入淘招商门店池使用的招商活动优惠金额
        props: {
          ...formItemProps({
            label: '优惠金额',
          })
        },
        children: {
          ...createNumberRangeInput()
        }
      },
      shopPZLMString:{ //店铺品质联盟
        props: {
          ...formItemProps({
            label: '店铺品质联盟',
          })
        },
        children: {
          ...multiSelect({ dataSource: shopPZLMStringSourceStore, single: true, hasClear: true })
        },
      },
      storeActivitiesType: {
        props: {
          ...formItemProps({
            label: '门店活动类型',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: storeActivitiesTypeOptions})
        },
      },
    }
  }

  if (filtering) return processMeta(meta, {basePoolId}, 'marketInfo')
  return meta
}
