
import React from 'react'
import { Balloon } from '@alife/next';
import { formItemProps, createNumberRangeInput, createRadioGroup, processMeta, createCascaderSelect } from '../common/tools';

export function skuSceneInfo({ basePoolId, skuSceneInfoOptions, filtering }) {
  let fields = {};
  skuSceneInfoOptions.forEach(item=>{
    let { sceneList, sceneTypeName, sceneTypeKey } = item;
    fields[sceneTypeKey] = {
      props: {
        ...formItemProps({
          label: sceneTypeName,
        })
      },
      children: {
        ...createCascaderSelect({dataSource: sceneList})
      }
    }
  })
  let meta = {
    tabName: '场景属性',
    fields
  }
  if (filtering) return processMeta(meta, {basePoolId}, 'skuSceneInfo')
  return meta
}
