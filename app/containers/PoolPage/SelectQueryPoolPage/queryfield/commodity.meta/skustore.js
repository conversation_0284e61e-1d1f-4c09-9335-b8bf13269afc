import React from 'react'
import { Balloon } from '@alife/next';

import {
  createNumberRangeInput,
  formItemProps,
  validators,
  createCascaderSelect,
  renderPlaceHolder,
  createCommaArrayInput,
  createLevelCascadeSelect,
  createInput,
  processMeta,
  multiSelect
} from '../common/tools';
import { bussinessRegion, bussinessLine, newBuFlags, newServiceTag, storeScore, initialDeliveryPrice } from '../common/fields';
import { BDTip,ShopScoreTip,StoreAttributesTip } from '../store.meta/baseinfo';
import { createRadioGroup } from '../common/tools';
import {storeAttributesSourceStore,shopComprehensiveScoreDs} from "../../common";



export function skuStoreInfo({
  basePoolId,
  storeCascadeOptions,
  cityCascadeOptions,
  storeMainCascadeOptions,
  newStoreMainCascadeOptions,
  filtering,
  manageorgListOptions,
  bizLineListOptions,
}) {
  let isDirectDataSource = { dataSource: [{ label: '不限', value: -1 }, { label: '否', value: 0 }, { label: '是', value: 1 }], defaultValue: -1 };
  if (basePoolId && basePoolId !== '30005' && basePoolId !== '30006') {
    isDirectDataSource = { dataSource: [{ label: '否', value: 0 }, { label: '是', value: 1 }] };
  }
  let isDirect = {
    props: {
      labelCol: { fixedSpan: 7, align: 'center' },
      wrapperCol: { fixedSpan: 8, align: 'center' },
      ...formItemProps({
        label: '是否直营商户',
      }),
      // required: true,
    },
    children: {
      ...createRadioGroup(isDirectDataSource)
    }
  }
  if (basePoolId && basePoolId !== '30005' && basePoolId !== '30006') {
    isDirect.props.required = false;
  }
  let meta = {
    tabName: '门店属性',
    fields: {
      eleStoreId: {
        props: {
          ...formItemProps({
            label: 'ele门店ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      newRetailStoreId: {
        props: {
          ...formItemProps({
            label: '新零售门店id',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      storeId: {
        props: {
          ...formItemProps({
            label: '淘内门店ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      storeName: {
        props: {
          ...formItemProps({
            label: '门店名称',
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      storeCategoryIds: {
        props: {
          ...formItemProps({
            label: '门店类目',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: storeCascadeOptions})
        }
      },
      newStoreType: {
        props: {
          ...formItemProps({
            label: '门店主营类目(新)',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: newStoreMainCascadeOptions })
        }
      },
      storeType: {
        props: {
          ...formItemProps({
            label: '门店主营类目(旧)',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: storeMainCascadeOptions, disabled: true})
        }
      },
      businessRegion: {
        ...bussinessRegion
      },
      businessAttribute: {
        ...bussinessLine
      },
      newBuFlags: {
        ...newBuFlags
      },
      city: {
        props: {
          ...formItemProps({
            label: '所在城市',
          })
        },
        children: {
          ...createLevelCascadeSelect({dataSource: cityCascadeOptions})
        }
      },
      storeScore: {
        ...storeScore
      },
      newServiceTag: {
        ...newServiceTag
      },
      initialDeliveryPrice: {
        ...initialDeliveryPrice
      },
      brandName: {
        props: {
          ...formItemProps({
            label: '所属品牌',
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')}),
        }
      },
      nearly7DaysBusinessHours: {
        props: {
          ...formItemProps({
            label: '近7天营业总时长',
          })
        },
        children: {
          ...createNumberRangeInput({unitNode: '小时'})
        }
      },
      supplierId: {
        props: {
          ...formItemProps({
            label: '供应商ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      supplierIds: { // supplierId 为老字段，现在已不用，现在只有招商这俩池子再用新的. 老字段先留存，兼容
        props: {
          ...formItemProps({
            label: '供应商ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      sellerId: {
        props: {
          ...formItemProps({
            label: '淘系卖家ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({placeholder: renderPlaceHolder('commaSeperated')})
        }
      },
      shopModelScore: {
        props: {
          ...formItemProps({
            label: <>商户模型评分<ShopScoreTip/></>,
          }),
        },
        children: {
          ...createNumberRangeInput({max: 100, min: 0, precision: 0.01, step: 0.01})
        }
      },
      signatory: {
        props: {
          ...formItemProps({
            label: <>签约人<BDTip /></>,
          }),
        },
        children: {
          ...createInput({placeholder: '请输入姓名'})
        }
      },
      isDirect,
      isCityAgent: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '是否城代商户',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '否', value: 0 }, { label: '是', value: 1 }] })
        }
      },
      deliveryTimelyRate: {
        props: {
          ...formItemProps({
            label: '出货及时率',
          }),
        },
        children: {
          ...createNumberRangeInput({max: 100, min:0, step: 1, unitNode: '%'})
        }
      },
      orderPerformanceRate: {
        props: {
          ...formItemProps({
            label: '订单履约率',
          }),
        },
        children: {
          ...createNumberRangeInput({ max: 100, min: 0, step: 1, unitNode: '%' })
        }
      },
      selfFetch: {
        props: {
          ...formItemProps({
            label: '店类型',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: [{label: '外卖店', value: '0'}, {label: '自提店', value: '1'}, {label: '外卖自提店', value: '2'}]})
        }
      },
      shopLevel: {
        props: {
          ...formItemProps({
            label: '基建分层',
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: [{ label: '未入门', value: 0 }, { label: '入门', value: 100 }, { label: '健康', value: 200 }, { label: '完美', value: 300 }, { label: '超级', value: 500 }] })
        }
      },
      wholeCityDelivery: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '是否当日/半日/次日达（全城淘）',
          }),
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '否', value: 0}, {label: '是', value: 1}]})
        }
      },
      isOpenCommissionFee: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '当天是否开启CPS',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '否', value: 0 }, { label: '是', value: 1 }] })
        }
      },
      commissionRate1d: {
        props: {
          ...formItemProps({
            label: 'CPS出佣比例',
          }),
        },
        children: {
          ...createNumberRangeInput({ max: 100, min: 0, step: 1, precision: 0, unitNode: '%' })
        }
      },
      storeAttributes: { //店铺标示字段
        props: {
          ...formItemProps({
            label: <>店铺标示<StoreAttributesTip /></>,
          })
        },
        children: {
          ...multiSelect({ dataSource: storeAttributesSourceStore })
        },
      },
      shopComprehensiveScore: { //店铺综合分
        props: {
          ...formItemProps({
            label: '商户成长分',
          })
        },
        children: {
          ...multiSelect({dataSource: shopComprehensiveScoreDs})
        }
      },
      lastMonthShopComprehensiveScore:{ //上月店铺综合分
        props: {
          ...formItemProps({
            label: '上月店铺综合分',
          })
        },
        children: {
          ...multiSelect({dataSource: shopComprehensiveScoreDs})
        }
      },
      isSelfDelivery: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '是否自配送',
          }),
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '否', value: '0'}, {label: '是', value: '1'}]})
        }
      },
      serviceFeature: {
        props: {
          ...formItemProps({
            label: '业务属性',
          })
        },
        children: {
          ...multiSelect({
            dataSource: [
              {
                label: '全国KA',
                value: 'SF_9'
              },
              {
                label: '城市龙头',
                value: 'SF_10'
              },
              {
                label: '其他连锁',
                value: 'SF_11'
              },
              {
                label: '散店',
                value: 'SF_12'
              }
            ]
          })
        }
      },
      bizLineLv3Code: {
        props: {
          ...formItemProps({
            label: '业务线（废弃）',
            hasClear: true,
          }),
          isHidden: true
        },
        children: {
          ...createCascaderSelect({ dataSource: bizLineListOptions })
        }
      },
      bizLineLv3CodeV2: {
        props: {
          ...formItemProps({
            label: '业务线',
            hasClear: true,
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: bizLineListOptions })
        }
      },
      manageOrgLv2Code: {
        props: {
          ...formItemProps({
            label: '经营归属（废弃）',
            hasClear: true,
          }),
          isHidden: true
        },
        children: {
          ...createCascaderSelect({ dataSource: manageorgListOptions })
        }
      },
      manageOrgLv2CodeV2: {
        props: {
          ...formItemProps({
            label: '经营归属',
            hasClear: true,
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: manageorgListOptions })
        }
      },
      hyCategoryNameList: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '行业',
          }),
        },
        
        children: {
          ...multiSelect({
            dataSource: [{ label: '仓店', value: "CSBLYT_仓店" }, { label: '代购店', value: "CSBLYT_代购店" }, { label: '便利店', value: 'CSBLYT_便利店' }, { label: '奶站', value: "CSBLYT_奶站" }, { label: '快递店', value: "CSBLYT_快递店" }, { label: '校园店', value: 'CSBLYT_校园店' }, { label: '水站', value: 'CSBLYT_水站' }, { label: '电商店', value: 'CSBLYT_电商店' }, { label: '茶行', value: 'CSBLYT_茶行' }, { label: '超市', value: 'CSBLYT_超市' }, { label: '食杂店', value: 'CSBLYT_食杂店' }, { label: '饮料冰品', value: 'CSBLYT_饮料冰品' }],
            hasClear: true,
            defaultValue: ""
          })
        }
      },
      servicePackage: {
        props: {
          ...formItemProps({
            label: '服务包',
          })
        },
        children: {
          ...multiSelect({
            dataSource: [{  //todo 枚举需要换
              label: '新店',
              value: 'NEW_SHOP_S_TAG'
            }]
          })
        }
      },
      storeLayer: {
        props: {
          ...formItemProps({
            label: '商户分层',
          })
        },
        children: {
          ...createCascaderSelect({
            dataSource: [{
              label: 'KA',
              value: 'KAA'
            },{
              label: 'SMB',
              value: 'SMB'
            },{ 
              value: "CSBLFC_SMB",
              label: "超市便利_SMB"
            }, { 
              value: "CSBLFC_CKA",
              label: "超市便利_CKA"
            }, { 
              value: "CSBLFC_NKA",
              label: "超市便利_NKA"
            }, { 
              value: "CSBLFC_TT",
              label: "超市便利_TT"
            }, {
              value: "CSBLFC_LKA",
              label: "超市便利_LKA"
            }]
          })
        }
      },
      shopMall: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商家是否入驻全能商厦',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 2214674071299 }] })
        }
      },
      shopMall3: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商家是否入驻全能超市3.0',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'EMAlL3' }] })
        }
      },
      allowShoppingMoney: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商家是否开通购物金',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'ALLOW_SHOPPING_MONEY' }] })
        }
      },
      shopSkd: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '是否快递店',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'S_KD1' }, { label: '否', value: 'S_KD0' }] })
        }
      },
      shopBrandSonpre: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '是否官旗',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'B_SONPRE' }] })
        }
      },
    }
  }

  if (basePoolId === '30005' || basePoolId === '30006') {
    delete meta.fields.isCityAgent;
  }
  if (filtering) return processMeta(meta, {basePoolId}, 'skuStore')
  return meta
}
