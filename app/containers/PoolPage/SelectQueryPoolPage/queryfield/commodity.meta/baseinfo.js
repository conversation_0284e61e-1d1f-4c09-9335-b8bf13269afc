import React from 'react'
import { Balloon } from '@alife/next';

import {
  formItemProps,
  validators,
  renderPlaceHolder,
  processMeta,
  createCommaArrayInput,
  createLevelCascadeSelect,
  createGroupInputAndSelect,
  createNumberRangeInput,
  createRadioGroup,
  multiSelect,
  createCascaderSelect
} from '../common/tools';
import { basepoolIds } from '@/constants';
import { IsRiskCompTip } from '../store.meta/baseinfo';
import {alipayRelIdSourceCommodity} from '../../common';

const skuPool = basepoolIds.sku

const UpcTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    国际69码
  </Balloon.Tooltip>
)


const GroupTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    若已有标签群组不能满足需求，可在“数据洞察-标签管理”中重新创建群组
  </Balloon.Tooltip>
)

const HomologousTip = () => (
  <Balloon.Tooltip trigger={<span className="help">?</span>} >
    只可选择品类运营中心T-2生产的数据
  </Balloon.Tooltip>
)

export function baseInfo({
  categoryCascadeOptions,
  basePoolId,
  filtering,
  itemStatusOptions,
  groupActivityIdsTypesOptions,
  manageorgListOptions, 
  bizLineListOptions,
}){
  let meta = {
    tabName: '基础信息',
    fields: {
      seasonalTrendScore: {
        props: {
          ...formItemProps({
            label: <>时令商品趋势分<Balloon.Tooltip trigger={<span className="help">?</span>} >算法计算的时令性商品的分值，分值越高，该商品时令性越强（范围在0~100）</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createNumberRangeInput({min: 0, max: 100, precision: 1, step: 0.1})
        },
        // basePoolId: skuPool.SEASON
      },
      specialtyTrendScore: {
        props: {
          ...formItemProps({
            label: <>特产商品趋势分<Balloon.Tooltip trigger={<span className="help">?</span>} >特产商品分值为1000</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createNumberRangeInput({min: 1, max: 1100, precision: 1, step: 0.1})
        },
        // basePoolId: skuPool.SEASON
      },
      isHuoguo: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            required: true,
            label: <>火锅场景商品<Balloon.Tooltip trigger={<span className="help">?</span>} >算法计算得出的火锅场景的商品，可做二次筛选</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '是', value: 1}, {label: '否', value: 0}]})
        }
      },
      operateInvestActivityId: {
        props: {
          ...formItemProps({
            label: '运营位招商活动ID',
            required: true,
            validator: validators.joinArray(validators.chain([validators.idCommaSeperatedRequired, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
        // basePoolId: skuPool.INVEST
      },
      brandActivityIds: {
        props: {
          ...formItemProps({
            label: '品牌活动ID',
            required: basePoolId !== '20012',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
        // basePoolId: skuPool.INVEST
      },
      groupActivitiesType: {
        props: {
          // ...formItemProps({
          //   label: '类型', //todo 需要去掉的
          //   required: true,
          // }),
          defaultValue: {activityIdsTypes: 'marketing', activityIdsTypesValue: []},
          labelCol: { fixedSpan: 2, align: 'center' },
          wrapperCol: { fixedSpan: 20, align: 'center' },
        },
        children: {
          ...createGroupInputAndSelect({ validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)])),dataSource: groupActivityIdsTypesOptions,  required: true, defaultValue: {activityIdsTypes: 'marketing', activityIdsTypesValue: []}})
        }
      },
      activityIds: {
        props: {
          ...formItemProps({
            label: '营销活动id',
            required: basePoolId != "20010" && basePoolId !== '20012' && basePoolId !== '40001',
            validator: (basePoolId != "20010" && basePoolId !== '20012'&& basePoolId!='40001') ? validators.joinArray(validators.chain([validators.idCommaSeperatedRequired, validators.createMaxCommaItem(  basePoolId != "40001"?100:200)])) : null
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated', '营销活动id', basePoolId != "40001" ? 100 : 200)
          })
        },
        // basePoolId: skuPool.ACTIVITY,
      },
      goodsId: {
        props: {
          ...formItemProps({
            label: '商品ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      mainInviteIds: {
        props: {
          ...formItemProps({
            label: '主招商活动id',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      inviteIds: {
        props: {
          ...formItemProps({
            label: '子招商活动id',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      upcId: {
        props: {
          ...formItemProps({
            label: <>商品条形码<UpcTip/></>,
            validator: validators.joinArray(validators.chain([validators.upcIdCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      honeycombId: {
        props: {
          ...formItemProps({
            label: '蜂窝ID',
            validator: validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        },
      },
      goodsNameKeyWord: {
        props: {
          ...formItemProps({
            label: '商品名称',
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.optional(validators.createMaxCommaItem(100))])),
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated')
          })
        }
      },
      excludeGoodsKeyWords: {
        props: {
          ...formItemProps({
            label: '剔除商品关键词',
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.optional(validators.createMaxCommaItem(100))])),
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated', '剔除商品关键词')
          })
        }
      },
      goodsCategory: {
        props: {
          ...formItemProps({
            label: '商品分类'
          })
        },
        children: {
          ...createLevelCascadeSelect({dataSource: categoryCascadeOptions})
        }
      },
      bizLineLv3Code: {
        props: {
          ...formItemProps({
            label: '业务线（废弃）',
            hasClear: true,
          }),
          isHidden: true,
        },
        children: {
          ...createCascaderSelect({ dataSource: bizLineListOptions })
        }
      },
      bizLineLv3CodeV2: {
        props: {
          ...formItemProps({
            label: '业务线',
            hasClear: true,
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: bizLineListOptions })
        }
      },
      manageOrgLv2Code: {
        props: {
          ...formItemProps({
            label: '经营归属（废弃）',
            hasClear: true,
          }),
          isHidden: true,
        },
        children: {
          ...createCascaderSelect({ dataSource: manageorgListOptions })
        }
      },
      manageOrgLv2CodeV2: {
        props: {
          ...formItemProps({
            label: '经营归属',
            hasClear: true,
          })
        },
        children: {
          ...createCascaderSelect({ dataSource: manageorgListOptions })
        }
      },
      itemPicQualityScore: {
        props: {
          ...formItemProps({
            label: '商品图片质量分'
          })
        },
        children: {
          ...createLevelCascadeSelect({
            dataSource: [
              {
                value: '1', label: '含牛皮癣图片', children:
                  [
                    {value: '100', label: '高质量图片（含牛皮癣低）', children: null},
                    {value: '101', label: '中等质量图片(含牛皮癣中)', children: null},
                    {value: '102', label: '低质量图片（含牛皮癣高）', children: null},
                  ]
              },
              {
                value: '2', label: '透明底图', children:
                  [
                    {value: '201', label: '是(透明底图)', children: null},
                  ]
              },
              {
                value: '3', label: '白底图', children:
                  [
                    {value: '301', label: '是(白底图)', children: null},
                  ]
              },
              {
                value: '4', label: '主体面积占比', children:
                  [
                    {value: '400', label: '低质量图片(主体面积占比)', children: null},
                    {value: '401', label: '高质量图片(主体面积占比)', children: null},
                  ]
              }
            ]
          })
        }
      },
      // goodsBrands: {
      //   props: {
      //     ...formItemProps({
      //       label: '商品品牌属性',
      //       validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.optional(validators.createMaxCommaItem(30))])),
      //     })
      //   },
      //   children: {
      //     ...createCommaArrayInput({
      //       placeholder: renderPlaceHolder('commaSeperated', '商品品牌属性')
      //     })
      //   }
      // },
      is0Qi0Pei: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '新客0起0配商品',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: '0QI0PEI' }] })
        }
      },
      isMedcialYXHY: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '优享好药',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 'YXHY_MED' }] })
        }
      },
      isCatComp: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '类目挂靠商品准确性',
          }),
          // defaultValue: 1,
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 1}]})
        }
      },
      isRiskComp: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 13, align: 'center' },
          ...formItemProps({
            label: <>风控合规数据<IsRiskCompTip/></>,
          }),
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '普通等级', value: 1}, {label: '严格等级', value: 2}], defaultValue: 1})
        }
      },
      isAssembleItem: {
        props: {
          labelCol: { fixedSpan: 4, align: 'center' },
          ...formItemProps({
            label: '拼团商品',
          })
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '是', value: 1}, {label: '否', value: 0}, {label: '不限制', value: 2}]})
        }
      },
      isOverPrice: {
        props: {
          labelCol: { fixedSpan: 6, align: 'center' },
          ...formItemProps({
            label: '价格虚高商品',
          })
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '是', value: 1}, {label: '否', value: 0}, {label: '不限制', value: 2}]})
        }
      },
      isActivityPriceLessThanNearly7DayMinPrice: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '活动价格小于等于7天最低价 ',
          }),
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '是', value: 1}, {label: '不限制', value: 0}], defaultValue: 1})
        }
      },
      alipayRelId:{ //支付宝场景指标字段
        props: {
          ...formItemProps({
            label: '支付宝场景指标',
          })
        },
        children: {
          ...multiSelect({ dataSource: alipayRelIdSourceCommodity, single: true, hasClear: true })
        },
      },
      labelGroupIds: {
        props: {
          ...formItemProps({
            label: <>标签群组<GroupTip/></>,
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.optional(validators.createMaxCommaItem(100))])),
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated', '标签群组')
          })
        }
      },
      // isMoreSpecStandard: {
      //   props: {
      //     labelCol: { fixedSpan: 8, align: 'center' },
      //     wrapperCol: { fixedSpan: 10, align: 'center' },
      //     ...formItemProps({
      //       label: '是否缺少规格',
      //     }),
      //     defaultValue: '',
      //   },
      //   children: {
      //     ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 1}]})
      //   }
      // },
      isPicStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '图片质量是否合格',
          }),
          defaultValue: '',
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 'Y_PIC'}]})
        }
      },
      isPriceStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '价格是否合格',
          }),
          defaultValue: '',
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 'Y_PRICE'}]})
        }
      },
      isCatCompStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '类目是否正确',
          }),
        },
        children: {
          ...createRadioGroup({
            dataSource: [{label: '全部', value: ''}, {label: '正确', value: 'Y_CAT'}],
            defaultValue: (basePoolId != "20011" && basePoolId !== '21011') ? '' : 'Y_CAT'
          })
        }
      },
      isUpcNameStandardStr: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          wrapperCol: { fixedSpan: 10, align: 'center' },
          ...formItemProps({
            label: '品名是否合格',
          }),
          defaultValue: '',
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合格', value: 'Y_UPCNAME'}]})
        }
      },
      // isUpcStandard: {
      //   props: {
      //     labelCol: { fixedSpan: 8, align: 'center' },
      //     wrapperCol: { fixedSpan: 10, align: 'center' },
      //     ...formItemProps({
      //       label: '标品条形码是否合规',
      //     }),
      //     defaultValue: '',
      //   },
      //   children: {
      //     ...createRadioGroup({dataSource: [{label: '全部', value: ''}, {label: '合规', value: 1}]})
      //   }
      // }
      itemStatus:{ //商品状态
        props: {
          ...formItemProps({
            label: '商品状态',
          }),
        },
        children: {
          ...multiSelect({
            dataSource: itemStatusOptions,
            single: true,
            hasClear: true,
            defaultValue: "YOU_XIAO"
          })
        },
      },
      goodsType: {
        props: {
          labelCol: { fixedSpan: 6, align: 'center' },
          wrapperCol: { fixedSpan:18, align: 'center' },
          ...formItemProps({
            label: '售卖类型',
          })
        },
        children: {
          ...createRadioGroup({dataSource: [{label: '预售品', value: 'Y_PreSale'}, {label: '现货品', value: 'Y_OnSale'}, {label: '不限', value: ''}]})
        }
      },
      itemMall: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商品是否入住全能商厦',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '是', value: 2214674071299 }] })
        }
      },
      wareHouse: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '是否前置仓',
          }),
        },
        children: {
          ...createRadioGroup({ dataSource: [{ label: '不限', value: "" }, { label: '否', value: "NOT_WARE_HOUSE" }, { label: '是', value: 'WARE_HOUSE' }] })
        }
      },
      tradeStoreLayer: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: '行业商户分层',
          }),
        },
        children: {
          ...multiSelect({
            dataSource: [{ label: 'KA直营', value: "KA_DIRECT" }, { label: 'KA代理', value: "KA_NDIRECT" }, { label: 'SMB直营连锁', value: 'SMB_DIRECT_NOTSF12' }, { label: 'SMB直营散店', value: "SMB_DIRECT_SF12" }, { label: 'SMB代理连锁', value: "SMB_NOTDIRECT_NOTSF12" }, { label: 'SMB代理散店', value: 'SMB_NOTDIRECT_SF12' }],
            hasClear: true,
            defaultValue: ""
          })
        }
      },
      itemTPC: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 11, align: 'center' },
          ...formItemProps({
            label: <>商品同品簇<HomologousTip/></>,
          }),
        },
        children: {
          ...createCascaderSelect({dataSource: [],dataSourceName:'TPCName'})
        }
      },
      itemGroupIdList: {
        props: {
          ...formItemProps({
            label: <>同品归一码</>,
            validator: validators.joinArray(validators.chain([validators.commaSeperated, validators.optional(validators.createMaxCommaItem(100))])),
          })
        },
        children: {
          ...createCommaArrayInput({
            placeholder: renderPlaceHolder('commaSeperated', '同品归一码')
          })
        }
      },
    }
  }

  if (filtering) return processMeta(meta, {basePoolId}, 'baseInfo')
  return meta
}
