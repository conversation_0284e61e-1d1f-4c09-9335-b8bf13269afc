import React from 'react'
import { Balloon } from '@alife/next';


import { formItemProps, createNumberRangeInput, processMeta, createCascaderSelect, createCheckboxGroup, multiSelect } from '../common/tools';
import { basepoolIds } from '@/constants';
import {activitiesSource, shopPZLMStringSourceStore} from '../../common';

const skuPool = basepoolIds.sku

export function marketInfo({basePoolId, filtering, marketingTypeOptions,newMarketingTypeOptions}) {
  let meta = {
    tabName: '营销信息',
    fields: {
      storeMarketRate: {
        props: {
          ...formItemProps({ //TODO: change ? mark here
            label: <>商户自营销率<Balloon.Tooltip trigger={<span className="help">?</span>} >商户自营销率=商户总补贴/GMV*100%</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createNumberRangeInput({max: 100})
        },

      },
      goodsOriginalPrice: {
        props: {
          ...formItemProps({
            label: '商品原价',
          })
        },
        children: {
          ...createNumberRangeInput({precision:2})
        }
      },
      goodsPresentPrice: {
        props: {
          ...formItemProps({
            label: '商品现价',
          })
        },
        children: {
          ...createNumberRangeInput({precision:2})
        }
      },
      goodsDiscount: {
        props: {
          ...formItemProps({
            label: '商品折扣力度',
          })
        },
        children: {
          ...createNumberRangeInput({min: 0, max: 10, precision: 1, step: 0.1})
        },
      },
      skuDiscount: { //淘内的商品折扣力度
        props: {
          ...formItemProps({
            label: '商品折扣力度',
          })
        },
        children: {
          ...createNumberRangeInput({min: 0, max: 10, precision: 1, step: 0.1})
        },
      },
      goodsActivitiesType: {
        props: {
          ...formItemProps({
            label: '商品活动类型',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: marketingTypeOptions})
        },
      },
      activityTypes: {
        props: {
          ...formItemProps({
            label: '商品活动类型(新)',
          })
        },
        children: {
          ...createCascaderSelect({dataSource: newMarketingTypeOptions})
        },
      },
      shopPZLMString:{ //店铺品质联盟
        props: {
          ...formItemProps({
            label: '店铺品质联盟',
          })
        },
        children: {
          ...multiSelect({ dataSource: shopPZLMStringSourceStore, single: true, hasClear: true })
        },
      },
      goodsPerfectActivitiesType:{ //特定商品活动
        props: {
          ...formItemProps({
            label: '特定商品活动',
          })
        },
        children: {
          ...multiSelect({ dataSource: activitiesSource, single: true, hasClear: true })
        },
      },
      itemDistribute: {
        props: {
          labelCol: { fixedSpan: 7, align: 'center' },
          wrapperCol: { fixedSpan: 13, align: 'center' },
          ...formItemProps({
            label: <>商品分布<Balloon.Tooltip trigger={<span className="help">?</span>} >畅销品：指定店铺范围内，指定商品类目的销售数量或GMV都占该店铺范围整体销售个数orGMV的TOP20%;<br/>
              滞销品：指定店铺范围内，指定商品类目的销售数量或GMV都占该店铺范围整体销售个数orGMV的TAIL20%;<br/>钩子：参加促销活动的商品;其他：上记以外。</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createCheckboxGroup({dataSource: [{label: '畅销品', value: 1}, {label: '滞销品', value: 2}, {label: '钩子', value: 3}, {label: '其他', value: 4}]})
        },
      },
    }
  }

  if (filtering) return processMeta(meta, {basePoolId}, 'skuMarket')
  return meta
}
