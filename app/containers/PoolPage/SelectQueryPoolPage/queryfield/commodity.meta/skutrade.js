
import React from 'react'
import { Balloon } from '@alife/next';
import { formItemProps, createNumberRangeInput, createRadioGroup, processMeta } from '../common/tools';

export function skuTradeInfo({ basePoolId, filtering }) {
  let nearly7DaysMinHistoryTransactionPrice = { dataSource: [{ label: '是', value: 1 }, { label: '否', value: 0 }] };
  let nearly15DaysMinHistoryTransactionPrice = { dataSource: [{ label: '是', value: 1 }, { label: '否', value: 0 }] };
  if (basePoolId === '30005' || basePoolId === '30006') {
    nearly7DaysMinHistoryTransactionPrice = { dataSource: [{label: '不限', value: -1}, { label: '是', value: 1 }, { label: '否', value: 0 }], defaultValue: -1 };
    nearly15DaysMinHistoryTransactionPrice = { dataSource: [{ label: '不限', value: -1 }, { label: '是', value: 1 }, { label: '否', value: 0 }], defaultValue: -1 };
  }
  let meta = {
    tabName: '浏览和交易信息',
    fields: {
      nearly7DaysOrders: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center'},
          ...formItemProps({
            label: '商品近7d有效订单量',
          }),
          style:{lineHeight: '14px'},
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly30DaysOrders: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '商品近30d有效订单量',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      d7ShopValidOrderCnt: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '门店近7d有效订单量',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      d30ShopValidOrderCnt: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '门店近30d有效订单量',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly7DaysTurnover: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '近7d交易额',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      nearly30DaysTurnover: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '近30d交易额',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      d7SaleAmt: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '近7D净交易总额',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      d15SaleAmt: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '近15D净交易总额',
          })
        },
        children: {
          ...createNumberRangeInput({precision: 0})
        }
      },
      categoryOrderPermeability: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: <>品类商品订单渗透率<Balloon.Tooltip trigger={<span className="help">?</span>} >商品二级类目订单／对应的一级类目总订单*100%，单位百分比，区间范围0-100，支持小数点后一位</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createNumberRangeInput({min: 0, max: 100, precision: 1, step: 0.1})
        }
      },
      categorySkuCityPermeability: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: <>二级品类商品到城市的覆盖率<Balloon.Tooltip trigger={<span className="help">?</span>} >二级品类商品数／城市商品品类sku总数*100，单位百分比，区间范围0-100，支持小数点后一位</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createNumberRangeInput({min: 0, max: 100, precision: 1, step: 0.1})
        }
      },
      category15DaySoldRate: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: <>三级品类商品15d动销率<Balloon.Tooltip trigger={<span className="help">?</span>} >商品三级品类动销商品数/在售商品数*100%，单位百分比，区间范围0-100，支持小数点后一位</Balloon.Tooltip></>,
          })
        },
        children: {
          ...createNumberRangeInput({min: 0, max: 100, precision: 1, step: 0.1})
        }
      },
      nearly7DaysMinHistoryTransactionPrice: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '近7d历史成交最低价',
          })
        },
        children: {
          ...createRadioGroup(nearly7DaysMinHistoryTransactionPrice, -1)
        }
      },
      nearly15DaysMinHistoryTransactionPrice: {
        props: {
          labelCol: { fixedSpan: 8, align: 'center' },
          ...formItemProps({
            label: '近15d历史成交最低价',
          })
        },
        children: {
          ...createRadioGroup(nearly15DaysMinHistoryTransactionPrice, -1)
        }
      },
    }
  }
  if (filtering) return processMeta(meta, {basePoolId}, 'skuTrade')
  return meta
}
