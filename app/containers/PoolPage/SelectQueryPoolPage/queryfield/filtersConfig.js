import { initialDeliveryPrice, newServiceTag, storeScore } from './common/fields';

const skuStore = [
  'eleStoreId',
  'newRetailStoreId',
  'storeId',
  'storeName',
  'storeCategoryIds',
  'businessRegion',
  'newBuFlags',
  'city',
  'storeScore',
  'newServiceTag',
  'initialDeliveryPrice',
  'brandName',
  'nearly7DaysBusinessHours',
  'supplierId',
  'shopModelScore',
  'signatory',
  'isOpenCommissionFee',
  'commissionRate1d',
  // "isDirect",
  // "isCityAgent",
]

const skuMarket = [ //淘外的
  'storeMarketRate',
  'goodsOriginalPrice',
  'goodsPresentPrice',
  'goodsDiscount',
  'goodsActivitiesType',
  // "itemDistribute"
]
/** 新建池子筛选项配置   []代表显示全部筛选项 */
export const poolFiltersConfig = {
  '10001': { //新零售全量商品池
    'baseInfo': [
      'goodsId',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': skuStore, // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': skuMarket, //all
  },
  '10002': {
    'baseInfo': [
      'activityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': skuStore, // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': skuMarket, // all
  },
  '10003': { //招商活动商品池
    'baseInfo': [
      'operateInvestActivityId',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': skuStore, // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': skuMarket, // all
  },
  '10004': { //新零售全量门店池
    'baseInfo': [
      'eleStoreId',
      'newRetailStoreId',
      'storeName',
      'storeCategoryId',
      'supplierId',
      'brandName',
      'businessArea',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'skuNum',
      'initialDeliveryPrice',
      'nearly7DaysBusinessHours',
      'createTime',
      'shopModelScore',
      'signatory'
    ],
    'marketInfo': [
      'storeMarketRate',
      'newCustomerActivity',
      'firstGearFullReduceThreshold',
      'firstGearFullReduceDiscountPrice',
      'businessVoucherThreshold',
      'businessVoucherDiscountPrice',
      'businessNewCustomerThreshold',
      'businessNewCustomerDiscountPrice'
    ],
    'storebrowseandtradeinfo': [],
    'crowdAttributeInfo': [],
  },
  '10005': { //新零售新招商商品池
    'baseInfo': [
      'inviteIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': skuStore, // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': skuMarket, // all
  },
  '10006': { //新零售时令场景池
    'baseInfo': [
      'seasonalTrendScore',
      'goodsId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': [
      'eleStoreId',
      'newRetailStoreId',
      'storeName',
      'storeCategoryIds',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'supplierId',
      'isOpenCommissionFee',
      'commissionRate1d',
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate'
    ],
    'skuMarket': [
      'goodsOriginalPrice',
      'goodsDiscount',
      'goodsActivitiesType',
    ],
  },
  '10007': { //新零售品牌商品池
    'baseInfo': [
      'brandActivityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': skuStore, // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': skuMarket, // all
  },
  '10009': { //新零售旅游特产池
    'baseInfo': [
      'specialtyTrendScore',
      'goodsId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': [
      'eleStoreId',
      'newRetailStoreId',
      'storeName',
      'storeCategoryIds',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'supplierId',
      'isOpenCommissionFee',
      'commissionRate1d',
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate'
    ],
    'skuMarket': [
      'goodsOriginalPrice',
      'goodsDiscount',
      'goodsActivitiesType',
    ],
  },
  '10010': { //新零售招商门店池
    'baseInfo': [
      'eleStoreId',
      'newRetailStoreId',
      'inviteIds',
      'storeName',
      'storeCategoryId',
      'supplierId',
      'brandName',
      'businessArea',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'skuNum',
      'initialDeliveryPrice',
      'nearly7DaysBusinessHours',
      'createTime',
      'shopModelScore',
      'signatory'
    ],
    'marketInfo': [
      'inviteActivityThreshold',
      'inviteActivityDiscountPrice'
    ],
    'storebrowseandtradeinfo': [],
    'crowdAttributeInfo': [],
  },
  '10011': {
    'baseInfo': [
      'inviteIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory'
    ],
    'skuStore': skuStore, // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': skuMarket, // all
  },
  '10012': { //新零售火锅场景池
    'baseInfo': [
      'isHuoguo',
      'goodsId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatComp',
      'itemPicQualityScore'
    ],
    'skuStore': [
      'eleStoreId',
      'newRetailStoreId',
      'storeName',
      'storeCategoryIds',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'supplierId',
      'isOpenCommissionFee',
      'commissionRate1d',
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate'
    ],
    'skuMarket': [
      'goodsOriginalPrice',
      'goodsDiscount',
      'goodsActivitiesType',
    ],
  },
  '10013': { //新零售商品转门店池
    'baseInfo': [
      'goodsId',
      'inviteIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory'
    ],
    'skuStore': [
      'eleStoreId',
      'newRetailStoreId',
      'storeName',
      'storeCategoryIds',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'supplierId',
      'shopModelScore',
      'signatory',
      'isOpenCommissionFee',
      'commissionRate1d',
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': skuMarket, // all
  },
  '20001': { //入淘全量商品池
    'baseInfo': [
      'goodsId',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatCompStandardStr',
      'itemPicQualityScore',
      'isRiskComp',
      'isAssembleItem',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus',
      'goodsType'
    ],
    'skuStore': [
      'eleStoreId',
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      'activityTypes',
      // 'shopPZLMString'
    ], //all
  },
  '20002': { //入淘营销商品池
    'baseInfo': [
      'activityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '20004': { //入淘新零售入淘时令场景池
    'baseInfo': [
      'seasonalTrendScore',
      'goodsId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatCompStandardStr',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      'storeId',
      // "newRetailStoreId",
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'sellerId',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate'
    ],
    'skuMarket': [
      'goodsOriginalPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      'activityTypes',
      // 'shopPZLMString'
    ],
  },
  '20005': { //入淘新零售旅游特产池
    'baseInfo': [
      'specialtyTrendScore',
      'goodsId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatCompStandardStr',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      'storeId',
      // "newRetailStoreId",
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'sellerId',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate'
    ],
    'skuMarket': [
      'goodsOriginalPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      'activityTypes',
      // 'shopPZLMString'
    ],
  },
  '20006': { //入淘火锅场景池
    'baseInfo': [
      'isHuoguo',
      'goodsId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatCompStandardStr',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      'storeId',
      // "newRetailStoreId",
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'sellerId',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate'
    ],
    'skuMarket': [
      'goodsOriginalPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      'activityTypes',
      // 'shopPZLMString'
    ],
  },
  '20007': { //入淘品牌商品池
    'baseInfo': [
      'brandActivityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatCompStandardStr',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '20008': {//入淘新零售新招商商品池
    'baseInfo': [
      'mainInviteIds',
      'inviteIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatCompStandardStr',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '20010': { //新零售入淘营销商品池升级版
    'baseInfo': [
      'activityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsPerfectActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '21002': { //新零售权益类营销活动商品池
    'baseInfo': [
      'activityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '21008': { //新零售权益类招商活动商品池
    'baseInfo': [
      'mainInviteIds',
      'inviteIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isCatCompStandardStr',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      // "eleStoreId",
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'serviceFeature',
      // 'servicePackage'
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '20011': { //商品类营销活动商品池New
    'baseInfo': [
      'groupActivitiesType',
      // 'activityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus',
      'isCatCompStandardStr'
    ],
    'skuStore': [
      // "eleStoreId",
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      // 'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '21011': { //权益类营销活动商品池New
    'baseInfo': [
      'groupActivitiesType',
      // 'activityIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'itemPicQualityScore',
      'isRiskComp',
      'honeycombId',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus',
      'isCatCompStandardStr'
    ],
    'skuStore': [
      'storeId',
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      // 'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '30001': { //新零售入淘全量门店池
    'baseInfo': [
      'eleStoreId',
      'storeId',
      'storeName',
      'storeCategoryId',
      'sellerId',
      'brandName',
      'newMainCategoryId',
      'businessArea',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'skuNum',
      'initialDeliveryPrice',
      'nearly7DaysBusinessHours',
      'createTime',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isRiskComp',
      'alipayRelId',
      'isOpenCommissionFee',
      'commissionRate1d',
      'labelGroupIds',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'

    ],
    'marketInfo': [
      'storeMarketRate',
      'newCustomerActivity',
      'superActivityAmountFunds',
      'firstGearFullReduceThreshold',
      'firstGearFullReduceDiscountPrice',
      'businessVoucherThreshold',
      'businessVoucherDiscountPrice',
      // 'shopPZLMString',
      'storeActivitiesType'
    ],
    'storebrowseandtradeinfo': [],
    'crowdAttributeInfo': [],
  },
  '30002': { //新零售入淘营销门店池
    'baseInfo': [
      'activityIds',
      'eleStoreId',
      'storeId',
      'storeName',
      'storeCategoryId',
      'sellerId',
      'brandName',
      'newMainCategoryId',
      'businessArea',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'skuNum',
      'initialDeliveryPrice',
      'nearly7DaysBusinessHours',
      'createTime',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isRiskComp',
      'alipayRelId',
      'isOpenCommissionFee',
      'commissionRate1d',
      'labelGroupIds',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ],
    'marketInfo': [
      'storeMarketRate',
      'newCustomerActivity',
      'superActivityAmountFunds',
      'firstGearFullReduceThreshold',
      'firstGearFullReduceDiscountPrice',
      'businessVoucherThreshold',
      'businessVoucherDiscountPrice',
      // 'shopPZLMString',
      'storeActivitiesType'
    ],
    'storebrowseandtradeinfo': [],
    'crowdAttributeInfo': []
  },
  '30003': { //新零售入淘招商门店池
    'baseInfo': [
      'eleStoreId',
      'storeId',
      // "newRetailStoreId",
      'mainInviteIds',
      'inviteIds',
      'storeName',
      'storeCategoryId',
      'sellerId',
      'brandName',
      'newMainCategoryId',
      'businessArea',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'skuNum',
      'initialDeliveryPrice',
      'nearly7DaysBusinessHours',
      'createTime',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isRiskComp',
      'alipayRelId',
      'isOpenCommissionFee',
      'commissionRate1d',
      'labelGroupIds',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ],
    'marketInfo': [
      'inviteStoreMjPrice',
      'inviteStoreCouponAmt',
      // 'shopPZLMString',
      'storeActivitiesType',
      'superActivityAmountFunds'
    ],
    'storebrowseandtradeinfo': [],
    'crowdAttributeInfo': [],
  },
  '30004': { //新零售入淘商品转门店池
    'baseInfo': [
      'goodsId',
      'mainInviteIds',
      'inviteIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isRiskComp',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      'eleStoreId',
      'storeId',
      // "newRetailStoreId",
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  '30005': {//招商专用新零售入淘门店池
    'baseInfo': [
      'eleStoreId',
      'storeId',
      'storeName',
      'storeCategoryId',
      'sellerId',
      'brandName',
      'supplierIds',
      'newMainCategoryId',
      'businessArea',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'skuNum',
      'initialDeliveryPrice',
      'nearly7DaysBusinessHours',
      'createTime',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isRiskComp',
      'alipayRelId',
      'storeAttributes',
      'isOpenCommissionFee',
      'commissionRate1d',
      'labelGroupIds',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage',
      'storeLayer',
      'shopMall',
      'shopMall3',
      'wareHouse',
      'tradeStoreLayer',
      'hyCategoryNameList',
      'bizLineLv3Code',
      'bizLineLv3CodeV2',
      'manageOrgLv2Code',
      'manageOrgLv2CodeV2',
      'allowShoppingMoney',
      'shopSkd',
      'shopBrandSonpre',
      'itemGroupIdList',
    ],
    'marketInfo': [
      'storeMarketRate',
      'newCustomerActivity',
      'superActivityAmountFunds',
      'firstGearFullReduceThreshold',
      'firstGearFullReduceDiscountPrice',
      'businessVoucherThreshold',
      'businessVoucherDiscountPrice'
    ],
    'storebrowseandtradeinfo': [],
  },
  '30006': { //招商-新零售以品选店池
    'baseInfo': [
      'goodsId',
      'upcId',
      'goodsNameKeyWord',
      // 'excludeGoodsKeyWords',
      'is0Qi0Pei',
      'isMedcialYXHY',
      'goodsCategory',
      'itemPicQualityScore',
      'isRiskComp',
      'alipayRelId',
      'labelGroupIds',
      // 'isMoreSpecStandard',
      'isPicStandardStr',
      'isPriceStandardStr',
      'isCatCompStandardStr',
      'isUpcNameStandardStr',
      'itemStatus',
      'itemMall',
      'wareHouse',
      'tradeStoreLayer',
      'itemTPC',
      'itemGroupIdList',
      // 'isUpcStandard'
    ],
    'skuStore': [
      'eleStoreId',
      // "newRetailStoreId",
      'storeId',
      'storeName',
      'storeCategoryIds',
      'supplierIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'orderPerformanceRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'storeAttributes',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      'hyCategoryNameList',
      'storeLayer',
      'shopMall',
      'shopMall3',
      'allowShoppingMoney',
      'shopSkd',
      'shopBrandSonpre',
      'bizLineLv3Code',
      'bizLineLv3CodeV2',
      'manageOrgLv2Code',
      'manageOrgLv2CodeV2',
      // 'servicePackage'
    ],
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute'
    ],
  },
  '30007': {//招商专用新零售入淘门店池
    'baseInfo': [
      'eleStoreId',
      'storeId',
      // "newRetailStoreId",
      'inviteIds',
      'storeName',
      'storeCategoryId',
      'sellerId',
      'brandName',
      'newMainCategoryId',
      'businessArea',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'skuNum',
      'initialDeliveryPrice',
      'nearly7DaysBusinessHours',
      'createTime',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'selfFetch',
      'wholeCityDelivery',
      'isRiskComp',
      'alipayRelId',
      'isOpenCommissionFee',
      'commissionRate1d',
      'labelGroupIds',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ],
    'marketInfo': [
      'inviteStoreMjPrice',
      'inviteStoreCouponAmt',
      // 'shopPZLMString'
    ],
    'storebrowseandtradeinfo': [],
    'crowdAttributeInfo': [],
  },
  '31004': { //新零售权益商品转门店池
    'baseInfo': [
      'goodsId',
      'mainInviteIds',
      'inviteIds',
      'upcId',
      'goodsNameKeyWord',
      'excludeGoodsKeyWords',
      'goodsCategory',
      'isRiskComp',
      'alipayRelId',
      'labelGroupIds',
      'itemStatus'
    ],
    'skuStore': [
      'eleStoreId',
      'storeId',
      // "newRetailStoreId",
      'storeName',
      'storeCategoryIds',
      'newStoreType',
      'businessRegion',
      'newBuFlags',
      'city',
      'storeScore',
      'newServiceTag',
      'initialDeliveryPrice',
      'brandName',
      'nearly7DaysBusinessHours',
      'sellerId',
      'shopModelScore',
      'signatory',
      'isDirect',
      'isCityAgent',
      'deliveryTimelyRate',
      'selfFetch',
      'shopLevel',
      'wholeCityDelivery',
      'isOpenCommissionFee',
      'commissionRate1d',
      'shopComprehensiveScore',
      // 'lastMonthShopComprehensiveScore',
      'isSelfDelivery',
      'serviceFeature',
      // 'servicePackage'
    ], // all
    'skuTrade': [
      'nearly7DaysOrders',
      'nearly30DaysOrders',
      'd7ShopValidOrderCnt',
      'd30ShopValidOrderCnt',
      'nearly7DaysTurnover',
      'nearly30DaysTurnover',
      'd7SaleAmt',
      'd15SaleAmt',
      'categoryOrderPermeability',
      'categorySkuCityPermeability',
      'category15DaySoldRate',
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    'skuMarket': [
      'storeMarketRate',
      'goodsOriginalPrice',
      'goodsPresentPrice',
      'skuDiscount',
      'goodsActivitiesType',
      'itemDistribute',
      // 'shopPZLMString'
    ], // all
  },
  "40001": { //百亿补贴基础
    "baseInfo": [
      "activityIds",
      "mainInviteIds",
      "inviteIds",
      "upcId",
      "goodsNameKeyWord",
      "excludeGoodsKeyWords",
      "goodsCategory",
      "itemPicQualityScore",
      "isRiskComp",
      "honeycombId",
      "alipayRelId",
      "isActivityPriceLessThanNearly7DayMinPrice",
      'labelGroupIds',
      'itemStatus'
    ],
    "skuStore": [
      // "eleStoreId",
      // "newRetailStoreId",
      "storeId",
      "storeName",
      "storeCategoryIds",
      "newStoreType",
      "businessRegion",
      "newBuFlags",
      "city",
      "storeScore",
      "newServiceTag",
      "initialDeliveryPrice",
      "brandName",
      "nearly7DaysBusinessHours",
      "sellerId",
      "shopModelScore",
      "signatory",
      "isDirect",
      "isCityAgent",
      "deliveryTimelyRate",
      "orderPerformanceRate",
      "selfFetch",
      "shopLevel",
      "wholeCityDelivery",
      'serviceFeature',
      // 'servicePackage'
    ], // all
    "skuTrade": [
      "nearly7DaysOrders",
      "nearly30DaysOrders",
      "d7ShopValidOrderCnt",
      "d30ShopValidOrderCnt",
      "nearly7DaysTurnover",
      "nearly30DaysTurnover",
      "d7SaleAmt",
      "d15SaleAmt",
      "categoryOrderPermeability",
      "categorySkuCityPermeability",
      "category15DaySoldRate",
    ],
    "skuMarket": [
      "storeMarketRate",
      "goodsOriginalPrice",
      "goodsPresentPrice",
      "skuDiscount",
      "goodsActivitiesType",
      "itemDistribute",
      // 'shopPZLMString'
    ], // all
  },
  "20012": { //百亿补贴池
    "baseInfo": [
      "activityIds",
      "mainInviteIds",
      "inviteIds",
      "brandActivityIds",
      "upcId",
      "goodsNameKeyWord",
      "excludeGoodsKeyWords",
      "goodsCategory",
      "itemPicQualityScore",
      "isRiskComp",
      "honeycombId",
      "alipayRelId",
      "isActivityPriceLessThanNearly7DayMinPrice",
      'labelGroupIds',
      'itemStatus'
    ],
    "skuStore": [
      // "eleStoreId",
      // "newRetailStoreId",
      "storeId",
      "storeName",
      "storeCategoryIds",
      "newStoreType",
      "businessRegion",
      "newBuFlags",
      "city",
      "storeScore",
      "newServiceTag",
      "initialDeliveryPrice",
      "brandName",
      "nearly7DaysBusinessHours",
      "sellerId",
      "shopModelScore",
      "signatory",
      "isDirect",
      "isCityAgent",
      "deliveryTimelyRate",
      "orderPerformanceRate",
      "selfFetch",
      "shopLevel",
      "wholeCityDelivery",
      'serviceFeature',
      // 'servicePackage'
    ], // all
    "skuTrade": [
      "nearly7DaysOrders",
      "nearly30DaysOrders",
      "d7ShopValidOrderCnt",
      "d30ShopValidOrderCnt",
      "nearly7DaysTurnover",
      "nearly30DaysTurnover",
      "d7SaleAmt",
      "d15SaleAmt",
      "categoryOrderPermeability",
      "categorySkuCityPermeability",
      "category15DaySoldRate",
      'nearly7DaysMinHistoryTransactionPrice',
      'nearly15DaysMinHistoryTransactionPrice'
    ],
    "skuMarket": [
      "storeMarketRate",
      "goodsOriginalPrice",
      "goodsPresentPrice",
      "skuDiscount",
      "goodsActivitiesType",
      "itemDistribute",
      // 'shopPZLMString'
    ], // all
  },
}
