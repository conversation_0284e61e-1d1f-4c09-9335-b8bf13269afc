import React from 'react';
import { Form, Tab } from '@alife/next';
import { marketInfo, skuStoreInfo, skuTradeInfo, baseInfo, skuSceneInfo } from './commodity.meta';
import * as storemeta from './store.meta';

const insetLayout = {
  labelCol: { fixedSpan: 7, align: 'center' }
};

export const generate = (meta,queryField)=>generateUsingMeta(meta,queryField)

export function createCommodityMeta({basePoolId, filtering}, {marketingTypeOptions,newMarketingTypeOptions, categoryCascadeOptions, storeCascadeOptions, cityCascadeOptions, storeMainCascadeOptions, newStoreMainCascadeOptions,itemStatusOptions, manageorgListOptions, bizLineListOptions}){
  return {
    baseInfo: baseInfo({categoryCascadeOptions, basePoolId, filtering,storeMainCascadeOptions, newStoreMainCascadeOptions,itemStatusOptions, manageorgListOptions, bizLineListOptions}),
    skuStoreInfo: skuStoreInfo({ basePoolId, storeCascadeOptions, cityCascadeOptions, storeMainCascadeOptions, newStoreMainCascadeOptions, filtering}),
    skuTradeInfo: skuTradeInfo({ basePoolId, filtering }),
    marketInfo: marketInfo({basePoolId, filtering, marketingTypeOptions,newMarketingTypeOptions})
  }
}

export function createAliCommodityMeta({ basePoolId, filtering }, { marketingTypeOptions,newMarketingTypeOptions, categoryCascadeOptions, storeCascadeOptions, cityCascadeOptions, storeMainCascadeOptions,newStoreMainCascadeOptions, skuSceneInfoOptions,itemStatusOptions, groupActivityIdsTypesOptions, manageorgListOptions, bizLineListOptions }) {
  if (+basePoolId === 20001) {
    return {
      baseInfo: baseInfo({categoryCascadeOptions, basePoolId, filtering,itemStatusOptions, manageorgListOptions, bizLineListOptions}),
      itemStoreInfo: skuStoreInfo({ basePoolId, storeCascadeOptions,storeMainCascadeOptions,newStoreMainCascadeOptions, cityCascadeOptions, filtering, manageorgListOptions, bizLineListOptions}),
      itemTradeInfo: skuTradeInfo({ basePoolId, filtering }),
      marketInfo: marketInfo({basePoolId, filtering, marketingTypeOptions,newMarketingTypeOptions }),
      itemSceneInfo: skuSceneInfo({ basePoolId, skuSceneInfoOptions, filtering })
    }
  } else {
    return {
      baseInfo: baseInfo({categoryCascadeOptions, basePoolId, filtering,itemStatusOptions,groupActivityIdsTypesOptions, manageorgListOptions, bizLineListOptions}),
      itemStoreInfo: skuStoreInfo({ basePoolId, storeCascadeOptions,storeMainCascadeOptions,newStoreMainCascadeOptions, cityCascadeOptions, filtering, manageorgListOptions, bizLineListOptions}),
      itemTradeInfo: skuTradeInfo({ basePoolId, filtering }),
      marketInfo: marketInfo({basePoolId, filtering, marketingTypeOptions,newMarketingTypeOptions })
    }
  }
}

export function collectLabelMap(meta){
  // console.log(meta);
  let result = {}
  for (let key of Object.keys(meta)){
    let fields = meta[key].fields
    for (let fieldKey of Object.keys(fields)){
      let field = fields[fieldKey]
      result[fieldKey] = field.props.label
    }
  }
  return result
}


export function createStoreMeta({basePoolId, filtering}, {storeCascadeOptions, cityCascadeOptions, storeMainCascadeOptions, newStoreMainCascadeOptions, crowdAttributes, storeActivitiesTypeOptions, manageorgListOptions, bizLineListOptions}){
  if (basePoolId + '' === "10004" || basePoolId + '' === '30001' || basePoolId + '' === '30002' || basePoolId + '' === '30005') {
    return {
      baseInfo: storemeta.baseInfo({ storeCascadeOptions, cityCascadeOptions, storeMainCascadeOptions, newStoreMainCascadeOptions, basePoolId, filtering, manageorgListOptions, bizLineListOptions}),
      storeBrowseAndTradeInfo: storemeta.storeBrowseAndTradeInfo(),
      marketInfo: storemeta.marketInfo({basePoolId, filtering, storeActivitiesTypeOptions})
    }
  } else {
    return {
      baseInfo: storemeta.baseInfo({ storeCascadeOptions, cityCascadeOptions, storeMainCascadeOptions, newStoreMainCascadeOptions, basePoolId, filtering, manageorgListOptions, bizLineListOptions}),
      storeBrowseAndTradeInfo: storemeta.storeBrowseAndTradeInfo(),
      marketInfo: storemeta.marketInfo({basePoolId, filtering, storeActivitiesTypeOptions}),
      crowdAttributeInfo: storemeta.crowdAttributeInfo({basePoolId, filtering, crowdAttributes}),
    }
  }
}

function generateUsingMeta(meta,queryField) {
  return Reflect.ownKeys(meta).map((key, idx) => {
    let tab = meta[key]
    let tabFields = tab.fields

    let fields = Reflect.ownKeys(tabFields).map((fieldKey) => {
      let field = tabFields[fieldKey]
      // 一些字段在填写时不展示
      if (field.props.isHidden) {
        return null
      }
      return React.createElement(Form.Item, {
        ...insetLayout,
        ...field.props,
        key: fieldKey
      }, createInputElement(field.children, key, fieldKey))
    }).filter(Boolean);

    function inputKey(tabkey, fieldKey) {
      return `${key}.${fieldKey}`
    }

    function createInputElement(children, tabkey, fieldKey) {
      const { props, component } = children;
      let cprops = null
      cprops = {
        ...props,
        name: inputKey(tabkey, fieldKey),
        queryField
      }
      return React.createElement(component, cprops)
    }

    return <Tab.Item title={tab.tabName} key={idx}>
      <div className="tabcontent">
        {fields}
      </div>
    </Tab.Item>
  })
}

export function collectDataSources(meta) {
  let result = []
  Reflect.ownKeys(meta).map((key) => {
    let tab = meta[key]
    let tabFields = tab.fields

    Reflect.ownKeys(tabFields).map((fieldKey) => {
      let field = tabFields[fieldKey]
      const {children} = field;
      const {props: {dataSource}} = children;
      if (dataSource) {
        result.push({
          fieldKey,
          dataSource
        })
      }
    })

  })
  return result
}
