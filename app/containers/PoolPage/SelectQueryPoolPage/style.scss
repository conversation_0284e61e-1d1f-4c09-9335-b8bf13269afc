.page-CreateCommodityPoolPage {

  .nav-wrapper {
    padding: 20px 20px 0;
  }

  .next-tabs-content{
    padding: 20px 0;
  }
  .outer-form {
    margin-top: 60px;
    margin-bottom: 40px;

    .next-range-picker {
      width: 100%;
    }
  }

  .help {
    background-color: gray;
    border-radius: 50%;
    color: white;
    display: inline-block;
    width: 16px;
    text-align: center;
    height: 16px;
    line-height: 16px;
    margin-left: 2px;
  }

  .query-form{
    span.next-select{
      display: block;
    }
    .group-activities-types{
      overflow: hidden;
      span.next-select{
        min-width:110px;
        margin-right:5px;
      }
      span.next-select,span.next-input{
        float:left;
      }
      span.next-select-multiple,span.next-input{
        max-width:250px;
      }
      .next-form-item{
      }
      .red-mark{
        color: #FF2D4B;
        float:left;
        margin:8px 5px 0 0;
      }
      .next-form-item-help{
        color: #FF2D4B;
        width:100%;
        float:left;
        display:block;
        margin-left:10px;
      }
    }
  }

  div[name="baseInfo.createTime"]{
    width: 355px;
  }

  .tag-group {
    overflow: auto;
    max-height: 130px;

    .next-tag-closable > .next-tag-body {
      max-width: calc(100% - 20px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
    }

    .next-tag-closable {
      border:none;

      // &:hover {
      //   > .next-tag-close-btn{
      //     display: inline-block;
      //   }
      // }

      // > .next-tag-close-btn{
      //   display: none;
      // }
    }
    .tagbody{
      font-family:'PingFang SC' !important ;
      line-height:20px;
      padding: 0 12px 0 12px;
      color: #666;
      display:inline-block;
      margin-top:0px;
      margin-bottom:8px;
    }
    .taglabel{
      font-size:14px;
    }
    .tagvalue{
      color: #FF7C4D;
      font-size:14px;
    }
    .tagclose{
      margin-left:3px;
      cursor:pointer;
    }
  }

  .selected-items {
    padding: 20px;
    border: 1px solid #EBEBEB;

    .title {
      margin-bottom: 20px;
    }
  }

  .next-tag > .next-tag-body {
    text-overflow: unset;
    overflow: unset;
  }

  .tabcontent {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    >div{
      flex: 0 1 30%;
      margin-left: 1em;

      .next-form-item-label {
        line-height: 14px;
      }

    }
  }

  .pagination{
    margin-top: 20px;
    text-align: right;
  }


  .buttons {
    button {
      margin-right: 10px;
    }

    &.bottom {
      margin: 30px 0;
    }
  }

  .sort-bar{
    display: flex;
    justify-content: space-between;
    margin-top: 50px;
  }

  .error-bar{
    font-size: 14px;
    line-height: 14px;
    color: #666666;

    .row {
      border: 1px solid #FFD5CC ;
      background-color: #FFF3F0 ;
      border-radius: 4px;
      padding: 8px;
      margin-bottom: 4px;

      .err-msg {
        display: inline-block;
        white-space: nowrap;
        max-width: calc(100% - 15em);
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
      }
      span.red {
        color: #FF2F00;
      }
    }
  }
}
