import React from 'react';
import { env, ENV_ENUM } from '@/config';
import {Progress, Icon, Button, Breadcrumb, Form, Input, Upload, Field, Select, Radio, Checkbox, Message, Loading} from '@alife/next';
import { Link } from 'react-router-dom';
import { PageWrapper } from '@/components/PageWrapper';
import { Steps, PoolPageBase } from '../common';
import {getValueByUrlKey, promisify} from '@/utils/others';
import * as api from '@/utils/api';
import { config } from '@/config';
import './style.scss';
import * as validators from "@/utils/validators";
import {TimeRangePicker} from "@/components/TimeRangePicker";
import moment from "moment";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
}

const formLayout = {
  style: {
    margin: '20px auto'
  },
}

const idModeEnum = [
  {label:'商品ID',value:'ITEM_ID',type:'good'},
  {label:'商品条码',value:'ITEM_UPC',type:'good'},
  {label:'ELE门店ID',value:'SHOP_ELE_ID',type:'store'},
  {label:'淘内门店ID',value:'SHOP_ID',type:'store'},
]
const createModeEnum = [
  {label:'Excel上传',value:'CREATE_EXCEL'},
  // {label:'标签选品',value:'CREATE_RULE'}
]

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    if(start.isBefore(moment().startOf('day'))) return cb('开始时间不能小于当前时间')
    if (end.clone().subtract(4, 'month').isAfter(start.clone())) return cb('时间段不能超过4个月')
  }
  cb()
}

export class DataSetUploadPage extends PoolPageBase {
  constructor(props){
    console.log("con");
    super(props)
    this.state = {
      outSynPlatforms: [],
      fileName: '',
      fileUrl: '',
      defaultTime: [moment(), moment().add(1, 'months').set({hour: 23, minute: 59, second: 59})],
      defaultValue: [],
      isSubmit: false,
      canSubmit: true,
      isLoad:false
    }

    this.field = new Field(this, {});
  }

  get basePoolId() {
    return this.isStore ? '32001' : '22001';
  }

  get poolId() {
    return this.params.poolId
  }

  get isEdit(){
    return !!this.poolId
  }

  componentDidMount() {
    this.getSynPoolPlatformList();
    if(this.isEdit){
      this.setPool();
    }
  }

  componentWillReceiveProps(nextProps) {
    console.log(nextProps);
    this.field.reset();
    this.field.setValue("createMode", "CREATE_EXCEL");
    this.setState({defaultValue:[]})
  }

  async setPool(){
    try{
      let resp = await api.ali.getPoolDetail({id:this.poolId});
      this.field.setValues({
        poolId: resp.poolId,
        poolName: resp.poolName,
        effectRange:[moment(resp.effectAt), moment(resp.expireAt)],
        outSynPlatforms: resp.outSynPlatforms,
        createMode: resp.createMode,
        refreshMode: resp.refreshMode,
        fileName: resp.fileName,
        idMode: resp.idMode,
        fileUrl: resp.fileUrl
      })
      if(resp.fileUrl || this.field.getValue("fileUrl")){
        this.setState({
          fileUrl:resp.fileUrl,
          fileName:resp.fileName,
          defaultValue: [{
            uid: "0",
            name: this.field.getValue("fileName"),
            state: "done",
            url: this.field.getValue("fileUrl")
          }]
        })
      }
    }catch (error){
      api.onRequestError(error)
    }
  }

  async getSynPoolPlatformList() {
    try {
      let param = this.basePoolId;
      let resp = await api.getSynPoolPlatformList(param).then(api.onRequestSuccess);
      console.log(resp);
      resp =  [
        {label: "主站算法", platformCode: "zzsf"},
        {label: "零售算法", platformCode: "lssf"},
        {label: "口碑智库", platformCode: "kbzk"},
        {label: "大促打标", platformCode: "dcdb"}
      ]
      resp.forEach((respItem) => {
        respItem.value = respItem.platformCode
      })
      this.setState(() => ({ outSynPlatforms: resp }))
    } catch (error) {
      api.onRequestError(error)
    }
  }

  savePool = async () => {
    const outerForm = await (promisify(this.field.validate)());
    this.setState({
      isSubmit: true,
    })
    const {fileName, fileUrl, isSubmit} = this.state;
    if(fileUrl!="") {
      this.setState({
        isLoad:true
      })
      try {
        let data = {
          poolRule: {
            basePoolId: this.basePoolId,
          },
          poolId: this.poolId,
          poolName: outerForm.poolName,
          effectAt: outerForm.effectRange[0].valueOf(),
          expireAt: outerForm.effectRange[1].valueOf(),
          outSynPlatforms: outerForm.outSynPlatforms,
          createMode: outerForm.createMode,
          refreshMode: 0,
          fileName,
          idMode: outerForm.idMode,
          fileUrl
        }
        let request = this.isStore ? api.ali.saveAsStorePool : api.ali.saveAsSkuPool;
        let resp = await request(data).then(api.onRequestSuccess);
        this.setState({
          canSubmit:false
        })
        if (!this.isEdit && resp) {
          this.publishPool(resp);
        }else{
          this.skipList();
        }
      } catch (error) {
        this.setState({
          canSubmit:true,
          isLoad: false
        })
        api.onRequestError(error)
      }
    }
  }

  publishPool = async (id) => {
    let self = this;
    try {
      let resp = await api.ali.publishPool({isStore: this.isStore, id});
      console.log(resp);
      this.setState({
        isLoad: false,
        canSubmit: true
      },()=>{
        self.skipList();
      })
    }catch (error) {
      this.setState({
        canSubmit: true,
        isLoad: false
      })
      api.onRequestError(error)
    }
  }

  skipList = () =>{
    // this.history.push(`/${this.isStore? 'store' : 'commodity'}pool/list/progress/${id}?${this.encodeQuery({name, basePoolId: this.basePoolId, dataType:this.dataType })}`)
    // this.history.push(`/pool/list`)
    this.history.push(`/${this.isStore ? 'store' : 'commodity'}pool/list`)
  }

  onSuccess = (info) => {
    if (info.response.code == '200') {
      this.setState({
        fileName: info.response.data.fileName,
        fileUrl: info.response.data.url,
        defaultValue: [{
          uid: "0",
          name: info.response.data.fileName,
          state: "done",
          url: info.response.data.url
        }]
      })
    } else {
      Message.error(info.response.msg || '上传失败');
    }
  }

  render() {
    let {outSynPlatforms, defaultTime, defaultValue, fileUrl, isSubmit, canSubmit, isLoad} = this.state;
    const disabled = this.poolId;
    let curIdModeEnum = this.isStore ? idModeEnum.filter(v => v.type == 'store') : idModeEnum.filter(v => v.type == 'good');
    return (
      <Loading tip="请等待..." size="large" visible={isLoad}>
        <PoolPageBase.Container className="CommodityUploadPage">
          <PageWrapper title={`${this.isStore ? '门店池' : '商品池'}数据集上传`}>
            <Form {...formLayout} field={this.field}>
              <FormItem label="ID类型：" {...formItemLayout}>
                <Select name="idMode" disabled={disabled} defaultValue={curIdModeEnum[0].value} dataSource={curIdModeEnum} style={{width: '150px'}}/>
              </FormItem>
              <FormItem label="上传方式：" {...formItemLayout}>
                <RadioGroup name="createMode" defaultValue={createModeEnum[0].value} disabled={disabled} dataSource={createModeEnum}/>
              </FormItem>
              <FormItem label="导入文件：" required {...formItemLayout}>
                <div style={{margin: '7px 0 10px 0'}}>请先下载
                  {/*<a target="_blank" download="带说明模板.csv" href="https://files.alicdn.com/tpsservice/5e941be4b148f11a61b288f47c5829a2.csv">导入模板</a>，按照要求填写完成，上传到系统*/}
                  <a target="_blank" download="带说明模板.csv" href="https://files.alicdn.com/tpsservice/b4beb2dac1f28e0986ee47576ceb2270.xlsx">导入模板</a>，按照要求填写完成，上传到系统
                </div>
                <Upload.Dragger
                  headers={{'X-Requested-With': null}}
                  action={`${config.API_ROOT}/api/ali/common/upload`}
                  onSuccess={this.onSuccess}
                  disabled={disabled}
                  listType="text"
                  // name="fileUrl"
                  value={defaultValue}
                  // multiple
                  // accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, text/csv">
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                  // accept="text/csv"
                >
                  <div className="next-upload-drag">
                    <p className="next-upload-drag-icon">
                      <Icon type="upload"/>
                    </p>
                    <p className="next-upload-drag-text">
                      点击或将文件拖拽到这里上传
                    </p>
                    {/*<p className="next-upload-drag-hint">支持扩展名：.csv</p>*/}
                    <p className="next-upload-drag-hint">支持扩展名：.xlsx</p>
                  </div>
                </Upload.Dragger>
                {fileUrl == '' && isSubmit && <div style={{color: '#FF2D4B'}}>请上传文件</div>}
              </FormItem>
              <FormItem label="数据集名称：" required {...formItemLayout} requiredMessage="数据集名称必填">
                <Input name="poolName" disabled={disabled} placeholder='请输入数据集名称' style={{width: '350px'}}/>
              </FormItem>
              <FormItem
                label="有效期："
                {...formItemLayout}
                required
                requiredMessage="请输入时间段"
                validator={timeRangeValidator}
                validatorTrigger={['onChange']}
              >
                <TimeRangePicker defaultValue={defaultTime} showTime={true} name="effectRange"
                                 disabledDate={(date) => date.isBefore(moment().startOf('day'))}></TimeRangePicker>
              </FormItem>
              <FormItem label="适用场景：" {...formItemLayout}>
                <Checkbox.Group name="outSynPlatforms" disabled={disabled} dataSource={outSynPlatforms}/>
              </FormItem>
              <FormItem label=" " colon={false}>
                <Form.Submit
                  type="primary"
                  validate
                  onClick={this.savePool}
                  disabled={!canSubmit}
                  style={{marginLeft: 200}}>
                  {!this.isEdit ? '发布' : '保存'}
                </Form.Submit>
              </FormItem>
            </Form>
          </PageWrapper>
        </PoolPageBase.Container>
      </Loading>
    );
  }
}
