/*
 * HomePage
 *
 * This is the first thing users see of our App, at the '/' route
 */

import './style.scss'

import React, { Fragment } from 'react';
import { PageWrapper } from '@/components/PageWrapper';
import { <PERSON><PERSON>, Breadcrumb, Dialog} from '@alife/next';
import { Link } from 'react-router-dom';
import { connectRedux } from './decorator';
import {ACLAccess} from '@/components/ACLAccess'
import { Steps, PoolPageBase } from '../common';
import { track } from '@/utils/aplus';
import * as api from '@/utils/api';

@connectRedux
export class SelectBasePoolPage extends PoolPageBase {
  constructor(props) {
    super(props)

    this.state = {
      rows: [],
    }
    this.isInvite = this.props.match.path === "/storepoolInvite/creation";
  }

  judgeAccess = async(poolId,callback) =>{
    let resp = await api.permission.checkPool(poolId);
    let {rediectUrl} = resp.data.data;
    this.setState({
      rediectUrl
    },()=>{
      if(rediectUrl){
        Dialog.confirm({
          title: '申请权限',
          footer: false,
          content: [<ACLAccess rediectUrl={rediectUrl}/>],
        })
      }else{
        callback();
      }
    });
  }

  onNext = (basePoolId, basePoolName, dataType, isUpload) => {
    let self = this;
    if (isUpload) {
      self.props.history.push("/storepoolInvite/fileUpload")
    }else{
      this.judgeAccess(`-${basePoolId}`,function () {
        track('clickEvent', ['/selection_kunlun.CREATE-POOL-choose-base-pool.create-btn', `basePoolId=${basePoolId}&poolType=${self.isStore ? 2 : 1}`]);
        self.props.goStep2({basePoolId,basePoolName, dataType, isStore: self.isStore, isInvite: self.isInvite})
      });
    }
  }

  componentDidMount(){
    this.getPoolList()
    localStorage.setItem('config_start', Date.now());
  }

  async getPoolList() {
    try {
      let resp = await api.getBasePoolList(this.isStore ? 2 : 1,this.isInvite).then(api.onRequestSuccess)
      let level1Rows = resp.rows.filter(row => row.level === 1);
      let level2Rows = resp.rows.filter(row => row.level === 2)
      let level3Rows =  [{
        id: "32005",
        name: "招商专用文件上传",
        description: "仅支持招商平台通过文件上传批量导入门店，其他不可用。",
        attention:"注：不支持超会运费满减活动",
        level: 1,
        levelTitle: null,
        tag: null,
        index: 3,
        isUpload:true
      }];
      this.setState({
        rows: [level1Rows, level2Rows, level3Rows]
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  render() {
    const { rows } = this.state;
    const middleText = this.isStore ? '创建门店池' : '创建商品池';
    const rowTitles = this.isStore ? [] : ['商品基础池', '商品细分场景池'];
    return (
      <PoolPageBase.Container className="CreateCommodityPoolPage">
        {this.isInvite&&<div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item><Link to={`/${this.isStore ? 'store' : 'commodity'}poolInvite/list`}>{`管理${this.isStore ? '门店' : '商品'}池`}</Link></Breadcrumb.Item>
            <Breadcrumb.Item> {middleText} </Breadcrumb.Item>
          </Breadcrumb>
        </div>}
        <PageWrapper title={`创建${this.isStore ? '门店' : '商品'}池`}>
          <Steps current={0} middleText={middleText} />
          <div className="divider"></div>
          {
            rows.map((level, index) => {
              return <Leval key={index} rowTitles={rowTitles} level={level} index={index} onClick={this.onNext} isInvite={this.isInvite}/>
            })
          }
        </PageWrapper>
      </PoolPageBase.Container>
    )
  }
}

function Leval ({rowTitles, level, index, onClick, isInvite}) {
  return <Fragment>
    {!!level.length && !!rowTitles[index] && <div className="level-title">{rowTitles[index]}</div>}
    <section className="items">
      {
        level.map((e) => {
          //todo 换成真实从record里面的数据
          let dataType = (parseInt(e.id) >= 20001) ? 1 : 2;
          let poolGroup = ["10001","10004","10006","10009","10013","20001","30001","20004","20005","20006","30004","30005","30006",'31004', '32005']; //T+1的池子
          // let timeliness = (e.id != '10001' && e.id != '10004' && e.id != '10006' && e.id != '10009' && e.id != '10013') ? '实时' : "T+1";
          let timeliness = (!poolGroup.includes(e.id)) ? '实时' : "T+1";
          return (
            <Card key={e.id} title={e.name} desc={e.description} attention={e.attention} timeliness={timeliness} onClick={() => onClick(e.id,e.name,dataType,e.isUpload)} />
          )
        })
      }
    </section>
  </Fragment>
}

function Card({ timeliness, title, desc, onClick, active, attention }) {
  return <div className={`card ${active ? 'active' : ''}`}>
    <div className="title"><span className="timeliness">{timeliness}</span>{title}</div>
    <div className="desc">{desc}</div>
    {attention && <div className="attention" style={{color:'red'}}>{attention}</div>}
    <div className="btns">
      <Button onClick={onClick} className="create_btn">创建</Button>
    </div>
  </div>
}

export class SelectBasePoolPageInvite extends SelectBasePoolPage {}
