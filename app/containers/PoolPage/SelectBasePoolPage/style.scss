@import '../../../styles/common.scss';

.CreateCommodityPoolPage {
  .nav-wrapper {
    padding: 20px 20px 0;
  }

  .page-wrapper{
    width: 860px;
    margin: 0 auto;
  }

  .main-content-wrapper {
    width: 664px;
    margin: 0 auto;
  }

  .step-wraper {
    margin-top: 10px;
    margin-bottom: 28px;
  }

  .divider {
    width: calc(100% + 40px);
    height: 10px;
    margin-left: -20px;
    background: #FAFAFA;

  }

  .level-title {
    font-size: 18px;
    color: #333333;
    line-height: 18px;
    margin:20px 0 0 0;
    padding-left: 4px;
    border-left: 2px solid #FF9500;
  }

  .items {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
    margin: 10px 55px 0;
  }

  .item {
    display: flex;
    margin-bottom: 40px;

    &:last-child{
      margin-bottom: 30px;
    }

    label {
      font-size: 16px;
      color: #333333;
      line-height: 16px;
      font-weight: bold;
      margin-top: 2px;
    }
    .desc {
      margin-left: 8px;
      color: #999999;
    }
  }
  .buttons {
    button {
      margin-right: 10px;
    }

    &.bottom {
      margin: 30px 0;
    }
  }

  .card {
    padding:15px;
    font-size: 14px;
    color: #333333;
    width: 320px;
    min-width: 320px;
    height: 174px;
    background-image: url('./assets/bg.png');
    background-repeat: no-repeat;
    background-position: bottom right;
    overflow-y: hidden;
    display: inline-block;
    margin: 10px 15px;
    // border: 2px solid $line_common;
    box-shadow: 1px 1px 2px 1px $line_common;
    position: relative;

    .create_btn{
      background-color: $fill_common;
      color: $link_main;
      border-color: $link_main;
    }

    &:hover {
      background-color: #fafafa;

      .create_btn{
        background-color: $link_main;
        color: white;
      }
    }

    &:last-child {
      margin-right: 0;
    }

    .title {
      margin-bottom: 14px;
      font-size: 15px;
      color: $font_main_height;
      font-family:$font-medium;
      .timeliness {
        font-family: $font-regular;
        background: $fill_common;
        border: 1px solid $link_main;
        border-radius: 4px;
        padding: 3px 6px;
        font-size: 12px;
        color: $link_main;
        margin-right: 8px;
      }
    }

    .desc {
      height: 3.4em;
      color: $font_main_light;
    }

    .btns {
      //text-align: right;
      //margin-top: 20px;
      position: absolute;
      right:15px;
      bottom:10px;
    }
  }
}
