import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import { selectors, reducer, constants, actions } from '../common';
import injectReducer from '@/utils/injectReducer';
import injectSaga from '@/utils/injectSaga';
import { saga } from './saga';

const mapDispatchToProps = (dispatch) => ({
  goStep2({basePoolId,basePoolName,dataType, isStore, isInvite}){
    dispatch(actions.goStep2({basePoolId,basePoolName,dataType, isStore, isInvite}))
  },
});

const mapStateToProps = createStructuredSelector({
  value: selectors.makeBasePoolIdSelector()
});

const withConnect = connect(mapStateToProps, mapDispatchToProps);
const withReducer = injectReducer({ key: constants.REDUCER_KEY, reducer: reducer.rootReducer });
const withSaga = injectSaga({ key: 'basepool', saga });

export const connectRedux = compose(withSaga, withReducer, withConnect)
