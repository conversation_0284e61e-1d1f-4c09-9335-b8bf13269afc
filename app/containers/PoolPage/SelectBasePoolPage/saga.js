import * as qs from 'query-string';

import { put, takeLatest } from 'redux-saga/effects';
import { actions, constants } from '../common';
import { push } from 'connected-react-router'

export function* rootSaga({payload}) {
  const {basePoolId, basePoolName, isStore, dataType, isInvite} = payload;
  yield put(actions.resetStep2Form())
  yield put(actions.updateBasePoolId(basePoolId))
  yield put(push(`/${isStore? 'store' : 'commodity'}pool${isInvite?"Invite":""}/creation/query?${qs.stringify({basePoolId,basePoolName,dataType})}`))
}

/**
 * Root saga manages watcher lifecycle
 */
export function* saga() {
  yield takeLatest(constants.GO_STEP_2, rootSaga);
}
