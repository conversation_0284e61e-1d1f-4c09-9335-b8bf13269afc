import React from 'react';
import {Form, Input, Grid, Select, Button, Field, Table, Pagination, Dialog,Tab} from '@alife/next';
import { Link } from 'react-router-dom';
import {PageWrapper} from '@/components/PageWrapper';
import * as api from '@/utils/api';
import * as apiL from "@/adator/api";
import {statusRender, formLayout, formItemLayout, createRuleRender, createSkuRuleRender, timeRangeRender, dateRender} from '../common';
import {sleep, promisify} from '@/utils/others';
import {PageBase} from '@/containers/base';
import {PoolPageBase} from '@/containers/PoolPage/common';
import {DialogBtn, DialogBtnDecoration} from '@/components/Button';
import {validators} from '../../SelectQueryPoolPage/queryfield/common/tools';
import {track} from '@/utils/aplus';
import {ACLAccess} from '@/components/ACLAccess'
import {PAGE_SIZE} from '@/constants';

const FormItem = Form.Item;
const {Row, Col} = Grid;
const {Option} = Select;

function NameInput(props) {
  return (
    <Form field={props.field}>
      <FormItem><Input name='exportName' placeholder={`文件名：${props.defaultFileName}`}/></FormItem>
    </Form>
  )
}
const createModeObject =
  {
    'Excel上传': 'Excel上传',
    'CREATE_EXCEL': 'Excel上传',
    '标签选品': '标签选品',
    'CREATE_RULE': '标签选品'
  }


const ExportInput = DialogBtnDecoration(NameInput)

export class BasePoolListPage extends PoolPageBase {
  field = new Field(this)

  constructor(props) {
    super(props)
    this.state = {
      isLoading: false,
      rows: [],
      pagination: {
        page: 1,
        total: 0,
        size: PAGE_SIZE,
      },
      onlyMine:1
    }
  }

  get columns() {
    const columns = [
      {title: '选品集ID', dataIndex: 'poolId', width: '100px'},
      {title: '商品池名称', dataIndex: 'poolName', width: '150px'},
      {
        title: '筛选规则', dataIndex: 'filterRules', width: '200px', cell: (value, index, record) => {
          return <>
            {createSkuRuleRender(value, this.props.queryMeta, false, record)}
          </>
        }
      },
      {
        title: '类型', dataIndex: 'createMode', width: '120px', cell: (v) => {
          return <span>{createModeObject[v]}</span>
        }
      },
      {
        title: '适用场景', dataIndex: 'synPlatform', width: '100px', cell: (value, index, record) => {
          return <span>{value ? value.join(",") : ''}</span>
        }
      },
      {title: '商品数量', dataIndex: 'num', width: '100px'},
      {title: '生效时间', cell: timeRangeRender, width: '180px'},
      {title: '状态', dataIndex: 'poolState', cell: statusRender, width: '100px'},
      {title: '操作时间', dataIndex: 'operateTime', cell: dateRender, width: '160px'},
      {title: '创建人', dataIndex: 'creater', width: '150px'},
      {title: '更新方式', dataIndex: 'refreshMode', width: '120px'},
      {
        title: '操作', lock: 'right', cell: (value, index, record) => {
          const {poolState} = record;
          return <div className="opbtns">
            {this.renderOption(poolState, record)}
          </div>
        },
        width: '120px'
      }
    ]

    return columns
  }

  get SPMCode() {
    return ''
  }

  renderOption(status, record) {
    const {basePoolId} = record.filterRules ? JSON.parse(record.filterRules) : '';
    const showSelectionPool =  (basePoolId != '20009' && basePoolId != '20013' && basePoolId != '40002' && basePoolId!='31009');
    const isExcelType = (record.createMode == 'CREATE_EXCEL');
    const options = [
      <Button type="primary" text={true} onClick={() => this.onView(record)}>查看</Button>
      // <Button type="primary" text={true} onClick={() => this.onExport(record)}>导出</Button>
    ];
    if (showSelectionPool) { //非选投平台建的池子不能删除
      options.push(<Button type="primary" text={true} onClick={() => this.onRemove(record)}>删除</Button>)
    }
    if (showSelectionPool && basePoolId != '40001' && basePoolId != '20012' && !isExcelType) { //todo 后续百亿补贴支持导出，再放开
      options.push(<Button type="primary" text={true}>
        <ExportInput
          title="确定将数据导出为csv文件？"
          defaultFileName={`${record.poolId}-${record.poolName}`}
          onOk={(field) => this.onOkExport(field, record)}
        >
          导出
        </ExportInput>
      </Button>)
    }

    if (showSelectionPool && record.dataType === 1 && !isExcelType) {
      options.push(<Button type="primary" text={true} onClick={() => this.onCopy(record)}>复制</Button>)
    }

    // 通过对接神兵产生的池子不能编辑。这种池子的basePoolId是40003.
    if (showSelectionPool && status !== 25 && status !== 5 && basePoolId !== '40003') {
      [<Button type="primary" text={true} onClick={() => this.onEdit(record)}>编辑</Button>].map(item => options.push(item))
    }

    if (status === 0 || status === 1) {
      options.push(<Button type="primary" text={true} onClick={() => this.onPublish(record)}>发布</Button>)
    }

    if (status === 20) {
      options.push(<Button type="primary" text={true} onClick={() => this.onRepublish(record)}>重新发布</Button>) //
    }

    if (showSelectionPool && (status === 10 || status === 15)) {
      options.push(
        <DialogBtn onOk={() => this.offLine(record)} content={`确认下线该${this.isStore ? '门店' : '商品'}池?`}>
          <Button type="primary" text={true}>下线</Button>
        </DialogBtn>)
    }
    return options
  }

  async sendRequest(payload,onlyMine) {
    // let resp = await api.getSkuPoolList(payload).then(api.onRequestSuccess)
    let resp = await api.ali.getSkuPoolList(payload,onlyMine).then(api.onRequestSuccess)  //直接换接口
    return {
      rows: resp.rows, total: resp.total
    }
  }

  componentDidMount() {
    this.load()
    this.props.initQueryMeta();
    //track
    track('setSpm', [this.SPMCode]);

    if (this.isInvite) {
      let wrapper = document.querySelector(".app-wrapper");
      wrapper.classList.add("invite")
    }
  }

  offLine = async (record) => { //直接替换接口
    //goldlog: MANAGE-LIST
    const self = this;
    this.judgeAccess(record.poolId,async function () {
      track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.offline-pool-btn', `poolId=${record.poolId}`])
      // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-offline'])
      let id = self.getRecordId(record);
      if (record.createMode == "CREATE_EXCEL" || record.createMode == "Excel上传") {
        try {
          await apiL.offLinePool(id);
          self.load()
        } catch (error) {
          api.onRequestError(error)
        }
      }else{
        let request = (self.getIsAli(record)) ? api.ali.poolOffLine : api.poolOffLine;
        request(id)
          .then(api.onRequestSuccess)
          .then(() => {
            self.toastSuccess();
            self.load()
          }).catch(api.onRequestError)
      }
    });
  }

  judgeAccess = async(poolId,callback) =>{
    let resp = await api.permission.checkPool(poolId);
    let {rediectUrl} = resp.data.data;
    this.setState({
      rediectUrl
    },()=>{
      if(rediectUrl){
        Dialog.confirm({
          title: '申请权限',
          footer: false,
          content: [<ACLAccess rediectUrl={rediectUrl}/>],
        })
      }else{
        callback();
      }
    });
  }

  onEdit = async (record) => {
    let self = this;
      this.judgeAccess(record.poolId,function () {
        let id = self.getRecordId(record)
        // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-edite'])
        if (record.createMode == "CREATE_EXCEL" || record.createMode == "Excel上传") {
          self.props.history.push(`/storepoolInvite/fileUpload/${id}`)
        }else{
          track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.edit-pool-btn', `poolId=${id}`])
          self.props.setDetail({id, isStore: self.isStore, type: 'edit', from: self.isInvite?'invite':"", dataType: self.getDataType(record),basePoolName:record.basePoolName,createMode:record.createMode})
        }
      });
  }

  onCopy = async (record) => {
    let id = this.getRecordId(record)
    // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-copy'])
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.copy-pool-btn', `poolId=${id}`])
    if (record.createMode == "CREATE_EXCEL" || record.createMode == "Excel上传") {
      this.props.history.push(`/storepoolInvite/fileUpload/${-id}`)
    }else{
      this.props.setDetail({id, isStore: this.isStore, type: 'copy',from: this.isInvite?'invite':"",dataType:this.getDataType(record),basePoolName:record.basePoolName,createMode:record.createMode})
    }
  }

  onView = async (record) => {
    // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-detail'])

    let id = this.getRecordId(record)

    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.view-pool-btn', `poolId=${id}`])

    if (record.createMode == "CREATE_EXCEL" || record.createMode == "Excel上传") {
      let query = {
        basePoolId: JSON.parse(record.filterRules).basePoolId,
        name: record.poolName,
        canModify: record.canModify,
        poolState: record.poolState,
        dataType: this.getDataType(record),
        createMode: record.createMode,
        hiddenNav:true,
        isSelectedStore:true
      }
      let path = `/pool/list/detail/${id}?${this.encodeQuery(query)}`
      this.history.push(path)
    }else{
      let query = {
        basePoolId: JSON.parse(record.filterRules).basePoolId,
        name: record.poolName,
        canModify: record.canModify,
        poolState: record.poolState,
        dataType: this.getDataType(record),
        createMode: record.createMode
      }
  
      let path = `/${this.isStore ? 'store' : 'commodity'}pool${this.isInvite?"Invite":""}/list/detail/${id}?${this.encodeQuery(query)}`
      this.history.push(path)
    }
    

  }

  //重新发布
  onRepublish = (record) => {
    // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-republish'])
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.re-publish-pool-btn', `poolId=${record.poolId}`])
    this.onPublish(record)
  }

  onPublish = async (record) => {
    let self = this;
    this.judgeAccess(record.poolId, async function () {
      track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.publish-pool-btn', `poolId=${record.poolId}`])
      // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-publish'])
      let id = self.getRecordId(record)
      if (record.createMode == "CREATE_EXCEL" || record.createMode == "Excel上传") {
        let req = await apiL.rePublishPool(id);
        self.load({page: self.state.pagination.page})
      }else{
        let query = {
          basePoolId: JSON.parse(record.filterRules).basePoolId,
          name: record.poolName,
          dataType: self.getDataType(record)
        }
        let request = (self.getIsAli(record) ? (api.ali.publishPool) : api.publishPool);
        console.log(request);
        await request({isStore: self.isStore, id});
        self.history.push(`/${self.isStore ? 'store' : 'commodity'}pool${self.isInvite?"Invite":""}/list/progress/${id}?${self.encodeQuery(query)}`)
      }
    })
  }

  onRemove = (record) => {
    let self = this;
    this.judgeAccess(record.poolId, async function () {
      track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.delete-pool-btn', `poolId=${record.poolId}`])
      // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-delete'])
      Dialog.confirm({
        title: '提示',
        content: '确定删除此条数据么?',
        onOk: async () => {
          let id = self.getRecordId(record)
          // let request = this.isStore ? api.removeStorePool : api.removeSkuPool;
          if (record.createMode == "CREATE_EXCEL" || record.createMode == "Excel上传") {
            try {
              await apiL.deletePool(id)
              self.toastSuccess()
              self.load({page: self.state.pagination.page})// reload
            } catch (error) {
              api.onRequestError(error)
            }
          }else{
            let request = (self.getIsAli(record)) ? (api.ali.removePool) : (self.isStore ? api.removeStorePool : api.removeSkuPool);
            try {
              await request(id).then(api.onRequestSuccess)
              self.toastSuccess()
              self.load() // reload
            } catch (error) {
              api.onRequestError(error)
            }
          }
        }
      });
    });
  }

  /**导出 */
  onOkExport = (field, record) => {
    let self = this;
    this.judgeAccess(record.poolId, async function () {

      track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.export-pool-btn', `poolId=${record.poolId}`])
      // track('clickEvent', ['/selection_kunlun.MANAGE-LIST.manage-list-export'])
      const poolId = self.getRecordId(record)
      const fileName = field.getValue('exportName') || `${record.poolId}-${record.poolName}`;
      console.log(fileName);
      const {isStore} = self;
      const {basePoolId} = JSON.parse(record.filterRules);
      let request = (self.getIsAli(record)) ? (api.ali.exportFile) : (api.exportFile);
      request({isStore: (isStore && ![10013,30004,30006,31004].includes(+basePoolId)), fileName, poolId})
        .then(
          (data) => self.history.push(`/${self.isStore ? 'store' : 'commodity'}pool${self.isInvite?"Invite":""}/list/exportProgress?${self.encodeQuery({data})}`)
        );
    });
  }

  onSeekResult = async (record) => {
    let id = this.getRecordId(record);
    window.open(`https://market.${window.CONFIG.ENV == 'prod' ? 'm' : 'wapa'}.taobao.com/app/eleme-xcy-fed/select-circle/index.html#/select-circle/outer/profile/${id}`);
  }

  getRecordId(record) {
    return record.poolId
  }

  getDataType(record){
    return record.dataType
  }

  getIsAli(record) { //todo 联调换record的字段
    return record.dataType == 1;
  }

  async toastSuccess() {
    this.toast('操作成功')
    await sleep(3000)
  }

  async load({page, size, query} = {}) {
    this.field.validate()
    const {pagination,onlyMine} = this.state;
    page = page || pagination.page
    size = size || pagination.size
    query = query || await (promisify(this.field.validate)())

    let payload = {page, size, query}
    try {
      let {rows, total} = await this.sendRequest(payload,onlyMine)
      this.setState({
        rows,
        pagination: {
          page, size, total
        }
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  getQuery() {
    return this.field.getValues()
  }

  onPageChange = (page) => {
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.page-change']);
    this.load({page})
  }

  onPageSizeChange = (size) => {
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.page-size-change']);
    this.load({size})
  }

  onQueryClicked = () => {
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.search-pool-btn']);
    this.load({page: 1})
  }

  changeTab = (val) => {
    let {pagination} = this.state;
    pagination.page = 1;
    pagination.size = PAGE_SIZE;
    this.setState({
      onlyMine: val,
      pagination
    }, () => {
      this.load();
    })
  }

  render() {
    const {rows, isLoading, pagination} = this.state;
    const tabGroup = [
      {title:'我创建的',key:1},
      {title:'已申请的',key:2},
      {title:'可查看的',key:3},
    ]
    return <PageBase.Container className="CommodityPoolListPage">
      {this.isInvite&&<Button className="new-btn"><Link to='/storepoolInvite/creation'>新建门店池</Link></Button>}
      <PageWrapper title={`管理${this.isStore ? '门店' : '商品'}池`}>
        <Form {...formLayout} field={this.field}>
          <Row>
            <Col>
              <FormItem label="选品集ID" {...formItemLayout} validator={validators.idCommaSeperated}>
                <Input placeholder="请输入选品集ID" name="poolId"/>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="名称" {...formItemLayout}>
                <Input placeholder="请输入商品池名称" name="poolName"/>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="创建人" {...formItemLayout}>
                <Input placeholder="请输入创建人" name="creater"/>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="状态" {...formItemLayout}>
                <Select placeholder="请选择状态" style={{width: '100%'}} name="state">
                  <Option value="">全部</Option>
                  <Option value="0">未发布</Option>
                  <Option value="1">需要发布</Option>
                  <Option value="5">发布中</Option>
                  <Option value="10">待生效</Option>
                  <Option value="15">已发布</Option>
                  <Option value="20">发布失败</Option>
                  <Option value="25">已下线</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <FormItem label="圈选方式" {...formItemLayout}>
                <Select placeholder="请选择圈选方式" style={{width: '100%'}}  name="createMode" dataSource={[{label:"Excel上传",value:"CREATE_EXCEL"},{label:"标签选品",value:"CREATE_RULE"}]}/>
              </FormItem>
            </Col>
            <Col span={18} style={{textAlign:"right"}}>
              <Button type="primary" onClick={this.onQueryClicked}>查询</Button>&nbsp;&nbsp;
              <Form.Reset type="normal">重置</Form.Reset>
            </Col>
          </Row>
        </Form>
        <Tab animation={false} shape="wrapped" activeKey={this.state.onlyMine} onChange={this.changeTab}>
          {tabGroup.map((v) => {
            return <Tab.Item title={v.title} key={v.key}>
              <Table dataSource={rows} loading={isLoading} width="1000px" hasBorder={false} primaryKey="poolId">
                {
                  this.columns.map((e, idx) => {
                    return <Table.Column {...e} key={idx}/>
                  })
                }
              </Table>
              <div className="pagination">
                <Pagination
                  popupProps={{align: 'bl tl'}}
                  pageSizeList={[20, 50, 100]}
                  current={pagination.page}
                  total={pagination.total}
                  pageSize={pagination.size}
                  onChange={this.onPageChange}
                  totalRender={() => `共${pagination.total}条`}
                  pageSizeSelector="dropdown" pageSizePosition="end"
                  onPageSizeChange={this.onPageSizeChange}/>
              </div>
            </Tab.Item>
          })}
        </Tab>

      </PageWrapper>
    </PageBase.Container>
  }
}
