import React from 'react';
import { BasePoolListPage } from "./shell";
import { connectRedux } from '../decorator';
import * as api from '@/utils/api';
import {createRuleRender, statusRender, timeRangeRender, dateRender} from '../common';

const createModeObject =
  {
    'Excel上传': 'Excel上传',
    'CREATE_EXCEL': 'Excel上传',
    '标签选品': '标签选品',
    'CREATE_RULE': '标签选品'
  }


@connectRedux
export class StoreListPage extends BasePoolListPage {
  get columns() {
    const columns = [
      { title: '选品集ID', dataIndex: 'poolId', width:'100px' },
      { title: '门店池名称', dataIndex: 'poolName', width:'150px' },
      {
        title: '筛选规则', dataIndex: 'filterRules', width: '200px', cell: (value, index, record) => {
          return <>
            {createRuleRender(value,this.props.queryMeta, true,record)}
          </>
        }
      },
      {
        title: '类型', dataIndex: 'createMode', width: '120px', cell: (v) => {
          return <span>{createModeObject[v]}</span>
        }
      },
      {
        title: '适用场景', dataIndex: 'synPlatform', width: '100px', cell: (value, index, record) => {
          return <span>{value ? value.join(",") : ''}</span>
        }
      },
      { title: '门店数量', dataIndex: 'num', width:'100px'},
      { title: '生效时间', cell: timeRangeRender, width:'180px'},
      { title: '状态', dataIndex: 'poolState',cell: statusRender, width:'100px'},
      { title: '操作时间', dataIndex: 'operateTime', cell: dateRender, width:'160px' },
      { title: '创建人', dataIndex: 'creater', width: '180px'},
      {title: '更新方式', dataIndex: 'refreshMode', width: '120px'},
      {
        title: '操作', lock: 'right', cell: (value, index, record) => {
          const { poolState } = record;
          return <div className="opbtns">
            {this.renderOption(poolState, record)}
          </div>
        },
        width:'120px'
      }
    ]

    return columns
  }

  get SPMCode() {
    return '12950779'
  }

  async sendRequest(payload,onlyMine) {
    // let resp = await api.getStorePoolList(payload).then(api.onRequestSuccess)
    let resp = await api.ali.getStorePoolList(payload,onlyMine).then(api.onRequestSuccess)  //直接换接口

    return {
      rows: resp.rows, total: resp.total
    }
  }
}
