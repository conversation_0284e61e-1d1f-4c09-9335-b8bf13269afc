import React from 'react';
import { BasePoolListPage } from "./shell";
import { connectRedux } from '../decorator';
import * as api from '@/utils/api';
import { request } from "@/utils/request";
import { createRuleRender, statusRender, timeRangeRender, dateRender } from '../common';

@connectRedux
class StoreListPageInviteInner extends BasePoolListPage {
  get columns() {
    const columns = [
      { title: '选品集ID', dataIndex: 'poolId', width:'9%' },
      { title: '门店池名称', dataIndex: 'poolName', width:'9%' },
      { title: '圈选方式', dataIndex: 'createMode', width:'8%' },
      { title: '筛选规则', dataIndex: 'filterRules', cell: (value, index, record) => {
        return <>
          {createRuleRender(value, this.props.queryMeta, true,record)}
        </>
      }},
      { title: '门店数量', dataIndex: 'num', width:'8%'},
      { title: '生效时间', cell: timeRangeRender, width:'16%'},
      { title: '状态', dataIndex: 'poolState',cell: statusRender, width:'8%'},
      { title: '操作时间', dataIndex: 'operateTime', cell: dateRender, width:'16%' },
      { title: '创建人', dataIndex: 'creater', width:'9%'},
      {
        title: '操作', cell: (value, index, record) => {
          const { poolState } = record;
          return <div className="opbtns">
            {this.renderOption(poolState, record)}
          </div>
        },
        width:'10%'
      }
    ]

    return columns
  }

  get SPMCode() {
    return '12950779'
  }

  get isInvite() {
    return true;
  }

  async sendRequest(payload,onlyMine) {
    let resp = await api.ali.getStorePoolList(payload,onlyMine,true).then(api.onRequestSuccess)  //直接换接口

    return {
      rows: resp.rows, total: resp.total
    }
  }
}

export class StoreListPageInvite extends React.Component {
  componentDidMount() {
    if (window.configEnv === "prod") {
      window.top.location.href =
        "https://invite.kunlun.alibaba-inc.com/#/selection/poolPage/poolList";
    } else if (window.configEnv === "ppe") {
      window.top.location.href =
        "https://pre-invite.kunlun.alibaba-inc.com/#/selection/poolPage/poolList";
    }
  }

  render() {
    return null;
  }
}
