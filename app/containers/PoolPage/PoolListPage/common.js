
import React from 'react';
import { humanizeQueryForm,dealQualityScoreToArray } from '@/containers/PoolPage/SelectQueryPoolPage/common';
import moment from 'moment';
import { formatTimeStamp, FORMAT } from '@/utils/time';
import { StatusLabel } from '@/components/Label';
import * as api from '@/utils/api';
import {  flatten } from '@/utils/others';
import * as actions from "../../App/actions";
import { alipayRelIdSourceCommodity } from '../SelectQueryPoolPage/common';

const defaultColType = { title: '', align: 'center' }


export function getCol(type) {
  return { ...defaultColType, ...type }
}

export const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol:{span: 16},
  style: {
    width: "100%",
  },
};

export const formLayout = {
  style: {
    margin: '20px 0px'
  },
}

export const publishStatusEnum = {
  UNPUBLISHED: '0', //未发布
  TWOUNPUBLISHED: '1', //需要发布
  PROCESSING: '5', //发布中
  SUCCESS: '15', //发布成功
  WAITING_SUCCESS: '10', //待生效
  FAILED: '20',//发布失败
  OFF_LINE:'25' //已下线
}

export function statusRender(status){
  let m = {
    [publishStatusEnum.UNPUBLISHED]: '未发布',
    [publishStatusEnum.TWOUNPUBLISHED]: '需要发布',
    [publishStatusEnum.PROCESSING]: '发布中',
    [publishStatusEnum.SUCCESS]: '已发布',
    [publishStatusEnum.WAITING_SUCCESS]: '待生效',
    [publishStatusEnum.FAILED]: '发布失败',
    [publishStatusEnum.OFF_LINE]: '已下线'
  }

  // let c = {
  //   15: 'green', 25: 'gray', 0: 'orange', 20: 'red', 5:'yellow', 10:'blue'
  // }

  let typeMap = {
    0:'warning',
    5:'notice',
    15:'success',
    10:'help',
    20:'error',
    25:'filed'
  }
  return <StatusLabel text={m[status]} type={typeMap[status]}></StatusLabel>
}

export const idModeEnum = {
  "ITEM_ID":"商品ID",
  "ITEM_UPC":"商品条码",
  "SHOP_ELE_ID":"ELE门店ID",
  "SHOP_ID":"淘内门店ID"
}

export const groupActivityIdsTypesEnum = {
  'marketing': '营销活动ID',
  'brand': '品牌活动ID',
  'invite': '招商活动ID',
  'mktType': '营销活动类型'
}

export function formatRule(rule, queryMeta, isStore,record){
  if (!queryMeta) return '---'
  if (!rule) return '---'
  rule = JSON.parse(rule)
  const {labelMap} = queryMeta;
  if(rule.baseInfo && rule.baseInfo.itemPicQualityScore && !rule.baseInfo.itemPicQualityScore.length) { // 恶心的商品质量分
    rule.baseInfo.itemPicQualityScore = dealQualityScoreToArray(rule.baseInfo.itemPicQualityScore);
  }
  if (!(isStore && getNoTranform(rule.basePoolId))) { //店和品的支付宝场景指标重名了，需要特殊处理一下
    labelMap.others.alipayRelId.valueMap = flatten(alipayRelIdSourceCommodity);
  }
  let activityTypeGoodLabel = "商品活动类型"; //直接从字段取
  let activityTypeStoreLabel = "门店活动类型"; //直接从字段取
  // if(rule.baseInfo && rule.baseInfo.labelGroupIds && rule.baseInfo.labelGroupIds.length) {
  //   rule.baseInfo.labelGroupIds = rule.baseInfo.labelGroupIds.map(idItem => {
  //     // 带了前缀的都默认不展示前缀
  //     if (idItem.indexOf('_') == 0) {
  //       const index = idItem.indexOf('_');
  //       return idItem.slice(index+1);
  //     } else {
  //       return idItem;
  //     }});
  // }
  return <span className='filter-text'>
    {(rule.baseInfo && rule.baseInfo.activityIdsTypes && rule.baseInfo.activityIdsTypes != 'mktType') && <p>{`${groupActivityIdsTypesEnum[rule.baseInfo.activityIdsTypes]}: ${rule.baseInfo.activityIdsTypesValue};`}</p>}
    {(record && record.idMode) && <p>{`${'ID类型'}: ${idModeEnum[record.idMode]};`}</p>}
    {
      humanizeQueryForm({
        queryForm: rule,
        labelMap,
        isStore: isStore && getNoTranform(rule.basePoolId)
      }).map(({label, value}) => {
        if (label == '活动价格小于等于7天最低价' && rule.basePoolId != "40001" &&  rule.basePoolId != "20012") {
          return null;
        }else {
          return ((label != activityTypeGoodLabel && label!= activityTypeStoreLabel) && <p>{`${label}: ${value};`}</p>)
        }
      })
    }
    {(record && record.activityType) && <p>{`${activityTypeGoodLabel}: ${record.activityType};`}</p>}
    {(record && record.storeActivityType) && <p>{`${activityTypeStoreLabel}: ${record.storeActivityType};`}</p>}
  </span>
}

function getNoTranform(basePoolId){ //不是品转店
  return ![10013,30004,30006,31004].includes(+basePoolId);
}


export function createRuleRender(rule, queryMeta, isStore, record) {
  return formatRule(rule, queryMeta, isStore, record)
}


export function createSkuRuleRender(rule, queryMeta, isStore, record) {
  console.log(rule);
  console.log(queryMeta);
  return formatRule(rule, queryMeta, isStore, record)
}


export function timeRangeRender(_value, _index, record){
  let {effectAt, expireAt} = record;
  effectAt = effectAt ? formatTimeStamp(moment(effectAt)) : ''
  expireAt = expireAt ? formatTimeStamp(moment(expireAt)) : ''
  return <div>
    <p>{effectAt}~</p>
    <p>{expireAt}</p>
  </div>
}


export function dateRender(value, _index, _record){
  if (!value) return value
  else return formatTimeStamp(moment(value))
}
