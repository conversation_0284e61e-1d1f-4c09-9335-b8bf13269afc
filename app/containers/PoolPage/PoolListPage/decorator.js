import { connect } from 'react-redux';
import { compose } from 'redux';
import { createStructuredSelector } from 'reselect';
import injectSaga from '@/utils/injectSaga';
import { saga } from './saga';
import { setRuleDetail } from '@/containers/PoolPage/common/actions';
import { actions } from '../common';
import { makeSelectQueryMeta } from '@/containers/App/selectors';
import injectReducer from '@/utils/injectReducer';
import { constants, reducer } from '../common';

const mapDispatchToProps = (dispatch) => ({
  initQueryMeta: (payload) => dispatch(actions.initQueryMeta(payload)),
  setDetail: (payload) => dispatch(setRuleDetail(payload)),
});

const mapStateToProps = createStructuredSelector({
  queryMeta: makeSelectQueryMeta()
});
const withConnect = connect(mapStateToProps, mapDispatchToProps);
const withReducer = injectReducer({ key: constants.REDUCER_KEY, reducer: reducer.rootReducer });
const withSaga = injectSaga({ key: 'ruledetail', saga });
export const connectRedux = compose(withSaga, withReducer, withConnect)
