import * as qs from 'query-string';

import { put, takeLatest } from 'redux-saga/effects';
import { SET_EDIT_DETAIL, INIT_QUERY_META } from '@/containers/PoolPage/common/constants';
import { updateBasePoolId, updateOuterField, updateQueryField } from '@/containers/PoolPage/common/actions';
import { createCommodityMeta, createStoreMeta, collectLabelMap, collectDataSources } from '@/containers/PoolPage/SelectQueryPoolPage/queryfield';
import { toastMessage } from '@/containers/App/actions';
import { push } from 'connected-react-router'
import  * as api from '@/utils/api';
import * as actions from '@/containers/App/actions';
import moment from 'moment';
import { formatTimeStamp } from '@/utils/time';

function walk(nodes, cb) {
  if (!nodes) return
  for (let node of nodes) {
    cb(node)
    walk(node.children, cb)
  }
}

function casCadeOptionToLabelMap(options) {
  let result = {}
  walk(options, (node) => {
    result[node.value] = node.label
  })
  return result
}

export function* initQueryMeta({ payload }) {
  const storeMainCascadeOptions = yield api.getStoreMainCatetories().then(api.onRequestSuccess)
  const categoryCascadeOptions = yield api.ali.getCategorySku().then(api.onRequestSuccess)
  const cityCascadeOptions = yield api.getCities().then(api.onRequestSuccess)
  const storeCascadeOptions = yield api.ali.getCategoryStore().then(api.onRequestSuccess)
  const newStoreMainCascadeOptions = yield api.ali.getNewAllStoreMainCategory().then(api.onRequestSuccess)
  const marketingTypeOptions = yield api.queryMarketingType().then(api.onRequestSuccess)
  const storeActivitiesTypeOptions = yield api.ali.queryMarketingType({id:payload}).then(api.onRequestSuccess)
  const newMarketingTypeOptions = yield api.queryNewMarketingType().then(api.onRequestSuccess)
  const crowdAttributes = yield api.getCrowdAttributes().then(api.onRequestSuccess)
  const itemStatusOptions = yield api.ali.getItemStatusOptions().then(api.onRequestSuccess)
  let filtering = false
  let meta = {
    sku: createCommodityMeta(
      { basePoolId: payload, filtering }, {
        marketingTypeOptions,
        newMarketingTypeOptions,
        categoryCascadeOptions,
        storeCascadeOptions,
        cityCascadeOptions,
        storeMainCascadeOptions,
        newStoreMainCascadeOptions,
        itemStatusOptions
      }
    ),
    store: createStoreMeta({ basePoolId: payload, filtering }, {
      cityCascadeOptions,
      storeCascadeOptions,
      storeMainCascadeOptions,
      newStoreMainCascadeOptions,
      crowdAttributes,
      storeActivitiesTypeOptions
    }),
  }
  let ds = collectDataSources(meta.sku).concat(collectDataSources(meta.store));
  let ot = ds.reduce((p, c) => {
    const { fieldKey, dataSource } = c;
    p[fieldKey] = {
      valueMap: casCadeOptionToLabelMap(dataSource)
    }
    return p
  }, {})

  let labelMap = {
    sku: collectLabelMap(meta.sku),
    store: collectLabelMap(meta.store),
    others: {
      ...ot,
      createTime: {
        format: (value) => {
          if (!value) return ''
          return formatTimeStamp(value)
        }
      }
    }
  }

  yield put(actions.setQueryMeta({
    meta, labelMap
  }))
}


export function* setRuleDetail({payload}) {
  const {isStore, id, type, from , dataType,basePoolName} = payload;
  try {
    // make this more like saga way ? using call ?
    // noop, just gonna leave it as it is
    let isCopy = type === 'copy'
    let url = '';
    if (payload.createMode != 'CREATE_EXCEL' && payload.createMode != 'Excel上传') { //todo 修改为key
      url = `/${isStore ? 'store' : 'commodity'}pool${from === 'invite' ? 'Invite' : ''}/creation/query`
    } else {
      url = `/${isStore ? 'store' : 'commodity'}pool${from === 'invite' ? 'Invite' : ''}/dataSetUpload`
    }

    let request = (dataType == 1) ? api.ali.getPoolDetail : api.getPoolDetail;

    const detail = yield request({isStore, id})

    const { basePoolId, effectAt, expireAt, poolName, filterRules } = detail;

    if (isCopy) {
      url = `${url}?${qs.stringify({basePoolId, dataType, basePoolName,isCopy})}`

      let outerForm = {
        name: poolName,
        effectRange: [moment(effectAt), moment(expireAt)],
        outSynPlatforms: detail.outSynPlatforms,
        poolResultLimit: detail.poolResultLimit ? detail.poolResultLimit.toString() : ''
      }
      yield put(updateBasePoolId(basePoolId))
      yield put(updateOuterField(outerForm))
      yield put(updateQueryField(filterRules))
    } else {
      url = `${url}/${id}?${qs.stringify({basePoolId, dataType, basePoolName})}`
    }
    yield put(push(url))
  } catch (error) {
    yield put(toastMessage({content: error.msg || '操作失败', type: 'warning', error}))
  }
}

/**
 * Root saga manages watcher lifecycle
 */
export function* saga() {
  yield takeLatest(SET_EDIT_DETAIL, setRuleDetail);
  yield takeLatest(INIT_QUERY_META, initQueryMeta);
}
