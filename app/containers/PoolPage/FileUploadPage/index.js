import React, {Fragment, useEffect, useState,createContext} from 'react';
import {withRouter} from "react-router-dom";
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {permissionAccess} from "@/components/PermissionAccess";
import {Radio, Step, Message} from '@alife/next';
import DataSetUploadPage from './dataSetUploadPage';
import {BreadcrumbTips, CompletePage} from "@/home/<USER>/comps";
import * as api from "@/adator/api";
import * as apiL from "@/utils/api";
import './style.scss';
import FinishPage from './finishPage';

export default function FileUpload(props) {
  const {location, match, history} = props;
  let {poolId = ''} = match.params;
  const isEdit = poolId > 0;
  const isCopy = poolId < 0;
  poolId = poolId < 0 ? -poolId : poolId;

  const [step, setStep] = useState(0);
  const [user,setUser] = useState({});
  const [query,setQuery] = useState({});
  const [submitPoolId, setSubmitPoolId] = useState(""); //发布成功的池子id
  const [isAction, setIsAction] = useState(false); //是否在行动
  

  const breadcrumbList = [
    {"title": '管理门店池', link: "#/storepoolInvite/list"},
    {"title": '创建门店池 (文件上传)' , link: ""}
  ];

  useEffect(()=>{
    getUser();
  },[])

  const getUser = () =>{
    try {
      api.getBucUser()
        .then((resp) => {
          setUser(resp.data.data);
          if(isEdit || isCopy) {
            getPoolDetail();
          }
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  const  getPoolDetail = ()=>{
    try {
      api.getPoolDetailByPoolId(poolId)
        .then((resp) => {
          if (resp.success) {
            setQuery(resp.data)
          }else{
            Message.error(resp.errMessage)
          }
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  // 新建 - 发布
  const submitPool = async (query) => {
    try {
      if (!isAction) {
        let resp = await apiL.ali.saveAsStorePool(query,true)
        if (resp.data.code == "200") {
          let id = resp.data.data;
          if (id) {
            let result = await api.publishPool(id);
            if (result.success) {
              Message.success("发布成功");
              setIsAction(false);
              setSubmitPoolId(id);
              setStep(1);
            } else {
              setIsAction(false);
              Message.error(result.errMessage);
            }
          }
        } else {
          setIsAction(false);
          Message.warning(resp.data.msg);
        }
      }
    } catch (error) {
      setIsAction(false);
      api.onRequestError(error);
    }
  };
  
  return (
    <div className="upload-pool">
      <BreadcrumbTips list={breadcrumbList}/>
      <div className="tag-pool-father">
        <Step current={step} shape="dot">
          {[['圈选规则', ''], ['完成', '']].map((item, index) => <Step.Item key={index} title={item[0]} content={item[1]} />)}
        </Step>
        {step == 0 &&
            <DataSetUploadPage poolId={poolId} isEdit={isEdit} isCopy={isCopy} location={location} history={history} submitPool={submitPool} query={query}
            goStep={setStep} /> }
        {step == 1 && <FinishPage submitPoolId={submitPoolId} history={history}></FinishPage>}
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(FileUpload), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const FileUploadPage = permissionAccess(LogTimePutInPage)