import React, { useEffect, useState } from "react";
import {
  Icon,
  Form,
  Input,
  Upload,
  Field,
  Radio,
  Message,
  Button,
} from "@alife/next";
import { PoolPageBase } from "../common";
import { TimeRangePicker } from "@/components/TimeRangePicker";
import { promisify } from "@/utils/others";
import { config } from "@/config";
import moment from "moment";
import "./style.scss";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  wrapperCol: {
    span: 20,
  },
  labelAlign: "center",
  labelTextAlign: "center",
  style: {
    verticalAlign: "top",
    color: "#666666",
    textAlign: "left",
    paddingRight: "12px",
  },
};
const formItemLayoutFile = {
  wrapperCol: {
    span: 20,
  },
  labelAlign: "center",
  labelTextAlign: "center",
};
const formLayout = {
  style: {
    margin: "20px auto",
  },
};

const formBaseInfo = {
  wrapperCol: {
    span: 22,
  },
  labelAlign: "center",
  labelTextAlign: "center",
};
const idModeEnum = [{ label: "淘内门店ID", value: "SHOP_ID", type: "store" }];

const dateGroup = [
  { label: "2个月", value: "2" },
  { label: "3个月", value: "3" },
  { label: "4个月", value: "4" },
];


const defaultTime = [
  moment().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }),
  moment()
    .add(1, "months")
    .set({ hour: 23, minute: 59, second: 59, millisecond: 0 }),
];

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb("请输入正确的时间段");
  else {
    if (start.isBefore(moment().startOf("day")))
      return cb("开始时间不能小于当前时间");
  }
  cb();
};

/*
 * 文件上传
 * */
export default function DataSetUploadPage(props) {

  const { poolId, isEdit,isCopy, location, history, query } = props;
  // 文件名称
  const [fileName, setFileName] = useState("");
  // 文件URL
  const [fileUrl, setFileUrl] = useState("");
  // 文件list
  const [defaultValue, setDefaultValue] = useState([]);

  // 无文件时弹出提醒
  const [isSubmit, setIsSubmit] = useState(false);
  // 是否是可下一步状态
  const [canSubmit, setCanSubmit] = useState(true);
  // 是否正在提交
  const [isLoad, setIsLoad] = useState(false);
  const field = Field.useField();
  // 改变上传拖拽颜色
  const [isDrag, setIsDrag] = useState(false);
  const [curMonth, setCurMonth] = useState(1);

  useEffect(() => {
    if (isEdit || isCopy) {
      field.setValues({
        poolId: poolId,
        refreshMode: query.refreshMode,
        fileName: query.fileName,
        idMode: query.idMode,
        fileUrl: query.fileUrl,
        poolName: query.poolName,
        effectRange: [moment(query.effectAt), moment(query.expireAt)],
      });
      setFileName(query.fileName);
      setFileUrl(query.fileUrl);
      setDefaultValue([
        {
          uid: "0",
          name: query.fileName,
          state: "done",
          url: query.fileUrl,
        },
      ]);
    }
  }, [query]);

  const nextStep = async () => {
    await promisify(field.validate)();
    setIsSubmit(true);
    let req = {
      fileName: fileName,
      fileUrl: fileUrl,
      poolId: isEdit ? poolId : null,
      idMode: field.getValue("idMode"),
      poolName: field.getValue("poolName"),
      effectAt: field.getValue("effectRange")[0].valueOf(),
      expireAt: field.getValue("effectRange")[1].valueOf(),
      poolRule: {
        basePoolId: "32005",
        baseInfo: {},
      },
      refreshMode: 0,
      createMode: "CREATE_EXCEL",
    };
    if (fileUrl && fileName) {
      props.submitPool(req);
    }else{
      Message.error('请上传文件')
    }
    
  };

  const previousStep = () => {
    history.push('/storepoolInvite/creation')
  }

  const onSuccess = (info) => {
    setIsDrag(false);
    if (info.response.code == "200") {
      // 如果上传成功则设置将此文件添加到DefaultValue中
      setFileName(info.response.data.fileName);
      setFileUrl(info.response.data.url);
      setDefaultValue([
        ...defaultValue,
        ...[
          {
            uid: "0",
            name: info.response.data.fileName,
            state: "done",
            url: info.response.data.url,
          },
        ],
      ]);
    } else {
      Message.error(info.response.msg || "上传失败");
    }
  };

  const onDragOver = () => {
    // 设置上传背景颜色
    setIsDrag(true);
  };
  const onDragLeave = () => {
    // 取消上传背景颜色
    setIsDrag(false);
  };

  const onRemove = (removeItem) => {
    // 每次删除都要将DefaultValue的值重新刷一遍，避免重复出现
    let list = [];
    defaultValue.map((items) => {
      if (items.url != removeItem.url) {
        list.push(items);
      }
    });
    setDefaultValue([...list]);
  };

  const beforeUpload = (file, options) => {
    // 在上传之前判断类型，如果类型不匹配直接失败。
    return new Promise((resolve, reject) => {
      if (
        file.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type == "application/vnd.ms-excel"
      ) {
        resolve(options);
      } else {
        setIsDrag(false);
        reject();
        Message.error("文件类型不匹配");
      }
    });
  };

  const setDate = (month) => {
    let time = [
      moment().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }),
      moment()
        .add(month, "months")
        .set({ hour: 23, minute: 59, second: 59, millisecond: 0 }),
    ];
    if (month == 4) {
      time = [
        moment().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }),
        moment()
          .add(month, "months")
          .add(-1, "days")
          .set({ hour: 23, minute: 59, second: 59, millisecond: 0 }),
      ];
    }
    setCurMonth(month);
    field.setValue("effectRange", time);
  };

  return (
    <PoolPageBase.Container className="UploadPage">
      <Form {...formLayout} field={field}>
        <h3>
          <i className="order-num">1</i>圈选方式：文件上传
        </h3>
        <h3>
          <i className="order-num ">2</i>上传文件
        </h3>
        <FormItem {...formItemLayout}>
          ID类型:&nbsp;&nbsp;
          <RadioGroup
            name="idMode"
            disabled={poolId && !isCopy}
            defaultValue={idModeEnum[0].value}
            dataSource={idModeEnum}
            style={{ width: "220px", marginTop: "20px" }}
          />
        </FormItem>
        <FormItem {...formItemLayoutFile} required>
          <div style={{ margin: "7px 0 10px 0" }}>
            请先下载
            <a
              target="_blank"
              download="带说明模板.csv"
              href="https://files.alicdn.com/tpsservice/b4beb2dac1f28e0986ee47576ceb2270.xlsx"
            >
              导入模板
            </a>
            ，按照要求填写完成，上传到系统。
          </div>
          <Upload.Dragger
            headers={{ "X-Requested-With": null }}
            action={`${config.API_ROOT}/api/ali/common/upload`}
            onSuccess={onSuccess}
            disabled={poolId && !isCopy}
            listType="text"
            onDragOver={onDragOver}
            onDragLeave={onDragLeave}
            beforeUpload={beforeUpload}
            value={defaultValue}
            onRemove={onRemove}
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
          >
            <div
              className={`next-upload-drag ${
                isDrag ? "next-upload-drag-onDragOver" : ""
              }`}
            >
              <p className="next-upload-drag-icon">
                <Icon type="upload" />
              </p>
              <p className="next-upload-drag-text">
                点击或将文件拖拽到这里上传
              </p>
              <p className="next-upload-drag-hint">支持扩展名：.xlsx,.xls</p>
            </div>
          </Upload.Dragger>
          {fileUrl == "" && isSubmit && (
            <div style={{ color: "#FF2D4B" }}>请上传文件</div>
          )}
        </FormItem>
        <h3 style={{ paddingTop: "0px" }}>
          <i className="order-num ">3</i>基本信息
        </h3>
        <FormItem
          label="门店池名称"
          required
          requiredMessage="门店池名称"
          {...formBaseInfo}
          className="notMargin"
        >
          <Input
            style={{ width: "408px" }}
            placeholder="请输入门店池名称,不超过20个字"
            name="poolName"
            maxLength={20}
            disabled={isEdit}
          />
        </FormItem>
        <FormItem
          label="有效期"
          required
          asterisk={false}
          {...formBaseInfo}
          requiredMessage="有效期必填"
          validator={timeRangeValidator}
          validatorTrigger={["onChange"]}
          className="notMargin"
        >
          <TimeRangePicker
            defaultValue={defaultTime}
            style={{ width: "258px", marginRight: "8px" }}
            name="effectRange"
            disabledDate={(date) => date.isBefore(moment().startOf("day"))}
          ></TimeRangePicker>
          <Button.Group size="size">
            {dateGroup.map((v) => {
              return (
                <Button
                  size="small"
                  className={`date_${v.value} ${
                    curMonth == v.value ? "cur" : ""
                  }`}
                  onClick={() => setDate(v.value)}
                >
                  {v.label}
                </Button>
              );
            })}
          </Button.Group>
        </FormItem>
        <FormItem label=" " colon={false}>
          <Form.Submit
              type="secondary"
              validate
              onClick={previousStep}
            >
              上一步
          </Form.Submit>
          &nbsp;&nbsp;&nbsp;
          <Form.Submit
            type="primary"
            validate
            onClick={nextStep}
            disabled={!canSubmit}
          >
            发布
          </Form.Submit>
        </FormItem>
      </Form>
    </PoolPageBase.Container>
  );
}
