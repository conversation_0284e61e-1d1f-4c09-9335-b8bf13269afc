import React from 'react';
import { env, ENV_ENUM } from '@/config';
import { Progress, Icon, Button, Breadcrumb } from '@alife/next';
import { Link } from 'react-router-dom';
import { PageWrapper } from '@/components/PageWrapper';
import { Steps, PoolPageBase } from '../common';
import { getValueByUrlKey } from '@/utils/others';
import * as api from '@/utils/api';

import './style.scss';


export class PoolCreationProgressPage extends PoolPageBase {
  constructor(props){
    super(props)
    this.state = {
      percent: 0,
      state: 'progressing'
    }
  }

  get id(){
    return this.params.id
  }

  componentDidMount() {
    let intervalTime = env === ENV_ENUM.localdev ? 5*1000 :  15 * 1000

    this.timer = setInterval(async()=>{
      try {
        let request = (getValueByUrlKey('dataType') == 1) ? api.ali.checkProgress : api.checkProgress;
        let percent = await request({id: this.id, isStore: this.isStore}).then(api.onRequestSuccess)
        // let percent = await api.checkProgressAli({id: this.id}).then(api.onRequestSuccess)
        if (percent >= 100 ){
          this.setState({
            state: 'success',
          })
          clearInterval(this.timer)
        } else {
          this.setState({
            percent: percent,
            state: 'progressing'
          })
        }
      } catch (error) {
        this.setState({
          state: 'failed'
        })
        console.error(error)
        clearInterval(this.timer)
      }

    }, intervalTime)
  }

  componentWillUnmount() {
    clearInterval(this.timer)
  }


  render() {
    let tag = this.isStore ? '门店' : '商品'
    const middleText = this.isStore ? '创建门店池' : '创建商品池'
    let {percent, state} = this.state;
    let step = state === 'success' ? 2 : 1

    return (
      <PoolPageBase.Container className="CommodityProgressPage">
        {this.isInvite&&<div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item><Link to={`/${this.isStore ? 'store' : 'commodity'}poolInvite/list`}>{`管理${tag}池`}</Link></Breadcrumb.Item>
            <Breadcrumb.Item> {middleText} </Breadcrumb.Item>
          </Breadcrumb>
        </div>}
        <PageWrapper title={`创建${tag}池`}>
          <Steps current={step} middleText={middleText}/>
          <div className="content">
            <Pg state={state} percent={percent}/>
            <Info tag={tag} state={state} onSuccess={()=> this.history.push(`/${this.isStore ? 'store': 'commodity'}pool${this.isInvite?"Invite":""}/list/detail/${this.id}?${this.encodeQuery(this.query)}`)}/>
          </div>
        </PageWrapper>
      </PoolPageBase.Container>
    );
  }
}

const textRender = percent => {
  if (percent === 100) {
    return <Icon type="select" size="medium" />;
  }
  return `${percent}%`;
};


function InfoProgress() {
  return <>
    <div className="title">发布中，请等待……</div>
  </>
}

function InfoError({ msg = '请核对信息后，再重新提交' }) {
  return <>
    <div className="title">发布失败</div>
    <p>{msg}</p>
  </>
}


function InfoSuccess({onSuccess, tag}) {
  return <>
    <div className="title">发布成功</div>
    <Button type="primary" onClick={onSuccess}>查看{tag}池</Button>
  </>
}

function Info({state, ...others}){
  let t = null
  switch (state) {
    case 'success':
      t = <InfoSuccess {...others} />
      break;
    case 'error':
      t = <InfoError {...others} />
      break;
    default:
      t = <InfoProgress {...others} />
      break;
  }
  return <div className="info">{t}</div>
}

function Pg({state, percent}){
  let t = null
  switch (state) {
    case 'error':
      t = <Icon size="large" type="delete-filling" />
      break;
    case 'success':
      t = <Icon size="large" type="success-filling" />
      break;

    default:
      t = <Progress percent={percent} shape="circle" size="small" state="normal" className="custom-progress" textRender={textRender} />
  }

  return <div className={`pg type-${state || ''}`}> {t} </div>
}

export class PoolCreationProgressPageInvite extends PoolCreationProgressPage {
  get isInvite() {
    return true;
  }
}
