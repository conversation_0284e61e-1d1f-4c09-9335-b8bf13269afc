import React from 'react';
import { TreeSelect,CascaderSelect} from '@alife/next';
import './index.scss';
import { getPermission} from "@/utils/api";
import {pageMap} from "@/containers/channel/market/common";
// import {queryResourceList} from "@/containers/channel/market/request";
import {getResourceMenuList} from "@/containers/report/request";
import * as api from "@/utils/api";

export class Resource extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      resourceList: [],
      asyncResourceList: []
    }
  }

  componentDidMount() {
    // await this.getResourceList();
    this.getMenuList();
  }

  getMenuList = () => {
    try {
      getResourceMenuList().then((res)=>{
        this.setState({
          resourceList: this.ctrlMenuTree(res)
        })
      });
    } catch (error) {
      api.onRequestError(error);
    }
  }

  ctrlMenuTree(list = [], result = []) {
    if (!list.length) return [];
    for (let item of list) {
      let node = {
        ...item,
        selectable:!(item.children && item.children.length > 0)
      }
      result.push(node);
      if (item.children && item.children.length > 0) {
        node.children = [];
        this.ctrlMenuTree(item.children, node.children);
      }
    }
    return result;
  }

  onChange = (value) => {
    this.props.onChange(value);
  }

  async getMenu() {
    let {asyncResourceList} = this.state;
    let data = await getPermission();
    let list = data.subMenus.filter((v) => v.menuName == "channel_manage")[0].subMenus;
    this.ctrlTree(list).then(res => {
      this.setState({
        resourceList: res
      })
    });
  }

  async getResourceList() {
    let {asyncResourceList} = this.state;
    // let {pageId} = pageMap.filter((v) => v.pageName == pageName)[0];
    pageMap.map(async (v) => {
      let queryResource = {
        page: 1,
        size: 80,
        query: {
          pageId: v.pageId
        }
      }
      let data = await queryResourceList(queryResource);
      asyncResourceList.push({
        pageId: v.pageId,
        data: data.resourceList
      })
      this.setState({
        asyncResourceList
      },()=>{
        if (asyncResourceList.length == pageMap.length) {
          this.getMenu();
        }
      })
    })
  }

  async ctrlTree(list = [], result = []) {
    let {asyncResourceList} = this.state;
    if (!list.length) return [];
    for (let item of list) {
      let node = {
        label: item.menuTitle,
        value: item.menuName,
        url: item.menuUrl,
        key: item.menuId,
        selectable:false
      }
      let havePageIdItem = pageMap.filter((v) => v.url == node.url);
      if (havePageIdItem.length > 0) {
        node.pageId = havePageIdItem[0].pageId;
        let res = asyncResourceList.filter((v)=>v.pageId==havePageIdItem[0].pageId)[0].data;
        node.children = res.map((v) => {
          return {
            value: v.resourceId,
            label: v.componentName,
            key: v.resourceId,
          }
        })
      }
      result.push(node);
      if (item.subMenus && item.subMenus.length > 0) {
        node.children = [];
        this.ctrlTree(item.subMenus, node.children);
      }
    }
    return result;
  }

  render(){
    const { resourceList,value} = this.state;
    return (
      <>
        {/*{resourceList && resourceList.length > 0 && <TreeSelect*/}
        {/*  defaultExpandAll={true}*/}
        {/*  aria-labelledby="a11y-tree-select"*/}
        {/*  hasClear*/}
        {/*  showLine={true}*/}
        {/*  name='resourceId'*/}
        {/*  onChange={this.onChange}*/}
        {/*  dataSource={resourceList}*/}
        {/*  style={{ width: 220 }}*/}
        {/*/>}*/}
        <CascaderSelect dataSource={resourceList} value={this.props.value || ''} onChange={this.onChange} style={{width: 360}}/>
      </>
    )
  }
}


