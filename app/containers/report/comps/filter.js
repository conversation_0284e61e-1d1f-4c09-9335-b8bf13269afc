import React, {Fragment} from 'react';
import {Form, Checkbox, Radio, Select, Input, Button, Grid, Field, CascaderSelect, Cascader, TreeSelect} from '@alife/next';
// import {PoolPageBase} from '../common/index';

import {TimeRangePicker} from "@/components/TimeRangePicker";
import moment from "moment";
// import {poolTypeEnum,publishStatusEnum} from "@/home/<USER>/common";
// import './style.scss';
// import {PageBase} from '@/home/<USER>/index'
import * as api from "@/adator/api";
import {SelectType} from "@/home/<USER>/poolList/selectType";
import {getAllDeliveryChannel, getPermission} from "@/utils/api";
import {queryResourceList} from "@/containers/channel/market/request";
import {pageMap} from "@/containers/channel/market/common";
import {isAllNumber} from "@/utils/validators";
import {Resource} from "@/containers/report/comps/resource";
import {doesFormHasErrors} from "@/utils/formTool";
import {getQueryString} from "@/utils/others";

const FormItem = Form.Item;
const { Row, Col } = Grid;
const formItemLayout = {
  labelCol: {span: 8},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    // if(end.isBefore(moment().startOf('day'))) return cb('结束时间不能大于当前时间')
    if (end.clone().subtract(1, 'month').isAfter(start.clone())) return cb('时间段不能超过1个月')
  }
  cb()
}

export class Filter extends React.Component {
  constructor(props) {
    super(props);
    let query = {};
    if(getQueryString("resourceId")){
      query.resourceId = getQueryString("resourceId");
    }
    if(getQueryString("scheduleId")){
      query.scheduleId = getQueryString("scheduleId");
    }
    this.state = {
      step: 0,
      query,
      resourceList: [],
      visible: false,
      defaultClient:['ALL'],
      defaultTime: [moment().set({hour: 0, minute: 0, second: 0, millisecond:0}), moment().set({hour: 23, minute: 59, second: 59, millisecond:0})],
      asyncResourceList: [],
      channelListMap: [
        {label: '全部', value: 'ALL',checkboxDisabled:false},
        {label: 'APP', value: 'eleme',checkboxDisabled:true},
        {label: '支付宝', value: 'alipay',checkboxDisabled:true},
        {label: '微信', value: 'WEIXIN',checkboxDisabled:true},
        // {label: '淘宝', value: 'taobao',checkboxDisabled:true},
        {label: '口碑', value: 'kb_app',checkboxDisabled:true},
      ]
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        let {query} = this.state;
        if (name == 'effectRange') {
          query.startTime = value[0] ? value[0].valueOf() : '';
          query.endTime = value[1] ? value[1].valueOf() : '';
        }  else {
          query[name] = value;
        }
        this.setState({query}, () => {
          this.props.updateQuery(this.state.query);
        })
      }
    })
  }

  async componentDidMount() {
    // await this.getResource();
    // await this.getResourceList();
    this.initField();
  }

  initField = () =>{
    let {defaultTime, query} = this.state;
    query.clientCode = ['ALL'];
    query.startTime = defaultTime[0] ? defaultTime[0].valueOf() : '';
    query.endTime = defaultTime[1] ? defaultTime[1].valueOf() : '';
    this.setState({query},()=>{
      this.props.updateQuery(this.state.query);
    });
  }

  setField = (key, value) => {
    this.setState({
      [key]:value
    })
  }

  changeDeliveryChannel = (channelList) => {
    let {channelListMap} = this.state;
    let newChannelListMap = JSON.parse(JSON.stringify(channelListMap));
    if (channelList.length != 0) {
      if (channelList.includes('ALL')) {
        newChannelListMap.forEach((item) => {
          item.checkboxDisabled = (item.value == 'ALL') ? false : true
        });
      } else {
        newChannelListMap.forEach((item) => {
          item.checkboxDisabled = (item.value != 'ALL') ? false : true
        });
      }
    } else {
      newChannelListMap.forEach((item) => {
        item.checkboxDisabled = false
      });
    }
    this.setState({channelListMap: newChannelListMap})
    this.field.setValue('channelList', channelList);
  }

  // async getResource () {
  //   let {resourceId, pageId, locationList} = this.state;
  //   let params = {
  //     pageId,
  //     resourceId
  //   }
  //   // 获取渠道列表
  //   let {channelList: channelListMap} = await getAllDeliveryChannel();
  //   console.log(channelListMap);
  //   this.setState({
  //     channelListMap
  //   })
  // }
  //
  // async getMenu() {
  //   let {asyncResourceList} = this.state;
  //   console.log(asyncResourceList.length)
  //   console.log(pageMap.length)
  //   console.log("getMenu");
  //   let data = await getPermission();
  //   let list = data.subMenus.filter((v) => v.menuName == "channel_manage")[0].subMenus;
  //   this.ctrlTree(list).then(res => {
  //     this.setState({
  //       resourceList: res
  //     })
  //   });
  // }
  //
  // async getResourceList() {
  //   let {asyncResourceList} = this.state;
  //   // let {pageId} = pageMap.filter((v) => v.pageName == pageName)[0];
  //
  //   pageMap.map(async (v) => {
  //     let queryResource = {
  //       page: 1,
  //       size: 80,
  //       query: {
  //         pageId: v.pageId
  //       }
  //     }
  //     let data = await queryResourceList(queryResource);
  //     asyncResourceList.push({
  //       pageId: v.pageId,
  //       data: data.resourceList
  //     })
  //     this.setState({
  //       asyncResourceList
  //     },()=>{
  //       if (asyncResourceList.length == pageMap.length) {
  //         this.getMenu();
  //       }
  //     })
  //   })
  // }
  //
  // async ctrlTree(list = [], result = []) {
  //   let {asyncResourceList} = this.state;
  //   if (!list.length) return [];
  //   for (let item of list) {
  //     let node = {
  //       label: item.menuTitle,
  //       value: item.menuName,
  //       url: item.menuUrl,
  //       key: item.menuId,
  //       selectable:false
  //     }
  //     let havePageIdItem = pageMap.filter((v) => v.url == node.url);
  //     if (havePageIdItem.length > 0) {
  //       node.pageId = havePageIdItem[0].pageId;
  //       let res = asyncResourceList.filter((v)=>v.pageId==havePageIdItem[0].pageId)[0].data;
  //       node.children = res.map((v) => {
  //         return {
  //           value: v.resourceId,
  //           label: v.componentName,
  //           key: v.resourceId,
  //         }
  //       })
  //     }
  //     result.push(node);
  //     if (item.subMenus && item.subMenus.length > 0) {
  //       node.children = [];
  //       this.ctrlTree(item.subMenus, node.children);
  //     }
  //   }
  //   return result;
  // }

  treeChange = (value) =>{
    value = value ? value : '';
    // this.field.setValue("resourceId",value);
    let {query} = this.state;
    query.resourceId = value;
    this.setState({
      query
    },()=>{
      this.props.updateQuery(this.state.query);
    })
  }

  searchData = () => {
    this.field.validate((e) => {
      if (!doesFormHasErrors(e)) {
        this.props.loadData(1);
      }
    });
  }

  resetData = () =>{
    this.field.reset();
    this.field.setValue("effectRange",this.state.defaultTime);
    this.field.setValue("clientCode",this.state.defaultClient);
    this.field.setValue("resourceId",'');
    this.initField();
    let {query} = this.state;
    query.resourceId = '';
    if (query.scheduleId) {
      query.scheduleId = '';
    }
    this.setState({query});
  }

  render() {
    let {defaultTime, defaultClient, query,channelListMap} = this.state;
    let {type} = this.props;
    return (
      <Form className="filter" field={this.field}>
        <Row>
          <Col span={5}>
            <FormItem label="渠道：" required requiredMessage={'渠道必填'} {...formItemLayout}>
              <CascaderSelect style={{width:'200px'}} defaultValue={defaultClient}  name="clientCode" multiple dataSource={channelListMap} onChange={(changeDeliveryChannel) => this.changeDeliveryChannel(changeDeliveryChannel)}/>
            </FormItem>
          </Col>
          <Col span={10}>
            <FormItem label="资源位" required requiredMessage={'资源位必填'} {...formItemLayout}>
              <Resource onChange={this.treeChange} value={query.resourceId} name='resourceId' />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="日期" required requiredMessage={'日期必填'} asterisk={false} {...formItemLayout} validator={timeRangeValidator} validatorTrigger={['onChange']}>
              <TimeRangePicker style={{width:'300px'}}  defaultValue={defaultTime} name="effectRange" disabledDate={(date) => !date.isBetween('2021-06-30',moment())}></TimeRangePicker>
            </FormItem>
          </Col>
        </Row>
        <Row>
          {type==2 && <Col span={6}>
            <FormItem label="排期ID："  {...formItemLayout}>
              <Input name='scheduleId' value={query.scheduleId}/>
            </FormItem>
          </Col>}
          <Col span={6}>
            <Button type="secondary"  onClick={()=>this.searchData()} style={{marginLeft:'95px'}}>查询</Button>
            <Button className="btn_reset" onClick={()=>this.resetData()}>重置</Button>
          </Col>
        </Row>
      </Form>
    )

  }
}
