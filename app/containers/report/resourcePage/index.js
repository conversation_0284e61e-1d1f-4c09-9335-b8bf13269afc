import React, {Fragment, useEffect, useState} from 'react';
import {Grid, Button, Dialog, Table, Pagination, Message, Input} from '@alife/next';
import {Filter} from "@/containers/report/comps/filter";
import {BreadcrumbTips} from "@/home/<USER>/comps";
import {CheckBoxWithAll} from "@/components/CheckBoxWithAll";
import './index.scss';
import * as api from "@/utils/api";
import {getResourceList,exportResourceList} from '../request';
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {withRouter} from "react-router-dom";
import {tipsRender} from "@/containers/report/common";
import {permissionAccess} from "@/components/PermissionAccess";

const {Row, Col} = Grid;

function ResourceReport() {
  const allColumns = [
    {title: '日期', name: '日期', lock: 'left', dataIndex: 'dataDt', width: '100px', type: 'base'},
    {title: '资源位', name: '资源位', lock: 'left', dataIndex: 'spmbcName', width: '100px', type: 'base'},
    {title: '渠道', name: '渠道', lock: 'left', dataIndex: 'clientCode', width: '100px'},
    {title: '曝光PV', name: '曝光PV', dataIndex: 'expPv', width: '100px'},
    {title: '曝光UV', name: '曝光UV', dataIndex: 'expUv', width: '100px'},
    {title: '点击PV', name: '点击PV', dataIndex: 'clkPv', width: '100px'},
    {title: '点击UV', name: '点击UV', dataIndex: 'clkUv', width: '100px'},
    {title: tipsRender('订单数', "订单只能统计到前一天的归因数据，暂不支持查看当天数据"), name: '订单数', dataIndex: 'orderCnt', width: '100px'},
    {
      title: tipsRender('下单用户数', "订单只能统计到前一天的归因数据，暂不支持查看当天数据"),
      name: '下单用户数',
      dataIndex: 'orderedUserCnt',
      width: '140px'
    },
    {title: tipsRender('PV CTR', "点击PV/曝光PV"), name: 'PV CTR', dataIndex: 'pvCtr', width: '120px'},
    {title: tipsRender("UV P1", "点击UV/曝光UV"), name: 'UV P1', dataIndex: 'uvP1', width: '120px'},
    {title: tipsRender('UV P2', "订单数/点击UV"), name: 'UV P2', dataIndex: 'uvP2', width: '120px'},
    {title: tipsRender('UV P1P2', "UV P1*UV P2"), name: 'UV P1P2', dataIndex: 'uvP1P2', width: '120px'},
    {title: tipsRender('PV CVR', "订单数/点击商户数"), name: 'PV CVR', dataIndex: 'pvCvr', width: '120px'},
    {title: tipsRender('曝光商户数', "订单只能统计到前一天的归因数据，暂不支持查看当天数据"), name: '曝光商户数', dataIndex: 'expShopCnt', width: '140px'},
    {title: tipsRender('点击商户数', "订单只能统计到前一天的归因数据，暂不支持查看当天数据"), name: '点击商户数', dataIndex: 'clkShopCnt', width: '140px'},
    {
      title: tipsRender('动效商户数', "订单只能统计到前一天的归因数据，暂不支持查看当天数据"),
      name: '动效商户数',
      dataIndex: 'orderedShopCnt',
      width: '140px'
    },
    // {title: '曝光商品数', dataIndex: 'expItemCnt', width: '120px'},
    // {title: '点击商品数', dataIndex: 'clickItemCnt', width: '120px'},
    // {title: '动效商品数', dataIndex: 'orderedItemCnt', width: '120px'},
  ];
  const breadcrumbList = [
    {"title": '报表', link: ""},
    {"title": '资源位报表', link: ""}
  ];
  const [dataSource, setDataSource] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [exportExcel, setExportExcel] = useState(false);
  const [fileName, setFileName] = useState('资源位报表');
  const [page, setPage] = useState(1);
  const pageSize = 30;
  const baseColumns = allColumns.filter((v) => v.type == 'base');
  const fieldColumns = allColumns.filter((v) => v.type != 'base');
  const [columns, setColumns] = useState([]);
  const [baseField, setBaseField] = useState(baseColumns.map((v) => v.dataIndex));
  const [checkQuotas, setCheckQuotas] = useState(fieldColumns.map((v) => v.dataIndex));
  const [originalQuotas, setOriginalQuotas] = useState(fieldColumns.map((v) => v.dataIndex));
  const quotaEum = fieldColumns.map(({name, dataIndex}) => ({label: name, value: dataIndex}));
  const [query, setQuery] = useState({});
  const [total, setTotal] = useState(0);
  const [dataVersion, setDataVersion] = useState('');
  useEffect(() => {
    ctrlCols();
  }, [])

  const loadData = (page) => {
    setIsLoading(true);
    let req = {
      page,
      pageSize,
      query:{
        ...query
      }
    };
    try {
      getResourceList(req).then((res)=>{
        if (res.data.code == '200') {
          let {dataVersion,rows,total} = res.data.data;
          setDataVersion(dataVersion);
          setTotal(total);
          setPage(page);
          setDataSource(rows);
        } else {
          Message.warning(res.data.msg || '操作失败');
        }
        setIsLoading(false);
      });
    } catch (error) {
      api.onRequestError(error);
      setIsLoading(false);
    }
  }

  const ctrlCols = () => {
    let tmpField = baseField.concat(checkQuotas);
    setColumns(allColumns.filter((v) => tmpField.includes(v.dataIndex)));
  }

  const checkChange = (v) => {
    setOriginalQuotas(Object.assign(checkQuotas));
    setCheckQuotas(v);
  }

  const resetCheck = () => {
    setCheckQuotas(Object.assign(originalQuotas));
    setVisible(false);
    ctrlCols()
  }

  const confirmCheck = () => {
    setVisible(false);
    ctrlCols()
  }

  const resetExport = () => {
    setExportExcel(false)
  }

  const confirmExport = () => {
    setExportExcel(false)
    if (!query.resourceId) {
      Message.warning('资源位不能为空');
    }else if (query.clientCode.length == 0){
      Message.warning('渠道不能为空');
    }else{
      setIsLoading(true)
      let req = {
        fileName,
        query:{
          ...query
        }
      };
      try {
        exportResourceList(req).then((res)=>{
          if (res.data.code == '200') {
            window.open(res.data.data.data);
            setIsLoading(false)
          } else {
            setIsLoading(false)
            Message.warning(res.data.msg || '操作失败');
          }
        });
      } catch (error) {
        api.onRequestError(error);
      }
    }
  }

  const changePage = (page) =>{
    setPage(page);
    loadData(page);
  }

  return (
    <div className="resource-report">
      <BreadcrumbTips list={breadcrumbList}/>
      <div className="container">
        <Filter type={1} loadData={loadData} updateQuery={setQuery}/>
        <Row>
          <Col span={14}></Col>
          <Col span={10}>
            <div className="date-panel">
              {dataVersion && <span className="date">数据更新时间：{dataVersion}</span>}
              <Button type={'secondary'} onClick={() => setVisible(true)}>指标配置</Button>
              {/* 资源位报表导出功能 */}
              &nbsp;&nbsp;
              <Button type={'secondary'} onClick={() => setExportExcel(true)}>导出</Button>
            </div>
            <Dialog
              title="指标设置"
              visible={visible}
              className='quota-container'
              style={{width: '500px'}}
              onOk={confirmCheck}
              onCancel={resetCheck}
              onClose={resetCheck}
            >
              <CheckBoxWithAll
                dataSource={quotaEum}
                value={checkQuotas}
                onChange={checkChange}/>
            </Dialog>
            <Dialog
              title="确定将数据导出为csv文件？"
              visible={exportExcel}
              className='quota-container'
              style={{width: '500px'}}
              onOk={confirmExport}
              onCancel={resetExport}
              closeable= 'mask'
            >
              <Input
                size='large'
                defaultValue={fileName}
                className='exportExcelChild'
                onChange={(item)=>setFileName(item)}
              ></Input>
            </Dialog>
          </Col>
        </Row>
        <Table dataSource={dataSource} loading={isLoading} hasBorder={false}>
          {
            columns.map((e, idx) => {
              return <Table.Column {...e} key={idx}/>
            })
          }
        </Table>
        <Pagination
          onChange={(page)=>{changePage(page)}}
          current={page}
          total={total}
          pageSize={pageSize}
          style={{float: 'right', marginTop: '10px'}}/>
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(ResourceReport), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ResourceReportPage = permissionAccess(LogTimePutInPage);


