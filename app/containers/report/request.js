import { request } from '../../utils/request';
import { onRequestSuccess }  from '@/utils/api'

const requestInstance = request();

/**查看资源位报表 */
export function getResourceList(params) {
  return requestInstance.post(`api/putin/analyse/getResourceList`,params)
}

/**查看排期报表 */
export function getScheduleList(params) {
  return requestInstance.post(`/api/putin/analyse/getScheduleList`,params)
}

/**查看频道管理的所有资源位列表 */
export function getResourceMenuList() {
  return requestInstance.get(`/api/putin/analyse/getResourceMenuList`).then(onRequestSuccess)
}

/**资源位报表导出 */
export function exportResourceList(params) {
  return requestInstance.post(`/api/putin/analyse/exportResourceList`,params)
}

/**排期报表导出 */
export function exportScheduleList(params) {
  return requestInstance.post(`/api/putin/analyse/exportScheduleList`,params)
}