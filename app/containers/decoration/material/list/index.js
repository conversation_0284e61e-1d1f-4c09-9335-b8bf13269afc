import {Input, Select, Tab, Button, Table, Pagination, Grid} from '@alife/next'
import {FilterForm, FilterItem} from '@/components/filter'
import React from 'react'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link,withRouter} from 'react-router-dom'
import useTableQuery from '@/components/useTableQuery'
import {fetchList} from '../api'
import {parseQuery} from '../../common'
import './index.scss'

const {Row, Col} = Grid

function doQuery(params, pageNo, pageSize) {
  const query = {
    ...params,
    pageNo,
    pageSize,
  }
  return fetchList(query)
    .then((result) => {
      const {list, total} = result.data;
      return {
        list,
        total: +total,
      }
    })
}

function MaterialList({history, location, match}) {
  const route = {
    history, location, match
  }
  let newHistory = {
    ...history,
    location: {
      ...location,
      query: parseQuery(route)
    }
  };
  const query = useTableQuery(newHistory, doQuery, {}, true);
  return (
    <div className="container">
      <div className="title">
        <h2>素材列表</h2>
        <Link to="/materialCenter/list/edit/-new">
          <Button type="primary">创建素材</Button>
        </Link>
      </div>
      <div className="body">
        <FilterForm query={query}>
          <FilterItem label="素材ID" name="id">
            <Input placeholder="auto"/>
          </FilterItem>
          <FilterItem label="素材名称" name="materialName">
            <Input placeholder="auto"/>
          </FilterItem>
          <FilterItem label="创建人" name="createName">
            <Input placeholder="auto"/>
          </FilterItem>
          <FilterItem label="素材状态" name="state">
            <Select style={{width: '100%'}} hasClear>
              <Select.Option value="2">未使用</Select.Option>
              <Select.Option value="1">使用中</Select.Option>
            </Select>
          </FilterItem>
          <FilterItem>
            <Button type="primary" onClick={query.confirmInput}>查询</Button>
            &nbsp;
            <Button onClick={query.resetInput}>重置</Button>
          </FilterItem>
        </FilterForm>
        <QueryResult query={query} history={history}/>
      </div>
    </div>
  )
}

function QueryResult({query,history}) {
  return (
    <div>
      <Table dataSource={query.result.list}>
        <Table.Column title="素材ID" dataIndex="id"/>
        <Table.Column title="素材名称" dataIndex="materialName"/>
        <Table.Column title="素材创建时间" dataIndex="createTime" cell={(v) => (v || '').replace(/\.0$/, '')}/>
        <Table.Column title="素材状态" dataIndex="stateName"/>
        <Table.Column title="创建人" dataIndex="createName"/>
        <Table.Column title="操作" dataIndex="_" cell={(v, i, r) => <Operation record={r} query={query}history={history}/>}/>
      </Table>
      <br/>
      <Pagination {...query.pagination} />
    </div>
  )
}

function Operation({record, query,history}) {
  const {id} = record;

  return (
    <div>
      <Link to={`/materialCenter/list/detail/${id}`}>
        <Button type="primary" text>查看</Button>
      </Link>
      <i className="sep"></i>
      <Link to={`/materialCenter/list/edit/-${id}`}>
        <Button type="primary" text>复制</Button>
      </Link>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(MaterialList), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MaterialListPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})



