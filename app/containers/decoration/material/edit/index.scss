.material-edit {
  .wrap {
    display: inline-block;
    vertical-align: top;
    min-height: 125px;
    min-width: 120px;
    margin-right: 1em;
  }
  .error {
    position: relative;
    .tip {
      position: absolute;
      top: -4px;
      left: 0;
      color: #F00;
      font-size: 12px;
      white-space: nowrap;
    }
  }

  .desc {
    display: inline-block;
    vertical-align: top;
    line-height: 180%;
    color: #999;
    font-size: 10px;
    padding: 5px;
    button {
      margin-top: 10px;
    }
  }
  .color-preview {
    position: absolute;
    top: -35px;
    left: 1px;
    width: 5px;
    height: 34px;
    border-radius: 3px;
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
    &-wrap {
      position: relative;
    }
  }

  div.legend {
    font-size: 1.1em;
    color: #999;
    padding: 1em;
    margin-top: 10px;
  }
  //.next-overlay-wrapper.opened .next-overlay-backdrop{ //todo 修改弹窗
  //  opacity:1;
  //}
}
