import { Dialog, Icon, Message, Breadcrumb, Grid, Form, Input, Button, Upload } from '@alife/next'
import { Redirect, <PERSON>, withRouter } from 'react-router-dom'
import React,{useEffect, useState} from 'react'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import useForm from '@/components/useForm'
import { fetchList, createMaterial, editMaterial } from '../api'
// import { getUserInfo } from '@/lib/intercept-userinfo'
import uploadImg from '@/lib/upload-img'
import './index.scss';

const { Row, Col } = Grid

const imgFieldKeys = [
  'shopHeadPic', 'shopCartNotToBuyLogoPic',
  'shopCartToBuyLogoPic', 'shopCartPic', 'shopButtonPic',
  'shopMarkPic', 'itemMarkPic','promotionScreenPic','newMarkPic','newMarkOneVersionPic','oldMarkPic',
]

function MaterialEdit ({ history, match }) {
  const editId = match.params.id

  const { value, set, bind, bindField, useValidate, bindSubmit } = useForm({}, (value) => {
    const save = {
      materialName: value.materialName,
      colorValue: value.colorValue,
    }
    imgFieldKeys.forEach((key) => {
      const val = value[key]
      if (val && Array.isArray(val) && val[0]) {
        save[key] = val[0].url
      }
    })
    if (editId > 0) {
      save.id = editId
      return editMaterial(save)
        .then(() => {
          return () => {
            history.replace('../detail/' + editId)
          }
        })
    } else {
      // save.createName = getUserInfo().name
      return createMaterial(save)
        .then(() => {
          return () => {
            history.replace('/materialCenter/list')
          }
        })
    }
  })

  const [loading, setLoading] = useState(editId !== '-new')
  useEffect(() => {
    if (editId !== '-new') {
      const id = editId < 0 ? -editId : editId
      fetchList({ id })
        .then((result) => {
          result = result.data.list[0]
          const data = {
            materialName: result.materialName,
            ...result.mainListDTO,
            ...result.channelListDTO,
            ...result.shopDTO,
          }
          imgFieldKeys.forEach((key) => {
            const url = data[key]
            if (url) {
              data[key] = [{ name: url.split('/').pop(), url, state: 'done' }]
            }
          })
          set(data)
          setLoading(false)
        })
    }
  }, [editId])

  useValidate('materialName', (val, strict) => {
    if (strict && !val) {
      return '必须填写模板名称'
    }
  })

  useValidate('colorValue', (val, strict) => {
    if (!val) {
      return
    }
    if (!/^\#[0-9a-f]*$/i.test(val) || val.length > 7) {
      return '请填写正确的色值'
    }
    if (strict) {
      if (val.length !== 4 && val.length !== 7) {
        return '请填写正确的色值'
      }
    }
  })

  useValidate('_', (val, strict) => {
    if (strict) {
      for (let i = 0, n = imgFieldKeys.length; i < n; i++) {
        const fval = value[imgFieldKeys[i]]
        if (Array.isArray(fval) && fval[0]) {
          if (fval[0].state !== 'done') {
            return '有图片尚在上传中或者上传失败'
          }
        }
      }
    }
  })

  function cancel () {
    history.go(-1)
  }

  return (
    <div className="container material-edit">
      <div className="title">
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/materialCenter/list">素材中心</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            {editId === '-new' ? '新建' : '编辑'}素材
          </Breadcrumb.Item>
        </Breadcrumb>
      </div>
      <div className="body">
        <h2>新建素材</h2>
        <div style={{ height: 30 }} />
        <Segment title="基本信息">
          <FormItem label="模版名称：" required {...bindField('materialName')}>
            <Input
              placeholder="请输⼊入活动名称，会在商家端展示，不能超过20字"
              maxLength={20}
              hasLimitHint
              {...bind('materialName')}
            />
          </FormItem>
        </Segment>
        <Segment title="主站首页列表">
          <FormItem label="老版本角标">
            <ImageInput imgSize={[180, 54]} {...bind('oldMarkPic')} />
            <div className="desc">
              上传尺寸：180 * 54 px
              <br />
              上传格式：png
              <br />
              在老版本主站首页店铺列表展示，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('老版本角标示例', 'https://img.alicdn.com/tfs/TB1stiZhG67gK0jSZFHXXa9jVXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="新版本角标">
            <ImageInput imgSize={[177, 48]} {...bind('newMarkPic')} />
            <div className="desc">
              上传尺寸：177 * 48 px
              <br />
              上传格式：png
              <br />
              在新版本主站首页店铺列表展示，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('新版本角标示例', 'https://img.alicdn.com/tfs/TB1VKKZhKL2gK0jSZPhXXahvXXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="新版本角标1.0">
            <ImageInput imgSize={[102, 42]} {...bind('newMarkOneVersionPic')} />
            <div className="desc">
              上传尺寸：102 * 42 px
              <br />
              上传格式：png
              <br />
              在新版本主站首页店铺列表展示，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('新版本角标1.0示例', 'https://img.alicdn.com/tfs/TB1VKKZhKL2gK0jSZPhXXahvXXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
        </Segment>
        <Segment title="新零售频道页">
          <FormItem label="店铺角标">
            <ImageInput imgSize={[102, 42]} {...bind('shopMarkPic')} />
            <div className="desc">
              上传尺寸：102 * 42 px
              <br />
              上传格式：png
              <br />
              在新零售频道首页店铺列表展示，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('店铺角标示例', 'https://img.alicdn.com/tfs/TB1okq0hFP7gK0jSZFjXXc5aXXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="商品角标">
            <ImageInput imgSize={[102, 42]} {...bind('itemMarkPic')} />
            <div className="desc">
              上传尺寸：102 * 42 px
              <br />
              上传格式：png
              <br />
              在新零售频道首页商品列表展示，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('商品角标示例', 'https://img.alicdn.com/tfs/TB16BuZhLb2gK0jSZK9XXaEgFXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="rank角标">
            <ImageInput imgSize={[180, 39]} {...bind('promotionScreenPic')} />
            <div className="desc">
              上传尺寸：180 * 39 px
              <br />
              上传格式：png
              <br />
              在新零售频道首页店铺列表展示，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('rank角标示例', 'https://img.alicdn.com/tfs/TB16BuZhLb2gK0jSZK9XXaEgFXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
        </Segment>
        <Segment title="店内配置">
          <FormItem label="头部背景图">
            <ImageInput imgSize={[1125, 558]} {...bind('shopHeadPic')} />
            <div className="desc">
              上传尺寸：1125 * 558 px
              <br />
              上传格式：png
              <br />
              在新零售店铺顶部展示，不超过300K
              <br />
              <Button
                text
                onClick={showDemo('头部背景图示例', 'https://img.alicdn.com/tfs/TB1MEy0hQL0gK0jSZFAXXcA9pXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="购物车图标">
            <div style={{ color: '#999', fontSize: 10, padding: '10px 0 5px 0' }}>
              <span style={{ display: 'inline-block', width: 100 }}>未加购</span>
              <span style={{ display: 'inline-block', width: 100, marginLeft: 34 }}>已加购</span>
            </div>
            <ImageInput imgSize={[210, 210]} {...bind('shopCartNotToBuyLogoPic')} />
            <ImageInput imgSize={[210, 210]} {...bind('shopCartToBuyLogoPic')}  />
            <div className="desc">
              上传尺寸：210 * 210 px
              <br />
              上传格式：png
              <br />
              购物篮子图标，不超过50K
              <br />
              <Button
                text
                onClick={showDemo('购物车图标示例', 'https://img.alicdn.com/tfs/TB1W0O2hQL0gK0jSZFAXXcA9pXa-603-361.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="购物车背景">
            <ImageInput imgSize={[750, 164]} {...bind('shopCartPic')} />
            <div className="desc">
              上传尺寸：750 * 164 px
              <br />
              上传格式：png
              <br />
              购物车背景展示，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('购物车背景示例', 'https://img.alicdn.com/tfs/TB1E8y1hO_1gK0jSZFqXXcpaXXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="结算按钮">
            <ImageInput imgSize={[176, 72]} {...bind('shopButtonPic')} />
            <div className="desc">
              上传尺寸：176 * 72 px
              <br />
              上传格式：png
              <br />
              购物车结算按钮，不超过500K
              <br />
              <Button
                text
                onClick={showDemo('结算按钮示例', 'https://img.alicdn.com/tfs/TB1MeKZhKL2gK0jSZPhXXahvXXa-284-334.png')}
                type="primary"
              >查看示例</Button>
            </div>
          </FormItem>
          <FormItem label="活动色值" {...bindField('colorValue')}>
            <Input placeholder="凑单栏背景色，示例：#8913FF" {...bind('colorValue')} />
            <div className="color-preview-wrap">
              <div style={{ background: value.colorValue }} className="color-preview"></div>
            </div>
          </FormItem>
        </Segment>
        <Segment>
          <Row>
            <Col fixedSpan="8" />
            <Col>
              <Button onClick={cancel}>取消</Button>
              &nbsp;
              <Button type="primary" {...bindSubmit()}>保存</Button>
            </Col>
          </Row>
        </Segment>
      </div>
    </div>
  )
}

const demoSizeCache = {}
function showDemo (title, src) {
  let size = demoSizeCache[src]
  if (!size) {
    size = new Promise((resolve) => {
      const img = document.createElement('img')
      img.onload = () => {
        resolve([img.naturalWidth, img.naturalHeight])
      }
      img.src = src
    })
    demoSizeCache[src] = size
  }
  return () => {
    size.then(([w, h]) => {
      Dialog.show({
        title,
        content: (
          <div style={{ minWidth: 500, textAlign: 'center' }}>
            <img src={src} width={w} height={h} />
          </div>
        ),
        cancelProps: { style: { display: 'none' } },
      })
    })
  }
}


function Segment ({ title, children }) {
  return (
    <Row>
      <Col offset="2" span="13">
        <div className="legend">{title}</div>
        {children}
      </Col>
    </Row>
  )
}

function FormItem ({ ...props }) {
  return <Form.Item {...props} labelCol={{ fixedSpan: 8 }} />
}

function ImageInput ({ desc = '', imgSize: size = [100, 100], value, onChange: setValue }) {
  const [tip, setTip] = useState('')
  const [uploadCounter, setUploadCounter] = useState(0)
  if (!setValue) {
    ;([value, setValue] = useState([]))
  }
  const beforeUpload = (file, options) => {
    const counter = uploadCounter
    validateImage(file, size)
      .then((base64) => {
        setValue([{ name: file.name, url: base64, state: 'uploading', percent: 10 }])
        setTimeout(() => {
          setUploadCounter((cur) => {
            if (cur === counter) {
              setValue([{ name: file.name, url: base64, state: 'uploading', percent: 70 }])
            }
            return cur
          })
        }, 10);
        return uploadImg(base64);
      })
      .then((url) => {
        setUploadCounter((cur) => {
          if (cur === counter) {
            setValue([{ name: file.name, url, state: 'done' }])
            setTip('')
            cur++
          }
          return cur
        })
      })
      .catch((error) => {
        setUploadCounter((cur) => {
          if (cur === counter) {
            if (error && error.message) {
              error = error.message
            }
            setValue([{ name: '', state: 'error' }])
            setTip(error)
            cur++
          }
          return cur
        })
      })
    return false
  }
  function remove () {
    setValue([])
    setTip('')
  }
  function change (v) {
    if (v && v.length === 0) {
      setUploadCounter((cur) => cur + 1)
      remove()
    }
  }
  function preview () {
    let [w, h] = size
    if (w < 200) {
      w *= 2
      h *= 2
    }
    Dialog.show({
      title: '图片预览',
      content: (
        <div style={{ minWidth: 500, textAlign: 'center' }}>
          <img src={value[0].url} width={w} height={h} />
        </div>
      ),
      cancelProps: { style: { display: 'none' } },
    })
  }
  return (
    <div className="wrap">
      <Upload.Card
        limit={1}
        beforeUpload={beforeUpload}
        listType="card"
        accept="image/png"
        value={value ? value : []}
        onRemove={remove}
        onPreview={preview}
        onChange={change}
      />
      <div className="error">
        <div className="tip">{tip}</div>
      </div>
    </div>
  )
}

function validateImage (file, size) {
  return Promise.resolve()
    .then(() => {
      const size = +(file.size / 1024).toFixed(2)
      if (size > 500) {
        throw new Error('文件超出500K限制')
      }
      return new Promise((resolve, reject) => {
        const fileReader = new FileReader()
        fileReader.onload = () => {
          resolve(fileReader.result)
        }
        fileReader.onerror = () => {
          reject(new Error('图片读取失败'))
        }
        fileReader.readAsDataURL(file)
      })
    })
    .then((base64) => {
      const mime = base64.substr(0, 50).split(';')[0].split(':')[1]
      if (mime !== 'image/png') {
        throw new Error(`检测文件类型为${mime}，不是正常的PNG格式`)
      }
      return new Promise((resolve, reject) => {
        const img = document.createElement('img')
        img.onload = () => {
          resolve(img)
        }
        img.onerror = () => {
          reject(new Error('图片读取失败'))
        }
        img.src = base64
      })
    })
    .then((img) => {
      const w = img.naturalWidth
      const h = img.naturalHeight
      if (w * h < size[0] * size[1] || w / h !== size[0] / size[1]) {
        throw new Error('上传尺寸不符合要求')
      }
      return img.src
    })
}

export const LogTimePutInPage = logTimeComponent(withRouter(MaterialEdit), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MaterialEditPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})
