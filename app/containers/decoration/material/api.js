import { decorationReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

export function fetchList (params) {
  return decorationReq.post('/api/material/getMaterialList', params).then(onRequestSuccess)
}

export function createMaterial (params) {
  return decorationReq.post('/api/material/createMaterial', params).then(onRequestSuccess)
}

export function editMaterial (params) {
  return decorationReq.post('/api/material/editMaterial', params).then(onRequestSuccess)
}
