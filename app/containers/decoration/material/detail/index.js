import {Dialog, Loading, Breadcrumb, Button, Grid} from '@alife/next'
import {<PERSON>, withRouter} from 'react-router-dom'
import React,{useEffect, useState} from 'react'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import SimpleTable from '@/components/SimpleTable'
import {fetchList} from '../api'
import './index.scss'

const {Row, Col} = Grid;

function MaterialDetail({match, history}) {
  const id = match.params.id
  const [data, setData] = useState()
  useEffect(() => {
    fetchList({id})
      .then((result) => {
        setData(result.data.list[0])
      })
  }, [0])
  return (
    <div className="container">
      <div className="title">
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/materialCenter/list">素材中心</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            素材详情
          </Breadcrumb.Item>
        </Breadcrumb>
      </div>
      {!data ? (
        <Loading inline={false}>
          <div className="body">
          </div>
        </Loading>
      ) : (
        <div className="body">
          <Row>
            <Col span="12">
              <h2 style={{marginTop: 0}}>
                素材详情
              </h2>
            </Col>
            {+data.state === 1 ? null : (
              <Col span="10" style={{textAlign: 'right'}}>
                <Link to={`../edit/${id}`}>
                  <Button text type="primary">修改</Button>
                </Link>
              </Col>
            )}
          </Row>
          <Row>
            <Col offset="2" span="20">
              <SimpleTable title="基础信息">
                <SimpleTable.Item label="素材状态">
                  {data.stateName}
                </SimpleTable.Item>
                <SimpleTable.Item label="素材名称">
                  {data.materialName}
                </SimpleTable.Item>
                <SimpleTable.Item label="素材ID">
                  {data.id}
                </SimpleTable.Item>
                <SimpleTable.Item label="创建人">
                  {data.createName}
                </SimpleTable.Item>
                <SimpleTable.Item label="创建时间">
                  {(data.createTime || '').replace(/\.0$/, '')}
                </SimpleTable.Item>
              </SimpleTable>
              <SimpleTable title="基础元素">
                <SimpleTable.Item label="主站首页列表">
                  <Image src={data.mainListDTO.oldMarkPic}>
                    老版本角标
                  </Image>
                  <Image src={data.mainListDTO.newMarkPic}>
                    新版本角标
                  </Image>
                  <Image src={data.mainListDTO.newMarkOneVersionPic}>
                    新版本角标1.0
                  </Image>
                </SimpleTable.Item>
                <SimpleTable.Item label="新零售频道列表">
                  <Image src={data.channelListDTO.shopMarkPic}>
                    店铺角标
                  </Image>
                  <Image src={data.channelListDTO.itemMarkPic}>
                    商品角标
                  </Image>
                  <Image src={data.channelListDTO.promotionScreenPic}>
                    rank角标
                  </Image>
                </SimpleTable.Item>
                <SimpleTable.Item label="店铺内配置">
                  <Image src={data.shopDTO.shopHeadPic}>
                    店铺装修头部背景图
                  </Image>
                  <Image src={data.shopDTO.shopCartNotToBuyLogoPic}>
                    未加购购物车图标
                  </Image>
                  <Image src={data.shopDTO.shopCartToBuyLogoPic}>
                    已加购购物车图标
                  </Image>
                  <Image src={data.shopDTO.shopCartPic}>
                    购物车背景
                  </Image>
                  <Image src={data.shopDTO.shopButtonPic}>
                    结算按钮
                  </Image>
                  <div style={{display: 'inline-block', verticalAlign: 'top', marginTop: 3}}>
                    活动色值：{data.shopDTO.colorValue}
                  </div>
                </SimpleTable.Item>
              </SimpleTable>
            </Col>
          </Row>
        </div>
      )}
    </div>
  )
}

function Image({src, children}) {
  function preview() {
    const img = document.createElement('img')
    img.onload = () => {
      let w = img.naturalWidth
      let h = img.naturalHeight
      if (w < 200) {
        w *= 2
        h *= 2
      }
      Dialog.show({
        title: '图片预览',
        content: (
          <div style={{minWidth: 500, textAlign: 'center'}}>
            <img src={src} width={w} height={h}/>
          </div>
        ),
        cancelProps: {style: {display: 'none'}},
      })
    }
    img.src = src
  }

  return (
    <div className="wrap detail-img">
      <div className="title">
        {children}
      </div>
      <div className="image" onClick={preview} style={{backgroundImage: `url(${src})`}}></div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(MaterialDetail), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const MaterialDetailPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})

