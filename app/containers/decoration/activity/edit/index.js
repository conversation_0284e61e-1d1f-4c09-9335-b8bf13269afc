import {
  Message,
  Loading,
  Icon,
  Step,
  Breadcrumb,
  Grid,
  Form,
  Select,
  Input,
  DatePicker,
  Radio,
  Button,
  NumberPicker,
  Upload
} from '@alife/next'
import React, {useState, useEffect} from 'react'
import {Redirect, <PERSON>, withRouter,Switch} from 'react-router-dom'
import useForm from '@/components/useForm'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {parseQuery} from '../../common';
import {permissionAccess} from '@/components/PermissionAccess';
import DayOfWeekPicker from '@/components/DayOfWeekPicker'
import TimeRangePicker from '@/components/timeRange'
// import {getUserInfo} from '@/lib/intercept-userinfo'
import PreviewMaterial from '../detail/materialCard'
import {fetchList, fetchMaterialList, createActivity, editActivity, downloadTemplate, queryPoolByIdOrName} from '../api'
import uploadFile from "@/lib/upload-file";
import './index.scss';

const {Row, Col} = Grid

const formItemLayout = {
  labelCol: {
    fixedSpan: 8,
  },
  wrapperCol: {},
}

let preservedData

try {
  preservedData = JSON.parse(localStorage.getItem('decoration:activity:edit'))
} catch (err) {

}

let saveFormDataTimeout

function saveFormData() {
  if (saveFormDataTimeout) {
    clearTimeout(saveFormDataTimeout)
  }
  saveFormDataTimeout = setTimeout(() => {
    localStorage.setItem('decoration:activity:edit', JSON.stringify(preservedData))
    saveFormDataTimeout = null
  }, 500)
}

function ActEdit({location, match, history}) {
  const route = {
    history, location, match
  };
  let query = parseQuery(route);
  const editId = match.params.id
  const step = +query.step;

  // 跨路由共享状态，每一个step都会跨路由
  let defaultFormData
  let isPreservedData
  if (preservedData && query._hash === preservedData._hash) {
    defaultFormData = preservedData
    isPreservedData = true
  } else {
    defaultFormData = {
      _hash: Math.random().toFixed(8).substr(2),
      effectTime: ['00:00:00', '23:59:59'],
    }
    isPreservedData = false
  }
  const [formData, setFormData] = useState(defaultFormData)

  if (!query.step || !query._hash) {
    const params = {
      ...query,
      step: query.step || 0,
      _hash: formData._hash,
    }
    const search = Object.keys(params).map((k) => [k, params[k]].map(encodeURIComponent).join('=')).join('&');
    history.replace(location.pathname + '?' + search);
    // return <Redirect to={{pathname:location.pathname,search:'?' + search}} push />;
  }
  preservedData = {...formData, _hash: query._hash}

  const needLoad = editId && editId !== '-new' && !isPreservedData
  const [loading, setLoading] = useState(needLoad)
  useEffect(() => {
    if (needLoad) {
      const id = editId < 0 ? -editId : editId
      fetchList({id})
        .then((result) => {
          const {list, total} = result.data
          const data = {...list[0]}
          data.effectDate = [data.effectStartDate || '', data.effectEndDate || '']
          data.effectTime = [
            (data.effectStartTime || '').replace(/^.*\s|\..*$/g, ''),
            (data.effectEndTime || '').replace(/^.*\s|\..*$/g, ''),
          ]
          data.weeks = (data.weeks || '').split('').map((v) => +v).filter(v => v)
          if (data.materialId && data.materialName) {
            data.materialId = `${data.materialName} (${data.materialId})`
          }
          data.activityIds = (data.activityIds || '').split(',').join('\n')
          if (+data.promotionType === 0) {
            delete data.promotionType
          }
          if (editId < 0) {
            delete data.id
          }
          setFormData({
            ...formData,
            ...data,
          })
          setLoading(false)
        })
    }
  }, [0])
  useEffect(() => {
    saveFormData()
  }, [formData])

  const setStep = (value, push = false) => {
    const params = {
      ...query,
      step: value,
    }
    const target = location.pathname + '?'
      + Object.keys(params).map((k) => [k, params[k]].map(encodeURIComponent).join('=')).join('&')
    if (push) {
      history.push(target)
    } else {
      history.replace(target)
    }
  }
  const nextStep = ({...value}) => {
    if (query.from !== 'detail' && step < 2) {
      return () => {
        setStep(step + 1, true)
      }
    } else {
      const offset = new Date().getTimezoneOffset() * 60 * 1000

      function toPlainTime(t) {
        return new Date(new Date(t).getTime() - offset).toISOString().split('T')
      }

      function toPlainDate(t) {
        return toPlainTime(t)[0]
      }
      ;([value.effectStartDate, value.effectEndDate] = value.effectDate.map(toPlainDate))
      delete value.effectDate
      ;([value.effectStartTime, value.effectEndTime] = value.effectTime)
      delete value.effectTime
      delete value._hash
      value.weeks = value.weeks.join('')
      if (!value.createName) {
        // value.createName = getUserInfo().name
        value.createTime = toPlainTime(Date.now()).join(' ')
      }
      if (!value.poolIds) {
        value.poolIds = '';
      }
      // value.updateName = getUserInfo().name
      value.updateTime = toPlainTime(Date.now()).join(' ')
      // value.activityIds = value.activityIds.split(/[\n\,]/).filter(v => v).join(',')
      if (value.materialId) {
        const m = value.materialId.toString().match(/^(.*) \(([0-9]+)\)$/); //后端materialId改成字符串，可去掉toString
        if (m) {
          value.materialName = m[1]
          value.materialId = m[2]
        }
      }
      const configSet = {
        "prod":1,
        "ppe":2,
        "daily":3,
      }
      value.environmentType = configSet[window.configEnv] ? configSet[window.configEnv] : 3;
      let save
      let back
      if (!value.id) {
        save = createActivity(value)
        back = () => {
          history.replace('/decorationActivity/list?filter=' + JSON.stringify({"showType": sessionStorage.getItem("showType")}));
        }
      } else {
        save = editActivity(value)
        back = () => {
          history.replace('/decorationActivity/list/detail/' + value.id)
        }
      }
      return save
        .then((result) => {
          if (+result.errorCode) {
            throw result
          }
          localStorage.removeItem('decoration:activity:edit')
          return back
        })
        .catch((error) => {
          const {errorCode, errorDesc, errorMsg} = error
          if (+errorCode === 1) {
            Message.warning(errorDesc || errorMsg)
            localStorage.removeItem('decoration:activity:edit')
            return new Promise((r) => {
              setTimeout(r, 2000)
            })
              .then(() => back)
          } else {
            throw new Error(errorDesc || errorMsg)
          }
        })
    }
  }
  const goBack = () => {
    history.go(-1)
  }
  const cancel = () => {
    if (query.from === 'detail') {
      goBack()
    } else {
      history.replace('..')
    }
  }
  return (
    <div className="container activity-edit">
      <div className="title">
        <Breadcrumb children={[
          <Breadcrumb.Item>
            <Link to="/decorationActivity/list">装修活动</Link>
          </Breadcrumb.Item>,
          query.from === 'detail' ? (
            <Breadcrumb.Item onClick={goBack} style={{cursor: 'pointer'}}>
              活动详情
            </Breadcrumb.Item>
          ) : null,
          <Breadcrumb.Item>
            {editId === '-new' ? '创建' : '编辑'}装修活动
          </Breadcrumb.Item>,
        ].filter(v => !!v)}>
        </Breadcrumb>
      </div>
      {loading ? (
        <Loading inline={false} visible={loading}>
          <div className="body"></div>
        </Loading>
      ) : (
        <div className="body">
          {query.from === 'detail' ? (
            <h2>
              <Button text onClick={goBack}>
                <Icon type="arrow-left"/>返回
              </Button>
              &nbsp;&nbsp;&nbsp;&nbsp;
              {['基础信息修改', '素材换绑', '关联活动修改', '添加白名单'][step]}
            </h2>
          ) : (
            <Step shape="circle" current={step}>
              <Step.Item title="基础信息"/>
              <Step.Item title="基础元素"/>
              <Step.Item title="关联活动"/>
            </Step>
          )}
          <div style={{height: query.from === 'detail' ? 20 : 80}}/>
          <Row>
            <Col span="4"/>
            <Col span="15">
              {step === 0 ? (
                <Step1
                  from={query.from}
                  onSubmit={nextStep}
                  data={formData}
                  setData={setFormData}
                  onCancel={cancel}
                  onBack={() => setStep(step - 1)}
                />
              ) : step === 1 ? (
                <Step2
                  from={query.from}
                  onSubmit={nextStep}
                  data={formData}
                  setData={setFormData}
                  onCancel={cancel}
                  onBack={() => setStep(step - 1)}
                />
              ) : step === 2 ? (
                <Step3
                  from={query.from}
                  onSubmit={nextStep}
                  data={formData}
                  setData={setFormData}
                  onCancel={cancel}
                  onBack={() => setStep(step - 1)}
                />
              ) : step === 3 ? (
                <Step4
                  from={query.from}
                  onSubmit={nextStep}
                  data={formData}
                  setData={setFormData}
                  onCancel={cancel}
                  onBack={() => setStep(step - 1)}
                />
              ) : null}
            </Col>
          </Row>
        </div>
      )}
    </div>
  )
}

function Step1({from, data, setData, onCancel, onSubmit}) {
  const {bind, bindField, useValidate, submit} = useForm([data, setData], onSubmit)
  useValidate('activityName', (val, strict) => {
    if (strict && !val) {
      return '必须填写名称'
    } else if (val && val.length > 20) {
      return '超过20字符'
    }
  })
  useValidate('effectDate', (val, strict) => {
    if (strict) {
      if (!val || !val[0] || !val[1]) {
        return '请选择活动装修的生效日期'
      }
    }
  })
  useValidate('effectTime', (val, strict) => {
    if (strict) {
      if (!val || !val[0] || !val[1]) {
        return '请选择活动装修的生效时段'
      }
      if (new Date('2000-1-1 ' + val[0]).getTime() >= new Date('2000-1-1 ' + val[1])) {
        return '生效时段结束时间必须大于开始时间'
      }
    }
  })
  useValidate('weeks', (val, strict) => {
    if (strict) {
      if (!val || !val.length) {
        return '请选择活动装修的生效的星期'
      }
    }
  })
  return (
    <Form {...formItemLayout}>
      <Form.Item label="装修活动名称" required {...bindField('activityName')}>
        <Input
          placeholder="输入1～20个中文、英文、数字字符"
          maxLength={20}
          hasLimitHint
          {...bind('activityName')}
        />
      </Form.Item>
      <Form.Item label="生效时间" required {...bindField('effectDate')}>
        <DatePicker.RangePicker {...bind('effectDate')} />
      </Form.Item>
      <Row>
        <Col {...formItemLayout.labelCol} />
        <Col {...formItemLayout.wrapperCol}>
          <div style={{background: '#fafafa', padding: 10, marginBottom: 10}}>
            <Form.Item label="星期" labelCol={{fixedSpan: 2}} {...bindField('weeks')}>
            <DayOfWeekPicker {...bind('weeks')} />
            </Form.Item>
            <Form.Item label="时段" labelCol={{fixedSpan: 2}} {...bindField('effectTime')}>
              <TimeRangeInput {...bind('effectTime')}/>
            </Form.Item>
          </div>
        </Col>
      </Row>
      <Form.Item label="权重" required {...bindField('weight')}>
        <NumberPicker {...bind('weight')} />
      </Form.Item>
      <Form.Item label="大促项目选项" {...bindField('promotionType')}>
        <Select style={{width: '100%'}} {...bind('promotionType')} placeholder="请选择大促项目" hasClear>
          <Select.Option value={1}>夏季战役</Select.Option>
          <Select.Option value={2}>双十一</Select.Option>
          <Select.Option value={3}>517</Select.Option>
          <Select.Option value={4}>年货节</Select.Option>
          <Select.Option value={5}>冬季战役</Select.Option>
          <Select.Option value={6}>618</Select.Option>
        </Select>
      </Form.Item>
      <Row>
        <Col {...formItemLayout.labelCol} />
        <Col {...formItemLayout.wrapperCol}>
          <Button onClick={onCancel}>取消</Button>
          &nbsp;
          <Button type="primary" onClick={submit}>
            {from === 'detail' ? '保存' : '下一步'}
          </Button>
        </Col>
      </Row>
    </Form>
  )
}

function TimeRangeInput({value, onChange, state: thruState}) {
  let selection
  if (value) {
    const [start, end] = value
    if (start === '00:00:00' && end === '23:59:59') {
      selection = 'full'
    } else {
      selection = 'pick'
    }
  }
  const [state, setState] = useState({value, selection})
  const onSelect = (val) => {
    setState({
      ...state,
      selection: val,
    })
    if (val === 'full') {
      onChange(['00:00:00', '23:59:59'])
    }
  }
  const onSetRange = (value) => {
    setState({value, selection: 'pick'})
    onChange(value)
  }
  return (
    <Radio.Group onChange={onSelect} value={state.selection}>
      <div>
        <Radio value="full">全天</Radio>
      </div>
      <div>
        <Radio value="pick">选择生效时段</Radio>
        <TimeRangePicker state={thruState} value={state.value} onChange={onSetRange}/>
      </div>
    </Radio.Group>
  )
}

let deferValue

function Step2({from, data, setData, onCancel, onSubmit, onBack}) {
  const {value, set, bind, bindField, useValidate, submit} = useForm([data, setData], onSubmit)
  const [sug, setSug] = useState([])
  const [focus, setFocus] = useState(false)
  const [materialDetail, setMaterialDetail] = useState()

  useValidate('materialId', (val, strict) => {
    if (strict && !materialDetail) {
      return '必须选择一个有效的素材'
    }
  })

  useEffect(() => {
    deferValue = value.materialId
    setTimeout(() => {
      if (deferValue !== value.materialId) {
        return
      }
      setMaterialDetail(null)
      let materialName = value.materialId ? (value.materialId.toString() || '').replace(/^\s+|\s+$/g, '') : '';  //后端materialId改成字符串，可去掉toString
      if (materialName) {
        let id = materialName
        const m = id.match(/\(([0-9]+)\)\s*$/)
        if (m) {
          id = m[1]
        } else if (!/^[0-9]+$/.test(id)) {
          id = ''
        }
        const idSearch = id ? fetchMaterialList({ id }) : Promise.resolve({ data: { list: []  } })
        idSearch
          .then((result) => {
            const val = result.data.list[0]
            if (val) {
              setMaterialDetail(val)
            }
          })
        Promise.all([idSearch, fetchMaterialList({ materialName, pageNo: 1, pageSize: 10 })])
          .then(([idResult, result]) => {
            setSug([...idResult.data.list, ...result.data.list].map((item) => {
              return `${item.materialName} (${item.id})`
            }))
          })
      }
    }, 300)
  }, [value.materialId])
  return (
    <Form {...formItemLayout}>
      <Form.Item label="选择素材" required {...bindField('materialId')}>
        <Select.AutoComplete
          placeholder="请输入素材名称"
          style={{width: '100%'}}
          {...bind('materialId')}
          dataSource={sug}
          visible={focus && (sug.length > 1 || !materialDetail)}
          onFocus={() => setFocus(true)}
          onBlur={() => setFocus(false)}
          hasClear
        />
      </Form.Item>
      <Row>
        <Col {...formItemLayout.labelCol} />
        <Col {...formItemLayout.wrapperCol}>
          <Link to="/materialCenter/list/edit/-new" target="_blank">
            <Button text type="primary">无可用素材？点击创建</Button>
          </Link>
        </Col>
      </Row>
      <br/><br/>
      <Row>
        <Col {...formItemLayout.labelCol} />
        <Col {...formItemLayout.wrapperCol}>
          {materialDetail ? (
            <PreviewMaterial value={materialDetail}/>
          ) : null}
        </Col>
      </Row>
      <Row>
        <Col {...formItemLayout.labelCol} />
        <Col {...formItemLayout.wrapperCol}>
          <Button onClick={onCancel}>取消</Button>
          &nbsp;
          {from === 'detail' ? null : (
            <Button onClick={onBack}>上一步</Button>
          )}
          &nbsp;
          <Button type="primary" onClick={submit}>
            {from === 'detail' ? '保存' : '下一步'}
          </Button>
        </Col>
      </Row>
    </Form>
  )
}

function Step3({from, data, setData, onCancel, onSubmit, onBack}) {
  const {bind, bindField, useValidate, submit} = useForm([data, setData], onSubmit)
  // const [searchTimeout, setSearchTimeout] = useState();
  const [poolIdsValue, setPoolIdsValue] = useState((data && data.poolIds) ? data.poolIds.split(",") : []);
  const [poolIdsSet, setPoolIdsSet] = useState([]);
  useValidate('poolIds', (val, strict) => {
    if (strict) {
      // if (!val) {
      //   return '必须至少填写一个选品集ID'
      // }
      if (val && val.split(/[\n\,]/).filter(v => !!v).length > 10) {
        return '最多可填写10个选品集ID'
      }
    }
  })
  const onChange = (value,actionType) => {
    if(actionType=='itemClick' || actionType=='tag'){
      data.poolIds = value.join(",");
      setData(data);
      setPoolIdsValue(value);
    }
  }

  let searchTimeout;
  const onSearch = (keyword) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      if(keyword){
        queryPoolByIdOrName({poolContent: keyword}).then((data) => {
          const dataSource = data.map(item => ({
            label:item.poolContent, value:item.poolId
          }));
          // setDataSource(dataSource);
          setPoolIdsSet(dataSource);
          setLocalPoolIdsSet(dataSource);
          // this.setState({poolIdsSet:dataSource},()=>{
          //   this.setLocalPoolIdsSet(dataSource);
          // })
        })
      }else{
        setPoolIdsSet([]);
        sessionStorage.setItem('materialPoolIdsSet',JSON.stringify([]));
      }
    }, 800);
  }

  const setLocalPoolIdsSet = (dataSource) =>{
    const LocalPoolIdsSet = sessionStorage.getItem('materialPoolIdsSet')?JSON.parse(sessionStorage.getItem('materialPoolIdsSet')):[];
    let wholePoolIdsSet = LocalPoolIdsSet.concat(dataSource);
    const newWholePoolIdsSet = removeDuplicate(wholePoolIdsSet);
    sessionStorage.setItem('materialPoolIdsSet',JSON.stringify(newWholePoolIdsSet));
  }

  const removeDuplicate = (arr) =>{
    var hash = {};
    let result = arr.reduce(function(item, next) {
      hash[next.value] ? '' : hash[next.value] = true && item.push(next);
      return item
    }, []);
    return result;
  }
  // const poolIdsValue = (data && data.poolIds) ? data.poolIds :[];
  const poolIdsSetDateSource = poolIdsSet.length > 0 ? poolIdsSet : JSON.parse(sessionStorage.getItem('materialPoolIdsSet'));
  return (
    <Form {...formItemLayout}>
      {/*<Form.Item label="关联活动" required {...bindField('activityIds')}>*/}
      {/*  <Input.TextArea*/}
      {/*    placeholder={'请输入要关联的活动ID，每行填写一个活动ID，示例如下：\n123456\n123457\n123458'}*/}
      {/*    rows={15}*/}
      {/*    {...bind('activityIds')}*/}
      {/*  />*/}
      {/*</Form.Item>*/}
      <Form.Item label="关联选品集"  {...bindField('poolIds')}>
        <Select mode="multiple" showSearch placeholder="请输入选品集ID，支持输入最多10个" value={poolIdsValue}   onChange={(value,actionType)=>onChange(value,actionType)}  onSearch={onSearch}  name='poolIds' dataSource={poolIdsSetDateSource}  style={{width:'100%'}} />
      </Form.Item>
      <Row>
        <Col {...formItemLayout.labelCol} />
        <Col {...formItemLayout.wrapperCol}>
          <Button onClick={onCancel}>取消</Button>
          &nbsp;
          {from === 'detail' ? null : (
            <Button onClick={onBack}>上一步</Button>
          )}
          &nbsp;
          <Button type="primary" onClick={submit}>
            {from === 'detail' ? '保存' : '完成'}
          </Button>
        </Col>
      </Row>
    </Form>
  )
}

function Step4({from, data, setData, onCancel, onSubmit, onBack}) {
  const {bind, bindField, useValidate, submit} = useForm([data, setData], onSubmit)
  useValidate('activityIds', (val, strict) => {
    if (strict) {
      if (!val) {
        return '必须至少填写一个活动ID'
      }
      if (val.split(/[\n\,]/).filter(v => !!v).length > 100) {
        return '最多可填写100个活动ID'
      }
    }
  })
  const [value, setValue] = useState([]);
  const [uploadCounter, setUploadCounter] = useState(0)

  function beforeUpload(file, options, activityId) {
    const counter = uploadCounter;
    let fileWholeName = file.name;
    let lastIndex = fileWholeName.lastIndexOf(".");
    let fileLength = fileWholeName.length;
    let fileSuffix = fileWholeName.substring(lastIndex + 1, fileLength);
    let fileName = fileWholeName.substring(0, lastIndex);
    validateFile(file)
      .then((base64) => {
        setValue([...value, {name: file.name, url: base64, state: 'uploading', percent: 10}]);
        setTimeout(() => {
          setUploadCounter((cur) => {
            if (cur === counter) {
              setValue([...value, {name: file.name, url: base64, state: 'uploading', percent: 70}])
            }
            return cur
          })
        }, 10)
        return uploadFile(base64, {fileName, fileType: (fileSuffix == 'xlsx') ? 1 : 2, activityId})
      })
      .then((url) => {
        setUploadCounter((cur) => {
          if (cur === counter) {
            setValue([...value, {name: file.name, url, state: 'done'}]);
            Message.success("上传成功");
            cur++
          }
          return cur
        })
      })
      .catch((error) => {
        setUploadCounter((cur) => {
          if (cur === counter) {
            if (error && error.message) {
              error = error.message
            }
            setValue([...value, {name: file.name, state: 'error'}])
            cur++
          }
          return cur
        })
      })
    return false
  }

  function validateFile(file) {
    return Promise.resolve()
      .then(() => {
        const size = +(file.size / 1024).toFixed(2)
        if (size > 600) {
          let errMsg = '文件超出600K限制，请重新上传';
          Message.error(errMsg);
          throw new Error(errMsg)
        }
        return new Promise((resolve, reject) => {
          const fileReader = new FileReader()
          fileReader.onload = () => {
            resolve(fileReader.result)
          }
          fileReader.onerror = () => {
            reject(new Error('文件读取失败，请重新上传'))
          }
          fileReader.readAsDataURL(file)
        })
      })
      .then((base64) => {
        const mine = base64.substr(0, 50).split(';')[0].split(':')[1];
        if (mine !== 'application/vnd.openxmlformats-officedocument' && mine !== 'application/octet-stream') {
          let errMsg = `检测文件类型为${mine}，不是允许上传的excel格式，请重新上传`;
          Message.error(errMsg);
          throw new Error(errMsg);
        }
        return new Promise((resolve, reject) => {
          resolve(base64);
        })
      })
  }

  function remove() {
    setTimeout(() => {
      setValue(value.filter(v => v.state != 'removed'));
    }, 10)
  }

  function downloadUrl() {
    return downloadTemplate()
      .then((result) => {
        const downloadUrl = result.data;
        let downloadEle = document.createElement('a');
        downloadEle.download = '白名单导入模板.xlsx';
        downloadEle.href = downloadUrl;
        downloadEle.click();
      }).catch((error) => {
        if (error.data) {
          const {errorCode, errorDesc, errorMsg} = error.data
          if (+errorCode === 1) {
            Message.warning(errorDesc || errorMsg)
          } else {
            throw new Error(errorDesc || errorMsg)
          }
        }
      })
  }

  const Hint = '支持扩展名：.xlsx，.xls';
  const Accept = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel';
  return (
    <Form {...formItemLayout}>
      <Form.Item label="添加方式" {...bindField('activityIds')} className='step4'>
        <p className='tips'>1、请先下载<a href='javascript:void(0)' onClick={() => downloadUrl()} className='download-excel'>白名单导入模版</a>，按照要求填写完成，上传到系统；</p>
        <p className='tips lst'>2、提交活动后在【活动详情】中查看，或继续补充上传文件。</p>
        <div className='upload-source'>
          <Upload.Dragger
            listType="text"
            value={value}
            accept={Accept}
            onRemove={remove}
            beforeUpload={(file, options) => beforeUpload(file, options, data.id)}
          >
            <div className="upload-text">
              <Icon type="upload" style={{color: '#333333'}} size={'xl'}/>
              <p className='download'>点击或将文件拖拽到这里上传</p>
              <p className='hint'>{Hint}</p>
            </div>
          </Upload.Dragger>
        </div>
      </Form.Item>
    </Form>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(ActEdit), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ActEditPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})

