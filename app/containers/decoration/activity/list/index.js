import React,{useState, useEffect} from 'react'
import {ACLPermission} from "@/containers//PutInPage/request";
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {FilterForm, FilterItem} from '@/components/filter'
import {Link, withRouter} from 'react-router-dom';
import useTableQuery from '@/components/useTableQuery'
import {parseQuery} from '../../common'
import {ACLAccess} from '@/components/ACLAccess'
import {Button, Input, Select, Table, Pagination, Tab, Dialog} from '@alife/next';
import {fetchList, fetchMaterialList, promotionACL, updateActivityState} from '../api'
import './index.scss';

function doQuery (params, curPage, pageSize) {
  const query = {
    ...params,
    pageNo: curPage,
    pageSize,
  }
  return fetchList(query)
    .then((result) => {
      const { list, total } = result.data;
      list.forEach((item) => {
        item.poolIds = item.poolIds ? item.poolIds.split(','):'';
        item.poolIdsNum = item.poolIds.length
      })
      return {
        list,
        total: +total,
      }
    })
}

function ActPage({history, location, match}) {
  const route = {
    history, location, match
  }
  let newHistory = {
    ...history,
    location: {
      ...location,
      query: parseQuery(route)
    }
  };
  const query = useTableQuery(newHistory, doQuery, { showType: 1 }, true);
  // console.log(query);
  const { bindInput, setField, filterInput } = query
  const changeTab = (val) => {
    setField({ showType: +val + 1 }, true)
  }
  return (
    <div className="container">
      <div className="title">
        <h2>装修活动列表</h2>
        <Link to="/decorationActivity/list/edit/-new">
          <Button type="primary">创建装修活动</Button>
        </Link>
      </div>
      <div className="body">
        <FilterForm query={query}>
          <FilterItem label="装修活动ID" name="id">
            <Input placeholder="auto" />
          </FilterItem>
          <FilterItem label="装修活动名称" name="activityName">
            <Input placeholder="auto" />
          </FilterItem>
          <FilterItem label="创建人" name="createName">
            <Input placeholder="auto" />
          </FilterItem>
          <FilterItem label="装修活动状态" name="state">
            <Select placeholder="auto" hasClear>
              <Select.Option value="1">待生效</Select.Option>
              <Select.Option value="2">已生效</Select.Option>
              <Select.Option value="3">已下线</Select.Option>
            </Select>
          </FilterItem>
          <FilterItem>
            <Button type="primary" onClick={query.confirmInput}>查询</Button>
            &nbsp;
            <Button onClick={() => query.resetInput(true)}>重置</Button>
          </FilterItem>
        </FilterForm>
        <Tab animation={false} shape="wrapped" activeKey={filterInput.showType - 1} onChange={changeTab}>
          <Tab.Item title="我负责的" >
            <QueryResult query={query} />
          </Tab.Item>
          <Tab.Item title="可查看的">
            <QueryResult query={query} />
          </Tab.Item>
        </Tab>
      </div>
    </div>
  )
}


function QueryResult ({ query }) {
  return (
    <div>
      <Table dataSource={query.result.list} rowProps={(record) => ({ style: +record.yn === 1 ? { opacity: 0.5 } : {} })}>
        <Table.Column title="装修活动ID" dataIndex="id" />
        <Table.Column title="装修活动名称" dataIndex="activityName" />
        <Table.Column title="素材模板名称" dataIndex="materialName" />
        <Table.Column title="生效时间" dataIndex="effectTime" cell={(v, i, record) => effectTime(record)} />
        <Table.Column title="装修活动状态" dataIndex="stateName" />
        <Table.Column title="关联选品集数" dataIndex="poolIdsNum" />
        <Table.Column title="权重" dataIndex="weight" />
        <Table.Column title="创建人" dataIndex="createName" />
        <Table.Column title="操作" dataIndex="_" cell={(v, i, record) => <Operation record={record} query={query} />} />
      </Table>
      <br />
      <Pagination {...query.pagination} />
    </div>
  )
}

function effectTime (record) {
  return record.effectStartDate + ' - ' + record.effectEndDate
}

function Operation ({ record, query}) {
  const { id, state, yn } = record;
  const [rediectUrl, setRediectUrl] = useState();
  useEffect(() => {
    judgeAccess();
  }, [0])
  function judgeAccess() {
    promotionACL( id )
      .then((result) => {
        let {rediectUrl} = result;
        if (rediectUrl) {
          setRediectUrl(rediectUrl);
        }
      })
  }
  function onDisable () {
    if(!rediectUrl) {
      Dialog.confirm({
        title: `确定要${+state === 3 ? '删除' : '下线'}此活动？`,
        onOk: () => {
          if (+state === 3) {
            // 删除
            updateActivityState({id, state: 3, isDel: true})
              .then(() => {
                query.doQuery()
              })
          } else {
            // 下线
            updateActivityState({id, state: 3})
              .then(() => {
                query.doQuery()
              })
          }
        },
      })
    }else{
      Dialog.confirm({
        title: '申请权限',
        footer:false,
        content:[<ACLAccess rediectUrl={rediectUrl} />],
      })
    }
  }

  return +yn === 1 ? (
    <i>已删除</i>
  ) : (
    <div>
      <Link to={`/decorationActivity/list/detail/${id}`}>
        <Button type="primary" text>查看</Button>
      </Link>
      <i className="sep"></i>
      <Link to={`/decorationActivity/list/edit/-${id}`}>
        <Button type="primary" text>复制</Button>
      </Link>
      <i className="sep"></i>
      <Button type="primary" text onClick={onDisable}>{+state !== 3 ? '下线' : '删除'}</Button>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(ActPage), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ActListPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})



