.container {
  height: 100%;
  padding: 10px 0;
  background-color: #fafafa;
  > .title {
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    h2 {
      margin-bottom: 10px;
      font-size: 20px;
      line-height: 30px;
    }
  }
  .body {
    position: relative;
    padding: 30px 20px;
    background: #FFF;
    min-height: calc(100vh - 100px);
    overflow: hidden;
    border-radius: 5px;
    margin: 20px;
  }
}

i.sep {
  display: inline-block;
  height: 1em;
  width: 1px;
  background: #EBEBEB;
  vertical-align: middle;
  margin: 5px;
}

.grey-text {
  color: #ccc;
}
