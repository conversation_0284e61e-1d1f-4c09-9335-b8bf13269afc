// import { request } from '@/utils/request';
import { decorationReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

export function fetchList(params) {
  return decorationReq.post('/api/promotion/getPromotionActivityList', params).then(onRequestSuccess)
}

export function updateActivityState (params) {
  return decorationReq.post('/api/promotion/updateActivityState', params).then(onRequestSuccess)
}

export function fetchMaterialList (params) {
  return decorationReq.post('/api/material/getMaterialList', params).then(onRequestSuccess)
}

// export function countWhiteList (params) {
//   return decorationReq.post('/api/whitelist/countWhiteList', params).then(onRequestSuccess)
// }

export function countWhiteList(id) {
  return decorationReq.get(`/api/whitelist/countWhiteList?activityId=${id}`)
}

export function offlineWhiteList (params) {
  return decorationReq.post('/api/whitelist/offlineWhiteList', params).then(onRequestSuccess)
}

export function getOperationRecord (params) {
  return decorationReq.post('/api/whitelist/getOperationRecord', params).then(onRequestSuccess)
}

export function downloadFile (params) {
  return decorationReq.post('/api/whitelist/downloadFile', params).then(onRequestSuccess)
}

export function createActivity (params) {
  return decorationReq.post('/api/promotion/createPromotionActivity', params).then(onRequestSuccess)
}

export function editActivity (params) {
  return decorationReq.post('/api/promotion/editPromotionActivity', params).then(onRequestSuccess)
}

export function downloadTemplate (params) {
  return decorationReq.post('/api/whitelist/downloadTemplate', params).then(onRequestSuccess)
}

export function queryPoolByIdOrName (params) {
  return decorationReq.post('/api/promotion/queryPoolByIdOrName', params).then(onRequestSuccess)
}

/**ACL权限认证 */
export function promotionACL(promotionId) {
  return decorationReq.post(`/api/acl/validate/${promotionId}`).then(onRequestSuccess)
}

