.operation-list {
  position: relative;
  margin-bottom:80px;
  padding: 5px 0;
  &.border {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
  .state{
    &>span{
      width:6px;
      height:6px;
      border-radius:50%;
      display:inline-block;
      margin:0 5px 2px 0;
    }
    &.state1{
      &>span{
        background:#FFBB33;
      }
    }
    &.state2{
      &>span{
        background:#00CCAA;
      }
    }
    &.state3,&.state4{
      &>span{
        background:#FF3333;
      }
    }
  }

  //button {
  //  position: absolute;
  //  right: 10px;
  //  top: 10px;
  //}
  .next-row {
    margin: 10px 0;
  }
}
