import React from 'react'
import { Grid, But<PERSON> } from '@alife/next'
import { Link } from 'react-router-dom'
import './materialCard.scss';
const { Row, Col } = Grid

export default function PreviewMaterial ({ disabled, noborder = false, value = {} }) {
  return (
    <div className={`material-card wrap ${noborder ? '' : 'border'}`} style={{ opacity: disabled ? 0.5 : 1 }}>
      <Link to={`/materialCenter/list/detail/${value.id}`} target="_blank" className='btn-detail'>
        <Button text type="primary">查看详情</Button>
      </Link>
      <Row>
        <Col style={{ textAlign: 'right' }} fixedSpan="4">
          素材名称
        </Col>
        <Col fixedSpan="1" />
        <Col>
          {value.materialName}
        </Col>
      </Row>
      <Row>
        <Col style={{ textAlign: 'right' }} fixedSpan="4">
          素材ID
        </Col>
        <Col fixedSpan="1" />
        <Col>
          {value.id}
        </Col>
      </Row>
      <Row>
        <Col style={{ textAlign: 'right' }} fixedSpan="4">
          创建人
        </Col>
        <Col fixedSpan="1" />
        <Col>
          {value.createName}
        </Col>
      </Row>
      <Row>
        <Col style={{ textAlign: 'right' }} fixedSpan="4">
          创建时间
        </Col>
        <Col fixedSpan="1" />
        <Col>
          {(value.createTime || '').replace(/\.0$/, '')}
        </Col>
      </Row>
    </div>
  )
}
