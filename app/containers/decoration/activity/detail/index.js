import {Loading, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Grid, Dialog, Message} from '@alife/next'
import {<PERSON>,withRouter} from 'react-router-dom'
import PreviewMaterial from './materialCard'
import SimpleTable from '@/components/SimpleTable'
import OperationLog from './operationLog'
import React,{useState, useEffect} from 'react'
import {ACLPermission} from '@/containers//PutInPage/request';
import { logTimeComponent, goldLog } from '@/utils/aplus';
import { permissionAccess }  from '@/components/PermissionAccess';
import {ACLAccess} from '@/components/ACLAccess'
import {fetchList, fetchMaterialList, countWhiteList, offlineWhiteList, promotionACL} from '../api'
// import {updateActivityState} from "@/pages/decoration/activity/api";

const {Row, Col} = Grid

function ActDetail({ match,history}) {
  const id = match.params.id;
  const [data, setData] = useState()
  const [material, setMaterial] = useState()
  useEffect(() => {
    fetchList({ id })
      .then((result) => {
        const { list, total } = result.data;
        if (list.length == 0) {
          Dialog.confirm({
            title: '提示',
            footerActions: ['ok'],
            content: '系统数据发生变化，将为你跳转到活动列表刷新最新数据?',
            onOk: () => {
              history.replace('/decorationActivity/list');
            },
          })
        }
        setData(list[0])
        if (list[0]) {
          const materialId = list[0].materialId
          return fetchMaterialList({ id: materialId })
            .then((result) => {
              console.log(result);
              setMaterial(result.data.list[0])
            })
        }
      })
    getWhiteCount(id);
    judgeAccess();
  }, [0])
  let weeks
  if (data) {
    if (data.weeks.length === 7) {
      weeks = '全选'
    } else {
      const dayNames = ['一', '二', '三', '四', '五', '六', '日']
      weeks = '周' + data.weeks.split('')
        .map((n) => dayNames[n - 1])
        .join('、')
    }
  }

  const [dialogVisible, setDialogVisible] = useState(false);
  const [whiteTotal, setWhiteTotal] = useState('');
  const [rediectUrl, setRediectUrl] = useState();
  function getWhiteCount (activityId) {
    return countWhiteList(activityId)
      .then((result) => {
        if (result.data) {
          setWhiteTotal(result.data.data.data);
          // if (+result.data.errorCode) {
          //     throw result
          // }
        }
      })
      .catch((error) => {
        if (error.data) {
          const { errorCode, errorDesc, errorMsg } = error.data
          if (+errorCode === 1) {
            Message.warning(errorDesc || errorMsg)

          } else {
            throw new Error(errorDesc || errorMsg )
          }
        }
      })
  }

  function offlineAllWhite(activityId) {
    if (!rediectUrl) {
      Dialog.confirm({
        title: '确定要全部下线？',
        onOk: () => {
          return offlineWhiteList({activityId})
            .then((result) => {
              if (result.data) {
                Message.success('下线成功');
              }
            })
            .catch((error) => {
              if (error.data) {
                const {errorCode, errorDesc, errorMsg} = error.data
                if (+errorCode === 1) {
                  Message.warning(errorDesc || errorMsg)
                } else {
                  throw new Error(errorDesc || errorMsg)
                }
              }
            })
        },
      })
    } else {
      openDialog();
    }
  }

  function judgeAccess() {
    promotionACL( id )
      .then((result) => {
        let {rediectUrl} = result;
        if (rediectUrl) {
          setRediectUrl(rediectUrl);
        }
      })
  }

  function openDialog() {
    Dialog.confirm({
      title:'申请权限' ,
      footer:false,
      content:[<ACLAccess rediectUrl={rediectUrl} />],
    })
  }

  function editInfo(url) {
    if(!rediectUrl) {
      history.replace(url);
    }else{
      openDialog();
    }
  }

  function openOperationList() {
    if(!rediectUrl) {
      setDialogVisible(true);
    }else{
      openDialog();
    }
  }
  return (
    <div className="container">
      <div className="title">
        <Breadcrumb>
          <Breadcrumb.Item>
            <Link to="/decorationActivity/list">装修活动</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            活动详情
          </Breadcrumb.Item>
        </Breadcrumb>
      </div>
      {data ? (
        <div className="body">
          <h2 style={{ marginTop: 0 }}>
            活动名称
          </h2>
          <Row>
            <Col offset="3" span="18">
              <SimpleTable
                title="基础信息"
                control={
                  +data.state === 3 ? (
                    <Button text type="primary" onClick={()=>editInfo(`../edit/${id}?step=0&from=detail`)}>修改</Button>
                  ) : null
                }
              >
                <SimpleTable.Item label="装修状态">
                  {data.stateName}
                </SimpleTable.Item>
                <SimpleTable.Item label="装修活动ID">
                  {data.id}
                </SimpleTable.Item>
                <SimpleTable.Item label="装修活动名称">
                  {data.activityName}
                </SimpleTable.Item>
                <SimpleTable.Item label="活动时间">
                  {data.effectStartDate}
                  &nbsp; - &nbsp;
                  {data.effectEndDate}
                  ，
                  {weeks}
                  ，
                  {data.effectStartTime}
                  &nbsp; - &nbsp;
                  {data.effectEndTime}
                </SimpleTable.Item>
                <SimpleTable.Item label="创建人">
                  {data.createName}
                </SimpleTable.Item>
                <SimpleTable.Item label="大促项目">
                  {data.promotionTypeName}
                </SimpleTable.Item>
              </SimpleTable>
              <SimpleTable
                title="基础元素"
                control={
                  +data.state === 3 ? (
                    <Button text type="primary"  onClick={()=>editInfo(`../edit/${id}?step=1&from=detail`)}>修改</Button>
                  ) : null
                }
              >
                <SimpleTable.Item label="绑定素材">
                  {material ? (
                    <PreviewMaterial value={material} noborder />
                  ) : null}
                </SimpleTable.Item>
              </SimpleTable>
              <SimpleTable
                title="招商对象"
                control={
                  +data.state === 3 ? (
                    <Button text type="primary"  onClick={()=>editInfo(`../edit/${id}?step=2&from=detail`)}>修改</Button>
                  ) : null
                }
              >
                <SimpleTable.Item label="关联选品集ID">
                  {data.poolIds ? data.poolIds.split(',').join(', ') : '-'}
                </SimpleTable.Item>
              </SimpleTable>
              <SimpleTable
                title="白名单门店"
                control={
                  (
                    [<Button text type="primary" onClick={() => openOperationList()}>操作记录</Button>,
                      <i className="sep"></i>,
                      <Button text type="primary" onClick={() => offlineAllWhite(id)}>全部下线</Button>,
                      <i className="sep"></i>,
                      <Button text type="primary" onClick={() => editInfo(`../edit/${id}?step=3&from=detail`)}>添加</Button>
                    ]
                  )
                }
              >
                <SimpleTable.Item label="白名单门店ID">
                  <span className={`${whiteTotal > 0 ? '' : 'grey-text'}`}>{whiteTotal > 0 ? `已添加 ${whiteTotal} 个门店` : '未添加门店ID'}</span>
                </SimpleTable.Item>
                {dialogVisible && <Dialog
                  title="操作记录"
                  className='operation'
                  footer={false}
                  // hasMask={false}
                  isFullScreen={true}
                  visible={dialogVisible}
                  onClose={()=>setDialogVisible(false)}
                >
                  <OperationLog activityId={id}/>
                </Dialog>}
              </SimpleTable>
            </Col>
          </Row>
        </div>
      ) : (
        <Loading inline={false}>
          <div className="body">
          </div>
        </Loading>
      )}
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(ActDetail), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ActDetailPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})


