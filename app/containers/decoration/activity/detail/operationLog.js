import {Table, Pagination, Message, Dialog} from '@alife/next'
import React,{useState, useEffect} from 'react'
import {with<PERSON>outer} from 'react-router-dom'
import {formatTimeStamp} from '@/utils/time';
import {getOperationRecord,downloadFile, offlineWhiteList} from '../api'
import './operationLog.scss'

function OperationLog({match}) {
  let activityId  = match.params.id;
  const [data, setData] = useState();
  const [total, setTotal] = useState(0);
  const defaultPagination = {
    pageNo: 1,
    pageSize: 10,
  }
  const [pageNo, setPageNo] = useState(defaultPagination.pageNo);
  const [pageSize, setPageSize] = useState(defaultPagination.pageSize);

  useEffect(() => {
    fetchList({pageNo, pageSize});
  }, [0])
  const onlineStateMap = {
    0: '待生效',
    1: '已生效',
    2: '下线中',
    3: '已下线',
    4: '未生效'
  }

  const stateMap = {
    1: '审核中',
    2: '验证通过',
    3: '部分验证通过',
    4: '验证未通过'
  }

  function fetchList(pagination) {
    getOperationRecord({
      ...pagination,
      activityId
    })
      .then((result) => {
        let {operationRecordList, total} = result.data;
        setData(operationRecordList ? operationRecordList : []);
        setTotal(total);
      })
  }

  function pageChange(v) {
    setPageNo(v);
    fetchList({
      pageNo: v,
      pageSize
    });
  }

  function pageSizeChange(v) {
    setPageSize(v);
    setPageNo(1);
    fetchList({
      pageNo: 1,
      pageSize: v
    });
  }

  function getFile(activityId, operationId) {
    return downloadFile({activityId, operationId})
      .then((result) => {
        if (result.data) {
          // if (+result.data.errorCode) {
          const downloadUrl = result.data;
          let downloadEle = document.createElement('a');
          downloadEle.href = downloadUrl;
          downloadEle.click();
          // }
        }

      })
      .catch((error) => {
        if (error.data) {
          const {errorCode, errorDesc, errorMsg} = error.data
          if (+errorCode === 1) {
            Message.warning(errorDesc || errorMsg)

          } else {
            throw new Error(errorDesc || errorMsg)
          }
        }
      })

  }

  function offlineWhite(activityId, operationId) {
    Dialog.confirm({
      title: `确定下线？`,
      onOk: () => {
        return offlineWhiteList({activityId, operationId})
          .then((result) => {
            if (result.data) {
              Message.success('下线成功');
              fetchList({
                pageNo,
                pageSize
              });
            }
          })
          .catch((error) => {
            if (error.data) {
              const {errorCode, errorDesc, errorMsg} = error.data
              if (+errorCode === 1) {
                Message.warning(errorDesc || errorMsg)
              } else {
                throw new Error(errorDesc || errorMsg)
              }
            }
          })
      },
    })
  }


  const renderFuc = (v, i, r) => {
    return <div>
      {(r.state != 1) && <a href='javascript:;' onClick={() => getFile(activityId, r.id)}>下载</a>}
      {(r.onlineState == 1 || r.onlineState == 2) && r.state != 1 && [<i className="sep"></i>,
        <a href='javascript:;' onClick={() => offlineWhite(activityId, r.id)}>下线</a>]}
    </div>
  };
  return (
    <div className="operation-list">
      <Table dataSource={data}>
        <Table.Column title="序号" width={70} cell={(v, i) => `${1 + i + pageSize * (pageNo - 1)}`}/>
        <Table.Column title="操作ID" dataIndex="id" width={90}/>
        <Table.Column title="操作时间" dataIndex="createTime" width={170}  cell={(value) => formatTimeStamp(value)} />
        <Table.Column title="操作人" dataIndex="createUser" width={100}/>
        <Table.Column title="验证状态" dataIndex="state" width={120} cell={(v, i) => <p className={`state state${v}`}><span/>{stateMap[v]}</p>}/>
        <Table.Column title="生效状态" dataIndex="onlineState" width={90} cell={(v, i) => onlineStateMap[v]}/>
        <Table.Column title="操作" dataIndex="_" cell={renderFuc}/>
      </Table>
      <br/>
      <Pagination
        current={pageNo} total={total} pageSize={pageSize} popupProps={{align: 'bl tl'}}
        totalRender={total => `共${total}个`}
        onChange={pageChange} onPageSizeChange={pageSizeChange} pageSizeSelector="dropdown" pageSizePosition="end"/>
    </div>
  )
}

export default withRouter(OperationLog);
