/**
 * PutInActEffectPage
 * 投放活动效果
 */

import React,{Fragment} from 'react';
import { PageBase } from '@/containers/base';
import { PageWrapper } from '@/components/PageWrapper';
import { Breadcrumb, Form, Input, Grid, Button, Select } from '@alife/next';
import { Link } from 'react-router-dom';
import moment from 'moment';
import { manageList, setTableCol, filterForm } from '@/components/Table';
import './index.scss';
import { logTimeComponent, goldLog } from '@/utils/aplus';
import { permissionAccess }  from '@/components/PermissionAccess';
import {getPutInActEffectList} from "../request";
import { DialogBtn } from '@/components/Button';
import { LineChart } from '@/components/TrendChart';
import { isAllNumber } from '@/utils/validators';
import { doesFormHasErrors } from '@/utils/formTool';
import { TimeRangePicker } from '@/components/TimeRangePicker';
import ViewDetail from './viewDetail.js'
import {ACLPermission} from "../../PutInPage/request";
import { formatTimeStamp, FORMAT } from '@/utils/time';
import { poolTypeEnum,putInActChartEum,moduleNameEum } from '../common';

const Option = Select.Option;
const FormItem = Form.Item;
const { Row, Col } = Grid;

const OptionCol = ({ record, reload }) => {
  return (
    <Fragment>
      <ViewDetail activityId={record.putinActivityId}>查看</ViewDetail>
    </Fragment>
  )
}

const defaultColType = { title: '' }

const columns = [
  { title: '日期', dataIndex: 'dataDt', width:110, cell: (value) => formatTimeStamp(value, FORMAT.DATE) },
  { title: '页面ID', dataIndex: 'pageId', width:90 },
  { title: '页面名称', dataIndex: 'pageName', width:100},
  {
    title: '组件名称', dataIndex: 'moduleName', width: 140, cell: function (value) {
      let item = moduleNameEum.filter((v) => v.value == value);
      return <span>{item.length > 0 ? item[0].name : ''}</span>
    }
  },
  { title: '活动ID', dataIndex: 'putinActivityId', width:100},
  { title: '活动创建人', dataIndex: 'activityCreateUser', width:110},
  { title: '选品集ID', dataIndex: 'poolIds', width:110},
  { title: '曝光PV', dataIndex: 'exposurePv', width:100 },
  { title: '曝光UV', dataIndex: 'exposureUv', width:100 },
  { title: '选品集类型', dataIndex: 'poolType', width:110, cell: (value) => poolTypeEnum[value]},
  { title: '活动点击uv', dataIndex: 'clickUv', width:110},
  { title: '新零售订单量', dataIndex: 'orderCnt', width:100},
  { title: '新零售净gmv', dataIndex: 'pureGmv', width:100},
  { title: '转化率', dataIndex: 'transRate', width:100},
  { title: 'ctr', dataIndex: 'ctr', width:100},
  { title:'数据详情', lock: 'right' ,  width:100, cell:function(value, rowIndex, record){
    return <OptionCol record={record} reload={this.reload}/>
  }}
]

function getCol(type) {
  return { ...defaultColType, ...type }
}

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: {
    span: 17
  },
  style: {
    width: '100%'
  },
  labelAlign: 'left',
  labelTextAlign: 'right'
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

/**检测活动id格式 */
const checkActivityId = function(rule, value, callback){
  const errors = isAllNumber(value);
  if(errors.length && value){
    callback('活动ID格式错误')
  } else {
    callback();
  }
}

const getActivityId = function () {
  let url = location.href;
  let newAtt = url.split('/');
  let result = newAtt[newAtt.length-1];
  if(!isAllNumber(result).length){
    return result;
  }else{
    return '';
  }
}

/**检测时间范围格式 */
const checkEffectRange = function (rule, value, cb) {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    // if(start.isBefore(moment().startOf('day'))) return cb('开始时间不能小于当前时间')
    if (end.clone().subtract(1, 'month').isAfter(start.clone())) return cb('时间段不能超过1个月')
  }
  cb()
}

// const fetchData = function(data){
//   //todo 调接口
//   console.log(data);
// }

function PutInForm({ field, searchData, reload }) {
  const onSearch = () => {
    field.validate((e) => {
      if(!doesFormHasErrors(e)){
        searchData();
        //fetchData();
      }
    })
  }

  const onReload = () =>{
    location.href = '#/putInActivityEffect/list';
    reload();
  }

  return (
    <Form {...formLayout} field={field}>
      <Row>
        <Col>
          <FormItem label="页面ID" {...formItemLayout} validator={checkActivityId}>
            <Input placeholder="请输入页面ID" name="pageId" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="页面名称" {...formItemLayout}>
            <Input placeholder="请输入页面名称" name="pageName" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="活动创建人" {...formItemLayout}>
            <Input placeholder="请输入活动创建人" name="activityCreateUser" />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem label="组件名称" {...formItemLayout}>
            {/*<Input placeholder="请输入组件名称" name="moduleName" />*/}
            <Select name="moduleName" placeholder="请选择组件名称" style={{width:'240px'}}>
              {
                moduleNameEum.map((v) => {
                  return <Select.Option key={v.value} value={v.value}>{v.name}</Select.Option>
                })
              }
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem label="选品集ID" {...formItemLayout}>
            <Input placeholder="请输入选品集ID" name="poolIds" />
          </FormItem>
        </Col>
        <Col>
          <FormItem label="活动ID" {...formItemLayout}>
            <Input placeholder="请输入活动ID" name="putinActivityId" defaultValue={getActivityId()}/>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={8}>
          <FormItem label="时间范围" {...formItemLayout} required requiredMessage='时间范围不能为空'  validator={checkEffectRange}>
            <TimeRangePicker style={{width:'125%'}} name="effectRange"  defaultValue={[moment().add(-31, 'days'), moment().add(-1, 'days')]}></TimeRangePicker>
          </FormItem>
        </Col>
        <Col span={8}>
          <div style={{marginLeft:"96px"}}>
            <Button type="primary" onClick={onSearch}>查询</Button>&nbsp;&nbsp;
            <Button type="normal" onClick={onReload}>重置</Button>
          </div>
        </Col>
      </Row>
    </Form>
  )
}

const PutInFilterForm = filterForm(PutInForm, ['pageId', 'pageName', 'putinActivityId','moduleName','poolIds','activityCreateUser','effectRange'])
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(PutInTable, PutInFilterForm,
  async (search) => {
    let { query } = search;
    let {pageId, pageName, putinActivityId, moduleName, poolIds, activityCreateUser, effectRange} = query;
    query = {
      pageId,
      pageName,
      putinActivityId: typeof(putinActivityId) == "undefined" ? getActivityId() : putinActivityId,
      moduleName,
      poolIds,
      activityCreateUser,
      startTime: effectRange ? formatTimeStamp(effectRange[0], FORMAT.DATE) : moment().add(-31, 'days').format('YYYY-MM-DD'),
      endTime: effectRange ? formatTimeStamp(effectRange[1], FORMAT.DATE) : moment().add(-1, 'days').format('YYYY-MM-DD'),
    }
    const listData = await getPutInActEffectList({...search, query});
    return listData
  },{size:40})

class PutInActEffectPageBase extends PageBase {
  constructor(props) {
    super(props);
    this.state = {
      listData:[],
      selectType:'exposurePv',
      selectActId:this.params.activityId || ''
    }
  }

  componentDidMount(){
    this.genData();
  }

  onChange = (value) =>{
    this.setState({
      selectType:value
    },()=>{
      this.genData()
    })
  }

  handleSource = (data) =>{
    let putinActivityId = '';
    if(this.params.activityId){
      putinActivityId = this.params.activityId;
    }else if(data.query.putinActivityId){
      putinActivityId = data.query.putinActivityId;
    }
    // let putinActivityId = data.query.putinActivityId;
    this.setState({
      // selectActId: typeof(putinActivityId) == "undefined" ? this.state.selectActId : putinActivityId,
      selectActId:putinActivityId,
      listData: data.rows
    })
  }

  genData = () =>{
    let {selectType,selectActId,listData} = this.state;
    let legendData = ['活动ID为' + selectActId];
    let xAxisData = [];
    let itemData = [];
    if(selectActId ==''){
      return '';
    }
    if(selectActId!='' && listData.length > 0) {
      let newListData = listData.sort((a, b) => { return a.dataDt - b.dataDt });
      newListData.map((v) => {
        xAxisData.push(formatTimeStamp(v.dataDt, FORMAT.DATE));
        itemData.push(v[selectType]);
      });
      return {
        legendData,
        xAxisData,
        seriesData: [{
          name: legendData[0],
          data: itemData
        }]
      }
    }
  }

  render() {
    let {selectType,selectActId} = this.state;
    return (
      <PageBase.Container className="put-in-act-effect">
        {/*<div className="nav-wrapper">*/}
          {/*<Breadcrumb>*/}
            {/*<Breadcrumb.Item>活动效果</Breadcrumb.Item>*/}
            {/*<Breadcrumb.Item><Link to={`/putIn/list`}>投放活动效果</Link></Breadcrumb.Item>*/}
          {/*</Breadcrumb>*/}
        {/*</div>*/}
        {/*<PageWrapper>*/}
          {/*<ManageList handleSource={this.handleSource}>*/}
            {/*<div className="chart-wrapper">*/}
              {/*<LineChart height={'350px'} showLegend={false} chartData={this.genData()} title='趋势图'>*/}
                {/*<Select className='select-panel' onChange={this.onChange} value={selectType} disabled={selectActId==''?'disabled':''}>*/}
                  {/*{putInActChartEum.map((v) => {*/}
                    {/*return <Option value={v.value} key={v.value}>{v.name}</Option>*/}
                  {/*})}*/}
                {/*</Select>*/}
              {/*</LineChart>*/}
            {/*</div>*/}
          {/*</ManageList>*/}
        {/*</PageWrapper>*/}
        <h5 className="without_permission">该页面已经废弃，如要查看会场效果，请移步【数据洞察】中的 <a style={{textDecoration:'underline'}} target="_blank" href="https://kunlun.alibaba-inc.com/dataAnalysis#/actEffect/list">活动效果</a></h5>
      </PageBase.Container>
    )
  }
}

export const LogTimePutInPage = logTimeComponent(PutInActEffectPageBase, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const PutInActEffectPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})


