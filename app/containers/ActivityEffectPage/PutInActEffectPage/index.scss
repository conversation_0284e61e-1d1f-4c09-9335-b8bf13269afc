.put-in-act-effect{
  padding: 20px 0;
  .next-breadcrumb{
    .next-breadcrumb-item .next-breadcrumb-text,.next-breadcrumb-text > a{
      font-size:14px;
    }
  }
  .detail-title {
    background-color: #fff;
  }
  .nav-wrapper{
    margin-left:20px;
  }
  .c-page-wrapper{
    padding:0 20px;
    h2{
      display:none;
    }
    .c-page-wrapper__content{
      background-color:transparent;
      padding:0;
    }
    .filter-list{
      background-color:transparent;
      .pagination{
        .next-pagination-size-selector{
          display:none;
        }
      }
      form{
        padding:30px 30px 30px 0;
        background-color:#fff;
      }
      .chart-wrapper{
        margin-top:20px;
        background-color:#fff;
        padding:30px 20px;
      }
      &>.next-table{
        margin-top:20px;
        padding:20px;
        background-color:#fff;
        td{
          .next-table-cell-wrapper{
            word-break:break-all;
          }
        }
        .next-table-lock-right{
          margin-right:20px;
        }
      }
      .pagination{
        background-color: #fff;
        padding:10px 0 30px 0;
        margin-top:0 !important;
        margin-right:20px;
      }
    }
  }
  //.next-table-lock-right.shadow{
  //  margin-top:0 !important;
  //}
}
.act-effect-detail-dialog {
  .next-dialog-header {
    font-size: 20px;
    color: #333333;
  }
  .next-dialog-body {
    padding-top: 0;
    .detail-section {
      width: 100% !important;
      min-width: auto !important;
    }
  }
}

