import React from 'react';
import { Breadcrumb } from '@alife/next';
import { PageWrapper } from '@/components/PageWrapper';
import { PageBase } from '@/containers/base';
import { DetailList } from '@/containers/PutInPage/DetailList';
import { defaultDetail } from '@/containers/PutInPage/constants'
import { getPutInDetail } from '@/containers/PutInPage/request'
import { onRequestError } from '@/utils/api'
import { track } from '@/utils/aplus';
import { Link } from 'react-router-dom';
import './index.scss';

export default class PutInActEffectDetailPage extends PageBase {
  constructor(props) {
    super(props);

    this.state = {
      detail: { ...defaultDetail }
    }
  }

  async componentDidMount() {
    //打点
    track('setSpm', [12964533]);

    const { activityId } = this.props;
    try {
      const detail = await getPutInDetail(activityId);
      this.setState({ detail })
    } catch (error) {
      onRequestError(error)
    }
  }

  render() {
    const { detail } = this.state;

    return (
        <DetailList detail={detail} />
    )
  }
}
