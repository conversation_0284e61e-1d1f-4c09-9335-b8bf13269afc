.act-rank-effect{
  padding: 20px 0;
  .next-breadcrumb{
    .next-breadcrumb-item .next-breadcrumb-text,.next-breadcrumb-text > a{
      font-size:14px;
    }
  }
  .c-page-wrapper{
    padding:0 20px;
    h2{
      display:none;
    }
    .c-page-wrapper__content{
      background-color:transparent;
      padding:0;
      form{
        padding:30px 30px 30px 0;
        background-color:#fff;
      }
      .statistics-list-wrapper{
        margin-top:20px;
        background-color:#fff;
        padding:20px 0;
      }
      .chart-wrapper{
        margin-top:20px;
        background-color:#fff;
        padding:30px 20px;
      }
      &>.next-row{
        margin-top:20px;
        .next-col-12{
          background-color:#fff;
          padding:20px;
          width:49%;
          max-width:49%;
          margin-right:2%;
          &:last-child{
            margin-right:0;
          }
        }
        .trend-chart{
          .next-tabs-capsule{
            z-index:10;
            position: relative;
            .next-tabs-bar{
              position:absolute;
              right:0;
              width:140px;
              .next-tabs-tab-inner{
                padding:2px 13px;
              }
              .next-tabs-tab{
                &.active{
                  color: #FF7C4D;
                  background-color:transparent;
                }
              }
            }
          }
        }
        margin-bottom:10px;
      }
    }
  }
  .detail-title {
    background-color: #fff;
  }
  .nav-wrapper{
    margin-left:20px;
  }
}

