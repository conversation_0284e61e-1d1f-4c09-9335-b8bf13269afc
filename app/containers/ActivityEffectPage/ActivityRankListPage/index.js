/**
 * ActivityRankListPage
 * 活动榜单
 */

import React from 'react';
import {PageBase} from '@/containers/base';
import {PageWrapper} from '@/components/PageWrapper';
import {Breadcrumb, Form, Input, Grid, Button, Select, Tab} from '@alife/next';
import {Link} from 'react-router-dom';
import moment from 'moment';
import {filterForm} from '@/components/Table';
import './index.scss';
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {LineChart, PieChart, DataChart} from '@/components/TrendChart';
import {StatisticsList} from '@/components/StatisticsList';
import {onRequestError} from '@/utils/api'
import {isAllNumber} from '@/utils/validators';
import {formatTimeStamp, FORMAT} from '@/utils/time';
import {doesFormHasErrors} from '@/utils/formTool';
import {TimeRangePicker} from '@/components/TimeRangePicker';
import {ACLPermission} from "../../PutInPage/request";
import {getActRankList, getRatio, getRank} from "../request";
import {noop,uniq} from 'lodash';
import { actRankChartEum,tabsEum } from '../common';

const Option = Select.Option;
const FormItem = Form.Item;
const {Row, Col} = Grid;

const baseFormItemLayout = {
  style: {
    width: '100%'
  },
  labelAlign: 'left',
  labelTextAlign: 'right'
}

const formItemLayout = {
  labelCol: {span: 5},
  wrapperCol: {
    span: 19
  }
};

const formItemLayoutTwo = {
  labelCol: {span: 9},
  wrapperCol: {
    span: 25
  }
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

/**检测时间范围格式 */
const checkEffectRange = function (rule, value, cb) {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    if (end.clone().subtract(1, 'month').isAfter(start.clone())) return cb('时间段不能超过1个月')
  }
  cb()
}

/**检测活动id格式 */
const checkActivityId = function (rule, value, callback) {
  const errors = isAllNumber(value);
  if (errors.length && value) {
    callback('活动ID格式错误')
  } else {
    callback();
  }
}

function PutInForm({field, searchData, reload}) {
  const onSearch = () => {
    field.validate((e) => {
      if (!doesFormHasErrors(e)) {
        searchData();
      }
    })
  }

  return (
    <Form {...formLayout} field={field}>
      <Row>
        <Col span={9}>
          <FormItem label="时间范围" {...baseFormItemLayout} {...formItemLayout}  validator={checkEffectRange}>
            <TimeRangePicker style={{width: '100%'}} name="effectRange" defaultValue={[moment().add(-31, 'days'), moment().add(-1, 'days')]}></TimeRangePicker>
          </FormItem>
        </Col>
        <Col>
          <FormItem label="投放活动ID" {...baseFormItemLayout} {...formItemLayoutTwo} required requiredMessage='投放活动ID不能为空' validator={checkActivityId}>
            <Input placeholder="请输入投放活动ID" name="mainActivityId"/>
          </FormItem>
        </Col>
        <Col>
          <FormItem label="对比活动ID" {...baseFormItemLayout} {...formItemLayoutTwo} validator={checkActivityId}>
            <Input placeholder="请输入对比活动ID" name="compareActivityId"/>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <Button type="primary" style={{marginLeft: '80px'}} onClick={onSearch}>查询</Button>&nbsp;&nbsp;
          <Button type="normal" onClick={reload}>重置</Button>
        </Col>
      </Row>
    </Form>
  )
}

const PutInFilterForm = filterForm(PutInForm, ['effectRange', 'mainActivityId', 'compareActivityId'])

export class ActivityRankListPageBase extends PageBase {
  constructor(props) {
    super(props);
    this.state = {
      selectType: 'exposurePv',
      query: {},
      rankType: 1, //
      ratioType: 1,
      ratioChartData: '',
      rankChartData: '',
      mainChartData: '',
      staticList: []
    }
  }

  updateState(cb = noop, after = noop) {
    const state = cb(this.state)
    this.setState({...state}, () => {
      after()
    })
  }

  componentDidMount() {

  }

  async fetchRatioData() {
    let {query, ratioType} = this.state;
    let {mainActivityId, startTime, endTime} = query;
    if (mainActivityId) {
      try {
        let resp = await getRatio({
          mainActivityId,
          type: ratioType,
          startTime,
          endTime
        })
        this.updateState(() => ({rotioList: resp.rotioList}), () => {
          this.genRatioData();
        });
      } catch (error) {
        onRequestError(error)
      }
    }
  }

  async fetchRankData() {
    let {query, rankType} = this.state;
    let {mainActivityId, startTime, endTime} = query;
    try {
      let resp = await getRank({
        mainActivityId,
        type: rankType,
        startTime,
        endTime,
      })
      this.updateState(() => ({rankList: resp.sortList}), () => {
        this.genRankData();
      })
    } catch (error) {
      onRequestError(error)
    }
  }

  async fetchRankListData() {
    let {query} = this.state;
    let {mainActivityId, compareActivityId, startTime, endTime} = query;
    try {
      let resp = await getActRankList({
        mainActivityId,
        compareActivityId,
        startTime,
        endTime,
      })
      this.updateState(() => ({mainList: resp.mainList, otherList: resp.otherList, cumulativeData: resp}), () => {
        this.genData();
        this.genCumulativeData();
      })
    } catch (error) {
      onRequestError(error)
    }
  }

  changeRankType = (value) => {
    this.updateState(() => ({rankType: value}), () => {
      this.fetchRankData();
    })
  }

  changeRatioType = (value) => {
    this.updateState(() => ({ratioType: value}), () => {
      this.fetchRatioData();
    })
  }

  genData = () => {
    let {mainList, otherList} = this.state;
    let {query} = this.state;
    let {mainActivityId} = query;
    if(mainList.length==0){
      this.updateState(() => ({
        mainChartData:''
      }))
    }
    if (mainActivityId && mainList && mainList.length > 0) {
      let {selectType} = this.state;
      let legendData = ['主活动', '对比活动'];
      let xAxisData = mainList.map(list => list.dataDt);
      let itemData = [];
      let otherXAxisData = [];
      if (otherList && otherList.length > 0) {
        otherXAxisData = otherList.map(list => list.dataDt);
        xAxisData = xAxisData.concat(otherXAxisData);
      }
      xAxisData = uniq(xAxisData.sort((a, b) => { return a - b }));
      let otherItemData = [];
      let seriesData = [];
      xAxisData.map((x)=>{
        let hasDate = mainList.filter((v)=>v.dataDt==x);
        if(hasDate.length<=0){
          itemData.push(0);
        }else{
          itemData.push(hasDate[0][selectType]);
        }
      })

      seriesData.push({
        name: legendData[0],
        data: itemData
      })
      if (otherList && otherList.length > 0) {
        xAxisData.map((x) => {
          let hasDate = otherList.filter((v)=>v.dataDt==x);
          if(hasDate.length<=0){
            otherItemData.push(0);
          }else{
            otherItemData.push(hasDate[0][selectType]);
          }
        })
        seriesData.push({
          name: legendData[1],
          data: otherItemData
        })
      }else{
        seriesData.push({
          name: legendData[1],
          data: []
        })
      }
      this.updateState(() => ({
        mainChartData: {
          legendData,
          xAxisData,
          seriesData
        }
      }))
    }
  }

  genRankData = () => {
    let {rankList} = this.state;
    let {query} = this.state;
    let {mainActivityId} = query;
    if (mainActivityId && rankList && rankList.length > 0) {
      this.updateState(() => ({
        rankChartData: {
          yAxisData: rankList.map(list => list.name),
          seriesData: rankList.map(list => {
            return {value:list.count,listData:list}
          }),
          formatter:function (params) {
            console.log(params[0].data);
            let {listData} = params[0].data;
            return `${listData.name}<br/>gmv：${listData.count}<br/>转化率：${listData.transRate}%`;
          }
        }
      }));
    }
  }

  genRatioData = () => {
    let {rotioList} = this.state;
    let {query} = this.state;
    let {mainActivityId} = query;
    if (mainActivityId && rotioList && rotioList.length > 0) {
      this.updateState(() => ({
        ratioChartData: {
          legendData: rotioList.map(list => list.name),
          seriesData: JSON.parse(JSON.stringify(rotioList).replace(/count/g, 'value'))
        }
      }));
    }
  }

  formatNumber = (d, key) => {
    if (!d) return '';
    const formated = Number(d[key]).toFixed(2);
    return `${formated}%`;
  }

  genCumulativeData = () => {
    let {cumulativeData} = this.state;
    let {mainDeliveryCumulative: v, deliveryCCRep: d} = cumulativeData;
    if (v || d) {
      this.updateState(() => ({
        staticList: [
          {title: '曝光数据', value: v.exposurePvTotal, contrast: this.formatNumber(d, 'exposurePvRatio')},
          {title: '日均uv', value: v.exposureUvAverage, contrast: this.formatNumber(d, 'exposureUvRatio')},
          {title: '日均点击uv', value: v.clickUvAverage, contrast: this.formatNumber(d, 'clickUvRatio')},
          {title: '订单量', value: v.orderCntTotal, contrast: this.formatNumber(d, 'orderCntRatio')},
          {title: '净gmv', value: v.pureGmvTotal, contrast: this.formatNumber(d, 'pureGmvTatio')},
          {title: '日均转化率', value: v.transRateAverage, contrast: this.formatNumber(d, 'transRateRation')},
          {title: '日均ctr', value: v.ctrAverage, contrast: this.formatNumber(d, 'ctrRation')}
        ]
      }))
    }else{
      this.updateState(() => ({
        staticList: []
      }))
    }
  }

  searchData = (filter) => {
    let {mainActivityId, compareActivityId, effectRange} = filter;
    this.setState({
      query: {
        mainActivityId,
        compareActivityId,
        startTime: effectRange ? formatTimeStamp(effectRange[0], FORMAT.DATE) : moment().add(-31, 'days').format('YYYY-MM-DD'),
        endTime: effectRange ? formatTimeStamp(effectRange[1], FORMAT.DATE) : moment().add(-1, 'days').format('YYYY-MM-DD'),
      }
    }, () => {
      this.fetchRankListData();
      this.fetchRatioData();
      this.fetchRankData();
    });
  }

  reload = () => {
    this.setState({
      query: {},
      ratioChartData: '',
      rankChartData: '',
      mainChartData: '',
      staticList: []
    }, () => {
    });
  }

  onChange = (value) =>{
    this.setState({
      selectType:value
    },()=>{
      this.genData()
    })
  }

  render() {
    let {staticList,mainChartData,selectType,rankChartData,ratioChartData} = this.state;
    return (
      <PageBase.Container className="act-rank-effect">
        <div className="nav-wrapper">
          <Breadcrumb>
            <Breadcrumb.Item>活动效果</Breadcrumb.Item>
            <Breadcrumb.Item><Link to={`/putIn/list`}>活动榜单</Link></Breadcrumb.Item>
          </Breadcrumb>
        </div>
        <PageWrapper>
          <PutInFilterForm searchData={this.searchData} reload={this.reload}/>
          {staticList.length > 0 && <StatisticsList data={staticList}/>}
          <div className="chart-wrapper">
            <LineChart height={'400px'} chartData={mainChartData} title='趋势图'>
              <Select className='select-panel' onChange={this.onChange} value={selectType}>
                {actRankChartEum.map((v) => {
                  return <Option value={v.value}>{v.name}</Option>
                })}
              </Select>
            </LineChart>
          </div>
          <Row>
            <Col span={'12'}>
              <DataChart height={'400px'} title='排名' chartData={rankChartData} noDataContent={'选择唯一投放活动，才可展示排名柱状图'}>
                <TabBtn tabs={tabsEum} shape={'capsule'} onChange={this.changeRankType} size='small'/>
              </DataChart>
            </Col>
            <Col span={'12'}>
              <PieChart height={'400px'} title='类型占比' chartData={ratioChartData} noDataContent={'选择唯一投放活动，才可展示类型占比饼图'}>
                <TabBtn tabs={tabsEum} shape={'capsule'} onChange={this.changeRatioType} size='small'/>
              </PieChart>
            </Col>
          </Row>
        </PageWrapper>
      </PageBase.Container>
    )
  }
}

export class TabBtn extends React.Component {
  render() {
    return (
      <Tab {...this.props} onChange={this.props.onChange}>
        {this.props.tabs.map(tab => <Tab.Item title={tab.name} key={tab.value}/>)}
      </Tab>
    )
  }
}

export const LogTimePutInPage = logTimeComponent(ActivityRankListPageBase, (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ActivityRankListPage = permissionAccess(LogTimePutInPage, async (activityId) => {
  return await ACLPermission(activityId);
})

