import React from 'react';
import moment from 'moment';
import { formatTimeStamp, FORMAT } from '@/utils/time';

export const poolTypeEnum = {
  '1':'选品',
  '2':'选店'
}

export function dateRender(value, _index, _record){
  if (!value) return value
  else return formatTimeStamp(moment(value))
}

/*投放活动效果枚举*/
export const putInActChartEum = [
  {name:'曝光pv',value:'exposurePv'},
  {name:'曝光uv',value:'exposureUv'},
  {name:'点击uv',value:'clickUv'},
  {name:'订单量',value:'orderCnt'},
  {name:'净gmv',value:'pureGmv'},
  {name:'转化率',value:'transRate'},
  {name:'ctr',value:'ctr'},
]

/*活动榜单枚举*/
export const actRankChartEum = [
  {name:'曝光pv',value:'exposurePv'},
  {name:'曝光uv',value:'exposureUv'},
  {name:'活动点击uv',value:'clickUv'},
  {name:'订单量',value:'orderCnt'},
  {name:'净gmv',value:'pureGmv'},
]

export const tabsEum = [
  {name: '按净gmv', value: '1'},
  {name: '订单数', value: '2'},
];

export const moduleNameEum = [
  {value: "op-cookbook", name: "菜谱组件"},
  {value: "op-jrdp-brand", name: "今日大牌-品牌专用"},
  {value: "op-custom-goodslist-recommendation", name: "千人千榜-猜你喜欢"},
  {value: "op-custom-goodslist-more", name: "千人千榜-更多榜单"},
  {value: "op-custom-goodslist-mainlist", name: "千人千榜-主榜单"},
  {value: "op-brand-section", name: "品牌专区-平铺"},
  {value: "op-custom-goodslist-enter", name: "千人千榜入口"},
  {value: "op-hot-product", name: "超级爆品"},
  {value: "op-single-coupon-wall", name: "品牌单品券墙"},
  {value: "op-jrdp", name: "今日大牌"},
  {value: "op-goods-1x3", name: "商品1*3"},
  {value: "op-goods-1x2", name: "商品1*2"},
  {value: "op-limited-purchase-entry", name: "限量抢购-入口"},
  {value: "op-limited-purchase-list", name: "限量抢购"},
  {value: "op-flashsale-innershoplist", name: "店铺列表-榜单"},
  {value: "op-brand", name: "品牌专区 - 横滑"},
  {value: "op-dphc", name: "单品会场"},
  {value: "op-goodslist-with-shopinfo", name: "商品列表（带店铺信息）-榜单"},
  {value: "op-flashsale-goodslist-tongyong", name: "商品列表（不带商户信息）-榜单"},
  {value: "op-business-list", name: "商家列表-品质优选"},
  {value: "op-hjf", name: "海景房"},
  {value: "op-reward", name: "福袋"},
  {value: "op-dpzt", name: "大牌站台"},
]
