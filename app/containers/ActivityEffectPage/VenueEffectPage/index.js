/**
 * VenueEffectPage
 * 会场效果
 */

import React,{Fragment} from 'react';
import { PageBase } from '@/containers/base';
import { PageWrapper } from '@/components/PageWrapper';
import { Breadcrumb, Form, Input, Grid, Button, Select, Dialog } from '@alife/next';
import { Link } from 'react-router-dom';
import moment from 'moment';
import { manageList, setTableCol, filterForm } from '@/components/Table';
import './index.scss';
import { getVenueEffectList } from "../request";
import { DialogBtn, GoldLogLink, LinkButton } from '@/components/Button';
import { LineChart , DataChart } from '@/components/TrendChart';
import { onRequestError } from '@/utils/api'
import { isAllNumber } from '@/utils/validators';
import { doesFormHasErrors } from '@/utils/formTool';
import { TimeRangePicker } from '@/components/TimeRangePicker';
import { formatTimeStamp, FORMAT } from '@/utils/time';

const Option = Select.Option;
const FormItem = Form.Item;
const { Row, Col } = Grid;

const defaultColType = { title: '' }

const columns = [
  { title: '日期', dataIndex: 'dataDt', width:110, cell: (value) => formatTimeStamp(value, FORMAT.DATE) },
  { title: '页面ID', dataIndex: 'pageId', width:90 },
  { title: '页面名称', dataIndex: 'pageName', width:140},
  { title: '页面创建人', dataIndex: 'creator', width:120},
  { title: '页面访问PV', dataIndex: 'pv', width:140},
  { title: '页面访问UV', dataIndex: 'uv', width:140},
  { title: '新零售订单量', dataIndex: 'orderCnt', width:140},
  { title: '新零售净gmv', dataIndex: 'retailPureGmv', width:140},
  { title: '新零售下单用户数', dataIndex: 'orderUserNt', width:160},
  { title: '转化率', dataIndex: 'transRate', width:80},
]

function getCol(type) {
  return { ...defaultColType, ...type }
}

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: {
    span: 17
  },
  style: {
    width: '100%'
  },
  labelAlign: 'left',
  labelTextAlign: 'right'
};

const formLayout = {
  style: {
    margin: '20px 0px 10px 0px'
  },
}

const chartSelectOptions = [{
  name: '页面访问pv',
  key: 'pv'
},{
  name: '页面访问uv',
  key: 'uv'
},{
  name: '订单量',
  key: 'orderCnt'
},{
  name: '净GMV',
  key: 'retailPureGmv'
},{
  name: '下单用户数',
  key: 'orderUserNt'
},{
  name: '转化率',
  key: 'transRate'
}]

/**检测活动id格式 */
const checkActivityId = function(rule, value, callback){
  const errors = isAllNumber(value);
  if(errors.length && value){
    callback('活动ID格式错误')
  } else {
    callback();
  }
}

/**检测时间范围格式 */
const checkEffectRange = function (rule, value, cb) {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    // if(start.isBefore(moment().startOf('day'))) return cb('开始时间不能小于当前时间')
    if (end.clone().subtract(1, 'month').isAfter(start.clone())) return cb('时间段不能超过1个月')
  }
  cb()
}

function PutInForm({ field, searchData, reload }) {
  const onSearch = () => {
    field.validate((e) => {
      if(!doesFormHasErrors(e)){
        searchData()
      }
    })
  }

  return (
    <Form {...formLayout} field={field}>
      <Row>
        <Col span={8}>
          <FormItem label="页面ID" {...formItemLayout} validator={checkActivityId}>
            <Input placeholder="请输入页面ID" name="pageId" />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem label="页面名称" {...formItemLayout}>
            <Input placeholder="请输入页面名称" name="pageName" />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem label="页面创建人" {...formItemLayout}>
            <Input placeholder="请输入页面创建人" name="creator" />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={8}>
          <FormItem label="时间范围" {...formItemLayout} required validator={checkEffectRange} requiredMessage="请选择时间范围">
            <TimeRangePicker
              style={{width:'125%'}}
              name="effectRange"
              defaultValue={[moment().add(-31, 'days'), moment().add(-1, 'days')]}
              disabledDate={(date) => date.isAfter(moment().startOf('day').add(-1, 'days'))}
            ></TimeRangePicker>
          </FormItem>
        </Col>
        <Col span={8}>
          <div style={{marginLeft:"96px"}}>
            <Button type="primary" onClick={onSearch}>查询</Button>&nbsp;&nbsp;
            <Button type="normal" onClick={reload}>重置</Button>
          </div>
        </Col>
      </Row>
    </Form>
  )
}

const PutInFilterForm = filterForm(PutInForm, ['pageId', 'pageName', 'effectRange', 'creator'])
const PutInTable = setTableCol(columns, getCol);

const ManageList = manageList(PutInTable, PutInFilterForm,
  async (search) => {
    let { query } = search;
    let { pageId } = query;
    if(pageId){
      query = { ...query, pageId:+pageId }
    }
    let param = {
      ...search,
      query: {
        pageId: query.pageId || '',
        pageName: query.pageName || '',
        creator: query.creator || '',
        startTime: query.effectRange ? formatTimeStamp(query.effectRange[0], FORMAT.DATE) : moment().add(-31, 'days').format('YYYY-MM-DD'),
        endTime: query.effectRange ? formatTimeStamp(query.effectRange[1], FORMAT.DATE) : moment().add(-1, 'days').format('YYYY-MM-DD'),
      }
    }

    const listData = await getVenueEffectList(param);

    return listData;
  } ,{size:40})

export class VenueEffectPage extends PageBase {
  constructor(props) {
    super(props);
    this.state = {
      data:{}, // 所有数据
      chartSelect:0, // 折线图下拉列表
      lineChartData: '',
    }
  }

  onChange = (value) =>{
    this.setState({
      chartSelect: value,
      lineChartData: this.filterChartData(this.state.data, value)
    })
  }

  handleSource = (data) => {
    this.setState({
      data: data,
      lineChartData: this.filterChartData(data, this.state.chartSelect),
    })

  }

  filterChartData(data, chartSelect) {
    let pageId = data.query.pageId,
        selectKey = chartSelectOptions[chartSelect].key,
        selectName = chartSelectOptions[chartSelect].name,
        seriesListData = [],
        xAxisData = [],
        legendData = [];

    // 未选择页面id 或者 筛选结果为[]
    if(!pageId || data.rows.length == 0) {
      return ''
    }

    data.rows.map(item => {
      seriesListData.push(item[selectKey])
      xAxisData.push(moment(item.dataDt).format('MM-DD'))
    })
    return {
      seriesData: [{
        name: selectName || '',
        data: seriesListData,
      }],
      xAxisData,
      legendData,
    }
  }

  render() {
    let { data, chartSelect, lineChartData } = this.state,
        pageId = (data.query && data.query.pageId) || '';
    return (
      <PageBase.Container className="venue-effect">
        {/*<div className="nav-wrapper">*/}
          {/*<Breadcrumb>*/}
            {/*<Breadcrumb.Item>活动效果</Breadcrumb.Item>*/}
            {/*<Breadcrumb.Item>会场效果</Breadcrumb.Item>*/}
          {/*</Breadcrumb>*/}
        {/*</div>*/}
        {/*<PageWrapper>*/}
          {/*<ManageList handleSource={this.handleSource}>*/}
            {/*<div className="chart-wrapper">*/}
              {/*<LineChart showLegend={false} chartData={lineChartData}  title='趋势图' noDataHeight={'200px'} noDataContent="选择唯一会场，才可展示趋势图">*/}
                {/*<Select className='select-panel' value={chartSelect} disabled={!lineChartData} onChange={this.onChange}>*/}
                  {/*{chartSelectOptions.map((option, index) => <Option key={option.key} value={index}>{option.name}</Option>)}*/}
                {/*</Select>*/}
              {/*</LineChart>*/}
            {/*</div>*/}
          {/*</ManageList>*/}
        {/*</PageWrapper>*/}
          <h5 className="without_permission">该页面已经废弃，如要查看会场效果，请移步【数据洞察】中的 <a style={{textDecoration:'underline'}} target="_blank" href="https://kunlun.alibaba-inc.com/dataAnalysis#/pageEffect/list">页面效果</a></h5>
      </PageBase.Container>
    )
  }
}
