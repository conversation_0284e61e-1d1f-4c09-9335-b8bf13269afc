import { request } from '../../utils/request';
import { putInReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

const requestInstance = request();

/*投放活动管理列表*/
export function getPutInList(params) {
  return putInReq.post('/api/putInActivity/list', params).then(onRequestSuccess)
}

/*投放效果列表*/
export function getPutInActEffectList(params) {
  return requestInstance.post('/api/analyse/queryActivityAnalyseList', params).then(onRequestSuccess)
}

/*活动榜单趋势图*/
export function getActRankList(params) {
  return requestInstance.post('/api/analyse/queryDeliveryInfo', params).then(onRequestSuccess)
}

/*活动榜单-排名*/
export function getRank(params) {
  return requestInstance.post('/api/analyse/querySort', params).then(onRequestSuccess)
}

/*活动榜单-类型占比*/
export function getRatio(params) {
  return requestInstance.post('/api/analyse/queryRotio', params).then(onRequestSuccess)
}

/**会场效果列表 */
export function getVenueEffectList(params){
  return requestInstance.post('/api/analyse/queryPageAnalyseList', params).then(onRequestSuccess)
}


