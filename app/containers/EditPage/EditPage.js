/**
 * EditPage
 *
 * This is the page we show when the user visits a url that doesn't have a route
 */

import React from 'react';
import './style.scss';
import { Radio, Input, Button, Message } from '@alife/next';
import { editPoolResult, onRequestError } from '@/utils/api';
const RadioGroup = Radio.Group;
const typeList = [
  {
    value: 'goods',
    label: '商品池'
    }, {
    value: 'shop',
    label: '门店池'
    }, {
    value: 'goods2shop',
    label: '品转店池'
    }
];
const addList = [
  {
    value: 'true',
    label: '是'
    }, {
    value: 'false',
    label: '否'
    }
];

export default class EditPage extends React.Component {
  constructor(props){
    super(props);

    this.state = {
      poolType: "goods", //选品集类型
      poolId: "", //选品集ID
      ids: "", //需要修改的门店id或者商品的skuid
      addManual: "false", //是否写入黑白名单
      shopId: "", //shopID
    }
  }

  handleChange = (value,type) => {
    let options = {};
    options[type] = value;
    this.setState(options);
  }

  editPool = (type) => {
    const { poolType, poolId, ids } = this.state;
    let data = {
      poolType,
      poolId,
      action: type,
      ids:ids.split(",")
    }
    editPoolResult(data).then((res) => {
      if (res&&res.code=="success") {
        Message.success('操作成功')
      } else {
        Message.error((res && res.message) || "操作失败")
      }
    }).catch(onRequestError)
  }

  render(){
    const { poolType, poolId, ids, addManual, shopId } = this.state;
    return (
      <div className="edit-page">
        <div className="edit-item">
          <span className="item-label">选品集类型：</span>
          <RadioGroup dataSource={typeList} value={poolType} onChange={(val)=>this.handleChange(val,"poolType")} />
        </div>
        <div className="edit-item">
          <span className="item-label">选品集ID：</span>
          <Input placeholder="请输入选品集ID" value={poolId} onChange={(val)=>this.handleChange(val,"poolId")} className="poolid-input"/>
        </div>
        <div className="edit-item">
          <span className="item-label">需要修改的门店ID&skuId：</span>
          <Input.TextArea placeholder="请输入需要修改的门店ID&skuId" value={ids} onChange={(val)=>this.handleChange(val,"ids")} className="ids-input"/>
        </div>
        {/*<div className="edit-item">
          <span className="item-label">是否写入黑白名单：</span>
          <RadioGroup dataSource={addList} value={addManual} onChange={(val)=>this.handleChange(val,"addManual")} />
        </div>*/}
        {/*<div className="edit-item">
          <span className="item-label">门店ID：</span>
          <Input placeholder="请输入选品集ID" value={shopId} onChange={(val)=>this.handleChange(val,"shopId")} className="shopid-input"/>
        </div>*/}
        <div className="edit-btns">
          <Button type="primary" className="add-btn" onClick={()=>this.editPool("add")}>添加</Button>
          <Button type="secondary" className="delete-btn" onClick={()=>this.editPool("del")}>删除</Button>
        </div>
      </div>
    )
  }
}
