/**
 * NotFoundPage
 *
 * This is the page we show when the user visits a url that doesn't have a route
 */

import React from "react";
import "./style.scss";
export default class NotFound extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      renderBody: "",
    };
  }

  componentDidMount() {
    setTimeout(() => {
      this.setState({
        renderBody: (
          <div className="not-found-page">
            <h1 className="not-found-page-msg">暂无权限</h1>
            <h2>
              请先<a href="#/permissionApply">申请权限</a>
            </h2>
          </div>
        ),
      });
    }, 700);
  }

  render() {
    const { renderBody } = this.state;
    return (
      <article>
        <h1>{renderBody}</h1>
      </article>
    );
  }
}
