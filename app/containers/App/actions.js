import {
  LOAD_REPOS,
  LOAD_REPOS_SUCCESS,
  LOAD_REPOS_ERROR,
  TOAST_MESSAGE,
  SET_QUERY_META,
  GET_PERMISSION,
  SET_PERMISSION,
  GET_CITIES,
  SET_CITIES,
  GET_DELIVERY_DATA,
  SET_DELIVERY_DATA,
} from './constants';

/**
 * Load the repositories, this action starts the request saga
 *
 * @return {object} An action object with a type of LOAD_REPOS
 */
export function loadRepos() {
  return {
    type: LOAD_REPOS,
  };
}

/**
 * Dispatched when the repositories are loaded by the request saga
 *
 * @param  {array} repos The repository data
 * @param  {string} username The current username
 *
 * @return {object}      An action object with a type of LOAD_REPOS_SUCCESS passing the repos
 */
export function reposLoaded(repos, username) {
  return {
    type: LOAD_REPOS_SUCCESS,
    repos,
    username,
  };
}

/**
 * Dispatched when loading the repositories fails
 *
 * @param  {object} error The error
 *
 * @return {object}       An action object with a type of LOAD_REPOS_ERROR passing the error
 */
export function repoLoadingError(error) {
  return {
    type: LOAD_REPOS_ERROR,
    error,
  };
}


export function toastMessage(payload) {
  return {
    type: TOAST_MESSAGE,
    payload,
  };
}

export function setQueryMeta(payload){
  return {
    type: SET_QUERY_META,
    payload
  }
}

/**
 * get permission
 */
export function getPermission() {
  return {
    type: GET_PERMISSION
  }
}

export function setPermission(permission) {
  return {
    type: SET_PERMISSION,
    permission
  }
}


/**获取城市 */
export function getCities(){
  return {
    type: GET_CITIES
  }
}

/**设置城市 */
export function setCities(cities){
  return {
    type: SET_CITIES,
    cities
  }
}

/**获取渠道 */
export function getDeliveryData(deliveryData){
  return {
    type: GET_DELIVERY_DATA,
    deliveryData
  }
}

/**设置渠道 */
export function setDeliveryData(deliveryData){
  return {
    type: SET_DELIVERY_DATA,
    deliveryData
  }
}

