/**
 * The global state selectors
 */

import { createSelector } from 'reselect';

const selectGlobal = (state) => state.get('global');

const selectRoute = (state) => state.get('route');

const makeSelectCurrentUser = () => createSelector(
  selectGlobal,
  (globalState) => globalState.get('currentUser')
);

const makeSelectLoading = () => createSelector(
  selectGlobal,
  (globalState) => globalState.get('loading')
);

const makeSelectError = () => createSelector(
  selectGlobal,
  (globalState) => globalState.get('error')
);

const makeSelectRepos = () => createSelector(
  selectGlobal,
  (globalState) => globalState.getIn(['userData', 'repositories'])
);

const makeSelectLocation = () => createSelector(
  selectRoute,
  (routeState) => routeState.get('location').toJS()
);

const makeSelectQueryMeta = () => createSelector(
  selectGlobal,
  (globalState) => globalState.get('queryMeta')
);

const makePermission = () => createSelector(
  selectGlobal,
  (globalState) => globalState.get('permission').toJS()
)

const makeCities = () => createSelector(
  selectGlobal,
  (globalState) => globalState.get('cities')
)

// const makeDeliveryData = () => createSelector(
//   selectGlobal,
//   (globalState) => ({
//     channelListMap: globalState.get('channelListMap'),
//     labelListMap: globalState.get('labelListMap'),
//     checkAllValue: globalState.get('checkAllValue'),
//   })
// )

const makeDeliveryData = () => createSelector(
  selectGlobal,
  (globalState) => globalState.get('deliveryData')
)


export {
  selectGlobal,
  makeSelectCurrentUser,
  makeSelectLoading,
  makeSelectError,
  makeSelectRepos,
  makeSelectLocation,
  makeSelectQueryMeta,
  makePermission,
  makeCities,
  makeDeliveryData
};
