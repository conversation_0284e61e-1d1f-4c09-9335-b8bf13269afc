/*
 * AppConstants
 * Each action has a corresponding type, which the reducer knows and picks up on.
 * To avoid weird typos between the reducer and the actions, we save them as
 * constants here. We prefix them with 'yourproject/YourComponent' so we avoid
 * reducers accidentally picking up actions they shouldn't.
 *
 * Follow this format:
 * export const YOUR_ACTION_CONSTANT = 'yourproject/YourContainer/YOUR_ACTION_CONSTANT';
 */

export const LOAD_REPOS = 'boilerplate/App/LOAD_REPOS';
export const LOAD_REPOS_SUCCESS = 'boilerplate/App/LOAD_REPOS_SUCCESS';
export const LOAD_REPOS_ERROR = 'boilerplate/App/LOAD_REPOS_ERROR';
export const DEFAULT_LOCALE = 'en';
export const TOAST_MESSAGE = 'TOAST_MESSAGE'
export const SET_QUERY_META = 'SET_QUERY_META'
export const GET_PERMISSION = 'get_permission'
export const SET_PERMISSION = 'set_permission'
export const GET_CITIES = 'get_cities'
export const SET_CITIES = 'set_cities'
export const GET_DELIVERY_DATA = 'get_delivery_data'
export const SET_DELIVERY_DATA = 'set_delivery_data'