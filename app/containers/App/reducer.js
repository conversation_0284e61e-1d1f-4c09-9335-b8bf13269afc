/*
 * AppReducer
 *
 * The reducer takes care of our data. Using actions, we can change our
 * application state.
 * To add a new action, add it to the switch statement in the reducer function
 *
 * Example:
 * case YOUR_ACTION_CONSTANT:
 *   return state.set('yourStateVariable', true);
 */

import { fromJS, List } from 'immutable';

import {
  LOAD_REPOS_SUCCESS,
  LOAD_REPOS,
  LOAD_REPOS_ERROR,
  SET_QUERY_META,
  SET_PERMISSION,
  SET_CITIES,
  SET_DELIVERY_DATA
} from './constants';

// The initial state of the App
const initialState = fromJS({
  loading: false,
  error: false,
  currentUser: false,
  userData: {
    repositories: false,
  },
  permission:[],
  queryMeta: null,
  cities: [],
  deliveryData:'',
});

function appReducer(state = initialState, action) {
  switch (action.type) {
    case LOAD_REPOS:
      return state
        .set('loading', true)
        .set('error', false)
        .setIn(['userData', 'repositories'], false);
    case LOAD_REPOS_SUCCESS:
      return state
        .setIn(['userData', 'repositories'], action.repos)
        .set('loading', false)
        .set('currentUser', action.username);
    case LOAD_REPOS_ERROR:
      return state
        .set('error', action.error)
        .set('loading', false);
    case SET_QUERY_META:
      return state.set('queryMeta', action.payload)
    case SET_PERMISSION:
      return state.set('permission', List(action.permission))
    case SET_DELIVERY_DATA:
      return state.set('deliveryData', action.deliveryData)
    case SET_CITIES:
      return state.set('cities', action.cities)
    default:
      return state;
  }
}

export default appReducer;
