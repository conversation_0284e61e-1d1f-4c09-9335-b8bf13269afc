import { call, takeLatest, put } from 'redux-saga/effects';
import { Message } from '@alife/next';
import { TOAST_MESSAGE, GET_CITIES, GET_DELIVERY_DATA, GET_PERMISSION } from '@/containers/App/constants';
import { sleep } from '@/utils/others';
import { formatTimeStamp, FORMAT} from '@/utils/time';
import { env } from '@/config';
import * as api from '@/utils/api';
import * as actions from './actions';
import { getCityList, getAllDeliveryChannel } from "@/utils/api";
import moment from 'moment';

export function* getToast({ payload }) {
  try {
    const { error, ...rest } = payload;
    if (error) console.error(error)
    Message.show(rest);
    yield call(sleep, 3000);
  } catch (error) {
    console.error(error)
  }
}

/**获取权限 */
export function* getPermission() {
  try {
    const permissionList = yield call(api.getPermission)
    if (env !== 'prod' && localStorage.getItem('showMockMenu')) {
      // 模拟菜单，用于未配置菜单的时候，可绕过 ACL 菜单配置提前进行开发
      permissionList.subMenus.forEach(item => {
        if (item.menuUrl === '/channelManage') {
          const mockMenuList = [{
            "code": 0,
            "msg": "OK",
            "result": null,
            "menuId": "1102328050000",
            "menuParentId": "110097772",
            "menuName": "mock_url",
            "menuTitle": "测试频道页",
            "menuTitleEN": "",
            "menuDescription": "100530000", // 每次改这里，具体值由后端提供
            "menuDescriptionEN": null,
            "menuUrl": "/channelManage/mockUrl",
            "permissionName": null,
            "subMenus": [],
            "sort": "100",
            "leaf": true,
            "success": true
          }];
          mockMenuList.map(menu => item.subMenus.push(menu));
        }
      })
  }
    
    yield put(actions.setPermission(permissionList.subMenus))
  } catch (error) {
    api.onRequestError(error)
  }
}

/**获取城市 */
export function* getCities(){
  try {
    const  cities = yield call(getCityList);
    yield put(actions.setCities(cities))

  } catch(error){
    api.onRequestError(error)
  }
}

function handleChannelList(channelList)  {
  channelList.forEach(channelItem => {
    if (channelItem.subChannels) {
      channelItem.children = JSON.parse(JSON.stringify(channelItem.subChannels));
      handleChannelList(channelItem.children);
    } else {
      channelItem.children = [];
    }
  })
}

/**获取渠道 */
export function* getDeliveryData(){
  try {
    let { channelList: channelListMap, labelList: labelListMap } = yield call(getAllDeliveryChannel);
    handleChannelList(channelListMap);
    let deliveryData = {
      channelListMap: channelListMap.slice(0),
      labelListMap,
      checkAllValue: channelListMap[0].value,
    }
    yield put(actions.setDeliveryData(deliveryData))
    localStorage.setItem("deliveryData", JSON.stringify(deliveryData));
  } catch(error){
    api.onRequestError(error)
  }
}

export function* saga() {
  yield takeLatest(TOAST_MESSAGE, getToast);
  yield takeLatest(GET_PERMISSION, getPermission)
  yield takeLatest(GET_CITIES, getCities);
  yield takeLatest(GET_DELIVERY_DATA, getDeliveryData);
}
