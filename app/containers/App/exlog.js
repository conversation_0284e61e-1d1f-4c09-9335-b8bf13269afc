import AlscExlog from "@ali/alsc-exlog-web";

function resolveAssetVersion() {
  try {
    const pattern = /o2o-selection-admin\/([\d\.]+)\/.*\.css/;
    const links = document.querySelectorAll("link");
    for (let i = 0; i < links.length; i++) {
      const el = links[i];
      const result = pattern.exec(el.href);
      if (result) {
        const version = result[1];
        return version;
      }
    }
  } catch (e) {
    console.error(e);
    return null;
  }
}

export const exlog = AlscExlog({
  pid: "selection",
  env: window.configEnv,
  version: resolveAssetVersion(),
  debug: localStorage.getItem("__selection_exlog_debug__") === "true",
  commonParams: {},
});

export function init() {}
