export const Schema = {
  "detail": {
    "title": "基础配置",
    "required": ["configName", "beginTime", "endTime", "imageUrl", "jumpUrl"],
    "type": "object",
    "properties": {
      "configId": {
        "type": "string",
        "title": "配置ID:",
        "disabled": true,
        "x-ui-placeholder": "保存后自动生成"
      },
      "configName": {
        "type": "string",
        "title": "配置名称:",
        "x-ui-placeholder": "请输入配置名称，最多20个字",
        "maximum": 20
      },
      "beginTime": {
        "type": "number",
        "title": "开始时间:",
        "x-ui-widget": "DateTimeWidget"
      },
      "endTime": {
        "type": "number",
        "title": "结束时间:",
        "x-ui-widget": "DateTimeWidget",
        "defaultTimeValue": "23:59:59"
      },
      "workDay": {
        "type": "array",
        "title": "星期:",
        "x-ui-className": "selection-form",
        "x-ui-widget": "dayOfWeek",
        "items": {
          "type": "number",
          "enum": [0, 1, 2, 3, 4, 5, 6, 7],
          "enumNames": ["全选", "周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        },
        "uniqueItems": true,
        "default": [1, 2, 3, 4, 5, 6, 7]
      },
      "timeSelection": {
        "type": "string",
        "title": "时段:",
        "enum": ["0", "1"],
        "x-ui-className": "selection-form",
        "x-ui-valueLabel": {
          "0": "全天",
          "1": "选择"
        },
        "default": "0",
        "x-ui-widget": "radio"
      },
      "timeRanges": {
        "type": "array",
        "title": " ",
        "x-ui-hidden": "$.timeSelection == 0",
        "maxItems": 3,
        "items": {
          "type": "string",
          "format": "time-range",
          "default": "-"
        }
      },
      "position": {
        "type": "string",
        "title": "指定位置:",
        "enum": ["1", "2", "3", "4"],
        "x-ui-className": "selection-form",
        "x-ui-valueLabel": {
          "1": "位置一",
          "2": "位置二",
          "3": "位置三",
          "4": "位置四"
        },
        "x-ui-widget": "radio"
      },
      "imageUrl": {
        "title": "banner图片:",
        "type": "string",
        "format": "uri",
        "x-ui-widget": "img-upload",
        "x-ui-validate": {
          "width": 1500,
          "height": 600,
          "maxSize": 500,
          "accept": "png,apng,jpg,jpeg,gif"
        }
      },
      "jumpType": {
        "title": "跳转形式：",
        "type": "string",
        "enum": [
          "1",
          "2"
        ],
        "x-ui-className": "selection-form",
        "x-ui-valueLabel": {
          "1": "内页",
          "2": "h5页"
        },
        "default": "2",
        "disabled": true,
        "x-ui-widget": "select"
      },
      "jumpUrl": {
        "title": "跳转链接：",
        "type": "string",
        "x-ui-placeholder": "请输入ele加密的h5链接"
      },
      //饿了么首页(新)-逛超市-中通banner  下拉框多选
      "brandIdList": {
        "type": "array",
        "title": "品牌类型:",
        "x-ui-className": "selection-form",
        "x-ui-widget": "select",
        "items": {
          "type": "string",
          "enum": ["42267992481", "22267016848", "2209155277", "2235666172", "1432872007", "2267035370", "1432872006", "2267191289", "1432872023", "2234685025", "2235931190", "1432872163", "2206315214", "2233208737", "22267029874", "2235682343", "2217446873", "32267026254", "1432872009", "1432872136", "100000050000200000", "2267230570", "1432872405", "2267041151", "2267237975", "42267071030", "1432872052", "2267254123", "2209642807"],
          "enumNames": ["天猫超市", "华润万家标超", "永辉超市", "中百超市", "大润发", "物美超市", "华润万家", "盒马", "步步高", "联华超市", "苏果生活超市", "世纪联华", "华润苏果", "家乐福", "家家悦", "中百仓储超市", "新华都", "华联超市", "百佳", "麦德龙", "小润发超市", "ole", "三江", "世纪联华超市", "blt", "SANJIANG", "维客（青岛）", "吉买盛", "百联华联超市"]
        },
        "uniqueItems": true
      },
      "activityType": {
        "title": "判空投放活动类型：",
        "type": "string",
        "x-ui-className": "selection-form",
        "enum": ["1", "2"],
        "x-ui-valueLabel": {
          "1": "商品",
          "2": "门店"
        },
        "x-ui-widget": "radio",
        "default": "1"
      },
      "activityIds": {
        "title": "",
        "type": "array",
        "maxItems": 1,
        "items": {
          "type": "number",
          "x-ui-placeholder": "请输入判空投放活动ID",
          "default": ""
        }
      },
      "priority": {
        "title": "权重：",
        "type": "number",
        "x-ui-placeholder": "取值优先级：数值＞时间",
        "maximum": 999999,
        "hasLimitHint": true
      },
      "peopleActualType": {
        "type": "array",
        "title": "实时用户类型:",
        "x-ui-className": "selection-form",
        "x-ui-widget": "CheckboxesWidget",
        "items": {
          "type": "string",
          "enum": ["0", "1", "3"],
          "enumNames": ["全部", "新零售新客", "医药新客"]
        },
        "uniqueItems": true,
        "default": ["0"]
      },
      "operatingCondition": { //多选
				"title": "商家营业状态：",
				"type": "array",
				"x-ui-className": "selection-form",
				"items": {
					"type": "string",
					"enum": ["1", "2"],
					"enumNames": ["营业中", "预定中"]
				},
				"default": [],
				"x-ui-widget": "checkboxes",
				"uniqueItems": true
			},
      "ext_search_whole_fill_color": {
        "type": "string",
        "x-ui-widget": "ColorWidget",
        "title": "搜索栏（整体图形填充颜色）:",
        "x-ui-placeholder": "参考#FFFFFF 90%透明度"
      },
      "ext_search_whole_pellucidity": {
        "type": "string",
        "x-ui-widget": "NumberWidget",
        "title": "搜索栏（整体图形透明度）:",
        "multipleOf": 0.1,
        "precision": 3,
        "minimum": 0,
        "maximum": 1,
        "x-ui-placeholder": "参考#FFFFFF 90%透明度"
      },
      "priceRange": { // 这个是类似选品里面的价格 —— 可参考场景的schema
        "title": "商品现价：",
        "type": "string",
        "x-ui-widget": "price-range",
        "default": "-",
        "precision": 2,
        "step": 0.01,
        "fieldGroup": "itemRule"
      },
      "inviteId": { // 这个是类似选品里面的商品id、招商id（英文逗号隔开的，输入多个的id或者名称的）
        "type": "string",
        "x-ui-widget": "string-array",
        "title": "招商ID:",
        "pattern": "^[0-9,]*$",
        "rows": 3,
        "x-ui-placeholder": "请输入招商ID,以英文逗号隔开",
        "maxLength": 100
      },
      "category": {  // 需要调接口的（这是商品分类） —— 可参考场景的schema
        "type": "string",
        "multiple": true,
        "x-ui-widget": "dynamicCascadeSelectWidget",
        "title": "商品分类:",
        "x-ui-placeholder": "请输入商品分类",
        "showSearch": true,
        "canGetThreeLevel": true, // 需要传三级出去的，需要不用填
        "requestOptions": {
          "method": "post",
          "apiUrl": "api/common/findDataSourceByKeyWordFuzzy/Category",
          "domainInfo": "admin"
        }
      },
      "brand": { // 需要调接口的，并且是模糊搜索的（这是所属品牌） —— 可参考场景的schema
        "type": "string",
        "multiple": true,
        "x-ui-widget": "dynamicCascadeSelectWidget",
        "title": "品牌名称:",
        "x-ui-placeholder": "请输入品牌名称",
        "showSearch": true,
        "requestOptions": {
          "method": "post",
          "apiUrl": "api/common/findDataSourceByKeyWordFuzzy/BrandName",
          "domainInfo": "admin",
          "searchField": "keyword"  // 模糊搜索的keyword
        }
      },
    }
  },
  "showPutIn": true,
  "showAoi": true,
  "showMainCategory":true,//商家主营类目
  "supChannel": true, //关联上几配置
	"outRequired": ["relatedSup"],//关联上级配置
  "showMultilineIdText": [{ //ID类型多行
    "key": "brand_id",
    "name": "商家黑名单",
    "placeholder":"提示框，例如：商家品牌id",
    "required": false,
    "maxLength": 20,
    "hidden":'position == 2'//只能识别!=和==。他会去判断formData中position的值和2是否符合条件，符合条件就隐藏
  },{
    "key": "brand_id2",
    "name": "商家黑名单2",
    "required": false,
    "maxLength": 20
  }],
  "oversupply": { //全能超市专用
    "maxTabLength": 6, //最多有几个TAB
    "maxCommodityLength": 2, //选品集最多有几个
    "nameSetting": "tab设置（1-6）", //整个TAB的lable名称
    "name": "tab名称", // 每个TAB下多出来的字段的名称
    "field": "tabName", //每个TAB下多出来的字段的key，有此字段才会展示
    "maxFieldLength": 4, //每个TAB下多出来的字段的最大字数
    "isAllowShow": true, //本资源位没用到的,是否一直展示供给类型
    "isRequired": false, // 是否必填  弃用
    "isNotRequired": false, // 非必填
    "initTabLength": 2, // 初始化几个tab
    "hideTabName": false,  //隐藏tab名称
    "defaultFieldValue": 1,
    "fieldType": "number",
    "supplyTypeDataSource": [
      {
        "label": "指定选品集",
        "value": 1
      },
      {
        "label": "不指定，商品聚类",
        "value": 2
      },
      {
        "label": "营销活动ID2",
        "value": 3,
        "options":{
          "rows":3,
          "maxLength":2,
          "x-ui-placeholder": "请输入营销活动ID，多个营销活动ID使用英文逗号隔开，至多30个"
        }
      },
      {
        "label": "招商活动ID2",
        "value": 4
      }
    ]
  },
  "multipleSchema": {
    "key": "queryWordList",
    "titleName": "话题词",
    "title": "",
    "type": "object",
    "max": 1,
    "initData": {
      "logoImgUrl": "",
      "word": ""
    },
    "properties": {
      "logoImgUrl": {
        "title": "logo图片:",
        "type": "string",
        "format": "uri",
        "x-ui-widget": "img-upload",
        "multiple": true,
        "x-ui-validate": {
          "width": 66,
          "height": 66,
          "maxSize": 500,
          "accept": "png,jpeg,jpg,apng,gif"
        }
      },
      "word": {
        "type": "string",
        "title": "query词文案:",
        "x-ui-placeholder": "请输入配置名称"
      }
    }
  },
  "blackStorePoolIds": {
    "type": "array",
    "multiple": true,
    "x-ui-widget": "poolOrShopSelectWidget",
    "title": "取消投放门店池:",
    "maxLength": 5,
    "showSearch": true,
    "requestOptions": {
      "apiUrl": "/api/deliverymanage/getPoolInfoSurge",
      "searchKey": "searchKey",
      "params": {
        "poolType": 2
      }
    }
  },
  "selectionCollection": [
    {
      "selectionName": "商品榜",
      "selectionFields": "pureDataSourceList",
      "maxCommodityLength": 2,
      "isNotRequired": false,
      "hidden": "dataSourceType == 0"
    },
    {
      "selectionName": "品牌榜",
      "selectionFields": "brandPureDataSourceList",
      "maxCommodityLength": 2,
      "isNotRequired": false,
      "hidden": "dataSourceType == 0"
    },
    {
      "selectionName": "店铺榜",
      "selectionFields": "storePureDataSourceList",
      "maxCommodityLength": 2,
      "isNotRequired": false,
      "hidden": "dataSourceType == 0"
    }
  ],
  "dynamicDataSourceForm":[
    {
      "fieldName":"rankIdList", // 字段名称
      "componentName":"榜单ID", // 组件名称
      "isMultiple":true, // 是不是多选
      "placeholder":"请输入榜单ID", //底纹
      "isSearch":true,//仅可控是否有搜索。
      "isDynamicDataSource":true,//是否是动态数据源
      "apiName":"/aaaa/bbbbb", //接口名称
      "isRequire":true, // 是否必填
      "hidden":false, // 是否隐藏
      "maxFields":1, // 最多填多少个。
      "requestMethod":'POST',//请求方法
      "requestGateway": "putInReq", //网关是选品还是投放
    }
  ]
};
