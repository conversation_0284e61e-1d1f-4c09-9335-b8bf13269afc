export const Schema = {
  'title':'基础配置',
  'type':'object',
  'properties': {
    'configId': {
      'type': 'string',
      'title': '配置ID:',
      'disabled': true,
      'x-ui-placeholder': '保存后自动生成'
    },
    'configName': {
      'type': 'string',
      'title': '配置名称:',
      'x-ui-placeholder': '请输入配置名称，最多20个字',
      'maximum': 20,
    },
    'redPackText': {
      'type': 'string',
      'title': '红包文案:',
      'x-ui-widget': 'redPackWidget',
    },
    'beginTime': {
      'type': 'number',
      'title': '开始时间:',
      'x-ui-widget': 'DateTimeWidget'
    },
    'endTime': {
      'type': 'number',
      'title': '结束时间:',
      'x-ui-widget': 'DateTimeWidget'
    },
    'workDay': {
      'type': 'array',
      'title': '星期:',
      'x-ui-className': 'selection-form',
      'x-ui-widget': 'dayOfWeek',
      'items': {
        'type': 'number',
        'enum': [0, 1, 2, 3, 4, 5, 6, 7],
        'enumNames': [
          '全选',
          '周一',
          '周二',
          '周三',
          '周四',
          '周五',
          '周六',
          '周日'
        ],
      },
      'uniqueItems': true
    },
    'timeSelection': {
      'type': 'string',
      'title': '时段:',
      'enum': ['0', '1'],
      'x-ui-className': 'selection-form',
      'x-ui-valueLabel': {
        '0': '全天',
        '1': '选择'
      },
      default: '0',
      'x-ui-widget': 'radio'
    },
    'timeRanges': {
      'type': 'array',
      'title': ' ',
      'x-ui-hidden': '$.timeSelection == 0',
      'maxItems': 3,
      'default': ['-'],
      'items': {
        'type': 'string',
        'format': 'time-range',
        'default': '-'
      }
    },
    'imageUrl': {
      'title': '背景图:',
      'type': 'string',
      'format': 'uri',
      'x-ui-widget': 'img-upload',
      'x-ui-validate': {
        'width': 488,
        'height': 600,
        'maxSize': 10000,
        'accept': 'png,jpeg,jpg,apng'
      }
    },
    'mainTitle': {
      'title': '主标题：',
      'type': 'string',
      'maximum': 9,
      'x-ui-placeholder': '请输入主标题名称，最多9个字',
    },
    'subTitle': {
      'title': '副标题：',
      'type': 'string',
      'maximum': 11,
      'x-ui-placeholder': '请输入副标题文案，最多11个字'
    },
    'brandLogo': {
      'title': '品牌logo图:',
      'type': 'string',
      'format': 'uri',
      'x-ui-widget': 'img-upload',
      'x-ui-validate': {
        'width': 30,
        'height': 30,
        'accept': 'png,jpeg,jpg,apng'
      }
    },
    'jumpType': {
      'title': '跳转：',
      'type': 'number',
      'enum': [1, 2],
      'x-ui-className': 'selection-form',
      'x-ui-valueLabel': {
        1: '门店',
        2: 'h5页'
      },
      'default': 1,
      'x-ui-widget': 'select'
    },
    'jumpUrl': {
      'title': '跳转链接：',
      'type': 'string',
      'x-ui-hidden': '$.jumpType == 1',
      'x-ui-placeholder': '请输入ele加密的h5链接'
    },
    'activityType': {
      'title': '投放活动ID：',
      'type': 'number',
      'x-ui-className': 'selection-form',
      'enum': [1, 2],
      'x-ui-valueLabel': {
        1: '商品',
        2: '门店'
      },
      'x-ui-widget': 'radio'
    },
    'activityIds': {
      'title': '投放活动ID：',
      'type': 'array',
      'maxItems': 3,
      'default': [],
      'items': {
        'type': 'string',
        'x-ui-placeholder': '请输入投放活动ID',
        'default': ''
      }
    },
    'showShopWindow': {
      'title': '是否展示橱窗：',
      'type': 'boolean',
      'x-ui-className': 'selection-form',
      'x-ui-valueLabel': {
        true: '是',
        false: '否'
      },
      'x-ui-widget': 'radio'
    },
    'insertInto': {
      'title': '加塞模块：',
      'type': 'array',
      'x-ui-className': 'selection-form',
      'items': {
        'type': 'number',
        'enum': [1, 2],
        'enumNames': [
          '超级爆品',
          '分类tab1'
        ],
      },
      'x-ui-widget': 'checkboxes',
      'uniqueItems': true
    },
    'priority': {
      'title': '权重：',
      'type': 'string',
      'x-ui-placeholder': '取值优先级：数值＞时间',
      'maximum': 20,
      'hasLimitHint': true
    },
    'skipIconContentType': {
      'title': '跳转按钮文案：',
      'type': 'string',
      'enum': ['1', '2'],
      'x-ui-valueLabel':
      {
        '1': '红包文案', '2': '普通文案'
      },
      'default': '1',
      'x-ui-widget': 'select'
    }
  },
  'dependencies': {
    'skipIconContentType':{
      'oneOf':[
        {
          'properties': {
            'skipIconContentType':{
              'enum': ['1']
            },
            'redPacketTitle': {
              'type': 'string',
              'title': '红包文案: ',
              'x-ui-widget': 'redPackWidget'
            },
            'rightId': {
              'title': '权益投放活动ID：',
              'type': 'array',
              'maxItems': 1,
              'default': [''],
              'items': { 'title': '', 'type': 'string', 'x-ui-placeholder': '请输入权益投放ID'}
            },
          },
          'required': ['redPacketTitle', 'rightId']
        },
        {
          'properties': {
            'skipIconContentType': {
              'enum': ['2']
            },
            'skipIconContent': {
              'title': '普通文案：',
              'type': 'string',
              'x-ui-placeholder': '请输入普通文案'
            },
          }
        }
      ]
    }
  }
};