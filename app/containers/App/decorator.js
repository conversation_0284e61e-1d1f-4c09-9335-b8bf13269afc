import { compose } from 'redux';
import injectSaga from '@/utils/injectSaga';
import { connect } from 'react-redux';
import { saga } from './saga';
import { getCities, getDeliveryData, getPermission } from './actions';
import { createStructuredSelector } from 'reselect';
import { makePermission } from './selectors';



const mapDispatchToProps = (dispatch) => ({
  getPermission: () => dispatch(getPermission()),
  getCities: () => dispatch(getCities()),
  getDeliveryData: () => dispatch(getDeliveryData()),
});

const mapStateToProps = createStructuredSelector({
  permission: makePermission()
});
const withConnect = connect(mapStateToProps, mapDispatchToProps);

const withSaga = injectSaga({ key: 'app', saga });
export const connectRedux = compose(withSaga, withConnect)
