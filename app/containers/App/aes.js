import AES from "@ali/aes-tracker";
import AESPluginPV from "@ali/aes-tracker-plugin-pv";
import AESPluginAutolog from "@ali/aes-tracker-plugin-autolog";
import { env } from "@/config";

const aesInstall = (name, id) => {
  if (window.AES) {
    return;
  }
  window.AES = new AES({
    pid: "dN5pbd", //自己新建项目的pid,
    user_type: "14",
    env: env,
    uid: id,
    username: name,
  });

  // 挂载插件
  window.AES.use([
    [
      AESPluginPV,
      {
        autoPV: true,
        autoLeave: true,
        enableHash: true,
        enableHistory: true,
        getPageId: (u) => {
          const urlObj = new URL(u);
          const hash = urlObj.hash ? urlObj.hash.replace("#", "") : "";
          if (hash.startsWith("/permissionApply/deny")) {
            return "权限申请-权限拦截引导页面";
          }
          if (hash.startsWith("/permissionApply")) {
            return "权限申请-权限申请兜底页面";
          }
          if (hash === "/pool/list") {
            return "选品选商-选品选商";
          }
          if (hash === "/setting/poolClearing") {
            return "选品选商-池子清理";
          }
          if (hash === "/putIn/list") {
            return "投放活动管理";
          }
          if (hash.indexOf("/channelManage") > -1) {
            return `资源管理-${hash.replace("/channelManage", "")}`;
          }
          if (hash === "/rankManage/list") {
            return "全能榜单管理-榜单列表";
          }
          if (hash === "/report/resource") {
            return "投放数据-资源位报表";
          }
          if (hash === "/schedule") {
            return "投放数据-排期报表";
          }
        },
      },
    ],
    AESPluginAutolog,
  ]);
};

export { aesInstall };
