.app-wrapper {
  margin: 0 auto;
  display: flex;
  height: 100%;
  flex-direction: column;
  .kl-sider {
    position: relative;
    z-index: 2;
    transition: all 0.3s;
    .kl-menu{
      &::-webkit-scrollbar{
        display: none;
      }
    }
  }

  .kl-sider-hidden{
    width: 0 !important;
    flex: 0;
  }

  .main-content {
    width: 100%;
    display: flex;
    flex: 1;
    max-height: calc(100vh - 60px);

    & > :nth-child(2) {
      flex: 1 1 0;
      max-width: calc(100vw - 198px);
      overflow-y: scroll;
    }
    .right-part{
      min-width:1200px;
      overflow-x: scroll;
      &.has-announcement{
        .part-content{
          margin-top: 50px;
        }
      }
      &.no-announcement{
        .announcement{
          display: none !important;
        }
        .resource-index>.body, .resource>.body{
          margin-top: 0 !important;
        }
      }
    }
  }

  &.invite {
    .header-wrap,.header-wrap-v2 {
      display: none;
    }

    .main-content {
      max-height: 100%;

      .kl-sider {
        display: none;
      }

      & > :nth-child(2) {
        max-width: 100%;
      }
    }
  }
}
