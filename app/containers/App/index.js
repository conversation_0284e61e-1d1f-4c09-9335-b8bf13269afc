import "./style.scss";

import qs from "query-string";
import debugFn from "debug";
import React from "react";
import axios from "axios";
import Tracker from "@ali/tracker";
import AES from "@ali/aes-tracker";
import AESPluginPV from "@ali/aes-tracker-plugin-pv";
import AESPluginAutolog from "@ali/aes-tracker-plugin-autolog";
import InterfacePlugin from "@ali/tracker/plugins/Interface";
import PerformancePlugin from "@ali/tracker/plugins/Performance";
import { aesInstall } from "@/containers/App/aes";
import { Switch } from "react-router-dom";
import { createReactRoutes, createMenuData } from "@/containers/routes";
import { Header } from "@alife/kunlun-base";
import { Sider } from "@alife/kunlun-base";
import { AnnouncementPage } from "@/home/<USER>";
import { connectRedux } from "./decorator";
import { init as initExLog, exlog } from "./exlog";
import { config } from "@/config";
import * as api from "@/adator/api";
import { client as yundingClient } from "@/adator/api/yunding";
import { Dialog, Message } from "@alife/next";
import { GatewayResultCode, ErrorName } from "@ali/alsc-gateway-web-client";
import { getAllMenu } from "@/utils/api";
import { __dangerous_updateStoreAndPoolEnum__ } from "@/home/<USER>/common";

const debug = debugFn("selection:app");
let setIntervalSearch;
let setIntervalCountdown;
console.log("process.env.NODE_ENV:" + process.env.NODE_ENV);
let tracker;
if (process.env.NODE_ENV === "production") {
  tracker = new Tracker({
    pid: "o2o-selection-admin",
    plugins: [[InterfacePlugin], [PerformancePlugin]],
  });
  tracker.install();
  tracker.log({
    code: 11, // 系统自动生成，请勿修改
    msg: "页面访问情况", // 异常信息，推荐传入
    sampleRate: 1.0, // 目前采样率为 100.00%
  });

  let scr = document.createElement("script");
  scr.type = "text/javascript";
  scr.src = "//g.alicdn.com/alilog/mlog/aplus_v2.js";
  scr.id = "beacon-aplus";
  scr.async = "true";
  scr.setAttribute("exparams", "clog=o&aplus&sidx=aplusSidx&ckx=aplusCkx");
  document.body.append(scr);

  window.onbeforeunload = function () {
    const path = window.location.href.slice(7);
    const queue = window.goldlog_queue || (window.goldlog_queue = []);
    queue.push({
      action: "goldlog.record",
      arguments: [
        "/selection_kunlun.EXIST-PATH.exist-path",
        "OTHER",
        `path=${path}`,
        "GET",
      ],
    });
  };
}

initExLog();

function visitMenu(menuData, callback, parents = []) {
  for (let i = 0; i < (menuData || []).length; i++) {
    const item = menuData[i];
    let abort = callback(item, parents);
    if (abort) {
      return abort;
    }
    if (Array.isArray(item.subMenus)) {
      abort = visitMenu(item.subMenus, callback, [...parents, item]);
      if (abort) {
        return abort;
      }
    }
  }
}

@connectRedux
export class App extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      announcementData: [],
      visible: false,
      countdown: 5,
      networkType: "",
      allMenu: null,
      // 选品选店模块有一些全局枚举值需要替换成后端接口下发的数据，页面真正启动之前要加载好，否则不能正常加载页面
      poolPageEnumInfoReady: false
    };
    this.aes = new AES({ pid: "dN5pbd", user_type: "14" });
  }

  componentDidMount() {
    const that = this;

    // 强制访问新域名 —— 预发
    if (location.href.indexOf("pre-kunlun.alibaba-inc.com/selection") >= 0) {
      location.href = location.href.replace(
        "pre-kunlun.alibaba-inc.com/selection",
        "selection.pre-fc.alibaba-inc.com",
      );
    }

    async function checkNetworkType(postSetupCallback) {
      try {
        const axiosRes = await axios({
          method: "GET",
          url: "https://kunlun.alibaba-inc.com/system/manifest/XTPT-v2",
          withCredentials: true,
          data: {},
          timeout: 5000,
        });

        /**
         * 该接口只有内网可以访问，通过返回值判断
         *  如果可以访问，说明是内网，继续执行
         *  如果不可以访问，说明是公网，禁止访问选投平台。
         *  公网访问这个接口会超时，禁止访问投放平台
         */
        if (axiosRes.data.data && axiosRes.data.data.url) {
          that.setState({ networkType: "neiWang" });
        } else {
          that.setState({ networkType: "other" });
        }

        try {
          that.aes.use([AESPluginPV, AESPluginAutolog]);
          // that.props.initQueryMeta()
          that.props.getPermission();
          that.props.getCities();
          that.props.getDeliveryData();
          that.loadData();
          that.monitorUserOperations();
          getAllMenu().then((allMenuData) => {
            that.setState({ allMenu: allMenuData?.subMenus || [] });
          });
        } catch (error) {
          api.onRequestError(error);
        }

        try {
          api.getBasePoolList().then(resp => {
            const POOL_TYPE_ITEM = 1;
            const POOL_TYPE_STORE = 2;
            const sourcePoolList = resp.data?.data?.data || [];
            const itemPoolList = sourcePoolList
              .filter((p) => p.basePoolType === POOL_TYPE_ITEM)
              .map((p) => {
                return {
                  id: p.basePoolId,
                  title: p.basePoolName,
                  tips: p.basePoolDesc,
                  type: p.poolEntryType,
                };
              });
            const storePoolList = sourcePoolList
              .filter((p) => p.basePoolType === POOL_TYPE_STORE)
              .map((p) => {
                return {
                  id: p.basePoolId,
                  title: p.basePoolName,
                  tips: p.basePoolDesc,
                  type: p.poolEntryType,
                };
              });
            __dangerous_updateStoreAndPoolEnum__(
              storePoolList,
              itemPoolList,
              (e) => {
                console.error(e);
                try {
                  exlog.logJsError(e);
                } catch(err) {
                  console.error(err);
                }
              }
            );
            that.setState({ poolPageEnumInfoReady: true });
          }).catch(error => {
            console.error(error)
            api.onRequestError(error)
          })
          api.getBucUser().then((resp) => {
            const data = resp.data.data || {};
            aesInstall(data.name || data.lastName, data.empId);
            exlog.updateConfig({ userid: data.empId });
            postSetupCallback(data.empId);
          });
        } catch (error) {
          api.onRequestError(error);
        }
      } catch (error) {
        try {
          tracker &&
            tracker.log({
              code: "public_network",
              msg: "选投平台公网访问",
              c1: error.msg || error.message || JSON.stringify(error),
              sampleRate: 1.0, // 目前采样率为 100.00%
            });
        } catch (trackerError) {
          console.log("trackerError=======埋点报错", trackerError);
        }
        // 接口超时也会被 catch
        that.setState({ networkType: "other" });
        api.onRequestError(error);
      }
    }

    checkNetworkType((empId) => {
      // 进行云鼎登录态检查，如果登录态不一致，会强制重新登录
      yundingClient
        .fetch({
          apiKey: "alsc-market-web.SessionQueryService.getLoginUser",
        })
        .then((res) => {
          const key = "__selection_mock_buc_not_match__";
          const mockNotMatch = localStorage.getItem(key) === "true";
          localStorage.removeItem(key);

          const isNotMatch = empId !== res.data.workId || mockNotMatch;
          exlog.logCustomEvent({
            code: "login",
            message: `check yunding login state: ${isNotMatch ? "not-match" : "match"}`,
            c1: empId,
            c2: res.data.workId,
          });
          debug("check yunding login state", { res, empId, isNotMatch });
          if (isNotMatch) {
            // 登录态不一致，中断用户正常操作，强制用户重新完成登录
            Dialog.alert({
              title: "提示",
              content: "系统检测到您的登录状态异常，请重新登录",
              closeable: false,
              okProps: {
                children: "重新登录",
              },
              onOk: () => {
                // 如果云鼎和选投后端BUC登录态不一致，这里强制登出云鼎重新登入，不修改选投后端 BUC 登录态
                const url = `https://agw-internet.alsc.alibaba-inc.com/auth/buc/logout?targetUrl=${encodeURIComponent(window.location.href)}`;
                window.location.replace(url);
              },
            });
          }
        })
        .catch((e) => {
          debug(e);
          let yundingNotLogin = false;
          if (e.name === ErrorName.GatewayError) {
            const { gwResultCode } = e.result;
            if (gwResultCode === GatewayResultCode.UNLOGIN) {
              yundingNotLogin = true;
            }
          }
          if (yundingNotLogin) {
            exlog.logCustomEvent({
              code: "login",
              message: "check yunding login state: goto login",
            });
            debug("goto yunding login");
            const url = `https://agw-internet.alsc.alibaba-inc.com/auth/buc/login?targetUrl=${encodeURIComponent(window.location.href)}`;
            window.location.replace(url);
          } else {
            exlog.logCustomEvent({
              code: "login",
              message:
                "check yunding login state: failed, " + (e?.message || "-"),
            });
            debug("check yunding login state: failed");
            const retry =
              localStorage.getItem(
                "__selection_retry_check_yunding_state__",
              ) !== "false";
            if (retry) {
              // 通过刷新页面来重新检测云鼎登录态
              window.location.reload();
            }
          }
        });
    });
  }

  loadData = () => {
    try {
      api.getAnnouncement({ platform: "pickerone", limit: 5 }).then((res) => {
        this.setState({ announcementData: res.data });
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  monitorUserOperations = () => {
    localStorage.setItem("last_Static_Operate_Time", Date.now());
    const EXPIRE_IN_1Day = 1000 * 60 * 60 * 24; // 1天
    function recordStaticOperation() {
      localStorage.setItem("last_Static_Operate_Time", Date.now());
    }
    // 监听用户的本地操作，并更新最后一次操作的时间 + 开始计时！
    document.addEventListener("mousedown", recordStaticOperation, true);
    document.addEventListener("keydown", recordStaticOperation, true);
    // 每过29min就判断一次是否需要登出了
    setIntervalSearch = setInterval(() => {
      let now_Time = Date.now();
      let last_Static_Operate_Time = localStorage.getItem(
        "last_Static_Operate_Time",
      );
      if (now_Time - last_Static_Operate_Time - EXPIRE_IN_1Day >= 0) {
        this.setState({ visible: true });
        setIntervalCountdown = setInterval(() => {
          if (+this.state.countdown !== 0) {
            this.setState({ countdown: this.state.countdown - 1 });
          } else {
            this.setState({ visible: false });
            // 关掉事件监听+关掉定时器
            document.removeEventListener(
              "mousedown",
              recordStaticOperation,
              true,
            );
            document.removeEventListener(
              "keydown",
              recordStaticOperation,
              true,
            );
            clearInterval(setIntervalSearch);
            clearInterval(setIntervalCountdown);
            localStorage.setItem("last_Static_Operate_Time", Date.now());
            window.location.reload(true);
          }
        }, 1000);
      }
    }, EXPIRE_IN_1Day / 1440);
  };

  resolveMenuWithPermissionInfo = (allMenu, availableMenu) => {
    if (!availableMenu?.length) {
      return allMenu;
    }
    const result = allMenu.map((menuItem) => {
      let ret = menuItem;
      const matchMenuItem = availableMenu.find(
        (_menuItem) => _menuItem.menuId === menuItem.menuId,
      );
      if (!matchMenuItem) {
        ret = { ...ret, hasPermission: false };
        return ret;
      }
      ret = { ...ret, hasPermission: true };
      if (ret.subMenus) {
        ret = {
          ...ret,
          subMenus: this.resolveMenuWithPermissionInfo(
            ret.subMenus,
            matchMenuItem.subMenus || [],
          ),
        };
      }
      return ret;
    });

    debug("resolveMenuWithPermissionInfo result:", result);
    return result;
  };

  render() {
    const { permission } = this.props;
    const { announcementData, networkType, allMenu, poolPageEnumInfoReady } = this.state;

    if (networkType === "other") {
      return (
        <h2
          style={{
            display: "flex",
            flex: 1,
            minHeight: "50%",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          暂不支持公网访问，请链接内网进行访问
        </h2>
      );
    }

    if (!allMenu || !permission || !poolPageEnumInfoReady) {
      return <></>;
    }

    const menuWithPermissionInfo = this.resolveMenuWithPermissionInfo(
      allMenu,
      permission,
    );
    debug("allMenu", allMenu, "menuWithPermissionInfo", menuWithPermissionInfo);

    return (
      <div className="app-wrapper">
        <Header
          subTitle="选投平台"
          appId="XTPT-v2"
          logoutUrl={config.LOGOUT_URL}
          onApplyPermission={() => {
            window.location.hash = "#/permissionApply";
          }}
        />
        <div className="main-content">
          <Sider
            data={createMenuData(menuWithPermissionInfo)}
            permissionCheckingEnabled
            onPermissionDenied={(menuItem) => {
              debug("permission denied:", menuItem);
              let permissionNames = null;
              visitMenu(allMenu, (item, parents) => {
                if (item.menuId === menuItem.originMenuId) {
                  permissionNames = Array.from(
                    // 每个菜单项上挂载的权限点可能重复，这里需要去重
                    new Set(
                      [
                        ...parents.map((p) => p.permissionName),
                        item.permissionName,
                      ].filter(Boolean),
                    ),
                  );
                  return true;
                }
              });
              debug("permissionNames to apply", permissionNames);
              if (permissionNames?.length) {
                const params = {
                  p: permissionNames.join(","),
                  redirect_url: window.location.href,
                };
                window.location.hash = `#/permissionApply/deny?${qs.stringify(params)}`;
              } else {
                Message.error("暂无权限");
              }
            }}
            skipAutoUpdateOnLocationChange
          />
          <div
            className={`right-part ${
              announcementData && announcementData.length > 0
                ? "has-announcement"
                : "no-announcement"
            }`}
          >
            <AnnouncementPage announcementData={announcementData} />
            <div className="part-content">
              <Switch>{createReactRoutes(permission)}</Switch>
            </div>
          </div>
        </div>
        <Dialog
          visible={this.state.visible}
          closeable={false}
          footerActions={["ok"]}
          locale={{
            ok: "取消",
          }}
          title={`由于您当前长时间未操作，系统将在${this.state.countdown}秒后刷新以请求最新资源`}
          onOk={() => {
            clearInterval(setIntervalCountdown);
            this.setState({ visible: false, countdown: 5 });
          }}
        ></Dialog>
      </div>
    );
  }
}
