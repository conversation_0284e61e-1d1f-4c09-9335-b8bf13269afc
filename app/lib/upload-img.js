// import MtopH5 from '@ali/mtop-core/build/MtopH5'
import { decorationReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

// const mtop = new MtopH5({
//   prefix: 'acs',
//   subDomain: 'm',
//   mainDomain: 'taobao.com',
// })

export default function uploadImg(base64) {
  return mtopApi(base64)
    .then((url) => {
      return new Promise((r) => {
        const img = document.createElement('img')
        img.onload = () => {
          r(url.data)
        }
        img.onerror = () => {
          r(new Error('图片上传失败'))
        }
        img.src = url.data
      })
    })
}

function mtopApi(base64) {
  base64 = base64.replace(/^data\:.*?;base64,/, '')
  // return mtop.request({
  //   api: 'mtop.promotion.PicService.uploadPic',
  //   v: '1.0',
  //   type: 'POST',
  //   noUserInfo: true,
  //   data: {
  //     base64,
  //     name: 'nr-img.png',
  //   },
  // })
  return decorationReq.post('/api/pic/uploadPic', {
    userId: '1',
    base64,
    name: 'nr-img.png'
  })
    .then((result) => {
      console.log(result);
      return result.data.data
    })
}

function iframeFuss(base64) {
  require('./origin-bridge-client')
  return window.TOOL_ORIGIN_BRIDGE.fuss(base64)
}

function directFuss(base64) {
  return new Promise((resolve, reject) => {
    fetch(base64)
      .then((f) => f.blob())
      .then((blob) => {
        const send = new FormData()
        send.append('file', blob, 'nr-image.png')
        const xhr = new XMLHttpRequest()
        xhr.open('POST', 'https://httpizza.ele.me/common/fuss/upload')
        let timedout = false
        const timeout = setTimeout(() => {
          timedout = true
          reject(new Error('上传超时'))
        }, 10000)
        xhr.withCredentials = true
        xhr.onload = () => {
          if (timedout) {
            return
          }
          clearTimeout(timeout)
          const resp = JSON.parse(xhr.responseText)
          if (resp.name === 'UNAUTHORIZED') {
            reject(new Error('请登录饿了么sso'))
          } else if (resp.message) {
            reject(new Error(resp.message))
          } else {
            resolve(resp.url.replace('fuss10', 'cube'))
          }
        }
        xhr.onerror = () => {
          clearTimeout(timeout)
          reject(new Error('图片上传失败'))
        }
        xhr.send(send)
      })
  })
}
