let userInfo

export const getUserInfo = () => userInfo

export const interceptUserinfo = () => new Promise((resolve) => {
  const oXMLHttpRequest = window.XMLHttpRequest
  window.XMLHttpRequest = class extends oXMLHttpRequest {
    constructor () {
      super()
    }
    open (method, url) {
      if (!userInfo && url.indexOf('/user/info') !== -1) {
        this.addEventListener('load', () => {
          userInfo = JSON.parse(this.responseText).data
          resolve(userInfo)
        })
      }
      return oXMLHttpRequest.prototype.open.call(this, method, url)
    }
  }
})
