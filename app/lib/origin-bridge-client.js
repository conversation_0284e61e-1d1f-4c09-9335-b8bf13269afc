window.TOOL_ORIGIN_BRIDGE = {}

!((exports) => {
  let requestIdMax = 1
  const requestIdPool = []
  const methods = ['fetch', 'setLocalStore', 'getLocalStore', 'fuss']
  let iframe
  if (true) {
    iframe = document.createElement('iframe')
    if (window.IS_DEVELOPMENT || window.IS_DEVELOPMENT_INDEX) {
      //iframe.src = 'https://h5-vip.ele.me/newretail/tool/origin-bridge.inline.js.html'
      iframe.src = 'https://h5.ele.me/newretail/tool/origin-bridge.inline.js.html'
    } else {
      iframe.src = 'https://h5.ele.me/newretail/tool/origin-bridge.inline.js.html'
    }
    iframe.src = 'https://newretail-tool.faas.ar.elenet.me/origin-bridge.inline.js.html'
    iframe.style.position = 'absolute'
    iframe.style.top = '-10px'
    iframe.style.left = '-10px'
    iframe.style.width = '1px'
    iframe.style.height = '1px'
    if (document.body) {
      if (document.readyState === 'complete') {
        document.body.appendChild(iframe)
      } else {
        window.addEventListener('load', () => {
          document.body.appendChild(iframe)
        })
      }
    }
  }
  let setReady
  const ready = new Promise(r => {
    setReady = r
  })
  const handlers = {}
  window.addEventListener('message', event => {
    const { data } = event
    if (typeof data === 'object' && data && data.action) {
      const { action, id, result, error } = data
      if (action === 'ORIGIN-BRIDGE-RESPONSE') {
        const handler = handlers[id]
        if (error) {
          handler.reject(error)
        } else {
          handler.accept(result)
        }
        requestIdPool.push(id)
      } else if (action === 'ORIGIN-BRIDGE-READY') {
        setReady()
      }
    }
  })
  function callBridge (...args) {
    const action = this
    const id = requestIdPool.pop() || requestIdMax++
    return new Promise((accept, reject) => {
      handlers[id] = { accept, reject }
      ready.then(() => {
        iframe.contentWindow.postMessage({ action, args, id }, '*')
      })
    })
  }

  methods.forEach((fn) => {
    exports[fn] = callBridge.bind(fn)
  })
})(window.TOOL_ORIGIN_BRIDGE)

