import { decorationReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'

export default function uploadFile (base64,options) {
  return mtopApi(base64,options);
}

function mtopApi (base64,options) {
  base64 = base64.replace(/^data\:.*?;base64,/, '');
  return decorationReq.post('/api/whitelist/uploadFile', {
    base64,
    fileType: options.fileType,
    fileName: options.fileName,
    activityId: options.activityId,
    // userId: info.userid,
    // nickName: info.cname,
    // userName: info.name,
  }).then((result) => {
    return result.data.data
  })
}

function iframeFuss (base64) {
  require('./origin-bridge-client')
  return window.TOOL_ORIGIN_BRIDGE.fuss(base64)
}

function directFuss (base64) {
  return new Promise((resolve, reject) => {
    fetch(base64)
      .then((f) => f.blob())
      .then((blob) => {
        const send = new FormData()
        send.append('file', blob, 'nr-image.png')
        const xhr = new XMLHttpRequest()
        xhr.open('POST', 'https://httpizza.ele.me/common/fuss/upload')
        let timedout = false
        const timeout = setTimeout(() => {
          timedout = true
          reject(new Error('上传超时'))
        }, 10000)
        xhr.withCredentials = true
        xhr.onload = () => {
          if (timedout) {
            return
          }
          clearTimeout(timeout)
          const resp = JSON.parse(xhr.responseText)
          if (resp.name === 'UNAUTHORIZED') {
            reject(new Error('请登录饿了么sso'))
          } else if (resp.message) {
            reject(new Error(resp.message))
          } else {
            resolve(resp.url.replace('fuss10', 'cube'))
          }
        }
        xhr.onerror = () => {
          clearTimeout(timeout)
          reject(new Error('图片上传失败'))
        }
        xhr.send(send)
      })
  })
}
