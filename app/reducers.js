/**
 * Combine all reducers in this file and export the combined reducers.
 */

import { combineReducers } from 'redux-immutable';
import { connectRouter } from 'connected-react-router/immutable'
import globalReducer from '@/containers/App/reducer';
import { defaultHistory } from '@/history';

/**
 * Creates the main reducer with the dynamically injected ones
 */
export default function createReducer(injectedReducers, history = defaultHistory) {
  return combineReducers({
    router: connectRouter(history),
    global: globalReducer,
    ...injectedReducers,
  })
}
