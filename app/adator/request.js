import axios from 'axios'
import { config } from '@/config';
import { goToLogin } from './auth';
var qs = require('qs');

/**
 * Parses the JSON returned by a network request
 *
 * @param  {object} response A response from a network request
 *
 * @return {object}          The parsed JSON from the request
 */
function parseJSON(response) {
  if (response.status === 204 || response.status === 205) {
    return null;
  }
  return response.json();
}

/**
 * Checks if a network request came back fine, and throws an error if not
 *
 * @param  {object} response   A response from a network request
 *
 * @return {object|undefined} Returns either the response, or throws an error
 */
function checkStatus(response) {
  if (response.status >= 200 && response.status < 300) {
    return response;
  }

  const error = new Error(response.statusText);
  error.response = response;
  throw error;
}

/**
 * Requests a URL, returning a promise
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 *
 * @return {object}           The response data
 */
export function jsonRequest(url, options) {
  return fetch(url, options)
    .then(checkStatus)
    .then(parseJSON);
}

export function createPrefixJsonRequest(prefix){
  return (path, option) => fetch(`${prefix}/${path}`, option).then(checkStatus).then(parseJSON)
}


export const request = ({
  baseURL = config.API_ROOT,
  withCredentials = true,
  headers,
  timeout = 600000
} = {}) => {  let instance = axios.create({
  baseURL,
  timeout,
  headers: {
    // 'X-Login-Auto-Redirection': false,
    ...headers
  },
  withCredentials,
  paramsSerializer(params) {
    return qs.stringify(params, {arrayFormat: 'repeat'})
  },
})

instance.interceptors.request.use(
  config => {
    // Do something before request is sent
    return config
  },
  error => {
    // Do something with request error
    return Promise.reject(error)
  }
)
instance.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response) {
      if (+error.response.status === 401) {
        return goToLogin();
      }
    }
    return Promise.reject(error);
  }
)

return instance
}

/**投放规则请求实例 */
export const putInReq = request({ baseURL: config.PUTIN_API_ROOT });
export const decorationReq = request({ baseURL: config.DECORATION_API_ROOT });
export const sceneReq = request({ baseURL: config.SCENE_ROOT });


