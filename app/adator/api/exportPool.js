import { request } from '../request';
import { onRequestSuccess } from './req.trans'

const requestInstance = request()

/**获取场景分类 */
export function querySceneList(type) {
  return requestInstance.get(`/api/v3/scene/getSceneCatTree?poolEntryType=${type}`).then(onRequestSuccess)
}

/**获取场景池*/
export function querySceneListByGroupName({sceneGroup,poolEntryType}) {
  return requestInstance.get(`/api/v3/scene/getScenePoolList?sceneGroup=${sceneGroup}&poolEntryType=${poolEntryType}`).then(onRequestSuccess)
}

/**通过场景池id查询场景数据*/
export function getSceneInfoById(params) {
  return requestInstance.get('/api/v2/scene/getSceneInfoById/'+ params).then(onRequestSuccess)
}

/**获取二级场景分类树*/
export function getSceneCatTree() {
  return requestInstance.get('/api/v3/scene/getSceneCatTree').then(onRequestSuccess)
}

/**根据底池数据源下发圈选指标 */
export function getRulesBySource(params) {
  return requestInstance.get('/api/v3/scene/getRulesBySource?ruleSourceCode='+params).then(onRequestSuccess)
}

/**根据底池数据源下发自定义维度指标 */
export function getCustomRulesBySource(params) {
  return requestInstance.get('/api/v3/scene/getCustomRulesBySource?ruleSourceCode='+params).then(onRequestSuccess)
}

/**底池规则预览&查询*/
export function queryPageSceneList(params) {
  return requestInstance.post(`/api/v3/scene/pageSceneList`, params)
}

/**底池规则预览&查询*/
export function querySceneCatCount(params) {
  return requestInstance.post(`/api/v3/scene/getSceneCatCount`, params)
}

/**品结果集预览*/
export function queryItemViewList(params) {
  return requestInstance.post(`/api/v3/scene/queryItemViewList`, params)
}

/**专家池保存*/
export function createScene(params) {
  return requestInstance.post(`/api/v3/scene/createScene`, params)
}

/**专家池发布*/
export function publishScenePool(sceneBaseId) {
  return requestInstance.get(`/api/v3/scene/publishScenePool?sceneBaseId=${sceneBaseId}`)
}

/**专家池重新发布*/
export function republishScenePool(sceneBaseId) {
  return requestInstance.get(`/api/v3/scene/republishScenePool?sceneBaseId=${sceneBaseId}`)
}

/**专家池编辑*/
export function updateScene(params) {
  return requestInstance.post(`/api/v3/scene/updateScene`, params)
}

/**专家池下线*/
export function offlineScenePool(sceneBaseId) {
  return requestInstance.get(`/api/v3/scene/offlineScenePool?sceneBaseId=${sceneBaseId}`)
}

/**专家池列表&查询*/
export function pageScenePoolList(params) {
  return requestInstance.post(`/api/v3/scene/pageScenePoolList`, params)
}

/**专家池详情信息*/
export function getScenePoolDetailInfo({sceneBaseId, poolEntryType}) {
  return requestInstance.get(`/api/v3/scene/getScenePoolDetailInfo?sceneBaseId=${sceneBaseId}&poolEntryType=${poolEntryType}`)
}

/**场景名称下专家池列表接口*/
export function getScenePoolList(params) {
  return requestInstance.post(`/api/v3/scene/getScenePoolList`, params)
}

/**tgi预览删除or批量删除*/
export function sceneManualUpdate(params) {
  return requestInstance.post(`/api/v3/scene/sceneManualUpdate`, params)
}

/**获取删除品类列表*/
export function querySceneManual(params) {
  return requestInstance.post(`/api/v3/scene/querySceneManual`, params)
}

/**获取当前品类列表*/
export function pageQuerySceneResultList(params) {
  return requestInstance.post(`/api/v3/scene/pageQuerySceneResultList`, params)
}

/**获取动态数据源*/
export function getDynamicDataSource(url) {
  return requestInstance.get(`${url}`)
}

/**获取动态数据源*/
export function getDynamicDataSourceWithParams(url, params = {}) {
  return requestInstance.post(`${url}`, params).then(onRequestSuccess)
}

/**获取专家池列表*/
export function getSceneExpertPoolList(params) {
  return requestInstance.post(`/api/v3/scene/getSceneExpertPoolList`, params)  
}

/**专家池商品列表查询*/
export function querySceneItemPoolDetail(params) {
  return requestInstance.post(`/api/v2/sceneBasePoolResult/queryItemPoolDetail`, params)
}

/**专家池黑白名单操作*/
export function querySceneManualUpdate(params) {
  return requestInstance.post(`/api/v2/sceneBasePoolResult/manualUpdate`, params)
}

/**专家池黑白名单查询*/
export function querySceneItemManualPoolDetail(params) {
  return requestInstance.post(`/api/v2/sceneBasePoolResult/queryItemManualPoolDetail`, params)
}
