
import {request} from '../request';
import { onRequestSuccess } from './req.trans'

const requestInstance = request()

export function getCityTop10List(params) {
  return requestInstance.post('/api/pick/analyse/getCityTop10List',params).then(onRequestSuccess)
}

export function getCategoryDistribution(params) {
  return requestInstance.post('/api/pick/analyse/getCategoryDistribution',params).then(onRequestSuccess)
}

// 城市分析表格
export function getPageQueryCityList(params) {
  return requestInstance.post('/api/pick/analyse/pageQueryCityList',params).then(onRequestSuccess)
}

// 网格分析表格
export function getPageQueryGridList(params) {
  return requestInstance.post('/api/pick/analyse/pageQueryGridList',params).then(onRequestSuccess)
}

export function getCategoryDistributionDetail(params) {
  return requestInstance.post('/api/pick/analyse/getCategoryDistributionDetail',params).then(onRequestSuccess)
}

export function getAllGridList(params) {
  return requestInstance.post('/api/pick/analyse/getAllGridList',params).then(onRequestSuccess)
}

export function getGridMap(params) {
  return requestInstance.post(`/api/pick/analyse/getGridMap`,params).then(onRequestSuccess)
}

export function getGridAnalyse(params) {
  return requestInstance.post(`/api/pick/analyse/getGridAnalyse`,params).then(onRequestSuccess)
}

export function getAllCityList(poolId) {
  return requestInstance.get(`/api/pick/analyse/getAllCityList?poolId=${poolId}`).then(onRequestSuccess)
}

export function getPickPieAnalyse(params) {
  return requestInstance.post(`/api/pick/analyse/getPickPieAnalyse`,params).then(onRequestSuccess)
}

/*获取当前数据更新时间*/
export function getPickAnalyseDataVersion() {
  return requestInstance.get(`/api/pick/analyse/getPickAnalyseDataVersion`).then(onRequestSuccess)
}



