import { putInReq } from '../request';
import { onRequestSuccess, onRequestError } from './req.trans'

//获取城市接口
export function createSceneCard(params) {
  return putInReq.post('/api/sceneManager/editSceneCard',params).then(onRequestSuccess)
}

export function getSceneListCard(params) {
  return putInReq.post('/api/sceneManager/listSceneCard',params)
}

export function getDetailSceneCard(params) {
  return putInReq.post('/api/sceneManager/getSceneCard',params).then(onRequestSuccess)
}

export function deleteSceneCard(params) {
  return putInReq.post('/api/sceneManager/delSceneCard',params).then(onRequestSuccess)
}

