
import { Message } from '@alife/next';
import { env, ENV_ENUM } from '@/config';
import { goToLogin } from '../auth';

export const onRequestSuccess = response => {
  const { data = {} } = response;
  if (!data) return Promise.resolve(null); // could be null here, contain no response.data
  const err = data.errors && data.errors[0] ||  {};
  if (+err.code === 302) {
    return Promise.reject(goToLogin(encodeURI(window.location.href)));
  }
  if (+data.code === 200) {
    return Promise.resolve(data.data);
  } else {
    return Promise.reject(data);
  }
};

export const onRequestError = error => {
  if (env === ENV_ENUM.localdev) console.error(error)
  let msg;
  const resp = error.errors && error.errors[0];
  if (resp && resp.code === '302') {
    return Promise.reject(goToLogin(location.href));
  } else if (error.response) {
    const response = error.response && error.response.data;
    msg = (response && response.msg) || response.statusText;
  } else {
    msg = error.msg ? error.msg : '网络请求出错请稍后再试';
  }
  Message.show({
    type: 'warning',
    content: msg
  });
};
