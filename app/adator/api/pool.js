
import { request } from '../request';
import { onRequestSuccess } from './req.trans'
// import { safeJSONParse, omit } from '../others';

const requestInstance = request()

/*start 入淘的接口*/

/*管理商品池-商品池列表*/
export function getPoolList(params) {
  let _request = '/api/v2/pool/getPoolList';
  return requestInstance.post(_request, params).then(onRequestSuccess)
}

/*全部类目*/
export function getSkuCategory() {
  return requestInstance.get(`/api/v2/config/queryAllSkuCategory`).then(onRequestSuccess)
}

// 新的主营类目
export function getNewAllStoreMainCategory() {
  return requestInstance.get('/api/v2/config/queryNewAllStoreMainCategory').then(onRequestSuccess)
}

// 查询商品状态
export function getItemStateEnumList() {
  return requestInstance.get('/api/v2/config/queryItemStateEnum').then(onRequestSuccess)
}

// 查询删除原因列表
export function getDelReasonList() {
  return requestInstance.get('api/v2/config/listDelReason').then(onRequestSuccess)
}

// 删除池子
export function deletePool(poolId) {
  return requestInstance.get(`/api/v2/pool/deletePool/${poolId}`).then(onRequestSuccess)
}

// 创建池子
export function createPool(params) {
  return requestInstance.post(`/api/v2/pool/createPool`, params)
}

// 编辑池子
export function updatePool(params) {
  return requestInstance.post(`/api/v2/pool/updatePool`,params)
}

/**
 * 校验池子：
 * @params params: 与 updatePool 一致
 * @return {
 *  warnInfoList: [ "当前选品池结果集上线1000，已超出上线850809" ],
 *  result: 1 // 0 校验通过，1 需要二次确认， 2不允许创建
 * }
 * */
export function checkpublish(params) {
  return requestInstance.post(`/api/v2/pool/checkpublish`,params).then(onRequestSuccess)
}

// 发布池子
export function publishPool(poolId) {
  return requestInstance.get(`/api/v2/pool/publishPool/${poolId}`).then(onRequestSuccess)
}

// 重新发布池子
export function rePublishPool(poolId) {
  return requestInstance.get(`/api/v2/pool/republishPool/${poolId}`).then(onRequestSuccess)
}

// 批量删除商品
export function manualUpdate(params) {
  return requestInstance.post(`/api/v2/poolResult/manualUpdate`, params).then(onRequestSuccess)
}

// 根据poolId查询详情
export function getPoolDetailByPoolId(poolId) {
  return requestInstance.get(`/api/v2/pool/getPoolDetailByPoolId/${poolId}`).then(onRequestSuccess)
}

// 下线池子
export function offLinePool(poolId) {
  return requestInstance.get(`/api/v2/pool/offLinePool/${poolId}`).then(onRequestSuccess)
}

// 批量下线池子
export function batchOffLinePool(params) {
  return requestInstance.post(`api/v2/pool/batchOffLinePool`, params).then(onRequestSuccess)
}

// 结果集预览
export function queryPoolViewList(params) {
  return requestInstance.post(`/api/v2/poolResult/queryItemViewList`, params).then(onRequestSuccess)
}

// 生成多维度分析/数据诊断（实时） FBI 链接
export function submitAnalyseTask(params) {
  return requestInstance.post(`/api/v2/poolResult/submitAnalyseTask`, params).then(onRequestSuccess)
}

// 批量删除商品 —— 品池详情 -> 删除商品Tab -> 批量删除商品（其实就是黑名单）
export function manualUpdateByFile(params) {
  return requestInstance.post(`/api/v2/poolResult/manualUpdateByFile`, params).then(onRequestSuccess)
}

// 新建门店池子结果集预览
export function getQueryStorePoolResultViewList(params) {
  return requestInstance.post(`/api/v2/poolResult/queryStoreViewList`, params).then(onRequestSuccess)
}

// 新建以品选店门店池子结果集预览
export function getNewQueryStorePoolResultViewList(params) {
  return requestInstance.post(`/api/v2/poolResult/queryItem2StoreViewList`, params).then(onRequestSuccess)
}

// 新建门店结果集预览删除商品列表
export function queryShopDeleteList(tempPoolId) {
  return requestInstance.get(`/api/v2/poolResult/manual/query/store/${tempPoolId}`).then(onRequestSuccess)
}

// 结果集预览删除商品列表
export function queryDeleteList(tempPoolId) {
  return requestInstance.get(`/api/v2/poolResult/manualUpdate/query/${tempPoolId}`).then(onRequestSuccess)
}

// 获取临时poolId
export function getTempPoolId() {
  return requestInstance.get(`/api/v2/pool/getTempPoolId`).then(onRequestSuccess)
}

// 内页查询-初选商品
export function queryItemPoolDetailGoodList(params) {
  return requestInstance.post(`/api/v2/poolResult/queryItemPoolDetail`,params).then(onRequestSuccess)
}

// 内页查询-初选门店
export function queryStorePoolDetail(params) {
  return requestInstance.post(`/api/v2/poolResult/queryStorePoolDetail`,params).then(onRequestSuccess)
}

// 内页查询-黑白名单（删除黑白单 2 , addOrDelete：1）
export function queryItemManualPoolDetail(params) {
  return requestInstance.post(`/api/v2/poolResult/queryItemManualPoolDetail`,params).then(onRequestSuccess)
}

// 选店内页查询-黑白名单（删除黑白单 2 , addOrDelete：1）
export function queryStoreManualPoolDetail(params) {
  return requestInstance.post(`/api/v2/poolResult/queryStoreManualPoolDetail`,params).then(onRequestSuccess)
}

// 内页查询-失败记录（删除黑白单 2 , addOrDelete：1）
export function queryFailedItemManualPoolDetail(params) {
  return requestInstance.post(`/api/v2/poolResult/queryFailedItemManualPoolDetail`,params).then(onRequestSuccess)
}

// 选店内页查询-失败记录（删除黑白单 2 , addOrDelete：1）
export function queryFailedStoreManualPoolDetail(params) {
  return requestInstance.post(`/api/v2/poolResult/queryFailedStoreManualPoolDetail`,params).then(onRequestSuccess)
}

/*门店类目*/
export function getCategoryStore() {
  return requestInstance.get(`/api/ali/store/queryAllStoreCategory`)
}

/*商品活动 */
export function queryMarketingType(sourcePoolId) {
  return requestInstance.get(`/api/v2/config/queryMarketingType/${sourcePoolId}`)
}

/*获取品牌名数据* */
export function queryBrand(params) {
  return requestInstance.post(`/api/common/findBrandByNameFuzzy`,params)
}

/*所在城市*/
export function getCities() {
  return requestInstance.get('/api/common/queryCityInfo')
}

/*用户信息*/
export function getBucUser() {
  return requestInstance.get('/api/v2/config/getBucUser')
}

/*用户信息*/
export function updatePoolNotice(params) {
  return requestInstance.post('/api/v2/pool/updatePoolNotice',params)
}

/*适用场景*/
export function synPoolPlatformList(sourcePoolId) {
  return requestInstance.get(`/api/v2/config/synPoolPlatformList/${sourcePoolId}`)
}
/* new 适用场景*/
export function newSynPoolPlatformList(sourcePoolId) {
  return requestInstance.get(`/api/v2/config/synPoolPlatformListWithLevel/${sourcePoolId}`)
}

/*文件上传适用场景*/
export function synPoolPlatformList1(sourcePoolId) {
  return requestInstance.get(`/api/common/synPoolPlatformList1/${sourcePoolId}`)
}

/*查询选品集池子信息*/
export function getBasePoolList() {
  return requestInstance.get(`api/v2/pool/getBasePoolList/4`)
}

/*获取选品池使用场景*/
export function getPoolUsage(poolId) {
  return requestInstance.get(`/api/analyse/pool/usage/${poolId}`)
}

/*获取新场景分类*/
export function queryNewSceneCatTree() {
  return requestInstance.get(`/api/v2/config/querySceneCatTree`)
}

/*获取无用池子列表*/
export function queryCleanPoolList(params) {
  return requestInstance.post(`/api/v2/pool/clean/query`,params)
}

/*处理无用池子*/
export function getCleanDeal(params) {
  return requestInstance.post(`/api/v2/pool/clean/deal`,params)
}

/*数据洞察上报*/
export function queryReportDataAnalysisPool(params) {
  return requestInstance.post(`/api/pick/analyse/reportDataAnalysisPool`,params)
}
