import { request } from '../request';
import { onRequestSuccess } from './req.trans'

const requestInstance = request()

/**获取场景列表*/
export function getSceneRuleSourceList() {
  return requestInstance.get(`/api/manual/rule/getSceneRuleSourceList`)
}

/**获取场景规则列表*/
export function getRuleListByRuleSource(params) {
  return requestInstance.post(`/api/manual/rule/getRuleListByRuleSource`, params)
}
  
/**新增场景规则*/
export function addRuleForRuleSource(params) {
  return requestInstance.post(`/api/manual/rule/addRuleForRuleSource`, params)
}

/**更新场景规则*/
export function updateRuleByRuleSource(params) {
  return requestInstance.post(`/api/manual/rule/updateRuleByRuleSource`, params)
}

/**删除场景规则*/
export function delRuleByRuleSource(params) {
  return requestInstance.post(`/api/manual/rule/delRuleByRuleSource`, params)
}