import {putInReq, request} from '../request';
import { onRequestSuccess, onRequestError } from './req.trans'

export { onRequestSuccess, onRequestError }
export * from './auth'
export * from './pool'
export * from './exportPool'
export * from './permission'
export * from './analysis'
export * from './manual'
export * from './scene'
export * from '../../utils/api/rankManage'

const requestInstance = request()

//获取城市接口
export function getCityList() {
  return putInReq.get('/api/common/cityInfo').then(onRequestSuccess)
}

//获取城市城市对应网格
export function getGridList({ cityId }) {
  return putInReq.get(`/api/putInActivity/listGridByCityId/${cityId}`).then(onRequestSuccess)
}

//根据搜索词搜索 省、市、网格
export function getSearchList({ key }) {
  return putInReq.get(`/api/putInActivity/getGridDetailByKey/${key}`).then(onRequestSuccess)
}

/** 根据poolId获取维度 poolId*/
export function getPoolType(params) {
  return putInReq.post('/api/common/poolType', params).then(onRequestSuccess)
}

/**获取门店分类 */
export function getStoreCategory() {
  return putInReq.get('/api/common/storeCategory').then(onRequestSuccess)
}

/**获取所有投放渠道 */
export function getAllDeliveryChannel() {
  return putInReq.get('/api/pageManage/queryAllDeliveryChannel').then(onRequestSuccess)
}

/**获取权限 */
export function getPermission() {
  return putInReq.get('/api/v1/acl/menuTree.json').then(onRequestSuccess)
}


/**查询商品状态枚举信息 */
export function getItemStateEnumList() {
  return requestInstance.get('/api/v2/config/queryItemStateEnum').then(onRequestSuccess)
}

/**公告  */
export function getAnnouncement({ platform,limit }) {
  return requestInstance.get(`/api/v2/platform/announcement?platform=${platform}&limit=${limit}`).then(onRequestSuccess)
}

/**校验是否有选择场景的权限  */
export function validateComponentsPermission(params) {
  return requestInstance.post(`/api/acl/validateComponentsPermission`,params)
}

/**导出选品选店集  */
export function exportExcel(params) {
  return requestInstance.post(`/api/v2/export/pool`,params)
}

/**获取审核列表  */
export function getAuditList(params) {
  return requestInstance.post(`/api/v2/base/item/emall/audit/list`,params)
}

/**审核操作(打标/去标)  */
export function emallAudit(params) {
  return requestInstance.post(`/api/v2/base/item/emall/audit`, params)
}

/**通过搜索词查询configType类型的数据 */
export function queryDataSourceByKeyWord({configType,...params}) {
  return requestInstance.post(`/api/common/findDataSourceByKeyWordFuzzy/${configType}`, params).then(onRequestSuccess)
}

/**通过roleName查询是否拥有某个权限 */
export function queryRoleValidate(roleName) {
  return requestInstance.get(`/api/acl/roleValidate/${roleName}`).then(onRequestSuccess)
}


/**全能榜单里的投放配置 */
export function queryDeliveryPosition() {
  return putInReq.get(`/api/common/deliveryPosition`).then(onRequestSuccess)
}
