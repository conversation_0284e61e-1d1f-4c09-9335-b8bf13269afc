export const POOL_TYPE = {
  COMMODITY: 1,
  SHOP: 2
}

export const PLACEHODLERS = {
  commaSeperated: '请输入, 多个英文逗号隔开',
  requireInput: '请输入, 必填',
  requireSelection: '请选择'
}

// :start local storage key
export const SSO_TOKEN = 'SSO_TOKEN'


export const RESTART_ON_REMOUNT = '@@saga-injector/restart-on-remount';
export const DAEMON = '@@saga-injector/daemon';
export const ONCE_TILL_UNMOUNT = '@@saga-injector/once-till-unmount';


export const basepoolIds = {
  sku: {
    ALL: '10001',
    ACTIVITY: '10002',
    INVEST: '10003',
    SELL_INVEST: '10005',
    SEASON: '10006',
    BRAND:'10007',
    TOUR: '10009',
    HUOGUO:'10012'
  }
}

export const PAGE_SIZE = 20
