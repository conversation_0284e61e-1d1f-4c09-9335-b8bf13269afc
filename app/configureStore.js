/**
 * Create the store with dynamic reducers
 */

import { createStore, applyMiddleware, compose } from 'redux';
import { fromJS } from 'immutable';
import { routerMiddleware } from 'connected-react-router/immutable'

import createSagaMiddleware from 'redux-saga';
import createReducer from './reducers';
import { defaultHistory } from '@/history';
// import {createLogger} from 'redux-logger'



const sagaMiddleware = createSagaMiddleware();

// const logger = createLogger({
//   stateTransformer: state=> fromJS(state).toJS()
// })

export default function configureStore(initialState = {}, history = defaultHistory) {
  const notProd = process.env.NODE_ENV !== 'production'
  // Create the store with two middlewares
  // 1. sagaMiddleware: Makes redux-sagas work
  // 2. routerMiddleware: Syncs the location/URL path to the state
  const middlewares = [
    sagaMiddleware,
    routerMiddleware(history),
    // ... notProd ? [logger] : []
  ];

  const enhancers = [applyMiddleware(...middlewares)];

  // If Redux DevTools Extension is installed use it, otherwise use Redux compose
  /* eslint-disable no-underscore-dangle */
  const composeEnhancers =
    notProd &&
      typeof window === 'object' &&
      window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({
        // Prevent recomputing reducers for `replaceReducer`
        shouldHotReload: false
      }) : compose;
  /* eslint-enable */
  const store = createStore(createReducer(undefined, history), fromJS(initialState), composeEnhancers(...enhancers));
  // Extensions
  store.runSaga = sagaMiddleware.run;
  store.injectedReducers = {}; // Reducer registry
  store.injectedSagas = {}; // Saga registry

  // Make reducers hot reloadable, see http://mxs.is/googmo
  /* istanbul ignore next */
  if (module.hot) {
    module.hot.accept('./reducers', () => {
      store.replaceReducer(createReducer(store.injectedReducers, history));
      store.dispatch({ type: '@@REDUCER_INJECTED' });
    });
  }



  return store;
}
