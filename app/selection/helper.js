import debugFn from "debug";

const debug = debugFn("selection:helper");

import { v4 as uuid4 } from "uuid";

export const genKey = () => uuid4();

export function parseValueIfNeed(_value) {
  let value = undefined;
  if (Array.isArray(_value)) {
    value = _value;
  } else if (typeof _value === "string" && _value) {
    try {
      value = JSON.parse(_value);
    } catch (e) {
      console.error(e);
    }
  }
  if (Array.isArray(value)) {
    value = value.map((item) => {
      return { ...item, key: item.key || genKey() };
    });
  }
  debug("parseValueIfNeed", { input: _value, result: value });
  return value;
}
