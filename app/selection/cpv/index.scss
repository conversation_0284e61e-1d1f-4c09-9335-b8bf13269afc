.xt-cpv-item {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0px;
  }

  &-main {
    flex: 1;
  }

  &-category {
    margin-bottom: 8px;
  }

  &-category,
  &-properties {
    flex: 1;
    display: flex;
    align-items: center;
    > .xt-label {
      margin-right: 8px;
    }

    > .next-select {
      display: block;
      flex: 1;
    }
  }

  .xt-cpv-delete-btn {
    margin-top: 7px;
    margin-left: 12px;
    cursor: pointer;
  }

  .xt-cpv-add-btn {
    margin-top: 20px;
  }
}
