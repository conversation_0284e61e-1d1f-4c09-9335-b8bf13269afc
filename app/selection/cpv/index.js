import "./index.scss";

import React, { useEffect, useMemo, useState } from "react";
import debugFn from "debug";
import { CascaderSelect, Button, Icon, Message } from "@alife/next";

import { request } from "@/utils/request";
import { client as yunding } from "@/adator/api/yunding";

import { gen<PERSON>ey, parseValueIfNeed } from "../helper";

const debug = debugFn("selection:pooling:cpv");

function CPVPreview({ value: _value, scene }) {
  const value = parseValueIfNeed(_value);
  debug("CPVPreview", value, scene);
  try {
    return (
      <div
        style={{
          lineHeight: "21px",
          color: scene === "listPage" ? "#999" : undefined,
        }}
      >
        {(value || []).map((item) => {
          const properties = [];
          item?.cpvList?.forEach((p) => {
            const values = p.children?.map((c) => c.value);
            properties.push({
              propName: p.value,
              propValue: values.join(","),
            });
          });
          return (
            <div>
              类目:{item?.itemGoodsCategory?.cate3Name}，
              {properties.map((p) => `${p.propName}=${p.propValue}`).join("，")}
            </div>
          );
        })}
      </div>
    );
  } catch (e) {
    console.error(e);
    return <>{JSON.stringify(_value)}</>;
  }
}

function CPVSelectItem({ categories, value, onChange, onDelete }) {
  const [properties, setProperties] = useState([]);

  useEffect(() => {
    if (!value?.itemGoodsCategory?.cate3Id) {
      return;
    }
    yunding
      .fetch({
        apiKey:
          "ele-newretail-pickerone.ConfigQueryService.listItemDetailPvgovernInfo",
        params: [
          {
            cateLevel4Id: value.itemGoodsCategory.cate3Id,
          },
        ],
      })
      .then((res) => {
        const _properties = [];
        (res || []).map((item) => {
          let p = _properties.find((prop) => prop.label === item.propName);
          if (!p) {
            p = {
              label: item.propName,
              value: item.propName,
              children: [],
            };
            _properties.push(p);
          }
          const index = p.children.findIndex(
            (child) => child.value === p.propValue,
          );
          if (index === -1) {
            p.children.push({
              label: `${item.propName}:${item.propValue}`,
              value: `${item.propName}@@@${item.propValue}`,
            });
          }
        });
        debug("properties", _properties);
        setProperties(_properties);
      });
  }, [value?.itemGoodsCategory?.cate3Id]);

  const selectedProperties = useMemo(() => {
    const ret = [];
    value?.cpvList?.forEach((propItem) => {
      if (Array.isArray(propItem.children)) {
        propItem.children.forEach((propValueItem) => {
          const propName = propItem.value;
          const propValue = propValueItem.value;
          ret.push(`${propName}@@@${propValue}`);
        });
      }
    });
    return ret;
  }, [value?.cpvList]);

  debug("value", value);
  debug(
    "selectedProperties",
    selectedProperties,
    "properties dataSource",
    properties,
  );

  function handleCpvListChange(v) {
    if (!v?.length) {
      onChange({ ...value, cpvList: null });
      return;
    }
    const valueSet = new Set();
    v.forEach((text) => {
      if (!text.includes("@@@")) {
        const prop = properties.find((p) => p.value === text);
        if (prop) {
          prop.children?.forEach((child) => {
            valueSet.add(child.value);
          });
        }
      } else {
        valueSet.add(text);
      }
    });
    const cpvList = [];
    Array.from(valueSet).forEach((text) => {
      try {
        const [propName, propValue] = text.split("@@@");
        let p = cpvList.find((item) => item.value === propName);
        if (!p) {
          p = {
            label: propName,
            value: propName,
            children: [],
          };
          cpvList.push(p);
        }
        const index = p.children.findIndex((c) => c.value === propValue);
        if (index === -1) {
          p.children.push({
            label: propValue,
            value: propValue,
          });
        }
      } catch (e) {
        console.error(e);
      }
    });
    onChange({ ...value, cpvList });
  }

  return (
    <div key={value.key} className="xt-cpv-item">
      <div className="xt-cpv-item-main">
        <div className="xt-cpv-item-category">
          <div className="xt-label">类目</div>
          <CascaderSelect
            placeholder="请选择类目"
            dataSource={categories}
            style={{ flex: 1 }}
            value={value.itemGoodsCategory?.cate3Id}
            hasClear
            showSearch
            onChange={(v, category) => {
              onChange({
                ...value,
                itemGoodsCategory: v
                  ? {
                      cate3Id: category.value,
                      cate3Name: category.label,
                    }
                  : null,
                cpvList: v ? [] : null,
              });
            }}
          />
        </div>
        <div className="xt-cpv-item-properties">
          <div className="xt-label">属性</div>
          <CascaderSelect
            placeholder="请选择类目属性"
            dataSource={properties}
            style={{ flex: 1 }}
            hasClear
            multiple
            showSearch
            value={selectedProperties}
            onChange={handleCpvListChange}
          />
        </div>
      </div>
      <div className="xt-cpv-delete-btn" onClick={onDelete}>
        <Icon type={"delete-filling"} style={{ color: "#ccc" }} size="small" />
      </div>
    </div>
  );
}

function CPVSelect({ value: _value, onChange, limit = 3 }) {
  let value = parseValueIfNeed(_value) || [];
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    request()
      .get(`/api/v2/config/queryAllSkuCategory`)
      .then((resp) => {
        setCategories(resp?.data?.data?.data || []);
      });
  }, []);

  function updateItem(key, partial) {
    onChange &&
      onChange(
        value
          ? value.map((cond) => {
              if (cond.key === key) {
                return { ...cond, ...partial };
              } else {
                return cond;
              }
            })
          : undefined,
      );
  }

  debug("categories", categories);
  debug("value", value);

  return (
    <div>
      {(value || []).map((item) => {
        return (
          <CPVSelectItem
            value={item}
            categories={categories}
            onChange={(v) => updateItem(item.key, v)}
            onDelete={() => {
              onChange?.(
                value
                  ? value.filter((cond) => cond.key !== item.key)
                  : undefined,
              );
            }}
          />
        );
      })}
      {value.length < limit ? (
        <div className="xt-cpv-add-btn">
          <Button
            type="primary"
            text
            onClick={() => {
              if (value && value.length >= limit) {
                Message.error(`最多只允许配置 ${limit} 组`);
                return;
              }
              onChange && onChange([...(value || []), { key: genKey() }]);
            }}
          >
            新增
          </Button>
        </div>
      ) : null}
    </div>
  );
}

export function getDisplayName() {
  return "定制组件-CPV属性配置";
}

/**
 * 渲染展示组件
 *
 * @param {*} props
 * @param {*} scene 场景，可选值：listPage、detailPage
 * @returns
 */
export function renderPreview(props, scene) {
  return <CPVPreview {...props} scene={scene} />;
}

/**
 * 渲染标签配置控件
 *
 * @param {*} props
 * @returns
 */
export function renderControl(props) {
  return <CPVSelect {...props} />;
}

/**
 * 业务定制化组件，往往不能简单通过 value 是否为空进行过滤，需要自定义空数据过滤逻辑
 *
 * @param {*} rule
 * @param {*} isRequestData
 */
export function manipulateEffectRule(rule, isRequestData) {
  function filterItem(item) {
    return !!item.itemGoodsCategory?.cate3Id && !!item.cpvList?.length;
  }

  let ret = rule;
  if (isRequestData) {
    if (ret.filterFieldValue) {
      try {
        let items = JSON.parse(ret.filterFieldValue);
        items = items.filter(filterItem);
        ret = {
          ...ret,
          filterFieldValue: items.length ? JSON.stringify(items) : undefined,
        };
      } catch (e) {
        console.error(e);
      }
    }
  } else {
    if (Array.isArray(ret.filterFieldValue)) {
      ret = {
        ...ret,
        filterFieldValue: ret.filterFieldValue.filter(filterItem),
      };
      if (!ret.filterFieldValue?.length) {
        ret.filterFieldValue = undefined;
      }
    } else {
      // nothing to do
    }
  }
  return ret;
}

/**
 * 用于选品选商规则配置，当控件数据发生变化时，基于控件最新的 value 生成对应要提交的最新数据
 *
 * @param {*} options
 */
export function generatePostDataOnChange(options) {
  return JSON.stringify(options.value);
}
