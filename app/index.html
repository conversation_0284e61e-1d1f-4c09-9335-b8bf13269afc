<!doctype html>
<html lang="en">
  <head>
    <!-- The first thing in any HTML file should be the charset -->
    <meta charset="utf-8">

    <!-- Make the page mobile compatible -->
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="icon" href="%publicPath%/favicon.ico" />
    <title>招选搭投运营后台</title>
    <script
      type="text/javascript"
      id="beacon-aplus"
      src="//g.alicdn.com/alilog/mlog/aplus_v2.js"
      exparams="userid=&clog=o&aplus&sidx=aplusSidx&ckx=aplusCkx"
    ></script>
  </head>
  <body>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=dabae8dcd3afd26242a60cd96f17f091&plugin=AMap.Autocomplete"></script>
    <!-- Display a message if J<PERSON> has been disabled on the browser. -->

    <!-- The app hooks into this div -->
    <div id="app"></div>
    <!-- A lot of magic happens in this file. HtmlWebpackPlugin automatically includes all assets (e.g. bundle.js, main.css) with the correct HTML tags, which is why they are missing in this HTML file. Don't add any assets here! (Check out the webpack config files in config/webpack for details) -->
  </body>
</html>
