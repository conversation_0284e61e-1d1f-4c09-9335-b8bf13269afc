import React from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { env } from "@/config";
import "./style.scss";

const srcForEnv = {
  pre: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-top1000/#/goodsDelivery",
  ppe: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-top1000/#/goodsDelivery",
  prod: "https://nr.alibaba-inc.com/app/op-fe/selection-top1000/#/goodsDelivery",
};

function GoodsDelivery() {
  return (
    <div className="goods-delivery">
      <div className="iframe-wrapper">
        <iframe
          src={srcForEnv[env || "prod"]}
          frameBorder="0"
          className="preview-iframe"
        ></iframe>
      </div>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(GoodsDelivery),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-goodsDelivery",
      `time=${timeConsume}`,
    ]);
  }
);

export const QualityGoodsDelivery = permissionAccess(LogTimePutInPage);
