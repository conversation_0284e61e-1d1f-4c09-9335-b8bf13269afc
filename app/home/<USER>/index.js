import React, { useRef, useState } from "react";
import { useTablePage } from "@alife/nr-crud-hooks";
import {
  Balloon,
  Button,
  DatePicker,
  Icon,
  Input,
  Pagination,
  Select,
  Tab,
  Table,
  Dialog,
} from "@alife/next";
import moment from "moment";

import { FilterForm, FilterItem } from "@/components/filter";
import * as api from "./api";
import JSONEdit from "./jsEdit";

const { RangePicker } = DatePicker;

// enum 只能在 ts 中定义
const TypeEnum = {
  SCHEMA: "0",
  ORIGIN_SCHEMA: "1",
  FEI_ZI_YUAN_WEI: "2",
};

const typeDataSource = [
  { label: "Schema", value: TypeEnum.SCHEMA },
  { label: "Schema元数据", value: TypeEnum.ORIGIN_SCHEMA },
  { label: "非资源位", value: TypeEnum.FEI_ZI_YUAN_WEI },
];

const evnName = {
  1: "生产",
  2: "预发",
  3: "日常",
};

const transStrToArray = (value) => {
  return value
    ?.replace(/，/g, ",")
    ?.split(",")
    ?.filter(Boolean)
    .map((item) => item.trim());
};

export const SchemaReleaseRecordPage = () => {
  const [curSchema, setCurSchema] = useState(null);
  const typeRef = useRef(TypeEnum.SCHEMA);
  const isNomalSchema = typeRef.current === TypeEnum.SCHEMA; // 普通 schema
  const isFeiZiYuanWei = typeRef.current === TypeEnum.FEI_ZI_YUAN_WEI; // 非资源位
  const isZiYuanWei = typeRef.current !== TypeEnum.FEI_ZI_YUAN_WEI; // 普通 schema 和 schema 元数据

  // 接口请求
  const commodityRequest = async (searchValue, pagination) => {
    let service;
    switch (typeRef.current) {
      case TypeEnum.SCHEMA:
        // schema 接口
        service = api.queryResourceSchemaVersions;
        break;
      case TypeEnum.ORIGIN_SCHEMA:
        // schema 元数据接口
        service = api.queryMetadataSchemaVersions;
        break;
      case TypeEnum.FEI_ZI_YUAN_WEI:
        // 非资源位接口
        service = api.queryOtherSchemaVersions;
        break;

      default:
        break;
    }

    if (searchValue.env === "0") {
      // 不传查全部
      delete searchValue.env;
    }

    // 开始时间默认时分秒补充为 00:00:00, 结束时间默认时分秒补充为 23:59:59 并获取为时间戳
    const startTime =
      searchValue.dateTimeRange?.[0] &&
      moment(searchValue.dateTimeRange?.[0]).startOf("day").valueOf();
    const endTime =
      searchValue.dateTimeRange?.[1] &&
      moment(searchValue.dateTimeRange?.[1]).endOf("day").valueOf();

    const params = {
      ...searchValue,
      resourceIdList: transStrToArray(searchValue.resourceIds),
      locationIdList: transStrToArray(searchValue.locationIds),
      startTime,
      endTime,
      pageIndex: pagination.page,
      pageSize: pagination.pageSize,
    };

    // 清理处理前的数据
    delete params.dateTimeRange;
    delete params.resourceIds;
    delete params.locationIds;

    const res = await service(params);

    return Promise.resolve({
      data: {
        data: res.rows,
        total: res.total,
      },
    });
  };

  const tp = useTablePage({
    defaultPageSize: 15,
    defaultSearchValue: {},
    request: (searchValue, pagination) => {
      return commodityRequest(searchValue, pagination);
    },
  });

  const { loading, searchValue, dataSource, total, pagination } = tp || {};

  return (
    <div style={{ marginBottom: 40 }}>
      <h1>SchemaReleaseRecord</h1>
      <FilterForm
        layout={{
          labelSpan: 8,
          inputSpan: 16,
        }}
      >
        {/* FilterForm 中布局不支持动态设置，需要退而求其次 */}
        {isZiYuanWei ? (
          <FilterItem
            label={
              <span>
                资源位Id
                <Balloon.Tooltip
                  align="t"
                  trigger={
                    <Icon
                      type="help"
                      style={{ color: "#CCCCCC", marginLeft: 4 }}
                      size={"inherit"}
                    />
                  }
                >
                  resourceId
                </Balloon.Tooltip>
              </span>
            }
            name="resourceId"
          >
            <Input
              placeholder="请输入资源位ID,多个用逗号分隔"
              value={searchValue.resourceIds}
              onChange={(v) => {
                tp.updateSearchValue({ resourceIds: v });
              }}
            />
          </FilterItem>
        ) : (
          <FilterItem label="bizId" name="bizId">
            <Input
              placeholder="请输入bizID"
              value={searchValue.bizId}
              onChange={(v) => {
                tp.updateSearchValue({ bizId: v });
              }}
            />
          </FilterItem>
        )}
        {isZiYuanWei ? (
          <FilterItem
            label={
              <span>
                坑位ID
                <Balloon.Tooltip
                  align="t"
                  trigger={
                    <Icon
                      type="help"
                      style={{ color: "#CCCCCC", marginLeft: 4 }}
                      size={"inherit"}
                    />
                  }
                >
                  posId/locationId
                </Balloon.Tooltip>
              </span>
            }
            name="locationId"
          >
            <Input
              placeholder="请输入坑位ID,多个用逗号分隔"
              value={searchValue.locationIds}
              onChange={(v) => {
                tp.updateSearchValue({ locationIds: v });
              }}
            />
          </FilterItem>
        ) : (
          <FilterItem label="bizType" name="bizType">
            <Input
              placeholder="请输入bizType"
              value={searchValue.bizType}
              onChange={(v) => {
                tp.updateSearchValue({ bizType: v });
              }}
            />
          </FilterItem>
        )}
        <FilterItem label="资源位类型" name="env">
          <Select
            placeholder="请选择资源位类型"
            hasClear
            value={searchValue.env}
            onChange={(v) => {
              tp.updateSearchValue({ env: v });
            }}
          >
            <Select.Option value="0">全部</Select.Option>
            <Select.Option value="1">生产</Select.Option>
            <Select.Option value="2">预发</Select.Option>
            <Select.Option value="3">日常</Select.Option>
          </Select>
        </FilterItem>
        <FilterItem label="创建人" name="creatorName">
          <Input
            placeholder="请输入创建人"
            value={searchValue.creatorName}
            onChange={(v) => {
              tp.updateSearchValue({ creatorName: v });
            }}
          />
        </FilterItem>
        {isZiYuanWei ? (
          <FilterItem
            label={
              <span>
                元数据类型
                <Balloon.Tooltip
                  align="t"
                  trigger={
                    <Icon
                      type="help"
                      style={{ color: "#CCCCCC", marginLeft: 4 }}
                      size={"inherit"}
                    />
                  }
                >
                  resourceType
                </Balloon.Tooltip>
              </span>
            }
            name="resourceType"
          >
            <Input
              placeholder="请输入元数据类型"
              value={searchValue.resourceType}
              onChange={(v) => {
                tp.updateSearchValue({ resourceType: v });
              }}
            />
          </FilterItem>
        ) : (
          <></>
        )}
        <FilterItem label="时间段" name="dateTimeRange">
          <RangePicker
            style={{ width: 340 }}
            onChange={(v) => {
              tp.updateSearchValue({ dateTimeRange: v });
            }}
          />
        </FilterItem>
        <FilterItem>
          <Button type="primary" onClick={() => tp.search()}>
            查询
          </Button>
          &nbsp;
          <Button
            onClick={() => {
              tp.resetPagination();
              tp.resetSearch();
            }}
          >
            重置
          </Button>
        </FilterItem>
      </FilterForm>

      <Tab
        style={{ marginTop: 20 }}
        value={typeRef.current}
        onChange={(v) => {
          typeRef.current = v;
          tp.resetPagination();
          tp.search();
        }}
        shape="wrapped"
      >
        {typeDataSource.map((type) => {
          return <Tab.Item title={type.label} key={type.value} />;
        })}
      </Tab>

      <Table dataSource={dataSource} loading={loading}>
        <Table.Column title="Version" dataIndex="version" />
        <Table.Column
          title="Name/Id"
          dataIndex="creatorName"
          cell={(v, i, r) => {
            return <div>{`${r.creatorName}(${r.creatorUid})`}</div>;
          }}
        />
        <Table.Column
          title="创建时间"
          dataIndex="createTime"
          cell={(v, i, r) => {
            return (
              <div>{moment(r.createTime).format("YYYY-MM-DD HH:mm:ss")}</div>
            );
          }}
        />
        {isNomalSchema ? (
          <Table.Column
            title="资源位(resourceId)"
            dataIndex="resourceId"
            cell={(v, i, r) => {
              return r.resourceId ? (
                <div>{`${r.resourceName}(ID: ${r.resourceId})`}</div>
              ) : (
                "--"
              );
            }}
          />
        ) : null}
        {isNomalSchema ? (
          <Table.Column
            title="坑位(posId/locationId)"
            dataIndex="locationId"
            cell={(v, i, r) => {
              return r.locationId ? (
                <div>{`${r.locationName}(ID: ${r.locationId})`}</div>
              ) : (
                "--"
              );
            }}
          />
        ) : null}
        {isFeiZiYuanWei ? (
          <Table.Column
            title="非资源位(bizId/bizType)"
            dataIndex="bizId"
            cell={(v, i, r) => {
              let bizName = "";
              if ((v === 1 || v === 100001) && r.bizType === 2) {
                bizName = "橱窗推荐";
              } else if ((v === 1 || v === 100001) && r.bizType === 1) {
                bizName = "全能榜单管理-榜单列表";
              }
              return r.bizId ? (
                <div>{`${bizName}(${r.bizId}/${r.bizType})`}</div>
              ) : (
                "--"
              );
            }}
          />
        ) : null}
        {isZiYuanWei ? (
          <Table.Column
            title="元数据类型(resourceType)"
            dataIndex="resourceType"
            cell={(v, i, r) => {
              return r.resourceType ? <div>{r.resourceType}</div> : "--";
            }}
          />
        ) : null}

        <Table.Column
          title="Schema"
          dataIndex="schema"
          cell={(v, i, r) => {
            return (
              <Button
                type="primary"
                text
                onClick={() => {
                  setCurSchema(r.schema);
                }}
              >
                点击查看
              </Button>
            );
          }}
        />
        <Table.Column
          title="环境"
          dataIndex="env"
          cell={(v) => {
            return <div>{evnName[v] || "--"}</div>;
          }}
        />
      </Table>
      <Pagination
        pagination={pagination}
        pageSize={pagination.pageSize}
        pageSizeList={[15, 30, 50]}
        total={total}
        pageSizeSelector="dropdown"
        onChange={tp.changePage}
        onPageSizeChange={tp.changePageSize}
      />

      <Dialog
        height="700px"
        visible={!!curSchema}
        title="Schema 详情"
        footer={
          <Button type="primary" onClick={() => setCurSchema(null)}>
            确定
          </Button>
        }
        onClose={() => setCurSchema(null)}
      >
        <JSONEdit json={curSchema} />
      </Dialog>
    </div>
  );
};
