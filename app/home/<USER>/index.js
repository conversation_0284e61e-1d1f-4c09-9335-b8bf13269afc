import React from 'react';
import {Message} from '@alife/next';
import clz from 'classnames';
import * as qs from 'query-string';
import { noop } from 'lodash';


export class PageBase extends React.Component {
  static Container = ({className, children}) => <div className={clz(`page`, className)}>{children}</div>

  toastRequestError(error, msg='数据请求失败,请稍后再试'){
    console.error('got request error: ', error)
    this.toast({
      content: msg,
      type: 'error'
    })
  }

  // https://fusion.design/component/basic/message
  toast(options){
    let content = ''
    if (typeof options === 'string'){
      content = options
      options = null
    } else {
      content = options.content
    }
    Message.show({
      content,
      ...options
    });
  }

  updateState(cb = noop, after = noop){
    const state = cb(this.state)
    this.setState({...state}, () =>{
      after()
    })
  }

  get params() {
    return this.props.match ? this.props.match.params : ''
  }

  get location(){
    return this.props.location
  }

  get history(){
    return this.props.history
  }

  get query(){
    return qs.parse(this.location.search)
  }

  //=> 'foo=unicorn&ilike=pizza'
  encodeQuery(obj){
    return qs.stringify(obj)
  }
}
