import React, { useState, useEffect, useCallback } from "react";
import { isAllNumber } from "@/utils/validators";
import Clipboard from "clipboard";
import {
  Form,
  Select,
  Input,
  Button,
  Table,
  Message,
  Dialog,
  Pagination,
  Divider,
  Grid,
  Field,
} from "@alife/next";
import "./style.scss";
import * as api from "@/utils/api/supermarketChannelBigStore";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { queryUserByKeyWord } from "@/containers/channel/market/request";
import { onRequestError } from "@/utils/api";
import { dateRender } from "../poolPage/common/index";
import { debounce } from "lodash";
import { renderDeliveryTime, renderCitys } from "./cell";

const { Row, Col } = Grid;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};

const defaultFieldValue = { sceneTypes: [2, 3] };

function SupermarketChannelBigStore({ history }) {
  const clipboard = new Clipboard("#copy");
  const [dataSource, setDataSource] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [size, setSize] = useState(15);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState({...defaultFieldValue});
  const [roleDataSource, setRoleDataSource] = useState([]); //员工工号
  const field = Field.useField({
    values: {...defaultFieldValue},
    onChange: (name, value) => {
      if (name === "sceneTypes" && value.length === 0) {
        Message.warning("至少选择一个场景类型");
        field.setValue("sceneTypes", [2, 3]);
        query[name] = [2, 3];
      } else {
        query[name] = value;
      }
      setQuery(query);
    },
  });

  /**
   * 池子查询列表
   **/
  const getPoolList = (_page, _query, newSize) => {
    setIsLoading(true);

    try {
      let request = api.getBigStoreList;
      request({
        pageSize: newSize ? newSize : size,
        pageNum: _page || 1,
        query: _query,
      }).then((resp) => {
        const { data = [], total: _total, code } = resp.data;
        if (code === 200) {
          setDataSource(data);
          setTotal(_total);
          setPage(_page);
          setSize(newSize ? newSize : size);
          setIsLoading(false);
        } else {
          onRequestError("网络异常");
          setIsLoading(false);
        }
      });
    } catch (error) {
      setIsLoading(false);
      onRequestError(error);
    }
  };

  const onView = (record) => {
    history.push(
      `/channelManage/supermarketChannelBigStore/sceneManage/view/${record.sceneId}`
    );
  };

  const onEdit = (record) => {
    history.push(
      `/channelManage/supermarketChannelBigStore/sceneManage/create/${record.sceneId}`
    );
  };

  const onCopy = (record) => {
    history.push(
      `/channelManage/supermarketChannelBigStore/sceneManage/create/-${record.sceneId}`
    );
  };

  const onOffLine = useCallback((record) => {
    Dialog.confirm({
      title: "提示",
      content: "确定删除当前场景么?",
      onOk: async () => {
        let request = api.delMall4Scene;
        request({ sceneId: record.sceneId })
          .then(() => {
            Message.success("操作成功");
            getPoolList(1, query);
          })
          .catch(onRequestError);
      },
    });
  }, [query]);

  const renderNewOption = (record) => {
    let divider = <Divider direction="ver" />;
    let detailEle = (
      <Button text={true} onClick={() => onView(record)}>
        查看
      </Button>
    );
    let editEle = (
      <Button text={true} onClick={() => onEdit(record)}>
        编辑
      </Button>
    );
    let copyEle = (
      <Button text={true} onClick={() => onCopy(record)}>
        复制
      </Button>
    );
    let offLineEle = (
      <Button text={true} onClick={() => onOffLine(record)}>
        删除
      </Button>
    );
    let options = (
      <>
        {copyEle}
        {divider}
        {detailEle}
        {divider}
        {editEle}
        {divider}
        {offLineEle}
      </>
    );
    return options;
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "sceneId",
      width: "120px",
      cell: (value) => {
        return (
          <div className="pool-info">
            <p className="pool-id">
              ID：{value}
              <span className="btn-copy" id="copy" data-clipboard-text={value}>
                复制
              </span>
            </p>
          </div>
        );
      },
    },
    {
      title: "场景名称",
      dataIndex: "sceneName",
      width: "150px",
      cell: (value) => {
        return (
          <div className="pool-info">
            <div className="fontWeight">{value}</div>
          </div>
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "gmtCreate",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "投放时段",
      dataIndex: "mall4ItemRule",
      width: "200px",
      cell: renderDeliveryTime,
    },
    {
      title: "投放城市",
      dataIndex: "mall4ItemRule.citys",
      width: "150px",
      cell: renderCitys,
    },
    { title: "权重", dataIndex: "weight", width: "80px" },

    { title: "创建人", dataIndex: "creatorName", width: "80px" },
    {
      title: "操作",
      dataIndex: "sceneId",
      cell: (value, index, record) => {
        return <div className="opbtns">{renderNewOption(record)}</div>;
      },
      width: "200px",
    },
  ];

  const onPageChange = useCallback((newPage) => {
    getPoolList(newPage, query);
  }, [query]);

  const onPageSizeChange = useCallback((newSize) => {
    getPoolList(page, query, newSize);
  }, [page, query]);

  useEffect(() => {
    getPoolList(1, query);

    clipboard.on("success", function () {
      Message.success("复制成功");
    });
    clipboard.on("error", function () {
      Message.success("复制失败，请刷新重试");
    });
  }, []);

  /**检测id格式 */
  const checkId = function (rule, value, callback) {
    const errors = isAllNumber(value);
    if (errors.length && value) {
      callback("格式错误");
    } else {
      callback();
    }
  };

  const onSearchUser = useCallback(
    debounce((keyword) => {
      if (keyword) {
        const reg = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]+$");
        if (!!keyword && !reg.test(keyword)) {
          Message.error("不能输入特殊字符");
          return;
        }
        queryUserByKeyWord(keyword)
          .then((data) => {
            const _dataSource = data.map((v) => ({
              value: v.empId,
              label: `${v.lastName}_${v.empId}`,
              record: v,
            }));
            setRoleDataSource(_dataSource);
          })
          .catch(onRequestError);
      }
    }, 800),

    [roleDataSource]
  );

  // 查询
  const searchPoolList = useCallback(() => {
    getPoolList(1, query);
  }, [query]);

  // 重置表单
  const resetPoolList = () => {
    field.reset();
    field.setValues(defaultFieldValue);
    setQuery(defaultFieldValue);
  };

  return (
    <div className="pool-list">
      <Form className="filter" field={field}>
        <div className="top">
          <span className="pool-title">全能超市大店场景列表</span>
          <Button
            type="primary"
            className="btn-create-pool"
            onClick={() => {
              history.push(
                "/channelManage/supermarketChannelBigStore/sceneManage/create"
              );
            }}
          >
            新建场景
          </Button>
        </div>
        <Row>
          <Col span={5}>
            <FormItem label="场景ID " {...formItemLayout} validator={checkId}>
              <Input name="sceneId" />
            </FormItem>
          </Col>
          <Col span={5}>
            <FormItem label="场景名称" {...formItemLayout}>
              <Input name="sceneName" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="场景类型" {...formItemLayout}>
              <Select
                name="sceneTypes"
                style={{ width: "100%" }}
                mode="multiple"
                dataSource={[
                  { label: "搭配购", value: 2 },
                  { label: "普通货架", value: 3 },
                ]}
              />
            </FormItem>
          </Col>
          <Col span={5}>
            <FormItem label="创建人" {...formItemLayout}>
              <Select
                name="creatorId"
                mode="single"
                hasClear
                hasArrow={false}
                onSearch={(value) => {
                  onSearchUser(value);
                }}
                showSearch
                dataSource={roleDataSource}
                style={{ width: "100%" }}
              />
            </FormItem>
          </Col>
          <Col
            span={3}
            style={{
              marginBottom: "20px",
              display: "flex",
              justifyContent: "end",
            }}
          >
            <Button
              type="secondary"
              onClick={searchPoolList}
              style={{ marginLeft: "95px" }}
            >
              查询
            </Button>
            <Button className="btn_reset" onClick={resetPoolList}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>

      <div className="table-panel">
        <Table
          dataSource={dataSource}
          loading={isLoading}
          hasBorder={false}
          primaryKey="id"
        >
          {columns.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
        <Pagination
          onChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          total={total}
          current={page}
          pageSize={size}
          pageSizeSelector="dropdown"
          pageSizeList={[10, 15, 20]}
          popupProps={{ align: "bl tl" }}
          style={{ float: "right", marginTop: "10px" }}
        />
      </div>
    </div>
  );
}

export const LogTimeBigStoreManegePage = logTimeComponent(
  withRouter(SupermarketChannelBigStore),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const BigStoreManegePage = permissionAccess(LogTimeBigStoreManegePage);
