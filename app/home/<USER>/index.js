import React from "react";

import { IframeContainer } from "@/components/IframeContainer";

const srcForEnv = {
  pre: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-permission-page#/",
  ppe: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-permission-page#/",
  prod: "https://nr.alibaba-inc.com/app/op-fe/selection-permission-page#/",
};

export function Page() {
  return <IframeContainer pageMap={srcForEnv} />;
}

const denyPageMap = {
  pre: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-permission-page#/deny",
  ppe: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-permission-page#/deny",
  prod: "https://nr.alibaba-inc.com/app/op-fe/selection-permission-page#/deny",
};

export function DenyPage() {
  return <IframeContainer pageMap={denyPageMap} syncQuery />;
}
