import React from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { env } from "@/config";
import "./style.scss";

const srcForEnv = {
  pre: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-top1000/#/goodsManage",
  ppe: "https://pre-nr.alibaba-inc.com/app/op-fe/selection-top1000/#/goodsManage",
  prod: "https://nr.alibaba-inc.com/app/op-fe/selection-top1000/#/goodsManage",
};

function GoodsManage() {
  return (
    <div className="goods-manage">
      <div className="iframe-wrapper">
        <iframe
          src={srcForEnv[env || "prod"]}
          frameBorder="0"
          className="preview-iframe"
        ></iframe>
      </div>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(GoodsManage),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-goodsManage",
      `time=${timeConsume}`,
    ]);
  }
);

export const QualityGoodsManage = permissionAccess(LogTimePutInPage);
