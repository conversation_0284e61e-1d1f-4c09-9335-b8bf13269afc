import React, { useContext, useEffect, useState } from "react";
import { CascaderSelect } from "@alife/next";

import { onRequestError } from "@/utils/api";
import * as api from "@/utils/api/supermarketChannelBigStore";

import { deepCopy } from "../utils";
import { GeneralUtilsContext } from "../hooks/generalUtils";

const CategorySelect = ({ disabled, value, onChange }) => {
  let widgetValue = [];
  if (value) {
    // hasSelectedDisplay 前端用来展示，hasSelected 用来供后端使用
    const { hasSelectedDisplay = [] } = value || {};
    widgetValue = hasSelectedDisplay.map((v) => v.value);
  }
  const { catDataSource } = useContext(GeneralUtilsContext);
  const [dataSource, setDataSource] = useState(catDataSource || []);
  const [widgetCasValue, setWidgetCasValue] = useState(widgetValue);

  /*获取商品分类数据*/
  const fetchSkuCategory = async () => {
    try {
      let request = api.getSkuCategory;
      let resp = await request();
      const _dataSource = resp.data.map((data) => {
        const dataItem = deepCopy(data);
        dataItem.value = dataItem.value.toString();
        dataItem.children &&
          dataItem.children.map((subItem) => {
            subItem.value = subItem.value.toString();
            subItem.children &&
              subItem.children.map((thirdItem) => {
                thirdItem.value = thirdItem.value.toString();
              });
          });
        return dataItem;
      });
      setDataSource(_dataSource);
    } catch (error) {
      onRequestError(error);
    }
  };

  useEffect(() => {
    if (dataSource.length > 0) {
      return;
    } else {
      fetchSkuCategory();
    }
  }, []);

  const onCategoryChange = (_value, data, extra = {}) => {
    setWidgetCasValue(_value && _value.length > 0 ? _value : "");
    const hasSelected = (extra.checkedData || []).map((item) => {
      const { value: itemValue, label, pos } = item;
      return {
        value: itemValue,
        label,
        level: pos.split("-").length - 1,
      };
    });
    const hasSelectedDisplay = data.map((item) => {
      const { value: itemValue, label, pos } = item;
      return {
        value: itemValue,
        label,
        level: pos.split("-").length - 1,
      };
    });

    onChange &&
      onChange({
        hasSelected,
        hasSelectedDisplay,
      });
  };
  return (
    <CascaderSelect
      showSearch
      multiple={true}
      expandTriggerType={"hover"}
      placeholder="请选择商品类目"
      name="category"
      value={(widgetCasValue || []).map((i) => {
        return i;
      })}
      disabled={disabled}
      onChange={(_value, data, extra) => onCategoryChange(_value, data, extra)}
      dataSource={dataSource || []}
      style={{ width: "100%" }}
    />
  );
};

export default CategorySelect;
