import React, { useEffect, useState } from "react";
import { CascaderSelect } from "@alife/next";

import { onRequestError } from "@/utils/api";
import * as api from "@/utils/api/supermarketChannelBigStore";

import { deepCopy } from "../utils";

const CategorySelect = ({ disabled, value, onChange }) => {
  let widgetValue = [];
  if (value && value.length > 0) {
    widgetValue = value.map((v) => v.value);
  }
  const [dataSource, setDataSource] = useState([]);
  const [widgetCasValue, setWidgetCasValue] = useState(widgetValue);

  /*获取商品分类数据*/
  const fetchSkuCategory = async () => {
    try {
      let request = api.getSkuCategory;
      let resp = await request();
      const _dataSource = resp.data.map((data) => {
        let dataItem = deepCopy(data);
        dataItem.value = dataItem.value.toString();
        if (dataItem.children) {
          dataItem.children = dataItem.children.map((subItem) => {
            const _subItem = { ...subItem };
            _subItem.value = _subItem.value.toString();
            if (_subItem.children) {
              _subItem.children.map((thirdItem) => {
                const _thirdItem = { ...thirdItem };
                _thirdItem.value = _thirdItem.value.toString();
                return _thirdItem;
              });
            }
            return _subItem;
          });
        }
        return dataItem;
      });
      setDataSource(_dataSource);
    } catch (error) {
      onRequestError(error);
    }
  };

  useEffect(() => {
    fetchSkuCategory();
  }, []);

  const onCategoryChange = (_value, data) => {
    setWidgetCasValue(_value && _value.length > 0 ? _value : "");
    const hasSelectedDisplay = data.map((item) => {
      const { value: categoryValue, label, pos } = item;
      return {
        value: categoryValue,
        label,
        level: pos.split("-").length - 1,
      };
    });

    onChange && onChange(hasSelectedDisplay);
  };
  return (
    <CascaderSelect
      showSearch
      multiple={true}
      expandTriggerType={"hover"}
      placeholder="请选择商品类目"
      name="category"
      value={(widgetCasValue || []).map((i) => {
        return i;
      })}
      disabled={disabled}
      onChange={(_value, data, extra) => onCategoryChange(_value, data, extra)}
      dataSource={dataSource || []}
      style={{ width: "100%" }}
    />
  );
};

export default CategorySelect;
