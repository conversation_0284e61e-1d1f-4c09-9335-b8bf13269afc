import React, { useEffect, useState } from "react"
import { CascaderSelect } from "@alife/next"
import { queryAoiList } from '@/utils/api'


function AOISelect(props) {
  const [data, setData] = useState()

  const getData = async () => {
    try {
      const result = await queryAoiList()
      setData(result)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getData()
  }, [])

  return (
    <CascaderSelect
      dataSource={data}
      style={{ width: '100%' }}
      {...props}
    />
  )
}

export default AOISelect