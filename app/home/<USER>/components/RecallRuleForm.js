import React, { useContext } from "react";
import {
  CascaderSelect,
  Checkbox,
  Form,
  NumberPicker,
  Radio,
} from "@alife/next";

import { FormItem } from "../components/index";
import { GeneralUtilsContext } from "../hooks/generalUtils";
const labelCol = { span: 4 };

const statusList = [
  { value: 1, label: "预定中" },
  { value: 5, label: "营业中" },
  { value: 4, label: "即将休息" },
  { value: 0, label: "休息中" },
];

const defaultTopItemSortRuleDataSource = [
  { value: 1, label: "商品销量排序" },
  { value: 2, label: "商品折扣排序" },
  { value: 5, label: "门店距离排序" },
  { value: 3, label: "商品算法排序-点击率最高" },
  { value: 10, label: "商品算法排序-转化率最高" },
  { value: 14, label: "商品价格升序" },
];

const RecallRuleForm = ({
  disabled,
  field,
  showTopSortRule,
  showShopScatter,
  topItemSortRuleDataSource,
}) => {
  const { storeMajorDataSource } = useContext(GeneralUtilsContext);
  return (
    <Form className="rankv2-form rankv2-recall-rule " field={field}>
      {showTopSortRule ? (
        <FormItem labelCol={labelCol} label="置顶商品排序:">
          <Radio.Group
            disabled={disabled}
            name="topItemSortRule"
            dataSource={
              topItemSortRuleDataSource || defaultTopItemSortRuleDataSource
            }
          />
        </FormItem>
      ) : null}

      {showShopScatter ? (
        <FormItem labelCol={labelCol} label="是否按照店铺打散:">
          <Radio.Group disabled={disabled} name="shopScatter">
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </FormItem>
      ) : null}

      <FormItem labelCol={labelCol} label="时段:">
        <Checkbox.Group
          disabled={disabled}
          name="openStatusSet"
          dataSource={statusList}
        />
      </FormItem>

      <FormItem labelCol={labelCol} label="最大配送费:">
        <NumberPicker
          disabled={disabled}
          style={{ width: "140px" }}
          name="maxDeliveryPrice"
          min={0}
          max={999999}
        />
      </FormItem>

      <FormItem labelCol={labelCol} label="商家主营类目:">
        <CascaderSelect
          disabled={disabled}
          multiple={true}
          dataSource={storeMajorDataSource}
          value={(field.getValue("shopMajorCategoryContent") || [])
            .map((item) => {
              if (item) {
                return item.value;
              }
            })
            .filter(Boolean)}
          onChange={(_value) => {
            const selectedItems = _value.map((val) => {
              const foundOption = storeMajorDataSource.find(
                (option) => option.value === val
              );
              if (foundOption) return foundOption;
              return null; // 或者抛出错误，表示找不到对应的选项
            });
            field.setValue("shopMajorCategoryContent", selectedItems);
          }}
        />
      </FormItem>
    </Form>
  );
};

export default RecallRuleForm;
