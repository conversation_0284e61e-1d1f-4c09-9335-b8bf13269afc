import React, { useEffect, useState } from 'react';
import { CascaderSelect } from '@alife/next';
import { getAllDeliveryChannel } from '@/utils/api'


function ChannelSelect(props) {
  const [data, setData] = useState()

  const getData = async () => {
    try {
      const result = await getAllDeliveryChannel()
      if (result && result.channelList) {
        const formatChannelList = result.channelList.map((item) => ({ ...item, children: item.subChannels }))
        setData(formatChannelList)
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getData()
  }, [])

  return (
    <CascaderSelect
      dataSource={data}
      style={{ width: '100%' }}
      {...props}
    />
  )
}

export default ChannelSelect