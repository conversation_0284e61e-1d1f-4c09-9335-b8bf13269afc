import React, { useEffect, useState, useRef } from "react";
import { Select, Message } from "@alife/next";
import _ from "lodash";

import { onRequestError } from "@/utils/api";
import * as api from "@/utils/api/supermarketChannelBigStore";

const PoolIdsSelect = ({
  maxCommodityLength = 2,
  disabled,
  value,
  onChange,
}) => {
  const isFirst = useRef(false);
  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    if (isFirst.current) {
      return;
    }
    if (value && value.length > 0) {
      const promiseList = [];
      value.map((item) => {
        promiseList.push(
          api.getPoolInfoSurge({
            searchKey: item,
            poolType: 1, // 1：选品 2：选店
          })
        );
      });
      Promise.all(promiseList)
        .then((res) => {
          const _dataSource = res.map((item) => {
            if (item && item[0]) {
              return {
                label: item[0].poolContent,
                value: item[0].poolId,
                newPlatformFlag: item[0].newPlatformFlag,
              };
            } else {
              return undefined;
            }
          });
          isFirst.current = true;
          setDataSource(_dataSource.filter(Boolean));
        })
        .catch((e) => {
          onRequestError(e);
        });
    }
  }, [value]);

  const onPoolChange = (_value, actionType) => {
    if (_value.length > maxCommodityLength) {
      Message.warning(`选品集ID输入最多${maxCommodityLength}个`);
    } else {
      if (actionType == "itemClick" || actionType == "tag") {
        onChange && onChange(_value);
      }
    }
  };

  const onSearch = _.debounce((keyword) => {
    if (keyword) {
      api
        .getPoolInfoSurge({
          searchKey: keyword,
          poolType: 1, // 1：选品 2：选店
        })
        .then((data) => {
          const _dataSource = data.map((item) => ({
            label: item.poolContent,
            value: item.poolId,
            newPlatformFlag: item.newPlatformFlag,
          }));
          setDataSource(_dataSource);
        })
        .catch((e) => {
          onRequestError(e);
        });
    } else {
      setDataSource([]);
    }
  }, 800);

  return (
    <Select
      mode="multiple"
      showSearch
      placeholder={`请输入选品集ID${
        maxCommodityLength ? "，支持输入最多" + maxCommodityLength + "个" : ""
      }`}
      name="poolIds"
      value={value}
      disabled={disabled}
      onChange={(_value, actionType) => onPoolChange(_value, actionType)}
      onSearch={(key) => onSearch(key)}
      dataSource={dataSource}
      style={{ width: "100%" }}
    />
  );
};
export default PoolIdsSelect;
