import React, { useEffect, useState } from "react"
import { CascaderSelect } from "@alife/next"
import { getCityList } from '@/utils/api'


const formatData = (data, idx = 1) => {
  return (data || []).map((item) => ({
    value: '' + item.value,
    label: item.label,
    level: idx,
    children: formatData(item.children, idx + 1),
  }))
}

function CityCascaderSelect(props) {
  const { value, onChange, onSelectCitysChange, ...reset } = props
  const [data, setData] = useState()

  const getData = async () => {
    try {
      const result = await getCityList()
      const resultFormat = formatData(result)
      setData(resultFormat)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    getData()
  }, [])

  const _value = (value || []).map(item => item.value)
  const handleChange = (_val, selectedData, extra) => {
    const citys = selectedData.map(item => ({
      value: item.value,
      label: item.label,
      level: item.level
    }))
    const selectCitys = extra.checkedData.filter(item => item.level === 2).map(item => item.value)
    onChange && onChange(citys)
    onSelectCitysChange && onSelectCitysChange(selectCitys)
  }

  return (
    <CascaderSelect
      dataSource={data}
      style={{ width: '100%' }}
      value={_value}
      onChange={handleChange}
      {...reset}
    />
  )
}

export default CityCascaderSelect