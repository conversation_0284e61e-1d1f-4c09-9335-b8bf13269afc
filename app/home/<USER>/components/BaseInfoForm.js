import React from "react";
import { DatePicker, Form, Input, NumberPicker, Radio } from "@alife/next";
import moment from "moment";

import DayOfWeekPicker from "@/utils/Form/src/components/widgets/DayOfWeekWidget";

import { FormItem, ImageUpload, TimeRangeArray } from "./index";

const BaseInfoForm = ({ field, disabled = false }) => {
  return (
    <Form className="rankv2-form" field={field}>
      {/* <FormItem label="榜单ID:">
        <Input disabled name="id" />
      </FormItem> */}

      <FormItem label="配置名称:" required>
        <Input
          disabled={disabled}
          name="configName"
          maxLength={20}
          showLimitHint={true}
          placeholder="请输入配置名称，最多20个字"
        />
      </FormItem>

      <FormItem label="榜单名称:" required>
        <Input
          disabled={disabled}
          name="name"
          maxLength={4}
          showLimitHint={true}
          placeholder="请输入榜单名称，最多4个字"
        />
      </FormItem>

      <FormItem label="榜单名称图片:" required>
        <ImageUpload
          disabled={disabled}
          name="nameImgUrl"
          imgOptions={{
            width: 570,
            height: 70,
            maxSize: 500,
            accept: "png,apng,jpg,jpeg,gif",
          }}
        />
      </FormItem>

      <FormItem label="开始时间:" required>
        <DatePicker
          disabled={disabled}
          name="beginTime"
          showTime
          placeholder="请选择开始时间"
        />
      </FormItem>

      <FormItem label="结束时间:" required>
        <DatePicker
          disabled={disabled}
          showTime={{
            defaultValue: moment("23:59:59", "HH:mm:ss", true),
          }}
          name="endTime"
          placeholder="请选择结束时间"
        />
      </FormItem>

      <FormItem label="星期:" required>
        <DayOfWeekPicker disabled={disabled} name="workDay" />
      </FormItem>

      <FormItem label="时段:" required>
        <Radio.Group name="timeSelection" disabled={disabled}>
          <Radio value={0}>全天</Radio>
          <Radio value={1}>选择</Radio>
        </Radio.Group>
      </FormItem>

      {+field.getValue("timeSelection") === 1 ? (
        <FormItem label=" " >
          <TimeRangeArray name="timeRanges" disabled={disabled} />
        </FormItem>
      ) : null}

      <FormItem label="榜单分类:" required>
        <Radio.Group name="boardCategory" disabled={disabled}>
          <Radio value={1}>标品榜单</Radio>
          <Radio value={2}>非标品榜单</Radio>
        </Radio.Group>
      </FormItem>

      <FormItem label="榜单最少展示商品数量:" required>
        <NumberPicker
          disabled={disabled}
          style={{ width: "140px" }}
          name="minDisplayCount"
          min={0}
          max={999999}
          showLimitHint={true}
          placeholder="最多999999"
        />
      </FormItem>

      <FormItem label="榜单最大召回商品数量:" required>
        <NumberPicker
          disabled={disabled}
          style={{ width: "140px" }}
          name="maxRecallCount"
          min={0}
          max={999999}
          showLimitHint={true}
          placeholder="最多999999"
        />
      </FormItem>

      <FormItem label="外投分享文案:" required>
        <Input
          disabled={disabled}
          name="shareContent"
          maxLength={20}
          showLimitHint={true}
          placeholder="请输入外投分享文案，最多20个字"
        />
      </FormItem>

      <FormItem label="权重:">
        <NumberPicker
          disabled={disabled}
          style={{ width: "200px" }}
          name="weight"
          min={0}
          max={999999}
          showLimitHint={true}
          placeholder="取值优先级：数值＞时间"
        />
      </FormItem>
    </Form>
  );
};

export default BaseInfoForm;
