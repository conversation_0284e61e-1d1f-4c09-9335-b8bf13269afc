import React from "react"
import { Select, Input, Button } from "@alife/next"

const versionGroupSet = [
  {
    value: "in",
    label: "包含(支持多选,分割)",
  },
  {
    value: "not_in",
    label: "不包含(支持多选,分割)",
  },
  {
    value: "gt",
    label: "大于",
  },
  {
    value: "lt",
    label: "小于",
  },
  {
    value: "ge",
    label: "大于等于",
  },
  {
    value: "le",
    label: "小于等于",
  },
];
const defaultValue = {
  operateType: undefined,
  value: undefined
}

function VersionSelect(props) {
  const { value = [defaultValue], onChange, isPreview } = props || {}

  const _onValueChange = (key, val, idx) => {
    const _value = [...value];
    _value[idx] = { ..._value[idx], [key]: val };
    onChange(_value);
  };

  const onRemove = (index) => {
    const _value = [...value];
    _value.splice(index, 1);
    onChange(_value.length > 0 ? _value : undefined);
  };

  const onAdd = () => {
    onChange([...value, defaultValue]);
  };

  return (
    <div>
      {Array.isArray(value) ? value.map((item, idx) => (
        <div style={{ width: "600px", marginBottom: "10px",display: "flex" }} key={idx}>
          <Select
            placeholder="请选择"
            style={{ width: "200px", marginRight: "5px" }}
            name="operateType"
            isPreview={isPreview}
            dataSource={versionGroupSet}
            value={item.operateType}
            onChange={(val) =>
              _onValueChange("operateType", val, idx)
            }
          />
          <Input
            name="versionValue"
            style={{ marginRight: "5px" }}
            placeholder="请输入版本号(比如9.1,9.1.1)"
            isPreview={isPreview}
            value={item.value}
            onChange={(val) =>
              _onValueChange("value", val, idx)
            }
          />
          {!isPreview && <Button
            type={"primary"}
            style={{ marginRight: "5px" }}
            onClick={() => onRemove(idx)}
          >
            -
          </Button>}
          {idx === 0 && !isPreview && (
            <Button type={"primary"}
              onClick={onAdd}
            >
              and
            </Button>
          )}
        </div>
      )) : null}
    </div>
  )

}

export default VersionSelect