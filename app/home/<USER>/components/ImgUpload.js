import React, { Fragment, useEffect, useState, useRef } from "react";

import { Message, Upload } from "@alife/next";

import { putInReq } from "@/utils/request";

function ImageUpload({ disabled, value, onChange, imgOptions = {} }) {
  const [version, setVersion] = useState(0);
  const request = (base64, fileName, width, height) => {
    base64 = base64.replace(/^data\:.*?;base64,/, "");
    return putInReq
      .post("/api/pic/uploadPic", {
        userId: "1",
        base64,
        name: fileName,
      })
      .then((res) => {
        if (res.status === 200 && res.data.code === "200") {
          const newVal = (value || []).slice(0);
          newVal.push({
            url: res.data.data,
            width,
            height,
          });
          Message.show("上传成功");
          onChange(newVal);
        } else {
          Message.error(
            `上传失败，${res.data && (res.data.msg || "未知错误")}`
          );
        }
      })
      .catch((e) => {
        Message.error(
          `上传失败，${res.error && (res.error.message || "未知错误")}`
        );
      });
  };

  const beforeUpload = (file) => {
    const reader = new FileReader();
    reader.onload = () => {
      const img = new Image();
      const url = "";
      img.onload = async () => {
        const { size, type } = file;
        const { width, height } = img;
        if (width !== imgOptions.width) {
          setVersion((prev) => prev + 1)
          return Message.error(
            `图片宽度需为${imgOptions.width}px，目前宽度${width}px`
          );
        } else if (height !== imgOptions.height) {
          setVersion((prev) => prev + 1)
          return Message.error(
            `图片高度需为${imgOptions.height}px，目前高度${height}px`
          );
        } else if (imgOptions.maxSize * 1024 < size) {
          setVersion((prev) => prev + 1)
          return Message.error(`图片体积需小于${imgOptions.maxSize}kb`);
        } else if (imgOptions.accept) {
          const imageType = type.replace("image/", "");
          const availableTypes = imgOptions.accept
            .split(",")
            .map((item = "") => item.trim());
          if (availableTypes.indexOf("jpg") >= 0) availableTypes.push("jpeg");
          if (availableTypes.indexOf(imageType) === -1) {
            setVersion((prev) => prev + 1)
            return Message.error(
              `图片格式需为${imgOptions.accept}，检测到格式为${imageType}`
            );
          }
        }
        await request(reader.result, file.name, width, height);
      };
      img.src = reader.result;
    };
    reader.readAsDataURL(file);
    return false;
  };

  return (
    <div className="upload_img_comp">
      <Upload.Card
        key={version}
        accept="image/png, image/jpg, image/jpeg, image/apng, image/gif, image/bmp"
        className="alsc-form-item-uploader"
        disabled={disabled}
        limit={1}
        value={value || []}
        onRemove={(file) => {
          const newVal = (value || []).slice(0);
          newVal.splice(newVal.indexOf(file), 1);
          onChange(newVal);
        }}
        beforeUpload={beforeUpload}
      />
      <div className="restriction_text">
        <div className="restrict_item">
          上传尺寸：
          {[
            imgOptions.width && `宽${imgOptions.width}px`,
            imgOptions.minWidth && `宽>${imgOptions.minWidth}px`,
            imgOptions.maxWidth && `宽<${imgOptions.maxWidth}px`,
            imgOptions.height && `高${imgOptions.height}px`,
            imgOptions.minHeight && `高>${imgOptions.minHeight}px`,
            imgOptions.maxHeight && `高<${imgOptions.maxHeight}px`,
          ]
            .filter((x) => !!x)
            .join(", ")}
        </div>
        <div className="restrict_item">上传格式：{imgOptions.accept}</div>
        {(imgOptions.minSize || imgOptions.maxSize) && (
          <div className="restrict_item">
            图片大小：
            {[
              imgOptions.minSize && `体积>${imgOptions.minSize}kb`,
              imgOptions.maxSize && `体积<${imgOptions.maxSize}kb`,
            ]
              .filter((x) => !!x)
              .join(", ")}
          </div>
        )}
      </div>
    </div>
  );
}

export default ImageUpload;
