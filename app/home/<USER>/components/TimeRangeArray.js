import React from "react";
import { Button } from "@alife/next";
import TimeRangeWidget from "@/utils/Form/src/components/widgets/TimeRangeWidget";

/**
 * @param {value} param0 string[] [`${startTime}-${endTime}`]
 * @param {onChange} param1 (value: string[]) => void
 * @returns JSX.Element
 */
const TimeRangeArray = (props) => {
  const { disabled, value, onChange } = props
  const _onValueChange = (v, i) => {
    const _value = [...value];
    _value[i] = v;
    onChange(_value);
  };

  const onRemove = (index) => {
    const _value = [...value];
    _value.splice(index, 1);
    onChange(_value);
  };

  const onAdd = () => {
    const _value = Array.isArray(value) ? [...value] : [];
    _value.push("");
    onChange(_value);
  };

  return (
    <div>
      {Array.isArray(value) && value.map((item, i) => {
        return (
          <div key={i} style={{ marginBottom: 10, display: "flex", alignItems: "center" }}>
            <TimeRangeWidget
              disabled={disabled}
              value={item}
              onChange={(v) => _onValueChange(v, i)}
            />
            <Button
              style={{ marginLeft: 10 }}
              disabled={disabled}
              onClick={() => onRemove(i)}
              text
              type="primary"
            >
              删除
            </Button>
          </div>
        );
      })}

      {!value || value.length < 3 ? (
        <Button disabled={disabled} onClick={onAdd} text type="primary">
          添加
        </Button>
      ) : null}
    </div>
  );
};

export default TimeRangeArray;
