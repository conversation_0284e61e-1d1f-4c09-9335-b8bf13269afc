import React from "react";
import { Button, TimePicker } from "@alife/next";
import { genId } from "../utils";

/**
 * @param {value} param0 TimeRange[] {key: number; startTime: string; endTime: string}[]
 * @param {onChange} param1 (value: TimeRange[]) => void
 * @returns JSX.Element
 */
const TimeRangeArray = ({ disabled, value = [], onChange }) => {
  const onAdd = () => {
    const _value = [...value];
    _value.push({
      key: genId(),
      startTime: "",
      endTime: "",
    });
    onChange(_value);
  };

  const onRemove = (key) => {
    const _value = [...value];
    onChange(_value.filter((_item) => _item.key !== key));
  };

  // 更新item
  const onItemChange = (curItem, type, time) => {
    const _value = [...value];
    onChange(
      _value.map((item) => {
        if (item.key === curItem.key) {
          item[type] = time;
        }
        return item;
      })
    );
  };

  return (
    <div>
      {value.map((item) => {
        return (
          <div key={item.key} className="time_range_list">
            <TimeRangeWidget
              disabled={disabled}
              value={item}
              onChange={(type, v) => onItemChange(item, type, v)}
            />
            <Button
              className="trl_remove_btn"
              disabled={disabled}
              onClick={() => onRemove(item.key)}
              text
              type="primary"
            >
              删除
            </Button>
          </div>
        );
      })}

      {value.length < 3 ? (
        <Button disabled={disabled} onClick={onAdd} text type="primary">
          添加
        </Button>
      ) : null}
    </div>
  );
};

export default TimeRangeArray;

function TimeRangeWidget(props) {
  const { placeholder, disabled, readonly, value, onChange, onBlur } = props;
  const { startTime, endTime } = value || {};

  return (
    <div className="range-time-picker">
      <TimePicker
        placeholder={placeholder}
        value={startTime}
        disabled={disabled}
        readOnly={readonly}
        defaultValue={""}
        onChange={(v) => {
          onChange("startTime", v);
        }}
        onBlur={onBlur}
      />
      <span className="separator">-</span>
      <TimePicker
        placeholder={placeholder}
        value={endTime}
        disabled={disabled}
        readOnly={readonly}
        defaultValue={""}
        onChange={(v) => {
          onChange("endTime", v);
        }}
        onBlur={onBlur}
      />
    </div>
  );
}
