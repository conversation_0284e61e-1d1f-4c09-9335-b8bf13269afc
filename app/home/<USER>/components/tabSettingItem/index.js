import React, { useEffect } from "react";
import { Button, Field, Form, Input, Select } from "@alife/next";

import {
  genId,
  XUAN_PIN_JI,
  SHANG_PIN_JU_LEI,
  selectionCommodityUrl,
  supplyTypeList,
  onItemDown as _onItemDown,
  onItemRemove as _onItemRemove,
  onItemUp as _onItemUp,
} from "../../utils";
import {
  CategorySelect,
  FixedPureSettingItem,
  FormItem,
  PoolIdsSelect,
} from "../index";

import "./style.scss";

const labelCol = { span: 4 };

const TabSettingItem = ({
  currentNum = 1,
  disabled,
  showDownBtn,
  showFixedPureDataSource,
  showRemoveBtn,
  showTopBrandIdList,
  showUpBtn,
  value = [],
  onChange,
  onItemDown,
  onItemRemove,
  onItemUp,
  onSaveField,
}) => {
  const field = Field.useField({
    values: {
      fixedPureDataSource: [],
      ...value,
    },
    onChange: (name, _value) => {
      if (name === "supplyType") {
        field.setValues({
          [name]: _value,
          poolIds: undefined,
          itemCat: undefined,
        });
      } else {
        field.setValue(name, _value);
      }
    },
  });

  useEffect(() => {
    onSaveField(field);
  }, []);

  const _onItemAdd = () => {
    const _fixedPureDataSource = [...field.getValue("fixedPureDataSource")];
    _fixedPureDataSource.push({
      key: genId(),
      recallNum: 1,
      supplyType: XUAN_PIN_JI,
    });
    field.setValue("fixedPureDataSource", _fixedPureDataSource);
  };

  return (
    <div className="tab_setting_item">
      <div className="tsi_title">
        <div>
          <div className="tsi_title_num">{currentNum}</div>
          {` ${field.getValue("tabName") || ""}`}{" "}
        </div>

        <div className="tsi_title_btns">
          {showUpBtn ? (
            <Button className="tsi_btn" onClick={onItemUp} text type="primary">
              上移
            </Button>
          ) : null}

          {showDownBtn ? (
            <Button
              className="tsi_btn"
              onClick={onItemDown}
              text
              type="primary"
            >
              下移
            </Button>
          ) : null}
          {showRemoveBtn ? (
            <Button onClick={onItemRemove} text type="primary">
              删除
            </Button>
          ) : null}
        </div>
      </div>
      <Form field={field}>
        <FormItem labelCol={labelCol} label="tab名称:" required>
          <Input
            disabled={disabled}
            value={field.getValue("tabName")}
            minLength={1}
            maxLength={8}
            name="tabName"
            placeholder="请输入tab名称, 最多8个字"
          />
        </FormItem>

        <FormItem
          labelCol={labelCol}
          label="供给来源:"
          required
          requiredMessage="供给来源不能为空"
        >
          <Select
            disabled={disabled}
            name="supplyType"
            style={{ width: "100%" }}
            dataSource={supplyTypeList}
            value={field.getValue("supplyType")}
          />
        </FormItem>

        {field.getValue("supplyType") === XUAN_PIN_JI ? (
          <FormItem
            labelCol={labelCol}
            label="选品集ID:"
            required
            requiredMessage={`选品集ID不能为空`}
            hasFeedback
          >
            <PoolIdsSelect
              disabled={disabled}
              name="poolIds"
              value={field.getValue("poolIds")}
            />
            <a href={selectionCommodityUrl[window.configEnv]} target="_blank">
              去创建
            </a>
          </FormItem>
        ) : null}

        {field.getValue("supplyType") === SHANG_PIN_JU_LEI ? (
          <FormItem
            labelCol={labelCol}
            label="商品类目:"
            required
            requiredMessage={`商品类目不能为空`}
            hasFeedback
          >
            <CategorySelect
              disabled={disabled}
              name="itemCat"
              value={field.getValue("itemCat")}
            />
          </FormItem>
        ) : null}

        {showTopBrandIdList ? (
          <FormItem labelCol={labelCol} label="指定品牌置顶ID:" required>
            <Input.TextArea
              disabled={disabled}
              style={{ width: "302px" }}
              name="topItemBrandIdListStr"
              placeholder={`请输入指定品牌置顶ID,多个指定品牌置顶ID使用英文逗号隔开,至多20个`}
              value={field.getValue("topItemBrandIdListStr")}
            />
          </FormItem>
        ) : null}

        {showFixedPureDataSource ? (
          <>
            <FormItem labelCol={labelCol} label="定坑设置(1-10):">
              <div className="second_tab_setting_list">
                {field.getValue("fixedPureDataSource") &&
                  field.getValue("fixedPureDataSource").map((item, i) => {
                    if (!item) {
                      return null;
                    }
                    return (
                      <FixedPureSettingItem
                        disabled={disabled}
                        currentNum={i + 1}
                        key={item.key}
                        value={item}
                        showUpBtn={
                          i !== 0 &&
                          field.getValue("fixedPureDataSource").length > 1 &&
                          !disabled
                        }
                        showDownBtn={
                          i !==
                            field.getValue("fixedPureDataSource").length - 1 &&
                          field.getValue("fixedPureDataSource").length > 1 &&
                          !disabled
                        }
                        showRemoveBtn={!disabled}
                        onItemDown={() =>
                          _onItemDown(
                            item.key,
                            field.getValue("fixedPureDataSource"),
                            (_value) => {
                              field.setValue("fixedPureDataSource", _value);
                            }
                          )
                        }
                        onItemRemove={() =>
                          _onItemRemove(
                            item.key,
                            field.getValue("fixedPureDataSource"),
                            (_value) => {
                              field.setValue("fixedPureDataSource", _value);
                            }
                          )
                        }
                        onItemUp={() =>
                          _onItemUp(
                            item.key,
                            field.getValue("fixedPureDataSource"),
                            (_value) => {
                              field.setValue("fixedPureDataSource", _value);
                            }
                          )
                        }
                        onChange={(val) => {
                          const _value = field
                            .getValue("fixedPureDataSource")
                            .map((_item) => {
                              if (_item.key === val.key) {
                                return val;
                              }
                              return _item;
                            });
                          field.setValue(`fixedPureDataSource`, _value);
                        }}
                      />
                    );
                  })}
              </div>
              {field.getValue("fixedPureDataSource").length < 10 &&
              !disabled ? (
                <Button onClick={_onItemAdd} text type="primary">
                  添加
                </Button>
              ) : null}
            </FormItem>
          </>
        ) : null}
      </Form>
    </div>
  );
};

export default TabSettingItem;
