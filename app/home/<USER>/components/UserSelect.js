import React, { useState, useEffect, useRef } from "react"
import { Select, Message } from "@alife/next"
import { onRequestError } from '@/utils/api'
import { getUserTagGroupIdsList } from '@/utils/api/supermarketChannelBigStore'

const getUserList = async (keyword) => {
  const data = await getUserTagGroupIdsList({ keyword })
  return Array.isArray(data) ? data.map((item) => ({
    label: `${item.groupName}(${item.groupId})`,
    value: item.groupId
  })) : []
}

function UserSelect(props) {
  const { onChange, value, ...rest } = props
  const isFirst = useRef(false);
  const [dataSource, setDataSource] = useState()

  useEffect(() => {
    if (isFirst.current) {
      return;
    }
    if (Array.isArray(value) && value.length > 0) {
      const promiseList = [];
      value.map((item) => {
        promiseList.push(
          getUserList(item)
        );
      });
      Promise.all(promiseList)
        .then((res) => {
          const _dataSource = res.map((item) => item ? item[0] : undefined);
          isFirst.current = true;
          setDataSource(_dataSource.filter(Boolean));
        })
        .catch((e) => {
          onRequestError(e);
        });
    }
  }, [value]);

  const onSearch = (keyword) => {
    getUserList(keyword).then((data) => {
      setDataSource(data)
    })
  }

  const handleChange = (val) => {
    if (val && val.length > 5) {
      Message.notice('最多可选5个用户')
    } else {
      onChange && onChange(val)
    }
  }



  return (
    <Select
      style={{ width: '100%' }}
      {...rest}
      mode="multiple"
      showSearch
      placeholder="请输入用户ID，支持输入最多5个"
      value={value || []}
      onChange={handleChange}
      onSearch={onSearch}
      dataSource={dataSource}

    />
  )
}

export default UserSelect