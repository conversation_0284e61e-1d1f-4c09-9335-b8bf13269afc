import React from "react";
import { Button, NumberPicker, Select } from "@alife/next";

import {
  XUAN_PIN_JI,
  SHANG_PIN_JU_LEI,
  selectionCommodityUrl,
  supplyTypeList,
} from "../utils";

import { CategorySelect, FormItem, PoolIdsSelect } from "./index";

const labelCol = { span: 4 };

const FixedPureSettingItem = ({
  catDataSource,
  currentNum,
  disabled,
  value,
  showUpBtn,
  showDownBtn,
  showRemoveBtn,
  onItemDown,
  onItemUp,
  onItemRemove,
  onChange,
}) => {
  const onItemChange = (key, v) => {
    onChange({
      ...value,
      [key]: v,
    });
  };

  return (
    <div className="tab_setting_item">
      <div className="tsi_title">
        <div className="tsi_title_num">{currentNum}</div>

        <div className="tsi_title_btns">
          {showUpBtn ? (
            <Button className="tsi_btn" onClick={onItemUp} text type="primary">
              上移
            </Button>
          ) : null}

          {showDownBtn ? (
            <Button
              className="tsi_btn"
              onClick={onItemDown}
              text
              type="primary"
            >
              下移
            </Button>
          ) : null}
          {showRemoveBtn ? (
            <Button onClick={onItemRemove} text type="primary">
              删除
            </Button>
          ) : null}
        </div>
      </div>
      <FormItem labelCol={labelCol} label="召回个数:" required>
        <NumberPicker
          disabled={disabled}
          value={value.recallNum}
          style={{ width: "140px" }}
          min={1}
          max={9999999999}
          precision={0}
          placeholder="请输入召回个数"
          onChange={(val) => {
            onItemChange("recallNum", val);
          }}
        />
      </FormItem>
      <FormItem
        labelCol={labelCol}
        label="供给来源:"
        required
        requiredMessage="供给来源不能为空"
      >
        <Select
          disabled={disabled}
          style={{ width: "100%" }}
          dataSource={supplyTypeList}
          value={value.supplyType}
          onChange={(val) => {
            onItemChange("supplyType", val);
          }}
        />
      </FormItem>

      {value.supplyType === XUAN_PIN_JI ? (
        <FormItem
          labelCol={labelCol}
          label="选品集ID:"
          required
          requiredMessage={`选品集ID不能为空`}
          hasFeedback
        >
          <PoolIdsSelect
            disabled={disabled}
            value={value.poolIds}
            onChange={(val) => {
              onItemChange("poolIds", val);
            }}
          />
          <a href={selectionCommodityUrl[window.configEnv]} target="_blank">
            去创建
          </a>
        </FormItem>
      ) : null}

      {value.supplyType === SHANG_PIN_JU_LEI ? (
        <FormItem
          labelCol={labelCol}
          label="商品类目:"
          required
          requiredMessage={`商品类目不能为空`}
          hasFeedback
        >
          <CategorySelect
            disabled={disabled}
            dataSource={catDataSource}
            value={value.itemCat}
            onChange={(val) => {
              onItemChange("itemCat", val);
            }}
          />
        </FormItem>
      ) : null}
    </div>
  );
};

export default FixedPureSettingItem;
