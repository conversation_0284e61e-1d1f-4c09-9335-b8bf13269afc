import React from 'react';
import SimpleTable from "@/components/SimpleTable";
import {FORMAT, formatTimeStamp} from "@/utils/time";

const  traverseChannel = (ret, list) => {
  list.forEach((listItem) => {
    ret.push({
      value: listItem.value,
      label: listItem.label
    });
    if (listItem.children) {
      traverseChannel(ret, listItem.children);
    }
  })
}

export const getChannelList = (list) =>{
  const { channelListMap } = JSON.parse(localStorage.getItem("deliveryData"));
  let ret = [];
  let strRet = [];
  if (list.length) {
    traverseChannel(ret, channelListMap);
    list.forEach((listItem) => {
      ret.forEach((retItem) => {
        if(listItem === retItem.value) {
          strRet.push(retItem.label);
        }
      })
    })
  }
  return strRet.length > 0 ? strRet.join(',') : '--';

}

export const  getChannelLabel = (value) =>{
  let { labelListMap } = JSON.parse(localStorage.getItem("deliveryData"))
  if (value.length) {
    let result = labelListMap.filter((item) => value.indexOf(item.value) >= 0).map((item) => item.label).join(' , ')
    return result;
  } else {
    return '--';
  }
}


export const  getCity = (value) => {
  if (value) {
    let result = value.map((v) => v.label).join(",");
    return result;
  } else {
    return '--';
  }
}

/**
 * 渲染榜单详情
 * */
export const handleField = (key, info, fieldInfo) => {
  let result;
  let value = info[key];
  if (value || value === 0) {
    let widgetType = fieldInfo["x-ui-widget"];
    if (widgetType == "img-upload" && value != "") { //图片类型，展示图片
      let {width, height} = fieldInfo["x-ui-validate"];
      result = <img src={value} style={{width: '100%', height: 'auto'}}/>;
    } else if (widgetType == 'DateTimeWidget') {
      result = formatTimeStamp(value, FORMAT.TIMETwo);
    } else if (widgetType == "img-upload-new" && value != "") {
      let {url} = JSON.parse(value);
      result = <img src={url} style={{width: '100%', height: 'auto'}}/>;
    } else if (fieldInfo['x-ui-valueLabel'] && fieldInfo['x-ui-valueLabel'][value]) {
      return fieldInfo['x-ui-valueLabel'][value];
    } else if ((widgetType == 'select' || widgetType == 'checkboxes' || widgetType == 'CheckboxesWidget') && fieldInfo['type'] == 'array') { //下拉框多选的功能
      return fieldInfo["items"]["enum"].map((v, index) => {
        return value.includes(v) && fieldInfo["items"]["enumNames"][index];
      }).filter((v) => !!v).join(',');
    } else if (widgetType == 'dynamicCascadeSelectWidget') {
      let {canGetThreeLevel} = fieldInfo;
      value = ((canGetThreeLevel) ? value.hasSelectedDisplay : value ) || [];
      const labelGroup = value.map((o) => o.label) || [];
      result = labelGroup.join(",");
    } else if (widgetType == 'string-array') {
      result = value.join(",");
    } else if (fieldInfo.type === 'array' && fieldInfo.items && fieldInfo.items.type === 'object') {
      if (Array.isArray(value)) {
        // 遍历 value ，根据 value 数量决定有几个内部的表格
        const inSideList = value.map((v) => {
          // 渲染内部表格框架
          return (
            <SimpleTable>
              {Object.keys(v).map((k) => {
                // 渲染内部表格 item 项
                return (
                  <SimpleTable.Item
                    key={k}
                    label={fieldInfo.items.properties[k].title}
                  >
                    {/* 渲染 item 对应的值 */}
                    {handleField(k, v, fieldInfo.items.properties[k])}
                  </SimpleTable.Item>
                );
              })}
            </SimpleTable>
          );
        });
        result = (
          <div>
            {inSideList.map((v, index) => {
              return <div key={index}>{v}</div>;
            })}
          </div>
        );
      }
    } else {
      result = value;
    }
  } else {
    result = '--';
  }
  console.log(key, info, fieldInfo,value,result);
  return result;
}

/**
 * 打平一个对象，数组类型保持不变，以免出现分拆数组的情况
 * */
export const flattenObject = (obj) => {
  let flattened = {};

  function flatten(obj) {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && !Array.isArray(obj[key]) && obj[key] !== null) {
          flatten(obj[key], key);
        } else {
          flattened[key] = obj[key];
        }
      }
    }
  }

  flatten(obj);

  return flattened;
}


/**
 * 接收detail接口的数据，抹平itemRule
 * 抹平价格字段的数据结构：后端返回的min、max格式
 * @param schedule
 * @param detailData
 * @returns {{}}
 */
export const filterResponseDTO = (schedule,detailData) =>{
  const property = schedule.detail.properties;
  const flattenedDetailData = {
    ...detailData,
    ...detailData.itemRule,
    ...(detailData.extMap || {}) // extMap 里边只存在字符串
  };
  const responseDTO = {};
  Object.keys(flattenedDetailData).forEach(key => {
    const value = flattenedDetailData[key] || '';
    const widgetType = property[key] && property[key]['x-ui-widget'] ? property[key]['x-ui-widget'] : '';
    const curItem = property[key];

    if (widgetType && widgetType == 'price-range') {
      responseDTO[key] = value ? `${value.min}-${value.max}` : "";
    } else if (curItem && curItem.tryDeserializeAsJsonArrayString) {
      try {
        // extMap 与 detailData 中字段冲突的情况可能会把 字符串类型的数据覆盖掉原有数据类型，需要重新 JSON 转换回来
        responseDTO[key] = JSON.parse(value);
      } catch(e) {
        console.error('e=======filterResponseDTO JSON化解析错误', e);
      }
    } else {
      responseDTO[key] = value;
    }
  })
  return responseDTO;
}

/**
 * 获取榜单schema的常用参数
 * */
export const baseQueryRankSchemaDTO = {
  bizId: 1,
  bizType: 1
}


/**
 * 获取场景schema的常用参数
 * */
export const baseQuerySceneSchemaDTO = {
  bizId: 1,
  bizType:2
}
