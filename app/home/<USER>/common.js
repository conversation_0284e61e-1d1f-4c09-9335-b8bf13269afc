import React from 'react';
import {FORMAT, formatTimeStamp} from "@/utils/time";

const  traverseChannel = (ret, list) => {
  list.forEach((listItem) => {
    ret.push({
      value: listItem.value,
      label: listItem.label
    });
    if (listItem.children) {
      traverseChannel(ret, listItem.children);
    }
  })
}

export const getChannelList = (list) =>{
  const { channelListMap } = JSON.parse(localStorage.getItem("deliveryData"));
  let ret = [];
  let strRet = [];
  if (list.length) {
    traverseChannel(ret, channelListMap);
    list.forEach((listItem) => {
      ret.forEach((retItem) => {
        if(listItem === retItem.value) {
          strRet.push(retItem.label);
        }
      })
    })
  }
  return strRet.length > 0 ? strRet.join(',') : '--';

}

export const  getChannelLabel = (value) =>{
  let { labelListMap } = JSON.parse(localStorage.getItem("deliveryData"))
  if (value.length) {
    let result = labelListMap.filter((item) => value.indexOf(item.value) >= 0).map((item) => item.label).join(' , ')
    return result;
  } else {
    return '--';
  }
}


export const  getCity = (value) => {
  if (value) {
    let result = value.map((v) => v.label).join(",");
    return result;
  } else {
    return '--';
  }
}

/**
 * 渲染榜单详情
 * */
export const handleField = (key, info, fieldInfo) => {
  let result;
  let value = info[key];
  if (value || value === 0) {
    let widgetType = fieldInfo["x-ui-widget"];
    if (widgetType == "img-upload" && value != "") { //图片类型，展示图片
      let {width, height} = fieldInfo["x-ui-validate"];
      result = <img src={value} style={{width: '100%', height: 'auto'}}/>;
    } else if (widgetType == 'DateTimeWidget') {
      result = formatTimeStamp(value, FORMAT.TIMETwo);
    } else if (widgetType == "img-upload-new" && value != "") {
      let {url} = JSON.parse(value);
      result = <img src={url} style={{width: '100%', height: 'auto'}}/>;
    } else if (fieldInfo['x-ui-valueLabel'] && fieldInfo['x-ui-valueLabel'][value]) {
      return fieldInfo['x-ui-valueLabel'][value];
    } else if ((widgetType == 'select' || widgetType == 'checkboxes' || widgetType == 'CheckboxesWidget') && fieldInfo['type'] == 'array') { //下拉框多选的功能
      return fieldInfo["items"]["enum"].map((v, index) => {
        return value.includes(v) && fieldInfo["items"]["enumNames"][index];
      }).filter((v) => !!v).join(',');
    } else {
      result = value;
    }
  } else {
    result = '--';
  }
  return result;
}

/**
 * 获取榜单schema的常用参数
 * */
export const baseQueryRankSchemaDTO = {
  bizId: 1,
  bizType: 1
}
