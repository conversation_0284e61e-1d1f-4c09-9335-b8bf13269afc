import React, { useRef, useEffect } from "react";
import { JSONEditor } from "vanilla-jsoneditor";

export default function JSONEdit(props) {
  const { json = "{}" } = props;
  const refContainer = useRef(null);
  const refEditor = useRef(null);

  useEffect(() => {
    refEditor.current = new JSONEditor({
      target: refContainer.current,
      props: {
        content: {
          json: JSON.parse(json),
        },
        mode: "text",
        readOnly: true,
        askToFormat: true,
        flattenColumns: false,
      },
    });
  }, []);

  return <div style={{ height: 700 }} ref={refContainer} />;
}
