import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Button,
  Field,
  Dialog,
  Table,
  Pagination,
  Icon,
} from "@alife/next";

import "./style.scss";
import * as api from "@/adator/api";
import { promisify } from "@/utils/others";
import { CascaderSelect } from "@alife/theme-nr-op";
import { deepCopy } from "../common";
import { Message } from "@alife/next";

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: {
    span: 17,
  },
  style: {
    width: "310px",
  },
};

export default function CategoryPreviewDialog(props) {
  const {
    groupOptions,
    scenePoolType,
    selectedCityIdList = [],
    tempPoolId = "",
    sourcePoolId = "",
    sceneGroup = "",
  } = props;
  const [dataSource, setDataSource] = useState([]);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(50);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [isSearch, setIsSearch] = useState(false);
  const [statisticsCategory, setStatisticsCategory] = useState([]); //类目统计数组
  const [selectedFakeKey, setSelectedFakeKey] = useState([]);
  const [isDeleted, setIsDeleted] = useState(false);
  const [deleteDataSource, setDeleteDataSource] = useState([]);
  const [requestSearch, setRequestSearch] = useState({
    //请求参数
    sceneCityId: [],
    catName: "",
  });

  let cityGroupOptions = []; //城市池能选中的城市
  try {
    const newcityList = groupOptions["scene_city_id"];
    newcityList.filter((item) => {
      if (item.children) {
        item.children.filter((cityItem) => {
          if (selectedCityIdList.indexOf(cityItem.value) > -1) {
            cityGroupOptions.push(cityItem);
          }
        });
      }
    });
  } catch {}

  // 列表树
  const columns = [
    { title: "场景名称", dataIndex: "sceneName", width: 100 },
    { title: "城市", dataIndex: "cityName", width: 100 },
    {
      title: "tgi",
      dataIndex: "tgi",
      width: 100,
      cell: (value, index, record) => {
        return Math.floor(Number(value) * 100) / 100;
      },
    },
    { title: "一级类目名称", dataIndex: "itemCat1Name", width: 150 },
    { title: "二级类目名称", dataIndex: "itemCat2Name", width: 150 },
    { title: "三级类目名称", dataIndex: "itemCat3Name", width: 150 },
    {
      title: "年增速",
      dataIndex: "annualGrowthRate",
      width: 100,
      cell: (value, index, record) => {
        if (value == "-1" || value == "999.99") {
          return "--";
        }
        return Math.floor(Number(value) * 10000) / 100 + "%";
      },
    },
    {
      title: "场景订单占比",
      dataIndex: "orderProportion",
      width: 150,
      cell: (value, index, record) => {
        return Math.floor(Number(value) * 10000) / 100 + "%";
      },
    },
    {
      title: "网格覆盖率",
      dataIndex: "gridCoverage",
      width: 120,
      cell: (value, index, record) => {
        return Math.floor(Number(value) * 10000) / 100 + "%";
      },
    },
    { title: "商品数", dataIndex: "itemNum", width: 100 },
    { title: "门店数", dataIndex: "shopNum", width: 100 },
    {
      title: "操作",
      lock: "right",
      cell: (value, index, record) => {
        return (
          <div className="opbtns">
            <Button text onClick={() => goDeleteCategory(record)}>
              {isDeleted ? "恢复删除" : "删除"}
            </Button>
          </div>
        );
      },
      width: 100,
    },
  ];

  const viewField = Field.useField({
    onChange: (name, value) => {
      requestSearch[name] = value;
      setRequestSearch(requestSearch);
    },
  });

  // 获取商品类目预览数据
  useEffect(() => {
    getViewCategoryViewList(page, size, isSearch);
  }, [props.sceneFilterFieldBizModelList]);

  useEffect(() => {
    if (isDeleted) {
      getViewDeleteCategoryList();
    } else {
      getViewCategoryViewList(page, size, isSearch);
    }
    setSelectedFakeKey([]);
  }, [isDeleted]);

  //请求预览接口
  const getViewCategoryViewList = async (
    page = 1,
    size = 50,
    isSearch = false
  ) => {
    if (isSearch) {
      await promisify(viewField.validate)();
    }
    setIsLoading(true);
    setIsSearch(isSearch);
    const { sourcePoolId, sceneFilterFieldBizModelList, sceneGroup } = props;
    const { catName, sceneCityId } = requestSearch;
    try {
      let req = {
        sceneGroup,
        sceneFilterFieldList: sceneFilterFieldBizModelList,
        catName: catName.length > 0 ? catName : null,
        pageSize: size,
        pageIndex: page,
        sceneCityId: sceneCityId.length > 0 ? sceneCityId.join(",") : null,
        scenePoolType,
        bizId: tempPoolId,
      };
      await getCategoryOfCommodities(req);
      let request = api.queryPageSceneList;
      let resp = await request(req);
      let newData = resp.data.data.data.filter((v) => v.status == 1);
      newData = newData.map((item, index) => {
        item.fakeKey = index;
        return item;
      });
      setDataSource(newData);
      setSelectedFakeKey([])
      setTotal(resp.data.data.totalCount);
      setIsLoading(false);
    } catch (error) {
      console.log(error);
      api.onRequestError(error);
      setIsLoading(false);
    }
  };

  //请求商品类目list
  const getCategoryOfCommodities = async (requestParams) => {
    try {
      let request = api.querySceneCatCount;
      let resp = await request({ ...requestParams });
      setStatisticsCategory(resp.data.data.data);
    } catch (error) {
      console.log(error);
      api.onRequestError(error);
    }
  };

  // 删除or恢复品类
  const goDeleteCategory = async (record) => {
    try {
      let sceneRule = [];
      selectedFakeKey.forEach((item) => {
        let newDataSource = []
        if (isDeleted) {
          newDataSource = deleteDataSource
        }else{
          newDataSource = dataSource
        }
        let deepCopyData = deepCopy(newDataSource[item]);
        delete deepCopyData.fakeKey;
        sceneRule.push(deepCopyData);
      });
      let req = {
        bizId: tempPoolId,
        sceneGroup: props.sceneGroup,
        addOrDelete:  1,
        tabType: isDeleted ? 4 : 3,
        sceneRuleDTOList: record ? [record] : sceneRule,
      };
      let request = api.sceneManualUpdate;
      let resp = await request(req);
      if (resp.data.code == 200) {
        if (isDeleted) {
          getViewDeleteCategoryList();
        } else {
          getViewCategoryViewList(page, size, isSearch);
        }
        !record && setSelectedFakeKey([])
        Message.success(
          `${record ? "" : "批量"}${isDeleted ? "恢复删除成功" : "删除成功"}`
        );
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 获取删除品类列表
  const getViewDeleteCategoryList = async () => {
    try {
      if (!isLoading) {
        setIsLoading(true);
        let request = api.querySceneManual;
        let resp = await request({
          sceneGroup: sceneGroup,
          sourcePoolId: sourcePoolId,
          bizId: tempPoolId,
          pageSize: 500,
          pageIndex: 1,
          tabType:4
        });
        if (resp.data.code == "200") {
          const newData = resp.data.data.data.map((item, index) => {
            item.fakeKey = index;
            return item;
          });
          setDeleteDataSource(newData);
          setSelectedFakeKey([])
        }
        setIsLoading(false);
      }
    } catch (error) {
      console.log(error);
      api.onRequestError(error);
    }
  };

  // 页面更改
  const onPageChange = (page) => {
    setPage(page);
    getViewCategoryViewList(page, size, true);
  };

  // 页面数量改变
  const onPageSizeChange = (size) => {
    setSize(size);
    getViewCategoryViewList(page, size, true);
  };

  // 下一步
  const nextStep = () => {
    props.goStep(2);
    props.setCategoryPreview(false);
  };

  // 查询预览结果
  const searchGoodList = () => {
    setIsSearch(true);
    setPage(1);
    getViewCategoryViewList(page, size, true);
  };

  return (
    <Dialog
      title={
        !isDeleted ? (
          <>
            {
              <div className="title-sum">
                <div className="title-sum-left">初选结果数量：</div>
                <div className="title-sum-right">
                  {statisticsCategory.length > 0
                    ? statisticsCategory.map((item) => {
                        return (
                          <div className="col2">
                            <b>{item.cityName}:&nbsp;</b>
                            {`一级类目${Number(
                              item.cat1Num
                            )}个，二级类目${Number(
                              item.cat2Num
                            )}个，三级类目${Number(
                              item.cat3Num
                            )}个，商品${Number(item.itemNum)}个;`}
                            &nbsp;
                          </div>
                        );
                      })
                    : "0个"}
                </div>
              </div>
            }
          </>
        ) : (
          <div className="delele-title">
            <Icon type={"left"} />
            <Button
              className="btn-back"
              text
              onClick={() => setIsDeleted(false)}
            >
              返回
            </Button>
            <span>当前位置：已删品类</span>
          </div>
        )
      }
      visible={props.categoryPreview}
      top={10}
      height={`${document.body.clientHeight * 0.84}px`}
      width={`${document.body.clientWidth * 0.67}px`}
      className={"view-dialog"}
      onClose={() => props.setCategoryPreview(false)}
      footer={
        isDeleted ? (
          <div className="footer-buttons">
            <Button onClick={() => setIsDeleted(false)}>返回</Button>
          </div>
        ) : (
          <div className="footer-buttons">
            <Button
              className="btn-delete-list"
              onClick={() => setIsDeleted(true)}
            >
              已删品类
            </Button>
            <Button
              className="btn-cancel"
              onClick={() => props.setCategoryPreview(false)}
            >
              取消
            </Button>
            <Button type={"primary"} onClick={() => nextStep()}>
              下一步
            </Button>
          </div>
        )
      }
    >
      <div className="filter-panel">
        <div className="batchDelete">
          <Icon type="prompt" style={{ color: "#FF7000" }} />
          已选<span>{selectedFakeKey.length}</span>项
          <Button
            type="primary"
            disabled={selectedFakeKey.length <= 0}
            onClick={() => {
              goDeleteCategory();
            }}
            style={{ marginLeft: "10px" }}
          >
            {isDeleted ? "批量恢复" : "批量删除"}
          </Button>
        </div>
        {!isDeleted && (
          <div className="rules">
            <Form
              field={viewField}
              inline={true}
              className="rules-views-form"
              labelAlign="left"
            >
              <FormItem label="所在城市" {...formItemLayout}>
                <CascaderSelect
                  className="sceneCity"
                  multiple
                  canOnlyCheckLeaf
                  showSearch
                  name="sceneCityId"
                  dataSource={
                    scenePoolType == 101
                      ? groupOptions["scene_city_id"]
                      : cityGroupOptions
                  }
                  style={{ width: "100%" }}
                ></CascaderSelect>
              </FormItem>
              <FormItem label="类目名称" {...formItemLayout}>
                <Input name="catName" placeholder="请输入类目名称" />
              </FormItem>
              <Button
                htmlType="submit"
                type="secondary"
                style={{ marginLeft: "10px" }}
                onClick={searchGoodList}
              >
                搜索
              </Button>
            </Form>
          </div>
        )}
      </div>
      <div
        style={{
          overflow: "auto",
          height: `${document.body.clientHeight * 0.84 - 250}px`,
        }}
      >
        {isDeleted ? (
          <Table
            loading={isLoading}
            dataSource={deleteDataSource}
            hasBorder={false}
            primaryKey="fakeKey"
            rowSelection={{
              onChange: (e) => {
                setSelectedFakeKey([...e]);
              },
              selectedRowKeys: selectedFakeKey,
              columnProps: () => {
                return {
                  lock: "left",
                  width: 50,
                  align: "center",
                };
              },
            }}
          >
            {columns.map((e, idx) => {
              return <Table.Column {...e} key={idx} />;
            })}
          </Table>
        ) : (
          <Table
            loading={isLoading}
            dataSource={dataSource}
            hasBorder={false}
            primaryKey="fakeKey"
            rowSelection={{
              onChange: (e) => {
                setSelectedFakeKey([...e]);
              },
              selectedRowKeys: selectedFakeKey,
              columnProps: () => {
                return {
                  lock: "left",
                  width: 50,
                  align: "center",
                };
              },
            }}
          >
            {columns.map((e, idx) => {
              return <Table.Column {...e} key={idx} />;
            })}
          </Table>
        )}
        {!isDeleted && (
          <Pagination
            popupProps={{ align: "bl tl" }}
            pageSizeList={[50, 100]}
            shape="arrow-only"
            style={{ float: "right", marginTop: "10px" }}
            current={page}
            total={total}
            pageSize={size}
            onChange={onPageChange}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
            onPageSizeChange={onPageSizeChange}
          />
        )}
      </div>
    </Dialog>
  );
}
