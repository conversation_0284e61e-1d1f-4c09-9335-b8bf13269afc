import React, { useEffect, useState } from "react";
import { Balloon, Button, Checkbox, Grid, Icon, Tab } from "@alife/next";
import "./style.scss";
import { poolFiltersConfig } from "@/home/<USER>/common/config";
import { deepCopy } from "@/home/<USER>/common";
const { Row, Col } = Grid;

export default function VisibilityScreenCriteria(props) {
  let newPoolFiltersConfig = [];
  newPoolFiltersConfig = deepCopy(poolFiltersConfig);
  const [selectedField, setSelectedField] = useState([]); //圈选规则选中的所有复选框
  const [selectedAllField, setSelectedAllField] = useState([]); //当前选中的所有复选框
  const [tabidx, setTabidx] = useState("baseInfo");
  const [visibilityRules, setVisibilityRules] = useState(
    props.visibilityRules || []
  );
  const [visibilityAllRules, setVisibilityAllRules] = useState([]);
  const [groupOptions, setGroupOptions] = useState(props.groupOptions || {});
  const [fieldMap, setFieldMap] = useState({});

  const onTabChange = (value) => {
    setTabidx(value);
    const element = document.getElementById(value);
    element.scrollIntoView(true);
  };

  useEffect(() => {
    // 如果不是第一次
    if (props.visibilityRules.length > 0) {
      props.visibilityRules.map((item) => {
        selectedAllField.push(item.filterFieldId);
      });
      Object.keys(groupOptions["fieldMap"]).map((key) => {
        groupOptions["fieldMap"][key].map((item) => {
          visibilityAllRules.push(item);
        });
        setVisibilityAllRules(visibilityAllRules);
      });
      setSelectedAllField(selectedAllField);
    } else {
      // 如果是第一次进来从state中获取圈选规则中的所有筛选项并打平
      Object.keys(groupOptions["fieldMap"]).map((key) => {
        groupOptions["fieldMap"][key].map((item) => {
          visibilityAllRules.push(item);
          visibilityRules.push(item);
          selectedAllField.push(item.filterFieldId);
        });
        setSelectedAllField(selectedAllField);
        setVisibilityRules(visibilityRules);
        setVisibilityAllRules(visibilityAllRules);
      });
    }

    // 缓存圈选规则选择的标签
    if (props.effectRules && props.effectRules.length > 0) {
      props.effectRules.forEach((item) => {
        selectedField.push(item.filterFieldId);
      });
      setSelectedField(selectedField);
    }
    setFieldMap(groupOptions["fieldMap"]);
  }, []);

  /*勾选复选框*/
  const checkField = (checked, event, item) => {
    if (checked) {
      selectedAllField.push(item);
      setSelectedAllField(Array.from(new Set(selectedAllField)));
    } else {
      delete selectedAllField[selectedAllField.indexOf(item)];
      setSelectedAllField(selectedAllField.filter((d) => d));
    }
  };

  /**
   * 保存
   **/
  const saveDetail = async () => {
    // 洗数据
    let newVisibilityRules = [];
    visibilityAllRules.map((item) => {
      if (selectedAllField.includes(item.filterFieldId)) {
        const newItem = deepCopy(item);
        newItem.filterFieldValue = "";
        newVisibilityRules.push(newItem);
      }
    });
    setVisibilityRules(newVisibilityRules);
    props.getVisibilityRules(newVisibilityRules);
    if (props.isEdit) {
      props.updatePool();
    } else {
      props.savePool();
    }
  };

  /**
   * 保存并发布
   **/
  const noPreview = async () => {
    // 洗数据
    let newVisibilityRules = [];
    visibilityAllRules.map((item) => {
      if (selectedAllField.includes(item.filterFieldId)) {
        const newItem = deepCopy(item);
        newItem.filterFieldValue = "";
        newVisibilityRules.push(newItem);
      }
    });
    setVisibilityRules(newVisibilityRules);
    props.getVisibilityRules(newVisibilityRules);
    if (props.isEdit) {
      props.updatePool(true);
    } else {
      props.submitPool();
    }
  };

  // 上一步
  const previousStep = async () => {
    let newVisibilityRules = [];
    visibilityAllRules.map((item) => {
      if (selectedAllField.includes(item.filterFieldId)) {
        const newItem = deepCopy(item);
        newItem.filterFieldValue = "";
        newVisibilityRules.push(newItem);
      }
    });
    setVisibilityRules(newVisibilityRules);
    props.getVisibilityRules(newVisibilityRules);
    props.goStep(2);
  };

  let group = [
    { value: "baseInfo", label: "基本信息" },
    { value: "skuStore", label: "门店信息" },
    { value: "skuTrade", label: "交易信息" },
    { value: "scene", label: "场景信息" },
    { value: "skuMarket", label: "营销信息" },
  ];

  return (
    <>
      <div className="label-rules">
        <h3 className="header">4. 选择运营可见筛选条件</h3>
        <Row className="tab-content-group">
          <Col className="left" span={24}>
            <Tab onChange={onTabChange} activeKey={tabidx}>
              {group.map((o) => {
                return <Tab.Item title={o.label} key={o.value}></Tab.Item>;
              })}
            </Tab>
            <div className="check-list">
              {selectedAllField &&
                group.map((o) => {
                  return (
                    <div key={o.value}>
                      {fieldMap[o.value] && <p id={`${o.value}`}>{o.label}</p>}
                      {fieldMap[o.value] &&
                        fieldMap[o.value].map((v) => {
                          return (
                            <Checkbox
                              key={v.filterFieldId}
                              value={v.filterFieldId}
                              checked={selectedAllField.includes(
                                v.filterFieldId
                              )}
                              disabled={selectedField.includes(v.filterFieldId)}
                              onChange={(checked, event) => {
                                checkField(checked, event, v.filterFieldId);
                              }}
                            >
                              {v.filterFieldLabel}{" "}
                              {v.filterFieldDesc && (
                                <Balloon.Tooltip
                                  align="t"
                                  trigger={
                                    <Icon
                                      type="help"
                                      style={{ color: "#CCCCCC" }}
                                      size={"inherit"}
                                    />
                                  }
                                >
                                  {v.filterFieldDesc}
                                </Balloon.Tooltip>
                              )}
                            </Checkbox>
                          );
                        })}
                    </div>
                  );
                })}
            </div>
          </Col>
        </Row>
      </div>
      <div className="btn-panel">
        <div className="next-step">
          <Button onClick={() => previousStep()}>上一步</Button>
          <Button type="primary" onClick={() => saveDetail()}>
            保存
          </Button>
          <Button type="primary" onClick={() => noPreview()}>
            保存并发布
          </Button>
        </div>
      </div>
    </>
  );
}
