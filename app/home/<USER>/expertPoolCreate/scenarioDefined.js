import React, { useEffect, useState } from "react";
import * as api from "@/adator/api";
import {
  Form,
  Checkbox,
  Radio,
  Input,
  Button,
  Field,
  Grid,
  Dialog,
  Message,
  Balloon,
  Icon,
  Collapse,
} from "@alife/next";
import { CascaderSelect } from "@alife/theme-nr-op";
import { validatorMap, filtersConfigMap } from "@/home/<USER>/common/config";
import { RangeNumberInput } from "@/home/<USER>/common/components";
import { deepCopy } from "@/home/<USER>/common";
import { promisify } from "@/utils/others";

const { Row, Col } = Grid;
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const Panel = Collapse.Panel;
const CheckboxGroup = Checkbox.Group;
const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: {
    span: 21,
  },
  style: {
    width: "100%",
  },
};
const percentage = [
  "scene_annual_growth_rate",
  "scene_grid_coverage",
  "scene_order_proportion",
];

export default function ScenarioDefined(props) {
  // 底池规则类型 1场景底池 2自定义底池 默认1
  const [scenarioRulesType, setScenarioRulesType] = useState(
    props.customRuleType || 1
  );
  // 场景底池底池规则数据源
  const [defingConditions, setDefingConditions] = useState(
     props.scenarioRules || []
  );
  const [haveDefingConditions, setHaveDefingConditions] = useState(true);
  // 自定义底池底池规则数据源
  const [customizeDefingConditions, setCustomizeDefingConditions] = useState(
    props.customizeScenarioRules || []
  );
  const [haveCustomizeDefingConditions, setHaveCustomizeDefingConditions] = useState(true);
  // 底池规则树,cityId:底池城市 rules:底池规则
  const [cityList, setCityList] = useState([]);
  // 所选城市数据来源
  const [cityDataSource, setCityDataSource] = useState([]);
  // 当前底池规则选中的城市id
  const [cityIdList, setCityIdList] = useState([]);
  // 能否新增底池规则
  const [canAddPoolRule, setCanAddPoolRule] = useState(false);
  // 底池规则是否可用
  const [universalPool, setUniversalPool] = useState(
    props.scenePoolType == 101
  );
  //可支持查询的下拉
  const [searchOptions, setSearchOptions] = useState({}); 
  // 是否可以预览
  const [isView, setIsView] = useState(cityList.length <= 0);
  const field = Field.useField();
  const {
    init,
    setValue,
    getValue,
    getValues,
    remove,
    setError,
    setValues,
    reset,
  } = field;

  // 搜索所属品牌
  let searchTimeout;
  const handleSearch = (keyword) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      if (keyword) {
        api.queryBrand({ brandName: keyword }).then((data) => {
          const dataSource = data.data.data.map((item) => ({
            label: item.name,
            value: item.id,
          }));
          ctrlSearchOptions("sku_brand_id", dataSource);
        });
      } else {
        ctrlSearchOptions("sku_brand_id", []);
      }
    }, 800);
  };

  const ctrlSearchOptions = (key, data) => {
    let group = ["sku_brand_id"];
    if (group.includes(key)) {
      const originData = searchOptions[key] ? searchOptions[key] : [];
      const wholeData = data.concat(originData);
      searchOptions[key] = Array.from(new Set(wholeData));
    }
    setSearchOptions(deepCopy(searchOptions));
  };

  // 获取城市列表
  useEffect(() => {
    (async () => {
      await fetchCities();
    })();
  }, []);

  useEffect(() => {
    // 判断是否能新增底池规则
    if (cityList.length >= 20 || universalPool) {
      setCanAddPoolRule(true);
    } else {
      setCanAddPoolRule(false);
    }
    setIsView(cityList.length <= 0 || scenarioRulesType != 1);
  }, [cityList, scenarioRulesType]);

  useEffect(() => {
    // 对数据进行初始化
    if (props.scenarioDefined.length > 0) {
      // 重置表单数据
      setValues({});
      reset();
      const newScenarioDefined = deepCopy(props.scenarioDefined);
      let canSetRuleType = true;
      let newCityList = newScenarioDefined.filter((item, index) => {
        setValue(`city_${index}`, item.cityId);
        setCityIdList([...cityIdList, ...item.cityId]);
        item.rules = item.rules.filter((ruleItem) => {
          if (ruleItem.costomRuleType && canSetRuleType) {
            setScenarioRulesType(ruleItem.costomRuleType);
            canSetRuleType = false;
          }
          // 解析筛选项值
          if (
            ["rangeNumberInput"].includes(ruleItem.filterFieldComponentType)
          ) {
            if (percentage.includes(ruleItem.filterFieldId)) {
              const newFilterFieldValue = JSON.parse(ruleItem.filterFieldValue);
              ruleItem.filterFieldValue = {
                start: Number(newFilterFieldValue.start) * 100 || null,
                end: Number(newFilterFieldValue.end) * 100 || null,
              };
            } else {
              ruleItem.filterFieldValue = JSON.parse(ruleItem.filterFieldValue);
            }
          }
          if (["selectSearch","multipleSelect","cascaderSelect","picStandard"].includes(ruleItem.filterFieldComponentType)) {
            if (ruleItem.filterFieldComponentType == "selectSearch") {
              searchOptions[ruleItem.filterFieldId] = JSON.parse(ruleItem.filterFieldValue)
            }
            if (!!ruleItem.filterFieldValue) {
              ruleItem.filterFieldValue = JSON.parse(ruleItem.filterFieldValue)
            }
            setValue(`${ruleItem.filterFieldId}_${index}`, true);
            setValue(
              `${ruleItem.filterFieldId}_${index}_value`,
              ruleItem.filterFieldValue.map(e => {return e.value} )
            );
            return ruleItem
          }
          if (["checkbox"].includes(ruleItem.filterFieldComponentType)) {
            if (!!ruleItem.filterFieldValue) {
              ruleItem.filterFieldValue = JSON.parse(ruleItem.filterFieldValue)
            }
          }
          if (["arrayInput"].includes(ruleItem.filterFieldComponentType)) {
            /* 输入框，以'，'隔开的json串 转为 数组的json串 */
            if (!!ruleItem.filterFieldValue) {
              ruleItem.filterFieldValue = JSON.parse(ruleItem.filterFieldValue)
            }
            setValue(`${ruleItem.filterFieldId}_${index}`, true);
            setValue(
              `${ruleItem.filterFieldId}_${index}_value`,
              ruleItem.filterFieldValue.toString()
            );
            return ruleItem;
          }
          setValue(`${ruleItem.filterFieldId}_${index}`, true);
          setValue(
            `${ruleItem.filterFieldId}_${index}_value`,
            ruleItem.filterFieldValue
          );
          return ruleItem;
        });
        return item;
      });
      setCityList(newCityList);
    } else if (
      // 通用会场初始化
      props.scenarioDefined.length == 0 &&
      cityList.length == 0 &&
      universalPool
    ) {
      setValue(`city_${0}`, ["all"]);
      cityList.push({ cityId: ["all"], rules: [] });
      setScenarioRulesType(1);
      setCityList(cityList);
      setIsView(cityList.length <= 0);
    }
  }, [props.scenarioDefined]);

  // 判断多维度与自定义维度是否可用
  useEffect(()=>{
    // 全国目前没有多维度数据，自定义维度可在第三步骤设置
    if (universalPool) {
      props.goStep(2)
      props.getCustomRuleType(2)
      setScenarioRulesType(2)
    }
    // 多维度无数据
    if (props.scenarioRules.length <=0) {
      setScenarioRulesType(2)
      props.getCustomRuleType(2);
      setHaveDefingConditions(false)
    }
    // 自定义底池数据
    if (props.customizeScenarioRules.length <=0 && scenarioRulesType == 2 && !props.isEdit) {
      setScenarioRulesType(1)
      props.getCustomRuleType(1);
      setHaveCustomizeDefingConditions(false)
    }
  },[props.scenarioRules,props.customizeScenarioRules])


  /*获取所在城市数据*/
  const fetchCities = async () => {
    try {
      let request = api.getCities;
      let resp = await request();
      let newCityDataSource = [...resp.data.data];
      if (universalPool) {
        newCityDataSource = [
          ...resp.data.data,
          { value: "all", label: "全国" },
        ];
      }
      setCityDataSource(newCityDataSource);
      let groupOptions = props.groupOptions;
      groupOptions["scene_city_id"] = newCityDataSource;
      groupOptions["store_city_id"] = newCityDataSource;
      props.getGroupOptions(groupOptions);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 文本输入类
  const handleChange = (value, item, index) => {
    let { filterFieldComponentType } = item;
    let newValue = value;
    if (filterFieldComponentType == "checkbox") {
      /* 复选框，转成json串 */
      if (value) {
        newValue = JSON.stringify(value);
      }
    } else if (filterFieldComponentType == "arrayInput") {
      /* 输入框，以'，'隔开的json串 转为 数组的json串 */
      const tmpValue = value.replace(/[\r\n]/g, "");
      if (tmpValue) {
        newValue = JSON.stringify(
          tmpValue.split(",").map((item) => item.replace(/(^\s*)|(\s*$)/g, ""))
        );
      }
    }
    // 设置当前页面的状态值
    cityList[index].rules.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = newValue;
    setValue(`${item.filterFieldId}_${index}_value`, value);
  };

  // 店铺评分类
  const handleRangeChange = (value, group, item, index) => {
    // 设置当前页面的状态值
    cityList[index].rules.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = { ...value };
    setValue(`${item.filterFieldId}_${index}_value`, { ...value });
  };

  // 默认input更改
  const inputChange = (value, item, index) => {
    cityList[index].rules.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = value;
    setValue(`${item.filterFieldId}_${index}_value`, value);
  };

  // 下拉多选
  const handleSelectChange = (value, data, item, index) => {
    const haveSelected = data.map((item) => {
      const { value, label, pos } = item;
      return {
        value,
        label,
        level: pos.split("-").length - 1,
      };
    });
    cityList[index].rules.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = JSON.stringify(haveSelected);
    setValue(`${item.filterFieldId}_${index}_value`, value);
  };

  // 底池规则渲染逻辑
  const switchItem = (item, index) => {
    const filterFieldComponentType = item.filterFieldComponentType;
    let filterItem = deepCopy(
      scenarioRulesType == 1 ? defingConditions : customizeDefingConditions
    ).filter((o) => o.filterFieldId == item.filterFieldId);
    const filterFieldExtend =
      filterItem && filterItem.length > 0
        ? deepCopy(
            scenarioRulesType == 1
              ? defingConditions
              : customizeDefingConditions
          ).filter((o) => o.filterFieldId == item.filterFieldId)[0]
            .filterFieldExtend
        : {};

    let attrs = {
      dataSource:
        item.filterFieldDataType == 1 ? JSON.parse(item.filterFieldData) : props.groupOptions[item.filterFieldId] || [],
      value: getValue(`${item.filterFieldId}_${index}_value`),
      name: `${item.filterFieldId}_${index}_value`,
    };
    let placeholderName = "英文逗号隔开，最多100个";
    if (item.validatorValue == "nameSeperatedRequired") {
      placeholderName = "英文逗号隔开，最多30个";
    }
    let defaultRangeExtend = { precision: 1, step: 0.01 };
    if (filterFieldComponentType == "rangeNumberInput" && filterFieldExtend) {
      defaultRangeExtend = JSON.parse(filterFieldExtend);
    }
    let result;
    switch (filterFieldComponentType) {
      case "arrayInput": //文本框
        // 设置校验规则，编辑等操作在后端没有校验规则字段，需自行匹配
        let arrayInputValidator = null;
        if (item.validatorValue) {
          arrayInputValidator = validatorMap[item.validatorValue];
        } else {
          filtersConfigMap.forEach((configItem) => {
            if (configItem.filterFieldId == item.filterFieldId) {
              if (configItem.validatorValue) {
                arrayInputValidator = validatorMap[configItem.validatorValue];
              }
            }
          });
        }
        result = (
          <Input.TextArea
            placeholder={placeholderName}
            {...field.init(`${item.filterFieldId}_${index}_value`, {
              rules: [
                {
                  ...attrs,
                },
                {
                  validator: (rule, value, callback) => {
                    if (typeof arrayInputValidator == "function") {
                      return arrayInputValidator(
                        rule,
                        value
                          ? value
                          : getValue(`${item.filterFieldId}_${index}_value`),
                        callback
                      );
                    }
                  },
                  trigger: ["onBlur"],
                },
              ],
              props: {
                onChange: (value) => handleChange(value, item, index),
              },
            })}
          />
        );
        break;
      case "rangeNumberInput": //店铺评分类
        result = (
          <RangeNumberInput
            {...attrs}
            {...defaultRangeExtend}
            onChange={(value, data) =>
              handleRangeChange(value, data, item, index)
            }
          />
        );
        break;
      case "radio": //单选框
        result = (
          <RadioGroup
            {...attrs}
            onChange={(value) => handleChange(value, item, index)}
          />
        );
        break;
      case "checkbox": //复选框
        result = (
          <CheckboxGroup
            {...attrs}
            onChange={(value) => handleChange(value, item, index)}
          />
        );
        break;
      case "multipleSelect": //下拉多选
      case "cascaderSelect": //下拉级联多选
      case "picStandard":
        result = (
          <CascaderSelect
            expandTriggerType={"hover"}
            {...attrs}
            {...field.init(`${item.filterFieldId}_${index}_value`, {
              props: {
                onChange: (value, data) =>
                  handleSelectChange(value, data, item, index),
              },
            })}
            showSearch
            style={{ width: "100%" }}
            multiple={true}
          />
        );
        break;
      case "selectSearch": //下拉多选
        result = (
          <CascaderSelect
            showSearch
            {...attrs}
            multiple={true}
            {...field.init(`${item.filterFieldId}_${index}_value`, {
              props: {
                onChange: (value, data) =>
                  handleSelectChange(value, data, item, index),
              },
            })}
            filterLocal={false}
            dataSource={
              searchOptions && searchOptions[item.filterFieldId]
                ? searchOptions[item.filterFieldId]
                : []
            }
            onSearch={(value) => handleSearch(value)}
            style={{ width: "100%" }}
          />
        );
        break;
      default:
        result = (
          <Input
            onChange={(value) => {
              inputChange(value, item, index);
            }}
          />
        );
        break;
    }
    return result;
  };

  // 底池规则选中逻辑
  const checkField = (ruleItem, ruleIndex, index) => {
    delete cityList[index].rules[ruleIndex];
    cityList[index].rules = cityList[index].rules.filter((d) => d);
    setCityList(cityList);
    setValue(`${ruleItem.filterFieldId}_${index}`, false);
  };

  // 新增底池规则
  const addBottomPoolRule = () => {
    setCityList([...cityList, { cityId: [], rules: [] }]);
  };

  // 过滤底池规则
  const filterCityList = () => {
    // 对不符合规则条件的进行过滤
    let rulesNotComplete = [];
    let rulesNotCompleteText = "";
    let selectedCityIdList = [];
    let postData = deepCopy(cityList);
    postData = postData.filter((item, index) => {
      if (item.rules.length == 0 || item.cityId.length == 0) {
        rulesNotComplete.push(index + 1);
      } else {
        if (item.rules.length > 0) {
          item.rules = item.rules.filter((ruleItem) => {
            // 对筛选项值进行修改并转换成字符串
            if (
              ruleItem.filterFieldValue &&
              JSON.stringify(ruleItem.filterFieldValue) !== "{}" &&
              JSON.stringify(ruleItem.filterFieldValue) !== "[]"
            ) {
              // 底池规则部分指标需要转换成小数
              if (
                ["rangeNumberInput"].includes(
                  ruleItem.filterFieldComponentType
                ) &&
                percentage.includes(ruleItem.filterFieldId)
              ) {
                ruleItem.filterFieldValue = {
                  start:
                    parseFloat(ruleItem.filterFieldValue.start) / 100 || null,
                  end: parseFloat(ruleItem.filterFieldValue.end) / 100 || null,
                };
              }
              if (typeof ruleItem.filterFieldValue != "string") {
                ruleItem.filterFieldValue = JSON.stringify(
                  ruleItem.filterFieldValue
                );
              }
              ruleItem.costomRuleType = scenarioRulesType;
              return ruleItem;
            }
          });
        }
        if (item.rules.length > 0) {
          return item;
        } else {
          rulesNotComplete.push(index + 1);
        }
      }
    });
    postData.forEach((item) => {
      selectedCityIdList = [...selectedCityIdList, ...item.cityId];
    });
    rulesNotComplete.forEach((item, textIndex) => {
      rulesNotCompleteText =
        rulesNotCompleteText + `${textIndex != 0 ? "、" : ""}底池规则${item}`;
    });

    return { rulesNotCompleteText, postData, selectedCityIdList };
  };

  const popupConfirmForNext = async (text, step) => {
    await promisify(field.validate)();
    const { rulesNotCompleteText, postData, selectedCityIdList } =
      filterCityList();
    if (rulesNotCompleteText.length > 0) {
      Dialog.confirm({
        title: "温馨提示",
        content: (
          <div className="dialog-text">
            您当前<span className="rules">{rulesNotCompleteText}</span>
            的所在城市或筛选条件不全，{`${text}`}
            操作可能会导致这些规则被过滤，是否进行操作。
          </div>
        ),
        messageProps: {
          type: "warning",
        },
        onOk: () => {
          props.setSelectedCityIdList(selectedCityIdList);
          props.updateScenarioDefined(postData);
          setCityIdList(selectedCityIdList);
          if (step == 1) {
            if (postData.length > 0) {
              props.setCategoryPreview(true);
            } else {
              Message.error("底池规则为空，不能预览哦！");
              props.setCategoryPreview(false);
            }
          }
          props.goStep(step);
        },
      });
    } else {
      props.setSelectedCityIdList(selectedCityIdList);
      props.updateScenarioDefined(postData);
      props.getCustomRuleType(scenarioRulesType);
      setCityIdList(selectedCityIdList);
      if (step == 1) {
        if (postData.length > 0) {
          props.setCategoryPreview(true);
        } else {
          Message.error("底池规则为空，不能预览哦！");
          props.setCategoryPreview(false);
        }
      }
      props.goStep(step);
    }
  };
  // 下一步
  const nextStep = () => {
    popupConfirmForNext("下一步", 2);
  };

  // 上一步
  const previousStep = () => {
    popupConfirmForNext("上一步", 0);
  };

  // 预览
  const previewCategory = () => {
    popupConfirmForNext("预览", 1);
  };

  // 删除底池规则
  const deleteRule = (index) => {
    let postData = deepCopy(cityList);
    postData[index].cityId.map((item) => {
      if (cityIdList.indexOf(item) > -1) {
        delete cityIdList[cityIdList.indexOf(item)];
      }
    });
    delete postData[index];
    const newPostData = postData.filter((d) => d);
    setCityIdList(cityIdList.filter((d) => d));
    setCityList(deepCopy(newPostData));
    setValues({});
    reset();
    newPostData.map((item, newIndex) => {
      setValue(`city_${newIndex}`, item.cityId);
      item.rules = item.rules.filter((ruleItem) => {
        setValue(`${ruleItem.filterFieldId}_${newIndex}`, true);
        switch (ruleItem.filterFieldComponentType) {
          case 'arrayInput':
            if (typeof ruleItem.filterFieldValue != 'string') {
              setValue(
                `${ruleItem.filterFieldId}_${newIndex}_value`,
                ruleItem.filterFieldValue.toString()
              );
            }else{
              setValue(
                `${ruleItem.filterFieldId}_${newIndex}_value`,
                ruleItem.filterFieldValue
              );
            }
            break;
          case "selectSearch":
          case "multipleSelect": //下拉多选
          case "cascaderSelect": //下拉级联多选
          case "picStandard":
            if (!!ruleItem.filterFieldValue) {
              setValue(
                `${ruleItem.filterFieldId}_${newIndex}_value`,
                JSON.parse(ruleItem.filterFieldValue).map(e => {return e.value} )
              );
            }else{
              setValue(
                `${ruleItem.filterFieldId}_${newIndex}_value`,
                ruleItem.filterFieldValue
              );
            }
            break;
          default:
            setValue(
              `${ruleItem.filterFieldId}_${newIndex}_value`,
              ruleItem.filterFieldValue
            );
            break;
        }
        return ruleItem;
      });
    });
  };

  // 快速选择城市
  const quickSelectionCity = (index, type) => {
    let citylist = [];
    switch (type) {
      case 1:
        citylist = ["1", "2", "18", "13", "8", "30", "35", "7", "21"];
        break;
      case 2:
        citylist = JSON.parse(
          '["6","3","5","9","15","28","17","14","24","51","4","10","19","11","16","12","32","44"]'
        );
        break;
      case 3:
        citylist = JSON.parse(
          '["1","2","18","13","8","30","35","7","21","6","3","5","9","15","28","17","14","24","51","4","10","19","11","16","12","32","44"]'
        );
        break;
      default:
        citylist = ["1", "2", "18", "13", "8", "30", "35", "7", "21"];
        break;
    }
    // let haveSame = citylist.some((item) => cityIdList.includes(item));
    const newCityIdList = Array.from(new Set([...cityList[index].cityId, ...citylist]));
    cityList[index].cityId = newCityIdList;
    setCityIdList(newCityIdList.filter((id) => id));
    setCityList(cityList);
    setValue(`city_${index}`, newCityIdList);
  };

  return (
    <>
      <div className="scenario-defined">
        <h3>
          2.场景定义({universalPool ? "通用池" : "城市池"}
          )：需要获取定义中维度信息
        </h3>
        <Form field={field}>
          <Row className="tab-content-group">
            <Col className="left" span={13}>
              <div className="check-list">
                {cityList.length > 0 ? (
                  <div className="check-radio">
                    <Radio.Group
                      disabled={props.isEdit || !haveDefingConditions || !haveCustomizeDefingConditions}
                      key={`${props.isEdit}_${haveDefingConditions}_${haveCustomizeDefingConditions}`}
                      value={scenarioRulesType}
                      dataSource={[
                        { value: 1, label: "多维度定义" },
                        { value: 2, label: "自定义维度" },
                      ]}
                      onChange={(e) => {
                        const { postData, selectedCityIdList } =
                          filterCityList();
                        if (
                          postData.length > 0 ||
                          selectedCityIdList.length > 0
                        ) {
                          Dialog.confirm({
                            title: "温馨提示",
                            content: (
                              <div className="dialog-text">
                                切换维度可能导致您当前定义的内容消失，是否继续？
                              </div>
                            ),
                            messageProps: {
                              type: "warning",
                            },
                            onOk: () => {
                              props.setSelectedCityIdList([]);
                              props.updateScenarioDefined([]);
                              props.getCustomRuleType(e);
                              setCityIdList([]);
                              setValues({});
                              reset();
                              if (universalPool) {
                                setValue(`city_${0}`, ["all"]);
                                setCityList([{ cityId: ["all"], rules: [] }]);
                              } else {
                                setCityList([{ cityId: [], rules: [] }]);
                              }
                              setScenarioRulesType(e);
                            },
                          });
                        } else {
                          props.setSelectedCityIdList([]);
                          props.updateScenarioDefined([]);
                          props.getCustomRuleType(e);
                          setCityIdList([]);
                          setValues({});
                          reset();
                          if (universalPool) {
                            setValue(`city_${0}`, ["all"]);
                            setCityList([{ cityId: ["all"], rules: [] }]);
                          } else {
                            setCityList([{ cityId: [], rules: [] }]);
                          }
                          setScenarioRulesType(e);
                        }
                      }}
                    />
                  </div>
                ) : null}
                {cityList.length > 0 ? (
                  cityList.map((cityItem, index) => {
                    return (
                      <>
                        <div
                          className="bottom-pool-rules"
                          key={`citylist_${index}`}
                        >
                          <div className="topName">
                            底池规则{index + 1}:
                            {!universalPool && (
                              <div
                                className="delete"
                                onClick={() => deleteRule(index)}
                              >
                                删除
                              </div>
                            )}
                          </div>
                          <FormItem label="所在城市" {...formItemLayout}>
                            <CascaderSelect
                              disabled={universalPool}
                              showSearch
                              {...init(`city_${index}`, {
                                props: {
                                  onChange: (
                                    currentIdList,
                                    currentItemList,
                                    operation
                                  ) => {
                                    const {
                                      checked,
                                      checkedData,
                                      currentData,
                                    } = operation;
                                    if (checked) {
                                      // 添加操作,每次去查询所有底池中的城市
                                      let canAdd =
                                        cityIdList.indexOf(currentData.value) >
                                        -1;
                                      if (!canAdd) {
                                        setCityIdList(
                                          Array.from(
                                            new Set([
                                              ...cityIdList,
                                              ...currentIdList,
                                            ])
                                          )
                                        );
                                        setValue(
                                          `city_${index}`,
                                          currentIdList
                                        );
                                        cityList[index].cityId = currentIdList;
                                        setCityList(cityList);
                                      } else {
                                        Message.error(
                                          "一个城市只能存在于一个底池规则中"
                                        );
                                        const newCurrentIdList =
                                          currentIdList.filter(
                                            (id) => id != currentData.value
                                          );
                                        setValue(
                                          `city_${index}`,
                                          newCurrentIdList
                                        );
                                        cityList[index].cityId =
                                          newCurrentIdList;
                                        setCityList(cityList);
                                      }
                                    } else {
                                      // 取消操作,删除对应的城市id
                                      let newCityIdList = Array.from(
                                        new Set(cityIdList)
                                      );
                                      delete newCityIdList[
                                        cityIdList.indexOf(currentData.value)
                                      ];
                                      setCityIdList(
                                        newCityIdList.filter((id) => id)
                                      );
                                      setValue(`city_${index}`, currentIdList);
                                      cityList[index].cityId = currentIdList;
                                      setCityList(cityList);
                                    }
                                  },
                                },
                              })}
                              multiple
                              canOnlyCheckLeaf
                              name={"city_" + `${index}`}
                              dataSource={cityDataSource}
                              style={{ width: "38%" }}
                            />
                            {!universalPool ? (
                              <div
                                className="selectionCity"
                                style={{ width: "62%" }}
                              >
                                <div>
                                  <Button
                                    type="normal"
                                    size="small"
                                    onClick={() => quickSelectionCity(index, 1)}
                                  >
                                    核心9城
                                  </Button>
                                  <Balloon.Tooltip
                                    align="t"
                                    trigger={
                                      <Icon
                                        type="help"
                                        style={{ color: "#CCCCCC" }}
                                        size={"inherit"}
                                      />
                                    }
                                  >
                                    核心9城：上海、杭州、宁波、厦门、苏州、温州、无锡、武汉、长沙
                                  </Balloon.Tooltip>
                                </div>
                                <div>
                                  <Button
                                    type="normal"
                                    size="small"
                                    onClick={() => quickSelectionCity(index, 2)}
                                  >
                                    战略18城
                                  </Button>
                                  <Balloon.Tooltip
                                    align="t"
                                    trigger={
                                      <Icon
                                        type="help"
                                        style={{ color: "#CCCCCC" }}
                                        size={"inherit"}
                                      />
                                    }
                                  >
                                    战略18城：南京、北京、天津、福州、西安、青岛、合肥、成都、佛山、东莞、广州、哈尔滨、济南、深圳、沈阳、长春、郑州、重庆
                                  </Balloon.Tooltip>
                                </div>
                                <div>
                                  <Button
                                    type="normal"
                                    size="small"
                                    onClick={() => quickSelectionCity(index, 3)}
                                  >
                                    27城
                                  </Button>
                                  <Balloon.Tooltip
                                    align="t"
                                    trigger={
                                      <Icon
                                        type="help"
                                        style={{ color: "#CCCCCC" }}
                                        size={"inherit"}
                                      />
                                    }
                                  >
                                    27城：上海、杭州、宁波、厦门、苏州、温州、无锡、武汉、长沙、南京、北京、天津、福州、西安、青岛、合肥、成都、佛山、东莞、广州、哈尔滨、济南、深圳、沈阳、长春、郑州、重庆
                                  </Balloon.Tooltip>
                                </div>
                              </div>
                            ) : (
                              ""
                            )}
                          </FormItem>
                          <div className="selectRule">
                            {[
                              ...(scenarioRulesType == 1
                                ? defingConditions
                                : customizeDefingConditions),
                            ].map((v, defingIndex) => {
                              return (
                                <div
                                  className="ruleItem"
                                  key={`definedconditions_${defingIndex}`}
                                >
                                  <Checkbox
                                    checked={Boolean(
                                      getValue(`${v.filterFieldId}_${index}`)
                                    )}
                                    {...init(`${v.filterFieldId}_${index}`, {
                                      props: {
                                        onChange: (checked) => {
                                          if (checked) {
                                            //增加指标
                                            cityList[index].rules.push(
                                              deepCopy(v)
                                            );
                                            setCityList(cityList);
                                          } else {
                                            // 删除指标
                                            cityList[index].rules = cityList[
                                              index
                                            ].rules
                                              .map((ruleItem) => {
                                                if (
                                                  ruleItem.filterFieldId !=
                                                  v.filterFieldId
                                                ) {
                                                  return ruleItem;
                                                }
                                              })
                                              .filter((d) => d);
                                            setCityList(cityList);
                                          }
                                          setValue(
                                            `${v.filterFieldId}_${index}`,
                                            checked
                                          );
                                        },
                                      },
                                    })}
                                  >
                                    {v.filterFieldLabel}{" "}
                                    {v.filterFieldDesc && (
                                      <Balloon.Tooltip
                                        align="t"
                                        trigger={
                                          <Icon
                                            type="help"
                                            style={{ color: "#CCCCCC" }}
                                            size={"inherit"}
                                          />
                                        }
                                      >
                                        {v.filterFieldDesc}
                                      </Balloon.Tooltip>
                                    )}
                                  </Checkbox>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </>
                    );
                  })
                ) : (
                  <div className="noRules">
                    当前没有底池规则。可通过点击左下方按钮新增底池规则
                    <img src="https://img.alicdn.com/imgextra/i2/O1CN01Xrk7731gsjTS32fKD_!!6000000004198-2-tps-886-946.png"></img>
                  </div>
                )}
              </div>
            </Col>
            {cityList.length > 0 ? (
              <Col className="right" span={11}>
                <p className="right-title">
                  编辑规则<span>请在左侧选择标签</span>
                </p>
                <Collapse
                  defaultExpandedKeys={[
                    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
                    17, 18, 19, 20,
                  ]}
                >
                  {cityList.map((cityItem, index) => {
                    return (
                      <Panel
                        title={"底池规则" + `${index + 1}`}
                        key={index + 1}
                      >
                        {cityItem.rules.map((ruleItem, ruleIndex) => {
                          return (
                            <FormItem
                              key={ruleIndex}
                              labelAlign={"top"}
                              {...{ labelCol: { fixedSpan: 4 } }}
                              label={
                                <>
                                  <span>{ruleItem.filterFieldLabel}</span>
                                  <Icon
                                    type={"delete-filling"}
                                    style={{ color: "#CCCCCC" }}
                                    onClick={(checked, event) =>
                                      checkField(
                                        ruleItem,
                                        ruleIndex,
                                        index,
                                        checked,
                                        event
                                      )
                                    }
                                  />
                                </>
                              }
                              hasFeedback
                              required={ruleItem.required}
                            >
                              {switchItem(ruleItem, index)}
                            </FormItem>
                          );
                        })}
                      </Panel>
                    );
                  })}
                </Collapse>
              </Col>
            ) : (
              ""
            )}
          </Row>
        </Form>
      </div>
      <div className="fixed-btn-panel">
        <Button onClick={() => addBottomPoolRule()} disabled={canAddPoolRule}>
          新增底池规则
        </Button>
        <div className="next-step">
          <Button onClick={() => previousStep()}>上一步</Button>&nbsp;
          <Button disabled={isView} onClick={() => previewCategory()}>
            预览
          </Button>
          &nbsp;
          <Button type="primary" onClick={() => nextStep()}>
            下一步
          </Button>
        </div>
      </div>
    </>
  );
}
