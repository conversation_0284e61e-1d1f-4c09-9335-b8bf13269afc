import React, { useEffect, useState } from "react";
import {
  Balloon,
  Button,
  CascaderSelect,
  Checkbox,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Radio,
  Select,
  Tab,
} from "@alife/next";
import "./style.scss";
import { dealQualityScoreToObject, deepCopy } from "@/home/<USER>/common";
import * as api from "@/adator/api";
import {
  poolFiltersConfig,
  validatorMap,
  filtersConfigMap,
} from "@/home/<USER>/common/config";
import { RangeNumberInput } from "@/home/<USER>/common/components";
import { promisify } from "@/utils/others";
const { Row, Col } = Grid;

const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { fixedSpan: 4 },
};
const RadioGroup = Radio.Group;
export default function TagRules(props) {
  
  const { sourcePoolId } = props;
  const ctrlConfigArray = (poolConfig) => {
    let result = [];
    for (let o in poolConfig) {
      poolConfig[o].map((v) => {
        result.push(v);
      });
    }
    return result;
  };
  let newPoolFiltersConfig = [];
  newPoolFiltersConfig = deepCopy(poolFiltersConfig);
  const [selectionPoolIdRules, setSelectionPoolIdRules] = useState(
    props.selectionRules || []
  );
  const [selectedField, setSelectedField] = useState({});
  const [selectedAllField, setSelectedAllField] = useState([]); //当前选中的所有复选框
  const [tabidx, setTabidx] = useState("baseInfo");
  const [tmpData, setTmpData] = useState([]);
  const [data, setData] = useState(props.effectRules || []); //对应effectRules
  const [postData, setPostData] = useState(props.requestEffectRules || []); //对应requestEffectRules todo修改名称
  const [groupOptions, setGroupOptions] = useState(props.groupOptions || {});
  const [searchOptions, setSearchOptions] = useState({}); //可支持查询的下拉
  const [fieldMap, setFieldMap] = useState({});

  const onTabChange = (value) => {
    setTabidx(value);
    const element = document.getElementById(value);
    element.scrollIntoView(true);
  };

  const getOptions = (item) => {
    return groupOptions[item.filterFieldId];
  };

  const handleChange = (value, item) => {
    data.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = value;
    let newPostData = deepCopy(postData);
    let { filterFieldComponentType } = item;
    let newValue = value;
    if (filterFieldComponentType == "checkbox") {
      /* 复选框，转成json串 */
      if (value) {
        newValue = JSON.stringify(value);
      }
    } else if (filterFieldComponentType == "arrayInput") {
      /* 输入框，以'，'隔开的json串 转为 数组的json串 */
      const tmpValue = value.replace(/[\r\n]/g, "");
      if (tmpValue) {
        newValue = JSON.stringify(
          tmpValue.split(",").map((item) => item.replace(/(^\s*)|(\s*$)/g, ""))
        );
      }
    }
    newPostData.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = newValue;
    setData(data);
    setPostData(newPostData);
  };

  // 下拉选择器类
  const handleSelectChange = (value, group, item) => {
    if (!value) return;
    const haveSelected = group.map((item) => {
      const { value, label, pos } = item;
      return {
        value,
        label,
        level: pos.split("-").length - 1,
      };
    });
    let newData = deepCopy(data);
    let newPostData = deepCopy(postData);
    let index = newPostData.findIndex(
      (o) => o.filterFieldId == item.filterFieldId
    );
    if (!newPostData[index].filterFieldValue)
      newPostData[index].filterFieldValue = "";
    newPostData[index].filterFieldValue = JSON.stringify(haveSelected);
    if (item.filterFieldComponentType == "picStandard") {
      /*商品质量分需要特殊处理成  {mainRatioLabel:['0', '1'],psoriasisLabel: ['0'],transparentLabel: ['1'] } 格式 */
      newPostData[index].filterFieldValue = JSON.stringify(
        dealQualityScoreToObject(group)
      );
    }
    if (!newData[index].filterFieldValue) newData[index].filterFieldValue = "";
    newData[index].filterFieldValue = value;
    tmpData[item.filterFieldId] = haveSelected;
    setPostData(newPostData);
    setData(newData);
    setTmpData(tmpData);
  };

  //店铺评分类
  const handleRangeChange = (value, group, item) => {
    data.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = { ...value };
    let newPostData = deepCopy(postData);
    newPostData.filter(
      (o) => o.filterFieldId == item.filterFieldId
    )[0].filterFieldValue = JSON.stringify({ ...value });
    setData(data);
    setPostData(newPostData);
  };

  // 搜索
  let searchTimeout;
  const handleSearch = (keyword) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      if (keyword) {
        api.queryBrand({ brandName: keyword }).then((data) => {
          const dataSource = data.data.data.map((item) => ({
            label: item.name,
            value: item.id,
          }));
          ctrlSearchOptions("sku_brand_id", dataSource);
        });
      } else {
        ctrlSearchOptions("sku_brand_id", []);
      }
    }, 800);
  };

  const field = Field.useField({});

  const switchItem = (item) => {
    const filterFieldComponentType = item.filterFieldComponentType;
    let filterItem = selectionPoolIdRules.filter(
      (o) => o.filterFieldId == item.filterFieldId
    );
    const filterFieldExtend =
      filterItem && filterItem.length > 0
        ? selectionPoolIdRules.filter(
            (o) => o.filterFieldId == item.filterFieldId
          )[0].filterFieldExtend
        : {};

    const { init } = field;
    let attrs = {
      dataSource:
        item.filterFieldDataType == 1
          ? JSON.parse(item.filterFieldData)
          : getOptions(item),
      value: item.filterFieldValue,
      name: item.filterFieldId,
    };
    let placeholderName = "英文逗号隔开，最多100个";
    if (item.validatorValue == "nameSeperatedRequired") {
      placeholderName = "英文逗号隔开，最多30个";
    }
    let defaultRangeExtend = { precision: 1, step: 0.01 };
    if (filterFieldComponentType == "rangeNumberInput" && filterFieldExtend) {
      defaultRangeExtend = filterFieldExtend;
    }
    let result;
    switch (filterFieldComponentType) {
      case "arrayInput": //文本框
        // 设置校验规则，编辑等操作在后端没有校验规则字段，需自行匹配
        let arrayInputValidator = null;
        if (item.validatorValue) {
          arrayInputValidator = validatorMap[item.validatorValue];
        } else {
          filtersConfigMap.forEach((configItem) => {
            if (configItem.filterFieldId == item.filterFieldId) {
              if (configItem.validatorValue) {
                arrayInputValidator = validatorMap[configItem.validatorValue];
              }
            }
          });
        }
        result = (
          <Input.TextArea
            placeholder={placeholderName}
            {...field.init(item.filterFieldId, {
              rules: [
                {
                  ...attrs,
                },
                {
                  validator: (rule, value, callback) => {
                    if (typeof arrayInputValidator == "function") {
                      arrayInputValidator(
                        rule,
                        value ? value : item.filterFieldValue,
                        callback
                      );
                    } else {
                      callback();
                    }
                  },
                  trigger: ["onBlur"],
                },
              ],
              props: {
                onChange: (value) => handleChange(value, item),
              },
            })}
            value={item.filterFieldValue}
          />
        );
        break;
      case "radio": //单选框
        result = (
          <RadioGroup
            {...attrs}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "checkbox": //复选框
        result = (
          <CheckboxGroup
            {...attrs}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "multipleSelect": //下拉多选
      case "cascaderSelect": //下拉级联多选
      case "picStandard":
        let newValue = item.filterFieldValue;
        result = (
          <CascaderSelect
            expandTriggerType={"hover"}
            {...init(item.filterFieldId, {
              props: {
                onChange: (value, data) =>
                  handleSelectChange(value, data, item),
              },
            })}
            showSearch
            style={{ width: "100%" }}
            dataSource={getOptions(item)}
            value={newValue}
            multiple={true}
          />
        );
        break;
      case "rangeNumberInput": //店铺评分类
        result = (
          <RangeNumberInput
            {...attrs}
            {...defaultRangeExtend}
            onChange={(value, data) => handleRangeChange(value, data, item)}
          />
        );
        break;
      case "select": //下拉多选
        result = (
          <Select
            showSearch
            {...attrs}
            style={{ width: "200px" }}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "selectSearch": //下拉多选
        result = (
          <CascaderSelect
            showSearch
            multiple={true}
            {...init(item.filterFieldId, {
              props: {
                onChange: (value, data) =>
                  handleSelectChange(value, data, item),
              },
            })}
            value={item.filterFieldValue}
            filterLocal={false}
            dataSource={
              searchOptions && searchOptions[item.filterFieldId]
                ? searchOptions[item.filterFieldId]
                : []
            }
            onSearch={(value) => handleSearch(value)}
            style={{ width: "100%" }}
          />
        );
        break;
      default:
        result = <Input />;
        break;
    }
    return result;
  };

  // const ctrlGroupOptions = (key, data) => {
  //   let othersGroup = [
  //     "item_goodsCategory",
  //     "store_main_category_id",
  //     "store_cat2_id",
  //     "store_city_id",
  //     "scene_city_id",
  //     "item_activity_types",
  //     "activity_child_type",
  //   ];
  //   if (othersGroup.includes(key)) {
  //     groupOptions[key] = data;
  //   }
  //   props.getGroupOptions(groupOptions);
  //   setGroupOptions(groupOptions);
  // };

  const ctrlSearchOptions = (key, data) => {
    let group = ["sku_brand_id"];
    if (group.includes(key)) {
      const originData = searchOptions[key] ? searchOptions[key] : [];
      const wholeData = data.concat(originData);
      searchOptions[key] = Array.from(new Set(wholeData));
    }
    setSearchOptions(deepCopy(searchOptions));
  };



  

  useEffect(() => {
    initRightField();
    initSearchOption();
  }, [props.effectRules, props.requestEffectRules, sourcePoolId]);

  useEffect(() => {
    // 请求接口获取当前的分组
    selectionPoolIdRules.forEach((item) => {
      // 过滤掉没用的分类
      if (
        ["baseInfo", "skuStore", "skuTrade", "scene","skuMarket"].includes(
          item.filterFieldIdGroup
        )
      ) {
        if (!fieldMap[item.filterFieldIdGroup]) {
          fieldMap[item.filterFieldIdGroup] = [];
        }
        fieldMap[item.filterFieldIdGroup].push(item);
      }
    });
    groupOptions["fieldMap"] = fieldMap;
    props.getGroupOptions(groupOptions);
    setFieldMap(fieldMap);
  }, []);

  /*初始化标签复选框*/
  const initRightField = () => {
    if (props.isEdit || props.effectRules.length > 0) {
      let { effectRules, requestEffectRules } = props;
      let tempSelectedAllField = effectRules.map((o) => o.filterFieldId);
      setSelectedAllField(tempSelectedAllField);
      let tempSelectedField = {};
      effectRules.map((item) => {
        let hasField = effectRules.filter(
          (v) => v.filterFieldId == item.filterFieldId
        );
        tempSelectedField[item.filterFieldIdGroup] = [];
        if (hasField.length > 0) {
          tempSelectedField[item.filterFieldIdGroup].push(item);
        }
      });
      setSelectedField(tempSelectedField);
      if (props.isEdit) {
        //编辑模式，构建data、postData
        let newData = deepCopy(effectRules);
        let newPostData = deepCopy(requestEffectRules);
        setData(newData);
        setPostData(newPostData);
      }
    }
  };

  const initSearchOption = () => {
    let newSearchOptions = {};
    if (
      (props.isEdit || props.isCopy) &&
      props.requestEffectRules &&
      props.requestEffectRules.length > 0
    ) {
      props.requestEffectRules.map((v) => {
        if (v.filterFieldComponentType == "selectSearch") {
          newSearchOptions[v.filterFieldId] = v.filterFieldValue
            ? JSON.parse(v.filterFieldValue)
            : [];
        }
      });
    }
    setSearchOptions(newSearchOptions);
  };

  /*勾选复选框*/
  const checkField = (checked, event, curGroup) => {
    let value = event.target ? event.target.value : event;
    let newSelectedAllField = deepCopy(selectedAllField);
    let item = fieldMap[curGroup].filter((v) => v.filterFieldId == value)[0];
    let newData = deepCopy(data);
    let newPostData = deepCopy(postData);

    if (checked) {
      newSelectedAllField.push(value);
      if (!selectedField[item.filterFieldIdGroup])
        selectedField[item.filterFieldIdGroup] = [];
      selectedField[item.filterFieldIdGroup].push(value);
      newData.push(item);
      // 在勾选时判断值是否为string
      if (typeof item.filterFieldValue != "string" && item.filterFieldValue) {
        let newItem = deepCopy(item);
        newItem.filterFieldValue = JSON.stringify(newItem.filterFieldValue);
        newPostData.push(newItem);
      } else {
        newPostData.push(item);
      }
    } else {
      field.setValue(item.filterFieldId, "");
      if (item.filterFieldComponentType == "arrayInput") {
        fieldMap[item.filterFieldIdGroup] = fieldMap[
          item.filterFieldIdGroup
        ].map((mapItem) => {
          if (item.filterFieldId == mapItem.filterFieldId) {
            mapItem.filterFieldValue = "";
          }
          return mapItem;
        });
        setFieldMap(fieldMap);
      }
      const index = newSelectedAllField.findIndex((o) => o == value);
      const index2 = selectedField[item.filterFieldIdGroup].findIndex(
        (o) => o == value
      );
      const index3 = newData.findIndex(
        (o) => o.filterFieldId == item.filterFieldId
      );
      if (index > -1) {
        newSelectedAllField = [
          ...newSelectedAllField.slice(0, index),
          ...newSelectedAllField.slice(index + 1),
        ];
        selectedField[item.filterFieldIdGroup] = [
          ...selectedField[item.filterFieldIdGroup].slice(0, index2),
          ...selectedField[item.filterFieldIdGroup].slice(index2 + 1),
        ];
        newData = [...newData.slice(0, index3), ...newData.slice(index3 + 1)];
        newPostData = [
          ...newPostData.slice(0, index3),
          ...newPostData.slice(index3 + 1),
        ];
      }
    }
    setSelectedAllField(newSelectedAllField);
    setSelectedField(selectedField);
    setData(newData);
    setPostData(newPostData);
  };

  /**
   * 点击预览按钮
   **/
  const previewSample = async () => {
    await promisify(field.validate)();
    // let speciGroup = ['item_ac_goodsType', 'item_ac_CAT'];
    let newData = data.filter((v) => !!v.filterFieldValue);
    let newPostData = postData.filter((v) => !!v.filterFieldValue);
    setData(deepCopy(newData));
    setPostData(deepCopy(newPostData));
    props.getEffectRules(newData, newPostData);
    props.setGoodsPreviewDialog(true);
  };

  /**
   * 不预览，直接下一步
   **/
  const noPreview = async () => {
    await promisify(field.validate)();
    let newData = data.filter((v) => !!v.filterFieldValue);
    let newPostData = postData.filter((v) => !!v.filterFieldValue);
    setData(deepCopy(newData));
    setPostData(deepCopy(newPostData));
    props.getEffectRules(newData, newPostData);
    props.goStep(3);
  };

  // 上一步
  const previousStep = async () => {
    await promisify(field.validate)();
    let newData = data.filter((v) => !!v.filterFieldValue);
    let newPostData = postData.filter((v) => !!v.filterFieldValue);
    setData(deepCopy(newData));
    setPostData(deepCopy(newPostData));
    props.getEffectRules(newData, newPostData);
    if (props.scenePoolType == 101) {
      props.goStep(0);
    }else{
      props.goStep(1);
    }
  };

  let group = [
    { value: "baseInfo", label: "基本信息" },
    { value: "skuStore", label: "门店信息" },
    { value: "skuTrade", label: "交易信息" },
    { value: "scene", label: "场景信息" },
    { value: "skuMarket", label: "营销信息" },
  ];
  return (
    <>
      <div className="label-rules">
        <h3 className="header">3. 设置圈选规则</h3>
        <Row className="tab-content-group">
          <Col className="left" span={13}>
            <Tab onChange={onTabChange} activeKey={tabidx}>
              {group.map((o) => {
                return <Tab.Item title={o.label} key={o.value}></Tab.Item>;
              })}
            </Tab>
            <div className="check-list">
              {group.map((o) => {
                return (
                  <div key={o.value}>
                    {fieldMap[o.value] && <p id={`${o.value}`}>{o.label}</p>}
                    {fieldMap[o.value] &&
                      fieldMap[o.value].map((v) => {
                        return (
                          <Checkbox
                            key={v.filterFieldId}
                            value={v.filterFieldId}
                            checked={selectedAllField.includes(v.filterFieldId)}
                            onChange={(checked, event) => {
                              checkField(checked, event, o.value);
                            }}
                          >
                            {v.filterFieldLabel}{" "}
                            {v.filterFieldDesc && (
                              <Balloon.Tooltip
                                align="t"
                                trigger={
                                  <Icon
                                    type="help"
                                    style={{ color: "#CCCCCC" }}
                                    size={"inherit"}
                                  />
                                }
                              >
                                {v.filterFieldDesc}
                              </Balloon.Tooltip>
                            )}
                          </Checkbox>
                        );
                      })}
                  </div>
                );
              })}
            </div>
          </Col>
          <Col className="right" span={11}>
            <p className="right-title">
              编辑规则<span>请在左侧选择标签</span>
            </p>
            <Form className="rules-form" field={field}>
              {data &&
                data.length > 0 &&
                data.map((item, index) => {
                  const textWidth =
                    (document.body.offsetWidth - 260) / 2 - 80 + "px";
                  if (item.filterFieldId == "item_expert_pool_id") {
                    //隐藏部分不展示但需要传的指标
                    return "";
                  }
                  return (
                    <FormItem
                      key={`${item.filterFieldId}_${index}`}
                      labelAlign={"top"}
                      {...formItemLayout}
                      label={
                        <>
                          <span>{item.filterFieldLabel}</span>
                          <Icon
                            type={"delete-filling"}
                            style={{ color: "#CCCCCC" }}
                            onClick={(e) =>
                              checkField(
                                false,
                                item.filterFieldId,
                                item.filterFieldIdGroup
                              )
                            }
                          />
                        </>
                      }
                      hasFeedback
                    >
                      {switchItem(item)}
                    </FormItem>
                  );
                })}
            </Form>
          </Col>
        </Row>
      </div>
      <div className="btn-panel">
        <div className="next-step">
          <Button onClick={() => previousStep()}>上一步</Button>
          <Button onClick={() => previewSample()}>预览</Button>
          <Button type="primary" onClick={() => noPreview()}>
            不预览，下一步
          </Button>
        </div>
      </div>
    </>
  );
}
