import React, { useEffect, useState } from "react";
import { Icon, Button } from "@alife/next";
import "./style.scss";

/*
 *完成页面 （公用组件）
 */
export default function FinishPage(props) {
  const { submitPoolId = "" } = props;

  const [timer, setTimer] = useState("");
  let [seconds, setSeconds] = useState(5);

  let Interval = setInterval(() => {
    setSeconds(seconds - 1);
  }, 1000);

  useEffect(() => {
    setTimer(
      setTimeout(() => {
        clearInterval(Interval);
        props.history.push("/expertPool/list");
      }, 5000)
    );
  }, []);

  const seekDetail = () => {
    timer && clearTimeout(timer);
    props.history.push(`/pool/expertPoolDetail/${submitPoolId}`);
  };

  const backList = () => {
    timer && clearTimeout(timer);
    props.history.push("/expertPool/list");
  };

  return (
    <div className="complete-page">
      <div className="success-info">
        <Icon
          type="success"
          style={{ color: "#1DC11D", marginRight: "10px" }}
        />
        已提交
      </div>
      {seconds && <div className="success-tips">{seconds}s后自动跳转</div>}
      <div className="success-btns">
        <Button onClick={seekDetail}>查看详情</Button>
        <Button onClick={backList}>返回列表</Button>
      </div>
    </div>
  );
}
