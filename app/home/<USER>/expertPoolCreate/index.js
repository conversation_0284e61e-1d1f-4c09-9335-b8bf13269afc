import React, { useCallback, useEffect, useState } from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { permissionAccess } from "@/components/PermissionAccess";
import { BreadcrumbTips } from "@/home/<USER>/comps";
import { optionMap } from "@/home/<USER>/common/map";
import { Step, Message } from "@alife/next";
import { withRouter } from "react-router-dom";
import BaseInfo from "./baseInfo";
import ScenarioDefined from "./scenarioDefined";
import TagRules from "./tagRules";
import CategoryPreviewDialog from "./categoryPreviewDialog";
import GoodsPreviewDialog from "./goodsPreviewDialog";
import VisibilityScreenCriteria from "./visibilityScreenCriteria";
import FinishPage from "./finishPage";
import * as api from "@/adator/api";
import "./style.scss";
import { deepCopy } from "../common";

function expertPoolCreate({ location, match, history }) {
  let { scenePoolType = 101, sceneId = "" } = match.params;
  const isEdit = sceneId > 0;
  const [step, setStep] = useState(0); //步骤
  const [scenarioRules, setScenarioRules] = useState([]); // 场景底池规则相关指标
  const [customizeScenarioRules, setCustomizeScenarioRules] = useState([]); // 自定义底池规则相关指标
  const [selectionRules, setSelectionRules] = useState([]); // 圈选条件相关指标
  const [customRuleType, setCustomRuleType] = useState(1); // 场景底池类型
  const [tempPoolId, setTempPoolId] = useState(""); // 临时的池子id
  const [submitPoolId, setSubmitPoolId] = useState(""); //发布成功的池子id
  const [user, setUser] = useState({}); //用户id
  const [baseInfo, setBaseInfo] = useState({}); //基础信息
  const [selectedCityIdList, setSelectedCityIdList] = useState([]); //底池规则选中的城市
  const [scenarioDefined, setScenarioDefined] = useState([]); //底池规则
  const [query, setQuery] = useState({}); //请求参数
  const [effectRules, setEffectRules] = useState([]); //编辑标签表单的圈选规则effectRules
  const [requestEffectRules, setRequestEffectRules] = useState([]); //在请求接口中流通的圈选规则requestEffectRules
  const [visibilityRules, setVisibilityRules] = useState([]); //运营可见的筛选条件
  const [groupOptions, setGroupOptions] = useState({ ...optionMap }); //数据来源集合
  const [categoryPreview, setCategoryPreview] = useState(false); //场景定义的预览弹窗
  const [goodsPreviewDialog, setGoodsPreviewDialog] = useState(false); //圈选规则的预览弹窗
  const [isAction, setIsAction] = useState(false); //是否在行动
  const [isCanEditRules, setIsCanEditRules] = useState(true);

  /*获取临时池子id*/
  const getTempId = () => {
    try {
      api.getTempPoolId().then((resp) => {
        setTempPoolId(resp.data);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const getUser = () => {
    try {
      api.getBucUser().then((resp) => {
        setUser(resp.data.data);
        if (isEdit) {
          getScenePoolDetail(resp.data.data.empId);
        }
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*将requestEffectRules转化为effectRules*/
  const changeEffectRules = (effectRules) => {
    let newEffectRules = [];
    if (effectRules && effectRules.length > 0) {
      effectRules.map((v) => {
        let value = v.filterFieldValue;
        if (v.filterFieldComponentType == "arrayInput") {
          value = JSON.parse(value).join(",");
        }
        if (v.filterFieldComponentType == "picStandard") {
          value = dealQualityScoreToArray(JSON.parse(value)).map(
            (v) => v.value
          );
        }
        let typeGroup = ["rangeNumberInput", "checkbox"];
        if (typeGroup.includes(v.filterFieldComponentType)) {
          value = JSON.parse(value);
        }
        if (
          v.filterFieldComponentType == "cascaderSelect" ||
          v.filterFieldComponentType == "multipleSelect" ||
          v.filterFieldComponentType == "selectSearch"
        ) {
          value = JSON.parse(value).map((v) => v.value);
        }
        newEffectRules.push({
          filterFieldId: v.filterFieldId,
          filterFieldKey: v.filterFieldKey,
          filterFieldLabel: v.filterFieldLabel,
          filterFieldValue: value,
          filterFieldComponentType: v.filterFieldComponentType,
          filterFieldIdGroup: v.filterFieldIdGroup,
          operator: v.operator,
        });
      });
    }
    setEffectRules(newEffectRules);
  };

  // 获取场景池信息进行初始化
  const getScenePoolDetail = (empId) => {
    try {
      api
        .getScenePoolDetailInfo({
          sceneBaseId: sceneId,
          poolEntryType: scenePoolType,
        })
        .then((resp) => {
          const {
            sourcePoolId,
            basePoolName,
            sceneCat,
            sceneGroup,
            basePoolDesc,
            sceneFilterFieldList,
            effectFilterFieldList,
            optionalFilterFieldList,
            customRuleType,
            state,
          } = resp.data.data.data;
          setIsCanEditRules([-1].includes(state));
          setCustomRuleType(customRuleType);
          baseInfo.basePoolName = basePoolName;
          baseInfo.basePoolDesc = basePoolDesc;
          baseInfo.sceneCat = sceneCat;
          baseInfo.sceneGroup = sceneGroup;
          baseInfo.sceneName = sceneGroup;
          baseInfo.sourcePoolId = sourcePoolId;
          setScenarioDefined(
            deconstructionScenarioDefined(sceneFilterFieldList)
          );
          changeEffectRules(effectFilterFieldList);
          setRequestEffectRules(effectFilterFieldList);
          setVisibilityRules(optionalFilterFieldList);
          query["basePoolName"] = basePoolName;
          query["basePoolDesc"] = basePoolDesc;
          query["sceneCat"] = sceneCat;
          query["sceneGroup"] = sceneGroup;
          query["sourcePoolId"] = sourcePoolId;
          query["sceneFilterFieldList"] = sceneFilterFieldList;
          query["effectFilterFieldList"] = effectFilterFieldList;
          query["optionalFilterFieldList"] = optionalFilterFieldList;
        });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  useEffect(() => {
    getUser();
    getTempId();
  }, []);

  // 获取圈选规则
  const getEffectRules = (effectRules, requestEffectRules) => {
    const newrequestEffectRules = requestEffectRules.filter(
      (v) => !!v.filterFieldValue
    );
    setEffectRules(effectRules.filter((v) => !!v.filterFieldValue));
    setRequestEffectRules(newrequestEffectRules);
    query["effectFilterFieldList"] = newrequestEffectRules;
    setQuery(query);
  };

  /* 更新底池规则 */
  const updateScenarioDefined = (newScenarioDefinedList) => {
    setScenarioDefined(newScenarioDefinedList);
    filterScenarioDefined(newScenarioDefinedList);
  };

  /* 更新基本信息 */
  const updateBaseInfo = (name, value) => {
    baseInfo[name] = value;
    query[name] = value;
    setQuery(query);
    setBaseInfo(baseInfo);
  };

  /* 更改分组数据来源 */
  const getGroupOptions = (groupOptions) => {
    setGroupOptions(groupOptions);
  };

  /* 更改场景维度 */
  const getCustomRuleType = (customRuleType) => {
    setCustomRuleType(customRuleType);
  };

  /*打平底池规则给服务端 */
  const filterScenarioDefined = (newScenarioDefinedList) => {
    let sceneFilterFieldBizModelList = [];
    newScenarioDefinedList.forEach((item, index) => {
      let effectItem = {
        filterFieldLabel: "所在城市",
        filterFieldId: customRuleType == 1 ? "scene_city_id" : "store_city_id",
        filterFieldKey: customRuleType == 1 ? "scene_city_id" : "store_city_id",
        operator: 7,
        filterFieldComponentType: "cascaderSelect",
        filterFieldIdGroup: "expertScene",
        filterFieldValue: JSON.stringify(item.cityId),
        filterFieldRuleGroup: index,
      };
      sceneFilterFieldBizModelList.push(effectItem);
      item.rules.forEach((ruleItem) => {
        let rulesItem = {
          ...ruleItem,
          filterFieldRuleGroup: index,
        };
        sceneFilterFieldBizModelList.push(rulesItem);
      });
    });
    query["sceneFilterFieldList"] = sceneFilterFieldBizModelList;
    setQuery(deepCopy(query));
  };

  /*解构服务端底池规则 */
  const deconstructionScenarioDefined = (newScenarioDefinedList) => {
    let sceneFilterFieldBizModelList = [];
    newScenarioDefinedList.map((item) => {
      if (!sceneFilterFieldBizModelList[item.filterFieldRuleGroup]) {
        sceneFilterFieldBizModelList[item.filterFieldRuleGroup] = {};
      }
      if (
        item.filterFieldId == "scene_city_id" ||
        item.filterFieldId == "store_city_id"
      ) {
        sceneFilterFieldBizModelList[item.filterFieldRuleGroup].cityId =
          JSON.parse(item.filterFieldValue);
      } else {
        if (!sceneFilterFieldBizModelList[item.filterFieldRuleGroup].rules) {
          sceneFilterFieldBizModelList[item.filterFieldRuleGroup].rules = [];
        }
        sceneFilterFieldBizModelList[item.filterFieldRuleGroup].rules.push(
          item
        );
      }
    });
    return sceneFilterFieldBizModelList;
  };

  /* 设置运营可见规则 */
  const getVisibilityRules = (newVisibilityRules) => {
    query["optionalFilterFieldList"] = newVisibilityRules;
    setQuery(query);
    setVisibilityRules(newVisibilityRules);
  };

  // 新建 - 保存
  const savePool = async () => {
    let createSceneV3Request = { ...query, scenePoolType, customRuleType, tempId:tempPoolId };
    try {
      if (!isAction) {
        setIsAction(true);
        let request = api.createScene;
        let resp = await request(createSceneV3Request);
        if (resp.data.code != "200") {
          setIsAction(false);
          Message.warning(resp.data.msg);
        } else {
          setIsAction(false);
          Message.success("操作成功");
          history.push("/expertPool/list");
        }
      }
    } catch (error) {
      setIsAction(false);
      api.onRequestError(error);
    }
  };

  // 新建 - 发布
  const submitPool = async () => {
    let createSceneV3Request = { ...query, scenePoolType, customRuleType, tempId:tempPoolId };
    try {
      if (!isAction) {
        setIsAction(true);
        let request = api.createScene;
        let resp = await request(createSceneV3Request);
        if (resp.data.code == "200") {
          let id = resp.data.data.data;
          if (id) {
            let result = await api.publishScenePool(id);
            if (result.data.code == "200" && result.data.data.success) {
              Message.success("发布成功");
              setIsAction(false);
              setSubmitPoolId(id);
              setStep(4);
            } else {
              setIsAction(false);
              Message.error(result.data.data.errMessage);
            }
          }
        } else {
          setIsAction(false);
          Message.warning(resp.data.msg);
        }
      }
    } catch (error) {
      setIsAction(false);
      api.onRequestError(error);
    }
  };

  // 编辑 - 保存并发布
  const updatePool = async (isPush = false) => {
    let updateSceneV3Request = {
      ...query,
      scenePoolType,
      sceneBaseId: sceneId,
      customRuleType,
      tempId:tempPoolId
    };
    try {
      if (!isAction) {
        setIsAction(true);
        let request = api.updateScene;
        let resp = await request(updateSceneV3Request);
        if (resp.data.code == "200") {
          let id = resp.data.data.data;
          if (id) {
            if (isPush) {
              let result = await api.publishScenePool(id);
              if (result.data.code == "200" && result.data.data.success) {
                Message.success("发布成功");
                setIsAction(false);
                setSubmitPoolId(id);
                setStep(4);
              } else {
                setIsAction(false);
                Message.error(result.data.data.errMessage);
              }
            } else {
              Message.success("保存成功");
              setIsAction(false);
              setSubmitPoolId(id);
              setStep(4);
            }
          }
        } else {
          setIsAction(false);
          Message.warning(resp.data.msg);
        }
      }
    } catch (error) {
      setIsAction(false);
      api.onRequestError(error);
    }
  };

  const breadcrumbList = [
    { title: "场景专家池", link: "#/expertPool/list" },
    { title: "新建专家池", link: "" },
  ];
  return (
    <div className="expert-pool-create">
      <BreadcrumbTips list={breadcrumbList} />
      <div className="step-wraper">
        <Step current={step} shape="dot">
          {[
            ["基本信息", ""],
            ["场景定义", ""],
            ["圈选规则", ""],
            ["选择筛选条件", ""],
            ["完成", ""],
          ].map((item, index) => (
            <Step.Item
              key={`${item[0]}_${index}`}
              title={item[0]}
              content={item[1]}
            />
          ))}
        </Step>
      </div>
      {step == 0 && (
        <BaseInfo
          isEdit={isEdit}
          isCanEditRules={isCanEditRules}
          goStep={setStep}
          baseInfo={baseInfo}
          scenarioDefined={scenarioDefined}
          setScenarioRules={setScenarioRules}
          setCustomizeScenarioRules={setCustomizeScenarioRules}
          setSelectionRules={setSelectionRules}
          getGroupOptions={getGroupOptions}
          groupOptions={groupOptions}
          updateBaseInfo={updateBaseInfo}
          updateScenarioDefined={updateScenarioDefined}
          updatePool={updatePool}
        ></BaseInfo>
      )}
      {step == 1 && (
        <ScenarioDefined
          isEdit={isEdit}
          scenePoolType={scenePoolType}
          goStep={setStep}
          scenarioRules={scenarioRules}
          customRuleType={customRuleType}
          customizeScenarioRules={customizeScenarioRules}
          getCustomRuleType={getCustomRuleType}
          scenarioDefined={scenarioDefined}
          updateScenarioDefined={updateScenarioDefined}
          getGroupOptions={getGroupOptions}
          groupOptions={groupOptions}
          setCategoryPreview={setCategoryPreview}
          setSelectedCityIdList={setSelectedCityIdList}
        ></ScenarioDefined>
      )}
      {step == 2 && (
        <TagRules
          isEdit={isEdit}
          selectionRules={selectionRules}
          effectRules={effectRules}
          requestEffectRules={requestEffectRules}
          groupOptions={groupOptions}
          sourcePoolId={baseInfo.sourcePoolId}
          getEffectRules={getEffectRules}
          goStep={setStep}
          getGroupOptions={getGroupOptions}
          setGoodsPreviewDialog={setGoodsPreviewDialog}
          scenarioRules={scenarioRules}
          scenePoolType={scenePoolType}
        ></TagRules>
      )}
      {step == 3 && (
        <VisibilityScreenCriteria
          isEdit={isEdit}
          effectRules={effectRules}
          requestEffectRules={requestEffectRules}
          visibilityRules={visibilityRules}
          groupOptions={groupOptions}
          getEffectRules={getEffectRules}
          goStep={setStep}
          getGroupOptions={getGroupOptions}
          setGoodsPreviewDialog={setGoodsPreviewDialog}
          getVisibilityRules={getVisibilityRules}
          submitPool={submitPool}
          savePool={savePool}
          updatePool={updatePool}
        ></VisibilityScreenCriteria>
       )}
      {step == 4 && (
        <FinishPage history={history} submitPoolId={submitPoolId} />
      )}
      {categoryPreview && (
        <CategoryPreviewDialog
          tempPoolId={tempPoolId}
          sourcePoolId={baseInfo.sourcePoolId}
          scenePoolType={scenePoolType}
          selectedCityIdList={selectedCityIdList}
          sceneGroup={baseInfo.sceneName}
          categoryPreview={categoryPreview}
          groupOptions={groupOptions}
          sceneFilterFieldBizModelList={query["sceneFilterFieldList"]}
          goStep={setStep}
          setCategoryPreview={setCategoryPreview}
        ></CategoryPreviewDialog>
      )}
      {goodsPreviewDialog && (
        <GoodsPreviewDialog
          tempPoolId={tempPoolId}
          scenePoolType={scenePoolType}
          sourcePoolId={baseInfo.sourcePoolId}
          isEdit={isEdit}
          customRuleType={customRuleType}
          sceneGroup={baseInfo.sceneName}
          effectRules={effectRules}
          requestEffectRules={requestEffectRules}
          visible={goodsPreviewDialog}
          sceneFilterFieldList={query["sceneFilterFieldList"]}
          goStep={setStep}
          setGoodsPreviewDialog={setGoodsPreviewDialog}
        ></GoodsPreviewDialog>
      )}
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(expertPoolCreate),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const expertPoolCreatePage = permissionAccess(LogTimePutInPage);
