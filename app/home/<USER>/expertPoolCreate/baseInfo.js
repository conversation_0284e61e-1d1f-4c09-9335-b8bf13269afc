import React, { useEffect, useState } from "react";
import { Form, Radio, Input, Button, Field, Grid } from "@alife/next";
import { CascaderSelect, Dialog } from "@alife/theme-nr-op";
import { promisify } from "@/utils/others";
import * as api from "@/adator/api";
import { deepCopy, fetchGroupOption } from "../common";
const { Row, Col } = Grid;
const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: {
    span: 18,
  },
  style: {
    width: "100%",
  },
};

export default function BaseInfo(props) {
  const [sceneClass, setSceneClass] = useState({}); //场景分类树
  const [sceneClassName, setSceneClassName] = useState([]); //场景名称数据来源
  const [data, setData] = useState(props.baseInfo || {}); //当前数据
  const [newGroupOptions, setNewGroupOptions] = useState(
    props.groupOption || {}
  ); //数据源

  const field = Field.useField({
    onChange: (name, value) => {
      // 由于每个场景分类对应的底池是不一样的。如果场景分类变动，需要对底池的内容进行删除：
      switch (name) {
        case "sceneName":
          if (props.scenarioDefined && props.scenarioDefined.length > 0) {
            Dialog.confirm({
              title: "注意",
              content:
                "检测到您场景定义内容不为空，如果修改场景分类则会导致场景定义内容丢失，是否继续？",
              onOk: () => {
                props.updateScenarioDefined([]);
                props.updateBaseInfo("sceneCat", sceneClass[value]);
                props.updateBaseInfo("sceneName", value);
                props.updateBaseInfo("sceneGroup", value);
                data["sceneName"] = value;
                setData(data);
              },
              onCancel: () => {},
            });
          } else {
            props.updateBaseInfo("sceneCat", sceneClass[value]);
            props.updateBaseInfo("sceneName", value);
            props.updateBaseInfo("sceneGroup", value);
            data["sceneName"] = value;
            setData(data);
          }
          break;
        default:
          props.updateBaseInfo(name, value);
          data[name] = value;
          setData(data);
          break;
      }
      field.setValues(data);
    },
  });

  useEffect(() => {
    // 请求场景分类树
    (async () => {
      await fetchSceneCatTree();
    })();
  }, []);

  // 判断基本信息是否存在，存在则初始化
  useEffect(() => {
    if (JSON.stringify(props.baseInfo) != "{}") {
      field.setValues(props.baseInfo);
    }
  }, [props]);

  /*获取场景分类*/
  const fetchSceneCatTree = async () => {
    try {
      if (sceneClassName.length <= 0) {
        let request = api.queryNewSceneCatTree;
        let resp = await request();
        resp.data.data.data.map((item) => {
          if (item.children && item.children.length > 0) {
            item.children.map((childItem) => {
              if (childItem.children && childItem.children.length > 0) {
                childItem.children.map((childNextItem) => {
                  sceneClass[childNextItem.value] = childItem.label;
                })
              }
              return childItem;
            });
          }
          return item;
        });
        setSceneClass(sceneClass);
        setSceneClassName(resp.data.data.data);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /**
   * 基于场景and池子id 获取相关指标
   */
  const getRulesBySource = async () => {
    try {
      const resolveList = await Promise.all([
        new Promise(async (resolve, reject) => {
          try {
            let resp = await api.getRulesBySource(field.getValue("sceneName"));
            // 场景多维度相关指标
            props.setScenarioRules(resp.data);
            resolve(resp.data);
          } catch (error) {
            api.onRequestError(error);
            reject([]);
          }
        }),
        new Promise(async (resolve, reject) => {
          try {
            let resp = await api.getCustomRulesBySource(
              field.getValue("sceneName")
            );
            // 场景自定义维度相关指标
            props.setCustomizeScenarioRules(resp.data);
            resolve(resp.data);
          } catch (error) {
            api.onRequestError(error);
            reject([]);
          }
        }),
        new Promise(async (resolve, reject) => {
          try {
            let resp = await api.getRulesBySource(
              field.getValue("sourcePoolId")
            );
            // 圈选池子相关指标
            props.setSelectionRules(resp.data);
            resolve(resp.data);
          } catch (error) {
            api.onRequestError(error);
            reject([]);
          }
        }),
      ]);
      let requestList = {};
      resolveList.map((item) => {
        item.map((ruleItem) => {
          if (ruleItem.filterFieldDataType == 1) {
            newGroupOptions[ruleItem.filterFieldId] = JSON.parse(
              ruleItem.filterFieldData
            );
          } else if (ruleItem.filterFieldDataType == 2) {
            requestList[ruleItem.filterFieldId] = ruleItem.filterFieldData;
          }
        });
      });
      // TODO 请求所有数据
      // (async () => {
      //   let promiseList = [];
      //   Object.keys(requestList).forEach(async (key) => {
      //     promiseList.push(fetchGroupOption(key, requestList[key]));
      //   });
      //   const promiseResolveList = await Promise.all(promiseList);
      //   promiseResolveList.map((item) => {
      //     Object.keys(item).forEach((keyItem) => {
      //       if (keyItem == "scene_city_id") {
      //         newGroupOptions[keyItem] = [
      //           ...item[keyItem],
      //           { value: "all", label: "全国" },
      //         ];
      //       } else {
      //         newGroupOptions[keyItem] = item[keyItem];
      //       }
      //     });
      //   });
      //   let lastGroupOptions = {...props.groupOption,...newGroupOptions}
      //   props.getGroupOptions(lastGroupOptions)
      // })();
      // props.getGroupOptions(newGroupOptions);
      await fetchSkuCategory();
      await fetchMainCategory();
      await fetchCities();
      await fetchStoreCategory();
      await fetchMarketingType(field.getValue("sourcePoolId"));
      props.goStep(1);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取商品分类数据*/
  const fetchSkuCategory = async () => {
    try {
      if (
        !(
          newGroupOptions["item_goodsCategory"] &&
          newGroupOptions["item_goodsCategory"].length > 0
        )
      ) {
        let request = api.getSkuCategory;
        let resp = await request();
        const dataSource = resp.data.map((dataItem) => {
          dataItem.value = dataItem.value.toString();
          dataItem.children &&
            dataItem.children.map((subItem) => {
              subItem.value = subItem.value.toString();
              subItem.children &&
                subItem.children.map((thirdItem) => {
                  thirdItem.value = thirdItem.value.toString();
                });
            });
          return dataItem;
        });
        ctrlGroupOptions("item_goodsCategory", dataSource);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };
  // 获取主营类目数据
  const fetchMainCategory = async () => {
    try {
      if (
        !(
          newGroupOptions["store_main_category_id"] &&
          newGroupOptions["store_main_category_id"].length > 0
        )
      ) {
        let request = api.getNewAllStoreMainCategory;
        let resp = await request();
        ctrlGroupOptions("store_main_category_id", resp.data);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取所在城市数据*/
  const fetchCities = async () => {
    try {
      if (
        !(
          newGroupOptions["scene_city_id"] &&
          newGroupOptions["scene_city_id"].length > 0
        )
      ) {
        let request = api.getCities;
        let resp = await request();
        ctrlGroupOptions("store_city_id", resp.data.data);
        ctrlGroupOptions("scene_city_id", resp.data.data);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取门店分类数据*/
  const fetchStoreCategory = async () => {
    try {
      if (
        !(
          newGroupOptions["store_cat2_id"] &&
          newGroupOptions["store_cat2_id"].length > 0
        )
      ) {
        let request = api.getCategoryStore;
        let resp = await request();
        ctrlGroupOptions("store_cat2_id", resp.data.data);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取商品活动类型数据*/
  const fetchMarketingType = async (sourcePoolId) => {
    try {
      if (
        !(
          newGroupOptions["item_activity_types"] &&
          newGroupOptions["item_activity_types"].length > 0
        )
      ) {
        let request = api.queryMarketingType;
        let resp = await request(sourcePoolId);
        ctrlGroupOptions("item_activity_types", resp.data.data.data);
        ctrlGroupOptions("activity_child_type", resp.data.data.data);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取品牌名数据*/
  const fetchBrand = async (brandName) => {
    try {
      let request = api.queryBrand;
      let resp = await request({ brandName });
      ctrlGroupOptions("sku_brand_id", resp.data.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const ctrlGroupOptions = (key, data) => {
    newGroupOptions[key] = data;
    props.getGroupOptions(newGroupOptions);
    setNewGroupOptions(newGroupOptions);
  };

  // 下一步
  const saveAndnextStep = async () => {
    await promisify(field.validate)();
    getRulesBySource();
  };

  // 编辑 - 保存并发布
  const saveAndRelease = async () => {
    await promisify(field.validate)();
    props.updatePool();
  };

  return (
    <>
      <div className="baseinfo">
        <h3>1.基本信息</h3>
        <Form style={{ margin: "10px" }} field={field} className="base-form">
          <Row>
            <Col span={9}>
              <FormItem required label="专家池名称" {...formItemLayout}>
                <Input
                  {...field.init("basePoolName", {
                    rules: [
                      {
                        required: true,
                        message: "专家池名称不能为空",
                        trigger: "onBlur",
                      },
                      {
                        maxLength: 20,
                        message: "专家池名称不能超过20个字",
                        trigger: "onBlur",
                      },
                    ],
                  })}
                  placeholder="请输入专家池名称"
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={9}>
              <FormItem required label="底池数据源" {...formItemLayout}>
                <RadioGroup
                  disabled={props.isEdit}
                  shape="button"
                  size="large"
                  {...field.init("sourcePoolId", {
                    rules: [
                      {
                        required: true,
                        message: "底池数据源不能为空",
                        trigger: "onBlur",
                      },
                    ],
                  })}
                >
                  <Radio id="50005" value="50005">
                    零售全量品池
                  </Radio>{" "}
                  <Radio id="60005" value="60005">
                    医药全量品池
                  </Radio>
                </RadioGroup>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={9}>
              <FormItem required label="场景名称" {...formItemLayout}>
                <CascaderSelect
                  showSearch
                  disabled={props.isEdit}
                  {...field.init("sceneName", {
                    rules: [
                      {
                        required: true,
                        message: "场景名称不能为空",
                        trigger: "onBlur",
                      },
                    ],
                  })}
                  defaultValue={""}
                  dataSource={sceneClassName}
                  style={{ width: "100%" }}
                />
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span={9}>
              <FormItem required label="场景描述" {...formItemLayout}>
                <Input.TextArea
                  {...field.init("basePoolDesc", {
                    rules: [
                      {
                        required: true,
                        message: "场景描述不能为空",
                        trigger: "onBlur",
                      },
                    ],
                  })}
                  maxLength={200}
                  placeholder="请描述场景的定义，包括圈选商品的条件，触发场景的时间、人群、星期、时段等，至多200字。"
                  rows={4}
                  hasLimitHint={true}
                  autoHeight={{ minRows: 4, maxRows: 10 }}
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>
      <div className="btn-panel align">
        {props.isEdit && !props.isCanEditRules ? (
          <Button type={"primary"} onClick={() => saveAndRelease()}>
            保存
          </Button>
        ) : (
          <Button type={"primary"} onClick={() => saveAndnextStep()}>
            下一步
          </Button>
        )}
      </div>
    </>
  );
}
