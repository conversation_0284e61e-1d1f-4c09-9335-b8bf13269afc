import React, { useEffect, useState } from "react";
import {
  Form,
  Select,
  Input,
  Button,
  Field,
  Grid,
  Message,
  Dialog,
  Icon,
  Table,
  Pagination,
  Balloon,
} from "@alife/next";
import { operatorEum, renderNewStateMap } from "../common/index";
import "./style.scss";
import * as api from "@/adator/api";
import { promisify } from "@/utils/others";
import PreviewImage from "@/components/PreviewImage";
import { validatorMap } from "@/home/<USER>/common/config";
import { deepCopy } from "@/home/<USER>/common";
const { Row, Col } = Grid;
const FormItem = Form.Item;

const DEFAULT_GOODS_IMG = require("../../../images/default-goods-pic.png");

export default function GoodsPreviewDialog(props) {
  
  const [visible, setVisible] = useState(props.visible);
  const [dataSource, setDataSource] = useState([]);
  const [deleteDataSource, setDeleteDataSource] = useState([]);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(50);
  const [isLoading, setIsLoading] = useState(false);
  const [tabidx, setTabidx] = useState(1); //1:初选商品  0：删除商品
  const [tipsText, setTipsText] = useState("删除");
  const [total, setTotal] = useState(0);
  const [orderDirection, setOrderDirection] = useState("desc");
  const [orderBy, setOrderBy] = useState("d7ValidOrderCnt");
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const { sourcePoolId } = props;
  const [isSearch, setIsSearch] = useState(false);
  const [effectFilterFieldBizModelList, setEffectFilterFieldBizModelList] =
    useState([
      {
        filterFieldId: "goodsId",
        filterFieldKey: "goodsId",
        filterFieldValue: JSON.stringify([]),
        operator: operatorEum["goodsId"],
      },
    ]);
  const [filterKey, setFilterKey] = useState("goodsId");
  const [filterValue, setFilterValue] = useState("");
  const columns = [
    {
      title: "商品",
      dataIndex: "goodsName",
      lock: "left",
      width: 280,
      cell: (name, index, record) => {
        let { goodsUPCId } = record;
        let goodsUPCIdArray = goodsUPCId ? goodsUPCId.split(",") : [];
        return (
          <Row>
            <Col span={8}>
              <PreviewImage
                src={record.goodsPic || DEFAULT_GOODS_IMG}
                title={record.goodsName}
              />
            </Col>
            <Col span={16}>
              <p className="name">{record.goodsName}</p>
              <p className="tips">商品ID:{record.goodsId}</p>
              {goodsUPCIdArray.length > 1 ? (
                <p className="tips">
                  条形码:{goodsUPCIdArray[0]}
                  <Balloon
                    className="item-upc-ballon"
                    triggerType="click"
                    title=""
                    trigger={
                      <Button text style={{ textDecoration: "underline" }}>
                        {goodsUPCIdArray.length}条
                      </Button>
                    }
                    align="rt"
                    closable={false}
                  >
                    {record.goodsUPCId}
                  </Balloon>
                </p>
              ) : (
                <p className="tips">条形码:{record.goodsUPCId}</p>
              )}
            </Col>
          </Row>
        );
      },
    },
    {
      title: "在售状态",
      dataIndex: "goodsState",
      width: 100,
      cell: (state) => renderNewStateMap(state),
    },
    { title: "原价", dataIndex: "goodsOriginPrice", width: 100 },
    { title: "活动价", dataIndex: "goodsPresentPrice", width: 120 },
    { title: "库存", dataIndex: "goodsNumber", width: 140 },
    {
      title: "近7日销量",
      dataIndex: "d7ValidOrderCnt",
      width: 140,
      sortable: true,
    },
    {
      title: "门店",
      dataIndex: "storeName",
      width: 180,
      cell: (name, index, record) => {
        return (
          <div>
            <p className="name">{name ? name : "-"}</p>
            <p className="tips">ELE门店ID:{record.eleStoreId}</p>
            <p className="tips">淘内门店ID:{record.storeId}</p>
            <p className="tips">淘系卖家ID:{record.sellerId}</p>
          </div>
        );
      },
    },
    {
      title: "操作",
      lock: "right",
      cell: (value, index, record) => {
        return renderOpt(record);
      },
      width: "80px",
    },
  ];

  const viewField = Field.useField({});

  const onSelectChange = (...args) => {
    setSelectedRowKeys(args[1].map((v) => v.goodsId));
  };

  const renderOpt = (record) => {
    return (
      <Balloon.Tooltip
        align="t"
        trigger={
          <Button
            disabled={true}
            type={"primary"}
            text
            className="item-delete"
            onClick={() => deleteGood(record.goodsId)}
          >
            {tipsText}
          </Button>
        }
      >
        正在开发中～
      </Balloon.Tooltip>
    );
  };

  const deleteGood = async (goodsId) => {
    const { tempPoolId, sourcePoolId, requestEffectRules } = props;
    Dialog.confirm({
      title: "提示",
      content: `确定${tipsText}么?`,
      onOk: async () => {
        try {
          let req = {
            poolType: "goods",
            addOrDelete: tabidx,
            targetIdList: [goodsId],
          };
          req.tempPoolId = tempPoolId;
          req.operateType = 3;
          let request = api.manualUpdate;
          let resp = await request(req);
          if (resp.success) {
            Message.success(`${tipsText}成功`);
            getTableData();
          } else {
            Message.warning(resp.errMessage || `${tipsText}失败`);
          }
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  useEffect(() => {
    getTableData();
  }, [props.requestEffectRules, props.effectRules, tabidx]);

  const getTableData = () => {
    setSelectedRowKeys([]);
    if (tabidx == 1) {
      getViewGoodViewList(page, size, isSearch);
    } else {
      getViewDeleteGoodList();
    }
  };

  /*在初选商品列表和删除商品列表之间切换*/
  const changeTabidx = (tabidx) => {
    setTabidx(tabidx);
    setTipsText(tabidx == 1 ? "删除" : "恢复");
  };

  /* 删除商品列表,后端接口暂时不支持分页*/
  const getViewDeleteGoodList = () => {
    try {
      api.queryDeleteList(props.tempPoolId).then((resp) => {
        setDeleteDataSource(resp.data);
      });
    } catch (error) {
      console.log(error);
      api.onRequestError(error);
    }
  };

  //预览
  const getViewGoodViewList = async (page = 1, size = 50, isSearch = false) => {
    if (isSearch) {
      const outerForm = await promisify(viewField.validate)();
    }
    setIsLoading(true);
    setIsSearch(isSearch);
    const {
      tempPoolId,
      sourcePoolId,
      requestEffectRules,
      sceneBaseId,
      sceneFilterFieldList,
      sceneGroup,
      customRuleType
    } = props;
    try {
      let req = {
        sceneGroup,
        tempPoolId,
        sourcePoolId,
        sceneBaseId,
        pageSize: size,
        pageIndex: page,
        customRuleType,
        orderDirection: orderDirection || "desc",
        orderBy: orderBy || "d7ValidOrderCnt",
        effectFilterFieldList: !isSearch
          ? requestEffectRules
          : requestEffectRules.concat(effectFilterFieldBizModelList),
        sceneFilterFieldList: sceneFilterFieldList,
      };
      api.queryItemViewList(req).then((resp) => {
        if (resp.data.code == "200" && resp.data.data.success) {
          let newDataSource = resp.data.data.data.filter((v) => v.status == 1);
          setDataSource(newDataSource);
          setTotal(resp.data.data.totalCount);
        } else {
          Message.error(resp.data.msg || resp.data.data.errMessage);
        }
        setIsLoading(false);
      });
    } catch (error) {
      console.log(error);
      api.onRequestError(error);
      setIsLoading(false);
    }
  };

  // 页面更改
  const onPageChange = (page) => {
    setPage(page);
    getViewGoodViewList(page, size);
  };

  // 页面数量改变
  const onPageSizeChange = (size) => {
    setSize(size);
    getViewGoodViewList(page, size);
  };

  // 下一步
  const nextStep = () => {
    props.goStep(3);
    props.setGoodsPreviewDialog(false);
  };

  // TODO 删除
  const batchDelete = async () => {
    const { tempPoolId, sourcePoolId, requestEffectRules } = props;
    Dialog.confirm({
      title: "提示",
      content: `确定批量${tipsText}?`,
      onOk: async () => {
        try {
          let req = {
            poolType: "goods",
            addOrDelete: tabidx,
            targetIdList: selectedRowKeys,
          };
          req.tempPoolId = tempPoolId;
          req.operateType = 3;
          let request = api.manualUpdate;
          let resp = await request(req);
          Message.success(`${tipsText}成功`);
          getTableData();
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  const onChangeType = (value) => {
    let filterFieldValue =
      effectFilterFieldBizModelList.length > 0
        ? effectFilterFieldBizModelList[0].filterFieldValue
        : 0;
    let newEffectFilterFieldBizModelList = [];
    let item = {
      filterFieldId: value,
      filterFieldKey: value,
      operator: operatorEum[value],
      filterFieldValue,
    };
    newEffectFilterFieldBizModelList.push(item);
    setFilterKey(value);
    setEffectFilterFieldBizModelList(newEffectFilterFieldBizModelList);
  };

  const onInputChange = (value) => {
    let newEffectFilterFieldBizModelList = deepCopy(
      effectFilterFieldBizModelList
    );
    let item = newEffectFilterFieldBizModelList[0];
    item.filterFieldValue = value ? JSON.stringify(value.split(",")) : "[]";
    setFilterValue(value);
    setEffectFilterFieldBizModelList(newEffectFilterFieldBizModelList);
  };

  // 查询接口
  const searchGoodList = () => {
    setIsSearch(true);
    setPage(1);
    getViewGoodViewList(page, size, true);
  };

  // 排序接口
  const onSort = (key, order) => {
    setOrderDirection(order);
    setOrderBy(key);
    getTableData();
  };

  const filterDs = [
    { label: "商品名称", value: "goodsNameKeyWord" },
    { label: "商品条码", value: "upcId" },
    { label: "商品ID", value: "goodsId" },
  ];
  const validatorsGroup = {
    goodsId: validatorMap.idCommaSeperatedRequired,
    upcId: validatorMap.upcIdCommaSeperated,
    goodsNameKeyWord: "",
  };

  return (
    <Dialog
      title={
        <>
          {tabidx == 1 ? (
            <span>{`初选结果数量：${total}个`}</span>
          ) : (
            <div className="delele-title">
              <Icon type={"left"} />
              <Button className="btn-back" text onClick={() => changeTabidx(1)}>
                返回
              </Button>
              <span>当前位置：已删商品</span>
            </div>
          )}
        </>
      }
      visible={visible}
      top={10}
      height={`${document.body.clientHeight * 0.84}px`}
      width={`${document.body.clientWidth * 0.67}px`}
      className={"view-dialog"}
      onClose={() => props.setGoodsPreviewDialog(false)}
      footer={
        <>
          {tabidx == 1 ? (
            <div className="footer-buttons">
              <Balloon.Tooltip
                align="t"
                trigger={
                  <Button disabled={true} onClick={() => changeTabidx(0)}>
                    已删商品
                  </Button>
                }
              >
                正在开发中～
              </Balloon.Tooltip>
              <Button
                className="btn-cancel"
                onClick={() => props.setGoodsPreviewDialog(false)}
              >
                取消
              </Button>
              <Button type={"primary"} onClick={() => nextStep()}>
                下一步
              </Button>
            </div>
          ) : (
            <div className="footer-buttons">
              <Button onClick={() => changeTabidx(1)}>返回</Button>
            </div>
          )}
        </>
      }
    >
      <div className="filter-panel">
        <div className="batch-panel">
          <Icon type="prompt" style={{ color: "#FF7000" }} />
          已选<span>{selectedRowKeys.length}</span>项
          <Balloon.Tooltip
            align="t"
            trigger={
              <Button
                onClick={batchDelete}
                disabled={true || selectedRowKeys.length == 0}
              >
                批量{tipsText}
              </Button>
            }
          >
            正在开发中～
          </Balloon.Tooltip>
        </div>
        {tabidx == 1 && (
          <div className="rules">
            <Form
              field={viewField}
              inline={true}
              className="rules-views-form"
              labelAlign="left"
            >
              <FormItem
                label={
                  <Select
                    dataSource={filterDs}
                    value={filterKey}
                    onChange={onChangeType}
                  />
                }
                validator={validatorsGroup[filterKey]}
              >
                <Input
                  style={{ width: "200px" }}
                  onChange={onInputChange}
                  value={filterValue}
                  name={effectFilterFieldBizModelList[0].filterFieldKey}
                />
                <Button
                  type="secondary"
                  style={{ marginLeft: "10px" }}
                  onClick={searchGoodList}
                >
                  搜索
                </Button>
              </FormItem>
            </Form>
          </div>
        )}
      </div>
      <div
        style={{
          overflow: "auto",
          height: `${document.body.clientHeight * 0.84 - 250}px`,
        }}
      >
        {tabidx == 1 && (
          <Table
            rowSelection={{
              onChange: onSelectChange,
              columnProps: () => {
                return {
                  lock: "left",
                  width: 90,
                  align: "center",
                };
              },
            }}
            loading={isLoading}
            onSort={onSort}
            dataSource={dataSource}
            hasBorder={false}
            primaryKey="goodsId"
          >
            {columns.map((e, idx) => {
              return <Table.Column {...e} key={idx} />;
            })}
          </Table>
        )}
        {tabidx == 0 && (
          <Table
            rowSelection={{
              onChange: onSelectChange,
              columnProps: () => {
                return {
                  lock: "left",
                  width: 90,
                  align: "center",
                };
              },
            }}
            loading={isLoading}
            onSort={onSort}
            dataSource={deleteDataSource}
            hasBorder={false}
            primaryKey="goodsId"
          >
            {columns.map((e, idx) => {
              return <Table.Column {...e} key={idx} />;
            })}
          </Table>
        )}
        {tabidx == 1 && (
          <Pagination
            popupProps={{ align: "bl tl" }}
            pageSizeList={[50, 100]}
            shape="arrow-only"
            style={{ float: "right", marginTop: "10px" }}
            current={page}
            total={total}
            pageSize={size}
            onChange={onPageChange}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
            onPageSizeChange={onPageSizeChange}
          />
        )}
      </div>
    </Dialog>
  );
}
