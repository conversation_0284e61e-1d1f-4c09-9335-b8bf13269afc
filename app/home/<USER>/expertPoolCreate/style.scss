.expert-pool-create {
  margin: 12px 12px 0 12px;
  position: relative;
  .step-wraper {
    background-color: #fff;
    margin: 12px;
    padding: 20px 0 5px;
  }
  .baseinfo {
    background-color: #fff;
    margin: 12px;
    padding: 20px;
    & > h3 {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      font-weight: normal;
      margin: 0;
      padding: 0;
      cursor: pointer;
      & > .next-btn {
        cursor: pointer;
      }
    }
    .next-radio-group.next-radio-button.next-radio-button-medium {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .next-radio-wrapper {
        height: 40px;
        width: 48%;
        span {
          display: flex;
          justify-content: center;
        }
        .next-radio-label {
          height: 100%;
          align-items: center;
        }
      }
    }
  }
  .scenario-defined {
    position: relative;
    background-color: #fff;
    margin: 12px;
    margin-bottom: 70px;
    padding: 20px;
    & > h3 {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      font-weight: normal;
      margin: 0;
      padding: 0;
      cursor: pointer;
      & > .next-btn {
        cursor: pointer;
      }
    }
    .tab-content-group {
      .next-col {
        &.left {
          padding-left: 14px;
        }
        &.right {
          background: #fafafa;
          margin: 12px 24px 0 10px;
          padding: 0 10px 10px 10px;
          & > .right-title {
            margin-bottom: 16px;
            span {
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #9e9e9e;
              float: right;
              .next-icon {
                margin-right: 5px;
              }
            }
          }
          .cityGroup {
            background-color: red;
          }
          .next-collapse-panel-content {
            background-color: rgba(250, 250, 250, 1);
            .next-form-item {
              padding: 2px 10px 7px 10px;
              background-color: #fff;
              border-radius: 4px;
              margin-bottom: 8px;
              .next-form-item-label {
                font-family: PingFangSC-Medium;
                font-size: 14px;
                color: #333333;
                padding-right: 0;
                width: 100%;
                text-align: left;
                & > label {
                  .next-icon {
                    float: right;
                    margin-top: 5px;
                  }
                }
              }
              .next-form-item-control span {
                font-family: PingFangSC-Regular;
                font-size: 14px;
                color: #666666;
              }
            }
            .next-input {
              width: 100%;
            }
          }
        }
      }
      .next-tabs {
        margin: 13px 0 0 0px;

        .next-tabs-bar {
          border: 1px solid #ebebeb;
          border-bottom: 0;
        }
      }
      .check-radio{
        padding-top: 10px;
      }
      .check-list {
        p {
          padding: 0;
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #999999;
          margin: 7px 0 10px 0;
        }
        .next-checkbox-wrapper {
          margin-left: 12px !important;
          margin-bottom: 10px;
          display: inline-block;
          .next-checkbox-label {
            width: 150px;
            display: inline-block;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            line-height: 14px;
            color: #333333;
          }
        }
        .bottom-pool-rules {
          margin-top: 12px;
          .topName {
            display: flex;
            font-family: PingFangSC-Medium;
            font-size: 14px;
            line-height: 25px;
            color: #333333;
            font-weight: normal;
            margin: 0;
            padding: 0;
            .delete {
              display: inline-block;
              margin-left: 10px;
              cursor: pointer;
              color: #ff7c4d;
              &:hover {
                opacity: 0.8;
              }
            }
          }
          .selectionCity{
            display: inline-block;
            padding-left: 15px;
            div{
              display: inline-block;
              margin-right: 10px;
              button{
                font-size: 13px;
              }
            }
            i{
              margin-left: 5px;
              cursor: pointer;
            }
          }
          .selectRule {
            display: inline-block;
            padding-left: 20px;
            .ruleItem {
              display: inline-block;
            }
          }
        }
        .noRules {
          font-family: PingFangSC-Medium;
          font-size: 16px;
          line-height: 50px;
          color: red;
          font-weight: normal;
          display: inline-block;
          img {
            display: block;
            height: 500px;
            width: 500px;
          }
        }
      }
    }
    .btn-panel {
      height: 40px;
      opacity: 0;
    }
  }
  .fixed-btn-panel {
    margin: 12px;
    margin-bottom: 0;
    background-color: #fff;
    padding: 10px 20px;
    width: 100%;
    position: fixed;
    bottom: 0;
    z-index: 100;
    .next-step {
      display: inline-block;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-10%, -50%);
    }
  }
  .label-rules {
    background-color: #fff;
    margin: 12px 12px 60px 12px;
    padding: 20px 20px;
    & > h3 {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      font-weight: normal;
      margin: 0;
      padding: 0;
    }
    .search-panel {
      margin: 16px 0 0 24px;
      label {
        font-family: PingFangSC-Regular !important;
        font-size: 14px;
        color: #333333;
        font-weight: normal;
        margin-right: 9px;
      }
      .next-input {
        width: 278px;
        margin-right: 13px;
      }
    }
    .tab-content-group {
      .next-col {
        &.right {
          background: #fafafa;
          margin: 12px 24px 0 10px;
          padding: 0 10px 10px 10px;
          & > .right-title {
            margin-bottom: 16px;
            span {
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #9e9e9e;
              float: right;
              .next-icon {
                margin-right: 5px;
              }
            }
          }
        }
      }
      .next-tabs {
        margin: 13px 0 0 0px;

        .next-tabs-bar {
          border: 1px solid #ebebeb;
          border-bottom: 0;
        }
      }
      .check-list {
        border: 1px solid #ebebeb;
        padding: 8px 20px 24px 20px;
        p {
          padding: 0;
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #999999;
          margin: 7px 0 10px 0;
        }
        .next-checkbox-wrapper {
          margin-left: 12px !important;
          margin-bottom: 10px;
          display: inline-block;
          .next-checkbox-label {
            width: 150px;
            display: inline-block;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            line-height: 14px;
            color: #333333;
          }
        }
      }
    }
    .rules-form {
      .next-form-item {
        padding: 2px 10px 7px 10px;
        background-color: #fff;
        border-radius: 4px;
        margin-bottom: 8px;
        .next-form-item-label {
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #333333;
          padding-right: 0;
          width: 100%;
          text-align: left;
          & > label {
            .next-icon {
              float: right;
              margin-top: 5px;
            }
          }
        }
        .next-form-item-control span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #666666;
        }
      }
      .next-input {
        width: 100%;
      }
      .btn-panel {
        margin-top: 12px;
        overflow: hidden;
        .next-btn {
          float: right;
          margin-left: 8px;
        }
      }
    }
    .btn-panel {
      margin-top: 12px;
      text-align: center;
      overflow: hidden;
      .next-btn {
        margin-left: 8px;
      }
    }
  }
  .btn-panel {
    margin: 12px;
    margin-bottom: 0;
    background-color: #fff;
    padding: 10px 20px;
    width: 100%;
    position: fixed;
    bottom: 0;
    z-index: 100;
    .next-step {
      display: inline-block;
      transform: translate(35%, 0);
      button {
        margin-right: 12px;
      }
    }
    &.align {
      text-align: center;
      width: 85.5%;
    }
  }
  .next-dialog-body {
    padding-top: 0 !important;
  }
}
.dialog-text {
  max-width: 500px;
  font-family: PingFangSC-Medium;
  font-size: 14px;
  line-height: 25px;
  color: #333333;
  font-weight: normal;
  .rules {
    color: #ff7c4d;
  }
}
.view-dialog {
  padding-top: 0;
  .title-sum {
    max-height: 70px;
    overflow: auto;
    display: flex;
    flex-wrap: nowrap;
    flex-shrink: 0;
    .title-sum-left {
      display: inline-block;
      width: 120px;
    }
    .title-sum-right {
      display: flex;
      flex-wrap: wrap;
      width: 90%;
    }
    .col2 {
      display: inline-block;
      width: 50%;
    }
  }
  .filter-panel{
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    .batchDelete{
      margin-bottom: 16px;
    }
    
  }
  .rules-views-form {
    .next-col {
      margin-left: 10px;
    }
    .sceneCity {
      >span {
        max-height: 80px;
        overflow-y: auto;
        overflow-x: hidden;
      }
    }
  }
  .footer-buttons {
    > span {
      float: left;
    }
  }
  .exponent {
    span {
      display: inline-block;
      font-size: 4px;
      transform: translate(20%, -20%);
      vertical-align: text-top;
    }
  }
}
