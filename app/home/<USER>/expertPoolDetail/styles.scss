@import "../../../styles/common.scss";
.exportDetail {
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  > div {
    background-color: #fff;
    padding: 24px;
    margin-bottom: 10px;
  }
  .base-info {
    .name {
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #363636;
      font-style: normal;
    }
    .status {
      background: rgba(0, 204, 102, 0.04);
      border: 1px solid #00cc66;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      border-radius: 2px;
      padding: 0 4px;
      display: inline-block;
      margin-left: 12px;

      &.success {
        color: $success_high;
        border-color: $success_high;
      }

      &.notice {
        color: $notice_high;
        border-color: $notice_high;
      }

      &.warning {
        color: $warning_high;
        border-color: $warning_high;
      }

      &.error {
        color: $error_high;
        border-color: $error_high;
      }

      &.help {
        color: $help_high;
        border-color: $help_high;
      }

      &.filed {
        color: $font_tip;
        border-color: $font_tip;
      }
    }
    .next-row {
      margin: 15px 0;
    }
    .next-col-row {
      display: flex;
      justify-content: start;
      flex-shrink: 0;
      label {
        width: 120px;
        text-align: right;
        display: inline-block;
      }
      .row {
        display: inline-block;
        width: 80%;
        overflow: hidden;
        word-break: break-all;
      }
      >div{
        vertical-align: baseline;
      }
    }
  }
  .tab{
    position: relative;
    .button-list{
      position: absolute;
      top: 0px;
      right: 0px;
      display: flex;
      flex-direction: row;
      .left{
        display: flex;
        flex-direction: row;
        font-size: 14px;
        .sum,.batch-panel{
          margin: 12px 0;
          margin-left: 10px;
          line-height: 14px;
        }
      }
      .right{
        > *{
          margin-left: 10px;
        }
      }
    }
  }

  .bottom-rules {
    .rules {
      border-bottom: 1px dashed gray;
      &:last-child {
        border: 0px;
      }
    }
    .title {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
    }
    .item {
      margin-bottom: 15px;
      display: inline-block;
    }
    .next-row {
      margin-top: 15px;
    }
  }
  .pool-rules {
    .title {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
    }
    .item {
      margin: 15px 0;
      display: inline-block;
    }
    .item-table {
      margin: 15px 0;
      display: inline-block;
    }
  }

  .sample-pool{
    .title {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      margin-bottom: 15px;
    }
    .rules-views-form{
      .next-form-item{
        display: flex;
        .next-form-item-label{
          padding:0;
        }
        .next-form-item-control{
          position: relative;
          top: 1px;
        }
      }
  
    }
  }
  .btn-more{
    height: 40px;
    line-height: 40px;
    width: 100%;
    border-radius: 4px;
    background-color: #FAFAFA;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #666666;
    margin-top: 12px;
    cursor: pointer;
    &:hover{
      color: #FF7000;
    }
  }
  .deletion-reason{
    width: 500px;
    .deletion-reason-id{
      padding-top: 6px;
      line-height: 20px;
      word-break: break-all;
    }
    .deletion-reason-checkbox{
      label{
        width: 50%;
        text-align: left;
        display: inline-block;
        margin: 0;
      }
    }
  }
}
