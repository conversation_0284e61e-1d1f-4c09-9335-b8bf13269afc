import React, { useEffect, useState } from "react";
import { goldLog, logTimeComponent, track } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { Grid } from "@alife/next";
import moment from "moment";
import { formatTimeStamp, FORMAT } from "@/utils/time";
import { optionMap } from "@/home/<USER>/common/map";
import * as api from "@/adator/api";
import PoolRules from "./poolRules";
import { exportStateMap } from "../common";
import { deepCopy, fetchGroupOption } from "@/home/<USER>/common";
import "./styles.scss";

const { Row, Col } = Grid;
function exportPoolDetil({ history, match }) {
  const { expertPoolId, poolEntryType = 0 } = match.params;
  const [groupOption, setGroupOptions] = useState({});
  const [sceneFilterFieldLists, setSceneFilterFieldLists] = useState([]);
  const [sceneFilterFieldDetail, setSceneFilterFieldDetail] = useState({});
  const [delReasonList, setDelReasonList] = useState([]);//失败原因
  const [statisticsCategory, setStatisticsCategory] = useState([]); //类目统计数组

  const {
    sceneBaseId = 0,
    sourcePoolName = "",
    basePoolName = "",
    catPath = "",
    sceneGroupLabel = "",
    creator = "",
    empId = 0,
    basePoolDesc = "",
    createAt = 0,
    state = 1,
    sceneFilterFieldList = [],
    effectFilterFieldList = [],
    optionalFilterFieldList = [],
    customRuleType = 0,
  } = sceneFilterFieldDetail;

  useEffect(() => {
    (async () => {
      await fetchSceneFilterFieldList();
      await getDelReasonList();
    })();
  }, []);

  useEffect(() => {
    // 设置数据结构
    let newSceneFilterFieldList = [];
    sceneFilterFieldList.map((item) => {
      if (!newSceneFilterFieldList[item.filterFieldRuleGroup]) {
        newSceneFilterFieldList[item.filterFieldRuleGroup] = {};
      }
      if (item.filterFieldId == "scene_city_id" || item.filterFieldId == "store_city_id") {
        newSceneFilterFieldList[item.filterFieldRuleGroup].cityId = item;
      } else {
        if (!newSceneFilterFieldList[item.filterFieldRuleGroup].rules) {
          newSceneFilterFieldList[item.filterFieldRuleGroup].rules = [];
        }
        newSceneFilterFieldList[item.filterFieldRuleGroup].rules.push(item);
      }
    });
    setSceneFilterFieldLists(newSceneFilterFieldList);

    // 设置数据来源
    let newList = [...sceneFilterFieldList, ...effectFilterFieldList];
    let requestList = {};
    newList.map((item) => {
      if (item.filterFieldDataType == 1) {
        groupOption[item.filterFieldId] = JSON.parse(item.filterFieldData);
      } else if (item.filterFieldDataType == 2) {
        requestList[item.filterFieldId] = item.filterFieldData;
      } else {
        groupOption[item.filterFieldId] = optionMap[item.filterFieldKey];
      }
    });
    // 批量请求底池规则和圈选规则中为动态数据源的数据
    (async () => {
      let promiseList = [];
      Object.keys(requestList).forEach(async (key) => {
        promiseList.push(fetchGroupOption(key, requestList[key]));
      });
      const resolveList = await Promise.all(promiseList);
      resolveList.map((item) => {
        Object.keys(item).forEach((keyItem) => {
          if (keyItem == "scene_city_id" || keyItem == "store_city_id") {
            groupOption[keyItem] = [
              ...item[keyItem],
              { value: "all", label: "全国" },
            ];
          } else {
            groupOption[keyItem] = item[keyItem];
          }
        });
      });
      setGroupOptions(deepCopy(groupOption));
    })();
  }, [sceneFilterFieldDetail]);

  // 请求专家池详情
  const fetchSceneFilterFieldList = async () => {
    try {
      let request = api.getScenePoolDetailInfo;
      let resp = await request({
        sceneBaseId: expertPoolId,
        poolEntryType: poolEntryType,
      });
      setSceneFilterFieldDetail(resp.data.data.data);
      customRuleType && customRuleType == 1 && await getCategoryOfCommodities(resp.data.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 获取删除原因列表
  const getDelReasonList = async ()=>{
    try {
      let request = api.getDelReasonList;
      let resp = await request();
      setDelReasonList(resp);
    } catch (error) {
      api.onRequestError(error);
    }
  }

  //请求商品类目list
  const getCategoryOfCommodities = async (requestParams) => {
    try {
      let request = api.querySceneCatCount;
      let resp = await request({
        ...requestParams,
        scenePoolType: requestParams.basePoolType,
      });
      setStatisticsCategory(resp.data.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };
  return (
    <div className="exportDetail">
      <div className="base-info">
        <Row style={{ marginBottom: "20px" }}>
          <Col span={24}>
            <div>
              <span className="name">{basePoolName}</span>
              {
                <span className={`status ${exportStateMap[state].style}`}>
                  {exportStateMap[state].text}
                </span>
              }
            </div>
            <div className="id">ID：{sceneBaseId}</div>
          </Col>
        </Row>
        <Row>
          <Col className="next-col-row" span={12}>
            <label>底池数据源名称：</label>
            <span>{sourcePoolName}</span>
          </Col>
          <Col className="next-col-row" span={12}>
            <label>专家池描述：</label>
            <div className="row">{basePoolDesc}</div>
          </Col>
        </Row>
        <Row>
          <Col className="next-col-row" span={12}>
            <label>场景名称：</label>
            <span>{catPath}</span>
          </Col>
          <Col className="next-col-row" span={12}>
            <label>创建人：</label>
            <span>{`${creator}_${empId}`}</span>
          </Col>
        </Row>
        <Row>
          <Col className="next-col-row" span={12}>
            <label>创建时间：</label>
            <span>{formatTimeStamp(moment(createAt), FORMAT.TIMETwo)}</span>
          </Col>
        </Row>
      </div>
      <PoolRules
        groupOption={groupOption}
        delReasonList={delReasonList}
        sceneFilterFieldDetail={sceneFilterFieldDetail}
        sceneFilterFieldLists={sceneFilterFieldLists}
        effectFilterFieldList={effectFilterFieldList}
        optionalFilterFieldList={optionalFilterFieldList}
      ></PoolRules>
    </div>
  );
}
export const LogTimePutInPage = logTimeComponent(
  withRouter(exportPoolDetil),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const expertPoolDetail = permissionAccess(LogTimePutInPage);
