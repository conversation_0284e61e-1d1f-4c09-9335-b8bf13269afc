import React, { useState, useEffect } from "react";
import "./styles.scss";
import {
  Balloon,
  Grid,
  Icon,
  Tab,
  Table,
  Pagination,
  Button,
  Message,
  Field,
  Form,
  Input,
  Select,
  Dialog,
  Checkbox,
  Loading,
} from "@alife/next";
import * as api from "@/adator/api";
import { AccessBtn } from "@/components/Button";
import {
  changeEumToObject,
  changeToYuan,
  dealQualityScoreToArray,
  deepCopy,
  getTreeName,
} from "../common/index";
import {
  itemStatusEnum,
  getExpertPermission,
  renderNewStateMap,
  getPermission,
} from "@/home/<USER>/common";
import PreviewImage from "@/components/PreviewImage";
import * as validators from "@/utils/validators";
import { deepClone } from "@/utils/others";
const { Row, Col } = Grid;
const FormItem = Form.Item;

const percentage = [
  "scene_annual_growth_rate",
  "scene_grid_coverage",
  "scene_order_proportion",
];

const DEFAULT_GOODS_IMG = require("../../../images/default-goods-pic.png");

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: {
    span: 17,
  },
  style: {
    width: "100%",
  },
  labelAlign: "center",
  labelTextAlign: "center",
};

export default function poolRules(props) {
  const {
    groupOption = {},
    sceneFilterFieldLists = [],
    sceneFilterFieldDetail = {},
    delReasonList = [],
  } = props;
  const {
    sceneBaseId = 0,
    sourcePoolId = 0,
    sceneGroup = "",
    sourcePoolName = "",
    basePoolName = "",
    sceneCat = "",
    sceneGroupLabel = "",
    creator = "",
    empId = 0,
    basePoolDesc = "",
    createAt = 0,
    state = 1,
    sceneFilterFieldList = [],
    effectFilterFieldList = [],
    optionalFilterFieldList = [],
    customRuleType = 0,
  } = sceneFilterFieldDetail;

  const [isShow, setIsShow] = useState(true);
  const [selectedTab, setSelectedTab] = useState("");
  const [selectedFakeKey, setSelectedFakeKey] = useState([]); //当前品类的假key
  const [selectedKey, setSelectedKey] = useState([]); //商品的真key
  const [isMoreLoading, setIsMoreLoading] = useState(false); //加载更多Loading
  const [currentCategory, setCurrentCategory] = useState({
    data: [],
    page: 1,
    size: 10,
    total: 0,
    isLoading: false,
  });

  const [deleteCategory, setDeleteCategory] = useState({
    data: [],
    page: 1,
    size: 10,
    total: 0,
    isLoading: false,
  });

  const [currentGoods, setCurrentGoods] = useState({
    data: [],
    page: 1,
    size: 10,
    total: 1000,
    pageTotal: 100,
    isLoading: false,
    orderDirection: "desc",
  });

  const [deleteGoods, setDeleteGoods] = useState({
    data: [],
    page: 1,
    size: 50,
    total: 1000,
    isLoading: false,
    pageTotal: 100,
    orderDirection: "desc",
  });

  const [operationLogs, setOperationLogs] = useState({
    data: [],
    page: 1,
    size: 10,
    total: 0,
    isLoading: false,
  });

  const field = Field.useField({});
  const deletionReason = Field.useField({});

  const columns = [
    { title: "场景名称", dataIndex: "sceneName", width: 100 },
    { title: "城市", dataIndex: "cityName", width: 100 },
    {
      title: "tgi",
      dataIndex: "tgi",
      width: 100,
      cell: (value, index, record) => {
        return Math.floor(Number(value) * 100) / 100;
      },
    },
    { title: "一级类目名称", dataIndex: "itemCat1Name", width: 150 },
    { title: "二级类目名称", dataIndex: "itemCat2Name", width: 150 },
    { title: "三级类目名称", dataIndex: "itemCat3Name", width: 150 },
    {
      title: "年增速",
      dataIndex: "annualGrowthRate",
      width: 100,
      cell: (value, index, record) => {
        if (value == "-1" || value == "999.99") {
          return "--";
        }
        return Math.floor(Number(value) * 10000) / 100 + "%";
      },
    },
    {
      title: "场景订单占比",
      dataIndex: "orderProportion",
      width: 150,
      cell: (value, index, record) => {
        return Math.floor(Number(value) * 10000) / 100 + "%";
      },
    },
    {
      title: "网格覆盖率",
      dataIndex: "gridCoverage",
      width: 120,
      cell: (value, index, record) => {
        return Math.floor(Number(value) * 10000) / 100 + "%";
      },
    },
    { title: "商品数", dataIndex: "itemNum", width: 100 },
    { title: "门店数", dataIndex: "shopNum", width: 100 },
    {
      title: "操作",
      lock: "right",
      cell: (value, index, record) => {
        return (
          <div className="opbtns">{renderNewOption(record, selectedTab)}</div>
        );
      },
      width: "100px",
    },
  ];

  //商品查询结果
  let goodsColumns = [
    {
      title: "商品",
      dataIndex: "goodsName",
      lock: "left",
      width: 280,
      cell: (name, index, record) => {
        return (
          <Row>
            <Col span={8}>
              <PreviewImage
                src={record.goodsPic || DEFAULT_GOODS_IMG}
                title={record.goodsName}
              />
            </Col>
            <Col span={16}>
              <p className="name">{record.goodsName}</p>
              <p >商品ID:{record.goodsId}</p>
              <p >条形码:{record.goodsUPCId}</p>
            </Col>
          </Row>
        );
      },
    },
    {
      title: "在售状态",
      dataIndex: "goodsState",
      width: 100,
      cell: (_state) => renderNewStateMap(_state),
    },
    { title: "原价", dataIndex: "goodsOriginPrice", width: 100 },
    { title: "活动价", dataIndex: "goodsPresentPrice", width: 120 },
    { title: "库存", dataIndex: "goodsNumber", width: 140 },
    {
      title: "近7日销量",
      dataIndex: "d7ValidOrderCnt",
      width: 140,
      sortable: true,
    },
    {
      title: "门店",
      dataIndex: "storeName",
      width: 180,
      cell: (name, index, record) => {
        return (
          <div>
            <p className="name">{name ? name : "-"}</p>
            <p className="tips">ELE门店ID:{record.eleStoreId}</p>
            <p className="tips">淘内门店ID:{record.storeId}</p>
            <p className="tips">淘系卖家ID:{record.sellerId}</p>
          </div>
        );
      },
    },
    {
      title: "操作",
      lock: "right",
      cell: (value, index, record) => {
        return renderOpt(record);
      },
      width: "80px",
    },
  ];

  const createColumn = (tab)=>{
    if (tab == 'currentGoods') {
      return goodsColumns
    }else if(tab == 'deleteGoods'){
      let result = deepClone(goodsColumns)
      if (result[7].title != '删除原因') {
        result[8] = result[7]
        result[7] = {title: '删除原因', dataIndex: 'delReason', width: 140,cell:(value, index, record)=>{
          return record.delReason && record.delReason.length > 0 ? record.delReason : '无'
        }}
       }
      return result
    }
  }
  
  const searchGoods = () => {
    onTabChange(selectedTab);
  };

  // 重置表单
  const resetGoodsField = () => {
    field.setValues({});
    field.reset();
  };

  // 获取搜索类数据
  const getCascaderContent = (item, newGroupOption, isScene) => {
    let result = "";
    if (item && item.filterFieldValue) {
      let values = JSON.parse(item.filterFieldValue);
      if (item.filterFieldComponentType == "picStandard") {
        values = dealQualityScoreToArray(JSON.parse(item.filterFieldValue));
      }
      if (values && values.length > 0) {
        result = values
          .map((v) => {
            let labelValue = v.label;
            if (item.filterFieldComponentType == "picStandard") {
              labelValue = getTreeName(
                newGroupOption[item.filterFieldId],
                v.value
              );
            }
            if (
              isScene &&
              (item.filterFieldId == "scene_city_id" ||
                item.filterFieldId == "store_city_id")
            ) {
              labelValue = getTreeName(newGroupOption[item.filterFieldId], v);
            }
            return labelValue;
          })
          .join(",");
      }
    }
    return result;
  };

  const getLabelItem = (item, newGroupOption, isScene) => {
    const filterFieldComponentType = item.filterFieldComponentType || "";
    let result;
    if (item.filterFieldValue || item.filterFieldValue == "") {
      switch (filterFieldComponentType) {
        case "arrayInput": //文本框
          result = <span>{JSON.parse(item.filterFieldValue).join(",")}</span>;
          break;
        case "radio": //单选框
        case "select": //单选下拉框
          result = (
            <span>
              {
                changeEumToObject(newGroupOption[item.filterFieldId])[
                  item.filterFieldValue
                ]
              }
            </span>
          );
          break;
        case "cascaderSelect": //下拉级联多选
        case "multipleSelect": //下拉多选
        case "picStandard": //质量分
        case "selectSearch": //带搜索的下拉多选
          result = (
            <span>{getCascaderContent(item, newGroupOption, isScene)}</span>
          );
          break;
        case "rangeNumberInput": //店铺评分类
          const valueRange = changeToYuan(item);
          if (percentage.includes(item.filterFieldId)) {
            const start = valueRange.start
              ? (Number(valueRange.start) * 10000).toFixed(2) / 100
              : "";
            const end = valueRange.end
              ? (Number(valueRange.end) * 10000).toFixed(2) / 100
              : "";
            result = (
              <span>
                {start ? start + "%" : ""}~{end ? end + "%" : ""}
              </span>
            );
          } else {
            result = (
              <span>
                {valueRange.start}~{valueRange.end}
              </span>
            );
          }
          break;
        case "checkbox": //复选框
          const value = item.filterFieldValue
            ? JSON.parse(item.filterFieldValue)
                .map(
                  (m) =>
                    changeEumToObject(newGroupOption[item.filterFieldId])[m]
                )
                .join(",")
            : "";
          result = <span>{value}</span>;
          break;
        default:
          result = <span>{item.filterFieldValue}</span>;
          break;
      }
    } else {
      result = <span></span>;
    }
    return result;
  };

  // 删除品类
  const goDeleteCategory = async (record) => {
    try {
      let sceneRule = [];
      let dataSource =
        selectedTab == "currentCategory"
          ? currentCategory.data
          : deleteCategory.data;
      selectedFakeKey.forEach((item) => {
        let deepCopyData = deepCopy(dataSource[item]);
        delete deepCopyData.fakeKey;
        sceneRule.push(deepCopyData);
      });
      let req = {
        bizId: sceneBaseId,
        addOrDelete: 1,
        tabType: selectedTab == "currentCategory" ? 0 : 2,
        sceneRuleDTOList: record ? [record] : sceneRule,
        sceneGroup,
      };
      let request = api.sceneManualUpdate;
      let resp = await request(req);
      if (resp.data.data.success) {
        onTabChange(selectedTab, 1, 10);
        Message.success(
          `${record ? "" : "批量"}${
            selectedTab != "currentCategory" ? "恢复删除成功" : "删除成功"
          }`
        );
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // TAB发生改变
  const onTabChange = async (nextSelectedTab, page, pageSize, isMore = false) => {
    switch (nextSelectedTab) {
      case "currentCategory":
        try {
          let req = {
            sourcePoolId: sourcePoolId,
            bizId: sceneBaseId,
            sceneGroup: sceneGroup,
            pageSize: pageSize ? pageSize : currentCategory.size,
            pageIndex: page ? page : currentCategory.page,
          };
          currentCategory.isLoading = true;
          setCurrentCategory(currentCategory);
          setSelectedFakeKey([]);
          let request = api.pageQuerySceneResultList;
          let resp = await request(req);
          if (resp.data.data.success) {
            let newCurrentCategory = deepCopy(currentCategory);
            let newData = resp.data.data.data.filter((v) => v.dataStatus == 1);
            newData = newData.map((item, index) => {
              item.fakeKey = index;
              return item;
            });
            newCurrentCategory.data = newData;
            newCurrentCategory.size = pageSize
              ? pageSize
              : currentCategory.size;
            newCurrentCategory.page = page ? page : currentCategory.page;
            newCurrentCategory.total = resp.data.data.totalCount;
            newCurrentCategory.isLoading = false;
            setCurrentCategory(newCurrentCategory);
          }
        } catch (error) {
          currentCategory.isLoading = false;
          setCurrentGoods(currentCategory);
          api.onRequestError(error);
        }
        break;
      case "deleteCategory":
        try {
          let req = {
            sourcePoolId: sourcePoolId,
            bizId: sceneBaseId,
            pageSize: pageSize ? pageSize : currentCategory.size,
            pageIndex: page ? page : currentCategory.page,
            sceneGroup,
            tabType: 2,
          };
          deleteCategory.isLoading = true;
          setDeleteCategory(deleteCategory);
          setSelectedFakeKey([]);
          let request = api.querySceneManual;
          let resp = await request(req);
          if (resp.data.data.success) {
            let newDeleteCategory = deepCopy(deleteCategory);
            let newData = resp.data.data.data.map((item, index) => {
              item.fakeKey = index;
              return item;
            });
            newDeleteCategory.data = newData;
            newDeleteCategory.size = pageSize ? pageSize : deleteCategory.size;
            newDeleteCategory.page = page ? page : deleteCategory.page;
            newDeleteCategory.total = resp.data.data.totalCount;
            newDeleteCategory.isLoading = false;
            setDeleteCategory(newDeleteCategory);
          }
        } catch (error) {
          deleteCategory.isLoading = false;
          setCurrentGoods(deleteCategory);
          api.onRequestError(error);
        }
        break;
      case "currentGoods":
        try {
          const { resultUpcIds, resultItemNames, resultItemIds, itemStatus } =
            field.getValues();
          let req = {
            sourcePoolId: sourcePoolId,
            sceneBaseId: sceneBaseId,
            operateType: 0,
            orderBy: "d7ValidOrderCnt",
            orderDirection: currentGoods.orderDirection,
            pageSize: pageSize ? pageSize : currentGoods.size,
            pageIndex: page ? page : currentGoods.page,
            resultUpcIds: resultUpcIds ? resultUpcIds.split(",") : [],
            resultItemNames: resultItemNames ? resultItemNames.split(",") : [],
            resultItemIds: resultItemIds ? resultItemIds.split(",") : [],
            itemStatus,
          };
          setSelectedKey([]);
          setIsMoreLoading(true);
          let request = api.querySceneItemPoolDetail;
          let resp = await request(req);
          if (resp.data.data.success) {
            let newCurrentGoods = deepCopy(currentGoods);
            if (isMore && page >= 2) {
              newCurrentGoods.data = newCurrentGoods.data
                .concat(resp.data.data.data)
                .filter((v) => v.status == 1);
            } else {
              newCurrentGoods.data = resp.data.data.data;
            }
            newCurrentGoods.size = pageSize ? pageSize : currentGoods.size;
            newCurrentGoods.page = page ? page : currentGoods.page;
            newCurrentGoods.total = resp.data.data.totalCount;
            newCurrentGoods.pageTotal = resp.data.data.totalPages;
            setIsMoreLoading(false);
            setCurrentGoods(newCurrentGoods);
          }
        } catch (error) {
          setIsMoreLoading(false);
          api.onRequestError(error);
        }
        break;
      case "deleteGoods":
        try {
          let req = {
            sourcePoolId: sourcePoolId,
            sceneBaseId: sceneBaseId,
            operateType: 2,
            pageSize: pageSize ? pageSize : deleteGoods.size,
            pageIndex: page ? page : deleteGoods.page,
          };
          setIsMoreLoading(true);
          setDeleteGoods(deleteGoods);
          let request = api.querySceneItemManualPoolDetail;
          let resp = await request(req);
          if (resp.data.data.success) {
            let newDeleteGoods = deepCopy(deleteGoods);
            if (isMore && page >= 2) {
              newDeleteGoods.data = newDeleteGoods.data
                .concat(resp.data.data.data)
                .filter((v) => v.status == 1);
            } else {
              newDeleteGoods.data = resp.data.data.data;
            }
            newDeleteGoods.size = pageSize ? pageSize : deleteGoods.size;
            newDeleteGoods.page = page ? page : deleteGoods.page;
            newDeleteGoods.total = resp.data.data.totalCount;
            newDeleteGoods.pageTotal = resp.data.data.totalPages;
            setIsMoreLoading(false);
            setDeleteGoods(newDeleteGoods);
          }
        } catch (error) {
          setIsMoreLoading(false);
          api.onRequestError(error);
        }
        break;
      case "operationLogs":
        break;
      default:
        break;
    }
    setSelectedTab(nextSelectedTab);
  };

  //品类操作按钮
  const renderNewOption = (record, nextSelectedTab) => {
    return (
      nextSelectedTab &&
      state != 0 && (
        <AccessBtn
          getPermission={() =>
            getExpertPermission({ bizType: "scene", bizId: sceneBaseId })
          }
          btnText={nextSelectedTab == "deleteCategory" ? "取消删除" : "删除"}
          key={nextSelectedTab + Math.random(100)}
          callback={() => {
            goDeleteCategory(record);
          }}
        />
      )
    );
  };

  // 商品操作按钮
  const renderOpt = (record) => {
    let delBtn = (
      <AccessBtn
        getPermission={() =>
          getExpertPermission({ bizType: "scene", bizId: sceneBaseId })
        }
        btnText={"删除"}
        callback={() => deleteGood(record.goodsId)}
      />
    );
    let cancelDelBtn = (
      <AccessBtn
        getPermission={() =>
          getExpertPermission({ bizType: "scene", bizId: sceneBaseId })
        }
        btnText={"取消删除"}
        callback={() => deleteGood(record.goodsId)}
      />
    );
    if (selectedTab === "currentGoods") {
      //tab为初选商品
      return (
        <div className="opbtns" key={"currentGoodsDelBtn"}>
          {record.status == 1 && delBtn}
        </div>
      );
    } else if (selectedTab === "deleteGoods") {
      //tab为删除商品
      return (
        <div className="opbtns" key={"currentGoodsCancelDelBtn"}>
          {cancelDelBtn}
        </div>
      );
    }
  };

  const deleteGood = async (goodsId, isBatch = false) => {
    if (selectedTab === "currentGoods") {
      Dialog.show({
        content: (
          <div className="deletion-reason">
            <Form field={deletionReason} className="deletion-reason-form">
              <FormItem label="商品ID：" {...formItemLayout}>
                <div className="deletion-reason-id">
                  {isBatch ? selectedKey.join(",") : goodsId}
                </div>
              </FormItem>
              <FormItem label="删除原因：" {...formItemLayout} required>
                <Checkbox.Group
                  className="deletion-reason-checkbox"
                  dataSource={delReasonList}
                  onChange={(event) => {
                    deletionReason.setValue("deleteGoodValue", event);
                  }}
                />
              </FormItem>
            </Form>
          </div>
        ),
        locale: {
          ok: "提交",
        },
        onOk: () => {
          return new Promise(async (resolve) => {
            let deleteGoodValue =
              deletionReason.getValue("deleteGoodValue") || "";
            if (deleteGoodValue && deleteGoodValue.length > 0) {
              try {
                let req = {
                  poolType: "goods",
                  addOrDelete: 1,
                  targetIdList: isBatch ? selectedKey : [goodsId],
                  sourcePoolId: sourcePoolId,
                  sceneBaseId: sceneBaseId,
                  operateType: 2,
                  dataFlag: true,
                  refreshMode: 1,
                  delReasonList: deleteGoodValue,
                };
                let request = api.querySceneManualUpdate;
                let resp = await request(req);
                if (resp.data.data.success) {
                  Message.success(`${isBatch ? "批量" : ""}删除成功`);
                  onTabChange(selectedTab);
                } else {
                  Message.warning(
                    resp.errMessage || `${isBatch ? "批量" : ""}删除失败`
                  );
                  resolve(false);
                }
              } catch (error) {
                api.onRequestError(error);
                resolve(false);
              }
              deletionReason.setValues({});
              return resolve(true);
            } else {
              Message.error("请选择至少一项删除原因");
              return resolve(false);
            }
          });
        },
      });
    } else if (selectedTab == "deleteGoods") {
      Dialog.confirm({
        title: "提示",
        content: `确定${isBatch ? "批量" : ""}取消删除么?`,
        onOk: async () => {
          try {
            let req = {
              poolType: "goods",
              addOrDelete: 1,
              targetIdList: isBatch ? selectedKey : [goodsId],
              sourcePoolId: sourcePoolId,
              sceneBaseId: sceneBaseId,
              operateType: 2,
              dataFlag: true,
              refreshMode: 1,
              isRemove: 1,
            };
            let request = api.querySceneManualUpdate;
            let resp = await request(req);
            if (resp.data.data.success) {
              Message.success(`${isBatch ? "批量" : ""}取消删除成功`);
              onTabChange(selectedTab);
            } else {
              Message.warning(
                resp.errMessage || `${isBatch ? "批量" : ""}取消删除失败`
              );
            }
          } catch (error) {
            api.onRequestError(error);
          }
        },
      });
    }
  };

  // 加载更多
  const clickMore = (type) => {
    if (type === 1) {
      onTabChange(
        "currentGoods",
        currentGoods.page + 1,
        currentGoods.pageSize,
        true
      );
    } else if (type === 2) {
      onTabChange(
        "deleteGoods",
        deleteGoods.page + 1,
        deleteGoods.pageSize,
        true
      );
    }
  };

  useEffect(() => {
    if (sceneBaseId && sourcePoolId) {
      if (customRuleType === 1) {
        setSelectedTab("currentCategory");
        onTabChange("currentCategory");
      } else {
        setSelectedTab("currentGoods");
        onTabChange("currentGoods");
      }
    }
  }, [sceneBaseId, sourcePoolId]);

  return (
    <>
      {sceneFilterFieldLists.length > 0 && (
        <div className="bottom-rules">
          <Row>
            <span className="title">底池规则</span>
          </Row>
          {sceneFilterFieldLists.map((item) => {
            return (
              <div className="rules">
                <Row>
                  <Col span="8" className="item">
                    <label>所在城市：</label>
                    {item.cityId &&
                      getLabelItem(item.cityId, groupOption, true)}
                  </Col>
                </Row>
                {item.rules.map((rulesItem) => {
                  return (
                    <Col span="8" className="item">
                      <label>{rulesItem.filterFieldLabel}</label>
                      {rulesItem.filterFieldDesc && (
                        <Balloon.Tooltip
                          align="t"
                          trigger={
                            <Icon
                              type="help"
                              style={{ color: "#CCCCCC" }}
                              size={"inherit"}
                            />
                          }
                        >
                          {rulesItem.filterFieldDesc}
                        </Balloon.Tooltip>
                      )}
                      ：{getLabelItem(rulesItem, groupOption)}
                    </Col>
                  );
                })}
              </div>
            );
          })}
        </div>
      )}
      {effectFilterFieldList.length > 0 && (
        <div className="pool-rules">
          <Row>
            <span className="title">圈选规则</span>
          </Row>
          {effectFilterFieldList.map((item) => {
            return (
              <Col span="8" className="item">
                <label>{item.filterFieldLabel}：</label>
                {getLabelItem(item, groupOption)}
              </Col>
            );
          })}
        </div>
      )}
      {optionalFilterFieldList.length > 0 && (
        <div className="pool-rules">
          <Row>
            <span className="title">运营可选规则</span>
          </Row>
          {optionalFilterFieldList.map((item) => {
            return (
              <Col span="4" className="item-table">
                <label>{item.filterFieldLabel}</label>
              </Col>
            );
          })}
        </div>
      )}
      <div className="sample-pool ">
        <Row>
          <span className="title">圈选结果</span>
        </Row>
        {["currentGoods"].includes(selectedTab) && (
          <Form className="filter" field={field}>
            <Row>
              <Col span={6}>
                <FormItem
                  validator={validators.upcIdCommaSeperated}
                  label="商品条形码"
                  {...formItemLayout}
                >
                  <Input name="resultUpcIds" placeholder="英文逗号隔开" />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="商品名称" {...formItemLayout}>
                  <Input name="resultItemNames" />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem
                  label="商品ID "
                  {...formItemLayout}
                  validator={validators.idCommaSeperated}
                >
                  <Input name="resultItemIds" placeholder="英文逗号隔开" />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="商品状态" {...formItemLayout}>
                  <Select
                    name="itemStatus"
                    dataSource={itemStatusEnum}
                    style={{ width: "100%" }}
                  />
                </FormItem>
              </Col>
            </Row>
          </Form>
        )}
        <div className="tab">
          <Tab
            shape="wrapped"
            onChange={(e) => {
              setSelectedTab(e);
              onTabChange(e);
            }}
            activeKey={selectedTab}
          >
            {customRuleType == 1 && (
              <Tab.Item key="currentCategory" title="当前品类">
                <Table
                  loading={currentCategory.isLoading}
                  dataSource={currentCategory.data}
                  hasBorder={false}
                  primaryKey="fakeKey"
                  rowSelection={{
                    onChange: (e) => {
                      setSelectedFakeKey([...e]);
                    },
                    selectedRowKeys: selectedFakeKey,
                    columnProps: () => {
                      return {
                        lock: "left",
                        width: 50,
                        align: "center",
                      };
                    },
                  }}
                >
                  {columns.map((e, idx) => {
                    return <Table.Column {...e} key={idx} />;
                  })}
                </Table>
                <Pagination
                  onChange={(e) => {
                    onTabChange(selectedTab, e, null);
                  }}
                  onPageSizeChange={(e) => {
                    let newCurrentCategory = deepCopy(currentCategory);
                    newCurrentCategory.size = e;
                    setCurrentCategory(newCurrentCategory);
                    onTabChange(selectedTab, null, e);
                  }}
                  total={currentCategory.total}
                  current={currentCategory.page}
                  pageSize={currentCategory.size}
                  pageSizeSelector="dropdown"
                  pageSizeList={[10, 20]}
                  popupProps={{ align: "bl tl" }}
                  style={{ float: "right", marginTop: "10px" }}
                />
              </Tab.Item>
            )}
            {customRuleType == 1 && (
              <Tab.Item key="deleteCategory" title="已删类目">
                <Table
                  loading={deleteCategory.isLoading}
                  dataSource={deleteCategory.data}
                  hasBorder={false}
                  primaryKey="fakeKey"
                  rowSelection={{
                    onChange: (e) => {
                      setSelectedFakeKey([...e]);
                    },
                    selectedRowKeys: selectedFakeKey,
                    columnProps: () => {
                      return {
                        lock: "left",
                        width: 50,
                        align: "center",
                      };
                    },
                  }}
                >
                  {columns.map((e, idx) => {
                    return <Table.Column {...e} key={idx} />;
                  })}
                </Table>
                <Pagination
                  onChange={(e) => {
                    onTabChange(selectedTab, e, 0);
                  }}
                  onPageSizeChange={(e) => {
                    let newDeleteCategory = deepCopy(deleteCategory);
                    newDeleteCategory.size = e;
                    setDeleteCategory(newDeleteCategory);
                    onTabChange(selectedTab, 0, e);
                  }}
                  total={deleteCategory.total}
                  current={deleteCategory.page}
                  pageSize={deleteCategory.size}
                  pageSizeSelector="dropdown"
                  pageSizeList={[10, 20]}
                  popupProps={{ align: "bl tl" }}
                  style={{ float: "right", marginTop: "10px" }}
                />
              </Tab.Item>
            )}
            <Tab.Item key="currentGoods" title="初选商品">
              <Table
                dataSource={currentGoods.data}
                hasBorder={false}
                primaryKey="goodsId"
                rowSelection={{
                  onChange: (e) => {
                    setSelectedKey([...e]);
                  },
                  selectedRowKeys: selectedKey,
                  columnProps: () => {
                    return {
                      lock: "left",
                      width: 50,
                      align: "center",
                    };
                  },
                }}
              >
                {createColumn('currentGoods').map((e, idx) => {
                  return <Table.Column {...e} key={idx} />;
                })}
              </Table>
            </Tab.Item>
            <Tab.Item key="deleteGoods" title="删除商品">
              <Table
                dataSource={deleteGoods.data}
                hasBorder={false}
                primaryKey="goodsId"
                rowSelection={{
                  onChange: (e) => {
                    setSelectedKey([...e]);
                  },
                  selectedRowKeys: selectedKey,
                  columnProps: () => {
                    return {
                      lock: "left",
                      width: 50,
                      align: "center",
                    };
                  },
                }}
              >
                {createColumn('deleteGoods').map((e, idx) => {
                  return <Table.Column {...e} key={idx} />;
                })}
              </Table>
            </Tab.Item>
            {false && ( // 本期暂时不做操作日志
              <Tab.Item key="operationLogs" title="操作日志">
                <Pagination
                  onChange={() => {}}
                  onPageSizeChange={(e) => {
                    let newOperationLogs = deepCopy(operationLogs);
                    newOperationLogs.size = e;
                    setCurrentCategory(newOperationLogs);
                  }}
                  total={operationLogs.total}
                  current={operationLogs.page}
                  pageSize={operationLogs.size}
                  pageSizeSelector="dropdown"
                  pageSizeList={[10, 20]}
                  popupProps={{ align: "bl tl" }}
                  style={{ float: "right", marginTop: "10px" }}
                />
              </Tab.Item>
            )}
          </Tab>
          {["currentCategory", "deleteCategory"].includes(selectedTab) &&
            state != 0 && (
              <div className="button-list">
                <AccessBtn
                  getPermission={() =>
                    getExpertPermission({
                      bizType: "scene",
                      bizId: sceneBaseId,
                    })
                  }
                  type="primary"
                  text={false}
                  btnText={
                    selectedTab == "currentCategory" ? "批量删除" : "批量恢复"
                  }
                  key={selectedTab}
                  callback={() => {
                    goDeleteCategory();
                  }}
                ></AccessBtn>
              </div>
            )}
          {["currentGoods", "deleteGoods"].includes(selectedTab) && (
            <div className="button-list">
              <div className="left">
                <div className="sum">
                  共找到
                  {selectedTab == "currentGoods"
                    ? currentGoods.total
                    : deleteGoods.total}
                  个结果
                </div>
                <div className="batch-panel">
                  <Icon type="prompt" style={{ color: "#FF7000" }} />
                  已选<span>{selectedKey.length}</span>项
                </div>
              </div>
              <div className="right">
                <AccessBtn
                  getPermission={() =>
                    getExpertPermission({
                      bizType: "scene",
                      bizId: sceneBaseId,
                    })
                  }
                  type="primary"
                  text={false}
                  btnText={
                    selectedTab == "currentGoods" ? "批量删除" : "批量恢复"
                  }
                  key={selectedTab}
                  callback={() => {
                    deleteGood([], true);
                  }}
                ></AccessBtn>
                {selectedTab == "currentGoods" && (
                  <>
                    <Button onClick={searchGoods}>查询</Button>
                    <Button onClick={resetGoodsField}>重置</Button>
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {selectedTab == "currentGoods" &&
          currentGoods.total > 0 &&
          currentGoods.pageTotal > currentGoods.page && (
            <Loading
              tip="加载中..."
              visible={isMoreLoading}
              className="btn-more"
              size="medium"
            >
              <div onClick={() => clickMore(1)}>加载更多</div>
            </Loading>
          )}
        {selectedTab == "deleteGoods" &&
          deleteGoods.total > 0 &&
          deleteGoods.pageTotal > deleteGoods.page && (
            <Loading
              tip="加载中..."
              visible={isMoreLoading}
              className="btn-more"
              size="medium"
            >
              <div onClick={() => clickMore(2)}>加载更多</div>
            </Loading>
          )}
      </div>
    </>
  );
}
