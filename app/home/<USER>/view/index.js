import React, {Fragment, useEffect, useState} from 'react';
import {Button, Dialog, Grid} from '@alife/next';
import {withRouter} from "react-router-dom";
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {permissionAccess} from "@/components/PermissionAccess";
import RankSet from "@/containers/channel/market/comps/rankSet";
import './style.scss';
import {BreadcrumbTips} from "@/home/<USER>/comps";
import SchemaForm from "@/utils/Form/src";
import RankDetail from "@/containers/channel/market/comps/rankDetail";
import SimpleTable from "@/components/SimpleTable";
import {baseQueryRankSchemaDTO, getChannelLabel, getChannelList, getCity, handleField} from "@/home/<USER>/common";
import {versionDetailGroup} from "@/containers/channel/market/common";
import {getNormalTemplateSchema} from "@/home/<USER>/setSchema/request";
import * as api from "@/utils/api";
import {viewRank} from "@/utils/api";
const {Row, Col} = Grid
/**
 * 榜单创建表单
 * @param props
 * @returns {JSX.Element}
 */

// const schema = {
//   "detail": {
//     "title": "基础配置",
//     "type": "object",
//     "required": [
//       "configName",
//       "beginTime",
//       "endTime",
//       "ext_mainBoardName",
//       "ext_mainBoardNameImg",
//       "ext_boardCategory",
//       "minDisplayCount",
//       "maxRecallCount",
//       "ext_shareDesc"
//     ],
//     "properties": {
//       "configId": {
//         "type": "string",
//         "title": "榜单ID:",
//         "disabled": true,
//         "x-ui-placeholder": "保存后自动生成"
//       },
//       "ext_mainBoardName": {
//         "type": "string",
//         "title": "榜单名称:",
//         "x-ui-placeholder": "请输入榜单名称，最多4个字",
//         "maximum": 4
//       },
//       "ext_mainBoardNameImg": {
//         "title": "榜单名称图片:",
//         "type": "string",
//         "format": "uri",
//         "x-ui-widget": "img-upload",
//         "x-ui-validate": {
//           "width": 570,
//           "height": 70,
//           "maxSize": 500,
//           "accept": "png,apng,jpg,jpeg,gif"
//         }
//       },
//       "beginTime": {
//         "type": "number",
//         "title": "开始时间:",
//         "x-ui-widget": "DateTimeWidget"
//       },
//       "endTime": {
//         "type": "number",
//         "title": "结束时间:",
//         "x-ui-widget": "DateTimeWidget",
//         "defaultTimeValue": "23:59:59"
//       },
//       "workDay": {
//         "type": "array",
//         "title": "星期:",
//         "x-ui-className": "selection-form",
//         "x-ui-widget": "dayOfWeek",
//         "items": {
//           "type": "number",
//           "enum": [
//             0,
//             1,
//             2,
//             3,
//             4,
//             5,
//             6,
//             7
//           ],
//           "enumNames": [
//             "全选",
//             "周一",
//             "周二",
//             "周三",
//             "周四",
//             "周五",
//             "周六",
//             "周日"
//           ]
//         },
//         "uniqueItems": true,
//         "default": [
//           1,
//           2,
//           3,
//           4,
//           5,
//           6,
//           7
//         ]
//       },
//       "timeSelection": {
//         "type": "string",
//         "title": "时段:",
//         "enum": [
//           "0",
//           "1"
//         ],
//         "x-ui-className": "selection-form",
//         "x-ui-valueLabel": {
//           "0": "全天",
//           "1": "选择"
//         },
//         "default": "0",
//         "x-ui-widget": "radio"
//       },
//       "timeRanges": {
//         "type": "array",
//         "title": " ",
//         "x-ui-hidden": "$.timeSelection == 0",
//         "maxItems": 3,
//         "items": {
//           "type": "string",
//           "format": "time-range",
//           "default": "-"
//         }
//       },
//       "ext_boardCategory": {
//         "type": "string",
//         "title": "榜单分类:",
//         "enum": [
//           "1",
//           "2"
//         ],
//         "x-ui-className": "selection-form",
//         "x-ui-valueLabel": {
//           "1": "标品榜单",
//           "2": "非标品榜单"
//         },
//         "x-ui-widget": "radio"
//       },
//       "minDisplayCount": {
//         "title": "榜单最少展示商品数量：",
//         "type": "number",
//         "x-ui-placeholder": "榜单最少展示商品数量",
//         "maximum": 999999,
//         "hasLimitHint": true
//       },
//       "maxRecallCount": {
//         "title": "榜单最大召回商品数量：",
//         "type": "number",
//         "x-ui-placeholder": "榜单最大召回商品数量",
//         "maximum": 999999,
//         "default": 10,
//         "hasLimitHint": true
//       },
//       "ext_shareDesc": {
//         "type": "string",
//         "title": "外投分享文案:",
//         "x-ui-placeholder": "请输入外投分享文案，最多20个字",
//         "maximum": 20
//       },
//       "ext_rankInfoDisplayType": {
//         "title": "榜单展示类型：",
//         "type": "string",
//         "x-ui-className": "selection-form",
//         "enum": [
//           "1",
//           "2"
//         ],
//         "x-ui-valueLabel": {
//           "1": "人气榜",
//           "2": "品牌榜"
//         },
//         "x-ui-widget": "select",
//         "default": "1"
//       }
//     }
//   },
//   "rankSchema": [
//     {
//       "name": "人气榜",
//       "field": "rankInfo",
//       "type": 1,
//       "schema": {
//         "oversupply": {
//           "maxTabLength": 10,
//           "maxCommodityLength": 2,
//           "nameSetting": "tab设置（1-10）:",
//           "name": "tab名称",
//           "field": "tabName",
//           "maxFieldLength": 4,
//           "isAllowShow": true,
//           "isRequired": false,
//           "isNotRequired": false,
//           "initTabLength": 1,
//           "hideTabName": false
//         },
//         "showItemCat": {
//           "name": "指定叶子类目置顶",
//           "field": "topItemCatList"
//         },
//         "showMultilineIdText": [
//           {
//             "key": "topClusterIdListStr",
//             "name": "指定商品置顶",
//             "placeholder": "指定商品置顶",
//             "required": false,
//             "maxLength": 20
//           }
//         ],
//         "detail": {
//           "title": "商品召回规则：",
//           "required": [
//             "configName"
//           ],
//           "field": "recallRule",
//           "type": "object",
//           "properties": {
//             "openStatusSet": {
//               "type": "array",
//               "title": "时段:",
//               "x-ui-className": "selection-form",
//               "x-ui-widget": "CheckboxesWidget",
//               "items": {
//                 "type": "string",
//                 "enum": [
//                   "1",
//                   "5",
//                   "4",
//                   "0"
//                 ],
//                 "enumNames": [
//                   "休息中",
//                   "预定中",
//                   "即将休息",
//                   "营业中"
//                 ]
//               },
//               "uniqueItems": true,
//               "default": []
//             },
//             "shopScatter": {
//               "type": "string",
//               "title": "是否按照店铺打散:",
//               "enum": [
//                 "1",
//                 "0"
//               ],
//               "x-ui-className": "selection-form",
//               "x-ui-valueLabel": {
//                 "0": "否",
//                 "1": "是"
//               },
//               "default": "0",
//               "x-ui-widget": "radio"
//             },
//             "maxDeliveryPrice": {
//               "title": "最大配送费：",
//               "type": "number",
//               "maximum": 999999,
//               "hasLimitHint": true
//             }
//           }
//         }
//       }
//     },
//     {
//       "name": "品牌榜",
//       "field": "rankInfo",
//       "type": 2,
//       "schema": {
//         "oversupply": {
//           "maxTabLength": 10,
//           "maxCommodityLength": 2,
//           "nameSetting": "tab设置（1-10）:",
//           "name": "tab名称",
//           "field": "tabName",
//           "maxFieldLength": 4,
//           "isAllowShow": true,
//           "isRequired": false,
//           "isNotRequired": false,
//           "hideTabName": false,
//           "initTabLength": 1
//         },
//         "showMultilineIdText": [
//           {
//             "key": "topItemBrandIdListStr",
//             "name": "指定品牌置顶",
//             "placeholder": "指定品牌置顶",
//             "required": false,
//             "maxLength": 20
//           }
//         ],
//         "detail": {
//           "title": "商品召回规则：",
//           "required": [
//             "configName"
//           ],
//           "type": "object",
//           "properties": {
//             "openStatusSet": {
//               "type": "array",
//               "title": "时段:",
//               "x-ui-className": "selection-form",
//               "x-ui-widget": "CheckboxesWidget",
//               "items": {
//                 "type": "string",
//                 "enum": [
//                   "1",
//                   "5",
//                   "4",
//                   "0"
//                 ],
//                 "enumNames": [
//                   "休息中",
//                   "预定中",
//                   "即将休息",
//                   "营业中"
//                 ]
//               },
//               "uniqueItems": true,
//               "default": []
//             },
//             "shopScatter": {
//               "type": "string",
//               "title": "是否按照店铺打散:",
//               "enum": [
//                 "1",
//                 "0"
//               ],
//               "x-ui-className": "selection-form",
//               "x-ui-valueLabel": {
//                 "0": "否",
//                 "1": "是"
//               },
//               "default": "0",
//               "x-ui-widget": "radio"
//             },
//             "maxDeliveryPrice": {
//               "title": "最大配送费：",
//               "type": "number",
//               "maximum": 999999,
//               "hasLimitHint": true
//             }
//           }
//         }
//       }
//     }
//   ]
// }

function rankView(props) {
  const breadcrumbList = [
    {"title": '榜单管理', link: "#/rankManage/list"},
    {"title": '查看榜单', link: ""}
  ];
  const rankId = props.match.params.id || '';
  console.log('rankView',rankId);
  const [scheduleTemplate, setScheduleTemplate] = useState({});
  const [fieldKeys, setFieldKeys] = useState({});
  const [scheduleInfo, setScheduleInfo] = useState({});
  // let {rankSchema, detail: detailSchema} = schema;
  // const fieldKeys = scheduleTemplate.detail.properties;

/*  const scheduleInfo = {
    "resourceId": 200001,
    "createUserId": 2305110,
    "isPreview": false,
    "rankInfo": [
      {
        "pureDataSourceList": [
          {
            "itemCat": [
              {
                "level": 2,
                "label": "西瓜类",
                "value": "201228120"
              },
              {
                "level": 2,
                "label": "甜瓜蜜瓜类",
                "value": "201231515"
              },
              {
                "level": 2,
                "label": "木瓜类",
                "value": "201227526"
              }
            ],
            "tabName": "甜蜜瓜果",
            "shopList": [],
            "supplyType": 2,
            "categoryData": [],
            "tabIndex": 1,
            "poolIds": []
          },
          {
            "itemCat": [
              {
                "level": 3,
                "label": "西柚/葡萄柚",
                "value": "201219732"
              },
              {
                "level": 2,
                "label": "葡萄类",
                "value": "201218624"
              },
              {
                "level": 2,
                "label": "提子类",
                "value": "201222829"
              },
              {
                "level": 3,
                "label": "欧洲杏/西梅",
                "value": "201221047"
              },
              {
                "level": 2,
                "label": "梅子类",
                "value": "201220732"
              },
              {
                "level": 2,
                "label": "杨梅类",
                "value": "201219419"
              },
              {
                "level": 2,
                "label": "苹果类",
                "value": "201223433"
              }
            ],
            "tabName": "葡提梅果",
            "shopList": [],
            "supplyType": 2,
            "categoryData": [],
            "tabIndex": 2,
            "poolIds": []
          },
          {
            "itemCat": [
              {
                "level": 2,
                "label": "桃子类",
                "value": "201220634"
              },
              {
                "level": 2,
                "label": "枣子类",
                "value": "201222029"
              },
              {
                "level": 2,
                "label": "梨类",
                "value": "201223129"
              },
              {
                "level": 2,
                "label": "车厘子/樱桃类",
                "value": "201218221"
              }
            ],
            "tabName": "核果",
            "shopList": [],
            "supplyType": 2,
            "categoryData": [],
            "tabIndex": 3,
            "poolIds": []
          },
          {
            "itemCat": [
              {
                "level": 2,
                "label": "芒果类",
                "value": "201230123"
              },
              {
                "level": 2,
                "label": "榴莲类",
                "value": "201219034"
              },
              {
                "level": 2,
                "label": "木瓜类",
                "value": "201227526"
              },
              {
                "level": 2,
                "label": "菠萝蜜类",
                "value": "201230325"
              }
            ],
            "tabName": "热带水果",
            "shopList": [],
            "supplyType": 2,
            "categoryData": [],
            "tabIndex": 4,
            "poolIds": []
          }
        ],
        "recallRule": {
          "maxDeliveryPrice": 20,
          "shopMajorCategoryContent": [],
          "openStatusSet": [
            "5",
            "4",
            "0",
            "1"
          ],
          "shopScatter": "0"
        },
        "index": 1,
        "type": 1
      },
      {
        "pureDataSourceList": [],
        "recallRule": {
          "shopMajorCategoryContent": [],
          "openStatusSet": [],
          "shopScatter": "0"
        },
        "index": 2,
        "type": 2
      }
    ],
    "timeRanges": "",
    "userTagGroupIdsList": [
      "9723002001"
    ],
    "deliveryPosition": [
      {
        "name": "全能商厦首页推荐",
        "location": "20",
        "type": "1",
        "scheduleId": ""
      }
    ],
    "ext_rankInfoDisplayType": "1",
    "channelList": [
      "*"
    ],
    "maxRecallCount": 15,
    "yn": 0,
    "locationId": 1345,
    "configId": "96976",
    "id": 96976,
    "beginTime": 1694707200000,
    "ext_mainBoardNameImg": "https://img.alicdn.com/imgextra/i2/2305110/O1CN0191FNWP1ncQm6nI8Mq_!!2305110-2-newretailxdt.png",
    "configName": "水果-商品类目",
    "userOrigin": "",
    "ext_boardCategory": "2",
    "updateUser": "王善玲",
    "minDisplayCount": 4,
    "updateTime": 1694761527000,
    "pageId": 200000,
    "workDay": [
      1,
      2,
      3,
      4,
      5,
      6,
      7
    ],
    "priority": 9999,
    "channelVersionList": {
      "versionGroup": {
        "relationType": "and",
        "versionList": [
          {
            "operateType": "",
            "value": ""
          }
        ],
        "groupList": []
      },
      "allVersion": true,
      "channel": "*"
    },
    "createTime": 1694761239000,
    "ext_shareDesc": "甜瓜蜜果，确定不来尝一尝？",
    "createUser": "王善玲",
    "endTime": 1694879999000,
    "ext_mainBoardName": "水果",
    "timeSelection": "0",
    "status": 4
  }*/

  useEffect(() => {
    getNormalSchema();
    getRank();
  }, [])

  /**
   * 非资源位的获取元数据schema——常规链路
   * */
  const getNormalSchema = () => {
    getNormalTemplateSchema(baseQueryRankSchemaDTO)
      .then((result) => {
        let scheduleTemplate = JSON.parse(result);
        setScheduleTemplate(scheduleTemplate);
        setFieldKeys(scheduleTemplate.detail.properties);
        // setRankSchema(scheduleTemplate.rankSchema);
      })
      .catch(api.onRequestError);
  }


  const getRank = () =>{
    if(rankId) {
      viewRank({boardId: rankId})
        .then((result) => {
          // setFormData(result);
          setScheduleInfo(result);
          // sessionStorage.setItem('rankInfo',JSON.stringify(result.rankInfo));
        })
        .catch(api.onRequestError);
    }
  }

  return (
    <div className={'container'}>
      <BreadcrumbTips list={breadcrumbList}/>
      <div className="resource-body">
        <Row>
          <Col offset="3" span="18">
            <SimpleTable
              title={'基础信息'}
            >
              {Object.keys(fieldKeys).map((o) => {
                if (o != 'timeRanges' && o != 'activityIds' && o != 'configId') {
                  return <SimpleTable.Item label={fieldKeys[o].title}>
                    {handleField(o, scheduleInfo, fieldKeys[o])}
                  </SimpleTable.Item>
                }
              })}
              {Boolean(scheduleInfo && scheduleTemplate.rankSchema) && <RankDetail rankInfo={scheduleInfo.rankInfo} deliveryPosition={scheduleInfo.deliveryPosition} rankSchema={scheduleTemplate.rankSchema} />}
            </SimpleTable>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(rankView), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const rankViewPage = permissionAccess(LogTimePutInPage)

