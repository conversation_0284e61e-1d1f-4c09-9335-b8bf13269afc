import React, { useEffect, useState } from "react";
import { withRouter } from "react-router-dom";
import { Form, Select, Radio } from "@alife/next";

import { goldLog, logTimeComponent } from "@/utils/aplus";
import { permissionAccess } from "@/components/PermissionAccess";
import { BreadcrumbTips } from "@/home/<USER>/comps";
import { onRequestError } from "@/utils/api";
import * as api from "@/utils/api/supermarketChannelBigStore";
import {
  CategorySelect, PoolIdsSelect, FormItem, ChannelSelect,
  VersionSelect, UserSelect, AOICascaderSelect, CityCascaderSelect
} from '../components'
import { XUAN_PIN_JI, SHANG_PIN_JU_LEI, supplyTypeList, sceneTypeList, weekDaysEnum } from "../utils";
import moment from 'moment'

import "./style.scss";

const breadcrumbList = [
  {
    title: "场景管理",
    link: "#/channelManage/supermarketChannelBigStore/sceneManage",
  },
  { title: "查看场景", link: "" },
];

function BigStoreSceneView({ match }) {
  const sceneId = match.params.id || "";
  const [data, setData] = useState({});

  useEffect(() => {
    if (sceneId) {
      api
        .getMall4Scene({
          sceneId,
        })
        .then((res) => {
          // 基础信息回填
          const defaultValue = {
            sceneId: res.sceneId,
            sceneName: res.sceneName,
            sceneDisplayName: res.sceneDisplayName,
            weight: res.extMap.weight,
          };
          // 供给池信息回填
          if (res.mall4ItemRule) {
            defaultValue.mainPoolIds = res.mall4ItemRule.mainPoolIds;
            defaultValue.mainType = res.mall4ItemRule.mainType;
            defaultValue.subPoolIds = res.mall4ItemRule.subPoolIds;
            defaultValue.subType = res.mall4ItemRule.subType;
            if (res.mall4ItemRule.mainCat3Id) {
              defaultValue.mainCat3Id = res.mall4ItemRule.mainCat3Id;
            }
            if (res.mall4ItemRule.subCat3Id) {
              defaultValue.subCat3Id = res.mall4ItemRule.subCat3Id;
            }
            defaultValue.sceneType = res.sceneType;
            if (res.mall4ItemRule.startTime) {
              defaultValue.startTime = moment(res.mall4ItemRule.startTime).format("YYYY-MM-DD HH:mm:ss")
            }
            if (res.mall4ItemRule.endTime) {
              defaultValue.endTime = moment(res.mall4ItemRule.endTime).format("YYYY-MM-DD HH:mm:ss")
            }
            defaultValue.workDay = res.mall4ItemRule.workDay
            defaultValue.timeSelection = res.mall4ItemRule.timeSelection
            defaultValue.timeRanges = res.mall4ItemRule.timeRanges
            defaultValue.channelList = res.mall4ItemRule.channelList
            if (res.mall4ItemRule.channelVersionList) {
              defaultValue.allVersion = res.mall4ItemRule.channelVersionList.allVersion
              defaultValue.versionList = res.mall4ItemRule.channelVersionList.versionGroup.versionList
            }
            defaultValue.citys = res.mall4ItemRule.citys;
            defaultValue.userTagGroupIdsList = res.mall4ItemRule.userTagGroupIdsList;
            defaultValue.extAoiCodes = res.mall4ItemRule.extAoiCodes;
          }
          setData(defaultValue);
        })
        .catch((e) => {
          onRequestError(e);
        });
    }
  }, []);

  const showVersionSelect = (data.channelList || []).findIndex(item => {
    return ["*", "android", "ios"].includes(item)
  }) > -1
  const workDayText = Array.isArray(data.workDay) ? data.workDay.map(dayItem => {
    const result = weekDaysEnum.find(item => item.value === dayItem)
    return result ? result.label : ''
  }).join(' , ') : []

  return (
    <div className="big-store-view-page">
      <BreadcrumbTips list={breadcrumbList} />
      <h1 />
      <Form className="bsvp-form">
        <FormItem label="场景id">
          <div className="bsvp-content">{data.sceneId}</div>
        </FormItem>
        <FormItem
          label="场景名称"
          required={true}
          requiredMessage="场景名称不能为空"
        >
          <div className="bsvp-content">{data.sceneName}</div>
        </FormItem>
        <FormItem
          label="前台名称"
          required={true}
          requiredMessage="前台名称不能为空"
        >
          <div className="bsvp-content">{data.sceneDisplayName}</div>
        </FormItem>

        <FormItem
          label={`主-供给来源`}
          required
          requiredMessage="主-供给来源不能为空"
        >
          <Select
            name="mainType"
            style={{ width: "100%" }}
            dataSource={supplyTypeList}
            value={data.mainType || XUAN_PIN_JI}
            disabled
          />
        </FormItem>

        {data.mainType === XUAN_PIN_JI ? (
          <FormItem label={`主 - 选品集ID`} required hasFeedback>
            <PoolIdsSelect
              name="mainPoolIds"
              value={data.mainPoolIds}
              disabled
            />
          </FormItem>
        ) : null}

        {data.mainType === SHANG_PIN_JU_LEI ? (
          <FormItem label={`主 - 商品类目`} required hasFeedback>
            <CategorySelect value={data.mainCat3Id} disabled />
          </FormItem>
        ) : null}

        <FormItem
          label={`副-供给来源`}
          required
          requiredMessage="副-供给来源不能为空"
        >
          <Select
            disabled
            style={{ width: "100%" }}
            dataSource={supplyTypeList}
            value={data.subType || XUAN_PIN_JI}
          />
        </FormItem>
        {data.subType === XUAN_PIN_JI ? (
          <FormItem label={`副 - 选品集ID`} required hasFeedback>
            <PoolIdsSelect disabled value={data.subPoolIds} />
          </FormItem>
        ) : null}

        {data.subType === SHANG_PIN_JU_LEI ? (
          <FormItem label={`副 - 商品类目`} required hasFeedback>
            <CategorySelect disabled value={data.subCat3Id} />
          </FormItem>
        ) : null}
        <FormItem label="权重" required={true} requiredMessage="权重不能为空">
          <div className="bsvp-content">{data.weight}</div>
        </FormItem>

        <FormItem label="场景类型">
          <Radio.Group name="sceneType" dataSource={sceneTypeList} value={data.sceneType} isPreview={true} />
        </FormItem>
        <FormItem label="开始时间:">
          <div className="bsvp-content">{data.startTime}</div>
        </FormItem>
        <FormItem label="结束时间:">
          <div className="bsvp-content">{data.endTime}</div>
        </FormItem>
        <FormItem label="星期">
          <div className="bsvp-content">{workDayText}</div>
        </FormItem>
        <FormItem label="时段:">
          <div className="bsvp-content">{data.timeSelection === '0' ? '全天' : '选择'}</div>
        </FormItem>
        {data.timeSelection === '1' && Array.isArray(data.timeRanges) ? (
          <FormItem label=" ">
            <div className="bsvp-content">
              {Array.isArray(data.timeRanges) ? data.timeRanges.map(item => (
                <div>{item}</div>
              )) : null}
            </div>
          </FormItem>
        ) : null}
        <FormItem label="投放渠道:">
          <ChannelSelect name="channelList" multiple value={data.channelList} isPreview />
        </FormItem>
        {showVersionSelect ? (
          <FormItem label="投放版本号(仅针对app):">
            <div className="bsvp-content">{data.allVersion ? '默认全版本' : '不默认全版本'}</div>
          </FormItem>
        ) : null}
        {showVersionSelect && !data.allVersion ? (
          <FormItem label=" ">
            <VersionSelect name="versionList" value={data.versionList} isPreview />
          </FormItem>
        ) : null}

        <FormItem label="投放城市:">
          <CityCascaderSelect
            name="citys"
            multiple
            value={data.citys}
            isPreview={true}
          />
        </FormItem>
        <FormItem label="投放人群：">
          <UserSelect name="userTagGroupIdsList" value={data.userTagGroupIdsList} isPreview />
        </FormItem>
        <FormItem label="投放AOI类型:">
          <AOICascaderSelect name="extAoiCodes" multiple isPreview value={data.extAoiCodes} />
        </FormItem>
      </Form>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(BigStoreSceneView),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const BigStoreSceneViewPage = permissionAccess(LogTimePutInPage);
