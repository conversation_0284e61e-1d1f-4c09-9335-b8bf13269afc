import React, { useEffect, useState } from "react";
import { Field } from "@alife/next";
import cms from "classnames";
import { withRouter } from "react-router-dom";

import { BreadcrumbTips } from "@/home/<USER>/comps";
import { onRequestError } from "@/utils/api";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { permissionAccess } from "@/components/PermissionAccess";

import { viewRank } from "../api";
import {
  BaseInfoForm,
  SecondTitle,
  RecallRuleForm,
  TabSettingItem,
} from "../components/index";
import { GeneralUtilsProvider } from "../hooks/generalUtils";
import {
  REN_QI_BANG,
  PIN_PAI_BANG,
  FENG_SHENG_BANG,
  formatBaseInfoRes2Field,
  formatBangDanRes2Field,
  formatRecallRuleRes2Field,
  getValidBangDanCount,
} from "../utils";

const breadcrumbList = [
  { title: "榜单管理", link: "#/rankManage/list" },
  { title: "新建榜单", link: "" },
];

function MainTitle({ title }) {
  return <h1>{title}</h1>;
}
function ThirdTitle({ title }) {
  return <h3>{title}</h3>;
}

const rankView = (props) => {
  const rankId = props.match.params.id || "";
  const baseInfoField = Field.useField();

  // fields 疯省榜、人气榜、品牌榜
  const fengShengRecallRuleField = Field.useField({});
  const renQiBangRecallRuleField = Field.useField({});
  const pinPaiRecallRuleField = Field.useField({});

  // 展示数据 疯省榜、人气榜、品牌榜
  const [fengShengBangList, setFengShengBangList] = useState([]);
  const [renQiBangList, setRenQiBangList] = useState([]);
  const [pinPaiBangList, setPinPaiBangList] = useState([]);

  // 展开/收起控制 疯省榜、人气榜、品牌榜
  const [fengShengBangVisible, setFengShengBangVisible] = useState(false);
  const [pinPaiBangVisible, setPinPaiBangVisible] = useState(false);
  const [renQiBangVisible, setRenQiBangVisible] = useState(false);

  useEffect(() => {
    // 编辑、复制场景下，获取数据并进行回显
    if (rankId) {
      viewRank({ boardId: rankId })
        .then((result) => {
          // 处理基础数据回填
          const defaultBaseInfoValue = formatBaseInfoRes2Field(result);
          baseInfoField.setValues(defaultBaseInfoValue);
          // 处理榜单数据回填
          if (result.rankInfo && result.rankInfo.length > 0) {
            result.rankInfo.map((rankInfoItem) => {
              // 格式化 tab(1-10) 数据
              const _defaultValue = rankInfoItem.pureDataSourceList.map(
                (item) => {
                  return formatBangDanRes2Field(item);
                }
              );
              // 格式化召回规则数据
              const defaultRecallRuleValue = formatRecallRuleRes2Field(
                rankInfoItem.recallRule
              );

              if (rankInfoItem.type === FENG_SHENG_BANG) {
                setFengShengBangList(_defaultValue);
                fengShengRecallRuleField.setValues(defaultRecallRuleValue);
              }
              if (rankInfoItem.type === REN_QI_BANG) {
                setRenQiBangList(_defaultValue);
                renQiBangRecallRuleField.setValues(defaultRecallRuleValue);
              }
              if (rankInfoItem.type === PIN_PAI_BANG) {
                setPinPaiBangList(_defaultValue);
                pinPaiRecallRuleField.setValues(defaultRecallRuleValue);
              }
            });
          }
        })
        .catch((e) => {
          console.error("e=======viewRank", e);
          onRequestError(e);
        });
    }
  }, []);

  return (
    <div className={"container"}>
      <BreadcrumbTips list={breadcrumbList} />

      <div className="body rankv2-create">
        <MainTitle title="基础配置" />
        <BaseInfoForm field={baseInfoField} disabled={true} />

        <GeneralUtilsProvider>
          <MainTitle title="榜单配置" />
          {/* 疯省榜 */}
          <SecondTitle
            title={`疯省榜（${getValidBangDanCount(fengShengBangList)}/10）`}
            isOpen={fengShengBangVisible}
            showOpenBtn
            onOpenBtnClick={() =>
              setFengShengBangVisible(!fengShengBangVisible)
            }
          />
          <div
            className={cms({
              hidden: !fengShengBangVisible,
            })}
          >
            <ThirdTitle title="tab设置（1-10）" />
            <div className="tab_setting_list">
              {fengShengBangList.map((item, i) => {
                if (!item) {
                  return null;
                }
                return (
                  <TabSettingItem
                    key={item.key}
                    currentNum={i + 1}
                    disabled={true}
                    showFixedPureDataSource
                    showDownBtn={false}
                    showUpBtn={false}
                    showRemoveBtn={false}
                    value={item}
                    onSaveField={(field) => {
                      const _fengShengBangList = [...fengShengBangList];
                      _fengShengBangList[i].field = field;
                      setFengShengBangList(_fengShengBangList);
                    }}
                  />
                );
              })}
            </div>

            <ThirdTitle title="商品召回规则" />
            <RecallRuleForm
              disabled={true}
              field={fengShengRecallRuleField}
              showTopSortRule
            />
          </div>

          {/* 人气榜 */}
          <SecondTitle
            title={`人气榜（${+getValidBangDanCount(renQiBangList)}/10）`}
            isOpen={renQiBangVisible}
            showOpenBtn
            onOpenBtnClick={() => setRenQiBangVisible(!renQiBangVisible)}
          />
          <div
            className={cms({
              hidden: !renQiBangVisible,
            })}
          >
            <ThirdTitle title="tab设置（1-10）" />
            <div className="tab_setting_list">
              {renQiBangList.map((item, i) => {
                if (!item) {
                  return null;
                }
                return (
                  <TabSettingItem
                    key={item.key}
                    currentNum={i + 1}
                    disabled={true}
                    showFixedPureDataSource
                    showDownBtn={false}
                    showUpBtn={false}
                    showRemoveBtn={false}
                    value={item}
                    onSaveField={(field) => {
                      const _renQiBangList = [...renQiBangList];
                      _renQiBangList[i].field = field;
                      setRenQiBangList(_renQiBangList);
                    }}
                  />
                );
              })}
            </div>

            <ThirdTitle title="商品召回规则" />
            <RecallRuleForm
              disabled={true}
              field={renQiBangRecallRuleField}
              showTopSortRule
            />
          </div>

          {/* 品牌榜 */}
          {baseInfoField.getValue("boardCategory") !== 2 ? (
            <>
              <SecondTitle
                title={`品牌榜（${getValidBangDanCount(pinPaiBangList)}/10）`}
                isOpen={pinPaiBangVisible}
                showOpenBtn
                onOpenBtnClick={() => setPinPaiBangVisible(!pinPaiBangVisible)}
              />
              <div
                className={cms({
                  hidden: !pinPaiBangVisible,
                })}
              >
                <ThirdTitle title="tab设置（1-10）" />
                <div className="tab_setting_list">
                  {pinPaiBangList.map((item, i) => {
                    if (!item) {
                      return null;
                    }
                    return (
                      <TabSettingItem
                        key={item.key}
                        currentNum={i + 1}
                        disabled={true}
                        showDownBtn={false}
                        showRemoveBtn={false}
                        showTopBrandIdList
                        showUpBtn={false}
                        value={item}
                        onSaveField={(field) => {
                          const _pinPaiBangList = [...pinPaiBangList];
                          _pinPaiBangList[i].field = field;
                          setPinPaiBangList(_pinPaiBangList);
                        }}
                      />
                    );
                  })}
                </div>

                <ThirdTitle title="商品召回规则" />
                <RecallRuleForm disabled={true} field={pinPaiRecallRuleField} />
              </div>
            </>
          ) : null}
        </GeneralUtilsProvider>
      </div>
    </div>
  );
};

export const LogTimePutInPage = logTimeComponent(
  withRouter(rankView),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const rankViewPage = permissionAccess(LogTimePutInPage);
