import React, {
  Fragment,
  useEffect,
  useState,
} from "react";
import {
  Form,
  Checkbox,
  Radio,
  Select,
  Input,
  Button,
  Field,
  Grid,
  Dialog,
  Message,
} from "@alife/next";
import { ACLAccess } from "@/components/ACLAccess";

import "./style.scss";
import { TimeRangePicker } from "@/components/TimeRangePicker";
import moment from "moment";
import {
  refreshNewModeEum,
  refreshRealModeEum,
  refreshBYRealModeEum,
} from "@/home/<USER>/common";
import * as api from "@/adator/api";
import { promisify } from "@/utils/others";
import { queryUserByKeyWord } from "@/containers/channel/market/request";
import { onRequestError } from "@/utils/api";

const { Row, Col } = Grid;
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 2 },
  wrapperCol: {
    span: 18,
  },
  style: {
    width: "100%",
  },
  labelAlign: "left",
  labelTextAlign: "right",
};

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb("请输入正确的时间段");
  else {
    if (start.isBefore(moment().startOf("day")))
      return cb("开始时间不能小于当前时间");
  }
  cb();
};

export default function BaseInfo(props) {
  const defaultTime = [
    moment().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }),
    moment()
      .add(1, "months")
      .set({ hour: 23, minute: 59, second: 59, millisecond: 0 }),
  ];
  const [curMonth, setCurMonth] = useState(1);
  const { sourcePoolId, isEdit, baseInfo = {} } = props;
  const [activePoolPlatform, setActivePoolPlatform] = useState(0);
  const [outSynPlatformsDs, setOutSynPlatformsDs] = useState([]);
  const [outSynPlatforms, setOutSynPlatforms] = useState([[]]);
  const [isNotice, setIsNotice] = useState(0);
  const [noticeUid, setNoticeUid] = useState("");
  const [roleDataSource, setRoleDataSource] = useState([]);
  const [currentChildIndex, setCurrentChildIndex] = useState("");
  const [imageItem, setImageItem] = useState({});
  const [maxMonth, setMaxMonth] = useState(4);
  const dataSource = parseInt(props.sourcePoolId) === 33002
    ? parseInt(props.sourcePoolId) != 41001
      ? refreshRealModeEum
      : refreshBYRealModeEum
    : refreshNewModeEum

  const defaultRefreshMode = dataSource[0].value;


  const field = Field.useField({
    onChange: (name, value) => {
      props.updateBaseInfo(name, value);
    },
  });

  const getDefaultOutSynPlatforms = (sourcePoolId) =>{
    let result;
    if(sourcePoolId=='41001'){
      result = ["billion_item"];
    }else if(sourcePoolId=='33003'){
      result = ['market'];
    }else{
      result = ["xt"];
    }
    return result;
  }

  useEffect(() => {
    if (!props.isEdit && !props.isCopy) {
      props.updateBaseInfo(
        "outSynPlatforms",
        getDefaultOutSynPlatforms(props.sourcePoolId)
      );
      props.updateBaseInfo("effectRange", defaultTime);
      props.updateBaseInfo("refreshMode", defaultRefreshMode);
    }
    initField(baseInfo);
    fetchOutSynPlatforms();
  }, []);

  const fetchOutSynPlatforms = () => {
    try {
      api.newSynPoolPlatformList(sourcePoolId).then((res)=>{
        const data = res.data.data.data;
        if (data.length > 0) {
          const code = baseInfo.outSynPlatforms && baseInfo.outSynPlatforms[0] ? baseInfo.outSynPlatforms[0] : '';
          const newOutSynPlatforms = [];
          let _activePoolPlatform = 0;
          data.forEach((v,k) => {
            newOutSynPlatforms[k] = [];
            if(k === 0) {
              newOutSynPlatforms[k].push('xt'); // 必选项
            }
            if (v.children.length) {
              v.children.forEach((i)=>{
                if (i.platformCode == code) {
                  _activePoolPlatform = k;
                  newOutSynPlatforms[k] = baseInfo.outSynPlatforms;
                }
              })
            }
          });
          setActivePoolPlatform(_activePoolPlatform);
          setOutSynPlatforms(newOutSynPlatforms);
          
          setOutSynPlatformsDs(data);
        }
      });
      // api.synPoolPlatformList(sourcePoolId).then((resp) => {
      //   // console.log('%c [ resp ]-119', 'font-size:13px; background:pink; color:#bf2c9f;', resp)
      //   if (resp.data.data.data.length > 0) {
      //     setOutSynPlatformsDs(resp.data.data.data);
      //   }
      // });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const initField = (baseInfo) => {
    if (baseInfo) {
      field.setValues({
        poolName: baseInfo.poolName,
        outSynPlatforms: baseInfo.outSynPlatforms,
        effectRange: baseInfo.effectRange,
        isNotice: baseInfo.isNotice,
        noticeUid: baseInfo.noticeUid,
        refreshMode: baseInfo.refreshMode,
      });
      // 解决在复制已下线池子时返回outSynPlatforms为空的场景零售选投不展示的问题
      if(props.isCopy && baseInfo.outSynPlatforms.length === 0){
        props.updateBaseInfo("outSynPlatforms", (props.sourcePoolId != '41001') ? ["xt"] : ["billion_item"]);
      }
      // setOutSynPlatforms([baseInfo.outSynPlatforms]);
      if (props.isEdit || props.isCopy) {
        initNoticeUid(baseInfo.noticeUid);
      } else {
        setIsNotice(baseInfo.isNotice);
        setNoticeUid(baseInfo.noticeUid);
      }
    }
  };

  const initNoticeUid = (noticeUid) => {
    let noticeUidGroup = noticeUid.split(",");
    if (noticeUidGroup.length > 0) {
      noticeUidGroup.map((v) => {
        ctrlRoleDataSource(v);
      });
    }
  };

  const ctrlRoleDataSource = (id) => {
    try {
      queryUserByKeyWord(id).then((resp) => {
        roleDataSource.push({
          value: resp[0].empId,
          label: `${resp[0].lastName}_${resp[0].empId}`,
          record: resp[0],
        });
        setRoleDataSource(roleDataSource);
        if (roleDataSource.length == baseInfo.noticeUid.split(",").length) {
          setIsNotice(baseInfo.isNotice);
          setNoticeUid(baseInfo.noticeUid);
        }
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const checkNotice = (value) => {
    let newValue = value ? 1 : 0;
    setIsNotice(newValue);
    props.updateBaseInfo("isNotice", newValue);
    field.setValue("isNotice", newValue);
  };

  const changeNoticeId = (value) => {
    props.updateBaseInfo("noticeUid", value);
    setNoticeUid(value);
  };

  const setDate = (month) => {
    let time = [
      moment().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }),
      moment()
        .add(month, "months")
        .add(-1, "days")
        .set({ hour: 23, minute: 59, second: 59, millisecond: 0 }),
    ];
    // let time = [moment(), moment().add(month, 'months')];
    // if (month >= 4) {
    //   time = [
    //     moment().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }),
    //     moment()
    //       .add(month, "months")
    //       .add(-1, "days")
    //       .set({ hour: 23, minute: 59, second: 59, millisecond: 0 }),
    //   ];
    // }
    setCurMonth(month);
    props.updateBaseInfo("effectRange", time);
    field.setValue("effectRange", time);
    // this.setState({
    //   curMonth: month
    // }, () => {
    //   this.props.updateBaseInfo("effectRange", time);
    //   this.field.setValue("effectRange", time)
    // })
  };

  let searchTimeout;
  const onSearch = (keyword) => {
    //通过关键字查用户
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      if (keyword) {
        var reg = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]+$");
        if (keyword != "" && !reg.test(keyword)) {
          Message.error("不能输入特殊字符");
          return;
        }
        queryUserByKeyWord(keyword)
          .then((data) => {
            const dataSource = data.map((v) => ({
              value: v.empId,
              label: `${v.lastName}_${v.empId}`,
              record: v,
            }));
            setRoleDataSource(dataSource);
          })
          .catch(onRequestError);
      } else {
        setRoleDataSource([]);
      }
    }, 800);
  };

  const addAcl = (value, actionType, item) => {
    if (value.length > 3) {
      Message.warning("备选人最多加三个");
      return;
    } else {
      let newValue = value.join(",");
      setNoticeUid(newValue);
      props.updateBaseInfo("noticeUid", newValue);
    }
  };

  const ctrlOutSynPlatforms = (checked, item) => {
    if (item.platformCode != "xt") {
      let scenePermissionType = "ACTION";
      if (item.platformCode == "dcdb") scenePermissionType = "ACTION_DCDB";
      judgeAccess(checked, scenePermissionType, item, function () {
        let newOutSynPlatforms = JSON.parse(JSON.stringify(outSynPlatforms));
        if (outSynPlatformsDs[activePoolPlatform].selectModel == 'radio') {
          if (checked) {
            newOutSynPlatforms[activePoolPlatform] = [item.platformCode];
          } else {
            newOutSynPlatforms[activePoolPlatform] = [];
          }
        } else {
          if (checked) {
            newOutSynPlatforms[activePoolPlatform].push(item.platformCode);
          } else {
            const index = outSynPlatforms[activePoolPlatform].findIndex(
              (o) => o == item.platformCode
            );
            if (index > -1) {
              newOutSynPlatforms[activePoolPlatform] = [
                ...outSynPlatforms[activePoolPlatform].slice(0, index),
                ...outSynPlatforms[activePoolPlatform].slice(index + 1),
              ];
            }
          }
        }
        saveOutSynPlatforms(newOutSynPlatforms);
      });
    }
  };

  const judgeAccess = async (checked, permissionType, item, callback) => {
    let value = item.platformCode;
    let resp = await api
      .validateComponentsPermission({
        permissionType: permissionType,
        permissionValue: value,
      })
      .then(api.onRequestSuccess);
    let { rediectUrl } = resp;
    if (rediectUrl) {
      Dialog.confirm({
        title: "申请权限",
        footer: false,
        content: [<ACLAccess rediectUrl={rediectUrl} />],
      });
    } else {
      callback(value);
    }
  };
  
  const saveOutSynPlatforms = (data) => {
    setOutSynPlatforms(data);
    props.updateBaseInfo("outSynPlatforms", data[activePoolPlatform]);
    field.setValue("outSynPlatforms", data[activePoolPlatform]);
  }

  const onChangeOutSynPlatformsDsType = (index) => {
    setActivePoolPlatform(index);
    props.updateBaseInfo("outSynPlatforms", outSynPlatforms[index]);
    field.setValue("outSynPlatforms", outSynPlatforms[index]);
  }



  const savePool = async () => {
    await promisify(field.validate)();
    if (field.values.outSynPlatforms && field.values.outSynPlatforms.length) {
      props.savePool();
    }

  };

  const submitPool = async () => {
    await promisify(field.validate)();
    if (field.values.outSynPlatforms && field.values.outSynPlatforms.length) {
      props.submitPool();
    }
  };

  /**
   * 适用场景父节点点击
   **/
  const parentPlatformsChecked = (index, v) => {
    /*选饿了么主站*/
    if (v.platformCode == "ele") {
      let item = outSynPlatformsDs[activePoolPlatform].children[index].children || {};
      // outSynPlatforms.push(item[0].platformCode);
      // setOutSynPlatforms(Array.from(new Set(outSynPlatforms)));
      setImageItem(item[0]);
      setCurrentChildIndex(index);
    } else {
      ctrlOutSynPlatforms(!outSynPlatforms[activePoolPlatform].includes(v.platformCode), v);
    }
  };

  useEffect(()=>{
    if (outSynPlatforms[activePoolPlatform] && outSynPlatforms[activePoolPlatform].length && outSynPlatformsDs.length) {
      const items = {};
      let _maxMonth = 4;
      outSynPlatformsDs.forEach((v,k) => {
        if (v.children.length) {
          v.children.forEach((i)=>{
            items[i.platformCode] = i.maxMonth;
          })
        }
      });
      outSynPlatforms[activePoolPlatform].forEach((v)=>{
        if (items[v] && _maxMonth < items[v]) {
          _maxMonth = items[v];
        }
      })
      setMaxMonth((oldValue)=> {
        if (oldValue > _maxMonth) {
          setDate(_maxMonth);
        }
        return _maxMonth;
      });
    }
  },[activePoolPlatform,outSynPlatforms,outSynPlatformsDs])
  const dateGroup = [
    { label: "2个月", value: "2" },
    { label: "3个月", value: "3" },
    { label: "4个月", value: "4" },
    { label: "12个月", value: "12" },
  ];

  return (
    <Form style={{ margin: "10px" }} field={field} className="base-info">
      <FormItem
        label="数据集名称"
        required
        asterisk={false}
        requiredMessage="数据集名称必填"
        {...formItemLayout}
      >
        <Input
          style={{ width: "408px" }}
          placeholder="请输入数据集名称,不超过20个字"
          name="poolName"
          maxLength={20}
        />
      </FormItem>
      {Boolean(isNotice == 1) && (
        <FormItem>
          <Row>
            <Col span={2}></Col>
            <Col span={15}>
              <div className="message-receipt">
                <Select
                  aria-label="tag mode"
                  placeholder={"请输入员工工号"}
                  mode="tag"
                  value={noticeUid ? noticeUid.split(",") : []}
                  filterLocal={false}
                  hasArrow={false}
                  onChange={(value, actionType, item) =>
                    addAcl(value, actionType, item)
                  }
                  onSearch={(value) => onSearch(value)}
                  dataSource={roleDataSource}
                  style={{ width: 400 }}
                />
              </div>
            </Col>
          </Row>
        </FormItem>
      )}

      <FormItem
        label="使用场景"
        requiredMessage="使用场景必填"
        name="outSynPlatforms"
        asterisk={false}
        required
        {...formItemLayout}
      >
        {outSynPlatformsDs && outSynPlatformsDs.length > 1 ? (
          <Radio.Group
            shape="button"
            defaultValue={activePoolPlatform}
            onChange={(v)=>onChangeOutSynPlatformsDsType(v)}
            dataSource={outSynPlatformsDs.map((v, k) => {
              return {
                value: k,
                label: v.label,
              };
            })}
          />
        ) : null}

        <div className="radio-list">
          {outSynPlatformsDs &&
            outSynPlatformsDs.length &&
            outSynPlatformsDs[activePoolPlatform].children &&
            outSynPlatformsDs[activePoolPlatform].children.map((v, i) => {
              let childPlatformGroup = [];
              let intersectionGroup = [];
              if (v.children && v.children.length > 0) {
                childPlatformGroup = v.children.map((j) => j.platformCode);
                intersectionGroup = outSynPlatforms[activePoolPlatform].filter(function (val) {
                  return childPlatformGroup.indexOf(val) > -1;
                });
              }
              return (
                <div
                  key={i}
                  className={`radio-item ${
                    outSynPlatforms[activePoolPlatform].includes(v.platformCode) ||
                    intersectionGroup.length > 0
                      ? "active"
                      : ""
                  }  ${currentChildIndex === i ? "current" : ""}`}
                  onClick={() => parentPlatformsChecked(i, v)}
                >
                  <span className="label">{v.label}</span>
                  <span className="desc">{v.desc}</span>
                  {v.children && v.children.length > 0 ? (
                    <>
                      {intersectionGroup.length > 0 && (
                        <span className="length">
                          {intersectionGroup.length}
                        </span>
                      )}
                    </>
                  ) : (
                    <Checkbox
                      checked={outSynPlatforms[activePoolPlatform].includes(v.platformCode)}
                    />
                  )}
                </div>
              );
            })}
        </div>

        {outSynPlatformsDs &&
          outSynPlatformsDs.length > 0 &&
          outSynPlatformsDs[activePoolPlatform].children &&
          outSynPlatformsDs[activePoolPlatform].children.length > 0 &&
          outSynPlatformsDs[activePoolPlatform].children[currentChildIndex] &&
          outSynPlatformsDs[activePoolPlatform].children[currentChildIndex]
            .children.length > 0 && (
          <Row className="radio-child">
            <Col span={8}>
              {outSynPlatformsDs[activePoolPlatform].children[currentChildIndex].children.map((o,k) => {
                return (
                  <div
                    key={k}
                    className={`child ${
                      o.platformCode == imageItem.platformCode ? "active" : ""
                    }`}
                  >
                    <Checkbox
                      checked={outSynPlatforms[activePoolPlatform].includes(o.platformCode)}
                      onChange={(checked) => ctrlOutSynPlatforms(checked, o)}
                    />
                    <div
                      className="text-panel"
                      onClick={() => setImageItem(o)}
                    >
                      <label>{o.label}</label>
                      <span>{o.desc}</span>
                    </div>
                  </div>
                );
              })}
            </Col>
            <Col
              span={16}
              style={{ textAlign: "center", paddingTop: "20px" }}
            >
              {imageItem &&
                imageItem.url &&
                imageItem.url.length > 0 &&
                imageItem.url.map((i,k) => {
                  return (
                    <img
                      key={k}
                      src={i}
                      style={{
                        marginLeft: "8px",
                        height: "350px",
                        width: "170px",
                      }}
                    />
                  );
                })}
            </Col>
          </Row>
        )}

        {!field.values.outSynPlatforms || field.values.outSynPlatforms.length === 0 ? <div className="next-form-item-help" style={{color:'#FF2D4B'}}>请选择使用场景</div> :null}
      </FormItem>

      <FormItem
        label="有效期"
        required
        asterisk={false}
        {...formItemLayout}
        requiredMessage="有效期必填"
        validator={timeRangeValidator}
        validatorTrigger={["onChange"]}
      >
        <TimeRangePicker
          defaultValue={defaultTime}
          style={{ width: "258px", marginRight: "8px" }}
          name="effectRange"
          disabledDate={(date) => date.isBefore(moment().startOf("day"))}
        ></TimeRangePicker>

        <Button.Group size="size">
          {dateGroup.map((v, k) => {
            if (v.value <= maxMonth) {
              return (
                <Button
                  key={k}
                  size="small"
                  className={`date_${v.value} ${
                    curMonth == v.value ? "cur" : ""
                  }`}
                  onClick={() => setDate(v.value)}
                >
                  {v.label}
                </Button>
              );
            }
          })}
        </Button.Group>
      </FormItem>
      <FormItem>
        <Row>
          <Col span={2}></Col>
          <Col span={15}>
            <div className="time-limit">
              <span>到期前默认提醒创建人</span>
              <label name="isNotice">
                <Checkbox
                  checked={isNotice == 1 ? true : ""}
                  onChange={checkNotice}
                />
                设置提醒备选人
              </label>
            </div>
          </Col>
        </Row>
      </FormItem>

      <FormItem
        label="更新机制"
        required
        asterisk={false}
        requiredMessage="更新机制必填"
        {...formItemLayout}
      >
        <Radio.Group
          disabled={isEdit}
          defaultValue={dataSource[0].value}
          dataSource={dataSource}
          name="refreshMode"
        />
      </FormItem>

      <div className="btn-panel">
        <Button onClick={() => props.goStep(1)}>返回上一步</Button>
        <Button type={"primary"} onClick={() => savePool()}>
          保存信息
        </Button>
        <Button type={"primary"} onClick={() => submitPool()}>
          立即发布
        </Button>
      </div>
    </Form>
  );
}
