import React, { useEffect, useState } from "react";
import { <PERSON>ton, Dialog, Grid } from "@alife/next";
import "./style.scss";
import * as api from "@/utils/api";
import * as requestPool from "@/adator/api";
import { storeEum } from "@/home/<USER>/common";
import SelectMethod from "@/home/<USER>/poolList/selectMethod";
const { Row, Col } = Grid;

const DEFAULT_Label_IMG = require("../../../images/icon-label.png");
const DEFAULT_Upload_IMG = require("../../../images/icon-upload.png");

export default function Create(props) {
  const methods = [
    {
      key: "selectedStore",
      title: "标签选店",
      tips: "根据特征规则选品，圈选数量",
      num: "最大100万",
      link: "/pool/list/tagPool",
      iconClass: "icon-label",
      src: DEFAULT_Label_IMG,
    },
    {
      key: "uploadPool",
      title: "文件上传",
      tips: "通过文件上传批量导入商品，数量",
      num: "上限10万",
      link: "/pool/list/fileUploadPage",
      iconClass: "icon-upload",
      src: DEFAULT_Upload_IMG,
    },
  ];

  const currentWay = 0;
  const wayItem = methods[currentWay];
  const [sourcePoolId, setSourcePoolId] = useState(""); //选中的底池的sourcePoolId
  const [sourcePoolName, setSourcePoolName] = useState(""); //选中的底池的sourcePoolName
  const [poolEumList, setPoolEumList] = useState([]) //选品选店集列表
  let { type, createVisible } = props;

  /*修改圈选方式二次弹窗*/
  const confirmChange = (callback) => {
    if (sourcePoolId) {
      Dialog.show({
        title: "修改圈选方式",
        content: "修改圈选方式，已设置的规则内容不保存。",
        okProps: { children: "确定" },
        cancelProps: { children: "取消" },
        footerActions: ["cancel", "ok"],
        onOk: () => callback(),
        onCancel: () => {
          return;
        },
      });
    } else {
      callback();
    }
  };

  const changeType = (_type) => {
    confirmChange(function () {
      props.setType(_type);
    });
  };

  /*获取池子列表*/
  const getBasePoolList = async () => {
    try {
      let request = requestPool.getBasePoolList;
      let resp = await request();
      if(resp.data.data.data.length > 0){
        setPoolEumList(resp.data.data.data)
      }
    } catch (error) {
      api.onRequestError(error)
    }
  }

  const initCreate = () => {
    let { _sourcePoolId } = props;
    if (_sourcePoolId) {
      setSourcePoolId(_sourcePoolId);
      if (poolEumList.length > 0) {
        let item = poolEumList.filter((v) => v.basePoolId === _sourcePoolId)[0]
        setSourcePoolName(item.basePoolName);
        props.setType(item.poolEntryType);
      }else{
        let item = storeEum.filter((v) => v.id === _sourcePoolId)[0];
        setSourcePoolName(item.title);
        props.setType(item.type);
      }
    }
  };

  useEffect(()=>{
    // 请求选店集数据
    getBasePoolList()
  },[])

  useEffect(() => {
    initCreate();
  }, [props.wayItem, props.createVisible, props.sourcePoolId]);

  const setPool = (item) => {
    confirmChange(() => {
      setSourcePoolId(item.id);
      setSourcePoolName(item.title);
      props.getSourcePoolId(item.id);
      props.setCreateVisible(false);
    });
  };

  const setTitleVisible = () => {
    if (props.isEdit || props.isCopy) return;
    props.setCreateVisible(true);
  };

  return (
    <div className="pool-create">
      {createVisible && (
        <>
          <h3 onClick={() => props.setCreateVisible(false)}>
            1. 选择圈选方式{" "}
            {sourcePoolId && (
              <Button text className="arrow-fold">
                收起
              </Button>
            )}
          </h3>
          <SelectMethod history={props.history} current={currentWay} />
          <div className="section-first">
            <div className="select-way">
              {wayItem && <h3 className="header">选品方式</h3>}
              {wayItem && (
                <Row>
                  <Col
                    span={7}
                    className={`${+type === 1 ? "active" : ""}`}
                    onClick={() => changeType(1)}
                  >
                    <h3>普通选店</h3>
                    <p>
                      标签按天更新，选品集更新时间为
                      <span className="count">T+1</span>
                    </p>
                    <p>
                      圈选数量<span className="count">最大20万</span>
                    </p>
                  </Col>
                  <Col
                    span={7}
                    className={`${+type === 2 ? "active" : ""}`}
                    onClick={() => changeType(2)}
                  >
                    <h3>实时选店</h3>
                    <p>
                      标签和选品集<span className="count">实时更新</span>
                    </p>
                    <p>
                      圈选数量<span className="count">最大20万</span>
                    </p>
                  </Col>
                </Row>
              )}
              {type && <h3 className="header">选择底池</h3>}
              {type && <Row wrap>
                {poolEumList.map((item)=>{
                  if (+type === item.poolEntryType && +item.basePoolType === 2) {
                    return <Col span={5} onClick={() => setPool({id:item.basePoolId,title:item.basePoolName})} className={`${ (+item.basePoolId === +sourcePoolId) ? 'active' : ''}`}>
                      <h3>{item.basePoolName}</h3>
                      <p>{item.basePoolDesc}</p>
                    </Col>
                  }
                })}
              </Row>}
            </div>
          </div>
        </>
      )}
      {!createVisible && (
        <h3 onClick={() => setTitleVisible()}>
          1. 已选：{wayItem.title} / {+props.type === 1 ? "普通选店" : "实时选店"}{" "}
          / {sourcePoolName}{" "}
          {!props.isEdit && !props.isCopy && (
            <Button text className="arrow-fold">
              展开
            </Button>
          )}
        </h3>
      )}
    </div>
  );
}
