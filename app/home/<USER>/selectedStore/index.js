import React, { Fragment, useEffect, useState } from "react";
import { <PERSON>ton, Dialog, Grid, Message } from "@alife/next";
import "./style.scss";
import { BreadcrumbTips, CompletePage } from "@/home/<USER>/comps";
import { PageWrapper } from "@/comps/PageWrapper";
import {
  dealQualityScoreToObject,
  storeEum,
  PoolPageBase,
  Steps,
  unique, yuanToFen,
} from "@/home/<USER>/common";
import Create from "./create";
import TagRules from "./tagRules";
import BaseInfo from "./baseInfo";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { dealQualityScoreToArray,fenToYuan,changeToYuan } from "@/home/<USER>/common";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import * as api from "@/adator/api";
import moment from "moment";
import { promisify } from "@/utils/others";
import { optionMap } from "@/home/<USER>/common/map";
import { isArray, isString } from "@/utils/validators";
import ViewDialog from "@/home/<USER>/selectedStore/viewDialog";
import FinishPage from "@/home/<USER>/selectedStore/finishPage";
import { deepCopy, fetchGroupOption } from "@/home/<USER>/common";

const { Row, Col } = Grid;

function selectedStore({ location, match, history }) {
  const breadcrumbList = [
    { title: "选品集管理", link: "#/pool/list" },
    { title: "新建门店选品集", link: "" },
  ];
  let { poolId = "" } = match.params;
  const isEdit = poolId > 0;
  const isCopy = poolId < 0;
  poolId = poolId < 0 ? -poolId : poolId;
  const [step, setStep] = useState(0);
  const [sourcePoolId, setSourcePoolId] = useState(""); //选中的底池的sourcePoolId
  const [detailData, setDetailData] = useState({});
  const [effectRules, setEffectRules] = useState([]); //编辑标签表单的effectRules
  const [requestEffectRules, setRequestEffectRules] = useState([]); //在请求接口中流通的requestEffectRules
  const [params, setParams] = useState({});
  const [baseInfo, setBaseInfo] = useState({});
  const [tempPoolId, setTempPoolId] = useState("");
  const [submitPoolId, setSubmitPoolId] = useState("");
  const [submitDisabled, setSubmitDisabled] = useState(false);
  const [isShowSecond, setIsShowSecond] = useState(false);
  const [isShowNext, setIsShowNext] = useState(false);
  const [wayItem, setWayItem] = useState("");
  const [createVisible, setCreateVisible] = useState(true);
  const [type, setType] = useState(""); //普通选店：1  ， 实时选店：2
  const [groupOptions, setGroupOptions] = useState({ ...optionMap });
  const [viewDiagVisible, setViewDiagVisible] = useState(false);
  const [user, setUser] = useState({});
  const [sourceFilterFieldList,setSourceFilterFieldList] = useState([]);//当前池子筛选项

  const getSourcePoolId = (nextSourcePoolId) => {
    setSourcePoolId(nextSourcePoolId);
  };

  const getWayItem = (nextWayItem) => {
    setWayItem(nextWayItem);
  };

  // 根据poolId查询详情
  const getPoolDetail = (empId) => {
    try {
      api.getPoolDetailByPoolId(poolId).then((resp) => {
        let { noticeUid } = resp.data;
        let newNoticeUid = noticeUid;
        if (noticeUid.indexOf(empId) >= 0) {
          const index = noticeUid.split(",").findIndex((o) => o == empId);
          const noticeUidGroup = noticeUid.split(",");
          noticeUidGroup.splice(index, 1);
          newNoticeUid = noticeUidGroup.join(",");
        }
        baseInfo.poolName = resp.data.poolName;
        baseInfo.effectAt = resp.data.effectAt;
        baseInfo.expireAt = resp.data.expireAt;
        baseInfo.isNotice = resp.data.isNotice;
        baseInfo.noticeUid = newNoticeUid;
        baseInfo.effectRange = [
          moment(resp.data.effectAt),
          moment(resp.data.expireAt),
        ];
        baseInfo.outSynPlatforms = resp.data.outSynPlatforms;
        baseInfo.refreshMode = resp.data.refreshMode;
        params.refreshMode = resp.data.refreshMode;
        params.poolName = resp.data.poolName;
        params.outSynPlatforms = resp.data.outSynPlatforms;
        params.isNotice = resp.data.isNotice;
        params.noticeUid = newNoticeUid;
        params.effectRange = [
          moment(baseInfo.effectAt),
          moment(baseInfo.expireAt),
        ];
        setSourcePoolId(resp.data.sourcePoolId);
        setSubmitPoolId(resp.data.poolId);
        setDetailData(resp.data);
        // setEffectRules(resp.data.effectRules);
        setRequestEffectRules(resp.data.effectRules);
        changeEffectRules(resp.data.effectRules);
        setBaseInfo(baseInfo);
        setParams(params);
        setWayItem({ key: "selectedStore", title: "标签选店" });
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*将requestEffectRules转化为effectRules*/
  const changeEffectRules = (nextEffectRules) => {
    let newEffectRules = [];
    if (nextEffectRules && nextEffectRules.length > 0) {
      nextEffectRules.map((v) => {
        let value = v.filterFieldValue;
        // if(isArray(value) && value.length == 0){
        //   return;
        // }
        // if (value || value == '') {
        if (v.filterFieldComponentType == "arrayInput") {
          value = JSON.parse(value).join(",");
        }
        if (v.filterFieldComponentType == "picStandard") {
          value = dealQualityScoreToArray(JSON.parse(value)).map(
            (_v) => _v.value
          );
        }
        let typeGroup = ["checkbox"];
        if (typeGroup.includes(v.filterFieldComponentType)) {
          // value = JSON.parse(value).map(v => v.value);
          value = JSON.parse(value);
        }
        if(v.filterFieldComponentType == 'rangeNumberInput'){
          value = changeToYuan(v);
        }
        if (
          v.filterFieldComponentType == "cascaderSelect" ||
          v.filterFieldComponentType == "multipleSelect"
        ) {
          value = JSON.parse(value).map((_v) => _v.value);
        }
        // let value = v.filterFieldValue ? ((v.type != 'arrayInput') ? v.filterFieldValue : JSON.parse(v.filterFieldValue)) : '';
        newEffectRules.push({
          filterFieldId: v.filterFieldId,
          filterFieldKey: v.filterFieldKey,
          filterFieldLabel: v.filterFieldLabel,
          filterFieldValue: value,
          filterFieldComponentType: v.filterFieldComponentType,
          filterFieldIdGroup: v.filterFieldIdGroup,
          operator: v.operator,
        });
        // }
      });
    }

    setEffectRules(newEffectRules);
  };

  /*获取临时池子id*/
  const getTempId = () => {
    try {
      api.getTempPoolId().then((resp) => {
        setTempPoolId(resp.data);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  useEffect(() => {
    getUser();
    if (isEdit || isCopy) {
      setCreateVisible(false);
    }
    getTempId();
  }, []);

  useEffect(()=>{
    (async ()=>{
      await getRulesBySource()
    })();
  },[sourcePoolId])

  // 批量请求dataType为2的筛选项并加入到groupOptions中
  useEffect(() => {
    if(sourceFilterFieldList.length > 0){
      let requestList = {};
      let newList = [...sourceFilterFieldList]
      newList.map((item) => {
        if (item.filterFieldDataType == 1) {
          groupOptions[item.filterFieldId] = JSON.parse(item.filterFieldData);
        } else if (item.filterFieldDataType == 2) {
          requestList[item.filterFieldId] = item.filterFieldData;
        } else {
          groupOptions[item.filterFieldId] = optionMap[item.filterFieldKey];
        }
      });
    // 批量请求底池规则和圈选规则中为动态数据源的数据
    (async () => {
      let promiseList = [];
      Object.keys(requestList).forEach(async (key) => {
        promiseList.push(fetchGroupOption(key, requestList[key]));
      });
      const resolveList = await Promise.all(promiseList);
      resolveList.map((item) => {
        Object.keys(item).forEach((keyItem) => {
          groupOptions[keyItem] = item[keyItem] || [];
        });
      });
      setGroupOptions(deepCopy(groupOptions));
    })();
    }
  }, [sourceFilterFieldList]);

  /*获取当前池子筛选项*/
  const getRulesBySource = async () =>{
    try {
      if (sourcePoolId) {
        let resp = await api.getRulesBySource(sourcePoolId);
        setSourceFilterFieldList(resp.data)
      }
      // 圈选池子相关指标
    } catch (error) {
      api.onRequestError(error);
    }
  }
  
  /*步骤的切换，1：上一步，2：下一步*/
  const goStep = (_type) => {
    setStep(_type == 1 ? step - 1 : 1 + step);
  };

  const getEffectRules = (nextEffectRules, nextRequestEffectRules) => {
    setEffectRules(nextEffectRules.filter((v) => !!v.filterFieldValue));
    setRequestEffectRules(
      nextRequestEffectRules.filter((v) => !!v.filterFieldValue)
    );
  };

  /*更新基本信息*/
  const updateBaseInfo = (name, value) => {
    baseInfo[name] = value;
    params[name] = value;
    setParams(params);
    setBaseInfo(baseInfo);
  };

  const getUser = () => {
    try {
      api.getBucUser().then((resp) => {
        setUser(resp.data.data);
        if (isEdit || isCopy) {
          getPoolDetail(resp.data.data.empId);
        }
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const ctrlReq = () => {
    let _type = storeEum.filter((v) => v.id == sourcePoolId)[0].type;
    let poolResultLimit = _type == 1 ? "1000000" : "100000";
    let { noticeUid = "" } = params;
    let noticeUidGroup = noticeUid ? noticeUid.split(",") : [];
    if (user.empId) {
      noticeUidGroup.push(user.empId);
    }
    noticeUid = unique(noticeUidGroup).join(",");

    return {
      sourcePoolId,
      poolType: 2,
      poolName: params.poolName,
      effectAt: params.effectRange[0].valueOf(),
      expireAt: params.effectRange[1].valueOf(),
      isNotice: params.isNotice || 0,
      noticeUid,
      // effectRules: this.ctrlNewEffectRules(effectRules),
      effectRules: requestEffectRules,
      poolStoreType: "",
      tempPoolId,
      poolResultLimit,
      refreshMode: params.refreshMode,
      // refreshMode: 1,
      outSynPlatforms: params.outSynPlatforms,
    };
  };

  const savePool = async () => {
    let createPoolV2Req = ctrlReq();
    let request = api.createPool;
    if (isEdit && !isCopy) {
      createPoolV2Req.poolId = submitPoolId;
      request = api.updatePool;
    }
    let resp = await request(createPoolV2Req);
    if (resp.data.code!='200') {
      Message.warning(resp.data.msg);
    } else {
      Message.success('操作成功');
      history.push("/pool/list");
    }

  };

  const submitPool = async () => {
    // let {params,effectRules} = this.state;
    // await (promisify(this.baseInfoRef.current.field.validate)());
    // this.setState({
    //   submitDisabled:true
    // })
    // try {
      let createPoolV2Req = ctrlReq();
      let request = api.createPool;
      if (isEdit && !isCopy) {
        createPoolV2Req.poolId = submitPoolId;
        request = api.updatePool;
      }
      let resp = await request(createPoolV2Req);
      let id = resp.data.data && resp.data.data.data;
    if (resp.data.code=='200' ) {
      if (id) {
        let result = await api.publishPool(id);
        setSubmitPoolId(id);
        goStep(2);
      }
    }else{
      setSubmitDisabled(false);
      Message.warning(resp.data.msg);
    }
    // } catch (error) {
    //   api.onRequestError(error);
    //   setSubmitDisabled(false);
    // }
  };

  const getGroupOptions = (_groupOptions) => {
    setGroupOptions(_groupOptions);
  };

  let commonProps = {
    isEdit,
    isCopy,
    sourcePoolId,
  };

  return (
    <div className="tag-pool">
      <BreadcrumbTips list={breadcrumbList} />
      <Steps current={step} middleText={"基本信息"} />
      {step == 0 && (
        <Create
          history={history}
          {...commonProps}
          getSourcePoolId={getSourcePoolId}
          getWayItem={getWayItem}
          wayItem={wayItem}
          setCreateVisible={setCreateVisible}
          createVisible={createVisible}
          type={type}
          setType={setType}
        />
      )}
      {step == 0 && sourcePoolId && sourceFilterFieldList?.length ? (
        <TagRules
          {...commonProps}
          getEffectRules={getEffectRules}
          goStep={goStep}
          getGroupOptions={getGroupOptions}
          groupOptions={groupOptions}
          requestEffectRules={requestEffectRules}
          setViewDiagVisible={setViewDiagVisible}
          effectRules={effectRules}
          detailData={detailData}
          sourceFilterFieldList={sourceFilterFieldList}
        />
      ) : null}
      {step == 1 && (
        <BaseInfo
          {...commonProps}
          baseInfo={baseInfo}
          goStep={goStep}
          updateBaseInfo={updateBaseInfo}
          savePool={savePool}
          submitPool={submitPool}
        />
      )}
      {step == 2 && <FinishPage history={history} poolId={submitPoolId} />}
      {tempPoolId && viewDiagVisible && (
        <ViewDialog
          {...commonProps}
          effectRules={effectRules}
          goStep={goStep}
          setViewDiagVisible={setViewDiagVisible}
          requestEffectRules={requestEffectRules}
          visible={viewDiagVisible}
          tempPoolId={tempPoolId}
        />
      )}
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(selectedStore),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const selectedStorePage = permissionAccess(LogTimePutInPage);
