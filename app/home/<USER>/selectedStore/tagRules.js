import debugFn from "debug";
import React, { useEffect, useState, Fragment } from "react";
import {
  Balloon,
  Button,
  CascaderSelect,
  Checkbox,
  Dialog,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Message,
  Radio,
  Select,
  Tab,
} from "@alife/next";
import "./style.scss";

import {
  storeEum,
  unique,
  dealQualityScoreToObject,
  yuanToFen,
  changeToFen,
  deepCopy
} from "@/home/<USER>/common";
import moment from "moment";
import * as api from "@/adator/api";
import { poolFiltersConfig as localPoolFiltersConfig, validatorMap, filtersConfigMap } from "@/home/<USER>/common/config";
import { RangeNumberInput } from "@/home/<USER>/common/components";
import { TimeRangePicker } from "@/components/TimeRangePicker";
import {promisify} from "@/utils/others";
const { Row, Col } = Grid;

const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;

const debug = debugFn('selection:poolPage:selectedStore:TagRules');
const formItemLayout = {
  labelCol: { fixedSpan: 4 },
};
const RadioGroup = Radio.Group;

export default function TagRules(props) {
  const { sourcePoolId, sourceFilterFieldList } = props;
  const ctrlConfigArray = (poolConfig) => {
    let result = [];
    for (let o in poolConfig) {
      poolConfig[o].map((v) => {
        result.push(v);
      });
    }
    return result;
  };
  let newPoolFiltersConfig = [];
  let curPoolConfig = {};
  let curPoolConfigArray = [];
  if (sourcePoolId && sourceFilterFieldList.length > 0) {
    curPoolConfigArray = sourceFilterFieldList
    sourceFilterFieldList.forEach((item) => {
      // 过滤掉没用的分类
      if (!curPoolConfig[item.filterFieldIdGroup]) {
        curPoolConfig[item.filterFieldIdGroup] = [];
      }
      curPoolConfig[item.filterFieldIdGroup].push(item);
    });
  }else{
    // 兜底，正常不触发
    console.error('invalid to reach here')
    newPoolFiltersConfig = JSON.parse(JSON.stringify(localPoolFiltersConfig));
    curPoolConfig = newPoolFiltersConfig[sourcePoolId]; //选中池子的配置对象
    curPoolConfigArray = ctrlConfigArray(curPoolConfig); //选中池子的配置数组
  }
  
  // let newPoolFiltersConfig = [];
  // newPoolFiltersConfig = JSON.parse(JSON.stringify(poolFiltersConfig));
  // let curPoolConfig = newPoolFiltersConfig[sourcePoolId]; //选中池子的配置对象
  // let curPoolConfigArray = ctrlConfigArray(curPoolConfig); //选中池子的配置数组

  let curPoolConfigField = {};
  for (let o in curPoolConfig) {
    curPoolConfigField[o] = curPoolConfig[o].map((v) => v.filterFieldKey);
  }
  const [selectedField, setSelectedField] = useState({});
  const [selectedAllField, setSelectedAllField] = useState([]);
  const [tabidx, setTabidx] = useState(sourcePoolId == '33004' ? "baseInfo" : (sourcePoolId == '33005'?"required" : "basicattributes")); //根据池子id判断默认项是哪个
  const [data, setData] = useState(props.effectRules || []); //对应effectRules
  const [tmpData, setTmpData] = useState([]);
  const [postData, setPostData] = useState(props.requestEffectRules || []); //对应requestEffectRules todo修改名称
  const [groupOptions, setGroupOptions] = useState(props.groupOptions || {});
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [analysisDialog, setAnalysisDialog] = useState(false);

  const onTabChange = (value) => {
    setTabidx(value);
    const element = document.getElementById(value);
    element.scrollIntoView(true);
  };

  const getOptions = (item) => {
    return groupOptions[item.filterFieldKey];
  };

  const handleChange = (value, item) => {
    if(value instanceof Array){
      value = value.filter(i=>i)
    }
    const newData = deepCopy(data)
    newData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = value;
    let newPostData = JSON.parse(JSON.stringify(postData));
    let { filterFieldComponentType } = item;
    let newValue = value;
    if (filterFieldComponentType == "checkbox") {
      /* 复选框，转成json串 */
      newValue = JSON.stringify(value);
    } else if (filterFieldComponentType == "arrayInput") { /* 输入框，以'，'隔开的json串 转为 数组的json串 */
      const tmpValue = value.replace(/[\r\n]/g, "");
      newValue = JSON.stringify(tmpValue.split(',').map(item => item.replace(/(^\s*)|(\s*$)/g,"")));
    } else if (filterFieldComponentType == "timeChoose") {
      //日期需要改格式
      const start = value && value[0] && moment(value[0]).format('YYYY-MM-DD HH:mm:ss') || '';
      const end = value && value[1] && moment(value[1]).format('YYYY-MM-DD HH:mm:ss') || '';
      newValue = JSON.stringify(end ? {start, end}: {start});
    }
    newPostData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = newValue;
    setData(newData);
    setPostData(newPostData);
  };

  const handleSelectChange = (value, group, item) => {
    if (!value) return;
    const haveSelected = group.map((item) => {
      const { value: itemValue, label, pos } = item;
      return {
        value: itemValue,
        label,
        level: pos.split("-").length - 1,
      };
    });
    let newData = deepCopy(data);
    let newPostData = JSON.parse(JSON.stringify(postData));
    let index = newPostData.findIndex(
      (o) => o.filterFieldKey == item.filterFieldKey
    );
    if (!newPostData[index].filterFieldValue)
      newPostData[index].filterFieldValue = "";
    newPostData[index].filterFieldValue = JSON.stringify(haveSelected);
    if (item.filterFieldComponentType == "picStandard") {
      /*商品质量分需要特殊处理成  {mainRatioLabel:['0', '1'],psoriasisLabel: ['0'],transparentLabel: ['1'] } 格式 */
      newPostData[index].filterFieldValue = JSON.stringify(
        dealQualityScoreToObject(group)
      );
    }

    let hasPlatformFreightManjian = newPostData.filter((v) => v.filterFieldKey == 'platform_freight_manjian_condition');
    if (sourcePoolId == '33003' && item.filterFieldKey == "store_activity_type_names_1d" && value.includes("运费满减") && hasPlatformFreightManjian.length == 0) { //门店活动类型，选中运费满减，自动添加运费满减门槛
      let manjianCondition = curPoolConfigArray.filter((v) => v.filterFieldKey == 'platform_freight_manjian_condition')[0];
      let manjianDiscount = curPoolConfigArray.filter((v) => v.filterFieldKey == 'platform_freight_manjian_discount')[0];
      newPostData.push(manjianCondition);
      newPostData.push(manjianDiscount);
      newData.push(manjianCondition);
      newData.push(manjianDiscount);
      let newSelectedAllField = JSON.parse(JSON.stringify(selectedAllField));
      newSelectedAllField.push('platform_freight_manjian_discount');
      newSelectedAllField.push('platform_freight_manjian_condition');
      if (!selectedField[item.filterFieldIdGroup])
        selectedField[item.filterFieldIdGroup] = [];
      selectedField[item.filterFieldIdGroup].push('platform_freight_manjian_discount');
      selectedField[item.filterFieldIdGroup].push('platform_freight_manjian_condition');
      setSelectedAllField(newSelectedAllField);
      setSelectedField(selectedField);
    }
    if (!newData[index].filterFieldValue) newData[index].filterFieldValue = "";
    newData[index].filterFieldValue = value;
    tmpData[item.filterFieldKey] = haveSelected;

    // this.field.setValue(item.filterFieldKey,value);

    setPostData(newPostData);
    setData(newData);
    setTmpData(tmpData);
  };

  const handleRangeChange = (value, group, item) => {
    const newData = deepCopy(data)
    newData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = { ...value };
    let newPostData = JSON.parse(JSON.stringify(postData));
    let newPostValue = changeToFen(value,item);
    newPostData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = JSON.stringify({ ...newPostValue });
    setData(newData);
    setPostData(newPostData);
  };

  const field = Field.useField({});

  const switchItem = (item) => {
    const filterFieldComponentType = item.filterFieldComponentType;
    let filterItem = curPoolConfigArray.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    );
    const filterFieldExtend =
      filterItem && filterItem.length > 0
        ? curPoolConfigArray.filter(
          (o) => o.filterFieldKey == item.filterFieldKey
        )[0].filterFieldExtend
        : {};

    const { init } = field;
    let attrs = {
      dataSource:
        item.filterFieldDataType == 1
          ? JSON.parse(item.filterFieldData)
          : getOptions(item),
      value: item.filterFieldValue,
      name: item.filterFieldKey,
    };
    let placeholderName = '英文逗号隔开，最多100个'
    if(item.validatorValue == 'nameSeperatedRequired'){
      placeholderName = '英文逗号隔开，最多30个'
    }
    let defaultRangeExtend = { precision: 1, step: 0.01 };
    if (filterFieldComponentType == "rangeNumberInput" && filterFieldExtend) {
      if (typeof filterFieldExtend == 'string' && filterFieldExtend != '') {
        defaultRangeExtend = JSON.parse(filterFieldExtend);
      }else{
        defaultRangeExtend = filterFieldExtend;
      }
    }
    let result;
    switch (filterFieldComponentType) {
      case "arrayInput": //文本框
        // 设置校验规则，编辑等操作在后端没有校验规则字段，需自行匹配
        let arrayInputValidator = null;
        if (item.validatorValue) {
          arrayInputValidator = validatorMap[item.validatorValue];
        } else {
          filtersConfigMap.forEach((configItem) => {
            if (configItem.filterFieldId == item.filterFieldId) {
              if (configItem.validatorValue) {
                arrayInputValidator = validatorMap[configItem.validatorValue];
              }
            }
          });
        }
        result = (
          <Input.TextArea
            placeholder={placeholderName}
            {...field.init(item.filterFieldKey, {
              rules: [
                {
                  ...attrs,
                },
                {
                  validator: (rule, value, callback) => {
                    if (typeof arrayInputValidator == "function") {
                      arrayInputValidator(
                        rule,
                        value ? value : item.filterFieldValue,
                        callback
                      );
                    } else {
                      callback();
                    }
                  },
                  trigger: ["onBlur"],
                },
              ],
              props: {
                onChange: (value) => handleChange(value, item),
              },
            })}
            value={item.filterFieldValue}
          />
        );
        break;
      case "radio": //单选框
        result = (
          <RadioGroup
            {...attrs}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "checkbox": //复选框
        result = (
          <CheckboxGroup
            {...attrs}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "multipleSelect": //下拉多选
      case "cascaderSelect": //下拉级联多选
      case "picStandard":
        let newValue = item.filterFieldValue;

        result = (
          <CascaderSelect
            expandTriggerType={"hover"}
            {...init(item.filterFieldKey, {
              props: {
                onChange: (value, data) =>
                  handleSelectChange(value, data, item),
              },
            })}
            showSearch
            style={{ width: "100%" }}
            dataSource={getOptions(item)}
            value={newValue}
            multiple={true}
          />
        );
        break;
      case "rangeNumberInput": //店铺评分类
        result = (
          <RangeNumberInput
            {...attrs}
            {...defaultRangeExtend}
            onChange={(value, data) => handleRangeChange(value, data, item)}
          />
        );
        break;
      case "select": //下拉多选
        result = (
          <Select
            showSearch
            {...attrs}
            style={{ width: "200px" }}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "timeChoose": //日期选择
        //日期需要改格式
        // eslint-disable-next-line no-case-declarations
        const timeTmp = attrs.value ;
        if(!Array.isArray(timeTmp) && timeTmp && typeof timeTmp === 'string') {
          const timeTmpObj = JSON.parse(timeTmp)
          const start = timeTmpObj.start || ''
          const end = timeTmpObj.end || ''
          attrs.value = [moment(start) , moment(end)]
        }

        result = (
          <TimeRangePicker
            {...attrs}
            showTime={true}
            style={{ width: "400px" }}
            onChange={(value) => {
              handleChange(value, item);
            }}
          ></TimeRangePicker>
        );
        break;
      default:
        result = <Input />;
        break;
    }
    return result;
  };

  const ctrlGroupOptions = (key, data) => {
    let othersGroup = [
      "store_new_main_category_id",
      "store_store_city_id",
      "store_shop_cat2_id",
      "store_main_category_id",
      "store_city_id",
      "shop_cat2_id",
      "activity_child_type",
      "item_goodsCategory",
    ];
    if (othersGroup.includes(key)) {
      groupOptions[key] = data;
    }
    props.getGroupOptions(groupOptions);
    setGroupOptions(groupOptions);
  };

  /*获取主营类目数据*/
  const fetchMainCategory = async () => {
    try {
      let request = api.getNewAllStoreMainCategory;
      let resp = await request();
      ctrlGroupOptions("store_main_category_id", resp.data);
      ctrlGroupOptions("store_new_main_category_id", resp.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取所在城市数据*/
  const fetchCities = async () => {
    try {
      let request = api.getCities;
      let resp = await request();
      ctrlGroupOptions("store_city_id", resp.data.data);
      ctrlGroupOptions("store_store_city_id", resp.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取门店分类数据*/
  const fetchStoreCategory = async () => {
    try {
      let request = api.getCategoryStore;
      let resp = await request();
      ctrlGroupOptions("shop_cat2_id", resp.data.data);
      ctrlGroupOptions("store_shop_cat2_id", resp.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取商品活动类型数据*/
  const fetchMarketingType= async () => {
    try {
      let request = api.queryMarketingType;
      let resp = await request(sourcePoolId);
      ctrlGroupOptions("activity_child_type",resp.data.data.data);
    } catch (error) {
      api.onRequestError(error)
    }
  }
  /*获取商品分类数据*/
  const fetchSkuCategory = async () => {
    try {
      let request = api.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map(dataItem => {
        dataItem.value = dataItem.value.toString();
        dataItem.children && dataItem.children.map(subItem => {
          subItem.value = subItem.value.toString();
          subItem.children && subItem.children.map(thirdItem => {
            thirdItem.value = thirdItem.value.toString();
          })
        })
        return dataItem;
      });
      ctrlGroupOptions("item_goodsCategory",dataSource);
    } catch (error) {
      api.onRequestError(error)
    }
  }
  useEffect(() => {
    (async () => {
      await fetchSkuCategory();
      await fetchMainCategory();
      await fetchCities();
      await fetchStoreCategory();
      await fetchMarketingType();
      initRightField();
    })();
  }, [props.effectRules, props.requestEffectRules, sourcePoolId, sourceFilterFieldList]);

  useEffect(() => {
    setTabidx(sourcePoolId == '33004' ? "baseInfo" : (sourcePoolId == '33005'?"required" : "basicattributes"))
  }, [sourcePoolId]);
  
  /*初始化标签复选框*/
  const initRightField = () => {
    if (props.isEdit || props.isCopy || props.effectRules.length > 0) {
      let { effectRules, requestEffectRules } = props;

      let tempSelectedAllField = effectRules.map((o) => o.filterFieldKey);
      setSelectedAllField(tempSelectedAllField);
      let tempSelectedField = {};
      effectRules.map((item) => {
        let hasField = effectRules.filter(
          (v) => v.filterFieldKey == item.filterFieldKey
        );
        tempSelectedField[item.filterFieldIdGroup] = [];
        if (hasField.length > 0) {
          tempSelectedField[item.filterFieldIdGroup].push(item);
        }
      });
      setSelectedField(tempSelectedField);
      if (props.isEdit || props.isCopy) {
        //编辑模式，构建data、postData
        let newData = JSON.parse(JSON.stringify(effectRules));
        let newPostData = JSON.parse(JSON.stringify(requestEffectRules));
        setData(newData);
        setPostData(newPostData);
      }
    } else {
      const defaultValueMap = {
        item_ac_CAT: "Y_CAT",
        item_status: "YOU_XIAO",
      }
      // 设置营销门店池必填项
      let temp = curPoolConfigArray.filter(
        (v) =>
          v.filterFieldKey == "item_ac_CAT" || v.filterFieldKey == "item_status"
      );

      temp = temp.map((_item) => {
        const item = { ..._item }
        // 改成使用后端底池配置覆盖本地配置以后，filterFieldValue 被清空了，会影响默认值回填逻辑，这里需要修复
        if (!item.filterFieldValue && defaultValueMap[item.filterFieldKey]) {
          item.filterFieldValue = defaultValueMap[item.filterFieldKey]
        }
        return item;
      })

      setData(temp);
      setPostData(JSON.parse(JSON.stringify(temp)));
      setSelectedField({
        baseInfo: curPoolConfigArray
          .filter(
            (v) =>
              v.filterFieldKey == "item_ac_CAT" ||
              v.filterFieldKey == "item_status"
          )
          .map((v) => v.filterFieldKey),
      });
      setSelectedAllField(["item_ac_CAT", "item_status"]);
    }
  };

  /*todo要去掉 勾选标签事件 Group的change事件 */
  const checkAttr = (key, v) => {
    let newValue = unique(v);
    let value = newValue.filter((v) => curPoolConfigField[key].includes(v));
    if (value != "" || value.length == 0) {
      selectedField[key] = value;
      let selectedAllField = [];
      for (let o in selectedField) {
        if (selectedField[o].length > 0) {
          selectedAllField.push(selectedField[o]);
        }
      }
      let newSelectedAllField = selectedAllField
        .toString()
        .split(",")
        .map((item) => item);
      setSelectedAllField(newSelectedAllField);
      setSelectedField(selectedField);
      let data = [];
      newSelectedAllField.map((o) => {
        let item = curPoolConfigArray.filter((v) => v.filterFieldKey == o);
        if (item && item.length > 0) {
          data.push(item[0]);
        }
      });
      setData(data);
      setPostData(JSON.parse(JSON.stringify(data)));
    }
  };

  /*勾选复选框*/
  const checkField = (checked, event) => {
    let value = event.target ? event.target.value : event;
    let newSelectedAllField = JSON.parse(JSON.stringify(selectedAllField));
    let item = curPoolConfigArray.filter((v) => v.filterFieldKey == value)[0];
    let newData = JSON.parse(JSON.stringify(data));
    let newPostData = JSON.parse(JSON.stringify(postData));
    if (checked) {
      newSelectedAllField.push(value);
      if (!selectedField[item.filterFieldIdGroup])
        selectedField[item.filterFieldIdGroup] = [];
      selectedField[item.filterFieldIdGroup].push(value);
      newData.push(item);
      newPostData.push(item);
    } else {
      const index = newSelectedAllField.findIndex((o) => o == value);
      const index2 = selectedField[item.filterFieldIdGroup].findIndex(
        (o) => o == value
      );
      const index3 = newData.findIndex(
        (o) => o.filterFieldKey == item.filterFieldKey
      );
      if (index > -1) {
        newSelectedAllField = [
          ...newSelectedAllField.slice(0, index),
          ...newSelectedAllField.slice(index + 1),
        ];
        selectedField[item.filterFieldIdGroup] = [
          ...selectedField[item.filterFieldIdGroup].slice(0, index2),
          ...selectedField[item.filterFieldIdGroup].slice(index2 + 1),
        ];
        newData = [...newData.slice(0, index3), ...newData.slice(index3 + 1)];
        newPostData = [
          ...newPostData.slice(0, index3),
          ...newPostData.slice(index3 + 1),
        ];
      }
    }

    setSelectedAllField(newSelectedAllField);
    setSelectedField(selectedField);
    setData(newData);
    setPostData(newPostData);
  };

  /**
   * 校验新建/编辑池子时必填项
   **/
  const validateEffects = () => {
    let requiredGroup = [];
    let result = [];
    let localPoolConfig = localPoolFiltersConfig[sourcePoolId];
    // 先从本地的配置中查找是否有必填分组，如果有优先从本地取（这个是老逻辑暂时保持不动）
    if (localPoolConfig?.required) {
      requiredGroup = localPoolConfig.required.map((v) => v.filterFieldKey);
    } else {
      // 如果本地配置中没有对应的数据，说明可能是新的底池，这种情况下从后端下发的数据中读取必填分组
      if (curPoolConfig?.required) {
        requiredGroup = curPoolConfig.required.map((v) => v.filterFieldKey);
      }
    }
    if (data && data.length > 0) {
      data.map((v) => {
        if (sourcePoolId != "41001") {
          if (
            requiredGroup.includes(v.filterFieldKey) &&
            v.filterFieldValue &&
            v.filterFieldValue != ""
          ) {
            result.push(v.filterFieldKey);
          }
        } else {
          //百亿补贴打标池只需要商品活动必填
          if (
            requiredGroup.includes(v.filterFieldKey) &&
            v.filterFieldKey == "activity_child_type" &&
            v.filterFieldValue &&
            v.filterFieldValue != ""
          ) {
            result.push(v.filterFieldKey);
          }
        }
      });
    }
    // let message = storeEum.filter((v) => v.id == sourcePoolId)[0].message;

    const ret = {
      state: result.length == 0,
      message: requiredGroup.length > 0 ? '标签必填项至少填一项' : null,
    };
    debug('validateEffects result:', ret, 'requiredGroup:', requiredGroup);
    return ret
  };

  /**
   * 点击预览按钮
   **/
  const previewSample = async () => {
    let validateResult = validateEffects();
    if (validateResult.state && validateResult.message) {
      Message.warning(validateResult.message);
      return;
    }
    await (promisify(field.validate)());
    props.getEffectRules(data, postData);
    props.setViewDiagVisible(true);
  };
  
  /**
   * 点击多维分析
   **/
  const onAnalysisData = async () => {
    let validateResult = validateEffects();
    if (validateResult.state && validateResult.message) {
      Message.warning(validateResult.message);
      return;
    }
    await promisify(field.validate)();
    // let speciGroup = ['item_ac_goodsType', 'item_ac_CAT'];
    let newData = data.filter((v) => !!v.filterFieldValue);
    let newPostData = postData.filter((v) => !!v.filterFieldValue);
    setData(deepCopy(newData));
    setPostData(deepCopy(newPostData));
    props.getEffectRules(newData, newPostData);
    setAnalysisDialog(true);
  };

  /**
   * 不预览，直接下一步
   **/
  const noPreview = async () => {
    let validateResult = validateEffects();
    if (validateResult.state && validateResult.message) {
      Message.warning(validateResult.message);
      return;
    }
    await (promisify(field.validate)());
    props.getEffectRules(data, postData);
    props.goStep(2);
  };

  let group = [
    { value: "required", label: "必填项" },
    { value: "basicattributes", label: "基本属性" },
    { value: "browsedeal", label: "浏览和交易信息" },
    { value: "marketing", label: "营销信息" },
    { value: "baseInfo", label: "基本属性" },
    { value: "skuStore", label: "门店信息" },
    { value: "skuMarket", label: "营销信息" },
    { value: "superMarket", label: "全能超市" },
  ];
  let filterGroup = group.filter((v) => {
    return curPoolConfig[v.value];
  });

  return (
    <div className="label-rules">
      <h3 className="header">2. 设置规则</h3>
      <Row className="tab-content-group">
        <Col className="left" span={13}>
          <Tab onChange={onTabChange} activeKey={tabidx}>
            {filterGroup.map((o) => {
              return <Tab.Item title={o.label} key={o.value}></Tab.Item>;
            })}
          </Tab>
          <div className="check-list">
            {filterGroup.map((o, i) => {
              return (
                <Fragment key={`check-list__${i}`}>
                  {curPoolConfig[o.value] && <p id={`${o.value}`}>{o.label}</p>}
                  {curPoolConfig[o.value] &&
                    curPoolConfig[o.value].map((v) => {
                      return (
                        <Checkbox
                          value={v.filterFieldKey}
                          checked={selectedAllField.includes(
                            v.filterFieldKey
                          )}
                          onChange={(checked, event) =>
                            checkField(checked, event)
                          }
                        >
                          {v.filterFieldLabel}{" "}
                          {v.filterFieldDesc && (
                            <Balloon.Tooltip
                              align="t"
                              trigger={
                                <Icon
                                  type="help"
                                  style={{ color: "#CCCCCC" }}
                                  size={"inherit"}
                                />
                              }
                            >
                              {v.filterFieldDesc}
                            </Balloon.Tooltip>
                          )}
                        </Checkbox>
                      );
                    })}
                </Fragment>
              );
            })}
          </div>
        </Col>
        <Col className="right" span={11}>
          <p className="right-title">
            编辑规则<span>请在左侧选择标签</span>
          </p>
          <Form className="rules-form" field={field}>
            {data &&
              data.length > 0 &&
              data.map((item, index) => {
                // type 为 date 日期格式需要强制转化为 moment 格式
                // item.value = item.type == 'date' ? moment(item.value, 'YYYY-MM-DD') : item.value;
                const textWidth =
                  (document.body.offsetWidth - 260) / 2 - 80 + "px";
                return (
                  <FormItem
                    key={index}
                    labelAlign={"top"}
                    {...formItemLayout}
                    label={
                      <>
                        <span>{item.filterFieldLabel?item.filterFieldLabel:''}</span>
                        <Icon
                          type={"delete-filling"}
                          style={{ color: "#CCCCCC" }}
                          onClick={() => checkField(false, item.filterFieldKey)}
                        />
                      </>
                    }
                    hasFeedback
                    // validator={validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))}
                    required={item.required}
                  >
                    {switchItem(item)}
                  </FormItem>
                );
              })}
          </Form>
        </Col>
      </Row>

      <Dialog
        title="数据诊断"
        type="confirm"
        onClose={() => setAnalysisDialog(false)}
        onCancel={() => setAnalysisDialog(false)}
        visible={analysisDialog}
        height="200px"
        footerActions={["ok"]}
        footer={
          <Button
            type="primary"
            loading={analysisLoading}
            onClick={async () => {
              setAnalysisLoading(true);
              try {
                const res = (await api.getTempPoolId()) || {};
                console.log('res=======', res, );
                if (res.success && res.data) {
                  // 打开多维分析 FBI页面
                  const { requestEffectRules } = props;
                  
                  let req = {
                    tempPoolId: res.data,
                    sourcePoolId,
                    orderDirection: "desc",
                    orderBy: "d7ValidOrderCnt",
                    effectFilterFieldBizModelList: requestEffectRules,
                    groupBy: "",
                  };
                  const resp = await api.submitAnalyseTask(req);
                  setAnalysisLoading(false);
                  if (resp.success) {
                    // 获取链接，跳转
                    setAnalysisDialog(false)
                    window.open(resp?.data?.fbiUrl);
                  } else {
                    api.onRequestError({ msg: resp.errMessage });
                  }
                } else {
                  setAnalysisLoading(false);
                  api.onRequestError({ msg: res.errMessage });
                }
              } catch (e) {
                console.log('e=======', e, );
                setAnalysisLoading(false);
                api.onRequestError(e || "请求异常，请稍后重试");
              }
            }}
          >
            确定
          </Button>
        }
      >
        <div style={{ height: 200, width: 400 }}>
          查看最新数据请刷新FBI看板。数据分析量较大，请耐心等待数据展示
        </div>
      </Dialog>

      <div className="btn-panel">
        <Button onClick={() => noPreview()}>不预览，下一步</Button>
        <Button type="primary" onClick={() => previewSample()}>
          预览
        </Button>
        <Button type="primary" onClick={() => onAnalysisData()}>
          数据诊断
        </Button>
      </div>
    </div>
  );
}
