import React, { Fragment, useEffect, useState } from "react";
import {
  Form,
  Select,
  Input,
  Button,
  Field,
  Grid,
  Message,
  Dialog,
  Icon,
  Table,
  Pagination,
} from "@alife/next";
import {
  operatorEum,
  renderShopStateMap,
} from "../common/index";

import "./style.scss";

import * as api from "@/adator/api";
import { promisify } from "@/utils/others";
import { validatorMap } from "@/home/<USER>/common/config";

const { Row, Col } = Grid;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
  labelAlign: "center",
  labelTextAlign: "center",
};

const DEFAULT_GOODS_IMG = require("../../../images/default-goods-pic.png");

export default function ViewDialog(props) {
  const [visible, setVisible] = useState(props.visible);
  const [dataSource, setDataSource] = useState([]);
  const [deleteDataSource, setDeleteDataSource] = useState([]);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(50);
  const [isLoading, setIsLoading] = useState(false);
  const [tabidx, setTabidx] = useState(1); //1:初选商品  0：删除商品
  const [tipsText, setTipsText] = useState("删除");
  const [total, setTotal] = useState(0);
  const [orderDirection, setOrderDirection] = useState("desc");
  const [orderBy, setOrderBy] = useState("d7ValidOrderCnt");
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const { sourcePoolId } = props;
  const [isSearch, setIsSearch] = useState(false);
  const [effectFilterFieldBizModelList, setEffectFilterFieldBizModelList] =
    useState(
      (sourcePoolId == '33004' || sourcePoolId == '33005')?[
        {
          filterFieldId: "store_id",
          filterFieldKey: "store_id",
          filterFieldValue: JSON.stringify([]),
          operator: operatorEum["store_id"],
        },
      ]:[
      {
        filterFieldId: "storeId",
        filterFieldKey: "storeId",
        filterFieldValue: JSON.stringify([]),
        operator: operatorEum["storeId"],
      },
    ]);
  const [filterKey, setFilterKey] = useState((sourcePoolId == '33004' || sourcePoolId == '33005')?"store_id":"storeId");
  const [filterValue, setFilterValue] = useState("");
  const columns = [
    {
      title: "门店",
      dataIndex: "shopName",
      width: 180,
      cell: (name, index, record) => {
        return (
          <div>
            <p className="name">{name ? name : "-"}</p>
            <p className="tips">ELE门店ID:{record.shopId}</p>
            <p className="tips">淘内门店ID:{record.storeId}</p>
            <p className="tips">淘系卖家ID:{record.sellerId}</p>
          </div>
        );
      },
    },
    { title: "标签", dataIndex: "storeLabel", width: 100 },
    { title: "所属品牌", dataIndex: "brand", width: 100 },
    {
      title: "营业状态",
      dataIndex: "businessStatus",
      width: 120,
      cell: (state) => renderShopStateMap(state),
    },
    { title: "门店7天销量", dataIndex: "sevenDayOrderNum", width: 140 },
    { title: "所在城市", dataIndex: "city", width: 140 },
    { title: "门店主营类目", dataIndex: "mainCategoryName", width: 140 },
    { title: "SKU数量", dataIndex: "skuCnt", width: 140 },
    { title: "店铺评分", dataIndex: "storeScore", width: 140 },
    {
      title: "店铺上月综合评分",
      dataIndex: "shopGeneralLevelLastMonthName",
      width: 140,
    },
    {
      title: "操作",
      lock: "right",
      cell: (value, index, record) => {
        return renderOpt(record);
      },
      width: "80px",
    },
  ];

  const viewField = Field.useField({});

  const onSelectChange = (...args) => {
    // rowSelection.selectedRowKeys = ids;
    // setRowSelection(rowSelection);
    setSelectedRowKeys(args[1].map((v) => v.storeId));
  };

  const renderOpt = (record) => {
    return (
      <Button
        type={"primary"}
        text
        className="item-delete"
        onClick={() => deleteGood(record.storeId)}
      >
        {tipsText}
      </Button>
    );
  };

  const deleteGood = async (storeId) => {
    const { tempPoolId, sourcePoolId, requestEffectRules } = props;
    Dialog.confirm({
      title: "提示",
      content: `确定${tipsText}么?`,
      onOk: async () => {
        try {
          let req = {
            poolType: "shop",
            addOrDelete: tabidx,
            targetIdList: [storeId],
          };
          req.tempPoolId = tempPoolId;
          req.operateType = 3;
          let request = api.manualUpdate;
          let resp = await request(req);
          if (resp.success) {
            Message.success(`${tipsText}成功`);
            getTableData();
          } else {
            Message.warning(resp.errMessage || `${tipsText}失败`);
          }
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  useEffect(() => {
    getTableData();
  }, [props.requestEffectRules, props.effectRules, tabidx]);

  const getTableData = () => {
    setSelectedRowKeys([]);
    if (tabidx == 1) {
      getViewGoodViewList(page, size, isSearch);
    } else {
      getViewDeleteGoodList();
    }
  };

  /*在初选商品列表和删除商品列表之间切换*/
  const changeTabidx = (tabidx) => {
    setTabidx(tabidx);
    setTipsText(tabidx == 1 ? "删除" : "恢复");
  };

  /* 删除商品列表,后端接口暂时不支持分页*/
  const getViewDeleteGoodList = () => {
    try {
      api.queryShopDeleteList(props.tempPoolId).then((resp) => {
        // setDataSource(resp.data);
        setDeleteDataSource(resp.data);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  //预览
  const getViewGoodViewList = async (page = 1, size = 50, isSearch = false) => {
    // let {pagination, pageIndex, tempPoolId, isSearch, effectRules, effectFilterFieldBizModelList, pageSize, orderBy, orderDirection, sourcePoolId, isLoading,dataSource,isMore} = this.state;
    if (isSearch) {
      const outerForm = await promisify(viewField.validate)();
    }
    // this.setState({isLoading:true})
    setIsLoading(true);
    setIsSearch(isSearch);
    const { tempPoolId, sourcePoolId, requestEffectRules } = props;
    try {
      let req = {
        tempPoolId,
        sourcePoolId,
        pageSize: size,
        pageIndex: page,
        orderDirection: orderDirection || "desc",
        orderBy: orderBy || "d7ValidOrderCnt",
        effectFilterFieldBizModelList: !isSearch
          ? requestEffectRules
          : requestEffectRules.concat(effectFilterFieldBizModelList),
        needTotalCount: true,
        groupBy: "",
      };
      // 根据池子id判断预览请求的接口。
      if(sourcePoolId == '33004' || sourcePoolId == '33005'){
        api.getNewQueryStorePoolResultViewList(req).then((resp) => {
          let newDataSource = resp.data.filter((v) => v.status == 1);
          setDataSource(newDataSource);
          setTotal(resp.totalCount);
          setIsLoading(false);
        });
      }else{
        api.getQueryStorePoolResultViewList(req).then((resp) => {
          let newDataSource = resp.data.filter((v) => v.status == 1);
          setDataSource(newDataSource);
          setTotal(resp.totalCount);
          setIsLoading(false);
        });
      }
    } catch (error) {
      // this.setState({isLoading:false,isMore:false})
      api.onRequestError(error);
      setIsLoading(false);
    }
  };

  const onPageChange = (page) => {
    setPage(page);
    getViewGoodViewList(page, size);
  };

  const onPageSizeChange = (size) => {
    setSize(size);
    getViewGoodViewList(page, size);
  };

  const nextStep = () => {
    props.goStep(2);
    props.setViewDiagVisible(false);
  };

  const batchDelete = async () => {
    const { tempPoolId, sourcePoolId, requestEffectRules } = props;
    Dialog.confirm({
      title: "提示",
      content: `确定批量${tipsText}?`,
      onOk: async () => {
        try {
          let req = {
            poolType: "goods",
            addOrDelete: tabidx,
            targetIdList: selectedRowKeys,
          };
          req.tempPoolId = tempPoolId;
          req.operateType = 3;
          let request = api.manualUpdate;
          let resp = await request(req);
          Message.success(`${tipsText}成功`);
          // this.initTable();
          getTableData();
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  const onChangeType = (value) => {
    let filterFieldValue =
      effectFilterFieldBizModelList.length > 0
        ? effectFilterFieldBizModelList[0].filterFieldValue
        : 0;
    let newEffectFilterFieldBizModelList = [];
    let item = {
      filterFieldId: value,
      filterFieldKey: value,
      operator: operatorEum[value],
      filterFieldValue,
    };
    newEffectFilterFieldBizModelList.push(item);
    setFilterKey(value);
    setEffectFilterFieldBizModelList(newEffectFilterFieldBizModelList);
  };

  const onInputChange = (value) => {
    let newEffectFilterFieldBizModelList = JSON.parse(
      JSON.stringify(effectFilterFieldBizModelList)
    );
    let item = newEffectFilterFieldBizModelList[0];
    item.filterFieldValue = value ? JSON.stringify(value.split(",")) : "[]";
    setFilterValue(value);
    setEffectFilterFieldBizModelList(newEffectFilterFieldBizModelList);
  };

  const searchGoodList = () => {
    setIsSearch(true);
    setPage(1);
    getViewGoodViewList(page, size, true);
  };

  const onSort = (key, order) => {
    setOrderDirection(order);
    setOrderBy(key);
    getTableData();
  };

  const filterDs = [
    { label: "淘内门店ID", value: "storeId" },
    { label: "门店名称", value: "shopName" },
    { label: "ELE门店ID", value: "shopId" },
    { label: "所属品牌", value: "brandName" },
  ];

  const filterDsNew = [
    { label: "淘内门店ID", value: "store_id" },
    { label: "门店名称", value: "shop_name" },
    { label: "ELE门店ID", value: "shop_id" },
    { label: "所属品牌", value: "item_brand_name" },
  ];

  const validatorsGroup = {
    shopName: "",
    shopId: validatorMap.upcIdCommaSeperated,
    storeId: validatorMap.upcIdCommaSeperated,
    shop_id: validatorMap.upcIdCommaSeperated,
    store_id: validatorMap.upcIdCommaSeperated,
    brandName: "",
  };

  return (
    <Dialog
      title={
        <>
          {tabidx == 1 ? (
            <span>{`初选结果数量：${total}个`}</span>
          ) : (
            <div className="delele-title">
              <Icon type={"left"} />
              <Button className="btn-back" text onClick={() => changeTabidx(1)}>
                返回
              </Button>
              <span>当前位置：已删门店</span>
            </div>
          )}
        </>
      }
      visible={visible}
      top={10}
      height={`${document.body.clientHeight * 0.84}px`}
      width={`${document.body.clientWidth * 0.67}px`}
      className={"view-dialog"}
      onClose={() => props.setViewDiagVisible(false)}
      footer={
        <>
          {tabidx == 1 ? (
            <div className="footer-buttons">
              <Button
                className="btn-delete-list"
                onClick={() => changeTabidx(0)}
              >
                已删门店
              </Button>
              <Button
                className="btn-cancel"
                onClick={() => props.setViewDiagVisible(false)}
              >
                取消
              </Button>
              <Button type={"primary"} onClick={() => nextStep()}>
                下一步
              </Button>
            </div>
          ) : (
            <div className="footer-buttons">
              <Button onClick={() => changeTabidx(1)}>返回</Button>
            </div>
          )}
        </>
      }
    >
      <div className="filter-panel">
        <div className="batch-panel">
          <Icon type="prompt" style={{ color: "#FF7000" }} />
          已选<span>{selectedRowKeys.length}</span>项
          <Button onClick={batchDelete} disabled={selectedRowKeys.length == 0}>
            批量{tipsText}
          </Button>
        </div>
        {tabidx == 1 && (
          <div className="rules">
            <Form
              field={viewField}
              inline={true}
              className="rules-views-form"
              labelAlign="left"
            >
              <FormItem
                label={
                  <Select
                    dataSource={(sourcePoolId == '33004' || sourcePoolId == '33005')?filterDsNew:filterDs}
                    value={filterKey}
                    onChange={onChangeType}
                    style={{ width: "120px" }}
                  />
                }
                validator={validatorsGroup[filterKey]}
              >
                <Input
                  style={{ width: "220px" }}
                  onChange={onInputChange}
                  value={filterValue}
                  name={effectFilterFieldBizModelList[0].filterFieldKey}
                />
                <Button
                  type="secondary"
                  style={{ marginLeft: "10px" }}
                  onClick={searchGoodList}
                >
                  搜索
                </Button>
              </FormItem>
            </Form>
          </div>
        )}
      </div>
      <div
        style={{
          overflow: "auto",
          height: `${document.body.clientHeight * 0.84 - 250}px`,
        }}
      >
        {tabidx == 1 && (
          <Table
            rowSelection={{
              onChange: onSelectChange,
              columnProps: () => {
                return {
                  lock: "left",
                  width: 90,
                  align: "center",
                };
              },
            }}
            loading={isLoading}
            onSort={onSort}
            // rowSelection={true}
            dataSource={dataSource}
            hasBorder={false}
            primaryKey="storeId"
          >
            {columns.map((e, idx) => {
              return <Table.Column {...e} key={idx} />;
            })}
          </Table>
        )}
        {tabidx == 0 && (
          <Table
            rowSelection={{
              onChange: onSelectChange,
              columnProps: () => {
                return {
                  lock: "left",
                  width: 90,
                  align: "center",
                };
              },
            }}
            loading={isLoading}
            onSort={onSort}
            // rowSelection={true}
            dataSource={deleteDataSource}
            hasBorder={false}
            primaryKey="storeId"
          >
            {columns.map((e, idx) => {
              return <Table.Column {...e} key={idx} />;
            })}
          </Table>
        )}
        {tabidx == 1 && !(sourcePoolId == '33004' || sourcePoolId == '33005')&&(
          <Pagination
            popupProps={{ align: "bl tl" }}
            pageSizeList={[50, 100]}
            shape="arrow-only"
            style={{ float: "right", marginTop: "10px" }}
            current={page}
            total={total}
            pageSize={size}
            onChange={onPageChange}
            pageSizeSelector="dropdown"
            pageSizePosition="end"
            onPageSizeChange={onPageSizeChange}
          />
        )}
      </div>
    </Dialog>
  );
}
