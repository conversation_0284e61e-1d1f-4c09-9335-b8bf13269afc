import React, { Fragment, useEffect, useState } from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import {config} from '../../../config/index'
import './index.scss'
export default function pageSetUp({ location, match, history }) {
  const URL = window.configEnv == 'prod' ? 'https://newretail-luna.alibaba-inc.com/#/pages?showNav=false' : 'https://pre-newretail-luna.alibaba-inc.com/#/pages?showNav=false'
  return (
    <div className="page_set_up">
      <div className="iframe-wrapper">
        <iframe
          height={'100vh'}
          src={URL}
          frameBorder="0"
          className="preview-iframe">
        </iframe>
      </div>
    </div>
  )
}

export const LogTimePageSetUp = logTimeComponent(
  withRouter(pageSetUp),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const PageSetUp = permissionAccess(LogTimePageSetUp);