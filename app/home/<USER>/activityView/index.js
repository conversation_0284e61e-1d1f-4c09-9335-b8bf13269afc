import React, {Fragment, useEffect, useState} from 'react';
import {
  <PERSON>rid,
  Button, Message, Breadcrumb
} from '@alife/next';
import './style.scss';
import {goldLog, logTimeComponent, track} from "@/utils/aplus";
import {Link, withRouter} from "react-router-dom";
import {permissionAccess} from "@/components/PermissionAccess";
import { ActivityEntity } from '@ali/luna-rmc';

function ActivityView({history,match}) {
  const {activityId} = match.params;
  console.log(activityId);

  const onDetailCallBack = data => {
    console.log(data);
  }

  return (
    <div className='activity-view-container'>
      <div className="nav-wrapper">
        <Breadcrumb>
          <Breadcrumb.Item link="javascript:void(0);"> <Link to={"/putIn/list"}>投放活动</Link></Breadcrumb.Item>
          <Breadcrumb.Item link="javascript:void(0);">
            查看投放活动
          </Breadcrumb.Item>
        </Breadcrumb>
      </div>
      <div className='activity-view'>
        <ActivityEntity style={{width:'700px'}} activityId={activityId} action={'preview'} onDetailCallBack={onDetailCallBack}/>
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(ActivityView), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ActivityViewPage = permissionAccess(LogTimePutInPage)





