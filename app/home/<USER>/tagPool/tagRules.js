import debugFn from "debug";
import React, { useEffect, useState } from "react";
import {
  Balloon,
  Button,
  CascaderSelect,
  Checkbox,
  Dialog,
  Field,
  Form,
  Grid,
  Icon,
  Input,
  Message,
  Radio,
  Select,
  Tab,
} from "@alife/next";
import "./style.scss";
import {
  poolEum,
  unique,
  dealQualityScoreToObject,
  deepCopy,
} from "@/home/<USER>/common";
import * as api from "@/adator/api";
import {
  poolFiltersConfig as localPoolFiltersConfig,
  validatorMap,
  filtersConfigMap
} from "@/home/<USER>/common/config";
import { RangeNumberInput } from "@/home/<USER>/common/components";
import { promisify } from "@/utils/others";

import SelectSearchWithApi from "./SelectSearchWithApi";
import BatchInputOrUploadOfFiles from "./BatchInputOrUploadOfFiles";
import { MktConditions } from "@/components/controls/MktConditions"
import { getFilterFieldPolicy } from "@/selection";

const { Row, Col } = Grid;

const debug = debugFn('selection:poolPage:tagPool:TagRules');
const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { fixedSpan: 4 },
};
const RadioGroup = Radio.Group;

export default function TagRules(props) {
  const { sourcePoolId, sceneDetail = {}, sourceFilterFieldList } = props;
  const { sceneBaseId = "", optionalFilterFieldList = [] } = sceneDetail;
  const ctrlConfigArray = (poolConfig) => {
    let result = [];
    for (let o in poolConfig) {
      poolConfig[o].map((v) => {
        result.push(v);
      });
    }
    return result;
  };
  let newPoolFiltersConfig = [];
  let curPoolConfig = {};
  let curPoolConfigArray = [];
  if (sourcePoolId && sourceFilterFieldList.length > 0) {
    curPoolConfigArray = sourceFilterFieldList
    sourceFilterFieldList.forEach((item) => {
      // 过滤掉没用的分类
      if (!curPoolConfig[item.filterFieldIdGroup]) {
        curPoolConfig[item.filterFieldIdGroup] = [];
      }
      curPoolConfig[item.filterFieldIdGroup].push(item);
    });
  }else if (!sceneBaseId){
    console.error('invalid to reach here');
    // 兜底，正常不触发
    newPoolFiltersConfig = JSON.parse(JSON.stringify(localPoolFiltersConfig));
    curPoolConfig = newPoolFiltersConfig[sourcePoolId]; //选中池子的配置对象
    curPoolConfigArray = ctrlConfigArray(curPoolConfig); //选中池子的配置数组
  }
  
  if (sceneBaseId) {
    curPoolConfig = {}
    curPoolConfigArray = optionalFilterFieldList;
    optionalFilterFieldList.forEach((item) => {
      // 过滤掉没用的分类
      if (!curPoolConfig[item.filterFieldIdGroup]) {
        curPoolConfig[item.filterFieldIdGroup] = [];
      }
      curPoolConfig[item.filterFieldIdGroup].push(item);
    });
  } 

  let curPoolConfigField = {};
  for (let o in curPoolConfig) {
    curPoolConfigField[o] = curPoolConfig[o].map((v) => v.filterFieldKey);
  }
  
  const [selectedField, setSelectedField] = useState({});
  const [selectedAllField, setSelectedAllField] = useState([]);
  const [tabidx, setTabidx] = useState("baseInfo");
  const [data, setData] = useState(props.effectRules || []); //对应effectRules
  const [tmpData, setTmpData] = useState([]);
  const [postData, setPostData] = useState(props.requestEffectRules || []); //对应requestEffectRules todo修改名称
  const [groupOptions, setGroupOptions] = useState(props.groupOptions || {});
  const [searchOptions, setSearchOptions] = useState({}); //可支持查询的下拉
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [analysisDialog, setAnalysisDialog] = useState(false);

  const onTabChange = (value) => {
    setTabidx(value);
    const element = document.getElementById(value);
    element.scrollIntoView(true);
  };

  const getOptions = (item) => {
    return groupOptions[item.filterFieldKey];
  };

  const handleChange = (value, item) => {
    if(value instanceof Array){
      value = value.filter(i=>i)
    }
    const newData = deepCopy(data)
    newData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = value;
    let newPostData = JSON.parse(JSON.stringify(postData));
    let { filterFieldComponentType } = item;
    let newValue = value || '';
    if (filterFieldComponentType == "checkbox") {
      /* 复选框，转成json串 */
      newValue = JSON.stringify(value);
    } else if (filterFieldComponentType === "arrayInput") {
      /* 输入框，以'，'隔开的json串 转为 数组的json串 */
      const tmpValue = value.replace(/[\r\n]/g, "");
      newValue = JSON.stringify(
        tmpValue.split(",").map((text) => text.replace(/(^\s*)|(\s*$)/g, ""))
      );
    } else if (filterFieldComponentType === "batchInputOrUploadOfFiles") {
      if (value && value.type === "input" && value.value) {
        // value: { type: 'input', value: 'xxx,xxx,xxx' }
        /* 输入框，以'，'隔开的json串 转为 数组的json串 */
        const curValue = { type: "input" };
        curValue.value =
          value.value
            .replace(/，/g, ",")
            .split(",")
            .map((inputValue) => inputValue.trim())
            .filter(Boolean)
        newValue = JSON.stringify(curValue);
      } else if (value && value.type === "upload" && value.value) {
        // value: { type: 'upload', value: 'https://xxx.xlsx' }
        newValue = JSON.stringify({
          type: "upload",
          value: value.value,
        });
      }
    } else if (filterFieldComponentType === "mktConditions") {
      /* 年货节定制组件，转成json串 */
      newValue = JSON.stringify(value);
    } else {
      const policy = getFilterFieldPolicy(filterFieldComponentType);
      if (policy) {
        newValue = policy.generatePostDataOnChange({ value });
      }
    }

    newPostData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = newValue;
    setData(newData);
    setPostData(newPostData);
  };

  const handleSelectChange = (value, group, item) => {
    if (!value) return;
    const haveSelected = group.map((g) => {
      const { value: itemValue, label, pos } = g;
      return {
        value: itemValue,
        label,
        level: pos ? pos.split("-").length - 1 : undefined,
      };
    });
    let newPostData = JSON.parse(JSON.stringify(postData));
    let index = newPostData.findIndex(
      (o) => o.filterFieldKey == item.filterFieldKey
    );
    if (!newPostData[index].filterFieldValue)
      newPostData[index].filterFieldValue = "";
    newPostData[index].filterFieldValue = JSON.stringify(haveSelected);
    if (item.filterFieldComponentType == "picStandard") {
      /*商品质量分需要特殊处理成  {mainRatioLabel:['0', '1'],psoriasisLabel: ['0'],transparentLabel: ['1'] } 格式 */
      newPostData[index].filterFieldValue = JSON.stringify(
        dealQualityScoreToObject(group)
      );
    }
    let newData = deepCopy(data)
    if (!newData[index].filterFieldValue) newData[index].filterFieldValue = "";
    newData[index].filterFieldValue = value;
    tmpData[item.filterFieldKey] = haveSelected;
    setPostData(newPostData);
    setData(newData);
    setTmpData(tmpData);
  };

  const handleRangeChange = (value, group, item) => {
    const newData = deepCopy(data)
    newData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = { ...value };
    let newPostData = JSON.parse(JSON.stringify(postData));
    newPostData.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    )[0].filterFieldValue = JSON.stringify({ ...value });
    setData(newData);
    setPostData(newPostData);
  };

  let searchTimeout;
  const handleSearch = (keyword) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      if (keyword) {
        api.queryBrand({ brandName: keyword }).then((resp) => {
          const dataSource = resp.data.data.map((item) => ({
            label: item.name,
            value: item.id,
          }));
          ctrlSearchOptions("sku_brand_id", dataSource);
        });
      } else {
        ctrlSearchOptions("sku_brand_id", []);
      }
    }, 800);
  };

  const isDisabled = (item) => {
    if (
      props.isEdit &&
      item.filterFieldKey == "item_orig_price" &&
      item.filterFieldComponentType == "rangeNumberInput" &&
      props.detailData &&
      props.detailData.outSynPlatforms &&
      props.detailData.outSynPlatforms.length &&
      props.detailData.outSynPlatforms[0] == 'invite_item'
    ) { // 单品叠加券编辑时 商品原价要禁用
      return true;
    } 
    return false
  }

  const field = Field.useField({});

  const switchItem = (item) => {
    const filterFieldComponentType = item.filterFieldComponentType;
    let filterItem = curPoolConfigArray.filter(
      (o) => o.filterFieldKey == item.filterFieldKey
    );
    const filterFieldExtend =
      filterItem && filterItem.length > 0
        ? curPoolConfigArray.filter(
            (o) => o.filterFieldKey == item.filterFieldKey
          )[0].filterFieldExtend
        : {};

    const { init } = field;
    let attrs = {
      dataSource:
        item.filterFieldDataType == 1
          ? JSON.parse(item.filterFieldData)
          : getOptions(item),
      value: item.filterFieldValue,
      name: item.filterFieldKey,
      disabled: item.isDisabled
    };
    let placeholderName = "英文逗号隔开，最多100个";
    // if (item.validatorValue == "upcIdCommaSeperated" || item.validatorValue == "idCommaSeperatedRequired") {
    if (item.validatorValue === 'nameSeperatedRequired') {
      placeholderName = "英文逗号隔开，最多30个";
    }
    let defaultRangeExtend = { precision: 1, step: 0.01 };

    if (isDisabled(item)) { // 单品叠加券编辑时 商品原价要禁用
      attrs.disabled = true;
    }
    if (filterFieldComponentType == "rangeNumberInput" && filterFieldExtend) {
      if (typeof filterFieldExtend == 'string' && filterFieldExtend != '') {
        defaultRangeExtend = JSON.parse(filterFieldExtend);
      }else{
        defaultRangeExtend = filterFieldExtend;
      }
    }

    let result;
 
    switch (filterFieldComponentType) {
      case "arrayInput": //文本框
        // 设置校验规则，编辑等操作在后端没有校验规则字段，需自行匹配
        let arrayInputValidator = null;
        if (item.validatorValue) {
          arrayInputValidator = validatorMap[item.validatorValue];
        } else {
          filtersConfigMap.forEach((configItem) => {
            if (configItem.filterFieldId == item.filterFieldId) {
              if (configItem.validatorValue) {
                arrayInputValidator = validatorMap[configItem.validatorValue];
              }
            }
          });
        }
        result = (
          <Input.TextArea
            placeholder={placeholderName}
            {...field.init(item.filterFieldKey, {
              rules: [
                {
                  ...attrs,
                },
                {
                  validator: (rule, value, callback) => {
                    if (typeof arrayInputValidator == "function") {
                      arrayInputValidator(
                        rule,
                        value ? value : item.filterFieldValue,
                        callback
                      );
                    } else {
                      callback();
                    }
                  },
                  trigger: ["onBlur"],
                },
              ],
              props: {
                onChange: (value) => handleChange(value, item),
              },
            })}
            disabled={attrs.disabled}
            value={item.filterFieldValue}
          />
        );
        break;
      case "radio": //单选框
        result = (
          <RadioGroup
            {...attrs}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "checkbox": //复选框
        result = (
          <CheckboxGroup
            {...attrs}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "multipleSelect": //下拉多选
      case "cascaderSelect": //下拉级联多选
      case "picStandard":
        let newValue = item.filterFieldValue;
        console.log("🚀 ~ file: tagRules.js:328 ~ switchItem ~ newValue:", newValue)
        result = (
          <CascaderSelect
            expandTriggerType={"hover"}
            {...init(item.filterFieldKey, {
              props: {
                onChange: (value, _data) =>
                  handleSelectChange(value, _data, item),
              },
            })}
            disabled={attrs.disabled}
            followTrigger
            showSearch
            style={{ width: "100%" }}
            popupClassName={`rules_from_${item.filterFieldKey}`}
            dataSource={getOptions(item)}
            value={newValue}
            multiple={true}
          />
        );
        break;
      case "rangeNumberInput": //店铺评分类
        result = (
          <RangeNumberInput
            {...attrs}
            {...defaultRangeExtend}
            onChange={(value, _data) => handleRangeChange(value, _data, item)}
          />
        );
        break;
      case "select": //下拉多选
        result = (
          <Select
            showSearch
            {...attrs}
            followTrigger
            style={{ width: "200px" }}
            onChange={(value) => handleChange(value, item)}
          />
        );
        break;
      case "selectSearch": //下拉多选
        result = (
          <CascaderSelect
            showSearch
            followTrigger
            multiple={true}
            {...init(item.filterFieldKey, {
              props: {
                onChange: (value, _data) =>
                  handleSelectChange(value, _data, item),
              },
            })}
            disabled={attrs.disabled}
            value={item.filterFieldValue}
            filterLocal={false}
            dataSource={
              searchOptions && searchOptions[item.filterFieldId]
                ? searchOptions[item.filterFieldId]
                : []
            }
            onSearch={(value) => handleSearch(value)}
            style={{ width: "100%" }}
          />
        );
        break;
      case "selectSearchWithApi": //下拉多选
        result = (
          <SelectSearchWithApi
            {...init(item.filterFieldKey)}
            filterFieldData={item.filterFieldData || ''}
            disabled={attrs.disabled}
            value={item.filterFieldValue}
            onChange={(_value, _data) => {
            handleSelectChange(_value, _data, item);
            }}
          />
        );
        break;
      case "batchInputOrUploadOfFiles": // 手动输入或批量上传
        result = (
          <BatchInputOrUploadOfFiles
            {...init(item.filterFieldKey)}
            disabled={attrs.disabled}
            filterFieldLabel={item.filterFieldLabel}
            placeholderName={placeholderName}
            value={item.filterFieldValue || {type: "input"}}
            onChange={(_value) => {
              handleChange(_value, item);
            }}
          />
        );
        break;
      case "mktConditions":
        result = (
          <MktConditions
            {...init(item.filterFieldKey)}
            dataSource={attrs.dataSource}
            value={item.filterFieldValue}
            onChange={(_value) => {
              handleChange(_value, item);
            }}
          />
        );
        break;
      default:
        const policy = getFilterFieldPolicy(filterFieldComponentType);
        if (policy.renderControl) {
          result = policy.renderControl({
            ...init(item.filterFieldKey),
            dataSource: attrs.dataSource,
            value: item.filterFieldValue,
            onChange: (_value) => {
              handleChange(_value, item);
            },
          });
        } else {
          result = <Input disabled={attrs.disabled}/>;
        }
        break;
    }
    return result;
  };

  const ctrlGroupOptions = (key, _data) => {
    let othersGroup = [
      "item_goodsCategory",
      "store_main_category_id",
      "store_cat2_id",
      "store_city_id",
      "item_activity_types",
      "activity_child_type",
    ];
    if (othersGroup.includes(key)) {
      groupOptions[key] = _data;
    }
    props.getGroupOptions(groupOptions);
    setGroupOptions(groupOptions);
  };

  const ctrlSearchOptions = (key, _data) => {
    let group = ["sku_brand_id"];
    if (group.includes(key)) {
      const originData = searchOptions[key] ? searchOptions[key] : [];
      const wholeData = _data.concat(originData);
      searchOptions[key] = Array.from(new Set(wholeData));
    }
    setSearchOptions(JSON.parse(JSON.stringify(searchOptions)));
  };

  /*获取商品分类数据*/
  const fetchSkuCategory = async () => {
    try {
      let request = api.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map((dataItem) => {
        dataItem.value = dataItem.value.toString();
        dataItem.children &&
          dataItem.children.map((subItem) => {
            subItem.value = subItem.value.toString();
            subItem.children &&
              subItem.children.map((thirdItem) => {
                thirdItem.value = thirdItem.value.toString();
              });
          });
        return dataItem;
      });
      ctrlGroupOptions("item_goodsCategory", dataSource);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取主营类目数据*/
  const fetchMainCategory = async () => {
    try {
      let request = api.getNewAllStoreMainCategory;
      let resp = await request();
      ctrlGroupOptions("store_main_category_id", resp.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取所在城市数据*/
  const fetchCities = async () => {
    try {
      let request = api.getCities;
      let resp = await request();
      ctrlGroupOptions("store_city_id", resp.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取门店分类数据*/
  const fetchStoreCategory = async () => {
    try {
      let request = api.getCategoryStore;
      let resp = await request();
      ctrlGroupOptions("store_cat2_id", resp.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取商品活动类型数据*/
  const fetchMarketingType = async () => {
    try {
      let request = api.queryMarketingType;
      let resp = await request(sourcePoolId);
      ctrlGroupOptions("item_activity_types", resp.data.data.data);
      ctrlGroupOptions("activity_child_type", resp.data.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取品牌名数据*/
  const fetchBrand = async (brandName) => {
    try {
      let request = api.queryBrand;
      let resp = await request({ brandName });
      ctrlGroupOptions("sku_brand_id", resp.data.data.data);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  useEffect(() => {
    (async () => {
      await fetchSkuCategory();
      await fetchMainCategory();
      await fetchCities();
      await fetchStoreCategory();
      await fetchMarketingType();
      initRightField();

      initSearchOption();
    })();
  }, [props.effectRules, props.requestEffectRules, sourcePoolId]);

  /*初始化标签复选框*/
  const initRightField = () => {
    if (props.isEdit || props.isCopy || props.effectRules.length > 0) {
      let { effectRules, requestEffectRules } = props;
      let tempSelectedAllField = effectRules.map((o) => o.filterFieldKey);
      setSelectedAllField(tempSelectedAllField);
      let tempSelectedField = {};
      effectRules.map((item) => {
        let hasField = effectRules.filter(
          (v) => v.filterFieldKey == item.filterFieldKey
        );
        tempSelectedField[item.filterFieldIdGroup] = [];
        if (hasField.length > 0) {
          tempSelectedField[item.filterFieldIdGroup].push(item);
        }
      });
      setSelectedField(tempSelectedField);
      if (props.isEdit || props.isCopy) {
        //编辑模式，构建data、postData
        let newData = JSON.parse(JSON.stringify(effectRules));
        let newPostData = JSON.parse(JSON.stringify(requestEffectRules));
        setData(newData);
        setPostData(newPostData);
      }
    } else if (
      ![
        "50003",
        "50007",
        "50004",
        "60003",
        "60004",
        "24005",
        "41001",
        "24002",
        "24003",
      ].includes(sourcePoolId)
    ) {
      const defaultValueMap = {
        item_ac_CAT: "Y_CAT",
        item_status: "YOU_XIAO",
        item_d30_valid_order_cnt: { start: 1 }
      }
      // 设置默认值，对获取的map进行渲染。
      let temp = curPoolConfigArray.filter(
        (v) =>
          v.filterFieldKey == "item_ac_CAT" ||
          v.filterFieldKey == "item_status" ||
          v.filterFieldKey == "item_d30_valid_order_cnt"
      );
      temp = temp.map((_item) => {
        let item = { ..._item }
        // 改成使用后端底池配置覆盖本地配置以后，filterFieldValue 被清空了，会影响默认值回填逻辑，这里需要修复
        if (!item.filterFieldValue && defaultValueMap[item.filterFieldKey]) {
          item.filterFieldValue = defaultValueMap[item.filterFieldKey]
        }
        if (typeof item.filterFieldValue == "string") {
          try {
            if (typeof JSON.parse(item.filterFieldValue) == "object") {
              item.filterFieldValue = JSON.parse(item.filterFieldValue);
            }
          } catch {
            item.filterFieldValue = item.filterFieldValue;
          }
        }
        return item;
      });
      setData(temp);
      let newTemp = JSON.parse(JSON.stringify(temp));
      newTemp.map((item) => {
        if (item && item.filterFieldValue && typeof item.filterFieldValue != "string") {
          item.filterFieldValue = JSON.stringify(item.filterFieldValue);
        }
      });
      setPostData(newTemp);
      setSelectedField({
        baseInfo: curPoolConfigArray
          .filter(
            (v) =>
              v.filterFieldKey == "item_ac_CAT" ||
              v.filterFieldKey == "item_status"
          )
          .map((v) => v.filterFieldKey),
        skuTrade: curPoolConfigArray
          .filter((v) => v.filterFieldKey == "item_d30_valid_order_cnt")
          .map((v) => v.filterFieldKey),
      });
      setSelectedAllField([
        "item_ac_CAT",
        "item_status",
        "item_d30_valid_order_cnt",
      ]);
    }
  };

  const initSearchOption = () => {
    let newSearchOptions = {};
    if (
      (props.isEdit || props.isCopy) &&
      props.requestEffectRules &&
      props.requestEffectRules.length > 0
    ) {
      props.requestEffectRules.map((v) => {
        if (v.filterFieldComponentType == "selectSearch") {
          newSearchOptions[v.filterFieldKey] = v.filterFieldValue
            ? JSON.parse(v.filterFieldValue)
            : [];
        }
      });
    }
    setSearchOptions(newSearchOptions);
  };

  /*todo要去掉 勾选标签事件 Group的change事件 */
  const checkAttr = (key, v) => {
    let newValue = unique(v);
    let value = newValue.filter((_v) => curPoolConfigField[key].includes(_v));
    if (value != "" || value.length == 0) {
      selectedField[key] = value;
      let selectedAllField = [];
      for (let o in selectedField) {
        if (selectedField[o].length > 0) {
          selectedAllField.push(selectedField[o]);
        }
      }
      let newSelectedAllField = selectedAllField
        .toString()
        .split(",")
        .map((item) => item);
      setSelectedAllField(newSelectedAllField);
      setSelectedField(selectedField);
      let data = [];
      newSelectedAllField.map((o) => {
        let item = curPoolConfigArray.filter((v) => v.filterFieldKey == o);
        if (item && item.length > 0) {
          data.push(item[0]);
        }
      });
      setData(data);
      setPostData(JSON.parse(JSON.stringify(data)));
    }
  };

  /*勾选复选框*/
  const checkField = (checked, event) => {
    let value = event.target ? event.target.value : event;
    let newSelectedAllField = JSON.parse(JSON.stringify(selectedAllField));
    let item = curPoolConfigArray.filter((v) => v.filterFieldKey == value)[0];
    let newData = JSON.parse(JSON.stringify(data));
    let newPostData = JSON.parse(JSON.stringify(postData));
    console.log('postData=======', postData, newPostData);
    if (checked) {
      newSelectedAllField.push(value);
      if (!selectedField[item.filterFieldIdGroup])
        selectedField[item.filterFieldIdGroup] = [];
      selectedField[item.filterFieldIdGroup].push(value);
      newData.push(item);
      // 在勾选时判断值是否为string
      if (typeof item.filterFieldValue != "string") {
        let newItem = deepCopy(item);
        newItem.filterFieldValue = newItem.filterFieldValue && JSON.stringify(newItem.filterFieldValue);
        newPostData.push(newItem);
      } else {
        newPostData.push(item);
      }
    } else {
      const index = newSelectedAllField.findIndex((o) => o == value);
      if (!selectedField[item.filterFieldIdGroup])
        selectedField[item.filterFieldIdGroup] = [];
      const index2 = selectedField[item.filterFieldIdGroup].findIndex(
        (o) => o == value
      );
      const index3 = newData.findIndex(
        (o) => o.filterFieldKey == item.filterFieldKey
      );
      if (index > -1) {
        newSelectedAllField = [
          ...newSelectedAllField.slice(0, index),
          ...newSelectedAllField.slice(index + 1),
        ];
        selectedField[item.filterFieldIdGroup] = [
          ...selectedField[item.filterFieldIdGroup].slice(0, index2),
          ...selectedField[item.filterFieldIdGroup].slice(index2 + 1),
        ];
        newData = [...newData.slice(0, index3), ...newData.slice(index3 + 1)];
        newPostData = [
          ...newPostData.slice(0, index3),
          ...newPostData.slice(index3 + 1),
        ];
      }
    }
    setSelectedAllField(newSelectedAllField);
    setSelectedField(selectedField);
    setData(newData);
    setPostData(newPostData);
  };

  /**
   * 校验新建/编辑池子时必填项
   **/
  const validateEffects = () => {
    let requiredGroup = [];
    let result = [];
    let message = "";
    if (!sceneBaseId) {
      let localPoolConfig = localPoolFiltersConfig[sourcePoolId];
      // 先从本地的配置中查找是否有必填分组，如果有优先从本地取（这个是老逻辑暂时保持不动）
      let localConfigWorks = false
      if (localPoolConfig) {
        if (localPoolConfig?.required) {
          localConfigWorks = true
          requiredGroup = localPoolConfig.required.map((v) => v.filterFieldKey);
        }
      }
      if (!localConfigWorks) {
        // 如果本地配置中没有去除对应的数据，说明是新的底池，这种情况下从后端下发的数据中读取必填分组
        if (curPoolConfig?.required) {
          requiredGroup = curPoolConfig.required.map((v) => v.filterFieldKey);
        }
      }

      if (data && data.length > 0) {
        data.map((v) => {
          if (sourcePoolId != "41001") {
            if (
              requiredGroup.includes(v.filterFieldKey) &&
              v.filterFieldValue &&
              v.filterFieldValue != ""
            ) {
              result.push(v.filterFieldKey);
            }
          } else {
            //百亿补贴打标池只需要商品活动必填
            if (
              requiredGroup.includes(v.filterFieldKey) &&
              v.filterFieldKey == "activity_child_type" &&
              v.filterFieldValue &&
              v.filterFieldValue != ""
            ) {
              result.push(v.filterFieldKey);
            }
          }
        });
      }
      // message = poolEum.filter((v) => v.id == sourcePoolId)[0]
      //   ? poolEum.filter((v) => v.id == sourcePoolId)[0].message
      //   : "";
      message = requiredGroup.length > 0 ? "标签必填项至少填一项" : ""
    } else {
      try {
        requiredGroup = curPoolConfigField.required;
        if (data && data.length > 0) {
          data.map((v) => {
            if (
              requiredGroup.includes(v.filterFieldKey) &&
              v.filterFieldValue &&
              v.filterFieldValue != ""
            ) {
              result.push(v.filterFieldKey);
            }
          });
        }
        message = "标签必填项至少填一项";
      } catch (error) {}
    }
    const ret = {
      state: result.length == 0,
      message,
    };
    debug('validateEffects result:', ret, 'requiredGroup:', requiredGroup);
    return ret
  };

  /**
   * 点击预览按钮
   **/
  const previewSample = async () => {
    let validateResult = validateEffects();
    if (validateResult.state && validateResult.message) {
      Message.warning(validateResult.message);
      return;
    }
    await promisify(field.validate)();
    // let speciGroup = ['item_ac_goodsType', 'item_ac_CAT'];
    let newData = data.filter((v) => !!v.filterFieldValue);
    let newPostData = postData.filter((v) => !!v.filterFieldValue);
    setData(deepCopy(newData));
    setPostData(deepCopy(newPostData));
    props.getEffectRules(newData, newPostData);
    props.setViewDiagVisible(true);
  };

  /**
   * 点击多维分析
   **/
  const onAnalysisData = async () => {
    let validateResult = validateEffects();
    if (validateResult.state && validateResult.message) {
      Message.warning(validateResult.message);
      return;
    }
    await promisify(field.validate)();
    // let speciGroup = ['item_ac_goodsType', 'item_ac_CAT'];
    let newData = data.filter((v) => !!v.filterFieldValue);
    let newPostData = postData.filter((v) => !!v.filterFieldValue);
    setData(deepCopy(newData));
    setPostData(deepCopy(newPostData));
    props.getEffectRules(newData, newPostData);
    setAnalysisDialog(true);
  };

  /**
   * 不预览，直接下一步
   **/
  const noPreview = async () => {
    let validateResult = validateEffects();
    if (validateResult.state && validateResult.message) {
      Message.warning(validateResult.message);
      return;
    }
    await promisify(field.validate)();
    props.getEffectRules(data, postData);
    props.goStep(2);
  };

  let group = [
    { value: "required", label: "必填项" },
    { value: "baseInfo", label: "基本信息" },
    { value: "skuStore", label: "门店信息" },
    { value: "skuTrade", label: "交易信息" },
    { value: "skuMarket", label: "营销信息" },
    { value: "scene", label: "场景信息" },
    { value: "superMarket", label: "全能超市" },
  ];
  let filterGroup = group.filter((v) => {
    return curPoolConfig[v.value];
  });

  return (
    <div className="label-rules">
      <h3 className="header">2. 设置规则</h3>
      <Row className="tab-content-group">
        <Col className="left" span={13}>
          <Tab onChange={onTabChange} activeKey={tabidx}>
            {filterGroup.map((o) => {
              return <Tab.Item title={o.label} key={o.value}></Tab.Item>;
            })}
          </Tab>
          <div className="check-list">
            {filterGroup.map((o) => {
              return (
                <>
                  {curPoolConfig[o.value] && <p id={`${o.value}`}>{o.label}</p>}
                  {curPoolConfig[o.value] &&
                    curPoolConfig[o.value].map((v) => {
                      return (
                        <Checkbox
                          value={v.filterFieldKey}
                          checked={selectedAllField.includes(
                            v.filterFieldKey
                          )}
                          onChange={(checked, event) =>
                            checkField(checked, event)
                          }
                        >
                          {v.filterFieldLabel}{" "}
                          {v.filterFieldDesc && (
                            <Balloon.Tooltip
                              align="t"
                              trigger={
                                <Icon
                                  type="help"
                                  style={{ color: "#CCCCCC" }}
                                  size={"inherit"}
                                />
                              }
                            >
                              {v.filterFieldDesc}
                            </Balloon.Tooltip>
                          )}
                        </Checkbox>
                      )
                    })}
                </>
              );
            })}
          </div>
        </Col>
        <Col className="right" span={11}>
          <p className="right-title">
            编辑规则<span>请在左侧选择标签</span>
          </p>
          <Form className="rules-form" field={field}>
            {data &&
              data.length > 0 &&
              data.map((item, index) => {
                // type 为 date 日期格式需要强制转化为 moment 格式
                // const textWidth =
                //   (document.body.offsetWidth - 260) / 2 - 80 + "px";
                if (item.filterFieldId == "item_expert_pool_id") {
                  //隐藏部分不展示但需要传的指标
                  return "";
                }
                return (
                  <FormItem
                    key={index}
                    labelAlign={"top"}
                    {...formItemLayout}
                    label={
                      <>
                        <span>{item.filterFieldLabel}</span>
                        {!isDisabled(item) && (
                          <Icon
                            type={"delete-filling"}
                            style={{ color: "#CCCCCC" }}
                            onClick={() =>
                              !item.isDisabled && checkField(false, item.filterFieldKey)
                            }
                          />
                        )}
                      </>
                    }
                    hasFeedback
                    required={item.required}
                  >
                    {switchItem(item)}
                  </FormItem>
                );
              })}
          </Form>
        </Col>
      </Row>
      <Dialog
        title="数据诊断"
        type="confirm"
        onClose={() => setAnalysisDialog(false)}
        onCancel={() => setAnalysisDialog(false)}
        visible={analysisDialog}
        height="200px"
        footerActions={["ok"]}
        footer={
          <Button
            type="primary"
            loading={analysisLoading}
            onClick={async () => {
              setAnalysisLoading(true);
              try {
                const res = (await api.getTempPoolId()) || {};
                if (res.success && res.data) {
                  // 打开多维分析 FBI页面
                  const { requestEffectRules } = props;
                  let req = {
                    tempPoolId: res.data,
                    sourcePoolId,
                    sceneBaseId,
                    orderDirection: "desc",
                    orderBy: "d7ValidOrderCnt",
                    effectFilterFieldBizModelList: requestEffectRules,
                  };
                  const resp = await api.submitAnalyseTask(req);
                  setAnalysisLoading(false);
                  if (resp.success) {
                    // 获取链接，跳转
                    setAnalysisDialog(false)
                    window.open(resp?.data?.fbiUrl);
                  } else {
                    api.onRequestError({ msg: resp.errMessage });
                  }
                } else {
                  setAnalysisLoading(false);
                  api.onRequestError({ msg: res.errMessage });
                }
              } catch (e) {
                setAnalysisLoading(false);
                api.onRequestError(e || "请求异常，请稍后重试");
              }
            }}
          >
            确定
          </Button>
        }
      >
        <div style={{ height: 200, width: 400 }}>
          查看最新数据请刷新FBI看板。数据分析量较大，请耐心等待数据展示
        </div>
      </Dialog>

      <div className="btn-panel">
        <Button onClick={() => noPreview()}>不预览，下一步</Button>
        <Button type="primary" onClick={() => previewSample()}>
          预览
        </Button>
        <Button type="primary" onClick={() => onAnalysisData()}>
          数据诊断
        </Button>
      </div>
    </div>
  );
}
