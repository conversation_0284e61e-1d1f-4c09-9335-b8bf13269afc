import React, { useEffect, useState} from 'react';
import {Button, Dialog, Grid,} from '@alife/next';
import {config} from '@/config';
import './style.scss';
import { poolEum} from "@/home/<USER>/common";
import * as api from "@/utils/api";
import * as requestPool from "@/adator/api";
import {ACLAccess} from "@/components/ACLAccess";
import SelectMethod from "@/home/<USER>/poolList/selectMethod";
const {Row, Col} = Grid;

const DEFAULT_Label_IMG = require('../../../images/icon-label.png');
const DEFAULT_Intelligent_IMG = require('../../../images/icon-intelligent.png');
const DEFAULT_Upload_IMG = require('../../../images/icon-upload.png');


export default function Create(props) {
  const methods = [
    {
      key:'tagPool',
      title: '标签选品',
      tips: '根据特征规则选品，圈选数量',
      num:'最大1000万',
      link: '/pool/list/tagPool',
      iconClass: 'icon-label',
      src: DEFAULT_Label_IMG
    },
    {
      key:'intelligentPool',
      title: '智能选品',
      tips: '根据算法智能选品，圈选数量',
      num:'最大100万',
      link: '/pool/list/intelligentPool',
      iconClass: 'icon-intelligent',
      src: DEFAULT_Intelligent_IMG
    },
    {
      key:'uploadPool',
      title: '文件上传',
      tips: '通过文件上传批量导入商品，数量',
      num:'上限10万',
      link: '/pool/list/fileUploadPage',
      iconClass: 'icon-upload',
      src: DEFAULT_Upload_IMG
    },
  ]

  const currentWay = 0;
  const wayItem = methods[currentWay];
  const [sourcePoolId, setSourcePoolId] = useState('');  //选中的底池的sourcePoolId
  const [sourcePoolName, setSourcePoolName] = useState(''); //选中的底池的sourcePoolName
  const [scenarioPoolId, setScenarioPoolId] = useState('');  //选中的场景池id
  const [poolEumList, setPoolEumList] = useState([]) //选品选店集列表
  const [sceneGroup,setSceneGroup] = useState(null);

  let {type,createVisible} = props;

  /*获取池子列表*/
  const getBasePoolList = async () => {
    try {
      let request = requestPool.getBasePoolList;
      let resp = await request();
      if(resp.data.data.data.length > 0){
        setPoolEumList(resp.data.data.data)
      }
    } catch (error) {
      api.onRequestError(error)
    }
  }

  /*获取新池子列表*/
  // const fetchSceneExpertPoolList = async (_type) => {
  //   return ;
  // };

  useEffect(()=>{
    // 请求选品集数据
    getBasePoolList()
  },[])

  useEffect(()=>{
    // if (sceneGroup !== null) {
    //   fetchSceneExpertPoolList(type,false,sceneGroup)
    // }
  },[sceneGroup])

  /*修改圈选方式二次弹窗*/
  const confirmChange = (callback) => {
    if (sourcePoolId !== '') {
      Dialog.show({
        title: "修改圈选方式",
        content: '修改圈选方式，已设置的规则内容不保存。',
        okProps: {children: '确定'},
        cancelProps: {children: '取消'},
        footerActions: ['cancel', 'ok'],
        onOk: () => callback(),
        onCancel: () => {
          return;
        }
      });
    } else {
      callback();
    }
  }

  const changeType = (_type) => {
    // 请求场景分类
    // fetchScenario(type)

    // fetchSceneExpertPoolList(_type)

    confirmChange(function (){
      props.setType(_type);
    });
  }

  // 通过sceneId查sceneInfo
  const getSceneInfoById = async(id, _type)=>{
    try {
      let request = requestPool.getScenePoolDetailInfo;
      let resp = await request({sceneBaseId:id,poolEntryType: _type});
      return resp.data.data.data
    } catch (error) {
      api.onRequestError(error)
    }
  }

  const initCreate = async () =>{
    let {sourcePoolId: _sourcePoolId, sceneBaseId} = props;
    if(sourcePoolId && !sceneBaseId){
      setSourcePoolId(_sourcePoolId);
      if (poolEumList.length > 0) {
        let item = poolEumList.filter((v) => +v.basePoolId === +_sourcePoolId) && poolEumList.filter((v) => +v.basePoolId === +_sourcePoolId)[0]
        setSourcePoolName(item.basePoolName);
        props.setType(item.poolEntryType);
      }else{
        let item = poolEum.filter((v) => +v.id === +_sourcePoolId) && poolEum.filter((v) => +v.id === +_sourcePoolId)[0];
        setSourcePoolName(item.title);
        props.setType(item.type);
      }
    }
    if (props.isEdit || props.isCopy) {
      setScenarioPoolId(props.sceneBaseId)
      if (props.sceneBaseId) {
        let newDetailInfo = await getSceneInfoById(props.sceneBaseId,type)
        props.setSceneDetail(newDetailInfo)
      }
    }
  }

  useEffect(() => {
    initCreate();
  }, [props.wayItem,props.createVisible,props.sourcePoolId,props.sceneBaseId])

  const judgeAccess = async(permissionType,value,callback) =>{
    let resp = await api.validateComponentsPermission({"permissionType":permissionType,"permissionValue":value}).then(api.onRequestSuccess);
    let {rediectUrl: _rediectUrl } = resp;
    if(_rediectUrl){
      Dialog.confirm({
        title: '申请权限',
        footer: false,
        content: [<ACLAccess rediectUrl={_rediectUrl}/>],
      })
    }else{
      callback(value);
    }
  }

  const setPool = (item) => {
    confirmChange(() => {
      if (item.id + '' !== '41001') {
        setSourcePoolId(item.id);
        setSourcePoolName(item.title);
        props.getSourcePoolId(item.id);
        props.setCreateVisible(false);
      } else {
        judgeAccess('ACTION_POOL_CREATE', 'tenbillion', function (value) {
          console.log(value);
          setSourcePoolId(item.id);
          setSourcePoolName(item.title);
          props.getSourcePoolId(item.id);
          props.setCreateVisible(false);
        });
      }
      // 切换池子先清空规则和场景池名称
      setScenarioPoolId('')
      props.setSceneName('')
      props.getSceneBaseId('')
      props.setSceneDetail({})
      props.getEffectRules([],[])
      props.changeEffectRules([])
    })
  }

  const setTitleVisible = () =>{
    if(props.isEdit || props.isCopy) return;
    props.setCreateVisible(true)
    // if (props.sceneDetail.sceneGroup) {
      setSceneGroup(props.sceneDetail.sceneGroup)
    // }else{
    //   fetchSceneExpertPoolList(type,false,null)
    // }
  }

  // let poolEumCurrent = poolEum.filter((o) => (o.type == type && o.id != '24001')); //普通池-营销池下线
  const BaseUrl = ()=>{
    let url = ''
    switch (window.configEnv) {
      case 'daily':
        url = config.FE_APP_ROOT + '#/pool/expertPoolDetail/'
        break;
      case 'ppe':
        url = window.origin +'/selection#/pool/expertPoolDetail/'
        break;
      case 'prod':
        url = window.origin +'/#/pool/expertPoolDetail/'
        break;
      default:
        url = window.origin +'/#/pool/expertPoolDetail/'
        break;
    }
    return url
  }
  return (
    <div className="pool-create">
      {createVisible && <><h3 onClick={()=>props.setCreateVisible(false)}>1. 选择圈选方式 {sourcePoolId && <Button text className='arrow-fold'>收起</Button>}</h3>
      <SelectMethod history={props.history} current={currentWay} poolType={'1'} />
      <div className="section-first">
        <div className="select-way">
          {wayItem && <h3 className="header">更新方式</h3>}
          {wayItem && <Row>
            <Col span={7} className={`${+type === 1 ? 'active' : ''}`} onClick={() => changeType(1)}>
              <h3>按天更新</h3>
              <p>标签按天更新，选品集更新时间为<span className="count">T+1</span></p>
              <p>圈选数量<span className="count">最大1000万</span></p>
            </Col>
            <Col span={7} className={`${+type === 2 ? 'active' : ''}`} onClick={() => changeType(2)}>
              <h3>营销信息实时更新</h3>
              <p>标签和选品集<span className="count">营销信息实时更新</span></p>
              <p>圈选数量<span className="count">最大20万</span></p>
            </Col>
          </Row>}
          {type !== "" && <h3 className="header">选择底池</h3>}
          {type !== "" && <Row wrap>
            {poolEumList.map((item)=>{
              if ( +type === +item.poolEntryType && +item.basePoolType === 1) {
                return <Col span={5} onClick={() => setPool({id:item.basePoolId,title:item.basePoolName})} className={`${ (+item.basePoolId === +sourcePoolId && !scenarioPoolId) ? 'active' : ''}`}>
                  <h3>{item.basePoolName}</h3>
                  <p>{item.basePoolDesc}</p>
                </Col>
              }
            })}
          </Row>}
          {/* scenarioShow && type != undefined && <div className='newScene'>
            <h3 className="header">场景池</h3>
            <Form.Item label="营销场景：" {...formItemLayout} style={{marginLeft:'-30px'}}>
              <CascaderSelect
                hasClear
                showSearch
                notFoundContent="未找到营销场景"
                name="sceneGroup"
                defaultValue={""}
                dataSource={props.sceneClassName}
                style={{ width: "100%" }}
                onChange={(e) => {
                  if (!e) fetchSceneExpertPoolList(type,false,e)
                  setSceneGroup(e)
                }}
                value={sceneGroup}
              />
            </Form.Item>
            <div className='newSceneList'>
            <Row wrap>
              {scenarioPoolList2.length > 0 ? scenarioPoolList2.map((o)=>{
                o.title = o.basePoolName;o.id = o.basePoolId;o.tips = o.basePoolDesc;
                return <Col span={5} onClick={(e) => setScenario(e,o,type)} className={`${ o.sceneBaseId == scenarioPoolId ? 'active' : ''}`} >
                  <h3>{o.title}</h3>
                  <p className='tips'>{o.tips}</p>
                  <p className='catPath'>{o.catPath}</p>
                  <p id='detail'>场景定义：<div onClick={(e)=>{e.stopPropagation();window.open(`${BaseUrl()}${o.sceneBaseId}/${type}`)}}>详情&gt;&gt;</div></p>
                </Col>
              }):"当前场景分类暂无数据，请尝试切换营销场景～。"}  
            </Row>
            </div>
            {hasMore && scenarioPoolList2.length > 0 && <Loading tip="加载中..." visible={isLoading} className="btn-more" size='medium'>
              <div onClick={()=>fetchSceneExpertPoolList(type,true,sceneGroup)}>加载更多</div>
            </Loading>}
          </div> */}
        </div>
      </div></>}
      {!createVisible && <h3 onClick={()=>setTitleVisible()}>1. 已选：{wayItem.title} / {+props.type === 1 ? '按天更新' : '实时更新'} / {props.sceneName?props.sceneName:sourcePoolName} {(!props.isEdit && !props.isCopy) && <Button text className='arrow-fold'>展开</Button>}{props.sceneName?<Button text className='detail'><span className="next-btn-helper" onClick={(e)=>{e.stopPropagation();window.open(`${BaseUrl()}${scenarioPoolId}/${type}`)}}>专家池详情&gt;&gt;</span></Button>:""}</h3>}
    </div>
  )
}

