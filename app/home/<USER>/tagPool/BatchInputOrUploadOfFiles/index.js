import "./style.scss";

import React, { useCallback, useState } from "react";
import { Dialog, Icon, Input, Message, Upload } from "@alife/next";
import { config } from "@/config";

const Hint = "支持扩展名：.xlsx，.xls";
const Accept =
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel";

const BatchInputOrUploadOfFiles = ({
  disabled,
  value,
  filterFieldLabel, // 名称，用于切换的时候提示
  onChange,
  placeholderName,
}) => {
  // 编辑类型
  const [filesData, setFilesData] = useState(
    value && value.value && value.type === "upload"
      ? [
          {
            uid: value.value.url,
            name: value.value.fileName,
            state: "done",
            url: value.value.url,
          },
        ]
      : undefined
  );

  const onEditTypeChange = useCallback(() => {
    if (value && value.value) {
      Dialog.show({
        title: "切换编辑类型",
        content: `切换编辑类型会导致 ${filterFieldLabel} 数据丢失，是否继续切换？`,
        onOk: () => {
          onChange(
            value.type === "input" ? { type: "upload" } : { type: "input" }
          );
          if (value.type === "upload") {
            setFilesData(undefined);
          }
        },
      });
    } else {
      onChange(value.type === "input" ? { type: "upload" } : { type: "input" });
    }
  }, [value]);

  const downloadUrl = () => {
    // 下载链接与后端确认 —— 写死
    location.href =
      "https://files.alicdn.com/tpsservice/5e4bdefb6a26ef1bf13b8f47108173f7.xlsx";
  };

  const onSuccess = (info) => {
    if (Number(info.response.code) === 200) {
      setFilesData([
        {
          uid: info.response.data.url,
          name: info.response.data.fileName,
          state: "done",
          url: info.response.data.url,
        },
      ]);
      onChange({
        type: "upload",
        value: {
          fileName: info.response.data.fileName,
          url: info.response.data.url,
        },
      });
    } else {
      Message.error(info.response.msg || "上传失败");
    }
  };

  return (
    <div className="batch-input-or-upload-of-files">
      <div>
        {value && value.type === "input" ? "输入" : "上传"}{" "}
        <a
          href="javascript:void(0)"
          lassName="download-excel"
          onClick={onEditTypeChange}
        >
          切换
        </a>
      </div>
      {value && value.type === "upload" && (
        <>
          <p className="tips">
            请先下载
            <a
              href="javascript:void(0)"
              onClick={() => downloadUrl()}
              className="download-excel"
            >
              导入模版
            </a>
            ，按照要求填写完成，上传到系统；
          </p>
          <div className="upload-source">
            <Upload.Dragger
              headers={{ "X-Requested-With": null }}
              // action={`https://api-newretail-pick-admin.ele.alibaba-inc.com/api/ali/common/upload`}
              action={`${config.API_ROOT}/api/ali/common/upload`}
              onSuccess={onSuccess}
              listType="text"
              value={filesData}
              accept={Accept}
              disabled={disabled}
              onRemove={() => {
                setFilesData(undefined);
                onChange(null);
              }}
            >
              <div className="upload-text">
                <Icon type="upload" style={{ color: "#333333" }} size={"xl"} />
                <p className="download">点击或将文件拖拽到这里上传</p>
                <p className="hint">{Hint}</p>
              </div>
            </Upload.Dragger>
          </div>
        </>
      )}

      {value && value.type === "input" && (
        <Input.TextArea
          placeholder={placeholderName}
          disabled={disabled}
          value={value && value.value ? value.value : ""}
          onChange={(val) => {
            onChange({
              type: "input",
              value: val,
            });
          }}
        />
      )}
    </div>
  );
};
export default BatchInputOrUploadOfFiles;
