import React, { useCallback, useRef, useState } from "react";
import { CascaderSelect, Message } from "@alife/next";
import _ from "lodash";

import { onRequestError } from "@/utils/api";
import * as api from "@/adator/api";

const SelectSearchWithApi = ({
  defaultDataSource = [],
  disabled,
  maxCommodityLength, // 目前不支持限制多个，留个扣子
  filterFieldData,
  value,
  onChange,
}) => {
  const [dataSource, setDataSource] = useState([]);
  const [selectDataSource, setSelectDataSource] = useState(defaultDataSource);
  const versionRef = useRef(0);
  const onSearchRef = useRef(_.debounce((keyword, _selectDataSource) => {
    if (keyword) {
      /**
       * 解析 /api/deliverymanage/getPoolInfoSurge?poolType=2&type=1&poolId=1
       *  从 ? 隔开，?前为api，?后为参数多个参数以&隔开
       *  */
      const [url, params] = filterFieldData.split("?");
      let paramsObj = [];
      if (params && params.length > 0) {
        paramsObj = params.split("&").reduce((acc, cur) => {
          const [key, _value] = cur.split("=");
          acc[key] = _value;
          return acc;
        }, {});
      }

      const version = ++versionRef.current;

      api
        .getDynamicDataSourceWithParams(url, {
          ...paramsObj,
          searchKey: keyword,
        })
        .then((data) => {
          // 防止多个请求同时触发导致顺序错乱
          if (version !== versionRef.current) {
            return;
          }
          const _dataSource = data.map((item) => {
            let level = undefined;
            if (item.pos) {
              level = item.pos.split("-").length - 1;
            }
            return {
              ...item,
              label: item.label || item.name,
              value: item.value || item.id,
              level,
            };
          });
          setDataSource([..._selectDataSource, ..._dataSource]);
        })
        .catch((e) => {
          console.error("e=======SelectSearchWithApi onSearch", e);
          onRequestError(e);
        });
    } else {
      setDataSource([]);
    }
  }, 800));

  const onHandleChange = useCallback((_value) => {
    if (maxCommodityLength > 0 && _value.length > maxCommodityLength) {
      Message.warning(`最多${maxCommodityLength}个`);
    } else {
      if (!_value) {
        return;
      }

      // 判断 selectDataSource 中不包含当前 _value 中的值，就将当前 _value 在 dataSource 中对应的值添加到 selectDataSource 中，否则，就直接调用 onChange
      const notContain = _value.some((item) => !selectDataSource.find((_item) => _item.value === item));
      if (notContain) {
        const curSelectList = dataSource.map((item) => {
          if(_value.includes(item.value)) {
            return item
          }
          return null
        }).filter(Boolean);
        setSelectDataSource([...selectDataSource, ...curSelectList]);
      }
      // 合并dataSource 和 selectDataSource 并按照 value 去重
      const _dataSource = [...selectDataSource, ...dataSource].reduce((acc, cur) => {
        if (!acc.find((item) => item.value === cur.value)) {
          acc.push(cur);
        }
        return acc;
      }, []);
      onChange && onChange(_value, _dataSource);
    }
  }, [ maxCommodityLength, selectDataSource, dataSource ]);

  return (
    <CascaderSelect
      showSearch
      followTrigger
      multiple={true}
      disabled={disabled}
      placeholder="请选择"
      maxCommodityLength={maxCommodityLength}
      value={value}
      onChange={(_value) => onHandleChange(_value)}
      onSearch={(val) => onSearchRef.current(val, selectDataSource)}
      dataSource={dataSource}
      style={{ width: "100%" }}
    />
  );
};
export default SelectSearchWithApi;
