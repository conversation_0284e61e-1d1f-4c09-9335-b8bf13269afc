import debugFn from 'debug'
import React, { useEffect, useState} from 'react';
import { Button, Dialog, Message} from '@alife/next';
import './style.scss';
import {BreadcrumbTips} from "@/home/<USER>/comps";
import { Steps, unique} from "@/home/<USER>/common";
import Create from './create';
import TagRules from './tagRules';
import BaseInfo from './baseInfo';
import {goldLog, logTimeComponent} from "@/utils/aplus";
import { dealQualityScoreToArray } from "@/home/<USER>/common";
import {withRouter} from "react-router-dom";
import {permissionAccess} from "@/components/PermissionAccess";
import * as api from "@/adator/api";
import moment from "moment";
import {optionMap} from "@/home/<USER>/common/map";
import ViewDialog from "@/home/<USER>/tagPool/viewDialog";
import FinishPage from "@/home/<USER>/tagPool/finishPage";
import { deepCopy, fetchGroupOption } from "@/home/<USER>/common";
import { getFilterFieldPolicy } from '@/selection';

const debug = debugFn('selection:legacy:TagPool');

function TagPool({match, history}) {
  const breadcrumbList = [
    {"title": '选品集管理', link: "#/pool/list"},
    {"title": '新建商品选品集', link: ""}
  ];
  let {poolId = ''} = match.params;
  const isEdit = poolId > 0;
  const isCopy = poolId < 0;
  poolId = poolId < 0 ? -poolId : poolId;
  const [step, setStep] = useState(0);
  const [sourcePoolId, setSourcePoolId] = useState('');  //选中的底池的sourcePoolId
  const [sceneBaseId, setSceneBaseId] = useState('');  //选中的底池的sceneBaseId
  const [sceneName, setSceneName] = useState('');  //选中的底池的场景名称
  const [sceneDetail, setSceneDetail] = useState({});  //选中的专家池详情
  const [detailData, setDetailData] = useState({});
  const [effectRules, setEffectRules] = useState([]);  //编辑标签表单的effectRules
  const [requestEffectRules, setRequestEffectRules] = useState([]);  //在请求接口中流通的requestEffectRules
  const [params, setParams] = useState({});
  const [baseInfo, setBaseInfo] = useState({});
  const [tempPoolId, setTempPoolId] = useState('');
  const [submitPoolId, setSubmitPoolId] = useState('');
  const [wayItem, setWayItem] = useState('');
  const [createVisible, setCreateVisible] = useState(true);
  const [type, setType] = useState('');  //按天更新：1  ， 实时更新：2
  const  [groupOptions,setGroupOptions] =  useState({...optionMap});
  const [viewDiagVisible,setViewDiagVisible] = useState(false);
  const [user,setUser] = useState({});
  const [tagRulesShow,setTagRulesShow] = useState(true); //是否展示规则列表，防止更新池子导致标签
  const [sceneClassName, setSceneClassName] = useState([]); //场景名称数据来源
  const [sourceFilterFieldList,setSourceFilterFieldList] = useState([]);
  const [delReasonList, setDelReasonList] = useState([]); //删除原因列表
  const [loadingType, setLoadingType] = useState('');

  const getSourcePoolId = (newSourcePoolId)=>{
    // 在每次更新池子时先把规则列表卸载
    if(newSourcePoolId!=sourcePoolId){
      setTagRulesShow(false)
    }
    setSourcePoolId(newSourcePoolId);
  }

  const getSceneBaseId = (newSceneBaseId) => {
    // 在每次更新场景池子时先把规则列表卸载
    if(sceneBaseId!=newSceneBaseId){
      setTagRulesShow(false)
    }
    setSceneBaseId(newSceneBaseId);
  }

  // 每次池子更新成功时展示
  useEffect(()=>{
    setTagRulesShow(true)
  },[sourcePoolId,sceneBaseId])

  // 动态获取筛选项
  useEffect(()=>{
    (async ()=>{
      if(!sceneBaseId){
        await getRulesBySource()
      }
    })();
  },[sourcePoolId])

  // 批量请求dataType为2的筛选项并加入到groupOptions中
  useEffect(() => {
    if(sourceFilterFieldList.length > 0){
      let requestList = {};
      let newList = [...sourceFilterFieldList]
      newList.map((item) => {
        if (+item.filterFieldDataType === 1) {
          groupOptions[item.filterFieldId] = JSON.parse(item.filterFieldData);
        } else if (+item.filterFieldDataType === 2) {
          requestList[item.filterFieldId] = item.filterFieldData;
        } else {
          groupOptions[item.filterFieldId] = optionMap[item.filterFieldKey];
        }
      });
    // 批量请求底池规则和圈选规则中为动态数据源的数据
    (async () => {
      let promiseList = [];
      Object.keys(requestList).forEach(async (key) => {
        promiseList.push(fetchGroupOption(key, requestList[key]));
      });
      const resolveList = await Promise.all(promiseList);
      resolveList.map((item) => {
        Object.keys(item).forEach((keyItem) => {
          groupOptions[keyItem] = item[keyItem] || [];
        });
      });
      setGroupOptions(deepCopy(groupOptions));
    })();
    }
  }, [sourceFilterFieldList]);


  const getWayItem = (_wayItem) =>{
    setWayItem(_wayItem);
  }

  const  getPoolDetail = (empId)=>{
    try {
      api.getPoolDetailByPoolId(poolId)
        .then((resp) => {
          let {noticeUid} = resp.data;
          let newNoticeUid = noticeUid;
          if (noticeUid.indexOf(empId) >= 0) {
            const index = noticeUid.split(',').findIndex((o) => o == empId);
            const noticeUidGroup = noticeUid.split(',');
            noticeUidGroup.splice(index, 1);
            newNoticeUid = noticeUidGroup.join(',');
          }
          baseInfo.poolName = resp.data.poolName;
          baseInfo.effectAt = resp.data.effectAt;
          baseInfo.expireAt = resp.data.expireAt;
          baseInfo.isNotice = resp.data.isNotice;
          baseInfo.noticeUid = newNoticeUid;
          baseInfo.effectRange = [moment(resp.data.effectAt), moment(resp.data.expireAt)];
          baseInfo.outSynPlatforms = resp.data.outSynPlatforms;
          baseInfo.refreshMode = resp.data.refreshMode;
          baseInfo.sceneGroup = resp.data.sceneGroup;
          params.refreshMode = resp.data.refreshMode;
          params.poolName = resp.data.poolName;
          params.outSynPlatforms = resp.data.outSynPlatforms;
          params.isNotice = resp.data.isNotice;
          params.noticeUid = newNoticeUid;
          params.effectRange = [moment(baseInfo.effectAt),moment(baseInfo.expireAt)];
          setType(resp.data.poolEntryType)
          setSceneBaseId(resp.data.sceneBaseId);
          setSceneDetail({sceneBaseId:resp.data.sceneBaseId})
          setSourcePoolId(resp.data.sourcePoolId);
          setSceneName(resp.data.sceneName);
          setSubmitPoolId(resp.data.poolId);
          setDetailData(resp.data);
          // setEffectRules(resp.data.effectRules);
          setRequestEffectRules(resp.data.effectRules);
          changeEffectRules(resp.data.effectRules);
          setBaseInfo(baseInfo);
          setParams(params);
          setWayItem({ key:'tagPool',title: '标签选品'});
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  /*将requestEffectRules转化为effectRules*/
  const changeEffectRules = (_effectRules) =>{
    let newEffectRules = [];
    if (_effectRules && _effectRules.length > 0) {
      _effectRules.map((v) => {
        let value = v.filterFieldValue;
        if (v.filterFieldComponentType === 'arrayInput') {
          value = JSON.parse(v.filterFieldValue) instanceof Array ? JSON.parse(v.filterFieldValue).join(","):JSON.parse(v.filterFieldValue);
        }
        if (v.filterFieldComponentType === 'picStandard') {
          value = dealQualityScoreToArray(JSON.parse(value)).map(
            (dealQualityScoreToItem) => dealQualityScoreToItem.value
          );
        }
        let typeGroup = ['rangeNumberInput', 'checkbox']
        if (typeGroup.includes(v.filterFieldComponentType)) {
          value = JSON.parse(value);
        }
        if (v.filterFieldComponentType === 'cascaderSelect' || v.filterFieldComponentType === 'multipleSelect' ||  v.filterFieldComponentType === 'selectSearch') {
          value = JSON.parse(value).map(_v => _v.value);
        }
        if (v.filterFieldComponentType === 'batchInputOrUploadOfFiles') {
          try {
            const valueObj = JSON.parse(value) || {};
            if (valueObj.type === 'input') {
              value = {
                type: 'input',
                value: valueObj.value.join(',')
              }
            } else if (valueObj.type === 'upload') {
              value = {
                type: 'upload',
                value: valueObj.value
              }
            }
          } catch (error) {
            value = {
              type: 'input',
              value: ''
            }
          }
        }
        newEffectRules.push({
          filterFieldId: v.filterFieldId,
          filterFieldKey: v.filterFieldKey,
          filterFieldLabel: v.filterFieldLabel,
          filterFieldValue: value,
          filterFieldComponentType: v.filterFieldComponentType,
          filterFieldIdGroup: v.filterFieldIdGroup,
          operator: v.operator,
          isDisabled: v.isDisabled
        })
      })
    }
    setEffectRules(newEffectRules);
  }

  /*获取临时池子id*/
  const getTempId = () =>{
    try {
      api.getTempPoolId()
        .then((resp) => {
          setTempPoolId(resp.data);
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  const getUser = () =>{
    try {
      api.getBucUser()
        .then((resp) => {
          setUser(resp.data.data);
          if(isEdit || isCopy) {
            getPoolDetail(resp.data.data.empId);
          }
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  useEffect(() => {
    getUser();
    if(isEdit || isCopy) {
      setCreateVisible(false);
    }
    getTempId();
    (async () => {
      await fetchSceneCatTree();
      await getDelReasonList();
    })();
  }, [])

  /*获取场景分类*/
  const fetchSceneCatTree = async () => {
    try {
      if (sceneClassName.length <= 0) {
        let request = api.queryNewSceneCatTree;
        let resp = await request();
        setSceneClassName(resp.data.data.data);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };
  
  /*获取当前池子筛选项*/
  const getRulesBySource = async () =>{
    try {
      if (sourcePoolId) {
        let resp = await api.getRulesBySource(sourcePoolId);
        setSourceFilterFieldList(resp.data)
      }
      // 圈选池子相关指标
    } catch (error) {
      api.onRequestError(error);
    }
  }

  // 获取删除原因列表
  const getDelReasonList= async ()=>{
    try {
      let request = api.getDelReasonList;
      let resp = await request();
      setDelReasonList(resp)
    } catch (error) {
      api.onRequestError(error);
    }
  }

  /*步骤的切换，1：上一步，2：下一步*/
  const goStep = (stepType) => {
    setStep((stepType == 1) ? (step - 1) : (1 + step));
  }

  const getEffectRules = (_effectRules, _requestEffectRules) => {
    // 一些业务定制化组件，不能简单通过 value 是否为空进行过滤
    function manipulateEffectRule(r, isRequestData) {
      let ret = r;
      if (ret.filterFieldComponentType === "mktConditions") {
        if (isRequestData) {
          if (ret.filterFieldValue) {
            try {
              let items = JSON.parse(ret.filterFieldValue);
              items = items.filter((item) => !!item.value);
              ret = {
                ...ret,
                filterFieldValue: items.length
                  ? JSON.stringify(items)
                  : undefined,
              };
            } catch (e) {
              console.error(e);
            }
          }
        } else {
          if (Array.isArray(ret.filterFieldValue)) {
            ret = {
              ...ret,
              filterFieldValue: ret.filterFieldValue.filter((item) => {
                return !!item.value;
              }),
            };
            if (!ret.filterFieldValue?.length) {
              ret.filterFieldValue = undefined;
            }
          } else {
            // nothing to do
          }
        }
      } else {
        const policy = getFilterFieldPolicy(ret.filterFieldComponentType);
        if (policy) {
          ret = policy.manipulateEffectRule(r, isRequestData);
        }
      }

      return ret;
    }
    const nextEffectRules = _effectRules
      .map((r) => manipulateEffectRule(r, false))
      .filter((v) => !!v.filterFieldValue);
    const nextRequestEffectRules = _requestEffectRules
      .map((r) => manipulateEffectRule(r, true))
      .filter((v) => !!v.filterFieldValue);

    debug("getEffectRules", { _effectRules, nextEffectRules });
    debug("getEffectRules", { _requestEffectRules, nextRequestEffectRules });
    setEffectRules(nextEffectRules);
    setRequestEffectRules(nextRequestEffectRules);
  }

  // 禁用专家池带过来的筛选项
  useEffect(()=>{
    const { effectFilterFieldList = [] } = sceneDetail;
    if (effectFilterFieldList.length > 0) {
        let newData = deepCopy(effectRules)
        newData = newData.map((item)=>{
          let resultItem = item
          effectFilterFieldList.map((eItem)=>{
            if (item.filterFieldId === eItem.filterFieldId) {
              resultItem.isDisabled = true
            }
          })
          return resultItem
       })
       setEffectRules(newData)
    }
  },[sceneDetail.effectFilterFieldList])

  /*更新基本信息*/
  const updateBaseInfo = (name,value) =>{
    baseInfo[name] = value;
    params[name] = value;
    setParams(params);
    setBaseInfo(baseInfo);
  }

  const ctrlReq = () =>{
    const _type = 1
    let poolResultLimit = (_type == 1) ? '1000000' : '100000';
    let {noticeUid = ''} = params;
    let noticeUidGroup = noticeUid ? noticeUid.split(",") : [];
    if(user.empId) {
      noticeUidGroup.push(user.empId);
    }
    noticeUid = unique(noticeUidGroup).join(",");
    return {
      sourcePoolId,
      sceneBaseId,
      poolType: 1,
      poolName: params.poolName,
      effectAt: params.effectRange[0].valueOf(),
      expireAt: params.effectRange[1].valueOf(),
      isNotice: params.isNotice || 0,
      sceneGroup: sceneDetail.sceneGroup || params.sceneGroup || '',
      noticeUid,
      effectRules: requestEffectRules ,
      poolStoreType: '',
      tempPoolId,
      poolResultLimit,
      refreshMode: params.refreshMode,
      outSynPlatforms: params.outSynPlatforms
    };
  }

  const checkParamsBeforeSaveOrSubmit = async (cb, _loadingType) => {
    let createPoolV2Req = ctrlReq();
    if (loadingType) {
      Message.warning('正在处理中，请勿重复提交');
      return;
    }
    setLoadingType(_loadingType)
    try {
      const checkRes = await api.checkpublish(createPoolV2Req);
      setLoadingType('');
      // result 0 校验通过，1 需要二次确认， 2不允许创建
      if (checkRes.data) {
        if (checkRes.data.result === 1) {
          const errorList = checkRes.data.warnInfoList || [];
          const checkDialog = Dialog.show({
            title: "当前池子存在以下问题，请谨慎创建",
            content: (
              <div>
                {errorList.map((errorItem) => (
                  <div>{errorItem}</div>
                ))}
                如有问题，请进群提工单：44552100
              </div>
            ),
            footerActions: ["ok", "cancel"],
            footer: [
              <Button
                type="primary"
                onClick={() => {
                  cb && cb();
                  checkDialog.hide();
                }}
              >
                继续创建
              </Button>,
              <Button style={{ marginLeft: 8 }} onClick={() => {
                checkDialog.hide();
              }}>返回修改</Button>,
            ],
          });
        } else if (checkRes.data.result === 2) {
          const errorList = checkRes.data.warnInfoList || [];
          Dialog.show({
            title: "当前池子不允许创建",
            footer: false,
            content: (
              <div style={{ minWidth: 500 }}>
                {errorList.map((errorItem) => (
                  <div>{errorItem}</div>
                ))}
                如有问题，请进群提工单：44552100
              </div>
            ),
          });
        } else {
          cb && cb();
        }
      }
    } catch (error) {
      setLoadingType('');
      api.onRequestError(error);
    }
  };

  const savePool = async () => {
    let createPoolV2Req = ctrlReq();
    let request = api.createPool;
    if (isEdit && !isCopy) {
      createPoolV2Req.poolId = submitPoolId;
      request = api.updatePool;
    }
    let resp = await request(createPoolV2Req);
    if (resp.data.code!='200') {
      Message.warning(resp.data.msg);
    } else {
      Message.success('操作成功');
      history.push("/pool/list");
    }
  }

  const submitPool = async () => {
    let createPoolV2Req = ctrlReq();
    let request = api.createPool;
    if (isEdit && !isCopy) {
      createPoolV2Req.poolId = submitPoolId;
      request = api.updatePool;
    }
    let resp = await request(createPoolV2Req);
    if (resp.data.code=='200' ) {
      let id = resp.data.data.data;
      if (id) {
        let result = await api.publishPool(id);
        Message.success('发布成功');
        setSubmitPoolId(id);
        goStep(2);
      }
    }else{
      Message.warning(resp.data.msg);
    }
  }

  const getGroupOptions = (_groupOptions) =>{
    setGroupOptions(_groupOptions);
  }

  let commonProps = {
    isEdit,
    isCopy,
    sourcePoolId
  }
  return (
    <div className="tag-pool">
      <BreadcrumbTips list={breadcrumbList} />
      <Steps current={step} middleText={"基本信息"} />
      {+step === 0 && (
        <Create
          history={history}
          {...commonProps}
          getSourcePoolId={getSourcePoolId}
          sceneBaseId={sceneBaseId}
          getSceneBaseId={getSceneBaseId}
          sceneName={sceneName}
          setSceneName={setSceneName}
          sceneClassName={sceneClassName}
          getWayItem={getWayItem}
          getEffectRules={getEffectRules}
          changeEffectRules={changeEffectRules}
          sceneDetail={sceneDetail}
          setSceneDetail={setSceneDetail}
          wayItem={wayItem}
          setCreateVisible={setCreateVisible}
          createVisible={createVisible}
          type={type}
          setType={setType}
        />
      )}
      {+step === 0 &&
      sourcePoolId &&
      tagRulesShow &&
      sourceFilterFieldList?.length ? (
        <TagRules
          {...commonProps}
          getEffectRules={getEffectRules}
          changeEffectRules={changeEffectRules}
          sourceFilterFieldList={sourceFilterFieldList}
          goStep={goStep}
          getGroupOptions={getGroupOptions}
          groupOptions={groupOptions}
          sceneDetail={sceneDetail}
          requestEffectRules={requestEffectRules}
          setViewDiagVisible={setViewDiagVisible}
          effectRules={effectRules}
          detailData={detailData}
        />
      ) : null}
      {+step === 1 && (
        <BaseInfo
          {...commonProps}
          loadingType={loadingType}
          sceneNewGroup={sceneDetail.sceneGroup}
          baseInfo={baseInfo}
          goStep={goStep}
          updateBaseInfo={updateBaseInfo}
          savePool={() => checkParamsBeforeSaveOrSubmit(savePool, "save")}
          submitPool={() => checkParamsBeforeSaveOrSubmit(submitPool, "submit")}
          sceneBaseId={sceneBaseId}
          updateType={type}
          sceneClassName={sceneClassName}
        />
      )}
      {+step === 2 && <FinishPage history={history} poolId={submitPoolId} />}
      {tempPoolId && viewDiagVisible && (
        <ViewDialog
          {...commonProps}
          effectRules={effectRules}
          goStep={goStep}
          delReasonList={delReasonList}
          setViewDiagVisible={setViewDiagVisible}
          requestEffectRules={requestEffectRules}
          visible={viewDiagVisible}
          tempPoolId={tempPoolId}
          sceneBaseId={sceneBaseId}
        />
      )}
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(withRouter(TagPool), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const tagPoolPage = permissionAccess(LogTimePutInPage)