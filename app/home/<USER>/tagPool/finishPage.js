import React, { useEffect, useState} from 'react';
import { CascaderSelect as BaseCascaderSelect, Icon, Button} from '@alife/next';
import './style.scss';
/*
 *完成页面 （公用组件）
*/

export default function FinishPage(props) {
  const {poolId = ''} = props;

  const [timer, setTimer] = useState('');

  useEffect(() => {
    setTimer(setTimeout(()=>{
      props.history.push("/pool/list");
    },5000));

  }, [])


  const seekDetail = () =>{
    timer && clearTimeout(timer);
    props.history.push(`/pool/list/detail/${poolId}`);
  }

  const backList = () =>{
    timer && clearTimeout(timer);
    props.history.push("/pool/list");
  }


  return (
    <div className="complete-page">
      <div className="success-info">
        <Icon type="success" style={{ color: "#1DC11D", marginRight: "10px" }} />
        已提交
      </div>
      <div className="success-tips">5s后自动跳转</div>
      <div className="success-btns">
        <Button onClick={seekDetail}>查看详情</Button>
        <Button onClick={backList}>返回列表</Button>
      </div>
    </div>
  )
}

