.tag-pool {
  .step-wraper {
    background-color: #fff;
    margin: 12px;
    padding: 20px 0 5px;
  }
  .pool-create {
    background-color: #fff;
    margin: 12px;
    padding: 20px;
    & > h3 {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      font-weight: normal;
      margin: 0;
      padding: 0;
      cursor: pointer;
      & > .next-btn {
        cursor: pointer;
      }
    }

    .arrow-fold {
      margin-left: 12px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
    }
    .detail {
      margin-left: 12px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      span {
        color: #4988fd;
        &:hover {
          text-decoration: underline;
        }
      }
    }
    .selection-method {
      width: 480px;
      margin: 14px 0 0 24px;
      .next-col {
        margin-right: 12px;
        border: 1px solid #ebebeb;
        background-color: #ffffff;
        border-radius: 4px;
        text-align: center;
        cursor: pointer;
        width: 50%;
        .icon {
          display: inline-block;
          margin-top: 20px;
          &.icon-upload {
            height: 36px;
            background-size: 100% 100%;
          }
          &.icon-intelligent {
            height: 36px;
            background-size: 100% 100%;
          }
          &.icon-label {
            height: 35px;
            background-size: 100% 100%;
          }
        }
        .title {
          font-family: PingFangSC-Medium;
          font-size: 16px;
          color: #333333;
          margin-top: 0;
          padding: 0;
          margin-bottom: 4px;
        }
        .tips {
          font-family: PingFangSC-Regular !important;
          font-size: 12px;
          color: #999999;
          width: 120px;
          margin: 0 auto;
          margin-bottom: 12px;
          .count {
            color: #4988fd;
          }
        }
        &:hover,
        &.active {
          background: rgba(255, 112, 0, 0.04);
          border: 1px solid rgba(255, 112, 0, 0.4);
        }
      }
    }
  }

  &.next {
    margin-bottom: 50px;
  }
  .c-new-page-wrapper__content {
    padding: 24px 0;
  }
  .next-btn {
    &.cur {
      color: red;
      border-color: red;
    }
  }

  .header {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #666666;
    padding: 13px 0 0 0px;
    font-weight: 500;
    & > span {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
      margin-left: 8px;
    }
  }

  .subHeader {
    font-family: PingFangSC-Medium;
    font-size: 13px;
    color: #666666;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .headItem {
    border: 0;
    .next-tabs-tab-inner {
      border: 0;
    }
  }

  .next-step-panel {
    height: 68px;
    width: calc(100vw - 240px);
    background: #fff;
    position: fixed;
    bottom: 10px;
    overflow: hidden;
    z-index: 10;
    box-shadow: 0 -1px 10px 0 rgba(0, 0, 0, 0.06);
    &.step0 {
      padding-left: calc(100vw - 340px);
    }
    &.step1 {
      padding-left: calc(100vw - 560px);
    }

    & > .next-btn {
      margin: 20px 8px 0 0;
    }
  }

  .order-num {
    width: 16px;
    height: 16px;
    line-height: 14px;
    text-align: center;
    display: inline-block;
    font-family: AlibabaSans102-Bold;
    font-size: 14px;
    font-style: normal;
    font-weight: normal;
    color: #fff;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #ff7000;

    &.third {
      border: 1px solid #ebebeb;
      color: #999;
      background-color: #fff;
    }
    &.current {
      border: 1px solid #ff7000;
      background-color: #ffffff;
      color: #ff7000;
    }
  }
  .order-result {
    font-family: PingFangSC-Regular !important;
    font-size: 16px !important;
    color: #333333 !important;
  }

  .section-first,
  .section-second {
    padding-left: 24px;
    .total-info {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      margin-bottom: 24px;
      & > span {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
        margin-left: 8px;
      }
    }
    .next-form {
      width: 100%;
      margin-top: 20px;
    }
    & > .next-btn {
      margin: 16px 24px 24px 24px;
    }
  }
  .section-second {
    padding-left: 24px;
  }

  .select-way {
    & > p {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #666666;
      margin-left: 48px;
    }

    & > .next-row {
      margin: 8px 0 0 0px;

      & > .next-col{
        border-radius: 4px;
        padding: 13px 10px 10px 10px;
        border: 1px solid #ebebeb;
        background-color: #ffffff;
        margin-right: 12px;
        cursor: pointer;
        margin-bottom: 10px;
        &:hover,
        &.active {
          background: rgba(255, 112, 0, 0.04);
          border: 1px solid rgba(255, 112, 0, 0.4);
        }

        & > h3 {
          font-family: PingFangSC-Medium;
          font-size: 16px;
          color: #333333;
          margin-bottom: 5px;
        }

        & > p {
          font-family: PingFangSC-Regular !important;
          font-size: 12px;
          color: #999999;
          padding: 0;
          margin: 0 0 4px 0;
          & > .count {
            color: #4988fd;
          }
        }
      }
    }
    .next-tabs-content {
      .next-row {
        margin: 8px 0 0 0px;

        & > .next-col {
          border-radius: 4px;
          padding: 13px 13px 10px 20px;
          border: 1px solid #ebebeb;
          background-color: #ffffff;
          margin-right: 12px;
          cursor: pointer;
          margin-bottom: 10px;
          &:hover,
          &.active {
            background: rgba(255, 112, 0, 0.04);
            border: 1px solid rgba(255, 112, 0, 0.4);
          }

          & > h3 {
            font-family: PingFangSC-Medium;
            font-size: 16px;
            color: #333333;
            margin-bottom: 5px;
          }

          & > p {
            font-family: PingFangSC-Regular !important;
            font-size: 12px;
            color: #999999;
            padding: 0;
            margin: 0 0 4px 0;
            & > .count {
              color: #4988fd;
            }
          }
        }
      }
      .tips {
        display: -webkit-box !important;
        flex-wrap: wrap;
        word-break: break-all;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        overflow: hidden;
        text-overflow: -o-ellipsis-lastline;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      #detail {
        display: flex;
        div {
          color: #4988fd;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    .newSceneList {
      .next-row {
        margin: 8px 0 0 0px;

        & > .next-col {
          border-radius: 4px;
          padding: 13px 10px 10px 10px;
          border: 1px solid #ebebeb;
          background-color: #ffffff;
          margin-right: 12px;
          cursor: pointer;
          margin-bottom: 10px;
          &:hover,
          &.active {
            background: rgba(255, 112, 0, 0.04);
            border: 1px solid rgba(255, 112, 0, 0.4);
          }

          & > h3 {
            font-family: PingFangSC-Medium;
            font-size: 16px;
            color: #333333;
            margin-bottom: 5px;
            display: -webkit-box !important;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          & > p {
            font-family: PingFangSC-Regular !important;
            font-size: 12px;
            color: #999999;
            padding: 0;
            margin: 0 0 4px 0;
            & > .count {
              color: #4988fd;
            }
          }
        }
      }
      .tips {
        display: -webkit-box !important;
        flex-wrap: wrap;
        word-break: break-all;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        overflow: hidden;
        text-overflow: -o-ellipsis-lastline;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        height: 44px;
        max-width: 280px;
      }
      .catPath{
        display: -webkit-box !important;
        flex-wrap: wrap;
        word-break: break-all;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        overflow: hidden;
        text-overflow: -o-ellipsis-lastline;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      #detail {
        display: flex;
        div {
          color: #4988fd;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .label-rules {
    background-color: #fff;
    margin: 12px;
    padding: 20px;
    & > h3 {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      font-weight: normal;
      margin: 0;
      padding: 0;
    }
    .search-panel {
      margin: 16px 0 0 24px;
      label {
        font-family: PingFangSC-Regular !important;
        font-size: 14px;
        color: #333333;
        font-weight: normal;
        margin-right: 9px;
      }
      .next-input {
        width: 278px;
        margin-right: 13px;
      }
    }
    .tab-content-group {
      .next-col {
        &.left {
          padding-left: 14px;
        }
        &.right {
          background: #fafafa;
          margin: 12px 24px 0 10px;
          padding: 0 10px 10px 10px;
          & > .right-title {
            margin-bottom: 16px;
            span {
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #9e9e9e;
              float: right;
              .next-icon {
                margin-right: 5px;
              }
            }
          }
        }
      }
      .next-tabs {
        margin: 13px 0 0 0px;

        .next-tabs-bar {
          border: 1px solid #ebebeb;
          border-bottom: 0;
        }
      }
      .check-list {
        border: 1px solid #ebebeb;
        padding: 8px 20px 24px 20px;
        p {
          padding: 0;
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #999999;
          margin: 7px 0 10px 0;
        }
        .next-checkbox-wrapper {
          margin-left: 12px !important;
          margin-bottom: 10px;
          display: inline-block;
          .next-checkbox-label {
            width: 150px;
            display: inline-block;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            line-height: 14px;
            color: #333333;
          }
        }
      }
    }
    .rules-form {
      .next-form-item {
        padding: 2px 10px 7px 10px;
        background-color: #fff;
        border-radius: 4px;
        margin-bottom: 8px;
        .next-form-item-label {
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #333333;
          padding-right: 0;
          width: 100%;
          text-align: left;
          & > label {
            .next-icon {
              float: right;
              margin-top: 5px;
            }
          }
        }
        .next-form-item-control span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #666666;
        }
      }

      .next-input {
        width: 100%;
      }
      .btn-panel {
        margin-top: 12px;
        overflow: hidden;
        .next-btn {
          float: right;
          margin-left: 8px;
        }
      }
    }
  }

  .base-info {
    background-color: #fff;
    padding: 20px;
    .date_2,
    .date_3,
    .date_4 {
      padding: 0 5px !important;
    }
    .time-limit {
      width: 408px;

      & > span {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
      }
      & > label {
        float: right;
      }
      .next-checkbox-wrapper {
        margin-right: 7px;
      }
    }
    .radio-list {
      overflow: hidden;
      margin-top: 0px;
      display: flex;
      flex-wrap: wrap;
      .radio-item {
        background: #ffffff;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        width: 168px;
        padding: 6px 16px;
        margin-top: 10px;
        color: #ff7000;
        float: left;
        margin-right: 8px;
        position: relative;
        cursor: pointer;
        &.current {
          border: 1px solid #ff7000;
        }
        &.active {
          border: 1px solid #ff7000;
          & > .label,
          & > .desc {
            color: #ff7000;
          }
        }
        & > div {
          &.label {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #333;
          }
          &.desc {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #666;
          }
        }
        .length {
          position: absolute;
          right: -1px;
          top: -1px;
          display: inline-block;
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          border-radius: 2px;
          background-color: #ff7000;
          font-family: AlibabaSans102-Bold;
          font-size: 14px;
          color: #ffffff;
        }
        .next-checkbox-wrapper {
          position: absolute;
          right: -1px;
          top: -9px;
        }
      }
    }
    .radio-child {
      background: #ffffff;
      border: 1px solid #ebebeb;
      border-radius: 4px;
      width: 900px;
      flex: 1;
      margin-top: 12px;
      & > .next-col:first-child {
        padding: 6px 0px 0;
        border-right: 1px solid #ebebeb;
        .child {
          padding: 12px 16px 0;
          overflow: hidden;
          &:last-child {
            margin-bottom: 10px;
          }
          cursor: pointer;
          &.active {
            background: #f5f5f5;
          }
          & > .next-checkbox-wrapper {
            float: left;
            .next-checkbox {
              vertical-align: top;
            }
          }

          & > .text-panel {
            margin-left: 25px;
            position: relative;
            top: -4px;

            & > label,
            & > span {
              display: inline-block;
            }

            & > span {
              font-family: PingFangSC-Regular;
              font-size: 12px;
              color: #999999;
              line-height: 18px;
            }
          }
        }
      }
    }
    .blue-text{
      display: inline-block;
      margin-left: 10px;
      color: #4988fd;
      cursor: pointer;
    }
  }

  .btn-panel {
    text-align: center;
    margin-top: 20px;
    .next-btn {
      margin-right: 8px;
    }
  }
}

.view-dialog {
  margin: 20px 30px 20px 0;
  .delele-title {
    & > .next-icon {
      position: relative;
      top: 1px;
    }
    .btn-back {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      margin-bottom: 0;
      margin-left: 5px;
    }
    & > span {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #333333;
      margin-left: 10px;
      position: relative;
      top: 1px;
    }
  }
  .footer-buttons {
    overflow: hidden;
    width: 100%;
    .btn-delete-list {
      float: left;
    }
    .btn-cancel {
      margin-right: 8px;
    }
  }
  &.supply {
    background-color: #fff;
    padding: 24px 0;
    margin: 10px 0 0 0;
  }

  .filter-panel {
    overflow: hidden;
    .batch-panel {
      display: inline-block;

      margin-bottom: 8px;
      .next-icon {
        margin-right: 8px;
      }
      & > span {
        margin: 0 5px;
        color: #ff7000;
      }
      .next-btn {
        margin: 2px 0 0 19px;
      }
    }
    .rules {
      float: right;
      .next-btn {
        margin: 0;
      }
    }
    & > span {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
    }
    .next-form {
      .next-form-item-label {
        padding-right: 0;
        position: relative;
        top: -1px;
      }
    }
  }
  .next-table {
    .next-dialog-header {
      box-shadow: 0 1px 0 0 #ebebeb;
    }
    .next-dialog-footer {
      box-shadow: 0 -1px 10px 0 rgba(0, 0, 0, 0.06);
    }
    td .next-table-cell-wrapper {
      padding: 10px 16px 6px;
    }
    .item-delete {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333333;
      cursor: pointer;
      &:hover {
        color: #ff7000;
      }
    }
    .name {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333333;
      margin: 0;
      margin-bottom: 8px;
    }
    .tips {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #999999;
      margin: 0;
      margin-bottom: 6px;
    }
    .preview-img-wrapper {
      margin-top: 6px;
    }
  }
}

.complete-page {
  background-color: #fff;
  padding-bottom: 20px;
  margin: 10px;
}

.item-upc-ballon {
  height: 500px;
  overflow: auto;
}

// 商品交易分层值太多，得宽一点
.rules_from_item_trad_layer{
  background-color: red;
  width: 300px;
  .next-cascader-inner{
    width: 300px !important;
    .next-cascader-menu-wrapper{
      width: 100% !important;
    }
  }
}
.crux-attribute{
  .crux-attribute-item{
    justify-content: space-between;
    display: flex;
    &-content{
      display: flex;
      width: 45%;
    }
    &-title{
      line-height: 36px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #666;
      margin-right: 8px;
    }
    &-button{
      line-height: 36px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      >button{
        span{
          color: rgb(255, 112, 0, 0.9) !important;
        }
        span:hover{
          color: rgb(255, 112, 0) !important;
        }
      }
    }
    &-select{
      width: 65%;
    }
  }
}
