.label-pool{
  &.next{
    margin-bottom:50px;
  }
  .c-new-page-wrapper__content{
    padding: 24px 0;
  }
  .next-btn{
    &.cur{
      color:red;
      border-color:red;
    }
  }

  .header{
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #333333;
    padding:20px 0 0 0px;
    font-weight:500;
    &>span{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
      margin-left:8px;
    }
  }

  .next-step-panel {
    height: 68px;
    width: calc(100vw - 240px);
    background: #fff;
    position: fixed;
    bottom: 10px;
    overflow: hidden;
    z-index:10;
    //right: 10px;
    box-shadow: 0 -1px 10px 0 rgba(0,0,0,0.06);
    &.step0{
      padding-left: calc(100vw - 340px);
    }
    &.step1{
      padding-left: calc(100vw - 560px);
    }

    & > .next-btn {
      //float: right;
      margin:20px 8px 0 0;
    }
  }

  .order-num {
    width: 16px;
    height: 16px;
    line-height: 14px;
    text-align: center;
    display: inline-block;
    font-family: AlibabaSans102-Bold;
    font-size: 14px;
    font-style: normal;
    font-weight: normal;
    color: #fff;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #FF7000;

    &.third{
      border:1px solid #ebebeb;
      color:#999;
      background-color: #fff;
    }
    &.current {
      border: 1px solid #FF7000;
      background-color: #FFFFFF;
      color: #FF7000;
    }
  }
  .order-result{
    font-family: PingFangSC-Regular !important;
    font-size: 16px !important;
    color: #333333 !important;
  }

  .section-first,.section-second{
    //margin-top:20px;
    padding-left: 24px;
    .total-info{
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      margin-bottom:24px;
      &>span{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
        margin-left: 8px;
      }
    }
    .next-form{
      width:100%;
      margin-top:20px;
    }
    &>.next-btn{
      margin:16px 24px 24px 24px;
    }
  }
  .section-second{
    padding-left: 24px;
  }

  .select-way{
    & > p {
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #666666;
      margin-left: 48px;
    }

    & > .next-row {
      margin:8px 0 0 48px;

      & > .next-col {
        border-radius: 4px;
        padding:20px 0 20px 20px;
        border: 1px solid #EBEBEB;
        background-color: #FFFFFF;
        margin-right: 12px;
        cursor: pointer;
        &:hover, &.active{
          background: rgba(255, 112, 0, 0.04);
          border: 1px solid rgba(255, 112, 0, 0.40);
        }

        & > h3 {
          font-family: PingFangSC-Medium;
          font-size: 16px;
          color: #333333;
          margin-bottom: 5px;
        }

        & > p {
          font-family: PingFangSC-Regular !important;
          font-size: 12px;
          color: #999999;
          padding: 0;
          margin:0 0 4px 0;
          &>.count{
            color: #4988FD;
          }
        }
      }
    }
  }

  .label-rules{
    .search-panel{
      margin:16px 0 0 24px;
      label{
        font-family: PingFangSC-Regular !important;
        font-size: 14px;
        color: #333333;
        font-weight: normal;
        margin-right: 9px;
      }
      .next-input{
        width:278px;
        margin-right: 13px;
      }
    }
    .tab-content-group{
      .next-col{
        &.left{
          padding-left:14px;
        }
        &.right{
          background:#FAFAFA;
          margin:0 24px 0 10px;
          padding:10px;
        }

      }
      .next-tabs{
        margin: 13px 0 0 0px;

        .next-tabs-bar {
          border: 1px solid #EBEBEB;
          border-bottom: 0;
        }
      }
      .check-list{
        border: 1px solid #EBEBEB;
        padding:0 20px 24px 20px;
        p{
          padding:0;
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #999999;
          margin:20px 0 10px 0;
        }
        .next-checkbox-wrapper{
          margin-left: 0 !important;
          margin-bottom: 10px;
          .next-checkbox-label{
            width:150px;
            display: inline-block;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            line-height:14px;
            color: #333333;
          }
        }
      }
    }
    .rules-form{
      .next-form-item{
        padding: 10px;
        background-color: #fff;
        border: 1px solid #EBEBEB;
        border-radius: 4px;
      }

      .detail-item {
        padding: 10px;
        background-color: #fff;
        margin-bottom: 10px;
        border: 1px solid #EBEBEB;
        border-radius: 4px;
        cursor: pointer;
        position: relative;
        label{
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #333333;
        }

        & > span {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #FF7000;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline-block;
          width: 80%;
          position: relative;
          top: 6px;
        }

        .next-icon {
          position: absolute;
          right: 10px;
          top: 13px;
          &:hover{
            color: #FF7000;
          }
        }
      }
      .next-input{
        width:100%;
      }
      .btn-panel{
        margin-top: 12px;
        overflow: hidden;
        .next-btn{
          float: right;
          margin-left:8px;
        }
      }
    }
  }


}
