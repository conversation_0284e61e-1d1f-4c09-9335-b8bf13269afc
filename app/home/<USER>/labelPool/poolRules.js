import React, {Fragment} from 'react';
import {Button, Dialog, Grid, Input, Message, Select} from '@alife/next';
import './style.scss';
import * as api from "@/utils/api";
import {changeEumToObject, operatorEum, poolEum} from "@/home/<USER>/common";
import {SamplePool} from "@/home/<USER>/comps/samplePool";
import {LabelRules} from "@/home/<USER>/labelPool/labelRules";
import { isString } from '@/utils/validators';
import {poolFiltersConfig} from "@/home/<USER>/common/config";
import {ACLAccess} from "@/components/ACLAccess";
const {Row, Col} = Grid;

/*
* 标签选品-圈选规则
* */
export class PoolRules extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      type: props.poolId != "" ? (poolEum.filter((v) => v.id == props.poolId)[0].type) : '',
      poolId: props.poolId || '',
      poolName: '',
      step: 0,
      tabidx:1,
      isShowRules: false,
      effectFilterFieldBizModelList: [
        {
          filterFieldId: "goodsId",
          filterFieldKey: "goodsId",
          filterFieldValue:JSON.stringify([]),
          operator: operatorEum["goodsId"]
        }
      ],
      effectRules:props.effectRules,
      // originEffectRules:props.originEffectRules,
      isShowSecond:props.isShowSecond
    }
  }

  componentDidMount() {
    // this.sampleRef = React.createRef();
    // this.initField(this.state.effectRules);
    // this.getGoodViewList();
    // console.log("poolRules componentDidMount");
    // console.log(this.state.effectRules);
  }

  componentWillReceiveProps(newProps) {
    // console.log(newProps);
    this.setState({
      type: newProps.poolId != "" ? (poolEum.filter((v) => v.id == newProps.poolId)[0].type) : '',
      poolId: newProps.poolId || '',
      effectRules: newProps.effectRules,
      // originEffectRules:newProps.originEffectRules,
      tempPoolId: newProps.tempPoolId,
      isShowSecond: newProps.isShowSecond
    }, () => {
      console.log("poolRules componentWillReceiveProps");
      console.log(this.state.effectRules);

    });
  }

  changeType = (type) =>{
    if (type != this.state.type) {
      this.setState({
        effectRules: []
      })
    }
    this.setState({
      type
    })
  }

  judgeAccess = async(permissionType,value,callback) =>{
    let resp = await api.validateComponentsPermission({"permissionType":permissionType,"permissionValue":value}).then(api.onRequestSuccess);
    let {rediectUrl} = resp;
    this.setState({
      rediectUrl
    }, () => {
      if(this.state.rediectUrl){
        Dialog.confirm({
          title: '申请权限',
          footer: false,
          content: [<ACLAccess rediectUrl={this.state.rediectUrl}/>],
        })

      }else{
        callback(value);
      }
    });
  }

  setPool = (item) => {
    console.log(item);
    let self = this;
    if (item.id != '41001') {
      self.setState({
        poolId: item.id,
        poolName: item.title
      }, () => {
        self.props.setSourcePoolId(item.id)
      });
    } else {
      this.judgeAccess('ACTION_POOL_CREATE', 'tenbillion', function (value) {
        console.log(value);
        self.setState({
          poolId: item.id,
          poolName: item.title
        }, () => {
          self.props.setSourcePoolId(item.id)
        });
      });
    }
  }

  onChangeMethod = () =>{
    Dialog.show({
      title: "提示",
      content: '圈选方式修改后，当前设置不保存。仍要修改吗？',
      // messageProps: {
      //   type: "warning"
      // },
      okProps: {children: '仍然修改'},
      cancelProps: {children: '取消'},
      footerActions: ['cancel', 'ok'],
      onOk: () => location.href="#/pool/list/create",
      onCancel: () => console.log("cancel")
    });
  }


  onChangePool = () =>{
    Dialog.show({
      title: "提示",
      content: '预选底池修改后，当前设置不保存。仍要修改吗？',
      okProps: {children: '仍然修改'},
      cancelProps: {children: '取消'},
      footerActions: ['cancel', 'ok'],
      onOk: () => {
        this.setState({
          type: "",
          poolId: '',
          poolName: ''
        })
      },
      onCancel: () => console.log("cancel")
    });
  }

  validateEffects = () => {
    const {effectRules,poolId} = this.state;
    let requiredGroup = [];
    let result = [];
    let curPoolConfig = poolFiltersConfig[this.props.poolId];
    if(curPoolConfig.required){
      requiredGroup = curPoolConfig.required.map((v)=>v.filterFieldKey);
    }
    if(effectRules && effectRules.length>0) {
      effectRules.map((v) => {
        if(poolId!='41001') {
          if (requiredGroup.includes(v.filterFieldKey) && v.filterFieldValue && v.filterFieldValue != '') {
            result.push(v.filterFieldKey);
          }
        }else{ //百亿补贴打标池只需要商品活动必填
          if (requiredGroup.includes(v.filterFieldKey) && v.filterFieldKey == 'activity_child_type' && v.filterFieldValue && v.filterFieldValue != '') {
            result.push(v.filterFieldKey);
          }
        }
      })
    }
    let message = poolEum.filter((v) => v.id == this.props.poolId)[0].message;
    return {
      state: result.length == 0,
      message
    }
  }

  onInspectView = () =>{
    // let {tabidx} = this.state;
    let validateResult = this.validateEffects();
    if(validateResult.state && validateResult.message){
      Message.warning(validateResult.message);
      return;
    }
    this.props.showSecond(true);
    this.props.showNextStep(true);
    if(this.sampleRef) {
      this.sampleRef.onPageChange(1);
    }
  }

  onViewList = (type) => { //1:预览，2：查询
    this.setState({
      type
    }, () => {
      this.getGoodViewList();
    })
  }

  onRef = (ref) => {
    this.sampleRef = ref;
  }

  render() {
    const {step, isShowRules, dataSource, total, tabidx, isShowSecond,poolId,effectRules} = this.state;
    let poolDs1 = poolEum.filter((v) => v.type == 1);
    let poolDs2 = poolEum.filter((v) => v.type == 2);
    let {type} = this.state;
    let poolName = poolId != "" ? poolEum.filter((v) => v.id == poolId)[0].title : '';
    return (
      <>
        <div className="section-first">
          <h3 className="header"><i className={`order-num`}>1</i>圈选方式：<span className="order-result">标签选品</span><Button disabled={this.props.isEdit} style={{marginLeft: '8px'}} text type={'primary'} onClick={()=>this.onChangeMethod()}>修改</Button></h3>
          {poolId == "" && <div className="select-way">
            <h3 className="header"><i className="order-num current">2</i>预选底池</h3>
            <p>选品方式</p>
            <Row>
              <Col span={7} className={`${type == 1 ? 'active' : ''}`} onClick={() => this.changeType(1)}>
                <h3>普通选品</h3>
                <p>标签按天更新，选品集更新时间为<span className="count">T+1</span></p>
                <p>圈选数量<span className="count">最大100万</span></p>
              </Col>
              <Col span={7} className={`${type == 2 ? 'active' : ''}`} onClick={() => this.changeType(2)}>
                <h3>实时选品</h3>
                <p>标签和选品集<span className="count">实时更新</span></p>
                <p>圈选数量<span className="count">最大10万</span></p>
              </Col>
            </Row>
            {type != "" && <p>选择底池</p>}
            {type == 1 && <Row>
              {poolDs1.map((v) => {
                return <Col span={5} onClick={() => this.setPool(v)}>
                  <h3>{v.title}</h3>
                  <p>{v.tips}</p>
                </Col>
              })}
            </Row>}
            {type == 2 && <Row>
              {poolDs2.map((v) => {
                return <Col span={5} onClick={() => this.setPool(v)}>
                  <h3>{v.title}</h3>
                  <p>{v.tips}</p>
                </Col>
              })}
            </Row>}
          </div>}
          {poolId != "" && <h3 className="header"><i className={`order-num ${poolId!=''?'':'current'}`}>2</i>预选底池：<span className="order-result">{type == 1 ? '普通选品' : '实时选品'}；{poolName}</span><Button disabled={this.props.isEdit} style={{marginLeft: '8px'}} text type={'primary'} onClick={this.onChangePool}>修改</Button></h3>}
           <h3 className="header"><i className={`order-num third ${poolId != '' ? 'current' : ''}`}>3</i>设置规则</h3>
          {(poolId != "" && type!="") && <LabelRules isEdit={this.props.isEdit} saveEffectRules={this.props.saveEffectRules} sourcePoolId={poolId} effectRules={this.props.ctrlNewEffectRules(effectRules)} />}
          {(poolId != "" && type!="") && <Button type="primary" onClick={()=>this.onInspectView()} >预览</Button>}
        </div>
        {(isShowSecond && poolId != "")  && <div className="section-second">
          <SamplePool onRef={this.onRef}   sourcePoolId={poolId} type={1} tempPoolId={this.props.tempPoolId} effectRules={this.props.ctrlNewEffectRules(effectRules)} />
          {/*<ViewResult type={1} onSort={this.onSort} onPageAndSizeChange={this.onPageAndSizeChange} dataSource={dataSource} total={total} getGoodViewList={this.getGoodViewList} getDeleteGoodList={this.getDeleteGoodList} poolId={this.props.tempPoolId} changeTabidx={this.changeTabidx} />*/}
        </div>}
      </>
    )
  }
}
