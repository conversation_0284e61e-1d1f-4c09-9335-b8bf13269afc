import React, {Fragment} from 'react';
import {
  Form,
  Checkbox,
  Radio,
  Select,
  Input,
  Button,
  Icon,
  Field,
  Message,
  Dialog,
  Grid,
  Tab,
  Balloon, CascaderSelect
} from '@alife/next';
import {arrRemoveJson, changeEumToObject, PoolPageBase} from '../common/index';
import {PageWrapper} from '@/comps/PageWrapper';
import { isString } from '@/utils/validators';

import './style.scss';
import {CheckBoxWithAll} from "@/components/CheckBoxWithAll";
import moment from "moment";
import {dealQualityScoreToArray,getTreeName,unique} from "@/home/<USER>/common";

import {BaseInfo} from "../intelligentPool/baseInfo";
import * as api from "@/adator/api";
import {queryPoolByIdOrName} from "@/containers/decoration/activity/api";
import {CommaSeperatedInput, RangeNumberInput} from '@/home/<USER>/common/components'
import {poolFiltersConfig,filtersConfigMap,validatorMap} from '@/home/<USER>/common/config'
import {Rules} from "@/home/<USER>/labelPool/rules";
import {optionMap} from "@/home/<USER>/common/map";
import {promisify} from "@/utils/others";
import * as validators from '@/utils/validators';

const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { fixedSpan: 4 }
};
const RadioGroup = Radio.Group;
const {Row, Col} = Grid;
/*
* 标签选品
* */

export  class LabelRules extends React.Component {
  constructor(props) {
    super(props);
    let newPoolFiltersConfig = [];
    newPoolFiltersConfig = JSON.parse(JSON.stringify(poolFiltersConfig));
    let curPoolConfig = newPoolFiltersConfig[props.sourcePoolId];
    let curPoolConfigArray = this.ctrlConfigArray(curPoolConfig);
    let curPoolConfigField = {};
    for(let o in curPoolConfig){
      curPoolConfigField[o] = curPoolConfig[o].map((v)=>v.filterFieldKey);
    }
    let selectedField = {
      "baseInfo": curPoolConfigArray.filter((v) => v.filterFieldKey == 'item_ac_CAT' || v.filterFieldKey == 'item_status').map((v) => v.filterFieldKey)
    };
    let selectedAllField =  ["item_ac_CAT", "item_status"];
    if(props.isEdit){
      selectedField = {};
      selectedAllField = props.effectRules.map((v) => v.filterFieldKey);
      props.effectRules.map((v)=>{
        if(!selectedField[v.filterFieldIdGroup]){
          selectedField[v.filterFieldIdGroup] = []
        }
        selectedField[v.filterFieldIdGroup].push(v.filterFieldKey);
      })
    }else{
      if(props.effectRules.length>0){
        selectedField = {};
        selectedAllField = props.effectRules.map((v) => v.filterFieldKey);
        props.effectRules.map((v)=>{
          if(!selectedField[v.filterFieldIdGroup]){
            selectedField[v.filterFieldIdGroup] = []
          }
          selectedField[v.filterFieldIdGroup].push(v.filterFieldKey);
        })
      }
    }
    this.state = {
      tabidx: 'baseInfo',
      effectRules:props.effectRules,
      sourcePoolId: props.sourcePoolId,
      selectedField,
      isEdit:props.isEdit,
      selectedAllField,
      searchPoolConfig: {},
      searchPoolConfigArray:[],
      // searchAllField:[],
      keyWord:'',
      isSearch:false,
      curPoolConfig,
      curPoolConfigField,
      curPoolConfigArray,
      curPoolConfigArrayField: curPoolConfigArray.map((v) => v.filterFieldKey),

      // otherOptions:{
      //   item_goodsCategory:[]
      // },
      groupOptions:{
        ...optionMap
      },
      tmpData:[],
      data:[],
      postData:[]
    }

    this.field = new Field(this, {
      onChange: (name, value) => {
        let item = curPoolConfigArray.filter((v)=>v.filterFieldKey == name);
        let {data} = this.state;
        if(item.filterFieldComponentType!='multipleSelect' && item.filterFieldComponentType!='cascaderSelect'){
          let current = data.filter((v)=>v.filterFieldKey == name);
          current[0].filterFieldValue = value;
          this.setState({data});
        }
      }
    });

    if (props.isEdit || props.effectRules.length > 0) {
      this.initSelectField();
    }
  }

  initSelectField = () =>{
    let {effectRules, selectedAllField, curPoolConfigArray,tmpData} = this.state;
    effectRules.map((v) => {
      let item = curPoolConfigArray.filter((o) => o.filterFieldKey == v.filterFieldKey);
      let result = '';
      switch (item[0] && item[0].filterFieldComponentType){
        case 'select':
        case 'radio':
          result = v.filterFieldValue;
          break;
        case 'cascaderSelect':
        case 'multipleSelect':
          result = v.filterFieldValue ? JSON.parse(v.filterFieldValue).map(v => v.value) : [];
          tmpData[v.filterFieldKey] = v.filterFieldValue ? JSON.parse(v.filterFieldValue) : [];
          break;
        case 'picStandard':
          result = dealQualityScoreToArray(JSON.parse(v.filterFieldValue)).map(v=>v.value);
          tmpData[v.filterFieldKey] = dealQualityScoreToArray(JSON.parse(v.filterFieldValue));
          break;
        case 'arrayInput':
          // result = isString(v.filterFieldValue) ? v.filterFieldValue : JSON.parse(v.filterFieldValue).join(",");
          result = JSON.parse(v.filterFieldValue).join(",");
          break;
        default:
          result = JSON.parse(v.filterFieldValue)
          break;
      }
      // item[0].filterFieldValue = (item[0].type == 'select' || item[0].type == 'radio') ? v.filterFieldValue : JSON.parse(v.filterFieldValue);
      item[0].filterFieldValue = result;
    })
    this.setState({curPoolConfigArray,tmpData});
  }

  componentWillReceiveProps(newProps) {
    let newPoolFiltersConfig = [];
    newPoolFiltersConfig = JSON.parse(JSON.stringify(poolFiltersConfig));
    this.setState({
      sourcePoolId: newProps.sourcePoolId,
      curPoolConfig: newPoolFiltersConfig[newProps.sourcePoolId],
      isEdit: newProps.isEdit
    });
  }

  ctrlConfigArray = (poolConfig) => {
    let result = [];
    for (let o in poolConfig) {
      poolConfig[o].map((v) => {
        result.push(v);
      })
    }
    return result;
  }

  async componentDidMount() {
    this.showRightField();
    await  this.fetchSkuCategory();
    await this.fetchMainCategory();
    await this.fetchCities();
    await this.fetchStoreCategory();
    await this.fetchMarketingType();
  }

  fetchSkuCategory = async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map(dataItem => {
        dataItem.value = dataItem.value.toString();
        dataItem.children && dataItem.children.map(subItem => {
          subItem.value = subItem.value.toString();
          subItem.children && subItem.children.map(thirdItem => {
            thirdItem.value = thirdItem.value.toString();
          })
        })
        return dataItem;
      });
      this.ctrlGroupOptions("item_goodsCategory",dataSource);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  fetchMainCategory= async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getNewAllStoreMainCategory;
      let resp = await request();
      this.ctrlGroupOptions("store_main_category_id",resp.data);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  fetchCities= async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getCities;
      let resp = await request();
      this.ctrlGroupOptions("store_city_id",resp.data.data);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  fetchStoreCategory= async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getCategoryStore;
      let resp = await request();
      this.ctrlGroupOptions("store_cat2_id",resp.data.data);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  fetchMarketingType= async () => {
    let {sourcePoolId} = this.state;
    try {
      let request = api.queryMarketingType;
      let resp = await request(sourcePoolId);
      this.ctrlGroupOptions("item_activity_types",resp.data.data.data);
      this.ctrlGroupOptions("activity_child_type",resp.data.data.data);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  showRightField = () => {
    let {selectedAllField, data,curPoolConfigArray} = this.state;
    if (data.length > 0) {
      let keyGroupFromData = data.map((v) => v.filterFieldKey);
      selectedAllField.map((v)=>{
        if (!keyGroupFromData.includes(v)) {
          let originFieldMap = curPoolConfigArray.filter((o) => o.filterFieldKey == v).map((v) => {
            return {
              ...v,
              edit: true
            }});
          data.push(originFieldMap[0]);
        }
      })
      data.map((v, index) => {
        if (v && !selectedAllField.includes(v.filterFieldKey)) {
          data = arrRemoveJson(data,"filterFieldKey",v.filterFieldKey);
        }
      })
    } else {
      data = curPoolConfigArray.filter((v) => selectedAllField.includes(v.filterFieldKey)).map((v) => {
        return {
          ...v,
          edit: v.filterFieldValue ? false : true
        }
      })
    }
    this.setState({data}, () => {
      this.ctrlPostData();
    })
  }

  confirmContent = async (v) =>{ //造postData
    const outerForm = await (promisify(this.field.validate)());
    let {data,tmpData} = this.state;
    let item = data.filter((o) => o.filterFieldKey == v.filterFieldKey);
    item[0].edit = false;
    // let value = (v.type != 'cascaderSelect' && v.type!='multipleSelect') ? this.field.getValue(v.filterFieldKey) : tmpData[v.filterFieldKey];
    // let value =  this.field.getValue(v.filterFieldKey);
    // item[0].filterFieldValue = value;
    this.setState({
      data
    },()=>{
      this.ctrlPostData();
    })
  }

  ctrlPostData = () =>{
    let {data,tmpData,postData} = this.state;
    postData = [];
    if (data.length > 0) {
      postData = data.map((v) => {
        let arrayInputValue = (v.filterFieldValue ? (isString(v.filterFieldValue) ? v.filterFieldValue.split(",") : v.filterFieldValue) : [])
        let value = (v.filterFieldComponentType != 'arrayInput') ? v.filterFieldValue : arrayInputValue;
        let item = {
          edit: v.edit,
          filterFieldId: v.filterFieldId,
          filterFieldKey: v.filterFieldKey,
          filterFieldLabel: v.filterFieldLabel,
          filterFieldValue: tmpData[v.filterFieldKey] ? tmpData[v.filterFieldKey] : value,
          operator: v.operator,
          filterFieldComponentType: v.filterFieldComponentType,
          filterFieldIdGroup: v.filterFieldIdGroup,
        }
        return item;
      })
    }
    this.setState({postData}, () => {
      this.props.saveEffectRules(postData);
    })
  }

  showItem = (v, bool) => {
    let {data, postData} = this.state;
    let item = data.filter((o) => o.filterFieldKey == v.filterFieldKey);
    let postItem = postData.filter((o) => o.filterFieldKey == v.filterFieldKey);
    item[0].edit = bool;
    item[0].filterFieldValue = postItem[0].filterFieldValue;
    this.setState({
      data,
    })
  }

  ctrlGroupOptions = (key, data) => {
    let {groupOptions} = this.state;
    let othersGroup = ["item_goodsCategory", "store_main_category_id", "store_cat2_id", "store_city_id","item_activity_types","activity_child_type"];
    if (othersGroup.includes(key)) {
      groupOptions[key] = data;
    }
    this.setState({groupOptions},()=>{
      // this.getCascaderContent();
    })
  }

  getOptions = (item) => {
    let {groupOptions} = this.state;
    return groupOptions[item.filterFieldKey];
  }

  handleSelectChange = (value,group,item) =>{
    let {tmpData,data} = this.state;
    if (!value) return
    const haveSelected = group.map(item => {
      const {value, label, pos} = item;
      return {
        value,
        label,
        level: pos.split('-').length - 1
      }
    })
    data.filter((o) => o.filterFieldKey == item.filterFieldKey)[0].filterFieldValue = value;
    tmpData[item.filterFieldKey] = haveSelected;
    // this.field.setValue(item.filterFieldKey,value);
    this.setState({tmpData,data});
  }

  handleInputChange = (value,item) =>{
    let {data} = this.state;
    // console.log(v.split(",").map((o) => o.trim()).join(","))
    // let value = v && v!='' ? v.split(",").map((o) => o.trim()).join(",") : '';
    data.filter((o) => o.filterFieldKey == item.filterFieldKey)[0].filterFieldValue = value;
    this.setState({data});
  }

  switchItem = (item) =>{
    const filterFieldComponentType = item.filterFieldComponentType;
    let {tmpData} = this.state;
    const { init } = this.field;
    let attrs = {
      dataSource: this.getOptions(item),
      value: item.filterFieldValue,
      name: item.filterFieldKey,
    }
    let result;
    switch (filterFieldComponentType) {
      case 'arrayInput': //文本框
        result = <Input.TextArea autoFocus placeholder={'英文逗号隔开，最多100个'}
          {...this.field.init(item.filterFieldKey,
            {
              rules: [{
                ...attrs,
                // required: item.filterFieldIdGroup == 'required',
                // message: `必填`,
              }, {
                validator: validatorMap[item.validatorValue],
                trigger: ['onBlur']
              }],
              props:{
                onChange:(value)=>this.handleInputChange(value,item)
              }
            })}
          value={item.filterFieldValue}
        />
        break;
      case 'radio': //单选框
        result = <RadioGroup  {...attrs}  />
        break;
      case 'checkbox': //复选框
        result = <CheckboxGroup  {...attrs} />
        break;
      case 'multipleSelect': //下拉多选
      case 'cascaderSelect': //下拉级联多选
      case 'picStandard':
        let newValue = [];
        if (item.filterFieldValue && item.filterFieldValue.length > 0) {
          newValue = (item.filterFieldValue[0].value) ? item.filterFieldValue.map((v) => v.value) : item.filterFieldValue;
        }
        // item.filterFieldValue.map((v)=>v.label);
        result = <CascaderSelect
          expandTriggerType={'hover'}
          autoFocus
          {...init(item.filterFieldKey,{
            props:{
              onChange:(value,data)=>this.handleSelectChange(value,data,item)
            }
          })}
          dataSource={this.getOptions(item)}
          value={newValue}
          multiple={true} />
        break;
      case 'rangeNumberInput': //店铺评分类
        result = <RangeNumberInput {...attrs} />
        break;
      case 'select': //下拉多选
        result = <Select autoFocus {...attrs} style={{width:'200px'}} />
        break;
      default:
        result = <Input />;
        break;
    }
    return result;
  }

  switchDetailItem = (item) => {
    const filterFieldComponentType = item.filterFieldComponentType;
    let result;
    let options = this.getOptions(item);
    switch (filterFieldComponentType) {
      case 'arrayInput': //文本框
        result = item.filterFieldValue;
        break;
      case 'radio': //单选框
      case 'select': //单选下拉框
        result = changeEumToObject(options)[item.filterFieldValue];
        break;
      case 'cascaderSelect': //下拉级联多选
      case 'multipleSelect': //下拉多选
      case 'picStandard': //下拉多选
        result = this.getCascaderContent(item)
        break;
      case 'rangeNumberInput': //店铺评分类
        result = item.filterFieldValue ? `${item.filterFieldValue.start ? item.filterFieldValue.start : ''}-${item.filterFieldValue.end ? item.filterFieldValue.end : ''}` : '';
        break;
      case 'checkbox': //复选框
        let value = item.filterFieldValue ? item.filterFieldValue.map((m) => changeEumToObject(options)[m]).join(",") : '';
        result = value;
        break;
      default:
        result = item.filterFieldValue;
        break;
    }
    return result;
  }

  getCascaderContent = (item) =>{
    let {groupOptions, tmpData, isEdit} = this.state;
    let result = "";
    if(item && item.filterFieldValue) {
      let values = tmpData[item.filterFieldKey];
      if (values.length > 0) {
        result = values.map((v) => {
          let labelValue = v.label;
          if (item.filterFieldComponentType == "picStandard") {
            labelValue = getTreeName(groupOptions['item_pic_standard'], v.value);
          }
          return labelValue;
        }).join(",");
      }
    }
    return result;
  }

  deleteItem = (e, item, index) => {
    e.stopPropagation();
    let {selectedAllField, selectedField} = this.state;
    let i = selectedField[item.filterFieldIdGroup].findIndex((v) => v == item.filterFieldKey);
    selectedField[item.filterFieldIdGroup].splice(i, 1);
    selectedAllField = [];
    for (let o in selectedField) {
      if (selectedField[o].length > 0) {
        selectedAllField.push(selectedField[o]);
      }
    }
    this.setState({
      selectedAllField: selectedAllField.toString().split(',').map(item => item),
      selectedField
    }, () => {
      this.showRightField();
    })
  }

  checkAttr = (key, v) => {
    let newValue = unique(v);
    let {selectedField, selectedAllField, curPoolConfigField} = this.state;
    let value = newValue.filter((v) => curPoolConfigField[key].includes(v));
    if (value != "" || value.length == 0) {
      selectedField[key] = value;
      selectedAllField = [];
      for (let o in selectedField) {
        if (selectedField[o].length > 0) {
          selectedAllField.push(selectedField[o]);
        }
      }
      this.setState({
        selectedField,
        selectedAllField: selectedAllField.toString().split(',').map(item => item)
      }, () => {
        this.showRightField();
      })
    }
  }

  changeSelectField = (selectedAllField) =>{
    this.setState({selectedAllField})
  }

  searchField = () => { //筛选出 searchAllField
    let {curPoolConfig,keyWord,searchPoolConfig,searchPoolConfigArray} = this.state;
    searchPoolConfigArray = [];
    for (var o in curPoolConfig) {
      searchPoolConfig[o] = [];
      curPoolConfig[o].filter((v)=>{
        if(v.filterFieldLabel.includes(keyWord)){
          searchPoolConfig[o].push(v);
          searchPoolConfigArray.push(v);
        }
      })
    }
    this.setState({searchPoolConfig,searchPoolConfigArray,isSearch:true},()=>{
      console.log(this.state);
    })
  }

  resetField = () =>{
    this.setState({
      keyWord: '',
      searchPoolConfig:{},
      searchPoolConfigArray:[],
      isSearch:false
    })
  }

  onInputChange = (value) => {
    this.setState({
      keyWord: value
    })
  }

  onTabChange = (value) =>{
    this.setState({
      tabidx:value
    },()=>{
      const element = document.getElementById(this.state.tabidx);
      element.scrollIntoView(true);
    })
  }

  render() {

    let {tabidx, sourcePoolId, selectedAllField,curPoolConfig,searchPoolConfig,isSearch,searchPoolConfigArray,keyWord,curPoolConfigArray,isEdit,effectRules} = this.state;
    // const {getFieldDecorator} = this.props.form;
    let {data} = this.state;
    let group = [
      {value: "required", label: "必填项"},
      {value: "baseInfo", label: "基本信息"},
      {value: "skuStore", label: "门店信息"},
      {value: "skuTrade", label: "交易信息"},
      {value: "skuMarket", label: "营销信息"},
    ]
    let filterGroup = group.filter((v) => {
      return curPoolConfig[v.value]
    })
    return (
      <div className="label-rules">
        <Row className="tab-content-group">
          <Col className="left">
            <div className="search-panel">
              <label>标签查询</label>
              <Input onChange={this.onInputChange} placeholder="请输入标签名称" value={keyWord} />
              <Button type="secondary" onClick={this.searchField}>查询</Button>
              <Button onClick={this.resetField}>重置</Button>
            </div>
            <Tab onChange={this.onTabChange} activeKey={tabidx}>
              {filterGroup.map((o)=>{
                return <Tab.Item title={o.label} key={o.value}></Tab.Item>
              })}
            </Tab>
            <div className="check-list">
              {filterGroup.map((o) => {
                if (searchPoolConfigArray.length>0) {
                  return <>
                  {searchPoolConfig[o.value]  && <p id={`${o.value}`}>{o.label}</p>}
                    {searchPoolConfig[o.value] && <Checkbox.Group value={selectedAllField} onChange={(v) => this.checkAttr(o.value, v)}>
                      {searchPoolConfig[o.value] && searchPoolConfig[o.value].map((v) => {
                        // let item = filtersConfigMap.filter((o) => o.filterFieldKey == v.filterFieldKey);
                        // console.log(item);
                        if (v.tips) {
                          return <Balloon.Tooltip align="b" trigger={<Checkbox
                            value={v.filterFieldKey}><span style={{color: '#FF7000'}}>{v.filterFieldLabel}</span></Checkbox>}>
                            {v.tips}
                          </Balloon.Tooltip>
                        } else {
                          return <Checkbox value={v.filterFieldKey}><span
                            style={{color: '#FF7000'}}>{v.filterFieldLabel}</span></Checkbox>
                        }
                      })}
                    </Checkbox.Group>}
                  </>
                } else {
                  return <>
                    {curPoolConfig[o.value] && <p id={`${o.value}`}>{o.label}</p>}
                    {curPoolConfig[o.value] && <Checkbox.Group value={selectedAllField} onChange={(v) => this.checkAttr(o.value, v)}>
                      {curPoolConfig[o.value].map((v) => {
                        // let item = filtersConfigMap.filter((o) => o.filterFieldKey == v.filterFieldKey);
                        if (v.tips) {
                          return <Balloon.Tooltip align="b" trigger={<Checkbox
                            value={v.filterFieldKey}>{v.filterFieldLabel}</Checkbox>}>
                            {v.tips}
                          </Balloon.Tooltip>
                        } else {
                          return <Checkbox value={v.filterFieldKey}>{v.filterFieldLabel}</Checkbox>
                        }
                      })}
                    </Checkbox.Group>}
                  </>
                }
              })}
            </div>
          </Col>
          <Col className="right">
            <p>编辑规则</p>
            <Form className="rules-form" field={this.field}>
              {data.map((item, index) => {
                  // type 为 date 日期格式需要强制转化为 moment 格式
                  // item.value = item.type == 'date' ? moment(item.value, 'YYYY-MM-DD') : item.value;
                  const textWidth =  (document.body.offsetWidth - 260)/2 - 80 +'px';
                  return (
                    <>
                      {item.edit ? <FormItem
                          key={index}
                          labelAlign={'top'}
                          {...formItemLayout}
                          label={item.filterFieldLabel}
                          hasFeedback
                          // validator={validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))}
                          required={item.required}>
                          {this.switchItem(item)}
                          <div className="btn-panel">
                            <Button type="primary" size="small" onClick={() => this.confirmContent(item)}>确定</Button>
                            <Button size="small" onClick={() => this.showItem(item, false)}>取消</Button>
                          </div>
                        </FormItem> :
                        <div className="detail-item" onClick={() => this.showItem(item, true)}>
                          <label>{item.filterFieldLabel}：</label>
                          {(item.filterFieldComponentType != 'radio' && item.filterFieldComponentType != 'rangeNumberInput') ? <Balloon.Tooltip align="b" trigger={<span style={{width:textWidth}}>{this.switchDetailItem(item)}</span>}>
                            <span>{this.switchDetailItem(item)}</span>
                          </Balloon.Tooltip>:<span>{this.switchDetailItem(item)}</span>}
                          <Icon type={'delete-filling'} style={{"color": "#CCCCCC"}}
                                onClick={(e) => this.deleteItem(e,item, index)}/>
                        </div>}
                    </>
                  )
                })
              }
            </Form>
            {/*<Rules onRulesRef={this.onRulesRef} isEdit={this.props.isEdit} effectRules={effectRules} saveEffectRules={this.props.saveEffectRules}  curPoolConfigArray={curPoolConfigArray} selectedAllField={selectedAllField} changeSelectField={this.changeSelectField}/>*/}
          </Col>
        </Row>
      </div>
    )
  }
}

