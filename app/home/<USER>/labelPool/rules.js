import React, {Fragment} from 'react';
import {
  Form,
  Checkbox,
  Radio,
  Select,
  Input,
  Button,
  Icon,
  Field,
  Message,
  Dialog,
  Grid,
  Tab,
  CascaderSelect,
  Balloon
} from '@alife/next';
import {changeEumToObject, PoolPageBase} from '../common/index';
import {PageWrapper} from '@/comps/PageWrapper';

import './style.scss';
import {CheckBoxWithAll} from "@/components/CheckBoxWithAll";
import moment from "moment";
import {operatorEum,timeEum,crowdEum,arrRemoveJson} from "@/home/<USER>/common";

import {BaseInfo} from "../intelligentPool/baseInfo";
import * as api from "@/adator/api";
import {queryPoolByIdOrName} from "@/containers/decoration/activity/api";
import {CommaSeperatedInput,RangeNumberInput,LevelCascade} from '@/home/<USER>/common/components'
import {optionMap} from '@/home/<USER>/common/map'
import {poolFiltersConfig,filtersConfigMap} from '@/home/<USER>/common/config'
import {formItemProps, validators} from "@/containers/PoolPage/SelectQueryPoolPage/queryfield/common/tools";
import {promisify} from "@/utils/others";
import {composeSelector} from "@/comps/CascaderSelect";
const CheckboxGroup = Checkbox.Group;
const FormItem = Form.Item;
// const formItemLayout = {
//   labelCol: {span:3},
//   wrapperCol: {
//     span: 18
//   },
//   style: {
//     width: '100%'
//   },
//   labelAlign: 'center',
//   labelTextAlign: 'right'
// };

const formItemLayout = {
  labelCol: { fixedSpan: 4 }
};
const RadioGroup = Radio.Group;
const {Row, Col} = Grid;


/*
* 智能选品
* */
export class Rules extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      tabidx: 'baseInfo',
      sourcePoolId:props.sourcePoolId,
      selectedAllField: props.selectedAllField,
      curPoolConfigArray:props.curPoolConfigArray,
      // otherOptions:{
      //   item_goodsCategory:[]
      // },
      groupOptions:{
        ...optionMap
      },
      tmpData:[],
      effectRules:props.effectRules,
      data:[
      //   {
      //     filterFieldLabel: '商品ID',
      //     required: true,
      //     edit: false,
      //     filterFieldId: "item_goodsId",
      //     filterFieldKey: "item_goodsId",
      //     filterFieldValue: '1',
      //     operator: 7,
      //     type: 'arrayInput'
      //   },
      //   {
      //     filterFieldLabel: '商品条码',
      //     required: false,
      //     edit: false,
      //     filterFieldId: "item_upcId",
      //     filterFieldKey: "item_upcId",
      //     filterFieldValue: '2',
      //     operator: 13,
      //     type: 'arrayInput'
      //   },
      //   {
      //     filterFieldLabel: '商品名称',
      //     required: false,
      //     edit: true,
      //     filterFieldId: "item_name",
      //     filterFieldKey: "item_name",
      //     filterFieldValue: '222',
      //     operator: 11,
      //     type: 'arrayInput'
      //   },
      //   {filterFieldLabel: '类目是否正确', filterFieldId: "item_ac_CAT", filterFieldKey: "item_ac_CAT", operator: 13, type: 'radio'},
      //   {filterFieldLabel:'商品分类', filterFieldId: "item_goodsCategory", filterFieldKey: "item_goodsCategory", operator: 16,type:'cascaderSelect'},
      //   {filterFieldLabel:'店铺评分', filterFieldId: "store_score", filterFieldKey: "store_score", operator: 23,type:'rangeNumberInput' },
      //   {filterFieldLabel:'服务标', filterFieldId: "item_ac_serviceTag", filterFieldKey: "item_ac_serviceTag", operator: 13, type:'multipleSelect'},
      //   {filterFieldLabel:'商品分布', filterFieldId: "item_distribute", filterFieldKey: "item_distribute", operator: 7, type:'checkbox'},
      //   { filterFieldLabel:'品质联盟', filterFieldId: "item_ac_PZ", filterFieldKey: "item_ac_PZ", operator: 13, type:'select'},
       ]
    }
    this.field = new Field(this, {
      // onChange: (name, value) => {
      //   // console.log(name,value);
      //   // if (name == 'store_score') {
      //   //   this.field.setValue("store_score", value);
      //   // }
      // }
    });
  }

  componentDidMount() {
    this.fetchSkuCategory();
    this.fetchMainCategory();
    this.fetchCities();
    this.fetchStoreCategory();
    this.showRightField();
    this.props.onRulesRef(this);
  }

  fetchSkuCategory = async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map(dataItem => {
        dataItem.value = dataItem.value.toString();
        dataItem.children && dataItem.children.map(subItem => {
          subItem.value = subItem.value.toString();
          subItem.children && subItem.children.map(thirdItem => {
            thirdItem.value = thirdItem.value.toString();
          })
        })
        return dataItem;
      });
      this.ctrlGroupOptions("item_goodsCategory",dataSource);
      // otherOptions["item_goodsCategory"] = dataSource;
      // this.setState({
      //   otherOptions
      // },()=>{
      //   console.log(this.state);
      // });
    } catch (error) {
      api.onRequestError(error)
    }
  }

  fetchMainCategory= async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getNewAllStoreMainCategory;
      let resp = await request();
      this.ctrlGroupOptions("store_main_category_id",resp.data);
      // otherOptions["store_main_category_id"] = resp.data;
      // this.setState({
      //   otherOptions
      // },()=>{
      //   console.log(this.state);
      // });
    } catch (error) {
      api.onRequestError(error)
    }
  }

  fetchCities= async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getCities;
      let resp = await request();
      this.ctrlGroupOptions("store_city_id",resp.data.data);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  fetchStoreCategory= async () => {
    let {otherOptions} = this.state;
    try {
      let request = api.getCategoryStore;
      let resp = await request();
      this.ctrlGroupOptions("store_cat2_id",resp.data.data);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  showRightField = () => {
    let {selectedAllField, data,curPoolConfigArray} = this.state;
    // let originFieldMap = filtersConfigMap.filter((v) => selectedAllField.includes(v.filterFieldKey));
    if (data.length > 0) {
      let keyGroupFromData = data.map((v) => v.filterFieldKey);
      selectedAllField.map((v)=>{
        if (!keyGroupFromData.includes(v)) {
          let originFieldMap = curPoolConfigArray.filter((o) => o.filterFieldKey == v).map((_v) => {
            return {
              ..._v,
              edit: true
            }});
          data.push(originFieldMap[0])
        }
      })
      data.map((v, index) => {
        if (!selectedAllField.includes(v.filterFieldKey)) {
          data = arrRemoveJson(data,"filterFieldKey",v.filterFieldKey);
        }
      })
    } else {
      data = curPoolConfigArray.filter((v) => selectedAllField.includes(v.filterFieldKey)).map((v) => {
        return {
          ...v,
          edit: v.filterFieldValue ? false : true
        }
      })
    }
    this.setState({data}, () => {
      console.log(this.state);
      this.props.saveEffectRules(data);
    })
  }

  componentWillReceiveProps(newProps) {
    // console.log(this.state.selectedAllField);
    // console.log(newProps.selectedAllField);
    // let {selectedAllField} = this.state;
    this.setState({
      sourcePoolId: newProps.sourcePoolId,
      selectedAllField: newProps.selectedAllField,
      curPoolConfigArray: newProps.curPoolConfigArray,
      effectRules:newProps.effectRules,
    },()=>{
      if(newProps.isEdit){
        this.initData()
      }
    })
  }

  initData = () =>{
    console.log("this.state.effectRules");
    console.log(this.state.effectRules);
  }

  confirmContent = async (v) =>{
    const outerForm = await (promisify(this.field.validate)());
    let {data,tmpData} = this.state;
    let item = data.filter((o) => o.filterFieldKey == v.filterFieldKey);
    item[0].edit = false;
    let value = (v.type != 'cascaderSelect' && v.type!='multipleSelect') ? this.field.getValue(v.filterFieldKey) : tmpData[v.filterFieldKey];
    item[0].filterFieldValue = value;
    this.setState({
      data
      // [v.filterFieldKey]: this.field.getValue(v.filterFieldKey)
    },()=>{
      this.props.saveEffectRules(data);
      // console.log(this.state);
    })
  }

  showItem = (v,bool) =>{
    let {data} = this.state;
    let item = data.filter((o) => o.filterFieldKey == v.filterFieldKey);
    item[0].edit = bool;
    this.setState({
      data,
    })
  }

  onChange = (v) =>{
    console.log(v);
  }

  ctrlGroupOptions = (key, data) => {
    let {groupOptions} = this.state;
    let othersGroup = ["item_goodsCategory", "store_main_category_id", "store_cat2_id", "store_city_id"];
    if (othersGroup.includes(key)) {
      groupOptions[key] = data;
    }
    this.setState({groupOptions},()=>{
      this.getCascaderContent();
    })
  }

  getOptions = (item) => {
    let {groupOptions} = this.state;
    return groupOptions[item.filterFieldKey];
  }

  switchItem = (item) =>{
    const type = item.type;
    let {tmpData} = this.state;
    const { init } = this.field;
    let attrs = {
      dataSource: this.getOptions(item),
      defaultValue: item.filterFieldValue,
      name: item.filterFieldKey,
    }
    switch (type) {
      case 'arrayInput': //文本框
        return <Input.TextArea
          {...attrs}
        /* {{...this.field.init(item.filterFieldKey,
           {
             rules: [{
               ...attrs,
               required: true,
               message: `英文逗号隔开`,
             }, {
               validator: validators.idCommaSeperated,
               trigger: ['onChange']
             }]
           })}}*/
        />
        break;
      case 'radio': //单选框
        return <RadioGroup  {...attrs}  />
        break;
      case 'checkbox': //复选框
        return <CheckboxGroup  {...attrs} />
        break;
      case 'multipleSelect': //下拉多选
      case 'cascaderSelect': //下拉级联多选
        // const dataSource = this.getOptions(item).map(dataItem => {
        //   dataItem.value = dataItem.value.toString();
        //   dataItem.children && dataItem.children.map(subItem => {
        //     subItem.value = subItem.value.toString();
        //     subItem.children && subItem.children.map(thirdItem => {
        //       thirdItem.value = thirdItem.value.toString();
        //     })
        //   })
        //   return dataItem;
        // });
        return <CascaderSelect
          {...init(item.filterFieldKey,{
            props:{
              onChange:(value,data)=>{
                if(!value) return
                const haveSelected = data.map(_item => {
                  const { value: _value, label, pos } = _item;
                  return {
                    value: _value,
                    label,
                    level: pos.split('-').length - 1
                  }
                })
                console.log(haveSelected);
                tmpData[item.filterFieldKey] = haveSelected;
                this.setState({tmpData});
                // this.field.setValue(item.filterFieldKey,[]);
              }
            }
          })}
          dataSource={this.getOptions(item)}
          defaultValue={item.filterFieldValue}
          name={item.filterFieldKey}
          multiple={true} />

        // const CategorySelector = composeSelector(this.getOptions(item));
        // return <CategorySelector
        //   multiple={true}
        //   {...attrs}
        //   {...init(item.filterFieldKey)}
        // />
        break;
      case 'rangeNumberInput': //店铺评分类
        return <RangeNumberInput  {...attrs} />
        break;
      // case 'multipleSelect': //下拉多选
      //   // const MultipleSelectSelector = composeSelector( this.getOptions(item));
      //   // return <MultipleSelectSelector
      //   //   defaultValue={item.filterFieldValue} name={item.filterFieldKey}
      //   //   {...init(item.filterFieldKey)}
      //   // />
      //   return <Select {...init(item.filterFieldKey, {
      //     props: {
      //       onChange: (value, data) => {
      //         console.log(value,data);
      //         // if (!value) return
      //         // const haveSelected = data.map(item => {
      //         //   const {value, label, pos} = item;
      //         //   return {
      //         //     value,
      //         //     label,
      //         //     level: pos.split('-').length - 1
      //         //   }
      //         // })
      //         // tmpData[item.filterFieldKey] = haveSelected;
      //         // this.setState({tmpData});
      //         // this.field.setValue(item.filterFieldKey,[]);
      //       }
      //     }
      //   })}  mode='multipleSelect' {...attrs} />
      //   break;
      case 'select': //下拉多选
        return <Select  {...attrs} />
        break;
      default:
        return <Input/>;
        break;
    }
  }

  switchDetailItem = (item) =>{
    const type = item.type;
    if(item.filterFieldValue) {
      let options =this.getOptions(item);
      // let value = JSON.parse(item.filterFieldValue);
      switch (type) {
        case 'arrayInput': //文本框
          return <span>{item.filterFieldValue}</span>
          break;
        case 'radio': //单选框
        case 'select': //单选下拉框
          return <span>{changeEumToObject(options)[item.filterFieldValue]}</span>
          break;
        case 'cascaderSelect': //下拉级联多选
        case 'multipleSelect': //下拉多选
          return <span>{this.getCascaderContent(item)}</span>
          break;
        case 'rangeNumberInput': //店铺评分类
          return <span>{item.filterFieldValue.start}~{item.filterFieldValue.end}</span>
          break;
        case 'checkbox': //复选框
          const value = item.filterFieldValue.map((m) => changeEumToObject(options)[m]).join(",");
          return <span>{value}</span>
          break;
        default:
          return <span>{item.filterFieldValue}</span>
          break;
      }
    }else{
      return <span></span>
    }
  }

  getTreeName = (list, v) => {
    let _this = this
    for (let i = 0; i < list.length; i++) {
      let a = list[i]
      if (a.value === v) {
        return a.label
      } else {
        if (a.children && a.children.length > 0) {
          let res = _this.getTreeName(a.children, v)
          if (res) {
            return res
          }
        }
      }
    }
  }

  getCascaderContent = (item) =>{
    let {groupOptions} = this.state;
    let result = "";
    if(item && item.filterFieldValue) {
      let values = item.filterFieldValue;
      if(values.length>0){
        result = values.map((v)=>{
          return v.label
        }).join(",");
      }
      // if (groupOptions[item.filterFieldKey]) {
      //   result = values.map((v) => {
      //     return this.getTreeName(groupOptions[item.filterFieldKey], v);
      //   }).join(",");
      // }
    }
    return result;
  }

  deleteItem = (item,index) =>{
    let {selectedAllField} = this.state;
    selectedAllField.splice(index,1);
    this.setState({
      selectedAllField
    },()=>{
      this.props.changeSelectField(selectedAllField);
    })
  }

  render() {
    let {tabidx, sourcePoolId,data} = this.state;
    // const {getFieldDecorator} = this.props.form;
    return (
      <Form className="rules-form" field={this.field}>
        {
          data.map((item, index) => {
            // type 为 date 日期格式需要强制转化为 moment 格式
            // item.value = item.type == 'date' ? moment(item.value, 'YYYY-MM-DD') : item.value;
            return (
              <>
                {item.edit ? <FormItem
                    key={index}
                    labelAlign={'top'}
                    {...formItemLayout}
                    label={item.filterFieldLabel}
                    hasFeedback
                    // validator={validators.joinArray(validators.chain([validators.idCommaSeperated, validators.createMaxCommaItem(100)]))}
                    required={item.required}>
                    {this.switchItem(item)}
                    <div className="btn-panel">
                      <Button type="primary" size="small" onClick={() => this.confirmContent(item)}>确定</Button>
                      <Button size="small" onClick={() => this.showItem(item, false)}>取消</Button>
                    </div>
                  </FormItem> :
                  <div className="detail-item" onClick={() => this.showItem(item, true)}>
                    <label>{item.filterFieldLabel}</label>：
                    {this.switchDetailItem(item)}
                    {/*<Balloon.Tooltip align="b" trigger={<Checkbox value={v}>{item[0].filterFieldLabel}</Checkbox>}>*/}
                    {/*  {item[0].tips}*/}
                    {/*</Balloon.Tooltip>*/}
                    <Icon type={'delete-filling'} style={{"color": "#CCCCCC"}}
                          onClick={() => this.deleteItem(item, index)}/>
                  </div>}
              </>
            )
          })
        }
      </Form>
    )
  }
}


