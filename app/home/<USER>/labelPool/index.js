import React, {Fragment} from 'react';
import {Form, Checkbox, Radio, Select, Input, Button, Breadcrumb, Message} from '@alife/next';
import {crowdEum, PoolPageBase, timeEum} from '../common/index';
import {PageWrapper} from '@/comps/PageWrapper';
import {GroupFilter, CompletePage,BreadcrumbTips} from '../comps';

import './style.scss';
import {TimeRangePicker} from "@/components/TimeRangePicker";
import moment from "moment";
import {Steps,operatorEum,dealQualityScoreToObject,poolEum} from "@/home/<USER>/common";
import {BaseInfo} from "../intelligentPool/baseInfo";
import {PoolRules} from "./poolRules";
import * as api from "@/adator/api";
import {promisify} from "@/utils/others";
import {isString,isArray} from "@/utils/validators";
import {poolFiltersConfig} from "@/home/<USER>/common/config";

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {span: 7},
  wrapperCol: {
    span: 11
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

/*
* 智能选品
* */
export class LabelPoolPage extends PoolPageBase {
  constructor(props) {
    super(props);
    this.state = {
      step: 0,
      isShowNext: false,
      breadcrumbList: [
        {"title": '选品集管理', link: "#/pool/list"},
        {"title": '新建商品选品集', link: ""}
      ],
      poolId: this.params.poolId || '',
      effectRules: [],
      params: {},
      baseInfo: {},
      tempPoolId: '',
      submitPoolId: '',
      submitDisabled: false,
      isShowSecond: false,
      sourcePoolId:''
    }

    this.baseInfoRef = React.createRef();
  }

  get poolId() {
    return this.params.poolId
  }

  get isEdit(){
    return !!this.poolId
  }

  componentDidMount() {
    if(this.isEdit) {
      this.getPoolDetail();
    }
    this.getTempId();
  }

  componentWillReceiveProps(newProps) {
    // this.setState({
    //   effectRules: newProps.effectRules
    // });
  }

  getTempId = async () =>{
    try {
      let request = api.getTempPoolId;
      let resp = await request();
      this.setState({
        tempPoolId:resp.data
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getPoolDetail() {
    const {poolId,baseInfo,params} = this.state;
    try {
      const request = api.getPoolDetailByPoolId;
      let resp = await request(poolId);
      baseInfo.poolName = resp.data.poolName;
      baseInfo.effectAt = resp.data.effectAt;
      baseInfo.expireAt = resp.data.expireAt;
      baseInfo.isNotice = resp.data.isNotice;
      baseInfo.noticeUid = resp.data.noticeUid;
      baseInfo.effectRange = [moment(resp.data.effectAt), moment(resp.data.expireAt)],
      baseInfo.outSynPlatforms = resp.data.outSynPlatforms;
      baseInfo.refreshMode = resp.data.refreshMode;
      params.refreshMode = resp.data.refreshMode;
      params.poolName = resp.data.poolName;
      params.outSynPlatforms = resp.data.outSynPlatforms;
      params.isNotice = resp.data.isNotice;
      params.noticeUid = resp.data.noticeUid;
      params.effectRange = [moment(baseInfo.effectAt),moment(baseInfo.expireAt)];
      this.setState({
        sourcePoolId:resp.data.sourcePoolId,
        detailData: resp.data,
        effectRules: resp.data.effectRules,
        baseInfo,
        params
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  nextStep = () => {
    this.setState({
      step: 1 + this.state.step
    })
  }

  prevStep = () => {
    this.setState({
      step: this.state.step - 1
    })
  }

  updateBaseInfo = (name, value) => {
    let {baseInfo, params} = this.state;
    baseInfo[name] = value;
    params[name] = value;
    this.setState({
      baseInfo,
      params
    })
  }

  updatePoolRules = (name, value) => {
    let {effectRules} = this.state;
    let item = {
      filterFieldId: name,
      filterFieldKey: name,
      filterFieldValue: value,
      operator: operatorEum[name]
    };
    let group = effectRules.filter((v) => v.filterFieldId == name);
    if (group.length == 0) {
      effectRules.push(item);
    } else {
      group[0].filterFieldValue = value;
    }
    this.setState({
      params:{
        effectRules,
        ...this.state.params
      }
    },()=>{
      console.log(this.state.params);
    })
  }

  ctrlReq = () =>{
    const {params, baseInfo, effectRules,tempPoolId,sourcePoolId} = this.state;
    let type = poolEum.filter((v) => v.id == sourcePoolId)[0].type;
    let poolResultLimit = (type == 1) ? '1000000' : '100000';
    return {
      sourcePoolId,
      poolType: 1,
      poolName: params.poolName,
      effectAt: params.effectRange[0].valueOf(),
      expireAt: params.effectRange[1].valueOf(),
      isNotice: params.isNotice || 0,
      noticeUid: params.noticeUid || '',
      effectRules: this.ctrlNewEffectRules(effectRules),
      poolStoreType: '',
      tempPoolId,
      poolResultLimit,
      refreshMode: params.refreshMode,
      // refreshMode: 1,
      outSynPlatforms: params.outSynPlatforms
    };
  }

  validateScene = () => {
    const {effectRules} = this.state;
    let result = [];
    effectRules.map((v) => {
      if ((v.filterFieldKey == 'timePeriod' || v.filterFieldKey == 'crowd') && v.filterFieldValue != "[]") {
        result.push(v.filterFieldKey);
      }
    })
    return result.length == 0
  }

  savePool = async () => {
    const {params, baseInfo, effectRules} = this.state;
    await (promisify(this.baseInfoRef.current.field.validate)());
    let createPoolV2Req = this.ctrlReq();
    let request = api.createPool;
    if (this.isEdit) {
      createPoolV2Req.poolId = this.poolId;
      request = api.updatePool;
    }
    let resp = await request(createPoolV2Req);
    if (!resp.data.data.success) {
      Message.warning(resp.data.data.errMessage);
    } else {
      Message.success('操作成功');
      this.history.push("/pool/list");
    }
  }

  submitPool = async () => {
    let {params,effectRules} = this.state;
    await (promisify(this.baseInfoRef.current.field.validate)());
    this.setState({
      submitDisabled:true
    })
    try {
      let createPoolV2Req = this.ctrlReq();
      let request = api.createPool;
      if (this.isEdit) {
        createPoolV2Req.poolId = this.poolId;
        request = api.updatePool;
      }
      let resp = await request(createPoolV2Req);
      let id = resp.data.data.data;
      if(id) {
        let result = await api.publishPool(id);
        Message.success('发布成功');
        this.setState({
          submitPoolId:id,
        })
        this.nextStep();
      }else{
        Message.info(resp.errMessage);
      }
    } catch (error) {
      api.onRequestError(error);
      this.setState({
        submitDisabled:false
      })
    }
  }

  showNextStep = (bool) => {
    this.setState({
      isShowNext: bool
    })
  }

  showSecond = (bool) =>{
    this.setState({
      isShowSecond: bool
    })
  }

  saveEffectRules = (data) => { //传给预览的
    let {effectRules} = this.state;
    effectRules = [];
    data.map((v) => {
      effectRules.push({
        filterFieldId: v.filterFieldId,
        filterFieldKey: v.filterFieldKey,
        filterFieldLabel: v.filterFieldLabel,
        filterFieldValue: v.filterFieldValue,
        filterFieldComponentType: v.filterFieldComponentType,
        filterFieldIdGroup: v.filterFieldIdGroup,
        operator: v.operator,
      })
    })
    this.setState({
      effectRules
    })
  }

  setSourcePoolId = (sourcePoolId) =>{
    this.setState({
      sourcePoolId
    })
  }

  ctrlNewEffectRules = (effectRules) =>{
    let newEffectRules = [];
    if (effectRules && effectRules.length > 0) {
      effectRules.map((v) => {
        let value = v.filterFieldValue;
        if(isArray(value) && value.length == 0){
          return;
        }
        if (value || value == '') {
          if (v.filterFieldComponentType == 'picStandard') {
            value = dealQualityScoreToObject(value);
          }
          // let value = v.filterFieldValue ? ((v.type != 'arrayInput') ? v.filterFieldValue : JSON.parse(v.filterFieldValue)) : '';
          newEffectRules.push({
            filterFieldId: v.filterFieldId,
            filterFieldKey: v.filterFieldKey,
            filterFieldLabel: v.filterFieldLabel,
            filterFieldValue: isString(value) ? value : JSON.stringify(value),
            filterFieldComponentType: v.filterFieldComponentType,
            filterFieldIdGroup: v.filterFieldIdGroup,
            operator: v.operator,
          })
        }
      })
    }
    return newEffectRules;
  }

  render() {
    const {step,breadcrumbList,effectRules,baseInfo,tempPoolId,detailData,submitPoolId,isShowNext,submitDisabled,isShowSecond,sourcePoolId} = this.state;
    return (
      <PoolPageBase.Container className={`label-pool ${isShowNext ? 'next' : ''}`}>
        <BreadcrumbTips list={breadcrumbList} />
        <PageWrapper>
          <Steps current={step} middleText={'基本信息'} />
          {step == 0 && <PoolRules saveEffectRules={this.saveEffectRules} isEdit={this.isEdit} isShowSecond={isShowSecond} showSecond={this.showSecond} ctrlNewEffectRules={this.ctrlNewEffectRules} setSourcePoolId={this.setSourcePoolId} showNextStep={this.showNextStep} tempPoolId={tempPoolId} poolId={sourcePoolId} effectRules={effectRules}  nextStep={this.nextStep} updatePoolRules={this.updatePoolRules}/>}
          {step == 1 && <BaseInfo ref={this.baseInfoRef} isEdit={this.isEdit} sourcePoolId={sourcePoolId}  baseInfo={baseInfo} detailData={detailData} updateBaseInfo={this.updateBaseInfo}/>}
          {step == 2 && <CompletePage {...this.props} poolId={submitPoolId}/>}
          {(isShowNext && step != 2) && <div className={`next-step-panel step${step}`}>
            {step != 0 && <Button onClick={this.prevStep}>返回上一步</Button>}
            {step == 0 && <Button type={'primary'} onClick={this.nextStep}>下一步</Button>}
            {step == 1 && <Button type={'primary'} onClick={this.savePool}>保存信息</Button>}
            {step == 1 && <Button type={'primary'} disabled={submitDisabled} onClick={this.submitPool}>立即发布</Button>}
          </div>}
        </PageWrapper>
      </PoolPageBase.Container>
    )

  }
}
