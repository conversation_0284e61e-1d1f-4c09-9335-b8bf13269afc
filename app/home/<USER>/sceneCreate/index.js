import React, {Fragment, useEffect, useState,useRef} from 'react';
import {Button, Dialog, Grid, Message} from '@alife/next';
import {withRouter} from "react-router-dom";
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {permissionAccess} from "@/components/PermissionAccess";
import RankSet from "@/containers/channel/market/comps/rankSet";
import './style.scss';
import {BreadcrumbTips} from "@/home/<USER>/comps";
import SchemaForm from "@/utils/Form/src";
import {getNormalTemplateSchema} from "@/home/<USER>/setSchema/request";
import * as api from "@/utils/api";
import {baseQuerySceneSchemaDTO, filterResponseDTO, flattenObject} from "@/home/<USER>/common";
import {showQrCodeMap} from "@/containers/channel/market/common";
import {createSceneCard, getDetailSceneCard} from "@/adator/api/index";
const {Row, Col} = Grid
/**
 * 榜单创建表单
 * @param props
 * @returns {JSX.Element}
 */

const DemoSchema = {
  "detail": {
    "title": "基础配置",
    "type": "object",
    "required": [
      "configName",
      "beginTime",
      "endTime",
      "title"
    ],
    "properties": {
      "sceneId": {
        "type": "string",
        "title": "场景ID:",
        "disabled": true,
        "x-ui-placeholder": "保存后自动生成"
      },
      "sceneName": {
        "type": "string",
        "title": "场景名称:",
        "x-ui-placeholder": "请输入场景名称，最多20个字",
        "maximum": 20
      },
      "scenePic": {
        "title": "场景氛围图:",
        "type": "string",
        "format": "uri",
        "x-ui-widget": "img-upload",
        "x-ui-validate": {
          "width": 1500,
          "height": 600,
          "maxSize": 500,
          "accept": "png,apng,jpg,jpeg,gif"
        }
      },
      "sceneLogo": {
        "title": "场景logo:",
        "type": "string",
        "format": "uri",
        "x-ui-widget": "img-upload",
        "x-ui-validate": {
          "width": 1500,
          "height": 600,
          "maxSize": 500,
          "accept": "png,apng,jpg,jpeg,gif"
        }
      },
      "maxLimit": {
        "title": "报名商品上限：",
        "type": "number"
      },
      "priceRange": {
        "title": "商品现价：",
        "type": "integer",
        "format": "price-range",
        "default": "-"
      },
      "originalPriceRange": {
        "title": "商品原价：",
        "type": "integer",
        "format": "price-range",
        "default": "-"
      },
      "barcode": {
        "type": "string",
        "x-ui-widget": "string-array",
        "title": "商品条形码:",
        "x-ui-placeholder": "请输入商品条形码"
      },
      "itemName": {
        "type": "string",
        "x-ui-widget": "string-array",
        "title": "商品名称:",
        "x-ui-placeholder": "请输入商品名称"
      },
      "timeRanges2": {
        "type": "array",
        "title": "测试多个价格：",
        "uniqueItems": true,
        "canMoveUp": false,
        "canRemove": false,
        "maxItems": 4,
        "items": {
          "type": "integer",
          "format": "price-range",
          "hasDrag": true
        },
        "hidden": true,
        "default": [
          "-"
        ]
      },
      "timeRanges3": {
        "type": "array",
        "title": "测试多个价格：",
        "uniqueItems": true,
        "canMoveUp": false,
        "canRemove": false,
        "maxItems": 4,
        "items": {
          "multiple": true,
          "type": "string",
          "x-ui-widget": "dynamicCascadeSelectWidget"
        },
        "hidden": true,
        "default": [
          ""
        ]
      },
      "category": {
        "type": "string",
        "multiple": true,
        "x-ui-widget": "dynamicCascadeSelectWidget",
        "title": "商品分类:",
        "x-ui-placeholder": "请输入商品分类",
        "requestOptions": {
          "method": "get",
          "api": "api/dynamic/queryAllSkuCategory",
          "domainInfo": "admin"
        }
      },
      "mainCategory": {
        "type": "string",
        "multiple": true,
        "x-ui-widget": "dynamicCascadeSelectWidget",
        "title": "主营类目:",
        "x-ui-placeholder": "请输入主营类目",
        "requestOptions": {
          "method": "get",
          "api": "api/common/queryStoreMajorCategory",
          "domainInfo": "putIn"
        }
      },
      "blackItemName": {
        "type": "string",
        "x-ui-widget": "string-array",
        "title": "剥除关键词:",
        "x-ui-placeholder": "请输入剥除关键词"
      },
      "inviteId": {
        "type": "string",
        "x-ui-widget": "string-array",
        "title": "招商ID:",
        "x-ui-placeholder": "请输入招商ID"
      }
    }
  }
}

function sceneCreate(props) {
  console.log(props);
  const breadcrumbList = [
    {"title": '场景管理', link: "#/channelManage/gallery/sceneManage"},
    {"title": '新建场景', link: ""}
  ];
  const originSceneId = props.match.params.id || '';
  const sceneId = originSceneId < 0 ? -originSceneId : originSceneId;
  const [formData,setFormData] = useState({});
  const [scheduleTemplate, setScheduleTemplate] = useState({});
  const [formSchema, setFormSchema] = useState({});

  const onFormChange = (formData) => {
    console.log('submit',formData);
    setFormData(formData);
  }
  /**
   * 非资源位的获取元数据schema——常规链路
   * */
  const getNormalSchema = () => {
    getNormalTemplateSchema(baseQuerySceneSchemaDTO)
      .then((result) => {
        let scheduleTemplate = JSON.parse(result);
        setScheduleTemplate(scheduleTemplate);
        setFormSchema(scheduleTemplate.rankSchema);
        if(sceneId) {
          getDetail(scheduleTemplate);
        }
      })
      .catch(api.onRequestError);
  }

  useEffect(()=>{
    getNormalSchema();
  },[])

  const getDetail = (schedule) => {
    if(sceneId) {
      getDetailSceneCard({sceneId: sceneId})
        .then((result) => {
          const _detailData = filterResponseDTO(schedule,result);
          console.log('_detailData',_detailData);
          setFormData(_detailData);
        })
        .catch(api.onRequestError);
    }
  }

  /**
   * 调后端请求前的requestDto的处理：价格数据结构2-3，转成 {min:2,max:3}
   * @returns {{}}
   */
  const preRequestDTO = () => {
    const property = scheduleTemplate.detail.properties;
    const requestDTO = {};

    Object.keys(formData).forEach(key => {
      const value = formData[key] || '';
      const {canGetThreeLevel} = property[key];
      const widgetType = property[key]['x-ui-widget'];
      if (widgetType == 'price-range') {
        requestDTO[key] = {
          min: value.split('-')[0],
          max: value.split('-')[1],
        };
      } else if (widgetType == 'string-array') {
        requestDTO[key] = value ? value : [];
      } else if (widgetType == 'dynamicCascadeSelectWidget'){
        requestDTO[key] = value ? value : (canGetThreeLevel ? {} : []);
      }
      // else if (widgetType == 'dynamicCascadeSelectWidget' && canGetThreeLevel) {
      //   requestDTO[key] = value.haveSelected;
      //   requestDTO[`${key}Display`] = value.haveSelectedDisplay;
      // }
      else {
        requestDTO[key] = value;
      }
    });

    console.log('preRequestDTO',formData, requestDTO);
    return requestDTO;
  }

  /**
   * 将规则分为一个group，字段名在schema的fieldGroup中配置
   * */
  const ctrlRequestDTO = () => {
    const property = scheduleTemplate.detail.properties;
    const requestDTO = {};
    const _formData = preRequestDTO(formData);

    Object.keys(_formData).forEach(key => {
      let tmpValue = _formData[key] || '';
      if (key === 'weightAtmosphereList') {
        tmpValue = tmpValue || null
      }
      const value = tmpValue;

      if (property[key] && property[key].fieldGroup) {
        const fieldGroup = property[key].fieldGroup;
        requestDTO[fieldGroup] = requestDTO[fieldGroup] || {};
        requestDTO[fieldGroup][key] = value;
      } else {
        requestDTO[key] = value;
      }
    });

    console.log('requestDTO', requestDTO);
    return requestDTO;
  }

  const formRef = useRef(null);

  const submit = () => {
    const errors = formRef.current.fieldErrorMap;
    if (!Object.keys(errors).length && formData) {
      let requestDTO = ctrlRequestDTO();
      delete requestDTO.sceneId;
      if (originSceneId && originSceneId > 0) {
        requestDTO.sceneId = sceneId;
      }
      console.log('submit 2',requestDTO);
      createSceneCard(requestDTO)
        .then((result) => {
          console.dir(result);
          sessionStorage.clear();
          location.href = '#/channelManage/gallery/sceneManage';
        })
        .catch(api.onRequestError);
    }
  }

  let showSchemaForm =  ((sceneId && formData.sceneName) || !sceneId) && scheduleTemplate.detail;
  return (
    <div className={'container'}>
      <BreadcrumbTips list={breadcrumbList}/>
      <div className={'body'}>
        <Row>
          <Col span={'2'}></Col>
          <Col span={'19'}>
            <div className="rank-create">
              {showSchemaForm && <SchemaForm ref={formRef} schema={scheduleTemplate.detail} formData={formData} onChange={(formData) => onFormChange(formData)}/>}
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={'8'}/>
          <Col span={'16'}>
            {/*<Button>取消</Button>*/}
            &nbsp;
            {showSchemaForm && <Button type="primary"  onClick={submit}>
              发布
            </Button>}
          </Col>
        </Row>
      </div>
    </div>
  )
}

export const LogTimeSceneCreatePage = logTimeComponent(withRouter(sceneCreate), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const SceneCreatePage = permissionAccess(LogTimeSceneCreatePage)

