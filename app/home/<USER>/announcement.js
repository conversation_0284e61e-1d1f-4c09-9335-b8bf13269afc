import {Slider,Dialog} from '@alife/next'
import React, {useState, useEffect} from 'react'
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {withRouter} from 'react-router-dom'
import './index.scss';
import * as api from "@/adator/api";

function Announcement(props) {

  const  showMessage = (content) =>{
    let re = /(http[s]?:\/\/([\w-]+.)+([:\d+])?(\/[\w-\.\/\?%&=]*)?)/gi;
    let showHtml = content.replace(re, function (url) {
      return (
        '<a href="' + url + '" target=_blank>' + url + '</a>'
      )
    })
    let html = { __html: showHtml };
    Dialog.confirm({
      title: "公告详情",
      width: "500px",
      className: "announceDialog",
      footer: false,
      content:<div dangerouslySetInnerHTML={html}></div>
    })
  }

  return (
    <div className="announcement">
      <Slider
        slideDirection="ver"
        dots={false}
        arrows={false}
        autoplaySpeed={5000}
        autoplay={true}
        className="ver-slick"
      >
        {props.announcementData.map((item, index) => (
          <div key={index} className="custom-slider">
            <h3 className="content">
              <span className="copy" onClick={()=>showMessage(item.content)}>{(item.content && item.content.length > 200) ? item.content.slice(0, 200) + '...' : item.content}</span>
            </h3>
          </div>
        ))}
      </Slider>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(Announcement), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const AnnouncementPage = permissionAccess(LogTimePutInPage)

