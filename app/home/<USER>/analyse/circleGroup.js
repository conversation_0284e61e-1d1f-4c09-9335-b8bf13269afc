import {Input, Select, Tab, But<PERSON>, Table, Pagination, Grid, Breadcrumb} from '@alife/next'
import React, {useState, useEffect} from 'react'
const {Row,Col} = Grid;
import { TreemapChart } from "bizcharts";
import './index.scss';
import * as api from "@/adator/api";
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/treemap';
import 'echarts/lib/component/title';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import {changeEumToObject} from "@/home/<USER>/common";

export default function CircleGroup(props) {
  const {poolId} = props;
  const groupList = [
    {value: 1, label: '价格分布', defaultDateType: 'd7'},
    {value: 2, label: '商户业态', defaultDateType: 'd1'},
    {value: 3, label: '销量', defaultDateType: 'd7'},
    {value: 4, label: '店铺评分', defaultDateType: 'd1'}
  ]
  const group = changeEumToObject(groupList);
  useEffect(() => {
    for (let i = 0; i < groupList.length; i++) {
      loadData(groupList[i].value, groupList[i].defaultDateType);
    }
  }, [])

  const loadData = (dimType='1',dateType) => { /* 1:价格分布 2：商户业态、3：销量 4:店铺评分 */
    let req = {
      dimType,
      dateType,
      poolId
    };
    try {
      api.getPickPieAnalyse(req)
        .then((res) => {
          let result = res.data.map(({dimSeg, dimValue, dimValue2}) => ({
            name: dimSeg,
            value: dimValue,
            value2: dimValue2
          }))
          let centerMsg = '';
          if (dimType == 2) {
            centerMsg = res.data[0] ? res.data[0].centerMsg : '';
          }
          renderChart(dimType, result, centerMsg);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const renderChart = (dimType, res, centerMsg) => {
    let chartElement = document.getElementById(`chart${dimType}`);
    let myChart = echarts.init(chartElement);
    let option = {
      // color:["#61DDAA","#5B8FF9","#65789B","#F6BD16"],
      color:["#5B8FF9","#5AD8A6","#9270CA","#F6BD16","#E8684A","#6DC8EC","#5D7092","#FF9D4D","#009D9A"],
      title: {
        text: (dimType != 2) ? '' : '总商户数',
        subtext: (dimType != 2) ? '' : centerMsg,
        left:'center',
        top:'middle',
        textStyle:{
          fontSize:12,
          align:'center',
          fontFamily: 'PingFangSC-Regular',
          color:'#4B535E',
        },
        subtextStyle:{
          fontFamily:'AlibabaSans-Medium',
          fontSize:32,
          color:'rgba(44,53,66,0.85)',
          align:'center',
          top:10
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: "{b}：{d} %",
      },
      legend: {
        show: false
      },
      series: [
        {
          name: group[dimType],
          type: 'pie',
          radius: (dimType != 2) ? '70%' : ['40%', '70%'],
          top:50,
          label:{
            formatter: function (params) {
              let {name, value, value2} = params.data;
              let nameUnitGroups = {1: '元', 2: '', 3: '单', 4: '分'};
              let nameUnit = nameUnitGroups[dimType];
              let showValue = (dimType != 2) ? value : value2;
              let valueUnitGroups = {1: '%', 2: '家', 3: '%', 4: '%'};
              let valueUnit = valueUnitGroups[dimType];
              return `${name}${nameUnit}：${showValue}${valueUnit}`;
            },
            // formatter: "{b}${dimType==1?'元':''}：{d} %",
          },
          data:res,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    if(res && res.length==0) {
      myChart.showLoading({
          text: '暂无数据',
          color: '#ffffff',
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 0.8)',
        }
      );
    }
    myChart.setOption(option);
  }

  const setTabidx = (value,dimType) =>{
    loadData(dimType,value);
  }

  return (
    <div className='circle-group'>
      {groupList.map((v) => {
        return <div className="item">
          {(v.value == 1 || v.value == 3) && <Tab shape="capsule" size="small" onChange={(o) => setTabidx(o, v.value)}>
            <Tab.Item title="近7天" key={'d7'}/>
            <Tab.Item title="近30天" key={'d30'}/>
          </Tab>}
          <span>{v.label}</span>
          <div id={`chart${v.value}`} className="chart"></div>
        </div>
      })}
    </div>
  )
}


