.analyse-page{

  /*选品洞察所有表格样式*/
  .next-table th{
    border: 1px solid #CFDBE5;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #326891;
  }
  .next-table.only-bottom-border table tr td:first-child, .next-table.only-bottom-border table tr th:first-child{
    border-left-width:1px !important;
  }
  .next-table th .next-table-cell-wrapper,.next-table td .next-table-cell-wrapper{
    padding:10px 16px;
  }
  .next-table td{
    border: 1px solid #ECF1F4;
    color: #1F2225;
  }

  .analyse-container{
    margin:10px 24px;
    position: relative;
    .time{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #666666;
      line-height: 46px;
      position: absolute;
      right: 24px;
      top: 0;
    }
    .next-tabs{
      padding:0 24px;
    }
  }

  .city-distribute{
    background-color: #fff;
    padding:0 24px 24px 20px;
    .type-panel{
      margin-bottom: 30px;
    }
  }

  .good-category-distribute{
    background-color: #fff;
    padding:0 24px 24px 20px;
    margin-top: 10px;
  }

  .circle-group {
    overflow: hidden;
    .item {
      width:49%;
      float: left;
      margin-top: 10px;
      background-color: #fff;
      margin-right: 1%;
      position: relative;
      .next-tabs{
        padding: 0;
        position: absolute;
        right: 10px;
        top: 16px;
        display: inline-block;
        width:130px;

        &.next-small .next-tabs-tab{
          .next-tabs-tab-inner {
            padding: 5px 12px !important;
            color: #666666;
          }
          &.active {
            background: rgba(255, 112, 0, 0.06) !important;
            border: 1px solid #FF7000 !important;
            .next-tabs-tab-inner {
              color: #FF7000 !important;
            }
          }
        }


        //.next-tabs-capsule > .next-tabs-bar .next-tabs-tab{
        //  &.active{
        //    background: rgba(255,112,0,0.06) !important;
        //    border: 1px solid #FF7000 !important;
        //  }
        //}
      }

      span {
        font-family: PingFangSC-Medium;
        font-size: 16px;
        text-align: left;
        line-height: 16px;
        color: #333;
        margin: 24px 0 0 24px;
        display: inline-block;
      }

      .chart {
        width: 95%;
        height: 360px;
      }
    }
  }

  .city-grid{
    position: relative;
    .info{
      background-color: #fff;
      position: absolute;
      z-index: 1000;
      box-shadow: 0 2px 8px 0 rgba(0,0,0,0.08);
      border-radius: 8px;
      top: 12px;
      left: 12px;
      padding: 16px 36px 16px 16px;
      min-width: 220px;
      .next-icon{
        position: absolute;
        right: 10px;
        top: 15px;
      }
      .name{
        word-break: break-all;
        margin-bottom: 5px;
      }
      .id{
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #666666;
        letter-spacing: 0;
        text-align: justify;
        line-height: 12px;
        border-bottom: 1px solid #EBEBEB;
        padding-bottom: 16px;
      }
      .cnt{
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #666666;
        letter-spacing: 0;
        line-height: 28px;
        span{
          float: right;
          margin-right: 20px;
        }
      }
      p{
        padding: 0;
        margin: 0;
      }
    }
    .color-group{
      height: 136px;
      width: 72px;
      box-shadow: 0 2px 8px 0 rgba(0,0,0,0.15);
      border-radius: 4px;
      background-color: #FFFFFF;
      position: absolute;
      bottom:12px;
      left: 12px;
      z-index: 100;
      padding: 12px;
      p{
        padding: 0;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #999999;
        line-height: 12px;
      }
      .item{
        overflow: hidden;
      }
      .color-block{
        display: inline-block;
        height: 20px;
        width: 24px;
        float: left;
      }
      .color-label{
        float: left;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
        margin-left: 12px;
      }
    }
  }

  .title-panel{
    overflow: hidden;
    padding:21px 0  15px 0;
    background-color: #fff;

    &.grid {
      height:60px;
      padding:0;
      border-bottom:1px solid #EBEBEB;
      .left {
        float: left;
        .grid-list{
          padding:21px 0 10px 21px;
        }
        & > a {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #666666;
          margin-left: 12px;
          display: inline-block;
          width: 80px;
          height:60px;
          line-height:60px;
          border-right: 1px solid #EBEBEB;
          margin-right: 20px;
        }
      }
      .next-tabs-bar{
        margin-top: 12px;
      }
    }

    &.table{
      .left{
        display: inline-block;
        .grid-list{
          margin-left: 21px;
          width:300px;
        }
      }
      .price-filter{
        margin-right: 21px;
      }
    }

    &>.level-brand{
      display: inline-block;
      &>span{
        font-family: PingFangSC-Medium;
        font-size: 16px;
        margin-top: 5px;
        display: inline-block;
      }
      &>.next-btn{
        float:inherit;
        //margin:6px 0 0 10px;
        margin-left: 10px;
      }
      &>label{
        margin-left: 8px;
      }
    }
    & > span {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      margin-top: 5px;
      display: inline-block;

      & > a {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #333333;
        margin-left: 10px;
      }
    }
    .next-btn{
      float: right;
    }
    .next-tabs{
      float: right;
      display: inline-block;
      width:150px;
      padding:0;
      .next-tabs-tab-inner{
        padding:6px 12px !important;
      }
    }
  }
}
