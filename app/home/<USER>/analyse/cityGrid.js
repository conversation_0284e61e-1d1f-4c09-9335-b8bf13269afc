import { Grid,Message,Icon} from '@alife/next'
import React, {useState, useEffect} from 'react'
import './index.scss';
import * as api from "@/adator/api";
import {getQueryString} from "@/utils/others";

export default function CityGrid(props) {
  const [gridData, setGridData] = useState([]);
  // const mapCenter = [107.52226, 26.253038]
  // const [groupPath, setGroupPath] = useState([]);
  const [info, setInfo] = useState({});
  const [infoVisible, setInfoVisible] = useState(false);
  const {poolId} = props;
  const queryCityId = getQueryString("cityId");
  const queryCityName = getQueryString("cityName");
  const cityId = (queryCityId == props.cityId) ? queryCityId : props.cityId;
  const cityName = (queryCityName == props.cityName) ? queryCityName : props.cityName;

  useEffect(() => {
    loadData();
  }, [cityId])

  const renderMap = (mapObj, list) => {
    if (list && list.length > 0) {
      let group = {};
      list.map((item, index) => {
        let newPath = [];
        if (item.gridScope && item.gridScope.length > 0) {
          item.gridScope.map((o) => {
            let item = [];
            item[0] = parseFloat(o.end);
            item[1] = parseFloat(o.start);
            newPath.push(item);
          })
          group[`polygon${index}`] = (function () {
            let current = colorGroup.filter(v => v.value == item.level);
            return new window.AMap.Polygon({
              map: mapObj,
              path: newPath,
              strokeColor: "#0055FF", //线颜色
              strokeOpacity: 1, //线透明度
              strokeWeight: 0,    //线宽
              fillColor: (current && current.length > 0) ? current[0].color : "#1791fc", //填充色
              fillOpacity: (current && current.length > 0) ? current[0].opacity : 0.35,//填充透明度
              bubble: false
            });
          })();
          group[`polygon${index}`].on('click', function (e) { //todo 还需要将已圈住的网格线取消
            console.log(group[`polygon${index}`]);
            if(group[`polygon${index}`]) {
              group[`polygon${index}`].setOptions({strokeWeight: 3});
            }
            openInfo(mapObj, item);
          });
        }
      })
      // map.setFitView();  //todo 看是否需要加回来 map总会报错
    }
  }

  const openInfo = (mapObj, item) => {
    setInfoVisible(true);
    loadInfoData(item.gridId);
    // setInfo({
    //   gridName:item.gridName,
    //   gridId:item.gridId,
    //   shopCnt:item.shopCnt,
    //   itmCnt:item.itmCnt,
    //   itmCntRate:item.itmCntRate
    // });
  }

  const createPath = (data) => {
    const map = new window.AMap.Map("container", {
      resizeEnable: true,
      zoom: 13,
      // center: mapCenter
    });
    window.AMap.plugin([
      'AMap.ToolBar',
    ], function () {
      // 在图面添加工具条控件, 工具条控件只有缩放功能
      map.addControl(new window.AMap.ToolBar({
        position: 'RB'
      }));
    });

    window.AMap.plugin([
      'AMap.DistrictSearch',
    ], function() {
      let opts = {
        subdistrict: 2,   //返回下一级行政区
        showbiz:false  //最后一级返回街道信息
      };
      let district = new window.AMap.DistrictSearch(opts);
      district.search('中国', function(status, result) {
        if(status=='complete'){
          let districtCityList = getDistrictCityList(result.districtList[0].districtList);
          let centerItem = districtCityList.filter((v)=>v.name.includes(cityName));
          let mapCenter;
          if (centerItem && centerItem.length > 0) {
            mapCenter = centerItem[0].center;
          } else { //以防没有中心点。
            if (data[0].gridCenterLocation && data[0].gridCenterLocation.start) {
              mapCenter = [parseFloat(data[0].gridCenterLocation.end), parseFloat(data[0].gridCenterLocation.start)];
            } else {
              mapCenter = [parseFloat(data[1].gridCenterLocation.end), parseFloat(data[1].gridCenterLocation.start)];
            }
          }
          map.setCenter(mapCenter);
        }
      });
    });

    renderMap(map, data);
  }

  const getDistrictCityList = (list = [], result = []) => {
    if (!list.length) return [];
    for (let item of list) {
      let node = {
        ...item,
      }
      if (item.level == 'province') {
        getDistrictCityList(item.districtList, result);
      } else if (item.level == 'city') {
        result.push(node);
      }
    }
    return result;
  }

  const loadInfoData = (gridId='37') => { //广安门医院右侧的可以点
    let req = {
      poolId,
      gridId,
      cityId
    }
    try {
      api.getGridAnalyse(req)
        .then((res) => {
          if(res.code=='200') {
            setInfo(res.data);
          }else{
            setInfo({});
            Message.warning(res.msg);
          }
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const loadData = () => {
    let req = {
      cityId,
      poolId
    }
    try {
      api.getGridMap(req)
        .then((res) => {
          setGridData(res.data);
          setInfoVisible(false);
          createPath(res.data);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const colorGroup = [
    {label: '高', color: 'rgba(25,102,255)', opacity: '0.7',value:4},
    {label: '', color: 'rgba(25,102,255)', opacity: '0.5',value:3},
    {label: '', color: 'rgba(25,102,255)', opacity: '0.3',value:2},
    {label: '低', color: 'rgba(25,102,255)', opacity: '0.15',value:1},
  ]

  return (
    <div  className="city-grid">
      {Boolean(info && info.gridId && infoVisible) && <div className="info">
        <div  key={info.gridId}>
          <Icon type="close" size='xs' style={{color: '#ccc', cursor: 'pointer'}} onClick={() => setInfoVisible(false)}/>
          <p className="name">{info.gridName}</p>
          <p className="id">ID:{info.gridId}</p>
          <p className="cnt">覆盖门店数<span>{info.shopCnt}</span></p>
          <p className="cnt">覆盖商品数<span>{info.itmCnt}</span></p>
          <p className="cnt">商品占比<span>{info.itmCntRate}%</span></p>
        </div>
      </div>}
      <div className="color-group">
        <p>覆盖占比</p>
        <>
          {colorGroup.map((v)=>{
            return <div className="item">
              <span className="color-block" style={{backgroundColor:v.color,opacity:v.opacity }} />
              <label className="color-label">{v.label}</label>
            </div>
          })}
        </>
      </div>
      <div style={{height: "570px", width: "100%"}} id="container" ></div>
    </div>
  )
}


