import {Input, Select, Tab, Button, Table, Pagination, Grid, Breadcrumb} from '@alife/next'
import React, {useState, useEffect} from 'react'
import { TreemapChart } from "bizcharts";
import './index.scss';
import * as api from "@/adator/api";
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/treemap';
import 'echarts/lib/component/title';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';

export default function GoodCategoryDistribute(props) {
  const {poolId} = props;
  const [data, setData] = useState([]);
  const [parentLevel, setParentLevel] = useState(1);
  const [parentName, setParentName] = useState('');
  const [parentId, setParentId] = useState('');
  const [firstId,setFirstId] = useState('');
  const [firstName,setFirstName] = useState('');
  const [secondId, setSecondId] = useState('');
  const [secondName, setSecondName] = useState('');
  const [currentLevel, setCurrentLevel] = useState(1);
  const [tabidx, setTabidx] = useState(1);
  const [tableData,setTableData] = useState([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState('');
  const [sortField, setSortField] = useState('');
  const [sortType, setSortType] = useState('');
  const size = 10;
  const columns = [
    {
      title: '三级类目名称', dataIndex: 'category3Name', width: '228px'
    },
    {title: '二级类目名称', dataIndex: 'category2Name', width: '228px'},
    {title: '一级类目名称', dataIndex: 'category1Name', width: '228px'},
    {title: '商品覆盖数', dataIndex: 'itmCnt', width: '228px', sortable: true},
    {
      title: '商品覆盖占比', dataIndex: 'itmCntRate', width: '228px', sortable: true, cell: (value, index, record) => {
        return <span>{value}%</span>
      }
    },
  ]

  const loadTableData = (page=1) =>{
    let req = {
      page,
      pageSize:10,
      query:{
        poolId
      }
    };
    if(sortField){
      req.sort = {};
      req.sort.sortField = sortField;
      req.sort.sortType = sortType;
    }
    try {
      api.getCategoryDistributionDetail(req)
        .then((res) => {
          setTableData(res.data);
          setPage(page);
          setTotal(res.total);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const onSort = (dataIndex, order) => {
    setSortField(dataIndex);
    setSortType(order);
  }

  const onPageChange = (v) =>{
    loadTableData(v);
  }

  useEffect(() => {
    if (tabidx == 1) {
      loadData();
    } else {
      loadTableData();
    }
  }, [tabidx, sortField, sortType])

  const colors = { //todo 颜色还是一样的，没区别
    1:['rgba(91,143,249, 0.56)', 'rgba(91,143,249, 0.4)', 'rgba(91,143,249, 0.32)', 'rgba(91,143,249, 0.24)', 'rgba(91,143,249, 0.16)', 'rgba(91,143,249, 0.08)'],
    2:['rgba(90,216,166, 0.6)','rgba(90,216,166, 0.5)','rgba(90,216,166, 0.4)','rgba(90,216,166, 0.3)','rgba(90,216,166, 0.2)','rgba(90,216,166, 0.1)'],
    3:['rgba(246,189,22, 0.6)','rgba(246,189,22, 0.5)','rgba(246,189,22, 0.4)','rgba(246,189,22, 0.3)','rgba(246,189,22, 0.2)','rgba(246,189,22, 0.1)'],
  }

  const loadData = (categoryId = '', categoryLevel = 1) => {
    let req = {
      categoryLevel,
      poolId
    };
    if(categoryId){
      req.categoryId = categoryId;
    }
    try {
      api.getCategoryDistribution(req)
        .then((res) => {
          let result = res.data.map(({categoryName, itemCnt, categoryLevel, categoryId}) => ({
            name: categoryName,
            value: itemCnt,
            level: categoryLevel,
            id: categoryId
          }))
          setData(result);
          setParentLevel(categoryLevel);
          setCurrentLevel(categoryLevel);
          renderChart(result, categoryLevel, colors[categoryLevel]);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const renderChart = (res,categoryLevel,curColor) =>{
    let chartDom = document.getElementById(`chartTree${categoryLevel}`);
    let myChart = echarts.init(chartDom);
    let option = {
      color: curColor,
      grid:{
        top:'40px',
        left:'40px',
        right:'40px',
        bottom:'40px'
      },
      legend:{
        show:false
      },
      series: [
        {
          breadcrumb:{
            show:false
          },
          leafDepth: 1,
          type: 'treemap',
          label: {
            position: 'insideTopLeft',
            formatter: function (params) {
              let arr;
              if (categoryLevel != 3) {
                arr = [
                  '{name|' + params.name + ' >}',
                  '{hr|}',
                  '{value|' + params.value + '}',
                ];
              } else {
                arr = [
                  '{name|' + params.name + ' }',
                  '{hr|}',
                  '{value|' + params.value + '}',
                ];
              }
              return arr.join('\n');
            },
            rich: {
              // label: {
              //   fontSize: 9,
              //   backgroundColor: 'rgba(0,0,0,0.3)',
              //   color: '#fff',
              //   borderRadius: 2,
              //   padding: [2, 4],
              //   lineHeight: 25,
              //   align: 'right'
              // },
              name: {
                fontSize: 14,
                color: '#333'
              },
              value: {
                fontSize: 24,
                color: '#333',
                fontFamily: 'AlibabaSans102-Medium'
              },
              hr: {
                width: '100%',
                borderColor: 'rgba(255,255,255,0.2)',
                borderWidth: 0,
                height: 0,
                lineHeight: 10
              }
            }
          },
          roam: false,
          // animation:false,
          data: res
        }
      ]
    };
    let newOption = {};
    newOption = Object.assign(newOption, option);
    if (res && res.length == 0) {
      myChart.showLoading({
          text: '暂无数据',
          color: '#ffffff',
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 0.8)',
        }
      );
    }
    myChart.setOption(newOption);
    // option && myChart.setOption(option);
    myChart.on("click", function (param) {
      let {id, level, name} = param.data;
      if(categoryLevel==1){
        loadData(id, level);
        setFirstId(id);
        setFirstName(name);
      }else if (categoryLevel == 2) {
        loadData(id, level);
        setSecondId(id);
        setSecondName(name);
      } else if (categoryLevel == 3) {
        // setParentName(name);
        // setParentId(id);
      }
    })
  }

  const arr = [1,2,3];
  return (
    <div className='good-category-distribute'>
      <div className="title-panel">
        <div className="level-brand">
          <span className="good">商品类目分布</span>
          {(currentLevel ==3 || currentLevel==2) && [<Button text onClick={() => loadData('', 1)}>{firstName}</Button>]}
          {(currentLevel ==3) &&[<label>></label>,<Button text onClick={()=>loadData(firstId, 2)}>{secondName}</Button>]}

        </div>
        <Tab shape="capsule" size="small" onChange={(v) => setTabidx(v)}>
          <Tab.Item title="图表" key={1}></Tab.Item>
          <Tab.Item title="明细" key={2}></Tab.Item>
        </Tab>
      </div>
      {(tabidx == 1) && arr.map((v) => {
        return <div style={{height: '500px',width: '100%', display: (v != parentLevel) ? 'none' : 'block'}} id={`chartTree${v}`}></div>
      })}
      {tabidx == 2 && <div  style={{overflow: 'hidden'}}>
        <Table dataSource={tableData} loading={false} onSort={onSort}  hasBorder={false} primaryKey="poolId">
          {
            columns.map((e, idx) => {
              return <Table.Column {...e} key={idx}/>
            })
          }
        </Table>
        <Pagination onChange={onPageChange} total={total} current={page} pageSize={size} style={{float: 'right', marginTop: '10px'}}/>
      </div>}
    </div>
  )
}


