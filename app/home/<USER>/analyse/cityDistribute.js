import {
  Select,
  Tab,
  Button,
  Table,
  Pagination,
  Message
} from '@alife/next'
import React, {useState, useEffect} from 'react'
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/line';
import 'echarts/lib/chart/bar';
import 'echarts/lib/component/title';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import './index.scss';
import * as api from "@/adator/api";
import {changeEumToObject} from "@/home/<USER>/common";

const TYPEDS = [
  {label: '覆盖门店数', value: 'shopCnt'},
  {label: '覆盖商品数', value: 'itmCnt'},
  {label: '商品占比', value: 'itmCntRate'},
  {label: '覆盖网格数', value: 'gridCnt'},
]

export default function CityDistribute(props) {
  const {poolId} = props;
  const [mainCoordinate, setMainCoordinate] = useState('shopCnt');
  const [subCoordinate, setSubCoordinate] = useState('itmCnt');
  const TYPEDSEum = changeEumToObject(TYPEDS);
  const [data, setData] = useState([]);
  const [cityNameGroup, setCityNameGroup] = useState([]);
  const [mainData, setMainData] = useState([]);
  const [subData, setSubData] = useState([]);
  const [tabidx, setTabidx] = useState(1);
  const [tableData,setTableData] = useState([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState('');
  const [sortField, setSortField] = useState('shopCnt');
  const [sortType, setSortType] = useState('desc');
  const size = 10;
  const columns = [
    {title: '城市名称', dataIndex: 'cityName', width: '190px'},
    // {title: '网格名称', dataIndex: 'gridName', width: '180px'},
    {title: '覆盖网格数', dataIndex: 'gridCnt', width: '180px', sortable: true},
    {title: '覆盖门店数', dataIndex: 'shopCnt', width: '180px', sortable: true},
    {title: '覆盖商品数', dataIndex: 'itmCnt', width: '180px', sortable: true},
    {
      title: '覆盖分布占比', dataIndex: 'itmCntRate', sortable: true, cell: (value, index, record) => {
        return `${value}%`;
      }, width: '180px'
    },
    {
      title: '操作', cell: (value, index, record) => {
        return <div className="opbtns">
          {renderOption(record)}
        </div>
      },
      width: '180px'
    }
  ]

  const renderOption = (record) => {
    const options = [
      <Button text type={'primary'} onClick={()=>{
        location.href = `#/pool/list/grid/${poolId}?cityId=${record.cityId}&cityName=${record.cityName}`;
      }}>分析</Button>,
    ];
    return options
  }

  const onSort = (dataIndex, order) => {
    setSortField(dataIndex);
    setSortType(order);
  }

  const loadData = (mainIndex = mainCoordinate) => {
    let req = {
      mainIndex,
      poolId
    };
    try {
      api.getCityTop10List(req)
        .then((res) => {
          let _data = res.data;
          setData(_data);
          let _subData = _data.map(v => v[subCoordinate]);
          let _mainData = _data.map(v => v[mainCoordinate]);
          let _cityNameGroup = _data.map(v => v.cityName)
          setMainData(_mainData);
          setSubData(_subData);
          setCityNameGroup(_cityNameGroup);
          renderChart(_data,_mainData, _subData, mainIndex, subCoordinate, _cityNameGroup);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const loadTableData = (page=1) =>{
    let req = {
      page,
      pageSize:20,
      query:{
        poolId
      }
    };
    if(sortField){
      req.sort = {};
      req.sort.sortField = sortField;
      req.sort.sortType = sortType;
    }
    try {
      api.getPageQueryCityList(req)
        .then((res) => {
          setTableData(res.data);
          setPage(page);
          setTotal(res.total);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const onPageChange = (v) =>{
    loadTableData(v);
  }

  useEffect(() => { /*表格*/
    if (tabidx == 2) {
      loadTableData();
    }
  }, [tabidx, sortField, sortType])

  useEffect(() => {
    if (tabidx == 1) {
      loadData();
    }
  }, [tabidx,mainCoordinate])

  const renderChart = (_data,_mainData,_subData,_mainCoordinate,_subCoordinate,_cityNameGroup) =>{
    let chartDom = document.getElementById('chart');
    let myChart = echarts.init(chartDom);
    let option = {
      // noDataLoadingOption: {
      //   text: '暂无数据',
      //   effect: 'bubble',
      //   effectOption: {
      //     effect: {
      //       n: 0
      //     }
      //   }
      // },
      title: {
        text: '图表数据为 TOP10 的城市',
        right:40,
        top:0,
        textStyle:{
          fontFamily:'PingFangSC-Regular',
          fontSize: 14,
          color: '#999999',
          fontWeight:'normal'
        }
      },
      color:['#5B8FF9','#5AD8A6'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: function (param) {
          let cityName = param[0].name;
          let item = _data.filter(v => v.cityName == cityName);
          return [
            `${cityName}<br/>`,
            `覆盖门店数: ${item[0].shopCnt}<br/>`,
            `覆盖商品数: ${item[0].itmCnt}<br/>`,
            `商品占比: ${item[0].itmCntRate}%<br/>`,
            `网格数: ${item[0].gridCnt}<br/>`,
          ].join('');
        }
      },
      grid:{
        top:'40px',
        left:'40px',
        right:'40px',
        bottom:'40px'
      },
      toolbox: {
        feature: {
          dataView: { show: true, readOnly: false },
          magicType: { show: true, type: ['line', 'bar'] },
          restore: { show: true },
          saveAsImage: { show: true }
        }
      },
      legend: {
        data: [TYPEDSEum[_mainCoordinate], TYPEDSEum[_subCoordinate]]
      },
      xAxis: [
        {
          type: 'category',
          data:_cityNameGroup,
          axisPointer: {
            type: 'shadow'
          },
          triggerEvent: true
        }
      ],
      yAxis: [
        {
          type: 'value',
          // name: 'Evaporation',
          min: 0,
          // max: 250,
          // interval: 50,
          axisLabel: {
            formatter: '{value}'
          },
          axisLine:{
            lineStyle:{
              color:'#ccc',
              width:1,
              type:'solid'
            }
          }
        },
        {
          type: 'value',
          // name: 'Temperature',
          min: 0,
          // max: 25,
          // interval: 5,
          axisLabel: {
            formatter: '{value}'
          },
          axisLine:{
            lineStyle:{
              color:'#ccc',
              width:1,
              type:'solid'
            }
          }
        }
      ],
      series: [
        {
          name: TYPEDSEum[_mainCoordinate],
          type: 'bar',
          barWidth:'40',
          data:_mainData,
        },
        {
          name: TYPEDSEum[_subCoordinate],
          type: 'line',
          yAxisIndex: 1,
          data:_subData
        }
      ]
    };
    option && myChart.setOption(option);
    if (_data && _data.length == 0) {
      myChart.showLoading({
          text: '暂无数据',
          color: '#ffffff',
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 0.8)',
        }
      );
    }
    myChart.on("click", function (param) {
      let cityName = '';
      if(param.componentType=='series'){
        cityName = param.name;
      } else if(param.componentType=='xAxis'){
        cityName = param.value;
      }
      let cityId = '';
      if (_data.length > 0) {
        cityId = _data.filter(o => o.cityName == cityName)[0].cityId;
        props.history.push(`/pool/list/grid/${poolId}?cityId=${cityId}&cityName=${cityName}`);
      }
    })
  }

  const setCoordinate = (v, type) => { // type为1：主坐标，2：辅坐标
    if (type == 1) {
      if (v == subCoordinate) {
        Message.warning('主坐标和辅坐标不能一样');
        return;
      }
      setMainCoordinate(v);
      setSortField(v);
      setSortType('desc');
      // setMainData(data.map(m => m[v]));
      loadData(v);
    } else {
      if (v == mainCoordinate) {
        Message.warning('主坐标和辅坐标不能一样');
        return;
      }
      setSubCoordinate(v);
      let _subData = data.map(m => m[v]);
      setSubData(_subData);
      renderChart(data,mainData, _subData, mainCoordinate, v, cityNameGroup);
    }
  }

  const loadFirst = () =>{
    if(data && data.length>0) {
      let {cityId,cityName} = data[0];
      location.href = `#/pool/list/grid/${poolId}?cityId=${cityId}&cityName=${cityName}`;
    }
  }

  return (
    <div className="city-distribute">
      <div className="title-panel">
        <span>城市分布</span>
        <Tab shape="capsule" size="small" onChange={(v) => setTabidx(v)}>
          <Tab.Item title="图表" key={1}></Tab.Item>
          <Tab.Item title="明细" key={2}></Tab.Item>
        </Tab>
        <Button onClick={loadFirst} style={{marginRight:'16px'}}>城市分析</Button>
      </div>
      {tabidx == 1 && <div className="type-panel">
        <Select dataSource={TYPEDS} style={{width: '120px'}} value={mainCoordinate} onChange={(value) => setCoordinate(value, 1)}/>
        <Select dataSource={TYPEDS} style={{width: '120px'}} value={subCoordinate} onChange={(value) => setCoordinate(value, 2)}/>
      </div>}
      {(tabidx==1) && <div id="chart" style={{height:'500px'}}></div>}
      {tabidx == 2 && <div style={{overflow: 'hidden'}}>
        <Table dataSource={tableData} loading={false} onSort={onSort}  hasBorder={false} primaryKey="poolId">
          {
            columns.map((e, idx) => {
              return <Table.Column {...e} key={idx}/>
            })
          }
        </Table>
        <Pagination onChange={onPageChange} total={total} current={page} pageSize={size} style={{float: 'right', marginTop: '10px'}}/>
      </div>}
    </div>
  )
}


