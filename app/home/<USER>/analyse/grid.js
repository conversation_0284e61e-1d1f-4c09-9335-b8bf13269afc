import {Select, Tab} from '@alife/next'
import React, {useState, useEffect} from 'react'
import {logTimeComponent, goldLog, track} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link,withRouter} from 'react-router-dom'
import './index.scss';
import CityGrid from "@/home/<USER>/analyse/cityGrid";
import CityTable from "@/home/<USER>/analyse/cityTable";
import * as api from "@/adator/api";
import {getQueryString} from "@/utils/others";

function grid({location, match, history}) {
  const {poolId} = match.params;
  const [tabidx, setTabidx] = useState(1);
  const [cityList,setCityList] = useState([]);
  const [cityId,setCityId] = useState(getQueryString("cityId"));
  const [cityName,setCityName] = useState(getQueryString("cityName"));

  useEffect(() => {
    loadCityList();
    // createPath();
  }, [])

  let searchTimeout;
  const searchCityList = (value) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      loadCityList(value);
    }, 1000);
  }

  const loadCityList = async () => {
    try {
      let request = api.getAllCityList;
      let res = await request(poolId);
      let cityList = res.data.map(item => ({
        label: item.regionName,
        value: item.regionId
      }))
      setCityList(cityList);
    } catch (error) {
      api.onRequestError(error)
    }
  }

  const setCity = (a,b,c) =>{
    setCityId(c.value);
    setCityName(c.label);
    // location.href = `#/pool/list/grid/5723?cityId=${id}`
  }

  const skip = () =>{
    history.go(-1);
  }

  return (
    <div className="analyse-page">
      <div className="analyse-container">
        <div className="title-panel grid">
          <div className="left">
            <a onClick={skip} href="javascript:void(0)">{'< 返回'}</a>
            <Select dataSource={cityList} style={{width:'200px'}} value={cityId} hasClear={true} showSearch onSearch={searchCityList} onChange={(a,b,c)=>setCity(a,b,c)}></Select>
            {/*<CascaderSelect*/}
            {/*  expandTriggerType={'hover'}*/}
            {/*  autoFocus*/}
            {/*  value={cityId}*/}
            {/*  onChange={setCity}*/}
            {/*  dataSource={cityList}/>*/}
          </div>
          <Tab shape="capsule" size="small" onChange={(v) => setTabidx(v)}>
            <Tab.Item title="地图模式" key={1}></Tab.Item>
            <Tab.Item title="数据明细" key={2}></Tab.Item>
          </Tab>
        </div>
        {/*{tabidx == 1 && <CityGrid3 poolId={poolId}/>}*/}
        {tabidx == 1 && <CityGrid poolId={poolId} cityId={cityId} cityName={cityName} />}
        {tabidx == 2 && <CityTable poolId={poolId} cityId={cityId} />}
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(grid), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const gridPage = permissionAccess(LogTimePutInPage)



