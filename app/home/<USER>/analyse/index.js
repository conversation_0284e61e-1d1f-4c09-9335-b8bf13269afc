import {Input, Select, Tab, Button, Table, Pagination, Grid, Breadcrumb, Message} from '@alife/next'
import React, {useState, useEffect} from 'react'
import {logTimeComponent, goldLog} from '@/utils/aplus';
import {permissionAccess} from '@/components/PermissionAccess';
import {Link,withRouter} from 'react-router-dom'
import {BreadcrumbTips} from "@/home/<USER>/comps";
import {PoolPageBase} from "@/home/<USER>/common";
import './index.scss';
import CityDistribute from './cityDistribute';
import GoodCategoryDistribute from './goodCategoryDistribute';
import CircleGroup from './circleGroup';
import * as api from "@/adator/api";
import {FORMAT, formatTimeStamp} from "@/utils/time";

function analyse({location, match, history}) {
  const breadcrumbList = [
    {"title": '选品选商', link: "#/pool/list"},
    {"title": '选品集名称', link: "#/pool/list"},
    {"title": '选品洞察', link: ""}
  ]
  const [tabidx, setTabidx] = useState(1);
  // console.log(location);
  // console.log(match);
  // console.log(history);
  const {poolId} = match.params;
  const [time, setTime] = useState('');

  useEffect(() => {
    loadTimeData();
  }, [])


  const loadTimeData = () => {
    try {
      api.getPickAnalyseDataVersion()
        .then((res) => {
          setTime(res);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  return (
    <div className="analyse-page">
      <BreadcrumbTips list={breadcrumbList} />
      <div className="analyse-container">
        <Tab shape="pure" onChange={setTabidx} activeKey={tabidx}>
          <Tab.Item title="选品分析" key={1}></Tab.Item>
          {/*<Tab.Item title="投放分析" key={2}></Tab.Item>*/}
        </Tab>
        {time!='' && <span className="time">数据更新时间：{formatTimeStamp(time, FORMAT.TIMETwo)}</span>}
        <CityDistribute history={history} poolId={poolId} />
        <GoodCategoryDistribute poolId={poolId} />
        <CircleGroup poolId={poolId}  />
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(analyse), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const analysePage = permissionAccess(LogTimePutInPage)



