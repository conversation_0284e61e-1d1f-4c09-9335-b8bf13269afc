import {Select, Button, Table, Pagination, Balloon, Grid, NumberPicker} from '@alife/next'
import React, {useState, useEffect} from 'react'
const {Row,Col} = Grid;
import './index.scss';
import * as api from "@/adator/api";
import {RangeNumberInput} from "@/home/<USER>/common/components";
import {getQueryString} from "@/utils/others";

export default function CityTable(props) {
  const queryCityId = getQueryString("cityId");
  const cityId = (queryCityId == props.cityId) ? queryCityId : props.cityId;
  const [tableData,setTableData] = useState([]);
  const [gridList,setGridList] = useState([]);
  const [balloonVisible,setBalloonVisible] = useState(false);
  const [queryGroup, setQueryGroup] = useState({poolId: props.poolId, cityId});
  const [filter, setFilter] = useState({});
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState('');
  const [sortField, setSortField] = useState('');
  const [sortType, setSortType] = useState('');
  const size = 20;
  const columns = [
    {
      title: '网格名称', dataIndex: 'gridName', width: '228px'
    },
    {title: '覆盖门店数', dataIndex: 'shopCnt', width: '228px'},
    {title: '覆盖商品数', dataIndex: 'itmCnt', width: '228px'},
    {
      title: '商品覆盖占比', dataIndex: 'itmCntRate', width: '228px', sortable: true, cell: (value, index, record) => {
        return <span>{value}%</span>
      }
    },
  ]

  const loadTableData = (page=1,query) => {
    let req = {
      page,
      pageSize:20,
      query
    };
    if(sortField){
      req.sort = {};
      req.sort.sortField = sortField;
      req.sort.sortType = sortType;
    }
    try {
      api.getPageQueryGridList(req)
        .then((res) => {
          setTableData(res.data);
          setPage(page);
          setTotal(res.total);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  const onSort = (dataIndex, order) => {
    setSortField(dataIndex);
    setSortType(order);
  }

  const onPageChange = (v) => {
    loadTableData(v, queryGroup);
  }

  useEffect(() => {
    if (queryGroup.cityId != cityId) { //切城市的时候
      queryGroup.cityId = cityId;
      setQueryGroup(queryGroup);
    }
    loadTableData(page, queryGroup);
  }, [sortField, sortType, cityId])

  useEffect(() => {
    loadGridList();
  }, [cityId])

  const loadGridList = (regionName='') => {
    let req = {
      regionLevel: 3,
      regionId: cityId,
      regionName
    };
    try {
      api.getAllGridList(req)
        .then((res) => {
          let gridList = res.data.map(item => ({
            label: item.regionName,
            value: item.regionId
          }))
          setGridList(gridList);
        })
    } catch (error) {
      api.onRequestError(error);
    }
  }

  let searchTimeout;
  const searchGrid = (value) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(() => {
      loadGridList(value);
    }, 1000);
  }

  const changeInput = (value, field) => {
    const data = {...filter};
    data[field] = value;
    setFilter(data);
  }

  const changeGrid = (value) => {
    const data = {...queryGroup};
    data.gridId = value;
    setQueryGroup(data);
    loadTableData(1,data);
  }

  const searchData = () =>{
    const data = {
      ...queryGroup
    };
    for (let o in filter) {
      data[o] = filter[o];
    }
    setQueryGroup(data);
    loadTableData(1,data);
    setBalloonVisible(false);
  }

  const resetData = () =>{
    const data = {
      cityId:queryGroup.cityId,
      poolId:queryGroup.poolId
    };
    if(queryGroup.gridId){
      data.gridId = queryGroup.gridId;
    }
    setQueryGroup(data);
    setFilter({});
    loadTableData(page,data);
    setBalloonVisible(false);
  }

  const filterList = [
    {label: '覆盖门店数', field: 'shopCntRange', precision: 0, step: 1},
    {label: '覆盖商品数', field: 'itmCntRange', precision: 0, step: 1},
    {label: '商品占比', field: 'itmCntRateRange', precision:2, step: 0.01},
  ]
  return (
    <div>
      <div className="title-panel table">
        <div className="left">
          <Select className="grid-list" dataSource={gridList} hasClear={true} showSearch onSearch={searchGrid} onChange={changeGrid}></Select>
        </div>
        <Balloon
          triggerType="click"
          title="条件筛选"
          visible={balloonVisible}
          onClose={()=>setBalloonVisible(false)}
          trigger={ <Button className="price-filter" onClick={()=>setBalloonVisible(true)}>条件筛选</Button>}
          align="bl"
        >
          {filterList.map((v)=>{
            return <Row style={{marginBottom:'15px'}}>
              <Col><p style={{margin:'8px 10px 0 0'}}>{v.label}</p></Col>
              <Col>
                <RangeNumberInput precision={v.precision} step={v.step} onChange={(value) => changeInput(value, v.field)} name={v.field} value={filter[v.field]} />
              </Col>
            </Row>
          })}
          <div style={{display:'inline-block',float:'right'}}>
            <Button onClick={resetData}>重置</Button>
            <Button type='primary' onClick={searchData} style={{marginLeft:'5px'}}>查询</Button>
          </div>
        </Balloon>

      </div>
      <div style={{overflow: 'hidden',padding:'0 21px',backgroundColor:'#fff'}}>
        <Table dataSource={tableData} loading={false} onSort={onSort}  hasBorder={false} primaryKey="poolId">
          {
            columns.map((e, idx) => {
              return <Table.Column {...e} key={idx}/>
            })
          }
        </Table>
        <Pagination onChange={onPageChange} total={total} current={page} pageSize={size} style={{float: 'right', marginTop: '10px'}}/>
      </div>
    </div>
  )
}


