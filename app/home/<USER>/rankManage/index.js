import React, { Fragment, useEffect, useState, useCallback } from "react";
import { isAllNumber } from "@/utils/validators";
import Clipboard from "clipboard";
import {
  Form,
  Checkbox,
  Radio,
  Select,
  Input,
  Button,
  Table,
  Message,
  Dialog,
  Pagination,
  Tab,
  Divider,
  Dropdown,
  Icon,
  Menu,
  Grid,
  Balloon,
  Field,
} from "@alife/next";
import moment from "moment";
import { StatusLabel } from "@/comps/Label";
import "./style.scss";
import * as api from "@/utils/api";
import { goldLog, logTimeComponent, track } from "@/utils/aplus";
import { AccessBtn, DialogBtn } from "@/components/Button";
import { formatTimeStamp, FORMAT } from "@/utils/time";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { queryUserByKeyWord } from "@/containers/channel/market/request";
import { onRequestError } from "@/utils/api";
import { dateRender } from "../../poolPage/common/index";
import { debounce } from "lodash";
import * as apiL from "@/adator/api";
const { Row, Col } = Grid;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};
const PAGE_SIZE = 10;
const stateEnum = [
  {
    label: "全部",
    value: "",
  },
  {
    label: "待生效",
    value: 1,
    type: "warning",
  },
  {
    label: "生效中",
    value: 2,
    type: "success",
  },
  {
    label: "已过期",
    value: 3,
    type: "filed",
  },
  {
    label: "已下线",
    value: 4,
    type: "filed",
  },
];

export function statusRender(status) {
  console.log("🚀 ~ file: index.js:70 ~ statusRender ~ status:", status);

  let item = stateEnum.filter((v) => v.value === status)[0] || {};

  // let labelText = (item && item.length > 0) ? item[0].label : '';
  return (
    <div style={{ paddingLeft: "10px" }}>
      <StatusLabel text={item.label} type={item.type}></StatusLabel>
    </div>
  );
}

function rankManage({ history }) {
  const clipboard = new Clipboard("#copy");
  const [dataSource, setDataSource] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [size, setSize] = useState(15);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState({});
  const [roleDataSource, setRoleDataSource] = useState([]); //员工工号
  const [canSetSchema, setCanSetSchema] = useState([]);
  const field = Field.useField({
    onChange: (name, value) => {
      query[name] = value;
      setQuery(query);
    },
  });
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      width: "180px",
      cell: (value, index, record) => {
        return (
          <div className="pool-info">
            <p className="pool-id">
              ID：{value}
              <span className="btn-copy" id="copy" data-clipboard-text={value}>
                复制
              </span>
            </p>
          </div>
        );
      },
    },
    {
      title: "配置名称",
      dataIndex: "configName",
      width: "150px",
      cell: (value, index, record) => {
        return (
          <div className="pool-info">
            <div className="fontWeight">{value}</div>
          </div>
        );
      },
    },
    {
      title: "榜单名称",
      dataIndex: "name",
      width: "100px",
      cell: (value, index, record) => {
        return (
          <div className="pool-info">
            <Button
              className="pool-name"
              text
              onClick={() => onView(record)}
              title={value}
            >
              {value}
            </Button>
          </div>
        );
      },
    },
    { title: "状态", dataIndex: "state", cell: statusRender, width: "150px" },
    {
      title: "生效时间",
      dataIndex: "beginTime",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      cell: dateRender,
      width: "170px",
    },
    { title: "创建人", dataIndex: "creatorName", width: "130px" },
    {
      title: "操作",
      cell: (value, index, record) => {
        return <div className="opbtns">{renderNewOption(record)}</div>;
      },
      width: "200px",
    },
  ];

  const onPageChange = (newPage) => {
    getPoolList(newPage, query);
  };

  const onPageSizeChange = (newSize) => {
    getPoolList(page, query, newSize);
  };

  useEffect(() => {
    getPoolList(1, query);
    getSetSchemaAuthority();
  }, []);

  /**检测id格式 */
  const checkId = function (rule, value, callback) {
    const errors = isAllNumber(value);
    if (errors.length && value) {
      callback("格式错误");
    } else {
      callback();
    }
  };

  const onSearchUser = useCallback(
    debounce((keyword) => {
      if (keyword) {
        var reg = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]+$");
        if (keyword != "" && !reg.test(keyword)) {
          Message.error("不能输入特殊字符");
          return;
        }
        queryUserByKeyWord(keyword)
          .then((data) => {
            const dataSource = data.map((v) => ({
              value: v.empId,
              label: `${v.lastName}_${v.empId}`,
              record: v,
            }));
            setRoleDataSource(dataSource);
          })
          .catch(onRequestError);
      }
    }, 800),

    [roleDataSource]
  );

  /**
   * 池子查询列表
   **/
  const getPoolList = (page = 1, query, newSize) => {
    console.log(
      "🚀 ~ file: index.js:249 ~ getPoolList ~ query:",
      query,
      newSize,
      size
    );
    setIsLoading(true);
    // 补充参数
    try {
      let request = api.getRankList;
      request({
        size: newSize ? newSize : size,
        page: page,
        query,
      }).then((resp) => {
        setDataSource(resp.rankList);
        setTotal(resp.totalCount);
        setPage(page);
        setSize(newSize ? newSize : size);
        setIsLoading(false);
      });
    } catch (error) {
      setIsLoading(false);
      api.onRequestError(error);
    }
  };
  // 查询
  const searchPoolList = () => {
    getPoolList(1, query);
  };

  // 重置表单
  const resetPoolList = () => {
    field.reset();
    field.setValues({});
    Object.keys(query).filter((key) => {
      delete query[key];
    });
    setQuery({});
  };

  const getPermission = async (id) => {
    try {
      let resp = await api.checkRankPool(id); //todo 换掉接口
      let { rediectUrl } = resp;
      return rediectUrl;
    } catch (error) {
      onRequestError(error);
    }
  };

  const onView = (record) => {
    history.push(`/rankManage/list/view/${record.id}`);
  };

  const onEdit = (record) => {
    history.push(`/rankManage/list/create/${record.id}`);
  };

  const onCopy = (record) => {
    history.push(`/rankManage/list/create/-${record.id}`);
  };

  const onOffLine = (record) => {
    Dialog.confirm({
      title: "提示",
      content: "榜单策略处于有效期内，是否下线?",
      onOk: async () => {
        let request = api.getRankOffline;
        request({ boardId: record.id })
          .then((e) => {
            Message.success("操作成功");
            getPoolList(1, query);
          })
          .catch(api.onRequestError);
      },
    });
  };

  const renderNewOption = (record) => {
    let { state } = record;
    let divider = <Divider direction="ver" />;
    let detailEle = (
      <Button text={true} onClick={() => onView(record)}>
        查看
      </Button>
    );
    let editEle = (
      <AccessBtn
        getPermission={() => getPermission(record.id)}
        btnText={"编辑"}
        callback={() => onEdit(record)}
      />
    );
    let copyEle = (
      <AccessBtn
        getPermission={() => getPermission(record.id)}
        btnText={"复制"}
        callback={() => onCopy(record)}
      />
    );
    let offLineEle = (
      <AccessBtn
        getPermission={() => getPermission(record.id)}
        btnText={"下线"}
        callback={() => onOffLine(record)}
      />
    );
    let options;
    switch (state) {
      case 1:
      case 2:
      case 3:
        options = (
          <>
            {copyEle}
            {divider}
            {detailEle}
            {divider}
            {editEle}
            {divider}
            {offLineEle}
          </>
        );
        break;
      case 4:
        options = (
          <>
            {copyEle}
            {divider}
            {detailEle}
          </>
        );
        break;
      default:
        break;
    }
    return options;
  };

  clipboard.on("success", function (e) {
    console.log(e);
    Message.success("复制成功");
  });
  clipboard.on("error", function (e) {
    console.log(e);
  });

  const getSetSchemaAuthority = async () => {
    try {
      let resp = await apiL.queryRoleValidate("set_schema");
      setCanSetSchema(resp === 'YES')
    } catch (error) {
      apiL.onRequestError(error);
    }
  };

  return (
    <div className="pool-list">
      <Form className="filter" field={field}>
        <div className="top">
          <span className="pool-title">榜单列表</span>
          <Button
            type="primary"
            className="btn-create-pool"
            onClick={() => {
              history.push("/rankManage/list/create");
            }}
          >
            新建榜单
          </Button>
        </div>
        <Row>
          <Col span={6}>
            <FormItem label="ID " {...formItemLayout} validator={checkId}>
              <Input name="id" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="名称" {...formItemLayout}>
              <Input name="name" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="榜单状态" {...formItemLayout}>
              <Select
                dataSource={stateEnum}
                name="state"
                style={{ width: "100%" }}
                defaultValue={""}
              />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="创建人" {...formItemLayout}>
              <Select
                maxTagCount={10}
                name="creatorName"
                aria-label="tag mode"
                mode="tag"
                filterLocal={false}
                hasArrow={false}
                onSearch={(value) => {
                  onSearchUser(value);
                }}
                dataSource={roleDataSource}
                style={{ width: "100%" }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col
            span={24}
            style={{
              marginBottom: "20px",
              display: "flex",
              justifyContent: "end",
            }}
          >
            <Button
              type="secondary"
              onClick={searchPoolList}
              style={{ marginLeft: "95px" }}
            >
              查询
            </Button>
            <Button className="btn_reset" onClick={resetPoolList}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>

      <div className="table-panel">
        <Table
          dataSource={dataSource}
          loading={isLoading}
          hasBorder={false}
          primaryKey="id"
        >
          {columns.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
        <Pagination
          onChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          total={total}
          current={page}
          pageSize={size}
          pageSizeSelector="dropdown"
          pageSizeList={[10, 15, 20]}
          popupProps={{ align: "bl tl" }}
          style={{ float: "right", marginTop: "10px" }}
        />
      </div>
    </div>
  );
}

export const LogTimeRankManagePage = logTimeComponent(
  withRouter(rankManage),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const RankManagePage = permissionAccess(LogTimeRankManagePage);
