.expert-pool-list {
  margin: 12px;
  .filter {
    background-color: #fff;
    margin-bottom: 10px;
    .top {
      overflow: hidden;
    }
    .next-row {
      margin-right: 20px;
    }
    .pool-title {
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #363636;
      line-height: 20px;
      margin: 20px;
      display: inline-block;
    }
    .btn-create-pool {
      float: right;
      margin: 20px 20px 20px 0;
    }
    .next-btn {
      margin-left: 10px;
    }
  }
  .table-panel {
    background-color: #fff;
    padding: 20px;
    overflow: hidden;
    .next-table {
      .poolId {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
      }
      th .next-table-cell-wrapper {
        padding: 10px 12px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #666666;
      }
      td .next-table-cell-wrapper {
        padding: 10px 12px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #666666;
        overflow: auto;
      }
      .item-num {
        text-align: right;
        display: block;
        margin-right: 10px;
      }
      .next-table-cell.first .next-table-cell-wrapper {
        overflow: inherit;
      }
    }
    .opbtns {
      .next-btn {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #333333;
        &:hover {
          color: #ff7000;
        }
      }
    }
    .pool-info {
      .pool-name {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #333333;
        line-height: 20px;

        &:hover {
          color: #ff7000;
          text-decoration: underline;
        }
      }
      p {
        margin: 0;
        line-height: 20px;
        padding: 0;
        &.pool-id {
          font-family: PingFangSC-Regular !important;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          .btn-copy {
            cursor: pointer;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #999999;
            margin-left: 4px;
          }
        }
      }
    }
    .item-notice {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #999999;
      &.no {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #ff7000;
      }
    }
  }
}
.select-expertType {
  .next-row {
    .next-col {
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #ffffff;
      border: 1px solid #ebebeb;
      border-radius: 4px;
      margin-right: 5px;
      height: 76px;
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #333333;
      padding-left: 12px;
      cursor: pointer;

      &:hover {
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
      }
      &.active {
        background: rgba(255, 112, 0, 0.04);
        border: 1px solid rgba(255, 112, 0, 0.4);
      }
    }
  }
}
.icon_city {
  height: 28px;
  width: 28px;
  border-radius: 14px;
  background: #0cc1c1;
  font-family: PingFangSC-Semibold;
  font-size: 14px;
  color: #ffffff;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  display: inline-block;
  margin: 0 5px;
  &.small {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
  }
}
.icon_general {
  height: 28px;
  width: 28px;
  border-radius: 14px;
  background-color: #4494f9;
  font-family: PingFangSC-Semibold;
  font-size: 14px;
  color: #ffffff;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  display: inline-block;
  margin: 0 5px;
  &.small {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
  }
}
