import React, { useState } from "react";
import { Dialog, Grid, Message } from "@alife/next";
import "./style.scss";

const { Row, Col } = Grid;
/*
 * 选类型
 * */

export default function SelectType(props) {
  console.log(props);
  const [poolType, setPoolType] = useState("102");

  const onOk = () => {
    if (poolType != "") {
      props.setVisible(false);
      props.history.push(`/pool/expertPool/create/${poolType}`);
    } else {
      Message.warning("请选择专家池类型");
    }
  };

  const groups = [
    { title: "创建城市场景底池", icon: "城", value: "102", disable: false },
    { title: "创建通用场景底池", icon: "通", value: "101", disable: false },
  ];
  return (
    <Dialog
      title="选择专家池类型"
      visible={props.visible}
      className="select-expertType"
      style={{ width: 540 }}
      okProps={{ children: "下一步" }}
      cancelProps={{ children: "取消" }}
      footerActions={["cancel", "ok"]}
      onOk={onOk}
      onCancel={() => props.setVisible(false)}
      onClose={() => props.setVisible(false)}
    >
      <Row>
        {groups.map((item) => {
          return (
            <Col
              key={item.value}
              className={`${poolType == item.value ? "active" : ""}`}
              onClick={() => {
                setPoolType(item.value);
              }}
            >
              <em className={`icon_${item.value == 101 ? "general" : "city"}`}>
                {item.icon}
              </em>
              {item.title}
            </Col>
          );
        })}
      </Row>
    </Dialog>
  );
}
