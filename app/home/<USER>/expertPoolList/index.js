import React, { Fragment, useCallback, useEffect, useState } from "react";
import SelectType from "./selectType";
import Clipboard from "clipboard";
import {
  Form,
  Checkbox,
  Radio,
  Select,
  Input,
  Button,
  Table,
  Message,
  Dialog,
  Pagination,
  Tab,
  Divider,
  Dropdown,
  Icon,
  Menu,
  Grid,
  Balloon,
  Field,
  CascaderSelect,
} from "@alife/next";
import { TimeRangePicker } from "@/components/TimeRangePicker";
import moment from "moment";
import { PageWrapper } from "@/comps/PageWrapper";
import "./style.scss";
import * as api from "@/adator/api";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { queryUserByKeyWord } from "@/containers/channel/market/request";
import { debounce } from "lodash";
import {
  dateRender,
  expertStatusEnum,
  getExpertPermission,
} from "@/home/<USER>/common";
import { StatusLabel } from "@/comps/Label";
import { AccessBtn } from "@/components/Button";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { onRequestError } from "@/utils/api";
import { exportStateMap } from "../common";
const FormItem = Form.Item;
const { Row, Col } = Grid;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};

function expertPoolList(props) {
  const [query, setQuery] = useState({}); // 请求数据
  const [dataSource, setDataSource] = useState([]); //列表数据
  const [classification, setClassification] = useState([]); //场景分类
  const [roleDataSource, setRoleDataSource] = useState([]); //员工工号
  const [isLoading, setIsLoading] = useState(false);
  const [size, setSize] = useState(10);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [visible, setVisible] = useState(false);
  const [submitDisabled, setSubmitDisabled] = useState(false); // 禁止重复点击

  const clipboard = new Clipboard("#copy");
  const columns = [
    {
      title: "",
      lock: "left",
      cell: (value, index, record) => {
        return (
          <em
            className={`icon_${
              record.basePoolType == 102 ? "city" : "general"
            } small`}
            style={{ margin: "0 5px" }}
          >
            {record.basePoolType == 102 ? "城" : "通"}
          </em>
        );
      },
      width: "40px",
    },
    {
      title: "状态",
      dataIndex: "state",
      cell: (value, index, record) => {
        let item = expertStatusEnum.filter((v) => v.value === record.state);
        return (
          <div style={{ paddingLeft: "0px" }}>
            <StatusLabel
              text={item[0].label}
              type={
                (exportStateMap[record.state] &&
                  exportStateMap[record.state].style) ||
                "error"
              }
            ></StatusLabel>
          </div>
        );
      },
      width: "120px",
    },
    {
      title: "名称/ID",
      lock: "left",
      dataIndex: "basePoolName",
      width: "180px",
      cell: (value, index, record) => {
        let name = value.length > 10 ? `${value.slice(0, 10)}...` : value;
        return (
          <div className="pool-info">
            <Button
              className="pool-name"
              text
              onClick={() => onView(record)}
              title={value}
            >
              {name}
              <div>
                <Icon
                  type="arrow-right"
                  size="xxs"
                  style={{ marginTop: "1px" }}
                />
              </div>
            </Button>
            <p className="pool-id">
              ID：{record.sceneBaseId}
              <span
                className="btn-copy"
                id="copy"
                data-clipboard-text={record.sceneBaseId}
              >
                复制
              </span>
            </p>
          </div>
        );
      },
    },
    { title: "底池数据源名称", dataIndex: "sourcePoolName", width: "160px" },

    { title: "场景名称", dataIndex: "catPath", width: "200px" },

    { title: "创建人", dataIndex: "creator", width: "100px" },
    {
      title: "创建时间",
      dataIndex: "createAt",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "操作",
      lock: "right",
      cell: (value, index, record) => {
        return <div className="opbtns">{renderNewOption(record)}</div>;
      },
      width: "150px",
    },
  ];

  clipboard.on("success", function (e) {
    console.log(e);
    Message.success("复制成功");
  });
  clipboard.on("error", function (e) {
    console.log(e);
  });

  const field = Field.useField({
    onChange: (name, value) => {
      if (!value && name == "basePoolName") {
        delete query[name];
        setQuery(query);
      } else {
        query[name] = value;
        setQuery(query);
      }
    },
  });

  useEffect(() => {
    (async () => {
      await fetchSceneCatTree();
    })();
    getPoolList(1, query);
  }, []);

  /*获取场景分类*/
  const fetchSceneCatTree = async () => {
    try {
      if (classification.length <= 0) {
        let request = api.queryNewSceneCatTree;
        let resp = await request();
        setClassification(resp.data.data.data);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 查询
  const searchPoolList = () => {
    getPoolList(1, query);
  };

  const onPageSizeChange = (size) => {
    setSize(size);
    getPoolList(1, query, size);
  };

  // 页面改变
  const onPageChange = (page) => {
    setPage(page);
    getPoolList(page, query);
  };

  // 重置表单
  const resetPoolList = () => {
    field.reset();
    field.setValues({});
    Object.keys(query).filter((key) => {
      delete query[key];
    });
    setQuery({});
  };

  /**
   * 池子查询列表
   **/
  const getPoolList = (page = 1, query, newSize) => {
    setIsLoading(true);
    // 补充参数
    try {
      let request = api.pageScenePoolList;
      let param = {
        ...query,
      };
      request({
        pageSize: newSize ? newSize : size,
        pageIndex: page,
        ...param,
      }).then((resp) => {
        setDataSource(resp.data.data.data);
        setTotal(resp.data.data.totalCount);
        setPage(page);
        setIsLoading(false);
      });
    } catch (error) {
      setIsLoading(false);
      api.onRequestError(error);
    }
  };

  const onSearchUser = useCallback(
    debounce((keyword) => {
      if (keyword) {
        var reg = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]+$");
        if (keyword != "" && !reg.test(keyword)) {
          Message.error("不能输入特殊字符");
          return;
        }
        queryUserByKeyWord(keyword)
          .then((data) => {
            const dataSource = data.map((v) => ({
              value: v.empId,
              label: `${v.lastName}_${v.empId}`,
              record: v,
            }));
            setRoleDataSource(dataSource);
          })
          .catch(onRequestError);
      }
    }, 800),

    [roleDataSource]
  );

  // 重新发布
  const onRePublish = async (record) => {
    setSubmitDisabled(true);
    let request = api.republishScenePool;
    try {
      let resp = await request(record.sceneBaseId);
      if (resp.data.code == "200" && resp.data.data.success) {
        Message.success("操作成功");
        getPoolList(1, query);
        setSubmitDisabled(false);
      } else {
        Message.error(resp.data.data.errMessage);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 发布
  const onPublish = async (record) => {
    setSubmitDisabled(true);
    let request = api.publishScenePool;
    try {
      let resp = await request(record.sceneBaseId);
      if (resp.data.code == "200" && resp.data.data.success) {
        Message.success("操作成功");
        getPoolList(1, query);
        setSubmitDisabled(false);
      } else {
        Message.error(resp.data.data.errMessage);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 编辑
  const onEdit = (record) => {
    props.history.push(
      `/pool/expertPool/create/${record.basePoolType}/${record.sceneBaseId}`
    );
  };

  // 下线
  const onOffLine = async (record) => {
    Dialog.confirm({
      title: "提示",
      content: "确定下线此条数据么?",
      onOk: async () => {
        // 新池子
        try {
          let request = api.offlineScenePool;
          let resp = await request(record.sceneBaseId);
          if (resp.data.code == "200" && resp.data.data.success) {
            Message.success("操作成功");
            getPoolList(1, query);
          } else {
            Message.error(resp.data.data.errMessage);
          }
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  // 查看
  const onView = (record) => {
    props.history.push(`/pool/expertPoolDetail/${record.sceneBaseId}/0`);
  };

  const renderNewOption = (record) => {
    let { state, createMode = "", poolType } = record;
    let divider = <Divider direction="ver" />;
    let detailEle = (
      <Button text={true} onClick={() => onView(record)}>
        查看
      </Button>
    );
    let editEle = (
      <AccessBtn
        getPermission={() =>
          getExpertPermission({ bizType: "scene", bizId: record.sceneBaseId })
        }
        btnText={"编辑"}
        callback={() => onEdit(record)}
      />
    );
    let offLineEle = (
      <AccessBtn
        getPermission={() =>
          getExpertPermission({ bizType: "scene", bizId: record.sceneBaseId })
        }
        btnText={"下线"}
        callback={() => onOffLine(record)}
      />
    );
    let rePublishEle = (
      <AccessBtn
        getPermission={() =>
          getExpertPermission({ bizType: "scene", bizId: record.sceneBaseId })
        }
        disabled={submitDisabled}
        btnText={"重新发布"}
        callback={() => onRePublish(record)}
      />
    );
    let publishEle = (
      <AccessBtn
        getPermission={() =>
          getExpertPermission({ bizType: "scene", bizId: record.sceneBaseId })
        }
        disabled={submitDisabled}
        btnText={"发布"}
        callback={() => onPublish(record)}
      />
    );
    let options;
    switch (state) {
      case 0 /*已下线：查看*/:
        options = <>{detailEle}</>;
        break;
      case 1 /*已发布：查看 编辑 下线*/:
        options = (
          <>
            {detailEle}
            {divider}
            {editEle}
            {divider}
            {offLineEle}
          </>
        );
        break;
      case 2 /*发布中：查看 编辑 下线*/:
        options = (
          <>
            {detailEle}
            {divider}
            {editEle}
            {divider}
            {offLineEle}
          </>
        );
        break;
      case 3 /*发布失败：查看 重新发布*/:
        options = (
          <>
            {detailEle}
            {divider}
            {editEle}
            {divider}
            {rePublishEle}
          </>
        );
        break;
      case -1 /*未发布：查看 编辑 发布*/:
        options = (
          <>
            {detailEle}
            {divider}
            {editEle}
            {divider}
            {publishEle}
          </>
        );
        break;
      case 11 /*发布成功，待生效：查看 编辑 下线*/:
        options = (
          <>
            {detailEle}
            {divider}
            {editEle}
            {divider}
            {offLineEle}
          </>
        );
        break;
      default:
        break;
    }
    return options;
  };
  return (
    <div className="expert-pool-list">
      <Form className="filter" field={field}>
        <div className="top">
          <span className="pool-title">专家池管理</span>
          <Button
            type="primary"
            className="btn-create-pool"
            onClick={() => setVisible(true)}
          >
            新建专家池
          </Button>
        </div>
        <SelectType {...props} visible={visible} setVisible={setVisible} />
        <Row>
          <Col span={6}>
            <FormItem label="池子名称" {...formItemLayout}>
              <Input name="basePoolName" placeholder="请输入池子名称" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="场景分类" {...formItemLayout}>
              <CascaderSelect
                showSearch
                name="sceneGroup"
                defaultValue={""}
                dataSource={classification}
                style={{ width: "100%" }}
              />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="状态" {...formItemLayout}>
              <Select
                name="state"
                defaultValue={""}
                dataSource={expertStatusEnum}
                style={{ width: "100%" }}
              />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="创建人" {...formItemLayout}>
              <Select
                maxTagCount={10}
                name="empId"
                aria-label="tag mode"
                mode="tag"
                filterLocal={false}
                hasArrow={false}
                onSearch={(value) => {
                  onSearchUser(value);
                }}
                dataSource={roleDataSource}
                style={{ width: "100%" }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col
            span={24}
            style={{
              marginBottom: "20px",
              display: "flex",
              justifyContent: "end",
            }}
          >
            <Button
              type="secondary"
              onClick={searchPoolList}
              style={{ marginLeft: "95px" }}
            >
              查询
            </Button>
            <Button className="btn_reset" onClick={resetPoolList}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
      <div className="table-panel">
        <Table
          dataSource={dataSource}
          loading={isLoading}
          hasBorder={false}
          primaryKey="poolId"
        >
          {columns.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
        <Pagination
          onChange={onPageChange}
          onPageSizeChange={(e) => {
            onPageSizeChange(e);
          }}
          total={total}
          current={page}
          pageSize={size}
          pageSizeSelector="dropdown"
          pageSizeList={[10, 20]}
          popupProps={{ align: "bl tl" }}
          style={{ float: "right", marginTop: "10px" }}
        />
      </div>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(expertPoolList),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const expertPoolListPage = permissionAccess(LogTimePutInPage);
