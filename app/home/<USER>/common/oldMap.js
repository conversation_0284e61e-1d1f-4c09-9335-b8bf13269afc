export const labelMap = {
  sku: {
    activityIds: "营销活动ID",
    activityTypes: "商品活动类型(新)",
    alipayRelId: "支付宝场景指标",
    brandActivityIds: "品牌活动ID",
    brandName: "所属品牌",
    businessAttribute: "业务线",
    businessRegion: "业务区域",
    category15DaySoldRate: '三级品类商品15d动销率',
    categoryOrderPermeability: '品类商品订单渗透率',
    categorySkuCityPermeability: '二级品类商品到城市的覆盖率',
    city: "所在城市",
    commissionRate1d: "门店佣金比例",
    d7SaleAmt: "近7D净交易总额",
    d7ShopValidOrderCnt: "门店近7D有效订单量",
    d15SaleAmt: "近15D净交易总额",
    d30ShopValidOrderCnt: "门店近30D有效订单量",
    deliveryTimelyRate: "出货及时率",
    eleStoreId: "ELE门店ID",
    excludeGoodsKeyWords: "剔除商品关键词",
    goodsActivitiesType: "商品活动类型",
    goodsCategory: "商品分类",
    goodsDiscount: "商品折扣力度",
    goodsId: "商品ID",
    goodsNameKeyWord: "商品名称",
    goodsOriginalPrice: "商品原价",
    goodsPerfectActivitiesType: "特定商品活动",
    goodsPresentPrice: "商品现价",
    goodsType: "售卖类型",
    groupActivitiesType: '商品活动类型',
    honeycombId: "蜂窝ID",
    initialDeliveryPrice: "起送价",
    inviteIds: "子招商活动ID",
    isActivityPriceLessThanNearly7DayMinPrice: "活动价格小于等于7天最低价",
    isAssembleItem: "拼团商品",
    isCatComp: "类目挂靠商品准确性",
    isCatCompStandardStr: "类目是否正确",
    isCityAgent: "是否城代商户",
    isDirect: "是否直营商户",
    isHuoguo: '火锅场景商品',
    isOpenCommissionFee: "是否出佣商家",
    isOverPrice: "价格虚高商品",
    isPicStandardStr: "图片质量是否合格",
    isPriceStandardStr: "价格是否合格",
    isRiskComp: '风控合规数据',
    isSelfDelivery: "是否自配送",
    isUpcNameStandardStr: "品名是否合格",
    itemDistribute: '商品分布',
    itemPicQualityScore: "商品图片质量分",
    itemStatus: "商品状态",
    labelGroupIds: '标签群组',
    lastMonthShopComprehensiveScore: "上月店铺综合分",
    mainInviteIds: "主招商活动ID",
    nearly7DaysBusinessHours: "近7天营业总时长",
    nearly7DaysMinHistoryTransactionPrice: "近7D历史成交最低价",
    nearly7DaysOrders: "商品近7D有效订单量",
    nearly7DaysTurnover: "近7D交易额",
    nearly15DaysMinHistoryTransactionPrice: "近15D历史成交最低价",
    nearly30DaysOrders: "商品近30D有效订单量",
    nearly30DaysTurnover: "近30D交易额",
    newBuFlags: "新业务线",
    newRetailStoreId: "新零售门店ID",
    newServiceTag: "服务标",
    newStoreType: "门店主营类目(新)",
    operateInvestActivityId: "运营位招商活动ID",
    orderPerformanceRate: "订单履约率",
    seasonalTrendScore: '时令商品趋势分',
    selfFetch: "店类型",
    sellerId: "淘系卖家ID",
    serviceFeature: "业务属性",
    servicePackage: "服务包",
    shopComprehensiveScore: "店铺综合分",
    shopLevel: "基建分层",
    shopModelScore: '商户模型评分',
    shopPZLMString: "店铺品质联盟",
    signatory: '签约人',
    skuDiscount: "商品折扣力度",
    specialtyTrendScore: '特产商品趋势分',
    storeAttributes: '店铺标示',
    storeCategoryIds: "门店类目",
    storeCategoryId: "门店类目",
    storeId: "淘内门店ID",
    storeMarketRate: '商户自营销率',
    storeName: "门店名称",
    storeScore: "店铺评分",
    storeType: "门店主营类目(旧)",
    supplierId: "供应商ID",
    upcId: '商品条形码',
    wholeCityDelivery: "是否当日/半日/次日达（全城淘）",
    businessArea:"业务区域",
    businessLine:"业务线",
    bussinessLine:"业务线",
    skuNum:"SKU数量",
    createTime:"创建时间",
    storeLayer:"商户分层",
    crowdAttributes:"适用人群",
    newCustomerActivity:"是否含新客活动",
    superActivityAmountFunds:"超会商家出资金额",
    firstGearFullReduceThreshold:"第一档满减门槛",
    firstGearFullReduceDiscountPrice:"第一档满减优惠金额",
    businessVoucherThreshold:"商家代金券门槛",
    businessVoucherDiscountPrice:"商家代金券优惠金额",
    businessNewCustomerThreshold:"商家新客券门槛",
    businessNewCustomerDiscountPrice:"商家新客券优惠金额",
    inviteStoreCouponActivity:"是否店铺代金券活动",
    inviteActivityThreshold:"门槛金额",
    inviteStoreMjPrice:"门槛金额",
    inviteActivityDiscountPrice:"优惠金额",
    inviteStoreCouponAmt:"优惠金额",
    storeActivitiesType:"门店活动类型",
    nearly7DaysBrowsePV:"近7d浏览总PV",
    nearly7DaysBrowseUV:"近7d浏览总UV",
    nearly15DaysBrowsePV:"近15d浏览总PV",
    nearly15DaysBrowseUV:"近15d浏览总UV",
    nearly7DaysOneCustomerPrice:"近7d客单价",
    nearly15DaysOneCustomerPrice:"近15d客单价",
    nearly7DaysOrderConversionRate:"近7d下单转化率",
    nearly15DaysOrderConversionRate:"近15D下单转化率",
    d7BenefitTotalAmt:"近7D总优惠金额",
    d30BenefitTotalAmt:"近30d总优惠金额",
    // d7InvalidOrderRate:"近7D无效订单率",
    // d30InvalidOrderRate:"近30d无效订单率",
    d7ComplaintRate:"近7D客诉率",
    d30ComplaintRate:"近30D客诉率",
    d7RefundDur:"近7D平均退款时长",
    d30Retention:"近30D留存率",
    d30ShopSubsidy:"近30D店铺补贴力度",
    d30PlatformSubsidy:"近30D平台补贴力度",
    newMainCategoryId:"门店主营类目(新)",
    field:"统计",
    order:"排序"
  },
  others:{ //todo 待补充
    activityTypes:{
      1: "单品折扣",
      2: "单品立减",
      3: "单品立减",
      4: "单品直降",
      5: "n元购",
      6: "品类满减",
      7: "全店满减",
      8: "新客立减",
      9: "配送费活动",
      10: "n元购配送费",
      11: "n选一",
      12: "买n赠m",
      13: "第n件特价折扣"
    },
    alipayRelId:{
      ALIPAY_MIDDLE_PAGE: "支付宝ssu"
    },
    businessArea:{华东战区: '华东战区', 华南战区: '华南战区', 华北战区: '华北战区', 中西战区: '中西战区'},
    businessRegion:{华东战区: '华东战区', 华南战区: '华南战区', 华北战区: '华北战区', 中西战区: '中西战区'},
    businessAttribute:{快消业务部: '快消业务部', 零售KA管理部: '零售KA管理部', 散店业务部: '散店业务部', 生鲜菜场业务部: '生鲜菜场业务部', 小连锁: '小连锁', 医药健康业务部: "医药健康业务部"},
    businessLine:{快消业务部: '快消业务部', 零售KA管理部: '零售KA管理部', 散店业务部: '散店业务部', 生鲜菜场业务部: '生鲜菜场业务部', 小连锁: '小连锁', 医药健康业务部: "医药健康业务部"},
    crowdAttributes:{1: '全部用户', 2: '新零售新客', 3: '超级会员', 6: '门店新客'},
    goodsActivitiesType:{2: '商品直降', 8: '品类满减', 32: '商品特价', 64: '商品折扣', 128: '商品买赠', 129: '正常', 130: '第N件', 133: '正常', 134: '第N件', 135: 'N选1', 136: '买N赠M'},
    // goodsCategory:
    goodsType: {Y_PreSale: '预售品', Y_OnSale: '现货品', "": '不限'},
    inviteStoreCouponActivity: {0: '否', 1: '是'},
    isActivityPriceLessThanNearly7DayMinPrice: {0: '不限制', 1: '是'},
    isAssembleItem:{0: '否', 1: '是', 2: '不限制'},
    isCatComp:{1: '合格', "": '全部'},
    isCatCompStandardStr:{"": '全部', Y_CAT: '正确'},
    isCityAgent:{0: '否', 1: '是'},
    isDirect: {0: '否', 1: '是', '-1': '不限'},
    isHuoguo: {0: '否', 1: '是'},
    isOpenCommissionFee:{0: '否', 1: '是'},
    isOverPrice: {0: '否', 1: '是', 2: '不限制'},
    isPicStandardStr:{"": '全部', Y_PIC: '合格'},
    isPriceStandardStr:{"": '全部', Y_PRICE: '合格'},
    isRiskComp:{1: '普通等级', 2: '严格等级'},
    isSelfDelivery:{0: '否', 1: '是'},
    isUpcNameStandardStr:{"": '全部', Y_UPCNAME: '合格'},
    itemDistribute:{1: '畅销品', 2: '滞销品', 3: '钩子', 4: '其他'},
    itemPicQualityScore:{
      1: "含牛皮癣图片",
      2: "透明底图",
      3: "白底图",
      4: "主体面积占比",
      100: "高质量图片（含牛皮癣低）",
      101: "中等质量图片(含牛皮癣中)",
      102: "低质量图片（含牛皮癣高）",
      201: "是(透明底图)",
      301: "是(白底图)",
      400: "低质量图片(主体面积占比)",
      401: "高质量图片(主体面积占比)"
    },
    itemStatus:{YOU_XIAO: '上架', WU_XIAO: '下架'},
    lastMonthShopComprehensiveScore:{L1: 'L1', L2: 'L2', L3: 'L3', L4: 'L4', L5: 'L5'},
    // mainCategoryId
    nearly7DaysMinHistoryTransactionPrice:{0: '否', 1: '是'},
    nearly15DaysMinHistoryTransactionPrice:{0: '否', 1: '是'},
    newBuFlags:{
      其他餐饮: "其他餐饮",
      大健康业务部: "大健康业务部",
      快消业务部: "快消业务部",
      散店业务部: "散店业务部",
      水果业务部: "水果业务部",
      泛零售业务部: "泛零售业务部",
      生鲜菜场业务部: "生鲜菜场业务部"
    },
    newCustomerActivity:{0: '否', 1: '是'},
    superActivityAmountFunds:{0: '否', 1: '是'},
    newMainCategoryId:{
      226:"卫浴用品",
      270:"母婴",
      271:"服装鞋包",
      227:"个护美妆",
      272:"火锅烤串食材",
      228:"文化用品",
      273:"眼镜店",
      229:"汽车用品",
      275:"果切",
      230:"纺织用品",
      276:"水果捞",
      231:"季节用品",
      279:"其它商店",    
      232:"母婴用品",
      280:"医院诊所",
      233:"计生用品",
      234:"其他",
      281:"休闲零食",
      282:"药店",
      283:"茶行",
      285:"饮料冰品",
      286:"大型超市",
      261:"宠物",
      262:"宠物食品",
      263:"宠物用品",
      264:"宠物医药",
      265:"宠物美容",
      266:"宠物周边",
      267:"3C电器",
      268:"日用百货",
      269:"美妆个护",
      1:"餐饮",
      173:"鲜花",
      174:"绿植",
      176:"送水",
      177:"送奶",
      180:"蔬菜",
      181:"禽类",
      182:"蛋类",
      183:"水产",
      184:"肉类",
      185:"药店",
      186:"药店",
      190:"水果店",
      191:"KA商超生鲜",
      219:"卤味鸭脖",
      220:"酒水饮料",
      221:"粮油干货",
      222:"生鲜食品",
      223:"速冻食品",
      224:"家居日化",
      225:"厨房用品"
    },
    newServiceTag:{
      BADPROD_S_TAG: "坏品包退",
      BOOK_S_TAG: "预定",
      BRAND_S_TAG: "品牌",
      MELTMP_S_TAG: "融化必赔",
      NEW_SHOP_S_TAG: "新店",
      QREFUND_S_TAG: "极速退款",
      RECEIPT_S_TAG: "开发票",
      REFUND_S_TAG: "无忧退",
      SLOW_S_TAG: "慢必赔",
      WARM_S_TAG: "冬季保温"
    },
    // newStoreType
    selfFetch: {0: '外卖店', 1: '自提店', 2: '外卖自提店'},
    serviceFeature:{SF_9: '全国KA', SF_10: '城市龙头', SF_11: '其他连锁', SF_12: '散店'},
    servicePackage:{NEW_SHOP_S_TAG: '新店'},
    shopComprehensiveScore:{L1: 'L1', L2: 'L2', L3: 'L3', L4: 'L4', L5: 'L5'},
    shopLevel:{0: '未入门', 100: '入门', 200: '健康', 300: '完美', 500: '超级'},
    shopPZLMString:{
      PDCHLM: "吃货联盟",
      PDGSCHLM: "果蔬商超吃货联盟",
      YTHBBLD: "业态红包-便利店",
      YTHBCHBL: "业态红包-超市便利",
      YTHBCS: "业态红包-超市",
      YTHBCW: "业态红包-宠物",
      YTHBJS: "业态红包-酒水",
      YTHBLS: "业态红包-零食",
      YTHBLSJS: "业态红包-零食酒水",
      YTHBMY: "业态红包-母婴",
      YTHBMZBH: "业态红包-美妆百货",
      YTHBMZGH: "业态红包-美妆个护",
      YTHBRYBH: "业态红包-日用百货",
      YTHBSG: "业态红包-水果",
      YTHBSGSX: "业态红包-水果生鲜",
      YTHBSX: "业态红包-生鲜",
      YTHBXHLZ: "业态红包-鲜花绿植"
    },
    storeActivitiesType:{2: '商品直降', 8: '品类满减', 32: '商品特价', 64: '商品折扣', 128: '商品买赠', 129: '正常', 130: '第N件', 133: '正常', 134: '第N件', 135: 'N选1', 136: '买N赠M', 2000:"全店满减",2002:"运费满减"},
    storeAttributes:{LP_ATTACH: '罗盘附店', INDIVIDUAL: '散店'},
    storeType: {
      173: "鲜花",
      174: "绿植",
      176: "送水",
      181: "禽类",
      184: "肉类",
      186: "药店",
      190: "水果",
      219: "休闲零食",
      220: "酒水饮料",
      222: "生鲜食品",
      223: "速冻食品",
      224: "家具日化",
      232: "母婴用品",
      234: "其他"
    },
    wholeCityDelivery: {0: '否', 1: '是'},
    storeLayer:{
      KA:"KAA",
      SMB:"SMB"
    },
    inviteStoreCouponActivity: {0: '否', 1: '是'},
    field:{
      d7StoreSalesNum:"门店7d销量",
      d7ValidOrder:"商品7d销量",
      presentPrice:"商品现价"
    },
    order:{
      des:"降序"
    }
  }
}
