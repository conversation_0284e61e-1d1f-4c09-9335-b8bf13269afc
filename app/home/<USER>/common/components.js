import React from 'react';
import {Input, NumberPicker, Checkbox, Radio, Select, Form, Balloon, Button, Message} from '@alife/next';
import { pick, cloneDeep,isArray } from 'lodash';
import moment from 'moment';
import { TimeRangePicker } from '@/components/TimeRangePicker';
import { CascaderSelectWithClear } from '@/components/CascaderSelect';
import {isString,isAllNumber} from "@/utils/validators";
import {getValueByUrlKey, promisify} from '@/utils/others';
import './components.scss';
// import {extensionMuti} from "@/utils/api";
import * as api from "@/utils/api";
import * as apiV2 from "@/adator/api";
import {queryUserByKeyWord} from "@/containers/channel/market/request";
import {onRequestError} from "@/utils/api";
import {deepCopy} from "@/home/<USER>/common/index";

const FormItem = Form.Item;

export class DateRangePicker extends React.PureComponent {
  onChange = (value)=>{
    let changed = {
      start: value[0] ? value[0].valueOf() : '',
      end: value[1] ? value[1].valueOf() : ''
    }
    this.props.onChange(changed)
  }

  render(){
    const {value} = this.props;
    let lvalue = value ? [value.start? moment(value.start): null, value.end ? moment(value.end): null] : []
    return <TimeRangePicker {...this.props} showTime={true} value={lvalue} onChange={this.onChange}></TimeRangePicker>
  }
}


export class LevelCascade extends React.PureComponent {
  constructor(props){
    super(props)
    this.state = {
      dataSource: []
    }
  }

  onChange = (value, data)=>{
    if(!value) return;
    let result
    if (Array.isArray(data)){
      result = data.map(mapF)
    } else {
      result = mapF(data)
    }
    this.props.onChange(result)

    function mapF(data){
      return {
        value: +data.value,
        level: data.level
      }
    }
  }

  mapV(v){
    if (!v) return v
    if (Array.isArray(v)){
      return v.map(e => +e.value)
    } else {
      return +v.value
    }
  }

  static getDerivedStateFromProps(props, state){
    if (props.dataSource !== state.dataSource && props.dataSource){
      addLevel(props.dataSource)
      return {
        dataSource: props.dataSource
      }
    }
    return null

    function addLevel(dataSource){
      if (!Array.isArray(dataSource)) return
      return f(dataSource, 1)

      function f(arr, lvl = 1){
        arr.forEach(node=> {
          node.level = lvl
          if (Array.isArray(node.children)){
            f(node.children, lvl+1)
          }
        })
      }
    }
  }

  render(){
    let lvalue = this.mapV(this.props.value)
    const {dataSource} = this.state;
    return <CascaderSelectWithClear {...this.props} dataSource={cloneDeep(dataSource)} value={lvalue} onChange={this.onChange}></CascaderSelectWithClear>
  }
}

export class GroupInputAndSelect extends React.PureComponent{
  constructor(props){
    super(props);
    this.state = {
      dataSource: props.dataSource,
      mode: props.value ? (props.dataSource ? props.dataSource.filter(v => v.value == props.value.activityIdsTypes)[0].mode : '') : 'input',
      value: props.value
    }
  }

  onChange = (v,actionType,item)=>{
    let a = v ? v : null;
    let {value} = this.state;
    value.activityIdsTypes = a;
    value.activityIdsTypesValue = [];
    this.props.onChange(value);
    this.setState({
      mode: item.mode,
    })
  }

  valueChange = (v) =>{
    let {value} = this.state;
    value.activityIdsTypesValue = v ? (isString(v)) ? v.split(',') : v : [];
    this.props.onChange(value);
    this.setState({value});
  }

  componentWillReceiveProps(nextProps) {
    let {value} = this.state;
    value.activityIdsTypesValue = (nextProps.value ? nextProps.value.activityIdsTypesValue : []);
    this.setState({value});
  }


  render() {
    const {mode, value} = this.state;
    const {dataSource, required,validator} = this.props;
    let activityIdsTypes = (value ? value.activityIdsTypes : '');
    let activityIdsTypesValue = (value ? value.activityIdsTypesValue : []);
    const ds = (dataSource && dataSource.length > 0) ? dataSource.filter(v => v.mode == 'select')[0].ds : [];
    const activityIdsTypesLabel = (dataSource && dataSource.length > 0) ?  dataSource.filter(v => v.value == activityIdsTypes)[0].label:'';
    const disabled = isAllNumber(location.hash.split("?")[0].split("/").slice(-1)[0]).length == 0 || getValueByUrlKey("isCopy");
    return <div className="group-activities-types">
      <FormItem required={required} requiredMessage={`请输入${activityIdsTypesLabel}`} validator={validator} validatorTrigger="onBlur">
      {required && <span className="red-mark">*</span>}
      <Select  {...this.props} style={{width:'130px'}} disabled={disabled} value={activityIdsTypes} onChange={(value, actionType, item) => this.onChange(value, actionType, item)} />
        {mode == 'input' ?
          <Input name="activityIdsTypesValue" value={activityIdsTypesValue} onChange={this.valueChange} placeholder="英文逗号隔开，最多100个" /> :
          <Select name="activityIdsTypesValue" style={{width:'240px'}} mode='multiple' value={activityIdsTypesValue} dataSource={ds} onChange={this.valueChange}/>}
      </FormItem>
    </div>
  }
}

export class CommaSeperatedInput extends React.PureComponent {
  onChange = (value)=>{
    let a = value ? value.split(',') : null;
    this.props.onChange(a)
  }

  render(){
    const {value} = this.props;
    let lvalue = (value || []).join(',')
    return <Input {...this.props} value={lvalue} onChange={this.onChange}></Input>
  }
}

export class RangeNumberInput extends React.Component {
  static defaultProps = {
    startProps: {},
    endProps: {},
    unitNode: null,
    after:''
  }

  constructor(props){
    super(props)
    this.state = {
      lvalue: undefined,
      rvalue: undefined
    }
  }

  static getDerivedStateFromProps(props, state){
    const {value} = props
    let { lvalue, rvalue } = state;

    if (value){
      if (value.start != lvalue){
        lvalue = value.start
      }
      if (value.end != rvalue){
        rvalue = value.end
      }
      return {
        lvalue, rvalue,
      }
    } else {
      return {lvalue: undefined, rvalue: undefined}
    }
  }

  onStartChange = (value)=>{
    const changed = {
      start: value,
      end: this.state.rvalue
    }
    this.props.onChange(changed)
  }

  onEndChange = (value)=>{
    const changed = {
      start: this.state.lvalue,
      end: value,
    }
    this.props.onChange(changed)
  }

  render() {
    const {id, unitNode, min, max, className, after, precision, step, disabled = false, ...rest} = this.props;
    const {lvalue, rvalue} = this.state;
    let rsp = pick(rest, ['precision', 'step'])

    return (
      <div id={id} className={`range-number-input ${className||''}`} style={{'whiteSpace': 'nowrap'}}>
        <NumberPicker {...rsp} min={min} max={rvalue || max} precision={precision} disabled={disabled} step={step} onChange={this.onStartChange} value={lvalue}/>-
        <NumberPicker {...rsp} min={lvalue} max={max} precision={precision} disabled={disabled} step={step} onChange={this.onEndChange} value={rvalue}/>{unitNode}
        {after}
      </div>
    )
  }
}


export class BalloonInput extends React.Component {

  constructor(props){
    super(props)
    this.state = {
      visible:false,
      poolId:props.poolId,
      noticeUid: props.noticeUid || '',
      type: props.type, // 1:list 2:detail,3 detail 有内容
      roleDataSource: [],
      showDataSource:[]
    }
  }

  componentDidMount() {
    this.initNoticeUid(this.state.noticeUid);
  }


  updateNotice = async () => {
    const {noticeUid,poolId} = this.state;
    let poolNoticeRequest = {
      poolId,
      noticeUid
    }
    try {
      let request = (apiV2.updatePoolNotice);
      const result = await request((poolNoticeRequest));
      if (result.data.data.success) {
        Message.success('操作成功');
        this.setState({visible: true},()=>{
          setTimeout(()=>{
            location.reload();
          },2000)
        });
      } else {
        Message.warning(result.errMessage);
      }
    } catch (error) {
      api.onRequestError(error)
    }
  }

  initNoticeUid = (noticeUid) =>{
    let noticeUidGroup = noticeUid.split(",");
    if (noticeUidGroup.length > 0) {
      noticeUidGroup.map((v)=>{
        this.ctrlRoleDataSource(v);
      })
    }
  }

  ctrlRoleDataSource = (id) => {
    if (id != '') {
      let {showDataSource} = this.state;
      try {
        queryUserByKeyWord(id)
          .then((resp) => {
            if (resp && resp.length > 0) {
              showDataSource.push({
                value: resp[0].empId,
                label: `${resp[0].lastName}_${resp[0].empId}`,
                record: resp[0]
              });
              this.setState({showDataSource})
            }
          })

      } catch (error) {
        api.onRequestError(error)
      }
    }
  }

  showInput = () =>{
    let {showDataSource,roleDataSource,noticeUid} = this.state;
    roleDataSource = deepCopy(showDataSource);
    this.setState({roleDataSource},()=>{
      if(noticeUid!="" && roleDataSource.length ==noticeUid.split(",").length){
        this.setState({visible: true})
      }
    })
  }


  getTriggerContent = () => {
    let {type, noticeUid, showDataSource} = this.state;
    let noticeUidName = "";
    if (showDataSource && showDataSource.length > 0 && noticeUid != "") {
      noticeUidName = showDataSource.map(v => v.label).join(",");
    } else {
      noticeUidName = noticeUid;
    }
    let result;
    switch (type) {
      case 1:
        result = <span style={{cursor: "pointer"}} onClick={() => this.setState({visible: true})}>设置提醒</span>;
        break;
      case 2:
        result = <Button type={'secondary'} size='small' onClick={() => this.setState({visible: true})}>设置到期提醒</Button>;
        break;
      case 3 :
        result = <span onClick={() => this.showInput()}>到期前接收过期提醒：{noticeUidName}</span>
        break;
      default:
        break;
    }
    return result;
  }

  onSearch = (keyword) =>{ //通过关键字查用户
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    this.searchTimeout = setTimeout(() => {
      if(keyword){
        var reg = new RegExp('^[A-Za-z0-9\u4e00-\u9fa5]+$');
        if(keyword!='' && !reg.test(keyword)){
          Message.error('不能输入特殊字符');
          return;
        }
        queryUserByKeyWord(keyword).then((data) => {
          const dataSource = data.map(v => ({
            value: v.empId,
            label: `${v.lastName}_${v.empId}`,
            record:v
          }));
          this.setState({roleDataSource:dataSource})
        }).catch(onRequestError)
      }else{
        this.setState({roleDataSource:[]})
      }
    }, 800);
  }

  addAcl = (value, actionType, item) => {
    if(value.length>2){
      Message.warning('备选人最多加两个');
      return;
    }else {
      let newValue = value.join(',');
      this.setState({noticeUid: newValue})
    }
  }

  render() {
    let {visible, type, noticeUid, roleDataSource} = this.state;
    return <Balloon
      popupClassName="balloon-container"
      triggerType="click"
      title="消息接收人"
      visible={visible}
      autoFocus
      trigger={this.getTriggerContent()}
      align="bl"
      closable={false}
    >
      <Select aria-label="tag mode" placeholder={'请输入员工工号'} mode="tag" value={noticeUid ? noticeUid.split(",") : []} filterLocal={false} hasArrow={false} onChange={(value, actionType, item) => this.addAcl(value, actionType, item)} onSearch={(value) => this.onSearch(value)} dataSource={roleDataSource} style={{width: 400}}/>
      {/*<Input placeholder={"可设置三个消息接收人"} value={noticeUid} onChange={(value) => this.setState({noticeUid: value})}/>*/}
      <div className="btn-panel">
        <Button size={'small'} type={'primary'} onClick={this.updateNotice}>确定</Button>
        <Button size={'small'} onClick={() => this.setState({visible: false})}>取消</Button>
      </div>
    </Balloon>
  }
}

export class BalloonTime extends React.Component {

  constructor(props){
    super(props)
    this.state = {
      poolId:props.poolId,
      effectAt: props.effectAt,
      expireAt: props.expireAt,
      visible:false
    }
  }

  editPool = async () => {
    const {effectAt, expireAt, poolId} = this.state;
    // await (promisify(this.field.validate)());
    let saveItemPoolReq = {
      poolId,
      effectAt,
      expireAt,
    }
    try {
      let request = (api.extensionMuti);
      const result = await request((saveItemPoolReq));
      if (result.success) {
        Message.success('操作成功');
        this.setState({visible:false})
      } else {
        Message.warning(result.errMessage);
      }
    } catch (error) {
      api.onRequestError(error)
    }
  }

  changeTime = (value) =>{
    this.setState({
      effectAt: value[0].valueOf(),
      expireAt: value[1].valueOf(),
    })
  }

  render() {
    const {visible} = this.state;
    // let lvalue = value ? [value.start? moment(value.start): null, value.end ? moment(value.end): null] : []
    return <Balloon
      popupClassName="balloon-time-container"
      triggerType="click"
      title="有效期"
      autoFocus
      visible={visible}
      trigger={<Button text type={'primary'} style={{cursor: "pointer",marginLeft:'5px'}} onClick={()=>this.setState({visible:true})}>修改</Button>}
      align="bl"
      closable={false}
    >
      <TimeRangePicker defaultValue={[moment(this.props.effectAt),moment(this.props.expireAt)]} followTrigger name="effectRange" disabledDate={(date) => date.isBefore(moment().startOf('day'))} onChange={this.changeTime}></TimeRangePicker>
      <div className="btn-panel">
        <Button size={'small'} type={'primary'} onClick={this.editPool}>确定</Button>
        <Button size={'small'} onClick={()=>this.setState({visible:false})}>取消</Button>
      </div>
    </Balloon>
  }
}

