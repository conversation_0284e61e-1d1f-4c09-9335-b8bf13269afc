import { operatorEum } from "@/home/<USER>/common/index";
import { RangeNumberInput } from "@/home/<USER>/common/components";
import React from "react";

export function idCommaSeperatedRequired(rule, value, callback) {
  if (value && value.split(",").length > 100) {
    callback(`输入超过最大长度100个`);
  } else if (value && !/^(\d+)(,\s*\d+)*$/.test(value)) {
    callback("输入格式不合法");
  } else if (value && value.indexOf(" ") != -1) {
    callback("输入不能有空格");
  } else {
    callback();
  }
}

export function upcIdCommaSeperated(rule, value, callback) {
  if (value && value.split(",").length > 100) {
    callback(`输入超过最大长度100个`);
  } else if (value && !/^([\w\-]+)(,\s*[\w\-]+)*$/.test(value)) {
    callback("输入格式不合法");
  } else {
    callback();
  }
}
export function nameSeperatedRequired(rule, value, callback) {
  if (value && value.split(",").length > 30) {
    callback(`输入超过最大长度30个`);
  } else if (value && value.indexOf("，") > 0) {
    callback("输入不能有中文逗号");
  } else if (value && value.indexOf("\n") > 0) {
    callback("输入不能有回车");
  } else if (value && value.indexOf(" ") != -1) {
    callback("输入不能有空格");
  } else {
    callback();
  }
}
export const validatorMap = {
  idCommaSeperatedRequired,
  upcIdCommaSeperated,
  nameSeperatedRequired,
};

export const filtersConfigMap = [
  //基础信息
  {
    filterFieldLabel: "商品ID",
    filterFieldId: "item_goodsId",
    filterFieldKey: "item_goodsId",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "baseInfo",
    validatorValue: "idCommaSeperatedRequired",
  },
  {
    filterFieldLabel: "商品条形码",
    filterFieldId: "item_upcId",
    filterFieldKey: "item_upcId",
    operator: 13,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "baseInfo",
    validatorValue: "upcIdCommaSeperated",
  },
  {
    filterFieldLabel: "商品名称",
    filterFieldId: "item_name",
    filterFieldKey: "item_name",
    operator: 11,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "baseInfo",
    validatorValue: "nameSeperatedRequired",
  },
  {
    filterFieldLabel: "剔除关键词",
    filterFieldId: "filter_item_name",
    filterFieldKey: "filter_item_name",
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "baseInfo",
    operator: 12,
    validatorValue: "nameSeperatedRequired",
  },
  {
    filterFieldLabel: "商品分类",
    filterFieldId: "item_goodsCategory",
    filterFieldKey: "item_goodsCategory",
    operator: 16,
    filterFieldComponentType: "cascaderSelect",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "图片质量分",
    filterFieldId: "item_pic_standard",
    filterFieldKey: "item_pic_standard",
    operator: 40,
    filterFieldComponentType: "picStandard",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "风控合规数据",
    filterFieldId: "item_risk_comp",
    filterFieldKey: "item_risk_comp",
    operator: 38,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "类目是否正确",
    filterFieldId: "item_ac_CAT",
    filterFieldKey: "item_ac_CAT",
    operator: 13,
    filterFieldComponentType: "radio",
    filterFieldValue: "Y_CAT",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "商品状态",
    filterFieldId: "item_status",
    filterFieldKey: "item_status",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldValue: "YOU_XIAO",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "活动价格≤7天最低价",
    filterFieldId: "marketItem_activity_price_less_than_min_price_7d",
    filterFieldKey: "marketItem_activity_price_less_than_min_price_7d",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldValue: "1",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "售卖类型",
    filterFieldId: "item_ac_goodsType",
    filterFieldKey: "item_ac_goodsType",
    filterFieldValue: "",
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "baseInfo",
    operator: 37,
  },
  {
    filterFieldLabel: "标签群组",
    filterFieldId: "item_label_group_id",
    filterFieldKey: "item_label_group_id",
    filterFieldValue: "",
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "baseInfo",
    operator: 13,
  },
  {
    filterFieldLabel: "商品竞争力",
    filterFieldId: "item_bar_compete_pwr_30d",
    filterFieldKey: "item_bar_compete_pwr_30d",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "baseInfo",
    filterFieldExtend: { min: 0, precision: 1, step: 0.01 },
  },
  {
    filterFieldLabel: "原价价格力",
    filterFieldId: "item_reserve_price_compete_pwr",
    filterFieldKey: "item_reserve_price_compete_pwr",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "baseInfo",
    filterFieldExtend: { min: 0, precision: 1, step: 0.01 },
  },
  {
    filterFieldLabel: "可售商品价格力",
    filterFieldId: "item_sale_price_compete_pwr",
    filterFieldKey: "item_sale_price_compete_pwr",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "baseInfo",
    filterFieldExtend: { min: 0, precision: 1, step: 0.01 },
  },
  {
    filterFieldLabel: "商品所属品牌",
    filterFieldId: "sku_brand_id",
    filterFieldKey: "sku_brand_id",
    operator: 71,
    filterFieldComponentType: "selectSearch",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "商品服务标",
    filterFieldId: "item_ac_item_service_tag",
    filterFieldKey: "item_ac_item_service_tag",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "商品出勤率指标",
    filterFieldId: "item_online_rate",
    filterFieldKey: "item_online_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "baseInfo",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "商品是否入驻全能商厦",
    filterFieldId: "item_e_mall",
    filterFieldKey: "item_e_mall",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "baseInfo",
  },

  {
    filterFieldLabel: "ELE门店ID",
    filterFieldId: "shop_id",
    filterFieldKey: "shop_id",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    validatorValue: "idCommaSeperatedRequired",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "淘内门店ID",
    filterFieldId: "store_id",
    filterFieldKey: "store_id",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    validatorValue: "idCommaSeperatedRequired",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "wid",
    filterFieldId: "item_wid",
    filterFieldKey: "item_wid",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    validatorValue: "idCommaSeperatedRequired",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "门店名称",
    filterFieldId: "store_name",
    filterFieldKey: "store_name",
    operator: 11,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "skuStore",
  },
  // { filterFieldLabel:'门店类目', filterFieldId: "store_cat2_id", filterFieldKey: "store_cat2_id", operator: 16 ,filterFieldComponentType:'cascaderSelect' ,filterFieldIdGroup:"skuStore" },
  {
    filterFieldLabel: "门店主营类目",
    filterFieldId: "store_main_category_id",
    filterFieldKey: "store_main_category_id",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "所在城市",
    filterFieldId: "store_city_id",
    filterFieldKey: "store_city_id",
    operator: 7,
    filterFieldComponentType: "cascaderSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "店铺评分",
    filterFieldId: "store_score",
    filterFieldKey: "store_score",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "服务标",
    filterFieldId: "item_ac_serviceTag",
    filterFieldKey: "item_ac_serviceTag",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "起送价",
    filterFieldId: "item_start_price",
    filterFieldKey: "item_start_price",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "所属品牌",
    filterFieldId: "item_brand_name",
    filterFieldKey: "item_brand_name",
    operator: 11,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "近7天营业总时长",
    filterFieldId: "item_d7_open_minutes",
    filterFieldKey: "item_d7_open_minutes",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "是否直营商户",
    filterFieldId: "store_direct",
    filterFieldKey: "store_direct",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "是否城代商户",
    filterFieldId: "store_city_agent",
    filterFieldKey: "store_city_agent",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "是否自配送",
    filterFieldId: "store_zps",
    filterFieldKey: "store_zps",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuStore",
  },
  // { filterFieldLabel:'基建分层', filterFieldId: "store_level", filterFieldKey: "store_level", operator: 71, filterFieldComponentType:'multipleSelect' ,filterFieldIdGroup:"skuStore" },
  {
    filterFieldLabel: "商户成长分",
    filterFieldId: "store_general_level_name",
    filterFieldKey: "store_general_level_name",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "当天是否开启CPS",
    filterFieldId: "item_is_open_commission_fee",
    filterFieldKey: "item_is_open_commission_fee",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "CPS出佣比例",
    filterFieldId: "item_commission_rate_1d",
    filterFieldKey: "item_commission_rate_1d",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuStore",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "上月店铺综合分",
    filterFieldId: "item_shop_general_level_last_month_name",
    filterFieldKey: "item_shop_general_level_last_month_name",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "业务区域",
    filterFieldId: "store_business_region",
    filterFieldKey: "store_business_region",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "新业务线",
    filterFieldId: "store_new_bu_flag",
    filterFieldKey: "store_new_bu_flag",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "业务属性",
    filterFieldId: "item_ac_serviceFeature",
    filterFieldKey: "item_ac_serviceFeature",
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
    operator: 132,
  },
  {
    filterFieldLabel: "商户分层",
    filterFieldId: "item_store_layer",
    filterFieldKey: "item_store_layer",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "剔除品牌",
    filterFieldId: "filter_item_brand_name",
    filterFieldKey: "filter_item_brand_name",
    operator: 12,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "skuStore",
    validatorValue: "nameSeperatedRequired",
  },
  {
    filterFieldLabel: "服务包",
    filterFieldId: "item_ac_servicePackage",
    filterFieldKey: "item_ac_servicePackage",
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuStore",
    operator: 132,
  },
  {
    filterFieldLabel: "是否C5明星店",
    filterFieldId: "item_store_shop_star",
    filterFieldKey: "item_store_shop_star",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "时令（月）",
    filterFieldId: "algorithm_season_tags",
    filterFieldKey: "algorithm_season_tags",
    operator: 13,
    filterFieldComponentType: "select",
    filterFieldIdGroup: "required",
  },
  {
    filterFieldLabel: "商家是否入驻全能商厦",
    filterFieldId: "item_shop_e_mall",
    filterFieldKey: "item_shop_e_mall",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuStore",
  },
  {
    filterFieldLabel: "商家是否入驻全能超市3.0",
    filterFieldId: "item_shop_e_mall3",
    filterFieldKey: "item_shop_e_mall3",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuStore",
  },

  {
    filterFieldLabel: "商品近7天有效订单量",
    filterFieldId: "item_d7_valid_order_cnt",
    filterFieldKey: "item_d7_valid_order_cnt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
    filterFieldExtend: { precision: 1, step: 0.01 },
  },
  {
    filterFieldLabel: "商品近30天有效订单量",
    filterFieldId: "item_d30_valid_order_cnt",
    filterFieldKey: "item_d30_valid_order_cnt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
    filterFieldValue: { start: 1 },
  }, //增加默认值
  {
    filterFieldLabel: "门店近7天有效订单量",
    filterFieldId: "store_d7_shop_valid_order_cnt",
    filterFieldKey: "store_d7_shop_valid_order_cnt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
  },
  {
    filterFieldLabel: "门店近30天有效订单量",
    filterFieldId: "store_d30_shop_valid_order_cnt",
    filterFieldKey: "store_d30_shop_valid_order_cnt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
  },
  {
    filterFieldLabel: "近7天交易额",
    filterFieldId: "item_d7_total_amt",
    filterFieldKey: "item_d7_total_amt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
  },
  {
    filterFieldLabel: "近30天交易额",
    filterFieldId: "item_d30_total_amt",
    filterFieldKey: "item_d30_total_amt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
  },
  {
    filterFieldLabel: "近7天历史成交最低价",
    filterFieldId: "item_min_price_7d",
    filterFieldKey: "item_min_price_7d",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
  },
  {
    filterFieldLabel: "近15天历史成交最低价",
    filterFieldId: "item_min_price_15d",
    filterFieldKey: "item_min_price_15d",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuTrade",
  },
  {
    filterFieldLabel: "是否近30天销量top500商品（除B2C门店品）",
    filterFieldId: "item_city_top500_sold",
    filterFieldKey: "item_city_top500_sold",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "skuTrade",
  },

  // { filterFieldLabel:'基建分层', filterFieldId: "store_level", filterFieldKey: "store_level", operator: 71, filterFieldComponentType:'multipleSelect' },
  // { filterFieldLabel:'店铺综合评分', filterFieldId: "store_general_level_name", filterFieldKey: "store_general_level_name", operator: 71, filterFieldComponentType:'multipleSelect' },

  {
    filterFieldLabel: "营销活动ID",
    filterFieldId: "market_activity_id",
    filterFieldKey: "market_activity_id",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
    validatorValue: "idCommaSeperatedRequired",
  },
  {
    filterFieldLabel: "招商活动ID",
    filterFieldId: "invite_activity_id",
    filterFieldKey: "invite_activity_id",
    operator: 13,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
    validatorValue: "idCommaSeperatedRequired",
  },
  {
    filterFieldLabel: "品牌活动ID",
    filterFieldId: "brand_activity_id",
    filterFieldKey: "brand_activity_id",
    operator: 13,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
    validatorValue: "idCommaSeperatedRequired",
  },
  // { filterFieldLabel:'营销活动类型', filterFieldId: "activity_child_type", filterFieldKey: "activity_child_type", operator: 7, filterFieldComponentType:'cascaderSelect'},
  /*实时类营销销池子 营销活动类型*/
  {
    filterFieldLabel: "商品活动",
    filterFieldId: "activity_child_type",
    filterFieldKey: "activity_child_type",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "required",
  },

  /* 实时的店铺活动字段 */
  {
    filterFieldLabel: "店铺活动",
    filterFieldId: "shop_activity_types",
    filterFieldKey: "shop_activity_types",
    operator: 132,
    filterFieldComponentType: "cascaderSelect",
    filterFieldIdGroup: "required",
  },
  /* 实时零售价格品池 */
  {
    filterFieldLabel: "原价折后价水平",
    filterFieldId: "item_orgin_price_baseline_7d",
    filterFieldKey: "item_orgin_price_baseline_7d",
    operator: 23,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
  },
  {
    filterFieldLabel: "同商圈最低价",
    filterFieldId: "item_min_gen_price_aoi",
    filterFieldKey: "item_min_gen_price_aoi",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "近N天最低折后价",
    filterFieldId: "item_latest_n_lowest_price",
    filterFieldKey: "item_latest_n_lowest_price",
    operator: 23,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "baseInfo",
  },
  {
    filterFieldLabel: "近30天同品折后价水平",
    filterFieldId: "item_price_baseline_30d",
    filterFieldKey: "item_price_baseline_30d",
    operator: 23,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
  },
  {
    filterFieldLabel: "近15天同品折后价水平",
    filterFieldId: "item_price_baseline_15d",
    filterFieldKey: "item_price_baseline_15d",
    operator: 23,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
  },
  {
    filterFieldLabel: "近7天同品折后价水平",
    filterFieldId: "item_price_baseline_7d",
    filterFieldKey: "item_price_baseline_7d",
    operator: 23,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
  },

  /*普通的*/
  {
    filterFieldLabel: "商品活动",
    filterFieldId: "item_activity_types",
    filterFieldKey: "item_activity_types",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "required",
  },
  {
    filterFieldLabel: "商品分布",
    filterFieldId: "item_distribute",
    filterFieldKey: "item_distribute",
    operator: 7,
    filterFieldComponentType: "checkbox",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "品质联盟",
    filterFieldId: "item_ac_PZ",
    filterFieldKey: "item_ac_PZ",
    operator: 13,
    filterFieldComponentType: "select",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "商品原价",
    filterFieldId: "item_orig_price",
    filterFieldKey: "item_orig_price",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "商品现价",
    filterFieldId: "item_present_price",
    filterFieldKey: "item_present_price",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "商品现价",
    filterFieldId: "marketItem_present_price",
    filterFieldKey: "marketItem_present_price",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "商品折扣力度",
    filterFieldId: "item_discount",
    filterFieldKey: "item_discount",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "商品折扣力度",
    filterFieldId: "marketItem_discount",
    filterFieldKey: "marketItem_discount",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "店铺活动",
    filterFieldId: "shop_activity_types",
    filterFieldKey: "shop_activity_types",
    operator: 132,
    filterFieldComponentType: "cascaderSelect",
    filterFieldIdGroup: "skuMarket",
  },

  {
    filterFieldLabel: "商户自营效率",
    filterFieldId: "item_store_market_rate",
    filterFieldKey: "item_store_market_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "会员人群",
    filterFieldId: "item_user_scope",
    filterFieldKey: "item_user_scope",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "skuMarket",
  },
  {
    filterFieldLabel: "商品起购量",
    filterFieldId: "item_start_with",
    filterFieldKey: "item_start_with",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "skuMarket",
  },

  {
    filterFieldLabel: "时段",
    filterFieldId: "timePeriod",
    filterFieldKey: "timePeriod",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "scene",
  },
  {
    filterFieldLabel: "人群",
    filterFieldId: "crowd",
    filterFieldKey: "crowd",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "scene",
  },
  {
    filterFieldLabel: "细分场景词",
    filterFieldId: "item_scene_id_subdivide_scene",
    filterFieldKey: "item_scene_id_subdivide_scene",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "scene",
  },
  {
    filterFieldLabel: "24节气 ",
    filterFieldId: "item_solar_term",
    filterFieldKey: "item_solar_term",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "scene",
  },

  /* 为了不和商品的属性起冲突实现解耦，这里全部都新建，即使有部分相同 */
  // 招商/营销
  {
    filterFieldLabel: "营销活动ID",
    filterFieldId: "store_activity_id",
    filterFieldKey: "store_activity_id",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
    validatorValue: "idCommaSeperatedRequired",
  },
  {
    filterFieldLabel: "招商活动ID",
    filterFieldId: "store_invite_id",
    filterFieldKey: "store_invite_id",
    operator: 13,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "required",
    validatorValue: "idCommaSeperatedRequired",
  },

  // 基本属性
  {
    filterFieldLabel: "ELE门店ID",
    filterFieldId: "store_shop_id",
    filterFieldKey: "store_shop_id",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    validatorValue: "idCommaSeperatedRequired",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "淘内门店ID",
    filterFieldId: "store_store_id",
    filterFieldKey: "store_store_id",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    validatorValue: "idCommaSeperatedRequired",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "门店名称",
    filterFieldId: "store_shop_name",
    filterFieldKey: "store_shop_name",
    operator: 11,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "商户分层",
    filterFieldId: "store_layer",
    filterFieldKey: "store_layer",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "剔除品牌",
    filterFieldId: "filter_store_brand_name",
    filterFieldKey: "filter_store_brand_name",
    operator: 12,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "basicattributes",
    validatorValue: "nameSeperatedRequired",
  },
  {
    filterFieldLabel: "商品服务标",
    filterFieldId: "item_ac_item_service_tag",
    filterFieldKey: "item_ac_item_service_tag",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  // { filterFieldLabel:'门店类目', filterFieldId: "store_shop_cat2_id", filterFieldKey: "store_shop_cat2_id", operator: 16 ,filterFieldComponentType:'cascaderSelect' ,filterFieldIdGroup:"basicattributes" },
  {
    filterFieldLabel: "门店主营类目",
    filterFieldId: "store_new_main_category_id",
    filterFieldKey: "store_new_main_category_id",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "淘系卖家ID",
    filterFieldId: "store_seller_id",
    filterFieldKey: "store_seller_id",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    validatorValue: "idCommaSeperatedRequired",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "所属品牌",
    filterFieldId: "store_brand_name",
    filterFieldKey: "store_brand_name",
    operator: 7,
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "业务区域",
    filterFieldId: "store_war_zone",
    filterFieldKey: "store_war_zone",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "新业务线",
    filterFieldId: "store_new_bu_flag",
    filterFieldKey: "store_new_bu_flag",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "所在城市",
    filterFieldId: "store_store_city_id",
    filterFieldKey: "store_store_city_id",
    operator: 7,
    filterFieldComponentType: "cascaderSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "店铺评分",
    filterFieldId: "store_shop_score",
    filterFieldKey: "store_shop_score",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "服务标",
    filterFieldId: "store_along_attribute_collection",
    filterFieldKey: "store_along_attribute_collection",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "创建时间",
    filterFieldId: "store_create_time",
    filterFieldKey: "store_create_time",
    operator: 23,
    filterFieldComponentType: "timeChoose",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "是否城代商户",
    filterFieldId: "store_is_city_agent",
    filterFieldKey: "store_is_city_agent",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "是否自配送",
    filterFieldId: "store_is_zps_shop",
    filterFieldKey: "store_is_zps_shop",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  // { filterFieldLabel:'基建分层', filterFieldId: "store_shop_level", filterFieldKey: "store_shop_level", operator: 71, filterFieldComponentType:'multipleSelect' ,filterFieldIdGroup:"basicattributes" },
  {
    filterFieldLabel: "商户成长分",
    filterFieldId: "store_shop_general_level_name",
    filterFieldKey: "store_shop_general_level_name",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "上月店铺综合评分",
    filterFieldId: "store_shop_general_level_last_month_name",
    filterFieldKey: "store_shop_general_level_last_month_name",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "SKU数量",
    filterFieldId: "store_sku_cnt",
    filterFieldKey: "store_sku_cnt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "basicattributes",
    filterFieldExtend: { min: 0 },
  },
  {
    filterFieldLabel: "风控合规数据",
    filterFieldId: "store_is_risk_comp",
    filterFieldKey: "store_is_risk_comp",
    operator: 38,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "标签群组",
    filterFieldId: "store_label_group_id",
    filterFieldKey: "store_label_group_id",
    filterFieldValue: "",
    filterFieldComponentType: "arrayInput",
    filterFieldIdGroup: "basicattributes",
    operator: 13,
  },
  {
    filterFieldLabel: "当天是否开启CPS",
    filterFieldId: "store_is_open_commission_fee",
    filterFieldKey: "store_is_open_commission_fee",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "CPS出佣比例",
    filterFieldId: "store_commission_rate_1d",
    filterFieldKey: "store_commission_rate_1d",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "basicattributes",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "业务属性",
    filterFieldId: "store_service_feature",
    filterFieldKey: "store_service_feature",
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "basicattributes",
    operator: 132,
  },
  {
    filterFieldLabel: "是否C5明星店",
    filterFieldId: "store_shop_star",
    filterFieldKey: "store_shop_star",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "商家是否入驻全能商厦",
    filterFieldId: "store_shop_e_mall",
    filterFieldKey: "store_shop_e_mall",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "商家是否入驻全能超市3.0",
    filterFieldId: "store_shop_e_mall3",
    filterFieldKey: "store_shop_e_mall3",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  {
    filterFieldLabel: "是否开通商家会员",
    filterFieldId: "store_member",
    filterFieldKey: "store_member",
    operator: 15,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "basicattributes",
  },
  // { filterFieldLabel:'测试专用', filterFieldId: "test_item", filterFieldKey: "test_item", operator: 15, filterFieldComponentType:'batchInputOrUploadOfFiles',filterFieldIdGroup:"baseInfo"},

  // 浏览和交易信息
  {
    filterFieldLabel: "近7天营业总时长",
    filterFieldId: "store_d7_open_minutes",
    filterFieldKey: "store_d7_open_minutes",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
  },
  {
    filterFieldLabel: "出货及时率",
    filterFieldId: "store_delivery_timely_rate",
    filterFieldKey: "store_delivery_timely_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "店类型",
    filterFieldId: "store_self_fetch",
    filterFieldKey: "store_self_fetch",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "browsedeal",
  },
  {
    filterFieldLabel: "是否当日/半日/次日达",
    filterFieldId: "store_whole_city_delivery",
    filterFieldKey: "store_whole_city_delivery",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "browsedeal",
  },
  {
    filterFieldLabel: "近7天有效订单量",
    filterFieldId: "store_d7_valid_order_cnt",
    filterFieldKey: "store_d7_valid_order_cnt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { precision: 1, step: 0.01 },
  },
  {
    filterFieldLabel: "近30天有效订单量",
    filterFieldId: "store_d30_valid_order_cnt",
    filterFieldKey: "store_d30_valid_order_cnt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
  },
  {
    filterFieldLabel: "近15天净交易总额",
    filterFieldId: "store_d15_sale_amt",
    filterFieldKey: "store_d15_sale_amt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
  },
  {
    filterFieldLabel: "近7天交易额",
    filterFieldId: "store_d7_total_amt",
    filterFieldKey: "store_d7_total_amt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
  },
  {
    filterFieldLabel: "近30天交易额",
    filterFieldId: "store_d30_total_amt",
    filterFieldKey: "store_d30_total_amt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
  },
  {
    filterFieldLabel: "近7天客单价",
    filterFieldId: "store_d7_order_price",
    filterFieldKey: "store_d7_order_price",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { unitNode: "元" },
  },
  {
    filterFieldLabel: "近15天客单价",
    filterFieldId: "store_d15_order_price",
    filterFieldKey: "store_d15_order_price",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { unitNode: "元" },
  },
  {
    filterFieldLabel: "近7天总优惠金额",
    filterFieldId: "store_d7_benefit_total_amt",
    filterFieldKey: "store_d7_benefit_total_amt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { unitNode: "元" },
  },
  {
    filterFieldLabel: "近30天总优惠金额",
    filterFieldId: "store_d30_benefit_total_amt",
    filterFieldKey: "store_d30_benefit_total_amt",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { unitNode: "元" },
  },
  {
    filterFieldLabel: "近7天无效订单率",
    filterFieldId: "store_d7_invalid_order_rate",
    filterFieldKey: "store_d7_invalid_order_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "近30天无效订单率",
    filterFieldId: "store_d30_invalid_order_rate",
    filterFieldKey: "store_d30_invalid_order_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "近7天客诉率",
    filterFieldId: "store_d7_complaint_rate",
    filterFieldKey: "store_d7_complaint_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "近30天客诉率",
    filterFieldId: "store_d30_complaint_rate",
    filterFieldKey: "store_d30_complaint_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "近7天平均退款时长",
    filterFieldId: "store_d7_refund_dur",
    filterFieldKey: "store_d7_refund_dur",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { unitNode: "分" },
  },
  {
    filterFieldLabel: "近30天留存率",
    filterFieldId: "store_d30_retention",
    filterFieldKey: "store_d30_retention",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "近30天店铺补贴力度",
    filterFieldId: "store_d30_shop_subsidy",
    filterFieldKey: "store_d30_shop_subsidy",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "近30天平台补贴力度",
    filterFieldId: "store_d30_platform_subsidy",
    filterFieldKey: "store_d30_platform_subsidy",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "browsedeal",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "商品分层",
    filterFieldId: "c5_item_layer",
    filterFieldKey: "c5_item_layer",
    operator: 132,
    filterFieldComponentType: "cascaderSelect",
    filterFieldIdGroup: "skuTrade",
  },
  // 营销信息
  {
    filterFieldLabel: "商户自营销率",
    filterFieldId: "store_shop_mkt_rate",
    filterFieldKey: "store_shop_mkt_rate",
    operator: 23,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "marketing",
    filterFieldExtend: { min: 0, max: 100, unitNode: "%" },
  },
  {
    filterFieldLabel: "是否含新客活动",
    filterFieldId: "store_is_newuser_activity",
    filterFieldKey: "store_is_newuser_activity",
    operator: 1,
    filterFieldComponentType: "radio",
    filterFieldIdGroup: "marketing",
  },
  {
    filterFieldLabel: "门店活动类型",
    filterFieldId: "store_activity_type_names_1d",
    filterFieldKey: "store_activity_type_names_1d",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "marketing",
  },
  {
    filterFieldLabel: "运费满减门槛",
    filterFieldId: "platform_freight_manjian_condition",
    filterFieldKey: "platform_freight_manjian_condition",
    operator: 26,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "marketing",
    unitNode: "fen",
  },
  {
    filterFieldLabel: "运费满减金额",
    filterFieldId: "platform_freight_manjian_discount",
    filterFieldKey: "platform_freight_manjian_discount",
    operator: 26,
    filterFieldComponentType: "rangeNumberInput",
    filterFieldIdGroup: "marketing",
    unitNode: "fen",
  },
  {
    filterFieldLabel: "活动类型",
    filterFieldId: "store_activity_child_type",
    filterFieldKey: "store_activity_child_type",
    operator: 71,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "marketing",
  },
  {
    filterFieldLabel: "年货玩法",
    filterFieldId: "activity_child_type_with_condition",
    filterFieldKey: "activity_child_type_with_condition",
    operator: 71,
    filterFieldComponentType: "mktConditions",
    filterFieldIdGroup: "required",
  },
  {
    filterFieldLabel: "营销标签",
    filterFieldKey: "item_data_scene",
    filterFieldId: "item_data_scene",
    operator: 132,
    filterFieldComponentType: "multipleSelect",
    filterFieldIdGroup: "required",
  },
];

function getPoolMap(params, Group) {
  let result = [];
  filtersConfigMap.map((v) => {
    if (params.includes(v.filterFieldKey) && v.filterFieldIdGroup == Group) {
      result.push(v);
    }
  });
  return result;
}

export const poolFiltersConfig = {
  23002: {
    //新零售全量商品池
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "sku_brand_id",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(
      [
        "item_d7_valid_order_cnt",
        "item_d30_valid_order_cnt",
        "store_d7_shop_valid_order_cnt",
        "store_d30_shop_valid_order_cnt",
        "item_d7_total_amt",
        "item_d30_total_amt",
        "item_min_price_7d",
        // 'item_min_price_15d',
      ],
      "skuTrade",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "item_present_price",
        "item_discount",
        // 'item_ac_PZ'
      ],
      "skuMarket",
    ),
    scene: getPoolMap(["timePeriod", "crowd"], "scene"),
  },
  50001: {
    //零售全量品池
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "item_bar_compete_pwr_30d",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "sku_brand_id",
        "item_ac_item_service_tag",
        "item_online_rate",
        "item_e_mall",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        "item_store_shop_star",
        "item_shop_e_mall",
        "item_shop_e_mall3",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(
      [
        "item_d7_valid_order_cnt",
        "item_d30_valid_order_cnt",
        "store_d7_shop_valid_order_cnt",
        "store_d30_shop_valid_order_cnt",
        "item_d7_total_amt",
        "item_d30_total_amt",
        "item_min_price_7d",
        "c5_item_layer",
        "item_city_top500_sold",
        // 'item_min_price_15d',
      ],
      "skuTrade",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "item_present_price",
        "item_discount",
        // 'item_ac_PZ'
      ],
      "skuMarket",
    ),
    scene: getPoolMap(
      [
        "timePeriod",
        "crowd",
        "item_scene_id_subdivide_scene",
        "item_solar_term",
      ],
      "scene",
    ),
  },
  60001: {
    //医药全量品池
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "item_bar_compete_pwr_30d",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "sku_brand_id",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        "item_shop_e_mall3",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(
      [
        "item_d7_valid_order_cnt",
        "item_d30_valid_order_cnt",
        "store_d7_shop_valid_order_cnt",
        "store_d30_shop_valid_order_cnt",
        "item_d7_total_amt",
        "item_d30_total_amt",
        "item_min_price_7d",
        "item_city_top500_sold",
        // 'item_min_price_15d',
      ],
      "skuTrade",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "item_present_price",
        "item_discount",
        // 'item_ac_PZ'
      ],
      "skuMarket",
    ),
  },
  70001: {
    //全能大店-全量商品池
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "item_bar_compete_pwr_30d",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "sku_brand_id",
        "item_ac_item_service_tag",
        "item_online_rate",
        "item_e_mall",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        "item_store_shop_star",
        "item_shop_e_mall",
        "item_shop_e_mall3",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(
      [
        "item_d7_valid_order_cnt",
        "store_d7_shop_valid_order_cnt",
        "store_d30_shop_valid_order_cnt",
        "item_d7_total_amt",
        "item_d30_total_amt",
        "item_min_price_7d",
        "c5_item_layer",
        "item_city_top500_sold",
        // 'item_min_price_15d',
      ],
      "skuTrade",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "item_present_price",
        "item_discount",
        // 'item_ac_PZ'
      ],
      "skuMarket",
    ),
    scene: getPoolMap(
      [
        "timePeriod",
        "crowd",
        "item_scene_id_subdivide_scene",
        "item_solar_term",
      ],
      "scene",
    ),
  },
  70003: {
    //全能大店-营销商品池
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "item_bar_compete_pwr_30d",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "sku_brand_id",
        "item_ac_item_service_tag",
        "item_online_rate",
        "item_e_mall",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        "item_store_shop_star",
        "item_shop_e_mall",
        "item_shop_e_mall3",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(
      [
        "item_d7_valid_order_cnt",
        "store_d7_shop_valid_order_cnt",
        "store_d30_shop_valid_order_cnt",
        "item_d7_total_amt",
        "item_d30_total_amt",
        "item_min_price_7d",
        "c5_item_layer",
        "item_city_top500_sold",
        // 'item_min_price_15d',
      ],
      "skuTrade",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "item_present_price",
        "item_discount",
        // 'item_ac_PZ'
      ],
      "skuMarket",
    ),
    scene: getPoolMap(
      [
        "timePeriod",
        "crowd",
        "item_scene_id_subdivide_scene",
        "item_solar_term",
      ],
      "scene",
    ),
  },
  80001: {
    // 选品池集合运算
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "item_bar_compete_pwr_30d",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "sku_brand_id",
        "item_ac_item_service_tag",
        "item_online_rate",
        "item_e_mall",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        "item_store_shop_star",
        "item_shop_e_mall",
        "item_shop_e_mall3",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(
      [
        "item_d7_valid_order_cnt",
        "store_d7_shop_valid_order_cnt",
        "store_d30_shop_valid_order_cnt",
        "item_d7_total_amt",
        "item_d30_total_amt",
        "item_min_price_7d",
        "c5_item_layer",
        "item_city_top500_sold",
        // 'item_min_price_15d',
      ],
      "skuTrade",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "item_present_price",
        "item_discount",
        // 'item_ac_PZ'
      ],
      "skuMarket",
    ),
    scene: getPoolMap(
      [
        "timePeriod",
        "crowd",
        "item_scene_id_subdivide_scene",
        "item_solar_term",
      ],
      "scene",
    ),
  },
  24001: {
    //营销商品池
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(
      [
        "item_d7_valid_order_cnt",
        "item_d30_valid_order_cnt",
        "store_d7_shop_valid_order_cnt",
        "store_d30_shop_valid_order_cnt",
        "item_d7_total_amt",
        "item_d30_total_amt",
        "item_min_price_7d",
        // 'item_min_price_15d',
      ],
      "skuTrade",
    ),
    // 'skuMarket': getPoolMap([
    //   'item_distribute',
    //   'item_ac_PZ',
    //   'item_activity_types',
    //   'shop_activity_types'
    // ])
    skuMarket: [
      {
        filterFieldLabel: "商品分布",
        filterFieldId: "item_distribute",
        filterFieldKey: "item_distribute",
        operator: 7,
        filterFieldComponentType: "checkbox",
        filterFieldIdGroup: "skuMarket",
      },
      // { filterFieldLabel:'品质联盟', filterFieldId: "item_ac_PZ", filterFieldKey: "item_ac_PZ", operator: 13, filterFieldComponentType: 'select',filterFieldIdGroup:"skuMarket"},
      {
        filterFieldLabel: "店铺活动",
        filterFieldId: "shop_activity_types",
        filterFieldKey: "shop_activity_types",
        operator: 132,
        filterFieldComponentType: "cascaderSelect",
        filterFieldIdGroup: "skuMarket",
      },
      {
        filterFieldLabel: "商品活动",
        filterFieldId: "item_activity_types",
        filterFieldKey: "item_activity_types",
        operator: 132,
        filterFieldComponentType: "multipleSelect",
        filterFieldIdGroup: "skuMarket",
      },
    ],
  },
  23003: {
    //算法精品池
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_shop_star",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
  },
  23004: {
    //时令商品池
    required: getPoolMap(["algorithm_season_tags"], "required"),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_general_level_name",
        // 'store_level',
        "item_ac_serviceFeature",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
  },
  24002: {
    //商品类营销活动商品池
    required: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "brand_activity_id",
        "activity_child_type",
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
      ],
      "skuMarket",
    ),
  },
  50003: {
    //零售营销品池
    required: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "brand_activity_id",
        "activity_child_type",
        "activity_child_type_with_condition",
        "item_data_scene",
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_bar_compete_pwr_30d",
        "item_ac_item_service_tag",
        "item_online_rate",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        "item_store_shop_star",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
        "item_user_scope",
        "item_start_with",
      ],
      "skuMarket",
    ),
    skuTrade: getPoolMap(
      ["c5_item_layer", "item_city_top500_sold"],
      "skuTrade",
    ),
    scene: getPoolMap(
      ["item_scene_id_subdivide_scene", "item_solar_term"],
      "scene",
    ),
  },
  50007: {
    // 零售价格品池
    required: getPoolMap(
      [
        "item_orgin_price_baseline_7d",
        "item_price_baseline_30d",
        "item_price_baseline_15d",
        "item_price_baseline_7d",
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_bar_compete_pwr_30d",
        "item_ac_item_service_tag",
        "item_online_rate",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        "item_store_shop_star",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
        "item_user_scope",
        "item_start_with",
      ],
      "skuMarket",
    ),
    skuTrade: getPoolMap(
      ["c5_item_layer", "item_city_top500_sold"],
      "skuTrade",
    ),
    scene: getPoolMap(
      ["item_scene_id_subdivide_scene", "item_solar_term"],
      "scene",
    ),
  },
  60003: {
    //医药营销品池
    required: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "brand_activity_id",
        "activity_child_type",
        "activity_child_type_with_condition",
        "item_data_scene"
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_bar_compete_pwr_30d",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuTrade: getPoolMap(["item_city_top500_sold"], "skuTrade"),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
        "item_start_with",
      ],
      "skuMarket",
    ),
  },
  24003: {
    //权益类营销活动商品池
    required: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "brand_activity_id",
        "activity_child_type",
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
      ],
      "skuMarket",
    ),
  },
  50004: {
    //零售权益品池
    required: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "brand_activity_id",
        "activity_child_type",
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_bar_compete_pwr_30d",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
      ],
      "skuMarket",
    ),
    skuTrade: getPoolMap(["c5_item_layer"], "skuTrade"),
  },
  60004: {
    //医药权益品池
    required: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "brand_activity_id",
        "activity_child_type",
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_bar_compete_pwr_30d",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        "item_store_layer",
        "filter_item_brand_name",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
      ],
      "skuMarket",
    ),
  },
  24005: {
    //百亿补贴商品池
    required: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "brand_activity_id",
        "activity_child_type",
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "item_goodsId",
        "item_upcId",
        "item_name",
        "filter_item_name",
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        // 'store_level',
        "store_general_level_name",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        // 'item_shop_general_level_last_month_name',
        "item_ac_serviceFeature",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    // skuTrade: getPoolMap(
    //   [
    //     "item_d7_valid_order_cnt",
    //     "item_d30_valid_order_cnt",
    //     "store_d7_shop_valid_order_cnt",
    //     "store_d30_shop_valid_order_cnt",
    //     "item_d7_total_amt",
    //     "item_d30_total_amt",
    //     "item_min_price_7d",
    //     // 'item_min_price_15d'
    //   ],
    //   "skuTrade",
    // ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        // 'item_ac_PZ',
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
        "shop_activity_types",
      ],
      "skuMarket",
    ),
    skuTrade: getPoolMap(["c5_item_layer"], "skuTrade"),
  },
  41001: {
    //百亿补贴打标池
    required: getPoolMap(
      [
        "activity_child_type",
        "market_activity_id",
        "invite_activity_id",
        // 'shop_activity_types'
      ],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        "market_activity_id",
        "invite_activity_id",
        "item_upcId",
        "item_name",
        "item_goodsCategory",
        "marketItem_activity_price_less_than_min_price_7d",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "store_id",
        "store_name",
        // 'store_cat2_id',
        "store_main_category_id",
        "store_city_id",
        "store_business_region",
        "store_new_bu_flag",
        // 'store_score',
        "item_brand_name",
        "item_d7_open_minutes",
        "item_ac_serviceFeature",
        "item_is_open_commission_fee",
        "item_commission_rate_1d",
        "store_general_level_name",
        "item_ac_serviceTag",
        // 'item_ac_servicePackage'
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_store_market_rate",
        // 'item_ac_PZ',
        "item_orig_price",
        // 'item_activity_types',
        // 'shop_activity_types',
        "marketItem_present_price",
        "marketItem_discount",
      ],
      "skuMarket",
    ),
  },
  33001: {
    //全量门店池
    basicattributes: getPoolMap(
      [
        "store_shop_id",
        "store_store_id",
        "store_shop_name",
        // 'store_shop_cat2_id',
        "store_new_main_category_id",
        // 'store_seller_id',
        "store_brand_name",
        // 'store_war_zone',
        // 'store_new_bu_flag',
        "store_store_city_id",
        "store_shop_score",
        "store_along_attribute_collection",
        "store_create_time",
        "store_is_city_agent",
        "store_is_zps_shop",
        // 'store_shop_level',
        "store_shop_general_level_name",
        // 'store_shop_general_level_last_month_name',
        "store_sku_cnt",
        "store_is_risk_comp",
        "store_label_group_id",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
        "store_service_feature",
        "store_layer",
        "filter_store_brand_name",
        "store_shop_star",
        "store_shop_e_mall",
        "store_shop_e_mall3",
        "store_member",
      ],
      "basicattributes",
    ),
    browsedeal: getPoolMap(
      [
        "store_d7_open_minutes",
        // 'store_delivery_timely_rate',
        // 'store_self_fetch',
        // 'store_whole_city_delivery',
        "store_d7_valid_order_cnt",
        "store_d30_valid_order_cnt",
        "store_d15_sale_amt",
        "store_d7_total_amt",
        "store_d30_total_amt",
        "store_d7_order_price",
        "store_d15_order_price",
        "store_d7_benefit_total_amt",
        "store_d30_benefit_total_amt",
        // 'store_d7_invalid_order_rate',
        // 'store_d30_invalid_order_rate',
        "store_d7_complaint_rate",
        "store_d30_complaint_rate",
        "store_d7_refund_dur",
        "store_d30_retention",
        "store_d30_shop_subsidy",
        "store_d30_platform_subsidy",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
      ],
      "browsedeal",
    ),
    marketing: getPoolMap(
      [
        "store_shop_mkt_rate",
        "store_is_newuser_activity",
        "store_activity_type_names_1d",
      ],
      "marketing",
    ),
  },
  33002: {
    //营销门店池
    required: getPoolMap(["store_activity_id", "store_invite_id"], "required"),
    basicattributes: getPoolMap(
      [
        "store_shop_id",
        "store_store_id",
        "store_shop_name",
        // 'store_shop_cat2_id',
        "store_new_main_category_id",
        // 'store_seller_id',
        "store_brand_name",
        // 'store_war_zone',
        // 'store_new_bu_flag',
        "store_store_city_id",
        "store_shop_score",
        "store_along_attribute_collection",
        "store_create_time",
        "store_is_city_agent",
        "store_is_zps_shop",
        // 'store_shop_level',
        "store_shop_general_level_name",
        // 'store_shop_general_level_last_month_name',
        "store_sku_cnt",
        "store_is_risk_comp",
        "store_label_group_id",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
        "store_service_feature",
        "store_layer",
        "filter_store_brand_name",
        "store_shop_star",
        "store_member",
      ],
      "basicattributes",
    ),
    browsedeal: getPoolMap(
      [
        "store_d7_open_minutes",
        // 'store_delivery_timely_rate',
        // 'store_self_fetch',
        // 'store_whole_city_delivery',
        "store_d7_valid_order_cnt",
        "store_d30_valid_order_cnt",
        "store_d15_sale_amt",
        "store_d7_total_amt",
        "store_d30_total_amt",
        "store_d7_order_price",
        "store_d15_order_price",
        "store_d7_benefit_total_amt",
        "store_d30_benefit_total_amt",
        // 'store_d7_invalid_order_rate',
        // 'store_d30_invalid_order_rate',
        "store_d7_complaint_rate",
        "store_d30_complaint_rate",
        "store_d7_refund_dur",
        "store_d30_retention",
        "store_d30_shop_subsidy",
        "store_d30_platform_subsidy",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
      ],
      "browsedeal",
    ),
    marketing: getPoolMap(
      [
        "store_shop_mkt_rate",
        "store_is_newuser_activity",
        "store_activity_child_type",
      ],
      "marketing",
    ),
  },
  33003: {
    // 平台运费红包专用门店池
    basicattributes: getPoolMap(
      [
        "store_shop_id",
        "store_store_id",
        "store_shop_name",
        // 'store_shop_cat2_id',
        "store_new_main_category_id",
        // 'store_seller_id',
        "store_brand_name",
        // 'store_war_zone',
        // 'store_new_bu_flag',
        "store_store_city_id",
        "store_shop_score",
        "store_along_attribute_collection",
        "store_create_time",
        "store_is_city_agent",
        "store_is_zps_shop",
        // 'store_shop_level',
        "store_shop_general_level_name",
        // 'store_shop_general_level_last_month_name',
        "store_sku_cnt",
        "store_is_risk_comp",
        "store_label_group_id",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
        "store_service_feature",
        "store_layer",
        "filter_store_brand_name",
      ],
      "basicattributes",
    ),
    browsedeal: getPoolMap(
      [
        "store_d7_open_minutes",
        // 'store_delivery_timely_rate',
        // 'store_self_fetch',
        // 'store_whole_city_delivery',
        "store_d7_valid_order_cnt",
        "store_d30_valid_order_cnt",
        "store_d15_sale_amt",
        "store_d7_total_amt",
        "store_d30_total_amt",
        "store_d7_order_price",
        "store_d15_order_price",
        "store_d7_benefit_total_amt",
        "store_d30_benefit_total_amt",
        // 'store_d7_invalid_order_rate',
        // 'store_d30_invalid_order_rate',
        "store_d7_complaint_rate",
        "store_d30_complaint_rate",
        "store_d7_refund_dur",
        "store_d30_retention",
        "store_d30_shop_subsidy",
        "store_d30_platform_subsidy",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
      ],
      "browsedeal",
    ),
    marketing: getPoolMap(
      [
        "store_shop_mkt_rate",
        "store_is_newuser_activity",
        "store_activity_type_names_1d",
        "platform_freight_manjian_condition",
        "platform_freight_manjian_discount",
      ],
      "marketing",
    ),
  },
  33004: {
    // 全量商户以品圈店池
    baseInfo: getPoolMap(
      [
        //复用商品池的map
        "item_goodsId",
        "item_upcId",
        "item_name",
        // 'filter_item_name', //剔除关键字
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "item_bar_compete_pwr_30d",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "sku_brand_id",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        "store_cat2_id",
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "item_store_layer",
        "store_general_level_name",
        // 'filter_item_brand_name' //剔除品牌
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "item_present_price",
        // 'marketItem_present_price',
        "item_discount",
        // 'marketItem_discount'
      ],
      "skuMarket",
    ),
  },
  33005: {
    // 营销以品圈店池
    required: getPoolMap(
      ["market_activity_id", "invite_activity_id", "activity_child_type"],
      "required",
    ),
    baseInfo: getPoolMap(
      [
        //复用商品池的map
        "item_goodsId",
        "item_upcId",
        "item_name",
        // 'filter_item_name', //剔除关键字
        "item_goodsCategory",
        "item_pic_standard",
        "item_risk_comp",
        "item_ac_CAT",
        "item_status",
        "item_ac_goodsType",
        "item_label_group_id",
        "item_bar_compete_pwr_30d",
        "item_reserve_price_compete_pwr",
        "item_sale_price_compete_pwr",
        "sku_brand_id",
        "item_ac_item_service_tag",
      ],
      "baseInfo",
    ),
    skuStore: getPoolMap(
      [
        "shop_id",
        "store_id",
        "store_name",
        "store_cat2_id",
        "store_main_category_id",
        "store_city_id",
        "store_score",
        "item_ac_serviceTag",
        "item_start_price",
        "item_brand_name",
        "item_d7_open_minutes",
        "store_direct",
        "store_city_agent",
        "store_zps",
        // 'store_level',
        "item_store_layer",
        "store_general_level_name",
        // 'filter_item_brand_name' //剔除品牌
      ],
      "skuStore",
    ),
    skuMarket: getPoolMap(
      [
        "item_distribute",
        "item_orig_price",
        "marketItem_present_price",
        "marketItem_discount",
      ],
      "skuMarket",
    ),
  },
  33007: {
    //全能大店门店池
    basicattributes: getPoolMap(
      [
        "store_shop_id",
        "store_store_id",
        "store_shop_name",
        // 'store_shop_cat2_id',
        "store_new_main_category_id",
        // 'store_seller_id',
        "store_brand_name",
        // 'store_war_zone',
        // 'store_new_bu_flag',
        "store_store_city_id",
        "store_shop_score",
        "store_along_attribute_collection",
        "store_create_time",
        "store_is_city_agent",
        "store_is_zps_shop",
        // 'store_shop_level',
        "store_shop_general_level_name",
        // 'store_shop_general_level_last_month_name',
        "store_sku_cnt",
        "store_is_risk_comp",
        "store_label_group_id",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
        "store_service_feature",
        "store_layer",
        "filter_store_brand_name",
        "store_shop_star",
        "store_shop_e_mall",
        "store_shop_e_mall3",
        "store_member",
      ],
      "basicattributes",
    ),
    browsedeal: getPoolMap(
      [
        "store_d7_open_minutes",
        // 'store_delivery_timely_rate',
        // 'store_self_fetch',
        // 'store_whole_city_delivery',
        "store_d7_valid_order_cnt",
        "store_d30_valid_order_cnt",
        "store_d15_sale_amt",
        "store_d7_total_amt",
        "store_d30_total_amt",
        "store_d7_order_price",
        "store_d15_order_price",
        "store_d7_benefit_total_amt",
        "store_d30_benefit_total_amt",
        // 'store_d7_invalid_order_rate',
        // 'store_d30_invalid_order_rate',
        "store_d7_complaint_rate",
        "store_d30_complaint_rate",
        "store_d7_refund_dur",
        "store_d30_retention",
        "store_d30_shop_subsidy",
        "store_d30_platform_subsidy",
        "store_is_open_commission_fee",
        "store_commission_rate_1d",
      ],
      "browsedeal",
    ),
    marketing: getPoolMap(
      [
        "store_shop_mkt_rate",
        "store_is_newuser_activity",
        "store_activity_type_names_1d",
      ],
      "marketing",
    ),
  },
  25002: {
    // 营销以品圈店池
    skuStore: getPoolMap(
      [
        "item_wid",
        // 'filter_item_brand_name' //剔除品牌
      ],
      "skuStore",
    ),
  },
};
