import debugFn from 'debug';
import React from 'react';
import {Radio, Step} from '@alife/next';
import { PageBase } from '@/home/<USER>';
import {formatTimeStamp} from "@/utils/time";
import moment from "moment";
import { StatusLabel } from '@/comps/Label';
import {isArray} from "@/utils/validators";
import * as api from "@/adator/api";
import {onRequestError} from "@/utils/api";
// import {formatRule, publishStatusEnum} from "@/containers/PoolPage/PoolListPage/common";

const debug = debugFn('selection:poolPage:common')

export function Steps({ current, middleText }) {

  return (
    <div className="step-wraper">
      <Step current={current} shape="dot">
        {[['圈选规则', ''], [middleText, ''], ['完成', '']].map((item, index) => <Step.Item key={index} title={item[0]} content={item[1]} />)}
      </Step>
    </div>
  )
}


export class PoolPageBase extends PageBase {
  static Container = PageBase.Container

  get isStore() {
    return this.location.pathname.startsWith('/storepool')
  }
}

// export const publishStatusEnum = {
//   UNPUBLISHED: '0', //未发布
//   TWOUNPUBLISHED: '1', //需要发布
//   PROCESSING: '5', //发布中
//   SUCCESS: '15', //发布成功
//   WAITING_SUCCESS: '10', //待生效
//   FAILED: '20',//发布失败
//   OFF_LINE:'25' //已下线
// }

export const publishStatusEnum = [
  {label: '全部', value: ''},
  {label: '未发布', value: 0},
  {label: '需要发布', value: 1},
  {label: '发布中', value: 5},
  {label: '已发布', value: 15},
  // {label: '待生效', value: 10},
  {label: '发布失败', value: 20},
  {label: '已下线', value: 25},
]

export const cleanResultEnum = [
  {label: '待处理', value: 'PENDING'},
  {label: '下线', value: 'DONE'},
  {label: '拒绝处理', value: 'REJECT'},
  {label: '延期处理', value: 'DELAY'},
]

export const expertStatusEnum = [
  {label: '全部', value: ''},
  {label: '已下线', value: 0},
  {label: '已发布', value: 1},
  {label: '发布中', value: 2},
  {label: '发布失败', value: 3},
  {label: '未发布', value: -1},
  {label: '发布成功，待生效', value: 11},
]

export const wayEnum = [
  {label: '标签选品', value: 'CREATE_RULE'},
  {label: '标签选店', value: 'CREATE_STORE'},
  {label: '文件上传', value: 'CREATE_EXCEL'},
]

export const exportStateMap = {
  '0':{text:"已下线",style:"filed"},
  '1':{text:"已发布",style:"success"},
  '2':{text:"发布中",style:"warning"},
  '3':{text:"发布失败",style:"error"},
  '-1':{text:"未发布",style:"help"},
  '11':{text:"发布成功，待生效",style:"warning"}
}

export const stateMap = {
  0: 'warning',
  1: 'warning',
  // 5: 'notice',
  5: 'warning',
  15: 'success',
  10: 'help',
  20: 'error',
  25: 'filed'
}

export const itemStatusEnum = [
  {label: '上架', value: '1'},
  {label: '下架', value: '0'},
]

export const informationEnum = {
  CHANNEL_SCHEDULE_INFO:"频道资源位配置",
  WELKIN_RESOURCE:"兵马俑资源位",
  ALSC_RESOURCE:"墨斗资源位"
}


export function statusRender(status){

  let item = publishStatusEnum.filter((v) => v.value === status);

  let labelText = (item && item.length > 0) ? item[0].label : '';
  return <div style={{paddingLeft:'10px'}}>
    <StatusLabel text={labelText} type={stateMap[status]}></StatusLabel>
  </div>
}


export function timeRangeRender(_value, _index, record){
  let {effectAt, expireAt} = record;
  effectAt = effectAt ? formatTimeStamp(moment(effectAt)) : ''
  expireAt = expireAt ? formatTimeStamp(moment(expireAt)) : ''
  return <div>
    <p>{effectAt}~</p>
    <p>{expireAt}</p>
  </div>
}

export function  renderNewStateMap(value) {
  let text;
  let icon;
  switch (value){
    case 1:
      text = '上架';
      icon = 'success';
      break;
    case 0:
      text = '下架';
      icon = 'error';
      break;
    default:
      break;
  }
  return <StatusLabel text={text} type={icon}/>;
}

export function  renderShopStateMap(value) {
  let text;
  let icon;
  switch (value){
    case 1:
      text = '营业中';
      icon = 'success';
      break;
    case 3:
      text = '休息中';
      icon = 'warning';
      break;
    case 9:
      text = '暂停营业';
      icon = 'error';
      break;
    case 500:
      text = '百度外卖侧异常';
      icon = 'error';
      break;
    default:
      break;
  }
  return <StatusLabel text={text} type={icon}/>;
}

export function dateRender(value, _index, _record){
  if (!value) return value
  else return formatTimeStamp(moment(value))
}

export const poolTypeEnum = [
  {label:'商品',value:1},
  {label:'门店',value:2}
]

export const operatorEum = {
  "timePeriod":13,
  "crowd":13,
  "goodsCategory":16,
  "isCatComp":13,
  "isRiskComp":1,
  "newMainCategoryId":7,
  "goodsNameKeyWord":11,
  "upcId":13,
  "goodsId":7,
  "item_name":11,
  "filter_item_name":12,
  "shopName": 11,
  "shopId": 7,
  "storeId": 7,
  "brandName": 7,
  "shop_name": 11,
  "shop_id": 7,
  "store_id": 7,
  "item_brand_name": 7,
}

export const createModeEum =
  {
    'CREATE_EXCEL': 'Excel上传',
    'CREATE_RULE': '标签选品'
  }

export const timeEum = [
  {label: '早间', value: 'morning'},
  {label: '中午', value: 'noon'},
  {label: '下午', value: 'afternoon'},
  {label: '晚间', value: 'evening'},
  {label: '上半夜', value: 'midnight'},
  {label: '下半夜', value: 'night'}
]

export const crowdEum = [
  {label: '小城中老年', value: 'middleAge'},
  {label: '小城青年', value: 'young'},
  {label: '年轻妈妈', value: 'youngMother'},
  {label: '都市银发', value: 'theOld'},
  {label: '资深中产', value: 'middleClass'},
  {label: '都市95后', value: 'post95'},
  {label: '都市白领', value: 'whiteCollar'},
  {label: '都市蓝领', value: 'blueCollar'},
  {label: '未知', value: 'unknown'},
]

export const isCatCompEum = [
  {label:'全部',value:''},
  {label:'正确',value:'Y_CAT'},
]

export const isRiskCompEum= [
  {label:'普通等级',value:1},
  {label:'严格等级',value:2},
]

export const  refreshModeListEum = [
  {label: '每天更新', value: 1},
  {label: '不更新', value: 0},
  {label: '实时更新', value: 2},
  {label: '实时更新', value: 3}
]

/*普通池子*/
export const  refreshModeEum= [
  {label: '每天更新', value: 1},
  {label: '不更新', value: 0}
]

/*实时池子*/
export const  refreshRealModeEum= [
  {label: '实时更新', value: 2},
  {label: '不更新', value: 0}
]

/*百亿补贴*/
export const  refreshBYRealModeEum= [
  {label: '实时更新', value: 3},
  {label: '不更新', value: 0}
]

export const refreshModeGroup = {
  1: refreshModeEum,
  2: refreshRealModeEum,
  3: refreshBYRealModeEum
}

/*普通池子*/
export const  refreshNewModeEum= [
  {label: '每日全量更新（有增有减）', value: 1},
]

export const outSynPlatformsEum = [
  {label: '选投', value: 'xt'},
  {label: '大促打标', value: 'dcdb'}
]

export const idModeEnum = {
  "ITEM_ID":"商品ID",
  "ITEM_UPC":"商品条码",
  "SHOP_ELE_ID":"ELE门店ID",
  "SHOP_ID":"淘内门店ID"
}

export const usageScenarioEnum = {
  "PUTIN_ACTIVITY":"投放活动",
  "WELKIN_RESOURCE":"兵马俑排期",
}



/**
 * 池子列表：type 1 非实时 2 实时
 *  注：更新机制用到 type
 * 
 * 注意：这里定义的 poolEum、storeEum 被依赖的地方很多，但是应该是从后端读取的，否则每次增加底池，前端这里的代码都要跟着做修改
 * 为了解决这个问题，现在选投应用启动时会使用 __dangerous_updateStoreAndPoolEnum__ 将这里的枚举值替换成后端接口下发的数据
 * 
 * 重要！！！这里的枚举值不应该再被修改了！！！
 */
const __originLegacyPoolEnum  = [
  {id: '50001', title: '【推荐使用】零售全量品池', tips: '从本地零售全量商品池中筛选商品，建议C4/C5使用', type: 1},
  {id: '60001', title: '【推荐使用】医药全量品池', tips: '从本地医药全量商品池中筛选商品，建议医药行业使用', type: 1},
  {id: '70001', title: '全能大店-全量商品池', tips: '从全能大店全量商品池中筛选商品', type: 1},
  {id: '70003', title: '全能大店-营销商品池', tips: '从全能大店全量商品营销池中筛选商品', type: 2},
  {id: '80001', title: '选品池集合运算', tips: '', type: 1},
  {id: '23003', title: '算法精品池', tips: '从算法生成的精品商品池中筛选商品', type: 1},
  {id: '23004', title: '时令商品池', tips: '从时令商品池中筛选商品', type: 1, message: '时令必填'},
  // {id: '23002', title: '【不推荐】全量商品池', tips: '从本地全量商品池中筛选商品，建议使用零售全量品池和医药全量品池', type: 1},

  {id: '50003', title: '【推荐使用】零售营销品池', tips: '筛选参加了商品类营销活动（如特价、折扣等）的零售商品，建议C4/C5使用', type: 2, message: '标签必填项至少填一项'},
  // {id: '50004', title: '【推荐使用】零售权益品池', tips: '筛选参加了权益类营销活动（如单品券、品类券等）的零售商品，建议C4/C5使用', type: 2, message: '标签必填项至少填一项'},
  {id: '60003', title: '【推荐使用】医药营销品池', tips: '筛选参加了商品类营销活动（如特价、折扣等）的医药商品，建议医药行业使用', type: 2, message: '标签必填项至少填一项'},
  // {id: '60004', title: '【推荐使用】医药权益品池', tips: '筛选参加了权益类营销活动（如单品券、品类券等）的医药商品，建议医药行业使用', type: 2, message: '标签必填项至少填一项'},
  {id: '24001', title: '营销商品池', tips: '从参加了营销活动（如商品特价、折扣等）的商品池中筛选商品', type: 1},
  {id: '24005', title: '百亿补贴商品池', tips: '从打了百亿补贴标的商品池中筛选商品', type: 2, message: '标签必填项至少填一项'},
  {id: '41001', title: '百亿补贴打标池', tips: '圈选参加了商品类营销活动的商品，打上百亿补贴的营销标', type: 2, message: '标签中商品活动必填，其他选填'},
  {id: '24002', title: '【不推荐】商品类营销活动商品池', tips: '从参加了商品类营销活动（如商品特价、折扣等）的商品池中筛选商品', type: 2, message: '标签必填项至少填一项'},
  // {id: '24003', title: '【不推荐】权益类营销活动商品池', tips: '从参加了本地零售单品券或品类券的商品池中筛选商品', type: 2, message: '标签必填项至少填一项'},
  {id: '25002', title: '全能商厦商品审核池', tips: '', type: 1},
  {id: '50007', title: '零售价格品池', tips: '零售价格品池', type: 2, message: '标签必填项至少填一项'},
]

export const poolEum = [...__originLegacyPoolEnum]

const __originLegacyStoreEnum = [
  {id: '33001', title: '全量门店池', tips: '支持新零售侧已迁移商超、果蔬、厨房生鲜、鸭脖卤味、鲜花绿植、休闲零食、医药品类全量门店的筛选', type: 1},
  {id: '33003', title: '平台运费红包专用门店池', tips: '用于创建平台营销活动（目前仅支持平台运费红包）', type: 1},
  {id: '33004', title: '以品选店池', tips: '支持根据商品信息反圈门店', type: 1},
  {id: '33005', title: '营销以品选店池', tips: '支持根据营销商品信息反圈门店', type: 1,message: '标签必填项至少填一项'},
  {id: '33002', title: '营销门店池', tips: '支持饿了么昆仑后台营销活动中门店营销', type: 2, message: '标签必填项至少填一项'},
  {id: '33007', title: '全能大店门店池', tips: '', type: 1},
]

export const storeEum = [...__originLegacyStoreEnum]

export function __dangerous_updateStoreAndPoolEnum__(nextStoreEnum, nextPoolEnum, onError) {
  storeEum.splice(0, storeEum.length, ...nextStoreEnum);
  poolEum.splice(0, poolEum.length, ...nextPoolEnum);

  try {
    if (JSON.stringify(storeEum) !== JSON.stringify(nextStoreEnum)) {
      throw new Error("fail to overide storeEum");
    }
    for (let i = 0; i < nextStoreEnum.length; i++) {
      const item = nextStoreEnum[i];
      const _item = __originLegacyStoreEnum.find((it) => it.id === item.id);
      if (_item && item.type !== _item.type) {
        throw new Error(
          `enum data not match: expected: ${JSON.stringify(
            _item
          )}, actual: ${JSON.stringify(item)}`
        );
      }
    }
    if (JSON.stringify(poolEum) !== JSON.stringify(nextPoolEnum)) {
      throw new Error("fail to overide poolEum");
    }
    for (let i = 0; i < nextPoolEnum.length; i++) {
      const item = poolEum[i];
      const _item = __originLegacyPoolEnum.find((it) => it.id === item.id);
      if (_item && item.type !== _item.type) {
        throw new Error(
          `enum data not match: expected: ${JSON.stringify(
            _item
          )}, actual: ${JSON.stringify(item)}`
        );
      }
    }
    debug('poolEum replaced', { prev: __originLegacyPoolEnum, current: poolEum })
    debug('storeEum replaced', { prev: __originLegacyStoreEnum, current: storeEum })
  } catch (e) {
    console.error(e);
    onError(e);
  }
}


export function changeEumToObject(params){
  let obj = {};
  if (params && params.length > 0) {
    params.map((v) => {
      obj[v.value] = v.label;
    })
  }
  return obj;
}

export function arrRemoveJson(arr, attr, value) {
  if (!arr || arr.length == 0) {
    return ""
  }
  let newArr = arr.filter(function (item, index) {
    if(item && item[attr]) {
      return item[attr] != value
    }
  })
  return newArr
}

export const itemPicQualityScoreSet = [
  {key: '1', field: 'psoriasisLabel', group: ["0", "1", "2"], value: []},
  {key: '2', field: 'transparentLabel', group: ["1"], value: []},
  {key: '3', field: 'wbLabel', group: ["1"], value: []},
  {key: '4', field: 'mainRatioLabel', group: ["0", "1"], value: []},
]

export function dealQualityScoreToArray(data) {
  let result = [];
  for (const o in data) {
    const fieldMap = itemPicQualityScoreSet.map(v => v.field);
    const curObj = itemPicQualityScoreSet.filter((v) => v.field == o);
    if (fieldMap.includes(o) && data[o].length == curObj[0].group.length) result.push({value: curObj[0].key, level: 1});
    if (data[o].length > 0 && data[o].length != curObj[0].group.length) {
      data[o].map((v) => {
        result.push({
          value: `${curObj[0].key}0${v}`,
          level: 2
        })
      })
    }
  }
  return result;
}

export function dealQualityScoreToObject(data) {
  let result = {};
  if (data && isArray(data) && data.length > 0 ) {
    data.map((v) => {
      if (v.value < 10) {
        let obj = itemPicQualityScoreSet.filter((o) => o.key == v.value)[0];
        result[obj.field] = obj.group;
      } else {
        let p = parseInt(v.value / 100).toString();
        let t = parseInt(v.value % 10).toString();
        let obj = itemPicQualityScoreSet.filter((o) => o.key == p)[0];
        if (typeof result[obj.field] == 'undefined') {
          result[obj.field] = [];
        }
        result[obj.field].push(t);
      }
    })
  }else{
    result = data;
  }
  return result;
}

export function getTreeName(list, v){
  if (list && list.length >= 0) {
    for (let i = 0; i < list.length; i++) {
      let a = list[i]
      if (a.value === v) {
        return a.label
      } else {
        if (a.children && a.children.length > 0) {
          let res = getTreeName(a.children, v)
          if (res) {
            return res
          }
        }
      }
    }
  }
}
export function unique(arr){
  let res = [];
  let obj = {};
  for(let i=0; i<arr.length; i++){
    if( !obj[arr[i]] ){
      obj[arr[i]] = 1;
      res.push(arr[i]);
    }
  }
  return res;
}

export function deepCopy(obj) {
  let result = Array.isArray(obj) ? [] : {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === 'object' && obj[key]!==null) {
        result[key] = deepCopy(obj[key]);
      } else {
        result[key] = obj[key];
      }
    }
  }
  return result;
}

/**
 * 校验权限
 **/
export const getPermission = async (id) => {
  try {
    let resp = await api.permission.checkPool(id);   //todo 换掉接口
    let {rediectUrl} = resp.data.data;
    return rediectUrl;
  } catch (error) {
    onRequestError(error)
  }
}

/**
 * 校验专家池权限
 **/
 export const getExpertPermission = async (params) => {
  try {
    let resp = await api.permission.checkExpertPool(params);   //todo 换掉接口
    let {rediectUrl} = resp.data.data;
    return rediectUrl;
  } catch (error) {
    onRequestError(error)
  }
}

/**
 * 元转分
 * */
export const yuanToFen = (amount) =>{
  return Math.round(Number(amount)*100);
}

/**
 * 分转元
 * */
export const fenToYuan = (amount) =>{
  return (Number(amount)/100).toFixed(2);
}

/**
 * 暂时的方法将金额元转分
 * */
export const changeToYuan = (item) =>{
  let value = JSON.parse(item.filterFieldValue);
  let keyGroup = ['platform_freight_manjian_condition','platform_freight_manjian_discount'];
  if (item.unitNode && item.unitNode == 'fen'  || (keyGroup.includes(item.filterFieldKey))){  // 当金额单位为fen的，转换规则; 目前暂时以key为标志，因为后端还没存金额单位
    value.start = value.start ? fenToYuan(value.start) : '';
    value.end = value.end ? fenToYuan(value.end) : '';
  }
  return value;
}

/**
 * 暂时的方法将金额分转元
 * */
export const changeToFen = (value,item) =>{
  let newPostValue = JSON.parse(JSON.stringify(value));
  let keyGroup = ['platform_freight_manjian_condition','platform_freight_manjian_discount'];
  if ((item.unitNode && item.unitNode == 'fen') || (keyGroup.includes(item.filterFieldKey))) {  // 当金额单位为fen的，转换规则; 目前暂时以key为标志，因为后端还没存金额单位
    newPostValue.start = yuanToFen(newPostValue.start);
    newPostValue.end = yuanToFen(newPostValue.end);
  }
  return newPostValue;
}

// 请求未知数据
export const fetchGroupOption = (key, url) => {
  return new Promise(async (resolve, reject) => {
    let resp = await api.getDynamicDataSource(url);
    resolve({ [key]: resp.data.data });
  });
};
