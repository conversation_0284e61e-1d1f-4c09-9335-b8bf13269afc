export const optionMap = {
  "item_ac_serviceTag": [
    // {
    //   label: '健康门店',
    //   value: 'HEALTHY_S_TAG'
    // },
    {
      label: '新店',
      value: 'NEW_SHOP_S_TAG'
    },
    {
      label: '品牌',
      value: 'BRAND_S_TAG'
    },
    {
      label: '预定',
      value: 'BOOK_S_TAG'
    },
    {
      label: '无忧退',
      value: 'REFUND_S_TAG'
    },
    {
      label: '开发票',
      value: 'RECEIPT_S_TAG'
    },
    {
      label: '坏品包退',
      value: 'BADPROD_S_TAG'
    },
    {
      label: '极速退款',
      value: 'QREFUND_S_TAG'
    },
    {
      label: '融化必赔',
      value: 'MELTMP_S_TAG'
    },
    // {
    //   label: '慢必赔',
    //   value: 'SLOW_S_TAG'
    // },
    {
      label: '省心加工',
      value: 'FREEHAND_S_TAG'
    },
    {
      label: '冬季保温',
      value: 'WARM_S_TAG'
    },
  ],
  "item_ac_PZ": [
    {
      label: '吃货联盟',
      value: 'PDCHLM'
    },
    {
      label: '果蔬商超吃货联盟',
      value: 'PDGSCHLM'
    },
    {
      label: '业态红包-超市',
      value: 'YTHBCS'
    },
    {
      label: '业态红包-便利店',
      value: 'YTHBBLD'
    },
    {
      label: '业态红包-水果',
      value: 'YTHBSG'
    },
    {
      label: '业态红包-生鲜',
      value: 'YTHBSX'
    },
    {
      label: '业态红包-零食',
      value: 'YTHBLS'
    },
    {
      label: '业态红包-酒水',
      value: 'YTHBJS'
    },
    {
      label: '业态红包-美妆个护',
      value: 'YTHBMZGH'
    },
    {
      label: '业态红包-日用百货',
      value: 'YTHBRYBH'
    },
    {
      label: '业态红包-母婴',
      value: 'YTHBMY'
    },
    {
      label: '业态红包-宠物',
      value: 'YTHBCW'
    },
    {
      label: '业态红包-鲜花绿植',
      value: 'YTHBXHLZ'
    },
    {
      label: '业态红包-超市便利',
      value: 'YTHBCHBL'
    },
    {
      label: '业态红包-水果生鲜',
      value: 'YTHBSGSX'
    },
    {
      label: '业态红包-零食酒水',
      value: 'YTHBLSJS'
    },
    {
      label: '业态红包-美妆百货',
      value: 'YTHBMZBH'
    }
  ],
  "item_ac_CAT": [{label: '全部', value: ''}, {label: '正确', value: 'Y_CAT'}],
  "item_distribute": [{label: '畅销品', value: 1}, {label: '滞销品', value: 2}, {label: '钩子', value: 3}, {
    label: '其他',
    value: 4
  }],
  "item_pic_standard": [
    {
      value: '1', label: '含牛皮癣图片', children:
        [
          {value: '100', label: '高质量图片（含牛皮癣低）', children: null},
          {value: '101', label: '中等质量图片(含牛皮癣中)', children: null},
          {value: '102', label: '低质量图片（含牛皮癣高）', children: null},
        ]
    },
    {
      value: '2', label: '透明底图', children:
        [
          {value: '201', label: '是(透明底图)', children: null},
        ]
    },
    {
      value: '3', label: '白底图', children:
        [
          {value: '301', label: '是(白底图)', children: null},
        ]
    },
    {
      value: '4', label: '主体面积占比', children:
        [
          {value: '400', label: '低质量图片(主体面积占比)', children: null},
          {value: '401', label: '高质量图片(主体面积占比)', children: null},
        ]
    }
  ],
  "item_status":[{value: "YOU_XIAO", label: "上架", children: null}, {value: "WU_XIAO", label: "下架", children: null}],
  "item_user_scope":[{ label : "商家会员" , value : 8}],
  "item_risk_comp":[{ label: '普通等级', value: '1' }, { label: '严格等级', value: '2' }],
  "c5_item_layer":[{value:"Y_C5_AA",label:"大型超市 A+商品",level:1}],
  "store_zps":[{label: '否', value: '0'}, {label: '是', value: '1'}],
  "store_city_agent": [{ label: '否', value: '0' }, { label: '是', value: '1' }],
  "store_direct": [{ label: '否', value: '0' }, { label: '是', value: '1' }],
  "item_is_open_commission_fee": [{ label: '否', value: '0' }, { label: '是', value: '1' }],
  "item_store_shop_star": [{ label: '是', value: 'C5_SHOP_STAR' }],
  "store_shop_star": [{ label: '是', value: 'C5_SHOP_STAR' }],
  "item_shop_e_mall": [{ label: '是', value: '2214674071299' }],
  "item_e_mall": [{ label: '是', value: '2214674071299' }],
  "store_shop_e_mall": [{ label: '是', value: '2214674071299' }],
  "item_shop_e_mall3": [{ label: '不限', value: '' },{ label: '是', value: 'EMAlL3' }],
  "store_shop_e_mall3": [{ label: '不限', value: '' },{ label: '是', value: 'EMAlL3' }],
  "item_city_top500_sold": [{ label: '是', value: 'TOP500_SOLD_ITM_CNT' }],
  "store_level": [{label: '未入门', value: 0}, {label: '入门', value: 100}, {label: '健康', value: 200}, {label: '完美', value: 300}, {label: '超级', value: 500}],
  "item_store_layer":[{label: 'KA', value: "KAA"},{label: 'SMB', value: 'SMB'}],
  "store_layer":[{label: 'KA', value: "KAA"},{label: 'SMB', value: 'SMB'}],
  "shop_activity_types":[
    {label: '商品特价', value: '商品特价'},
    {label: '商品折扣', value: '商品折扣'},
    {label: '商品直降', value: '商品直降'},
    {label: '商品立减', value: '商品立减'},
    {label: '品类满减', value: '商品满减券'},
    {label: '品牌联合满减券', value: '品牌联合满减券'},
    {label: '全店满减', value: '全店满减'},
    {label: '运费满减', value: '运费满减'},
    {label: '商品运费券', value: '商品运费券'},
    {label: '店铺红包(店内普通)', value: '店铺红包(店内普通)'},
    {label: '店铺红包(店内新客)', value: '店铺红包(店内新客)'},
    {label: '店铺红包(店外渠道)', value: '店铺红包(店外渠道)'},
    {label: '店铺红包(分享)', value: '店铺红包(吃货豆)'},
  ],
  "store_general_level_name":[
    {
      label: 'L1',
      value: 'L1'
    },
    {
      label: 'L2',
      value: 'L2'
    },
    {
      label: 'L3',
      value: 'L3'
    },
    {
      label: 'L4',
      value: 'L4'
    },
    {
      label: 'L5',
      value: 'L5'
    }
  ],
  "item_shop_general_level_last_month_name":[
    {
      label: 'L1',
      value: 'L1'
    },
    {
      label: 'L2',
      value: 'L2'
    },
    {
      label: 'L3',
      value: 'L3'
    },
    {
      label: 'L4',
      value: 'L4'
    },
    {
      label: 'L5',
      value: 'L5'
    }
  ],
  "algorithm_season_tags":[
    {label: '一月', value: 'January'},
    {label: '二月', value: 'February'},
    {label: '三月', value: 'March'},
    {label: '四月', value: 'April'},
    {label: '五月', value: 'May'},
    {label: '六月', value: 'June'},
    {label: '七月', value: 'July'},
    {label: '八月', value: 'August'},
    {label: '九月', value: 'September'},
    {label: '十月', value: 'October'},
    {label: '十一月', value: 'November'},
    {label: '十二月', value: 'December'},
  ],
  "marketItem_activity_price_less_than_min_price_7d": [{label: '是', value: '1'}, {label: '不限制', value: '0'}],
  "store_business_region": [
    {label:'华东战区',value:'华东战区'},
    {label:'华南战区',value:'华南战区'},
    {label:'华北战区',value:'华北战区'},
    {label:'中西战区',value:'中西战区'}],
  "store_new_bu_flag":[
    {label:'快消业务部',value:'快消业务部'},
    {label:'泛零售业务部',value:'泛零售业务部'},
    {label:'散店业务部',value:'散店业务部'},
    {label:'水果业务部',value:'水果业务部'},
    {label:'大健康业务部',value:'大健康业务部'},
    {label:'生鲜菜场业务部',value:'生鲜菜场业务部'},
    {label:'其他餐饮',value:'其他餐饮'},
  ],
  'item_ac_goodsType':[{label: '预售品', value: 'Y_PreSale'}, {label: '现货品', value: 'Y_OnSale'}, {label: '不限', value: null}],
  "item_ac_serviceFeature":[
    {
      label: '全国KA',
      value: 'SF_9'
    },
    {
      label: '城市龙头',
      value: 'SF_10'
    },
    {
      label: '其他连锁',
      value: 'SF_11'
    },
    {
      label: '散店',
      value: 'SF_12'
    }
  ],
  "item_ac_servicePackage":[ //todo 枚举需要换
    {
      label: '新店',
      value: 'NEW_SHOP_S_TAG'
    },
    {
      label: '品牌',
      value: 'BRAND_S_TAG'
    },
    {
      label: '预定',
      value: 'BOOK_S_TAG'
    },
    {
      label: '无忧退',
      value: 'REFUND_S_TAG'
    },
    {
      label: '开发票',
      value: 'RECEIPT_S_TAG'
    },
    {
      label: '坏品包退',
      value: 'BADPROD_S_TAG'
    },
    {
      label: '极速退款',
      value: 'QREFUND_S_TAG'
    },
    {
      label: '融化必赔',
      value: 'MELTMP_S_TAG'
    },
  ],
  "timePeriod":[
    {label: '早间', value: 'morning'},
    {label: '中午', value: 'noon'},
    {label: '下午', value: 'afternoon'},
    {label: '晚间', value: 'evening'},
    {label: '上半夜', value: 'midnight'},
    {label: '下半夜', value: 'night'}
  ],
  'crowd':[
    {label: '小城中老年', value: 'middleAge'},
    {label: '小城青年', value: 'young'},
    {label: '年轻妈妈', value: 'youngMother'},
    {label: '都市银发', value: 'theOld'},
    {label: '资深中产', value: 'middleClass'},
    {label: '都市95后', value: 'post95'},
    {label: '都市白领', value: 'whiteCollar'},
    {label: '都市蓝领', value: 'blueCollar'},
    {label: '未知', value: 'unknown'},
  ],
  // 门店
  "store_war_zone": [
    {label:'华东战区',value:'华东战区'},
    {label:'华南战区',value:'华南战区'},
    {label:'华北战区',value:'华北战区'},
    {label:'中西战区',value:'中西战区'}],
  "store_along_attribute_collection": [
    {
      label: '新店',
      value: 'NEW_SHOP_S_TAG'
    },
    {
      label: '品牌',
      value: 'BRAND_S_TAG'
    },
    {
      label: '预定',
      value: 'BOOK_S_TAG'
    },
    {
      label: '无忧退',
      value: 'REFUND_S_TAG'
    },
    {
      label: '开发票',
      value: 'RECEIPT_S_TAG'
    },
    {
      label: '坏品包退',
      value: 'BADPROD_S_TAG'
    },
    {
      label: '极速退款',
      value: 'QREFUND_S_TAG'
    },
    {
      label: '融化必赔',
      value: 'MELTMP_S_TAG'
    },
    {
      label: '冬季保温',
      value: 'WARM_S_TAG'
    },
    // {
    //   label: '慢必赔',
    //   value: 'SLOW_S_TAG'
    // }
  ],
  "store_is_city_agent": [{ label: '否', value: '0' }, { label: '是', value: '1' }],
  "store_is_zps_shop":[{label: '否', value: '0'}, {label: '是', value: '1'}],
  "store_shop_level": [{label: '未入门', value: 0}, {label: '入门', value: 100}, {label: '健康', value: 200}, {label: '完美', value: 300}, {label: '超级', value: 500}],
  "store_shop_general_level_name":[
    {
      label: 'L1',
      value: 'L1'
    },
    {
      label: 'L2',
      value: 'L2'
    },
    {
      label: 'L3',
      value: 'L3'
    },
    {
      label: 'L4',
      value: 'L4'
    },
    {
      label: 'L5',
      value: 'L5'
    }
  ],
  "store_shop_general_level_last_month_name":[
    {
      label: 'L1',
      value: 'L1'
    },
    {
      label: 'L2',
      value: 'L2'
    },
    {
      label: 'L3',
      value: 'L3'
    },
    {
      label: 'L4',
      value: 'L4'
    },
    {
      label: 'L5',
      value: 'L5'
    }
  ],
  'store_self_fetch':[
    {
      label: '外卖店',
      value: '0'
    },
    {
      label: '自提店',
      value: '1'
    },
    {
      label: '外卖自提店',
      value: '2'
    }
  ],
  'store_whole_city_delivery': [{ label: '否', value: '0' }, { label: '是', value: '1' }],
  'store_is_risk_comp':[{ label: '普通等级', value: '1' }, { label: '严格等级', value: '2' }],
  'store_is_newuser_activity': [{ label: '否', value: '0' }, { label: '是', value: '1' }],
  'store_member':[{ label: '是', value: 'OPEN_MEMBER' }],
  'store_activity_type_names_1d':[
    {
      label: '全店满减',
      value: '全店满减'
    },
    {
      label: '运费满减',
      value: '运费满减'
    },
  ],
  'store_activity_child_type':[
    {
      label: '全店满减',
      value: '3_1'
    },
    {
      label: '运费满减',
      value: '5_1'
    },
  ],
  "store_is_open_commission_fee": [{ label: '否', value: '0' }, { label: '是', value: '1' }],
  "store_service_feature":[
    {
      label: '全国KA',
      value: 'SF_9'
    },
    {
      label: '城市龙头',
      value: 'SF_10'
    },
    {
      label: '其他连锁',
      value: 'SF_11'
    },
    {
      label: '散店',
      value: 'SF_12'
    }
  ],
  "item_scene_id_subdivide_scene":[
    // {label:'火锅',value:'HOTPOT'},
    // {label:'送礼',value:'GIFT'},
    {label:'下午茶',value:'AFTERNOONTEA'},
    {label:'夜生活',value:'NIGHTLIFE'},
  ],
  "item_solar_term":[
    {label:'冬至',value:'冬至'},
    {label:'处暑',value:'处暑'},
    {label:'夏至',value:'夏至'},
    {label:'大寒',value:'大寒'},
    {label:'大暑',value:'大暑'},
    {label:'大雪',value:'大雪'},
    {label:'寒露',value:'寒露'},
    {label:'小寒',value:'小寒'},
    {label:'小暑',value:'小暑'},
    {label:'小满',value:'小满'},
    {label:'小雪',value:'小雪'},
    {label:'惊蛰',value:'惊蛰'},
    {label:'春分',value:'春分'},
    {label:'清明',value:'清明'},
    {label:'白露',value:'白露'},
    {label:'秋分',value:'秋分'},
    {label:'立冬',value:'立冬'},
    {label:'立夏',value:'立夏'},
    {label:'立春',value:'立春'},
    {label:'立秋',value:'立秋'},
    {label:'芒种',value:'芒种'},
    {label:'谷雨',value:'谷雨'},
    {label:'雨水',value:'雨水'},
    {label:'霜降',value:'霜降'}
  ],
  "item_ac_item_service_tag": [
    {
      label: '融化必赔',
      value: 'ITEM_MELTMP_S_TAG'
    },
    {
      label: '冰镇',
      value: 'ICED_S_TAG'
    },
    {
      label: '冬季保温',
      value: 'item_WARM_S_TAG'
    },
  ],
  "item_store_shop_skd": [{ label: '不限', value: "" }, { label: '是', value: 'S_KD1' }, { label: '否', value: 'S_KD0' }],
  "item_store_b_sonpre": [{ label: '不限', value: "" }, { label: '是', value: 'B_SONPRE' }],
  "store_b_sonpre": [{ label: '不限', value: "" }, { label: '是', value: 'B_SONPRE' }],
  "activity_child_type_with_condition": [
    { value: "7_1", label: "买N赠M" },
    { value: "8_1", label: "第N件特价/折扣" },
    { value: "11_2", label: "X件Y折" },
  ],
}
