.right-part.no-announcement{
  transition: all 0.3s;
  &.no-sider{
    width: 100vw;
    max-width: 100vw;
  }
}
.manage-set-schema {
  padding: 12px;
  width: calc(100vw - 200px);
  height: calc(100vh - 60px);
  transition: all 0.3s;
  &.no-sider{
    width: 100vw;
  }
  .schema-content{
    height: 100%;
    .schema-left-list-title{
      text-align: center;
      margin-bottom: 10px;
      font-weight: 600;
      font-size: 20px
    }
    .schema-left-list{
      height: 100%;
      overflow-y: scroll;
      position: relative;
      &::-webkit-scrollbar{
        display: none;
      }
      &.schema-left-list-true{
        margin-left: 10px;
      }

      .schema-left-list-icon{
        height: 22px;
        width: 22px;
        cursor: pointer;
        position: absolute;
        left: 0;
        top: 2px;
        &.schema-left-list-icon-true{
          transform: rotate(180deg);
        }
        img{
          width: 100%;
          height: 100%;
        }
      }

      .schema-left-list-component{

      }
    }
    .schema-left{
      position: relative;
      width: 100%;
      height: 100%;
      overflow-x: hidden;
      &::-webkit-scrollbar{
        display: none;
      }
      .schema-position{
        width: 120%;
        left: 0;
        top: -30px;
        position: absolute !important;
        transform: translateX(-10%);
        .next-col-4{
          width: 0 !important;
          max-width: 0 !important;
        }
        .next-col-15{
          width: 83.333% !important;
          max-width: 83.333% !important;
        }
        .legend{
          display: none;
        }
      }
    }
    .schema-right{
      padding: 0 !important;
      height: 100%;
      overflow-y: scroll;
      &::-webkit-scrollbar{
        display: none;
      }.schema-list-content {
        margin-bottom: 40px;
      }
      .button-set-schema-content{
        display: flex;
        width: calc((100% - 200px) / 3);
        background-color: #fff;
        flex-direction: row;     
        word-wrap: break-word;   
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 10px;
        margin-top: 10px;
        position: fixed;
        bottom: 0;
        right: 0;
        box-shadow: 0 -10px 10px -10px rgba(0,0,0,0.1);
        z-index: 1;
      }
      .button-set-schema{
        color: white !important;
        background-color: rgb(76, 129, 242) !important;
        border:none !important;
        border-radius: 5px !important;
        display: inline-block !important;
      }
      .schema-left-list-title{
        margin-bottom: 0;
      }
    }
  }

}

.vanilla-jsoneditor-react{
    height: 100%;
    position: relative;
    .button-jsoneditor{
      position: absolute;
      bottom: 100px;
      right: 50px;
      z-index: 1000;
      color: white !important;
      background-color: rgb(76, 129, 242) !important;
      border:none !important;
      border-radius: 5px !important;
    }
}

.component-item{
  display: flex;
  flex-direction: row;
  background-color: white;
  height: 100px;
  width: 95%;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 10px;
  position: relative;
  .component-item-image{
    width: 80px;
    height: 80px;
    margin: 10px;
    border-radius: 10px;
    position: relative;
    display: inline-block;
    img{
      width: 80px;
      height: 80px;
      border-radius: 10px;
    }
    &::before{
      content: ' ';
      display: none;
      width: 80px;
      height: 80px;
      border-radius: 10px;
      background-color: black;
      opacity: 0.5;
      position: absolute;
      z-index: 111;
      transition: all 1s;
    }
    &::after{
      content: ' ';
      display: none;
      position: absolute;
      z-index: 112;
      left: 30px;
      top: 30px;
      width: 20px;
      height: 20px;
      background-image: url('https://img.alicdn.com/imgextra/i3/O1CN01510kis1wfwLdl0gHA_!!6000000006336-2-tps-128-128.png');
      background-size: 100%;
      background-repeat: no-repeat;
      transition: all 1s;
    }
    &:hover{
      cursor: pointer;
      &::before{
        display: inline-block;
      }
      &::after{
        display: inline-block;
      }
    }
  }
  .component-item-context{
    margin: 10px 0px 10px 0;
    position: relative;
    padding-right: 10px;
    .component-item-name{
      font-size: 16px;
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
    }
    .component-item-desc{
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      list-style: none;
      font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';
    }

  }
  .component-item-button{
    display:inline-block;
    width: 50px;
    color: #FF7000;
    padding: 3px 0;
    text-align: center;
    font-weight: 600;
    border-radius: 4px;
    border: 1px solid #FF7000;
    position: absolute;
    bottom: 10px;
    right: 10px;
    &:hover{
      cursor: pointer;
      color: white;
      background-color: #FF7000;
    }
  }
}

.edit-component-item{
  display: flex;
  width: 100%;
  padding: 10px 10px  0;
  position: relative;
  .edit-component-content{
    background-color: #fff;
    width: 100%;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    >div{
      display: flex;
      flex-direction: row;
      width: 100%;
    }
    .edit-component-content-child{
      display: flex;
      flex-direction: column;
      width: 100%;
      .multiple-tab{
        margin: 0;
        padding: 10px 10px 0;
        >span{
          font-weight: 600;
        }
        .selectText{
          color: #ff6f00;
        }
      } 
    }
    .edit-component-content-title{
      color: rgba(255,111,0,1);
      font-size: 12px;
      font-weight: 600;
      list-style: none;
      border: 1px solid rgba(255,111,0,1);
      display: inline;
      padding: 2px 4px;
      border-radius: 5px;
      position: relative;
      top: -5px;
      left: -5px;
    }
    .edit-component-content-add{
      font-size: 12px;
      font-weight: 600;
      list-style: none;
      color: rgba(255,111,0,1);
      border: 1px solid rgba(255,111,0,1);
      padding: 2px 4px;
      border-radius: 5px;
      position: relative;
      display: inline-block;
      margin: 5px 10px;
      width: 96%;
      text-align: center;
      cursor: pointer;
      &:hover{
        color: white;
        background-color: rgba(255,111,0,1);
      }
    }
    .edit-component-content-lable{
      font-size: 16px;
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      min-width: 140px;
      text-align: right;
    }
    .edit-component-content-value{
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      font-weight: 600;
      list-style: none;
      font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';
    }
    .edit-component-child{
      flex-direction: column;
      >div{
        background-color: #fff;
        width: 100%;
        flex-direction: row;
        .edit-component-content-lable{
          display: inline-block;
        }
        .edit-component-content-button-value{
          display: inline-block;
          color: rgba(255, 111, 0,0.85);
          font-size: 14px;
          font-weight: 600;
          list-style: none;
          cursor: pointer;
          &:hover{
            color: rgba(255, 111, 0,1);
          }
        }
      }
      .edit-component-content-child{
        .edit-component-item{
          padding: 0;
          .edit-component-content{
            padding: 0px;
            border: none;
            padding-left: 30px;
            &::after{
              content: '子模块';
              display: flex;
              width: 30px;
              height: 100%;
              position: absolute;
              left: 0;
              top: 0;
              font-size: 10px;
              text-align: center;
              justify-content: center;
              flex-direction: column;
              border: 1px solid rgba(255, 111, 0,1);
              border-top-left-radius: 8px;
              border-bottom-left-radius: 8px;
              color: rgba(255, 111, 0,1);
              font-weight: 600;
              writing-mode:vertical-rl;
            }
          }
        }
      }
    }
    
    .edit-component-content-table{
      display: inline-block;
      .edit-component-content-table-li{
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      font-weight: 600;
      list-style: none;
      font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';
      }
    }
    .edit-component-button{
      display: inline-block;
      position: absolute;
      height: 100%;
      width: 10%;
      min-width: 50px;
      right: 0;
      top: 0;
      .button-edit-schema{
        height: 50%;
        width: 100%;
        text-align: center;
        align-items: center;
        padding: 0;
        border-radius: 0;
        border: none;
        &:first-child{
          border-left: 1px solid #f0f0f0;
          border-bottom: 1px solid #f0f0f0;
          &:hover{
            border: 1px solid #FF7000;
            border-top-right-radius: 8px;
          }
        }
        &:last-child{
          border-left: 1px solid #f0f0f0;
          &:hover{
            border: 1px solid #FF7000;
            border-bottom-right-radius: 8px;
          }
        }
      }
    }
  }
  .action-icon-list{
    position: absolute;
    display: flex;
    flex-direction: column;
    padding: 10px;
    z-index: 1;
    .action-icon{
      cursor: pointer;
      text-align: center;
      &:hover {
        color: #ff7000;
      }
    }
  }
  .edit-component-custom{
    width: 100%;
  }
}

.schema-edit-dialog{
  width: 40%;
  top: 100px !important;
  height: 80%;
  transition:all 0.01s;
  .next-form-item-label{
    min-width: 120px !important;
  }
  
  .next-dialog-body{
    height: calc(100% - 80px);
    overflow-y: scroll;
  } 
  .next-number-picker-normal.next-medium{
    width: 120px;
  }
}

.image-view-dialog{
  width: 100%;
  height: 100%;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  max-width: 100% !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: none !important;

  .next-dialog-body{
    width: 100%;
    height: 100%;
    max-height: 100% !important;
    position: relative;
    display: flex;
  }
  .image-view-dialog-image{
    display: inline-block;
    margin: auto;
    vertical-align: middle;
    transform: scale3d(1, 1, 1) ;
    cursor: grab;
    transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
    user-select: none;
    pointer-events: auto;
    transform-origin: center !important;
  }
  .next-dialog-close{
    background-color: rgba(0, 0, 0, 0.1);;
    padding: 25px;
    border-radius: 50%;
    color: #FFF;
    font-weight: 600;
    transition: all 0.3s;
    &:hover{
      background-color: rgba(0, 0, 0, 0.2);
      color: #FFF;
    }
  }
  .image-view-dialog-button-list{
    display: flex;
    align-items: center;
    padding: 0 24px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 100px;
    display: inline-block;
    position: fixed;
    left: 50%;
    bottom: 10%;
    transform: translate(-50%);
    .image-view-dialog-button-list-item{
      width: 50px;
      height: 50px;
      padding: 15px;
      display: inline-block;
      cursor: pointer;
      transition: all 0.3s;
      &:hover{
        color: #f0f0f0;
      }
      svg{
        width: 20px;
        height: 20px;
        
      }
    }
  }
}

.edit-component-popup{
  width: 400px;
}