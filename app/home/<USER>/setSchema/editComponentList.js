import React, { useRef, useEffect, useState } from "react";
import EditComponentItem from "./editComponentItem";
import Dependencies from "./dependencies";

export default function EditComponentList(props) {
  const { componentKey = "", schemaDetail = {}, schema = {}, field } = props;
  // detail做单独处理
  if (componentKey == "detail") {
    return (
      <>
        {schemaDetail.properties &&
          Object.keys(schemaDetail.properties).map((key, index) => {
            return (
              <EditComponentItem
                fatherComponentKey={componentKey}
                componentKey={key}
                schemaDetail={schemaDetail.properties[key]}
                required={schemaDetail.required}
                schema={schema}
                field={field}
                detailPropertiesLength={
                  Object.keys(schemaDetail.properties).length
                }
                index={index}
              ></EditComponentItem>
            );
          })}
        {schemaDetail.dependencies &&
          Object.keys(schemaDetail.dependencies).map((key, index) => {
            return (
              <Dependencies
                fatherComponentKey={componentKey}
                componentKey={key}
                schemaDetail={schemaDetail.dependencies[key]}
                schema={schema}
              ></Dependencies>
            );
          })}
      </>
    );
  } else {
    // 双组成行只保留单独，无用字段不展示
    if (
      ["showSupChannel", "outRequired", "showSingleOversupply"].includes(
        componentKey
      )
    )
      return null;
    return (
      <EditComponentItem
        hierarchy={1}
        componentKey={componentKey}
        schemaDetail={schemaDetail}
        schema={schema}
        field={field}
        isCustom={true}
      ></EditComponentItem>
    );
  }
}
