import { putInReq } from '@/utils/request';
import { request } from '@/adator/request';
import { onRequestSuccess }  from '@/utils/api'
import axios from 'axios'

/**设置schema**/
export function setSchema(params) {
  return putInReq.post('/api/deliverymanage/setSchema',params).then(onRequestSuccess)
}

/**获取schema**/
export function getScheduleTemplate(params) {
  return putInReq.post('/api/deliverymanage/getScheduleTemplate',params).then(onRequestSuccess)
}

/**设置元数据schema**/
export function setMetadataSchema(params) {
  return putInReq.post('/api/deliverymanage/setMetadataSchema',params).then(onRequestSuccess)
}

/**获取非资源位schema**/
export function getNormalTemplateSchema(params) {
  return putInReq.post('/api/deliverymanage/getTemplateSchema',params).then(onRequestSuccess)
}

/**设置非资源位schema**/
export function setNormalSchema(params) {
  return putInReq.post('/api/deliverymanage/saveTemplateSchema',params).then(onRequestSuccess)
}

/** schema 设置成功后发送钉钉消息, Schema 发布通知群 **/
export function rootSendMsg(params) {
  return request().post('/api/dingding/markdown',params).then(onRequestSuccess)
}
