import { deepCopy } from "@/home/<USER>/common";

export function isJSON(str) {
  if (typeof str != "string") {
    // 1、传入值必须是 字符串
    console.log("It is not a string! [" + str + "]");
    return false;
  }
  try {
    var obj = JSON.parse(str); // 2、仅仅通过 JSON.parse(str)，不能完全检验一个字符串是JSON格式的字符串
    if (typeof obj == "object" && obj) {
      //3、还必须是 object 类型
      console.log("转换成功：" + str);
      return true;
    } else {
      console.log("转换失败：" + str);
      return false;
    }
  } catch (e) {
    console.log("error：[" + str + "] !!! " + e);
    return false;
  }
}

// 组件列表枚举
export const componentList = [
  {
    title: "开关组件",
    desc: "常用于布尔值的传递，返回值为true或false",
    type: "boolean",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i4/O1CN01BsDmjw1ovxGvloBbn_!!6000000005288-0-tps-400-400.jpg",
    demoSchema: {
      title: "开关组件",
      description: "非特殊情况请勿关闭",
      type: "boolean",
      default: true,
    },
  },
  {
    title: "图片组件",
    desc: "用于图片上传，可自定义图片的尺寸、格式、体积等",
    type: "image",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i3/O1CN01J8Wsm81E3c71AB57V_!!6000000000296-0-tps-800-796.jpg",
    demoSchema: {
      title: "图片组件:",
      type: "string",
      format: "uri",
      "x-ui-widget": "img-upload",
      "x-ui-validate": {
        width: 100,
        height: 100,
        minWidth: 100,
        maxWidth: 100,
        minHeight: 100,
        maxHeight: 100,
        minSize: 0,
        maxSize: 500,
        accept: "png,apng,jpg,jpeg,gif",
        placeholder: "",
      },
      "x-ui-hidden": false,
      "x-ui-disable": false,
    },
  },
  {
    title: "输入框组件",
    desc: "用于普通文本输入，可自定义字数、前后缀、格式等",
    type: "input",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01OL8Wu51NwQ2yFMdpQ_!!6000000001634-0-tps-846-526.jpg",
    demoSchema: {
      title: "输入框组件:",
      type: "string",
      "x-ui-addonTextBefore": "",
      "x-ui-addonTextAfter": "",
      "x-ui-placeholder": "这里是输入框底纹",
      minLength: 0,
      maxLength: 10,
      default: "",
      "x-ui-hidden": false,
      "x-ui-disable": false,
      pattern: "",
    },
  },
  {
    title: "多行文本输入组件",
    desc: "用于多行文本输入",
    type: "textarea",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01Ml4GxD1Y234j8hB5s_!!6000000003000-0-tps-750-542.jpg",
    demoSchema: {
      title: "多行文本输入组件:",
      type: "string",
      "x-ui-widget": "textarea",
      "x-ui-placeholder": "这里是多行文本输入底纹",
      minLength: 0,
      maxLength: 100,
      default: "",
      "x-ui-hidden": false,
      "x-ui-disable": false,
      pattern: "",
    },
  },
  {
    title: "数值组件",
    desc: "用于数字类型输入的组件",
    type: "number",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i4/O1CN01AyIdhg1fUAha6d7Rd_!!6000000004009-0-tps-394-394.jpg",
    demoSchema: {
      title: "数值组件:",
      type: "number",
      "x-ui-addonTextBefore": "",
      "x-ui-addonTextAfter": "",
      "x-ui-widget":"NumberWidget",
      minimum: 0,
      maximum: 10,
      multipleOf: 1,
      default: "",
      exclusiveMaximum: false,
      exclusiveMinimum: false,
      "x-ui-hidden": false,
      "x-ui-disable": false,
    },
  },
  {
    title: "颜色组件",
    desc: "用于输出16进制颜色",
    type: "color",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i3/O1CN01gTUIjF26BO5P1fvMs_!!6000000007623-0-tps-644-570.jpg",
    demoSchema: {
      title: "颜色组件:",
      type: "string",
      "x-ui-widget": "ColorWidget",
    },
  },
  {
    title: "单选组件(数值)",
    desc: "用于选择单个数值",
    type: "radio",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01l8SHOo1Td51GDhUF7_!!6000000002404-0-tps-674-382.jpg",
    demoSchema: {
      type: "number",
      title: "单选组件(数值):",
      enum: [0, 1],
      "x-ui-className": "selection-form",
      enumNames:["第一个值","第二个值"],
      default: 0,
      "x-ui-widget": "radio",
      "x-ui-hidden": false,
      "x-ui-disable": false,
    },
  },
  {
    title: "单选组件(字符串)",
    desc: "用于选择单个字符串值",
    type: "radio",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01wulSIV1Xmw09Ejmad_!!6000000002967-0-tps-668-354.jpg",
    demoSchema: {
      type: "string",
      title: "单选组件(字符串):",
      enum: ["0", "1"],
      "x-ui-className": "selection-form",
      enumNames:["第一个值","第二个值"],
      default: "0",
      "x-ui-widget": "radio",
      "x-ui-hidden": false,
      "x-ui-disable": false,
    },
  },
  {
    title: "多选组件(数值)",
    desc: "用于选择多个值",
    type: "checkbox",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i4/O1CN015w9VRx1zMeWB5QIpk_!!6000000006700-0-tps-866-602.jpg",
    demoSchema: {
      type: "array",
      title: "多选组件(数值):",
      "x-ui-className": "selection-form",
      "x-ui-widget": "CheckboxesWidget",
      items: {
        type: "number",
        enum: [0, 1, 3],
        enumNames: ["第一个值", "第二个值", "第三个值"],
      },
      uniqueItems: true,
      default: [0],
      "x-ui-hidden": false,
      "x-ui-disable": false,
    },
  },
  {
    title: "多选组件(字符串)",
    desc: "用于选择多个字符串值",
    type: "checkbox",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01govL9S1vA39ZyvxMW_!!6000000006131-0-tps-872-644.jpg",
    demoSchema: {
      type: "array",
      title: "多选组件(字符串):",
      "x-ui-className": "selection-form",
      "x-ui-widget": "CheckboxesWidget",
      items: {
        type: "string",
        enum: ["0", "1", "3"],
        enumNames: ["第一个值", "第二个值", "第三个值"],
      },
      uniqueItems: true,
      default: ["0"],
      "x-ui-hidden": false,
      "x-ui-disable": false,
    },
  },
  {
    title: "选择器组件",
    desc: "用于在列表中选择数值",
    type: "select",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i4/O1CN01xBhiiw249RATxReZI_!!6000000007348-0-tps-914-558.jpg",
    demoSchema: {
      title: "选择器组件:",
      type: "string",
      enum: ["1", "2"],
      "x-ui-className": "selection-form",
      enumNames: ["第一个值","第二个值"],
      default: "1",
      "x-ui-widget": "select",
      "x-ui-hidden": false,
      "x-ui-disable": false,
    },
  },
  {
    title: "日期选择组件",
    desc: "用于选择日期",
    type: "dataTime",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i3/O1CN01i985Mp1MTjcwlm78c_!!6000000001436-0-tps-808-796.jpg",
    demoSchema: {
      type: "number",
      title: "日期选择:",
      "x-ui-widget": "DateTimeWidget",
    },
  },
  {
    title: "渠道城市人群",
    desc: "用于选择投放渠道、投放城市、投放人群",
    type: "showPutIn",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01ywkJZu1qpf3v9n8L9_!!6000000005545-0-tps-1506-436.jpg",
    demoSchema: true,
  },
  {
    title: "投放AOI类型",
    desc: "用于选择投放AOI类型",
    type: "showAoi",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i3/O1CN010uNSNF1jBAjxVipX5_!!6000000004509-0-tps-1028-208.jpg",
    demoSchema: true,
  },
  {
    title: "关联算法策略",
    desc: "用于选择关联算法策略",
    type: "isCategoryCenter",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01xNZ29D1eXY0k3IZU2_!!6000000003881-0-tps-1088-218.jpg",
    demoSchema: true,
  },
  {
    title: "投放选品集",
    desc: "用于投放普通选品集：",
    type: "poolTypeConfig",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01oTfTMU1Sw1n0YQjbX_!!6000000002310-0-tps-1404-666.jpg",
    demoSchema: 1,
  },
  {
    title: "自定义ID集合",
    desc: "用于投放自定义ID集合，例如招商ID",
    type: "showMultilineIdText",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01vRPgG11W8ngWBmZCj_!!6000000002744-0-tps-952-370.jpg",
    demoSchema: [
      {
        "key": "weightPoolIdList",
        "name": "自定义ID",
        "placeholder": "自定义ID",
        "required": false,
        "maxLength": 5,
        "hidden": "timeSelection == 0"
      }
    ],
  },
  {
    title: "自定义供给来源",
    desc: "用于投放自定义供给来源",
    type: "selectionCollection",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i4/O1CN01rgYwBT1d79ZTJ6cnj_!!6000000003688-0-tps-1110-338.jpg",
    demoSchema: [
      {
        "selectionName": "商品榜",
        "selectionFields": "pureDataSourceList2",
        "maxCommodityLength": 2,
        "isNotRequired": false,
        "hidden": "timeSelection == 0"
      }
    ],
  },
  {
    title: "商家主营类目",
    desc: "用于选择商家主营类目",
    type: "showMainCategory",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i2/O1CN01eQYW351oFopzE1Bbo_!!6000000005196-0-tps-910-524.jpg",
    demoSchema: true,
  },
  {
    title: "关联上级配置",
    desc: "用于选择关联上级配置",
    type: "supChannel",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i1/O1CN01j8swv01x7Q78xDZk0_!!6000000006396-0-tps-950-186.jpg",
    demoSchema: true,
  },
  {
    title: "高级供给来源（含TAB）",
    desc: "用于选择关联上级配置",
    type: "oversupply",
    demoImageUrl:
      "https://img.alicdn.com/imgextra/i3/O1CN01AQuF3K1FlPbehlaPH_!!6000000000527-0-tps-1504-1068.jpg",
    demoSchema: {
      "maxTabLength": 6,
      "maxCommodityLength": 2, 
      "nameSetting": "tab设置（1-6）", 
      "name": "tab名称", 
      "field": "tabName", 
      "maxFieldLength": 4, 
      "isAllowShow": true, 
      "isRequired": false, 
      "isNotRequired": false, 
      "initTabLength": 2, 
      "hideTabName": false,
      "defaultSupplyType":1,
      "fieldType":"string",
      "defaultFieldValue": null,
      "fieldType": "string"
    },
  },
];

/**
 * 递归处理两个对象的属性
 * @param {Object} schemaComplete - 需要处理的目标对象
 * @param {Object} formDate - 提供属性值的对象
 * @returns {Object} - 处理后的目标对象
 */
export const processObjects = (schema, formDate) => {
  // 创建一个深拷贝的schema对象
  let schemaComplete = deepCopy(schema);

  // 递归合并两个对象的属性
  const mergeObjects = (obj1, obj2) => {
    for (let key in obj2) {
      // 如果属性值是对象且不为null或数组，并且obj1也有相同的属性名
      if (
        typeof obj2[key] === "object" &&
        !Array.isArray(obj2[key]) &&
        obj2[key] !== null &&
        obj1.hasOwnProperty(key)
      ) {
        // 递归处理子对象
        mergeObjects(obj1[key], obj2[key]);
      }
      // 如果obj2有相同的属性名，则将属性值赋值给obj1
      else {
        obj1[key] = obj2[key];
      }
    }
  };

  // 合并formDate的属性值到schemaComplete中
  mergeObjects(schemaComplete, formDate);

  // 递归删除空对象属性
  const removeEmptyObjects = (obj) => {
    for (let key in obj) {
      // 如果属性值是对象且不为
      if (typeof obj[key] === "object" && obj[key] !== null) {
        // 如果子对象属性为空，则删除该属性
        if (Object.keys(obj[key]).length === 0) {
          delete obj[key];
        }
        // 否则继续递归处理子对象
        else {
          removeEmptyObjects(obj[key]);
        }
      }
    }
  };

  // 删除schemaComplete中的空对象属性
  removeEmptyObjects(schemaComplete);

  // 返回合并后的schemaComplete对象
  return schemaComplete;
};

export const typeMap = {
  string: "字符串",
  object: "对象",
  array: "数组",
  number: "数字",
  boolean: "布尔",
  atom: "原子"
};
export const formatMap = {
  "date-time": "日期时间格式",
  date: "日期格式",
  time: "时间格式",
  uri: "URI 格式",
  color: "color	颜色选择器",
};

export const xUiWidgetMap = {
  textarea: "长文本输入",
  radio: "按钮组",
  "img-upload": "图片上传",
  select: "选择组件",
  NumberWidget: "数值按钮",
  "img-upload-new": "图片上传",
  "img-upload": "图片上传",
  dayOfWeek: "星期选择",
  DateTimeWidget: "时间选择",
  ColorWidget: "颜色选择",
  color: "颜色选择",
  CheckboxesWidget: "多选组件",
  checkboxes: "多选组件",
  activityAndBenefitWidget: "级联选择",
};
