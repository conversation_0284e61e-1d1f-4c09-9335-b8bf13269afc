import React, { useRef, useEffect, useState } from "react";

// 自定义schema
export default function GetCustomComponent(props) {
  const { componentKey, schemaDetail, schema } = props;
  const [result, setResult] = useState(null);

  // 直接枚举
  useEffect(() => {
    switch (componentKey) {
      case "showPutIn":
        setResult(
          <div>
            <div className="edit-component-content-lable">
              开启渠道城市人群：
            </div>
            <div className="edit-component-content-value">
              {schemaDetail ? "是" : "否"}
            </div>
          </div>
        );
        break;
      case "showAoi":
        setResult(
          <div>
            <div className="edit-component-content-lable">
              开启投放AOI类型：
            </div>
            <div className="edit-component-content-value">
              {schemaDetail ? "是" : "否"}
            </div>
          </div>
        );
        break;
      case "showCategory":
        setResult(
          <div>
            <div className="edit-component-content-lable">开启所属类目：</div>
            <div className="edit-component-content-value">
              {schemaDetail ? "是" : "否"}
            </div>
          </div>
        );
        break;
      case "isCategoryCenter":
        setResult(
          <div>
            <div className="edit-component-content-lable">
              开启关联算法策略：
            </div>
            <div className="edit-component-content-value">
              {schemaDetail ? "是" : "否"}
            </div>
          </div>
        );
        break;
      case "poolTypeConfig":
        setResult(
          <div>
            <div className="edit-component-content-lable">投放选品集：</div>
            <div className="edit-component-content-value">
              {schemaDetail == 1
                ? "商品"
                : schemaDetail == 2
                ? "门店"
                : "不合规"}
            </div>
          </div>
        );
        break;
      case "showMultilineIdText":
        setResult(
          <>
            <div className="edit-component-content-title">自定义ID集合</div>
            {schemaDetail.map((item, index) => {
              const { key, name, placeholder, required, maxLength, hidden } =
                item;
              return (
                <>
                  {key && (
                    <div>
                      <div className="edit-component-content-lable">
                        组件字段-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">{key}</div>
                    </div>
                  )}
                  {name && (
                    <div>
                      <div className="edit-component-content-lable">
                        组件名称-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">{name}</div>
                    </div>
                  )}
                  {placeholder && (
                    <div>
                      <div className="edit-component-content-lable">
                        组件提示-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {placeholder}
                      </div>
                    </div>
                  )}
                  {typeof required != "undefined" && (
                    <div>
                      <div className="edit-component-content-lable">
                        组件必填-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {required ? "是" : "否"}
                      </div>
                    </div>
                  )}
                  {maxLength && (
                    <div>
                      <div className="edit-component-content-lable">
                        数组ID最大数量-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {maxLength}
                      </div>
                    </div>
                  )}
                  {hidden && (
                    <div>
                      <div className="edit-component-content-lable">
                        是否隐藏组件-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {hidden}
                      </div>
                    </div>
                  )}
                </>
              );
            })}
          </>
        );
        break;
      case "selectionCollection":
        setResult(
          <>
            <div className="edit-component-content-title">自定义供给来源</div>
            {schemaDetail.map((item, index) => {
              const {
                selectionName,
                selectionFields,
                maxCommodityLength,
                isNotRequired,
                hidden,
              } = item;
              return (
                <>
                  {selectionFields && (
                    <div>
                      <div className="edit-component-content-lable">
                        组件字段-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {selectionFields}
                      </div>
                    </div>
                  )}
                  {selectionName && (
                    <div>
                      <div className="edit-component-content-lable">
                        组件前缀分类-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {selectionName}
                      </div>
                    </div>
                  )}
                  {maxCommodityLength && (
                    <div>
                      <div className="edit-component-content-lable">
                        选品集最大值-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {maxCommodityLength}
                      </div>
                    </div>
                  )}
                  {typeof isNotRequired != "undefined" && (
                    <div>
                      <div className="edit-component-content-lable">
                        组件必填-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {isNotRequired ? "否" : "是"}
                      </div>
                    </div>
                  )}
                  {hidden && (
                    <div>
                      <div className="edit-component-content-lable">
                        是否隐藏组件-{index + 1}：
                      </div>
                      <div className="edit-component-content-value">
                        {hidden}
                      </div>
                    </div>
                  )}
                </>
              );
            })}
          </>
        );
        break;
      case "showMainCategory":
        setResult(
          <div>
            <div className="edit-component-content-lable">
              开启商家主营类目：
            </div>
            <div className="edit-component-content-value">
              {schemaDetail ? "是" : "否"}
            </div>
          </div>
        );
        break;
      case "supChannel":
        setResult(
          <div>
            <div className="edit-component-content-lable">
              开启关联上级配置：
            </div>
            <div className="edit-component-content-value">
              {schemaDetail &&
              schema["outRequired"] &&
              schema["outRequired"].includes("relatedSup")
                ? "是"
                : "否"}
            </div>
          </div>
        );
        break;
      case "oversupply":
        const {
          maxTabLength,
          maxCommodityLength,
          nameSetting,
          name,
          field,
          maxFieldLength,
          isAllowShow,
          isRequired,
          isNotRequired,
          initTabLength,
          hideTabName,
          defaultSupplyType,
          defaultFieldValue,
          fieldType,
        } = schemaDetail;
        setResult(
          <>
            <div className="edit-component-content-title">多样化供给来源</div>
            {maxTabLength && (
              <div>
                <div className="edit-component-content-lable">
                  最多有几个TAB：
                </div>
                <div className="edit-component-content-value">
                  {maxTabLength}
                </div>
              </div>
            )}
            {maxCommodityLength && (
              <div>
                <div className="edit-component-content-lable">
                  选品集最大个数：
                </div>
                <div className="edit-component-content-value">
                  {maxCommodityLength}
                </div>
              </div>
            )}
            {nameSetting && (
              <div>
                <div className="edit-component-content-lable">
                  TAB的lable名称：
                </div>
                <div className="edit-component-content-value">
                  {nameSetting}
                </div>
              </div>
            )}
            {name && (
              <div>
                <div className="edit-component-content-lable">字段的名称：</div>
                <div className="edit-component-content-value">{name}</div>
              </div>
            )}
            {field && (
              <div>
                <div className="edit-component-content-lable">字段的key：</div>
                <div className="edit-component-content-value">{field}</div>
              </div>
            )}
            {maxFieldLength && (
              <div>
                <div className="edit-component-content-lable">
                  字段的最大字数：
                </div>
                <div className="edit-component-content-value">
                  {maxFieldLength}
                </div>
              </div>
            )}
            {isAllowShow && (
              <div>
                <div className="edit-component-content-lable">一直展示：</div>
                <div className="edit-component-content-value">
                  {isAllowShow ? "是" : "否"}
                </div>
              </div>
            )}
            {(typeof isNotRequired != "undefined" ||
              typeof isRequired != "undefined") && (
              <div>
                <div className="edit-component-content-lable">是否必填：</div>
                <div className="edit-component-content-value">
                  {!isNotRequired || isRequired ? "是" : "否"}
                </div>
              </div>
            )}
            {initTabLength && (
              <div>
                <div className="edit-component-content-lable">
                  初始化几个tab：
                </div>
                <div className="edit-component-content-value">
                  {initTabLength}
                </div>
              </div>
            )}
            {hideTabName && (
              <div>
                <div className="edit-component-content-lable">
                  隐藏tab名称：
                </div>
                <div className="edit-component-content-value">
                  {hideTabName ? "是" : "否"}
                </div>
              </div>
            )}
            {defaultSupplyType && (
              <div>
                <div className="edit-component-content-lable">
                  默认供给来源：
                </div>
                <div className="edit-component-content-value">
                  {defaultSupplyType == 1 ? "选品集ID" : "商品聚类"}
                </div>
              </div>
            )}
            {typeof defaultFieldValue != "undefined" && (
              <div>
                <div className="edit-component-content-lable">
                  TAB下数值默认值：
                </div>
                <div className="edit-component-content-value">
                  {defaultFieldValue}
                </div>
              </div>
            )}
            {fieldType && (
              <div>
                <div className="edit-component-content-lable">
                  TAB下字段类型：
                </div>
                <div className="edit-component-content-value">
                  {fieldType == "string" ? "字符字段" : "数值字段"}
                </div>
              </div>
            )}
          </>
        );
        break;
      case "removeCheckJumpUrl":
        setResult(
          <div>
            <div className="edit-component-content-lable">
              忽略校验跳转链接：
            </div>
            <div className="edit-component-content-value">
              {schemaDetail ? "是" : "否"}
            </div>
          </div>
        );
        break;
      default:
        break;
    }
  }, [schemaDetail]);
  return result;
}
