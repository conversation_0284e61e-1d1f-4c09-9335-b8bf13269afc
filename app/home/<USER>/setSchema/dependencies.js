import React, { useRef, useEffect, useState } from "react";
import EditComponentItem from "./editComponentItem";
import { Dialog } from "@alife/next";
import { Form, Field, Select } from "@alife/next";
import { componentList } from "./utils";
import { deepCopy } from "@/home/<USER>/common";

// 处理渲染依赖关系
export default function Dependencies(props) {
  const { componentKey, fatherComponentKey, schemaDetail, schema } = props;
  const {
    oneOf = [], //是满足且只满足一个
    // 底下这俩整不了，写法不兼容，我没整明白
    anyOf = [], //满足任意一个 Schema 即可
    allOf = [], //是要满足所有 Schema
  } = schemaDetail || {};
  const getDependenciesText = (type = "", key, schemaKey) => {
    let enumText = "";
    const propertiesKeyValue =
      schema[fatherComponentKey]["properties"][key] || {};
    if (propertiesKeyValue["enum"] && propertiesKeyValue["enumNames"]) {
      schemaKey.enum.map((item, index) => {
        const itemIndex = propertiesKeyValue["enum"].indexOf(item);
        const itemText =
          itemIndex > -1 ? propertiesKeyValue["enumNames"][itemIndex] : item;
        enumText =
          `${index == 0 && !itemText ? "" : ","}` + enumText + `${itemText}`;
      });
    } else if (
      propertiesKeyValue["enum"] &&
      propertiesKeyValue["x-ui-valueLabel"]
    ) {
      schemaKey.enum.map((item, index) => {
        const itemText = propertiesKeyValue["x-ui-valueLabel"][item] || item;
        enumText = enumText + `${index == 0 ? "" : ","}` + `${itemText}`;
      });
    } else {
      enumText = schemaKey.enum.join();
    }
    return (
      <span>
        {`当${schemaKey.title}等于`}
        <span className="selectText">{enumText}</span>
        {`时会展示以下表单`}
      </span>
    );
  };

  const onAddDepSchema = (type, index) => {
    let dataSource = componentList
      .map((item) => {
        console.log(
          "🚀 ~ file: dependencies.js:61 ~ returncomponentList.map ~ item:",
          item
        );
        if (
          [
            "boolean",
            "image",
            "input",
            "number",
            "color",
            "radio",
            "checkbox",
            "dataTime",
            "textarea",
            "select",
          ].includes(item.type)
        ) {
          return {
            label: item.title,
            value: JSON.stringify(item.demoSchema),
          };
        }
      })
      .filter((i) => i);
    // 由于弹窗用的函数的，schema要改成静态变量，state不生效
    let selectSchema = "{}";
    Dialog.show({
      v2: true,
      title: "新增原子组件",
      content: (
        <div className="edit-component-popup">
          <Form>
            <Form.Item
              label={"请选择组件："}
              {...{
                labelCol: { span: 6 },
                wrapperCol: {
                  span: 18,
                },
              }}
            >
              <Select
                dataSource={dataSource}
                style={{ width: "100%" }}
                onChange={(e) => {
                  selectSchema = e;
                }}
              ></Select>
            </Form.Item>
          </Form>
        </div>
      ),
      onOk: () => {
        let newSchema = deepCopy(schema);
        let schemaDemo = JSON.parse(selectSchema);
        newSchema[fatherComponentKey]["dependencies"][componentKey][type][
          index
        ]["properties"][schemaDemo.type] = schemaDemo;
        const onSchemaUpdate = new Event("onSchemaUpdate");
        onSchemaUpdate.schema = newSchema;
        window.dispatchEvent(onSchemaUpdate);
      },
      closeable: "esc,close",
    });
  };

  return (
    <div className="edit-component-item" key={componentKey}>
      <div className="edit-component-content">
        {oneOf && oneOf.length > 0 && (
          <>
            <div className="edit-component-content-title">
              oneOf-满足下方schema且只满足一个
            </div>
            {oneOf.map((item, index) => {
              const { properties = {}, required = [] } = item || {};
              return (
                <>
                  <div className="edit-component-content-child">
                    {properties &&
                      Object.keys(properties).map((key, nIndex) => {
                        return (
                          <>
                            {componentKey == key && (
                              <div className="multiple-tab">
                                <i className="order-num">{index + 1}</i>
                                {getDependenciesText(
                                  "oneOf",
                                  key,
                                  properties[key]
                                )}
                              </div>
                            )}
                            <EditComponentItem
                              schemaDetail={properties[key]}
                              required={required}
                              componentKey={key}
                              detailPropertiesLength={
                                Object.keys(properties).length - 1
                              }
                              schema={schema}
                              index={nIndex == 0 ? -1 : nIndex - 1}
                              fatherComponentKey={fatherComponentKey}
                              dependenciesDemo={{
                                isDependencies: true,
                                isDependenciesfields: componentKey == key,
                                fatherIndex: index,
                                fatherType: "oneOf",
                                dependenciesKey: componentKey,
                              }}
                            ></EditComponentItem>
                          </>
                        );
                      })}
                  </div>
                  <div
                    className="edit-component-content-add"
                    onClick={() => onAddDepSchema("oneOf", index)}
                  >
                    新增
                  </div>
                </>
              );
            })}
          </>
        )}
      </div>
    </div>
  );
}
