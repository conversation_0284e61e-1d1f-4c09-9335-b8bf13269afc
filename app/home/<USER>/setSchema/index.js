import "./style.scss";
import React, { useEffect, useState } from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import * as qs from "query-string";
import {
  Grid,
  Button,
  Input,
  Form,
  Dialog,
  Message,
  Field,
  Drawer,
} from "@alife/next";
const { Row, Col } = Grid;
import { ACLPermission } from "@/containers//PutInPage/request";
import { getQueryString } from "@/utils/others";
import { env } from '@/config';
import * as api from "@/utils/api";
import { getBucUser } from "@/adator/api";
import * as apiL from '@/adator/api';
import { componentList } from "./utils";
import { setSchema, getScheduleTemplate, setMetadataSchema, setNormalSchema, getNormalTemplateSchema, rootSendMsg } from "./request";
import JSONEdit from "./jsEdit";
import EditComponentList from "./editComponentList";
const formItemLayout = {};
import MarketEdit from "../../../../app/containers/channel/market/resource/edit";
import { deepCopy } from "@/home/<USER>/common";
import ImageViewDialog from "./ImageViewDialog";
function manageSetSchema(props) {
  console.log("🚀 ~ file: index.js:31 ~ manageSetSchema ~ props:", props)
  const {
    pageId = "",
    resourceId = "",
    resourceName = "",
    posId: locationId = "",
    posName = "",
    resourceType = "",
    bizId = '',
    bizType = ''
  } = qs.parse(props.location.search) || {};
  const [tabKey, setTabKey] = useState(1);
  const [empId, setEmpId] = useState("");
  const [lastName, setLastName] = useState("");
  const [visible, setVisible] = useState(false);
  const [editSchema, setEditSchema] = useState(null);
  // 权限+白名单
  const [empIdGroup] = useState([
    "178401",
    "342681",
    "177759",
    "321792",
    "231380",
    "322141",
    "356306",
    "409739",
  ]);
  const [canSetSchema, setCanSetSchema] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [currentImage, setCurrentImage] = useState();
  const [hiddenSilder, setHiddenSilder] = useState(false);
  const [updateCount,setUpdateCount] = useState(0);
  const field = Field.useField();

  const onHiddenKlSider = (hidden,newHiddenSilder) => {
    let nextHiddenSilder = typeof newHiddenSilder != 'undefined' ? newHiddenSilder : hiddenSilder;
    let klSider = document.querySelector(".kl-sider");
    let rightAnnouncement = document.querySelector(
      ".right-part.no-announcement"
    );
    let manageSchema = document.querySelector(".manage-set-schema");
    if (hidden == !nextHiddenSilder) {
      if (nextHiddenSilder) {
        klSider.classList.remove("kl-sider-hidden");
        rightAnnouncement.classList.remove("no-sider");
        manageSchema.classList.remove("no-sider");
      } else {
        klSider.classList.add("kl-sider-hidden");
        rightAnnouncement.classList.add("no-sider");
        manageSchema.classList.add("no-sider");
      }
      setHiddenSilder(!nextHiddenSilder);
    }
  };

  useEffect(() => {
    getUserName();
    window.addEventListener("onSchemaUpdate", (e) => {
      if (e.schema) {
        field.setValue("schemaJson", JSON.stringify(deepCopy(e.schema)));
        setUpdateCount(updateCount + parseInt(Math.random(10) * 1000))
      }
    });
    return ()=>{
      onHiddenKlSider(false,true)
    }
  }, []);

  useEffect(() => {
    getSetSchemaAuthority();
  }, []);

  useEffect(()=>{
    if (canSetSchema) {
      getAllSchema();
    }
  },[canSetSchema])
  
  const getSetSchemaAuthority = async () => {
    try {
      let resp = await apiL.queryRoleValidate("set_schema");
      setCanSetSchema(resp === 'YES')
    } catch (error) {
      apiL.onRequestError(error);
    }
  };

  /**
   * 获取schema的总方法
   */
  const getAllSchema = () =>{
    if (resourceId) {
      getSchema();
    }else if (bizId) {
      getNormalSchema();
    }
  }

  /**
   * 设置schema的总方法
   */
  const setAllSchema = () =>{
    if (resourceId) {
      onSetSchema()
    } else if (bizId) {
      onSetNormalSchema();
    }
  }

  // 发送钉钉通知
  const sendRootMsg = (data, type) => {
    try {
      // * schemeJson: [格式化地址](https://www.bejson.com/?data=${data.content})
      const userInfo = `${lastName}(${empId})`;
      let contentTitle = `## Schema 设置成功; Env: ${env}`;
      let content = `
      #### 发布人: ${userInfo};
      * resourceId: ${data.resourceId}; 
      * locationId: ${data.locationId}; 
      `;
      const fileLocation = `
      * fileLocation: app/home/<USER>/setSchema
      * [查看schema发布记录](https://selection.kunlun.alibaba-inc.com/#/tools/schemaReleaseRecord)
      `
      if (type === "origin") {
        contentTitle = `## Schema 元数据设置成功; Env: ${env}`;
      } else if (type === "biz") {
        contentTitle = `## Schema （非资源位）设置成功; Env: ${env}`;
        content = `
        #### 发布人: ${userInfo}; 
        * bizId: ${data.bizId}; 
        * bizType: ${data.bizType} 
        `;
      }

      rootSendMsg({
        msgtype: "markdown",
        markdown:{
          title: `${userInfo} ${contentTitle}`,
          text: contentTitle + content + fileLocation,
        }
      });
    } catch(e) {
      return false;
    }
  };

  // 设置schema
  const onSetSchema = (newJsonSchema) => {
    const isUseProps = typeof newJsonSchema === "string";
    Dialog.confirm({
      title: `确定设置schema？`,
      onOk: () => {
        let schemeJson = field.getValue("schemaJson");
        let schemeRequestDTO = {
          resourceId: resourceId,
          locationId: locationId,
          schemeJson: isUseProps ? newJsonSchema : schemeJson,
        };
        setSchema(schemeRequestDTO)
          .then((result) => {
            // sendRootMsg(schemeRequestDTO);
            Message.success("设置成功");
            if (newJsonSchema) setVisible(false);
          })
          .catch(api.onRequestError);
      },
    });
  };

  /**
   * 非资源位的设置schema
   **/
  const onSetNormalSchema = (newJsonSchema) => {
    const isUseProps = typeof newJsonSchema === 'string'
    Dialog.confirm({
      title: `确定设置schema？`,
      onOk: () => {
        let schemeJson = field.getValue('schemaJson');
        let schemeRequestDTO = {
          bizId,
          bizType,
          content: isUseProps ? newJsonSchema : schemeJson,
        };
        setNormalSchema(schemeRequestDTO)
          .then((result) => {
            sendRootMsg(schemeRequestDTO, 'biz');
            Message.success("设置成功");
            // if (newJsonSchema) setVisible(false);
          })
          .catch(api.onRequestError);
      },
    });
  };

  /**
   * 资源位的设置元数据schema
   **/
  const setMetaSchema = () => {
    Dialog.confirm({
      title: `确定设置schema元数据？`,
      onOk: () => {
        let schemeJson = field.getValue("schemaJson");
        let schemeRequestDTO = {
          resourceId: resourceId,
          locationId: locationId,
          schemeJson: schemeJson,
        };
        setMetadataSchema(schemeRequestDTO)
          .then((result) => {
            sendRootMsg(schemeRequestDTO, 'origin');
            Message.success("设置成功");
          })
          .catch(api.onRequestError);
      },
    });
  };

  /**
   * 资源位的获取元数据schema
   **/
  const getSchema = () => {
    let baseQueryRequestDTO = {
      resourceId: resourceId,
      posId: locationId,
    };
    getScheduleTemplate(baseQueryRequestDTO)
      .then((result) => {
        Message.success("获取成功");
        field.setValue("schemaJson", result);
      })
      .catch(api.onRequestError);
  };

  /**
   * 非资源位的获取元数据schema——常规链路
   * */
  const getNormalSchema = () => {
    let baseQueryRequestDTO = {
      bizId,
      bizType
    };
    getNormalTemplateSchema(baseQueryRequestDTO)
      .then((result) => {
        Message.success("获取成功");
        field.setValue("schemaJson", result);
      })
      .catch(api.onRequestError);
  }

  const clear = () => {
    field.reset();
    field.setValues({ schemaJson: "{}" });
  };

  // 获取用户信息
  const getUserName = () => {
    try {
      getBucUser().then((resp) => {
        let { empId: _empId, lastName: _lastName } = resp.data.data;
        setEmpId(_empId);
        setLastName(_lastName);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 新增schema时需要对结构进行初始化
  const initSchemaObject = (newSchemaJson = "{}") => {
    let newSchemaObject;
    try {
      newSchemaObject = JSON.parse(newSchemaJson) || "{}";
      if (!newSchemaObject.detail) {
        newSchemaObject.detail = {
          title: "基础配置",
          type: "object",
          required: [],
          properties: {},
        };
      }
      if (!newSchemaObject.detail.required) {
        newSchemaObject.detail.required = [];
      }
      if (!newSchemaObject.detail.properties) {
        newSchemaObject.detail.properties = {};
      }
    } catch (error) {
      console.log("🚀 ~ file: index.js:162 ~ initSchemaObject ~ error:", error);
    }
    return newSchemaObject;
  };

  // 添加schema
  const addSchemaObject = (item) => {
    const { demoSchema = "", type = "" } = item;
    let newSchemaJson = field.getValue("schemaJson");
    let newSchemaObject = initSchemaObject(newSchemaJson);

    // 单纯的添加某个demo
    if (
      [
        "boolean",
        "image",
        "input",
        "number",
        "color",
        "radio",
        "checkbox",
        "dataTime",
        "textarea",
        "select",
      ].includes(type)
    ) {
      const inputName = type + parseInt(Math.random(1) * 1000);
      newSchemaObject.detail.properties[inputName] = demoSchema;
    }

    if ([
      "showPutIn",
      "showAoi",
      "showCategory",
      "isCategoryCenter",
      "poolTypeConfig",
      "showMultilineIdText",
      "selectionCollection",
      "showMainCategory",
      "supChannel",
      "oversupply",
    ].includes(type)) {
      if ('supChannel' === type) {
        newSchemaObject['outRequired'] = ["relatedSup"]
      }
      if ("oversupply" === type) {
        newSchemaObject['showSingleOversupply'] = true
      }
      newSchemaObject[type] = demoSchema;
    }
    const onSchemaUpdate = new Event("onSchemaUpdate");
    onSchemaUpdate.schema = newSchemaObject;
    window.dispatchEvent(onSchemaUpdate);
  };

  // 获取可编辑组件
  const getEditComponentList = (newSchemaJson = {}) => {
    return Object.keys(newSchemaJson).map((key) => {
      return (
        <EditComponentList
          componentKey={key}
          schemaDetail={newSchemaJson[key]}
          schema={JSON.parse(field.getValue("schemaJson")) || {}}
          field={field}
        ></EditComponentList>
      );
    });
  };

  if (canSetSchema) {
    const schemaJson = field.getValue("schemaJson") || '{}';
    const schemaJsonString = JSON.parse(schemaJson || "{}");
    return (
      <div className="manage-set-schema">
        <Row className="schema-content">
          <Col span={6}>
            <div
              className="schema-left-list"
              key={`schema-left-list-${hiddenSilder}`}
            >
              <div className="schema-left-list-title">
                <div
                  className={`schema-left-list-icon schema-left-list-icon-${hiddenSilder}`}
                  onClick={() => onHiddenKlSider(!hiddenSilder)}
                >
                  <img src="https://img.alicdn.com/imgextra/i1/O1CN01NC4F3u1HhrlfpEfJ9_!!6000000000790-2-tps-154-128.png"></img>
                </div>
                参考组件列表
              </div>
              <div className="schema-left-list-component">
                {componentList.map((item) => {
                  const { title = "", demoImageUrl = "", desc = "" } = item;
                  return (
                    <div className="component-item">
                      <div
                        className="component-item-image"
                        onClick={() => {
                          setCurrentImage(demoImageUrl);
                          setDialogVisible(true);
                        }}
                      >
                        <img src={demoImageUrl} width={80} height={80}></img>
                      </div>
                      <div className="component-item-context">
                        <div className="component-item-name">{title}</div>
                        <div className="component-item-desc">{desc}</div>
                      </div>
                      <div
                        className="component-item-button"
                        onClick={() => addSchemaObject(item)}
                      >
                        新增
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </Col>
          <Col span={10}>
            <div className="schema-left">
              <div
                className="schema-position"
                key={`schema-position-${visible}-${updateCount}`}
              >
                <MarketEdit
                  {...props}
                  pageId={pageId}
                  resourceId={resourceId}
                  resourceName={resourceName}
                  posId={locationId}
                  posName={posName}
                  resourceType={resourceType}
                  location={props.location}
                  newSchemaJson={schemaJson || "{}"}
                ></MarketEdit>
              </div>
            </div>
          </Col>
          <Col span={8}>
            <Form
              {...formItemLayout}
              style={{ padding: "20px" }}
              className="schema-right"
            >
              <div className="schema-left-list-title">当前组件列表</div>
              {schemaJson && (
                <div className="schema-list-content">
                  {getEditComponentList(schemaJsonString)}
                </div>
              )}
              <div className="button-set-schema-content">
                <Button className="button-set-schema" onClick={() => setAllSchema()}>
                  设置schema
                </Button>
                {sessionStorage.getItem("showSetOriginSchema") ? (
                  <Button
                    className="button-set-schema"
                    onClick={() => setMetaSchema()}
                  >
                    设置schema元数据
                  </Button>
                ) : null}
                <Button
                  className="button-set-schema"
                  onClick={() => getAllSchema()}
                >
                  重新获取schema
                </Button>
                <Button
                  className="button-set-schema"
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  进阶版
                </Button>
              </div>
            </Form>
          </Col>
        </Row>
        <Drawer
          placement="right"
          visible={visible}
          onClose={() => setVisible(false)}
          closeable="mask,esc"
          bodyStyle={{
            padding: 0,
            height: "100%",
          }}
          width="800px"
        >
          {schemaJson && (
            <JSONEdit
              json={schemaJson}
              setSchema={(e) => {
                field.setValue("schemaJson", e);
                setVisible(false);
              }}
            />
          )}
        </Drawer>
        {dialogVisible && (
          <ImageViewDialog
            dialogVisible={dialogVisible}
            image={currentImage}
            setDialogVisible={setDialogVisible}
          ></ImageViewDialog>
        )}
      </div>
    );
  } else {
    return (
      <div style={{ textAlign: "center", paddingTop: "200px" }}>
        <p>当前没有权限</p>
      </div>
    );
  }
}

export const LogTimeSetSchemaPage = logTimeComponent(
  withRouter(manageSetSchema),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const SetSchema = permissionAccess(LogTimeSetSchemaPage);
