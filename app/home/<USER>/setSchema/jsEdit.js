import React, { useRef, useEffect, useState } from "react";
import { JSONEditor } from "vanilla-jsoneditor";
import { Button, Message } from "@alife/next";
import { isJSON } from "./utils";

export default function JSONEdit(props) {
  const { json = "{}", setSchema = () => {} } = props;
  const [newJSON, setNewJSON] = useState(json || "{}");
  const [isJson, setIsJson] = useState(true);
  const refContainer = useRef(null);
  const refEditor = useRef(null);

  useEffect(() => {
    refEditor.current = new JSONEditor({
      target: refContainer.current,
      props: {
        content: {
          json: JSON.parse(newJSON),
        },
        mode: "text",
        readOnly: false,
        askToFormat: true,
        flattenColumns: false,
        onChange: (e, v) => {
          if (e && e.text && isJSON(e.text)) {
            setNewJSON(JSON.stringify(JSON.parse(e.text)));
            setIsJson(true);
          } else {
            setIsJson(false);
          }
        },
      },
    });
  }, []);

  return (
    <div className="vanilla-jsoneditor-react" ref={refContainer}>
      <Button
        className="button-jsoneditor"
        onClick={() => {
          if (isJson) {
            setSchema(newJSON);
            Message.success("已保存");
          } else {
            Message.error("这不是一个合格的JSON串，请按照下方文案修改");
          }
        }}
      >
        确认
      </Button>
    </div>
  );
}
