import React, { useRef, useEffect, useState } from "react";
import {
  Grid,
  Button,
  Input,
  Form,
  Dialog,
  Message,
  Field,
  Drawer,
  Icon,
} from "@alife/next";
import GetSchemaForm from "./getSchemaForm";
import GetCustomComponent from "./getCustomComponent";
import { typeMap, formatMap, xUiWidgetMap, processObjects } from "./utils";
import { deepCopy } from "@/home/<USER>/common";
const CustomIcon = Icon.createFromIconfontCN({
  scriptUrl: "//at.alicdn.com/t/font_3225765_i380mxjybeh.js",
});

export default function EditComponentItem(props) {
  const {
    componentKey = "",
    schemaDetail = {},
    required: requiredList = [],
    hierarchy = 1,
    schema = {},
    field,
    detailPropertiesLength,
    index,
    isCustom = false,
    fatherComponentKey,
    dependenciesDemo: {
      isDependencies, //是不是依赖下面的Item
      isDependenciesfields, //是不是依赖下面的依赖字段的枚举
      fatherIndex,
      fatherType,
      dependenciesKey,
    } = {},
  } = props;
  const {
    title,
    disabled,
    type,
    description,
    default: defaultValue, //默认值
    enum: enumList, //枚举从中选一个作为值
    enumNames, //枚举值对应的名字
    maximum, //number最大值
    minimum, //number最小值
    exclusiveMaximum, //number是否包含最大值边界（默认 false）
    exclusiveMinimum, //number是否包含最小值边界（默认 false）
    multipleOf, //number步长（可被多少整除，默认为1）
    precision, //number精度保留几位小数
    format, //字符串格式 date-time/date/time/uri/color
    maxLength, //字符串最大长度
    minLength, //字符串最小长度
    pattern, //字符串需满足的正则表达式
    items, //数组元素的 Schema
    maxItems, //数组最大元素个数
    minItems, //数组最小元素个数
    uniqueItems, //数组内相同元素是否只出现一次
    properties, //对象属性列表
    required, //对象须包含的属性列表
  } = schemaDetail;

  const xUiDisabled = schemaDetail["x-ui-disabled"]; //表单项禁用，支持表达式
  const xUiHidden = schemaDetail["x-ui-hidden"]; //表单项隐藏，支持表达式
  const xUiValueLabel = schemaDetail["x-ui-valueLabel"] || ""; //存在 enum 定义时可用key 为 enum 中的值（转为字符串）value 为选项 label
  const xUiAddonTextBefore = schemaDetail["x-ui-addonTextBefore"] || ""; //输入框前部的提示文字
  const xUiAddonTextAfter = schemaDetail["x-ui-addonTextAfter"] || ""; //输入框尾部的提示文字
  const xUiWidget = schemaDetail["x-ui-widget"] || ""; //自定义表单控件，见 常用表单控件
  const xNeedAudit = schemaDetail["x-need-audit"] || false; //需要安全审核字段
  const xUiPlaceholder = schemaDetail["x-ui-placeholder"] || ""; //输入框底纹文字
  const xUiValidate = schemaDetail["x-ui-validate"] || ""; //自定义表单控件的校验规则,目前仅用于图片上传

  let [itemsState, setItemsState] = useState(false);
  let [propertiesState, setPropertiesState] = useState(false);
  const [visible, setVisible] = useState(false);
  const ref = useRef(null);

  // 用于执行弹窗onClick⌚️
  const updateSectionSchema = () => {
    ref.current.onClickUpdateReturnAction(schema, field);
    setVisible(false);
  };

  const [isTop, setIsTop] = useState(false);
  const [isBottom, setIsBottom] = useState(false);

  useEffect(() => {
    // 如果当前组件是第一个或最后一个，没法移动
    if (index == 0) {
      setIsTop(true);
    } else {
      setIsTop(false);
    }
    if (index == detailPropertiesLength - 1) {
      setIsBottom(true);
    } else {
      setIsBottom(false);
    }
    if (index == -1) {
      setIsTop(true);
      setIsBottom(true);
    }
  }, [detailPropertiesLength]);

  // 移动组件对象位置 up：向上 down：向下
  const moveComponent = (isMoveUp) => {
    let newSchema = deepCopy(schema);
    let newSchemaDetail = {};
    let schemaDetailKeys = isDependencies
      ? Object.keys(
          newSchema[fatherComponentKey]["dependencies"][dependenciesKey][
            fatherType
          ][fatherIndex]["properties"]
        )
      : Object.keys(newSchema[fatherComponentKey]["properties"]);

    let schemaDetailIndex = schemaDetailKeys.indexOf(componentKey);
    if (isMoveUp) {
      [
        schemaDetailKeys[schemaDetailIndex - 1],
        schemaDetailKeys[schemaDetailIndex],
      ] = [
        schemaDetailKeys[schemaDetailIndex],
        schemaDetailKeys[schemaDetailIndex - 1],
      ];
    } else {
      [
        schemaDetailKeys[schemaDetailIndex],
        schemaDetailKeys[schemaDetailIndex + 1],
      ] = [
        schemaDetailKeys[schemaDetailIndex + 1],
        schemaDetailKeys[schemaDetailIndex],
      ];
    }
    if (isDependencies) {
      schemaDetailKeys.map((key) => {
        newSchemaDetail[key] =
          newSchema[fatherComponentKey]["dependencies"][dependenciesKey][
            fatherType
          ][fatherIndex]["properties"][key];
      });
      newSchema[fatherComponentKey]["dependencies"][dependenciesKey][
        fatherType
      ][fatherIndex]["properties"] = newSchemaDetail;
    } else {
      schemaDetailKeys.map((key) => {
        newSchemaDetail[key] = newSchema[fatherComponentKey]["properties"][key];
      });
      newSchema[fatherComponentKey]["properties"] = newSchemaDetail;
    }
    const onSchemaUpdate = new Event("onSchemaUpdate");
    onSchemaUpdate.schema = newSchema;
    window.dispatchEvent(onSchemaUpdate);
  };

  return (
    <div className="edit-component-item" key={componentKey}>
      {hierarchy == 1 && (
        <div className="action-icon-list">
          {!isTop && !isCustom && (
            <CustomIcon
              type="luna-icon-moveup"
              size={16}
              color="#666666"
              className="action-icon"
              onClick={() => moveComponent(true)}
            />
          )}
          {!isBottom && !isCustom && (
            <CustomIcon
              type="luna-icon-movedown"
              size={16}
              color="#666666"
              className="action-icon"
              onClick={() => moveComponent(false)}
            />
          )}
        </div>
      )}
      <div className="edit-component-content">
        {isCustom ? (
          <GetCustomComponent {...props}></GetCustomComponent>
        ) : (
          <>
            {componentKey && (
              <div>
                <div className="edit-component-content-lable">组件字段名：</div>
                <div className="edit-component-content-value">
                  {componentKey}
                </div>
              </div>
            )}
            {title && title.trim() && (
              <div>
                <div className="edit-component-content-lable">组件名称：</div>
                <div className="edit-component-content-value">{title}</div>
              </div>
            )}
            {type && (
              <div>
                <div className="edit-component-content-lable">数据类型：</div>
                <div className="edit-component-content-value">
                  {typeMap[type]}
                </div>
              </div>
            )}
            {description && (
              <div>
                <div className="edit-component-content-lable">描述：</div>
                <div className="edit-component-content-value">
                  {description}
                </div>
              </div>
            )}
            {typeof defaultValue != "undefined" && (
              <div>
                <div className="edit-component-content-lable">默认值：</div>
                <div className="edit-component-content-value">
                  {Array.isArray(defaultValue)
                    ? defaultValue.join()
                    : defaultValue}
                </div>
              </div>
            )}
            {(disabled || xUiDisabled) && (
              <div>
                <div className="edit-component-content-lable">表单项禁用：</div>
                <div className="edit-component-content-value">
                  {disabled ? "是" : "否"}
                </div>
              </div>
            )}
            {enumList && enumNames && (
              <div>
                <div className="edit-component-content-lable">枚举列表：</div>
                <div className="edit-component-content-table">
                  {enumList.map((item, index) => {
                    return (
                      <div className="edit-component-content-table-li">{`${item} -> ${
                        enumNames[index] || item
                      }`}</div>
                    );
                  })}
                </div>
              </div>
            )}
            {enumList && xUiValueLabel && (
              <div>
                <div className="edit-component-content-lable">枚举列表：</div>
                <div className="edit-component-content-table">
                  {enumList.map((item, index) => {
                    return (
                      <div className="edit-component-content-table-li">{`${item} -> ${
                        xUiValueLabel[item] || item
                      }`}</div>
                    );
                  })}
                </div>
              </div>
            )}

            {typeof maximum !== "undefined" && (
              <div>
                <div className="edit-component-content-lable">数值最大值：</div>
                <div className="edit-component-content-value">{maximum}</div>
              </div>
            )}
            {typeof minimum !== "undefined" && (
              <div>
                <div className="edit-component-content-lable">数值最小值：</div>
                <div className="edit-component-content-value">{minimum}</div>
              </div>
            )}
            {typeof exclusiveMaximum !== "undefined" && (
              <div>
                <div className="edit-component-content-lable">
                  包含最大值边界值：
                </div>
                <div className="edit-component-content-value">
                  {exclusiveMaximum}
                </div>
              </div>
            )}
            {typeof exclusiveMinimum !== "undefined" && (
              <div>
                <div className="edit-component-content-lable">
                  包含最小值边界值：
                </div>
                <div className="edit-component-content-value">
                  {exclusiveMinimum}
                </div>
              </div>
            )}
            {multipleOf && (
              <div>
                <div className="edit-component-content-lable">步长：</div>
                <div className="edit-component-content-value">{multipleOf}</div>
              </div>
            )}
            {precision && (
              <div>
                <div className="edit-component-content-lable">精度：</div>
                <div className="edit-component-content-value">{precision}</div>
              </div>
            )}
            {format && (
              <div>
                <div className="edit-component-content-lable">字符串格式：</div>
                <div className="edit-component-content-value">
                  {formatMap[format]}
                </div>
              </div>
            )}
            {typeof maxLength != "undefined" && (
              <div>
                <div className="edit-component-content-lable">
                  字符串最大长度：
                </div>
                <div className="edit-component-content-value">{maxLength}</div>
              </div>
            )}
            {typeof minLength != "undefined" && (
              <div>
                <div className="edit-component-content-lable">
                  字符串最小长度：
                </div>
                <div className="edit-component-content-value">{minLength}</div>
              </div>
            )}
            {pattern && (
              <div>
                <div className="edit-component-content-lable">
                  需满足的正则表达式：
                </div>
                <div className="edit-component-content-value">{pattern}</div>
              </div>
            )}
            {items && (
              <div className="edit-component-child">
                <div>
                  <div className="edit-component-content-lable">
                    数组元素schema：
                  </div>
                  <div
                    className="edit-component-content-button-value"
                    onClick={() => setItemsState(!itemsState)}
                  >
                    {itemsState ? "收起" : "展开"}
                  </div>
                </div>
                {itemsState && (
                  <div className="edit-component-content-child">
                    <EditComponentItem
                      hierarchy={hierarchy + 1}
                      schemaDetail={items}
                    ></EditComponentItem>
                  </div>
                )}
              </div>
            )}
            {maxItems && (
              <div>
                <div className="edit-component-content-lable">
                  数组最大元素个数：
                </div>
                <div className="edit-component-content-value">{maxItems}</div>
              </div>
            )}
            {minItems && (
              <div>
                <div className="edit-component-content-lable">
                  数组最小元素个数：
                </div>
                <div className="edit-component-content-value">{minItems}</div>
              </div>
            )}
            {uniqueItems && (
              <div>
                <div className="edit-component-content-lable">
                  数组元素唯一：
                </div>
                <div className="edit-component-content-value">
                  {uniqueItems ? "是" : "否"}
                </div>
              </div>
            )}
            {componentKey && requiredList.includes(componentKey) && (
              <div>
                <div className="edit-component-content-lable">是否必填：</div>
                <div className="edit-component-content-value">是</div>
              </div>
            )}
            {properties && (
              <div className="edit-component-child">
                <div>
                  <div className="edit-component-content-lable">
                    数组元素schema：
                  </div>
                  <div
                    className="edit-component-content-button-value"
                    onClick={() => setPropertiesState(!propertiesState)}
                  >
                    {propertiesState ? "收起" : "展开"}
                  </div>
                </div>
                {propertiesState && (
                  <div>
                    {Object.keys(properties).map((key) => {
                      <div className="edit-component-content-child">
                        <EditComponentItem
                          hierarchy={hierarchy + 1}
                          componentKey={key}
                          schemaDetail={properties[key]}
                          required={required}
                        ></EditComponentItem>
                      </div>;
                    })}
                  </div>
                )}
              </div>
            )}
            {xUiPlaceholder && (
              <div>
                <div className="edit-component-content-lable">
                  输入框底纹文字：
                </div>
                <div className="edit-component-content-value">
                  {xUiPlaceholder}
                </div>
              </div>
            )}
            {xUiHidden && (
              <div>
                <div className="edit-component-content-lable">隐藏组件：</div>
                <div className="edit-component-content-value">{xUiHidden}</div>
              </div>
            )}
            {xUiAddonTextBefore && (
              <div>
                <div className="edit-component-content-lable">组件前缀：</div>
                <div className="edit-component-content-value">
                  {xUiAddonTextBefore}
                </div>
              </div>
            )}
            {xUiAddonTextAfter && (
              <div>
                <div className="edit-component-content-lable">组件后缀：</div>
                <div className="edit-component-content-value">
                  {xUiAddonTextAfter}
                </div>
              </div>
            )}
            {xUiWidget && (
              <div>
                <div className="edit-component-content-lable">
                  表单控件类型：
                </div>
                <div className="edit-component-content-value">
                  {xUiWidgetMap[xUiWidget]}
                </div>
              </div>
            )}
            {xNeedAudit && (
              <div>
                <div className="edit-component-content-lable">
                  字段安全审核：
                </div>
                <div className="edit-component-content-value">是</div>
              </div>
            )}
            {xUiValidate && (
              <div>
                <div className="edit-component-content-lable">校验规则：</div>
                <div className="edit-component-content-table">
                  {Object.keys(xUiValidate).map((key) => {
                    let result = null;
                    switch (key) {
                      case "width":
                        result = (
                          <div className="edit-component-content-table-li">{`宽度 ${xUiValidate[key]} px`}</div>
                        );
                        break;
                      case "height":
                        result = (
                          <div className="edit-component-content-table-li">{`高度 ${xUiValidate[key]} px`}</div>
                        );
                        break;
                      case "minWidth":
                        result = (
                          <div className="edit-component-content-table-li">{`最小宽度 ${xUiValidate[key]} px`}</div>
                        );
                        break;
                      case "maxWidth":
                        result = (
                          <div className="edit-component-content-table-li">{`最大宽度 ${xUiValidate[key]} px`}</div>
                        );
                        break;
                      case "minHeight":
                        result = (
                          <div className="edit-component-content-table-li">{`最小高度 ${xUiValidate[key]} px`}</div>
                        );
                        break;
                      case "maxHeight":
                        result = (
                          <div className="edit-component-content-table-li">{`最大高度 ${xUiValidate[key]} px`}</div>
                        );
                        break;
                      case "minSize":
                        result = (
                          <div className="edit-component-content-table-li">{`最小尺寸 ${xUiValidate[key]} kb`}</div>
                        );
                        break;
                      case "maxSize":
                        result = (
                          <div className="edit-component-content-table-li">{`最大尺寸 ${xUiValidate[key]} kb`}</div>
                        );
                        break;
                      case "accept":
                        result = (
                          <div className="edit-component-content-table-li">{`支持格式 ${xUiValidate[key]}`}</div>
                        );
                        break;
                      case "placeholder":
                        result = (
                          <div className="edit-component-content-table-li">{`提示： ${xUiValidate[key]}`}</div>
                        );
                        break;
                      default:
                        break;
                    }
                    return result;
                  })}
                </div>
              </div>
            )}
          </>
        )}
        {hierarchy == 1 && (
          <div className="edit-component-button">
            <Button
              className="button-edit-schema"
              onClick={() => {
                setVisible(true);
              }}
            >
              编辑
            </Button>
            <Button
              className="button-edit-schema"
              onClick={() => {
                let newSchema = deepCopy(schema);
                if (isDependencies) {
                  if (isDependenciesfields) {
                    delete newSchema[fatherComponentKey]["dependencies"][
                      componentKey
                    ][fatherType][fatherIndex];
                    newSchema[fatherComponentKey]["dependencies"][componentKey][
                      fatherType
                    ] = newSchema[fatherComponentKey]["dependencies"][
                      componentKey
                    ][fatherType].filter((i) => i);
                  } else {
                    delete newSchema[fatherComponentKey]["dependencies"][
                      dependenciesKey
                    ][fatherType][fatherIndex]["properties"][componentKey];
                  }
                } else {
                  if (isCustom) {
                    delete newSchema[componentKey];
                  } else {
                    delete newSchema[fatherComponentKey]["properties"][
                      componentKey
                    ];
                    if (
                      newSchema[fatherComponentKey]["required"].indexOf(
                        componentKey
                      ) > -1
                    ) {
                      newSchema[fatherComponentKey]["required"] = newSchema[
                        fatherComponentKey
                      ]["required"].filter((i) => i != componentKey);
                    }
                  }
                }
                const onSchemaUpdate = new Event("onSchemaUpdate");
                onSchemaUpdate.schema = newSchema;
                window.dispatchEvent(onSchemaUpdate);
              }}
            >
              删除
            </Button>
          </div>
        )}
      </div>
      <Dialog
        v2
        centered
        visible={visible}
        width={1000}
        onOk={() => updateSectionSchema()}
        onClose={() => setVisible(false)}
        onCancel={() => setVisible(false)}
        className="schema-edit-dialog"
      >
        <GetSchemaForm
          ref={ref}
          {...props}
          isRequired={requiredList.includes(componentKey)}
        ></GetSchemaForm>
      </Dialog>
    </div>
  );
}
