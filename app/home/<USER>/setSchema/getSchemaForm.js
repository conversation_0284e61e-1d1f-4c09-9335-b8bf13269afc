import React, {
  useRef,
  useEffect,
  useState,
  memo,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  Grid,
  Button,
  Input,
  Form,
  Dialog,
  Message,
  Field,
  Drawer,
} from "@alife/next";
import SchemaForm from "@/utils/Form/src";
import { deepCopy } from "@/home/<USER>/common";
import { processObjects } from "./utils";
const getSchemaForm = memo(
  forwardRef((props, outRef) => {
    const {
      schemaDetail = {},
      componentKey = "",
      isRequired,
      isCustom = false,
      schema: realSchema,
      fatherComponentKey,
      dependenciesDemo: {
        isDependencies, //是不是依赖下面的Item
        isDependenciesfields, //是不是依赖下面的依赖字段的枚举
        fatherIndex,
        fatherType,
        dependenciesKey,
      } = {},
    } = props;

    const comField = Field.useField({});
    const formRef = useRef(null);
    const [formData, setFormData] = useState();
    // 初始化schema，防止未定义的时候报错
    const [schema, setSchema] = useState({
      title: "",
      type: "object",
      required: [],
      properties: isCustom
        ? {}
        : {
            isRequired: {
              title: "是否必填:",
              type: "boolean",
            },
            componentKey: {
              type: "string",
              title: "组件字段名:",
              "x-ui-placeholder": "请输入组件字段名",
            },
          },
    });

    // 更改schema
    const onFormChange = (formData) => {
      setFormData(formData);
    };

    useEffect(() => {
      let newSchema = deepCopy(schema);
      if (!isCustom) {
        // 如果不是自定义
        Object.keys(schemaDetail).map((key) => {
          switch (key) {
            case "title":
              newSchema.properties["title"] = {
                type: "string",
                title: "组件名称:",
              };
              break;
            case "type":
              newSchema.properties["type"] = {
                type: "string",
                title: "组件数据类型:",
                disabled: true,
              };
              break;
            case "description":
              newSchema.properties["description"] = {
                type: "string",
                title: "描述:",
              };
              break;
            case "default":
              let newType = Array.isArray(schemaDetail["default"])
                ? "array"
                : schemaDetail["type"];
              newSchema.properties["default"] = {
                type: newType || "string",
                title: "默认值:",
                "x-ui-placeholder":
                  "组件默认值，如为数组类型需与枚举值对应，否则默认值失效",
              };
              if (Array.isArray(schemaDetail["default"])) {
                newSchema.properties["default"].items = {
                  type: schemaDetail["items"].type || "string",
                };
              }
              break;
            case "maxItems":
              newSchema.properties["maxItems"] = {
                title: "数组最大元素个数:",
                type: "number",
              };
              break;
            case "minItems":
              newSchema.properties["minItems"] = {
                title: "数组最小元素个数:",
                type: "number",
              };
              break;
            case "uniqueItems":
              newSchema.properties["uniqueItems"] = {
                title: "数组元素唯一:",
                type: "boolean",
              };
              break;
            case "items":
              if (
                schemaDetail["items"].enum ||
                schemaDetail["items"].enumNames
              ) {
                newSchema.properties["items"] = {
                  title: "子模块",
                  type: "object",
                  properties: {},
                };
                if (schemaDetail["items"].enum) {
                  newSchema.properties["items"].properties["enum"] = {
                    type: "array",
                    title: "组件枚举值：",
                    items: {
                      type: schemaDetail["items"]["type"] || "string",
                    },
                  };
                }
                if (schemaDetail["items"].enumNames) {
                  newSchema.properties["items"].properties["enumNames"] = {
                    type: "array",
                    title: "枚举值中文：",
                    items: {
                      type: "string",
                    },
                    description: "枚举值中文需要与枚举值一一对应",
                  };
                }
              }

              break;
            case "enum":
              newSchema.properties["enum"] = {
                type: "array",
                title: "组件枚举值：",
                uniqueItems: false,
                items: {
                  type:
                    typeof schemaDetail["enum"][0] == "number"
                      ? "number"
                      : "string",
                },
              };
              break;
            case "enumNames":
              newSchema.properties["enumNames"] = {
                type: "array",
                title: "枚举值中文：",
                items: {
                  type: "string",
                },
              };
              break;
            case "x-ui-valueLabel":
              newSchema.properties["x-ui-valueLabel"] = {
                type: "array",
                title: "枚举值中文：",
                items: {
                  type: "string",
                },
              };
              break;
            case "maximum":
              newSchema.properties["maximum"] = {
                title: "数值最大值:",
                type: "number",
              };
              break;
            case "minimum":
              newSchema.properties["minimum"] = {
                title: "数值最小值:",
                type: "number",
              };
              break;
            case "exclusiveMaximum":
              newSchema.properties["exclusiveMaximum"] = {
                title: "是否包含最大值边界:",
                type: "boolean",
                default: false,
              };
              break;
            case "exclusiveMinimum":
              newSchema.properties["exclusiveMinimum"] = {
                title: "是否包含最小值边界:",
                type: "boolean",
                default: false,
              };
              break;
            case "multipleOf":
              newSchema.properties["multipleOf"] = {
                title: "步长:",
                type: "number",
              };
              break;
            case "precision":
              newSchema.properties["precision"] = {
                title: "精度:",
                type: "number",
              };
              break;
            case "format":
              newSchema.properties["format"] = {
                title: "字符串格式:",
                type: "string",
                enum: ["date-time", "date", "time", "uri", "color"],
                enumNames: [
                  "日期时间格式",
                  "日期格式",
                  "时间格式",
                  "URI 格式",
                  "颜色选择器",
                ],
                disabled: true,
              };
              break;
            case "x-ui-widget":
              newSchema.properties["x-ui-widget"] = {
                title: "表单控件类型:",
                type: "string",
                enum: [
                  "textarea",
                  "radio",
                  "img-upload",
                  "select",
                  "NumberWidget",
                  "img-upload-new",
                  "dayOfWeek",
                  "DateTimeWidget",
                  "ColorWidget",
                  "color",
                  "CheckboxesWidget",
                  "checkboxes",
                  "activityAndBenefitWidget",
                ],
                "x-ui-valueLabel": {
                  textarea: "长文本输入",
                  radio: "按钮组",
                  "img-upload": "图片上传",
                  select: "选择组件",
                  NumberWidget: "数值按钮",
                  "img-upload-new": "图片上传",
                  "img-upload": "图片上传",
                  dayOfWeek: "星期选择",
                  DateTimeWidget: "时间选择",
                  ColorWidget: "颜色选择",
                  color: "颜色选择",
                  CheckboxesWidget: "多选组件",
                  checkboxes: "多选组件",
                  activityAndBenefitWidget: "颜色选择",
                },
                disabled: true,
              };
              break;
            case "maxLength":
              newSchema.properties["maxLength"] = {
                title: "字符串最大长度:",
                type: "number",
              };
              break;
            case "pattern":
              newSchema.properties["pattern"] = {
                type: "string",
                title: "正则表达式:",
                "x-ui-placeholder":
                  "校验字符串格式的正则表达式，非专业人士勿填",
              };
              break;
            case "minLength":
              newSchema.properties["minLength"] = {
                title: "字符串最小长度:",
                type: "number",
              };
              break;
            case "disabled":
            case "x-ui-disabled":
              newSchema.properties[key] = {
                title: "表单项禁用:",
                description: "表单项是否可编辑",
                type: "boolean",
                default: false,
              };
              break;
            case "x-need-audit":
              newSchema.properties[key] = {
                title: "安全审核:",
                type: "boolean",
                default: false,
              };
              break;
            case "x-ui-hidden":
              newSchema.properties["hiddenType"] = {
                title: "隐藏表单类型:",
                type: "string",
                "x-ui-widget": "radio",
                enum: ["0", "1"],
                enumNames: ["简单模式", "复杂模式"],
                default:
                  typeof schemaDetail["x-ui-hidden"] == "boolean" ? "0" : "1",
              };
              newSchema.properties["x-ui-hidden1"] = {
                title: "表单隐藏:",
                type: "boolean",
                default: false,
                "x-ui-hidden": "$.hiddenType == 1",
              };
              newSchema.properties["x-ui-hidden2"] = {
                title: "表单隐藏:",
                type: "string",
                description:
                  "可以使用$获取同一级数据，例如:$.timeSelection == 0",
                "x-ui-hidden": "$.hiddenType == 0",
              };
              break;
            case "x-ui-placeholder":
              newSchema.properties["x-ui-placeholder"] = {
                type: "string",
                title: "输入框底纹文字:",
              };
              break;
            case "x-ui-addonTextBefore":
              newSchema.properties["x-ui-addonTextBefore"] = {
                type: "string",
                title: "组件前缀:",
                "x-ui-placeholder": "输入框前部的提示文字",
              };
              break;
            case "x-ui-addonTextAfter":
              newSchema.properties["x-ui-addonTextAfter"] = {
                type: "string",
                title: "组件后缀:",
                "x-ui-placeholder": "输入框后部的提示文字",
              };
              break;
            case "x-ui-validate":
              schemaDetail["x-ui-validate"]["width"] &&
                (newSchema.properties["width"] = {
                  title: "宽度:",
                  type: "number",
                  "x-ui-addonTextAfter": "px",
                });
              schemaDetail["x-ui-validate"]["height"] &&
                (newSchema.properties["height"] = {
                  title: "高度:",
                  type: "number",
                  "x-ui-addonTextAfter": "px",
                });
              schemaDetail["x-ui-validate"]["minWidth"] &&
                (newSchema.properties["minWidth"] = {
                  title: "最小宽度:",
                  type: "number",
                  "x-ui-addonTextAfter": "px",
                });
              schemaDetail["x-ui-validate"]["maxWidth"] &&
                (newSchema.properties["maxWidth"] = {
                  title: "最大宽度:",
                  type: "number",
                  "x-ui-addonTextAfter": "px",
                });
              schemaDetail["x-ui-validate"]["minHeight"] &&
                (newSchema.properties["minHeight"] = {
                  title: "最小高度:",
                  type: "number",
                  "x-ui-addonTextAfter": "px",
                });
              schemaDetail["x-ui-validate"]["maxHeight"] &&
                (newSchema.properties["maxHeight"] = {
                  title: "最大高度:",
                  type: "number",
                  "x-ui-addonTextAfter": "px",
                });
              schemaDetail["x-ui-validate"]["minSize"] &&
                (newSchema.properties["minSize"] = {
                  title: "最小尺寸:",
                  type: "number",
                  "x-ui-addonTextAfter": "kb",
                });
              schemaDetail["x-ui-validate"]["maxSize"] &&
                (newSchema.properties["maxSize"] = {
                  title: "最大尺寸:",
                  type: "number",
                  "x-ui-addonTextAfter": "kb",
                });
              schemaDetail["x-ui-validate"]["accept"] &&
                (newSchema.properties["accept"] = {
                  type: "string",
                  title: "支持格式:",
                });
              break;
            default:
              break;
          }
        });
        setSchema(newSchema);
        // 将xUiValueLabel对象打平为数组与enum一一对应
        let newXUiValueLabel = [];
        schemaDetail["x-ui-valueLabel"] &&
          Object.keys(schemaDetail["x-ui-valueLabel"]).map((key) => {
            let newKey = key;
            if (schemaDetail["type"] == "number") {
              newKey = Number(key);
            }
            let enumIndex = schemaDetail["enum"].indexOf(newKey);
            if (enumIndex != -1) {
              newXUiValueLabel[enumIndex] =
                schemaDetail["x-ui-valueLabel"][newKey];
            }
          });

        // 将hidden组件匹成两个。
        let newXUiHidden =
          typeof schemaDetail["x-ui-hidden"] == "boolean"
            ? {
                "x-ui-hidden1": schemaDetail["x-ui-hidden"],
              }
            : { "x-ui-hidden2": schemaDetail["x-ui-hidden"] };

        // 方便字段打平
        let xUiValidate = schemaDetail["x-ui-validate"] || {};
        // 将获取的字段值全部打平出来。
        setFormData({
          ...schemaDetail,
          isRequired,
          "x-ui-valueLabel": newXUiValueLabel,
          ...newXUiHidden,
          ...xUiValidate,
          componentKey,
        });
      } else {
        // 处理弹窗样式问题，多层接口弹窗需加宽
        if (
          ["showMultilineIdText", "selectionCollection"].includes(componentKey)
        ) {
          let dialog = document.getElementsByClassName("schema-edit-dialog")[0];
          dialog.style.width = "60%";
          dialog.style.left = "calc(50vw - 30%)";
        }
        // 对自定义的表单进行定制化的schema设置
        switch (componentKey) {
          case "showPutIn":
            newSchema.properties[componentKey] = {
              type: "boolean",
              title: "开启渠道城市人群:",
            };
            break;
          case "showAoi":
            newSchema.properties[componentKey] = {
              type: "boolean",
              title: "开启投放AOI类型:",
            };
            break;
          case "poolTypeConfig":
            newSchema.properties[componentKey] = {
              type: "number",
              title: "开启商品/门店选品集:",
              enum: [1, 2],
              enumNames: ["商品", "门店"],
              "x-ui-widget": "radio",
            };
            break;
          case "showCategory":
            newSchema.properties[componentKey] = {
              type: "boolean",
              title: "开启所属类目:",
            };
            break;
          case "isCategoryCenter":
            newSchema.properties[componentKey] = {
              type: "boolean",
              title: "开启关联算法策略:",
            };
            break;
          case "showMainCategory":
            newSchema.properties[componentKey] = {
              type: "boolean",
              title: "开启商家主营类目:",
            };
            break;
          case "supChannel":
            newSchema.properties[componentKey] = {
              type: "boolean",
              title: "开启关联上级配置:",
            };
            break;
          case "showMultilineIdText":
            newSchema.properties[componentKey] = {
              type: "array",
              title: "多行数组ID类型:",
              items: {
                type: "object",
                require: [],
                properties: {
                  key: {
                    title: "组件字段:",
                    type: "string",
                    "x-ui-placeholder": "请输入组件的字段",
                  },
                  name: {
                    title: "组件名称:",
                    type: "string",
                    "x-ui-placeholder": "请输入组件的名称",
                  },
                  placeholder: {
                    title: "组件提示框:",
                    type: "string",
                    "x-ui-placeholder": "请输入组件提示框,例如：商家品牌id",
                  },
                  required: {
                    title: "是否必填:",
                    type: "boolean",
                  },
                  maxLength: {
                    title: "数组ID最大数量:",
                    type: "number",
                  },
                  hidden: {
                    title: "是否隐藏组件:",
                    type: "string",
                    description:
                      "只能识别!=和==。他会去判断formData中当前的值是否符合条件，符合条件就隐藏",
                  },
                },
              },
            };
            break;
          case "selectionCollection":
            newSchema.properties[componentKey] = {
              type: "array",
              title: "多行供给来源类型:",
              items: {
                type: "object",
                require: [],
                properties: {
                  selectionFields: {
                    title: "组件字段:",
                    type: "string",
                    "x-ui-placeholder": "请输入组件的字段",
                  },
                  selectionName: {
                    title: "组件前缀分类:",
                    type: "string",
                    "x-ui-placeholder": "请输入组件的前缀分类，例如：店铺榜",
                  },
                  maxCommodityLength: {
                    title: "选品集最大数量:",
                    type: "number",
                  },
                  isNotRequired: {
                    title: "是否非必填:",
                    type: "boolean",
                  },
                  hidden: {
                    title: "是否隐藏组件:",
                    type: "string",
                    description:
                      "只能识别!=和==。他会去判断formData中当前的值是否符合条件，符合条件就隐藏",
                  },
                },
              },
            };
            break;
          case "oversupply":
            newSchema.properties[componentKey] = {
              type: "object",
              title: "多样化供给来源:",
              properties: {
                maxTabLength: {
                  title: "TAB最大数量:",
                  type: "number",
                },
                initTabLength: {
                  title: "初始化TAB数量:",
                  type: "number",
                },
                maxCommodityLength: {
                  title: "选品集最大数量:",
                  type: "number",
                },
                nameSetting: {
                  title: "lable名称:",
                  type: "string",
                },
                name: {
                  title: "字段名称:",
                  type: "string",
                },
                field: {
                  title: "字段的key:",
                  type: "string",
                },
                maxFieldLength: {
                  title: "字段最大字数:",
                  type: "number",
                },
                defaultSupplyType: {
                  type: "number",
                  title: "默认供给来源:",
                  enum: [1, 2],
                  enumNames: ["选品集ID", "商品聚类"],
                  "x-ui-widget": "radio",
                },
                isAllowShow: {
                  title: "一直展示供给类型:",
                  type: "boolean",
                },
                isRequired: {
                  title: "是否必填:",
                  type: "boolean",
                  description: "和是否非必填必须同时满足才生效",
                },
                isNotRequired: {
                  title: "是否非必填:",
                  type: "boolean",
                  description: "和是否必填必须同时满足才生效",
                },
                hideTabName: {
                  title: "是否隐藏tab名称:",
                  type: "boolean",
                },
                fieldType: {
                  title: "TAB下字段类型:",
                  type: "string",
                  "x-ui-widget": "radio",
                  enum: ["string", "number"],
                  enumNames: ["字符字段", "数值字段"],
                  default: "string",
                },
                defaultFieldValue: {
                  title: "数值默认值:",
                  type: "number",
                  "x-ui-hidden": "$.fieldType == 'string'",
                },
              },
            };
            newSchema.properties["showSingleOversupply"] = {
              type: "boolean",
              title: "展示为多tab:",
              "x-ui-hidden": "$.oversupply.isAllowShow == true",
            };
            break;
          default:
            break;
        }
        setSchema(newSchema);
        setFormData({
          showPutIn: realSchema["showPutIn"],
          showAoi: realSchema["showAoi"],
          showMultilineIdText: realSchema["showMultilineIdText"],
          selectionCollection: realSchema["selectionCollection"],
          showMainCategory: realSchema["showMainCategory"],
          supChannel: realSchema["supChannel"],
          oversupply: realSchema["oversupply"],
          showSingleOversupply: realSchema["showSingleOversupply"],
          isCategoryCenter: realSchema["isCategoryCenter"],
          showCategory: realSchema["showCategory"],
          poolTypeConfig: realSchema["poolTypeConfig"],
        });
      }
    }, []);

    // 透传给外面的修改schema的方法
    useImperativeHandle(outRef, () => {
      return {
        onClickUpdateReturnAction(schema, field) {
          let result = deepCopy(formData);
          // 自定义
          if (isCustom) {
            schema = processObjects(schema, result);
            if (schema["supChannel"]) {
              schema["outRequired"] = ["relatedSup"];
            }
            if (schema["oversupply"] && schema["oversupply"]["isAllowShow"]) {
              schema["showSingleOversupply"] = false;
            }
            if (schema["showSingleOversupply"]) {
              schema["oversupply"]["isAllowShow"] = false;
            }
          } else {
            let isRequired = result.isRequired;
            let newKey = result["componentKey"];
            delete result["isRequired"];
            delete result["componentKey"];
            if (isDependencies) {
              // 有可能换key 索性全删
              let newSchemaRequire =
                schema[fatherComponentKey]["dependencies"][dependenciesKey][
                  fatherType
                ][fatherIndex]["required"];
              if (newSchemaRequire && newSchemaRequire.includes(componentKey)) {
                schema[fatherComponentKey]["dependencies"][dependenciesKey][
                  fatherType
                ][fatherIndex]["required"] = newSchemaRequire
                  .map((key) => (key != componentKey ? key : undefined))
                  .filter((i) => i);
              }
              // 如果必填直接加
              if (isRequired) {
                schema[fatherComponentKey]["dependencies"][dependenciesKey][
                  fatherType
                ][fatherIndex]["required"] = [
                  ...schema[fatherComponentKey]["dependencies"][
                    dependenciesKey
                  ][fatherType][fatherIndex]["required"],
                  newKey,
                ];
              }
            } else {
              // 有可能换key 索性全删
              if (
                schema["detail"]["required"] &&
                schema["detail"]["required"].includes(componentKey)
              ) {
                schema["detail"]["required"] = schema["detail"]["required"]
                  .map((key) => (key != componentKey ? key : undefined))
                  .filter((i) => i);
              }
              // 如果必填直接加
              if (isRequired) {
                schema["detail"]["required"] = [
                  ...schema["detail"]["required"],
                  newKey,
                ];
              }
            }
            // 磨平x-ui-hidden字符串和布尔值的差异
            if (
              typeof result["x-ui-hidden1"] != "undefined" ||
              typeof result["x-ui-hidden2"] != "undefined"
            ) {
              result["x-ui-hidden"] =
                result["x-ui-hidden1"] || result["x-ui-hidden2"];
              delete result["x-ui-hidden1"];
              delete result["x-ui-hidden2"];
            }
            // 处理图片
            if (schemaDetail["x-ui-validate"]) {
              let newSUiValidate = {
                width: result.width,
                height: result.height,
                minWidth: result.minWidth,
                maxWidth: result.maxWidth,
                minHeight: result.minHeight,
                maxHeight: result.maxHeight,
                minSize: result.minSize,
                maxSize: result.maxSize,
                accept: result.accept,
              };
              result["x-ui-validate"] = newSUiValidate;
              delete result["width"];
              delete result["height"];
              delete result["minWidth"];
              delete result["maxWidth"];
              delete result["minHeigh"];
              delete result["maxHeigh"];
              delete result["minSize"];
              delete result["maxSize"];
              delete result["accept"];
            }

            // 处理x-ui-valueLabel为数组的情况
            if (
              result["x-ui-valueLabel"] &&
              Array.isArray(result["x-ui-valueLabel"])
            ) {
              let newValueLable = {};
              result["x-ui-valueLabel"].map((value, index) => {
                if (typeof result["enum"][index] != undefined) {
                  newValueLable[result["enum"][index]] = value;
                }
              });
              result["x-ui-valueLabel"] = newValueLable;
            }
            let newSchema = processObjects(schemaDetail, result);
            if (typeof newSchema["x-ui-valueLabel"] != "undefined") {
              newSchema["x-ui-valueLabel"] = result["x-ui-valueLabel"];
            }
            if (isDependencies) {
              if (componentKey != newKey) {
                delete schema[fatherComponentKey]["dependencies"][
                  dependenciesKey
                ][fatherType][fatherIndex]["properties"][componentKey];
              }
              schema[fatherComponentKey]["dependencies"][dependenciesKey][
                fatherType
              ][fatherIndex]["properties"][newKey] = newSchema;
            } else {
              if (componentKey != newKey) {
                delete schema["detail"]["properties"][componentKey];
              }
              schema["detail"]["properties"][newKey] = newSchema;
            }
          }
          const onSchemaUpdate = new Event("onSchemaUpdate");
          onSchemaUpdate.schema = schema;
          window.dispatchEvent(onSchemaUpdate);
        },
      };
    });

    return (
      <Form
        field={comField}
        style={{ padding: "20px" }}
        className="schema-edit-component-form"
      >
        {formData && (
          <SchemaForm
            ref={formRef}
            formData={formData}
            schema={schema}
            onChange={(formData) => {
              onFormChange(formData);
            }}
          />
        )}
      </Form>
    );
  })
);

export default getSchemaForm;
