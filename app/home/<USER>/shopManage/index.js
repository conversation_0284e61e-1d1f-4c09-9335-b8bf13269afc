import React from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import "./style.scss";
function shopManage() {
  return (
    <div className="shop-manage">
      <div className="iframe-wrapper">
        <iframe
          src={`https://nr.alibaba-inc.com/op-fe/Mojito#/merchant/almighty`}
          frameBorder="0"
          className="preview-iframe">
        </iframe>
      </div>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(shopManage),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const SuperMarketShopManage = permissionAccess(LogTimePutInPage);
