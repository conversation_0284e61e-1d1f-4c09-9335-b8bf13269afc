import React, { useEffect, useState } from "react";
import {
  Form,
  Select,
  Input,
  Button,
  Grid,
  Field,
  CascaderSelect,
  NumberPicker,
} from "@alife/next";
import "./style.scss";
import * as api from "@/adator/api";
import { auditStateEnum, storeStateEnum } from "../common";
const FormItem = Form.Item;
const { Row, Col } = Grid;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};

export default function Filter(props) {
  // 与外部数据同步筛选条件(query, setQuery)
  const { query, setQuery, getTableList } = props;
  const [goodsCategoryEnum, setGoodsCategoryEnum] = useState([]); //商品类目
  const [goodsPresentPrice, setGoodsPresentPrice] = useState({}); //商品价格区间

  const field = Field.useField({
    onChange: (name, value) => {
      if (name != "goodsCategory") {
        query[name] = value;
        setQuery(query);
      }
    },
  });

  const { getValue, setValue } = field;

  useEffect(() => {
    getGoodsCategoryEnum();
  }, []);

  // 获取全部商品类目
  const getGoodsCategoryEnum = () => {
    try {
      let request = api.getSkuCategory;
      request().then((res) => {
        const dataSource = res.data.map((dataItem) => {
          dataItem.value = dataItem.value.toString();
          dataItem.level = 1;
          dataItem.children &&
            dataItem.children.map((subItem) => {
              subItem.value = subItem.value.toString();
              subItem.level = 2;
              subItem.children &&
                subItem.children.map((thirdItem) => {
                  thirdItem.value = thirdItem.value.toString();
                  thirdItem.level = 3;
                });
            });
          return dataItem;
        });
        setGoodsCategoryEnum(dataSource);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  // 查询列表
  const searchPoolList = () => {
    let request = {
      ...field.getValues(),
      goodsPresentPrice,
    };
    // 获取商品审核列表
    getTableList();
  };

  // 清空筛选项
  const resetPoolList = () => {
    field.reset();
    setGoodsPresentPrice({});
    // 清空query
    Object.keys(query).forEach((key) => {
      delete query[key];
    })
    setQuery({});
  };

  // 讲树转换成一个数组
  function treeToArray(tree) {
    return tree.reduce((res, item) => {
      const { children, ...i } = item;
      return res.concat(
        i,
        children && children.length ? treeToArray(children) : []
      );
    }, []);
  }

  // 获取商品类目初始值
  const getGoodsCategoryValue = () => {
    const newGoodsCategory = getValue("goodsCategory");
    if (newGoodsCategory && newGoodsCategory.length > 0) {
      return newGoodsCategory.map((item) => {
        if (typeof item == "string") return item;
        return item.value;
      });
    } else {
      return [];
    }
  };

  // 更改商品类目
  const changeGoodsCategoryValue = (goodsCategoryList) => {
    const flatGoodsCategory = treeToArray(goodsCategoryEnum);
    let newGoodsCategoryList = goodsCategoryList.map((item) => {
      return flatGoodsCategory.find((e) => {
        return e.value == item;
      });
    });
    setValue("goodsCategory", newGoodsCategoryList);
    query["goodsCategory"] = newGoodsCategoryList;
    setQuery(query);
  };

  return (
    <Form className="filter" field={field}>
      <div className="top">
        <span className="pool-title">
          使用说明：商品审核通过后，C端可见的生效时间会有几分钟延时
        </span>
      </div>
      <Row>
        <Col span={6}>
          <FormItem label="商品名称:" {...formItemLayout}>
            {/* <Select dataSource={poolTypeEnum}  name="poolType" style={{width: '100%'}}/> */}
            <Input name="goodsName" placeholder="请输入商品名称" />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="商品ID:" {...formItemLayout}>
            <Input name="goodIds" placeholder="请输入商品ID" />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="条形码:" {...formItemLayout}>
            <Input name="upcs" placeholder="请输入条形码" />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="店铺审核状态:" {...formItemLayout}>
            <Select
              name="storeState"
              dataSource={storeStateEnum}
              style={{ width: "100%" }}
            />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={6}>
          <FormItem label="店铺名称:" {...formItemLayout}>
            <Input name="storeName" placeholder="请输入店铺名称" />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="店铺ID:" {...formItemLayout}>
            <Input name="storeId" placeholder="请输入店铺ID" />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="商品价格区间:" {...formItemLayout}>
            <NumberPicker
              min={0}
              precision={1}
              step={1}
              onChange={(start) => {
                let newGoodsPresentPrice = goodsPresentPrice.end
                  ? { start, end: goodsPresentPrice.end }
                  : { start };
                setGoodsPresentPrice(newGoodsPresentPrice);
                query["goodsPresentPrice"] = newGoodsPresentPrice;
                setQuery(query);
              }}
              value={goodsPresentPrice.start}
            />
            -
            <NumberPicker
              min={0}
              precision={1}
              step={1}
              onChange={(end) => {
                let newGoodsPresentPrice = goodsPresentPrice.start
                  ? { start: goodsPresentPrice.start, end }
                  : { end };
                setGoodsPresentPrice(newGoodsPresentPrice);
                query["goodsPresentPrice"] = newGoodsPresentPrice;
                setQuery(query);
              }}
              value={goodsPresentPrice.end}
            />
            元
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="商品审核状态:" {...formItemLayout}>
            <Select
              name="auditState"
              dataSource={auditStateEnum}
              style={{ width: "100%" }}
            />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={6}>
          <FormItem label="商品类目:" {...formItemLayout}>
            <CascaderSelect
              name="goodsCategory"
              multiple
              dataSource={goodsCategoryEnum}
              style={{ width: "100%" }}
              value={getGoodsCategoryValue()}
              onChange={(value) => changeGoodsCategoryValue(value)}
            />
          </FormItem>
        </Col>
        <Col span={6}></Col>
        <Col span={6}></Col>
        <Col span={6}>
          <Button
            type="secondary"
            onClick={searchPoolList}
            style={{ marginLeft: "95px" }}
          >
            查询
          </Button>
          <Button className="btn_reset" onClick={resetPoolList}>
            重置
          </Button>
        </Col>
      </Row>
    </Form>
  );
}
