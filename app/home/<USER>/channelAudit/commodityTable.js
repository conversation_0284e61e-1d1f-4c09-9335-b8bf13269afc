import React, { useState } from "react";
import { Button, Table, Message, Pagination, Dialog } from "@alife/next";
import { FORMAT, formatTimeStamp } from "@/utils/time";
import { auditStateEnum } from "../common";
import * as api from "@/adator/api";

export default function commodityTable(props) {
  const {
    tableList = [],
    page,
    total,
    getTableList,
    pageSize,
    setPageSize,
    loading
  } = props;

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 审核操作
  const fetchEmallAudit = (record, operType) => {
    let request = {
      goodsIdList: [],
      operationType: operType,
    };
    if (record) {
      request.goodsIdList.push(record.goodsId);
    } else {
      request.goodsIdList = selectedRowKeys;
    }
    if (request.goodsIdList.length <= 0) {
      Message.error("您当前没有选中任何标签哦！");
      return;
    }
    try{
      api.emallAudit(request).then((response) => {
        if (response.data.data.success) {
          Message.success(
            `${request.goodsIdList.length > 1 ? "批量" : ""}操作通过成功`
          );
          getTableList(page);
        } else {
          Message.error(response.data.data.errMessage);
        }
      });
    } catch{
      Message.error('请求服务异常')
    }

  };

  // 页数修改
  const onPageChange = (page) => {
    getTableList(page);
    setSelectedRowKeys([]);
  };

  // 二次弹窗确认操作
  const dialogVisibleClick = (record, operType) => {
    if (record) {
      // 单独
      Dialog.confirm({
        title: `是否确认审核${record.auditState == 1 ? "不" : ""}通过选择的商品`,
        onOk: () => {
          fetchEmallAudit(record, operType);
        },
      });
    } else {
      // 批量
      Dialog.confirm({
        title: `是否确认审核${operType == 'remove'?'不':''}通过选择的${selectedRowKeys.length}件商品`,
        onOk: () => {
          fetchEmallAudit(record, operType);
        },
      });
    }
  };

  const columns = [
    { title: "商品名称", dataIndex: "goodsName", width: "160px" },
    {
      title: "商品id",
      dataIndex: "goodsId",
      width: "160px",
      cell: (value, index, record) => {
        return <div>{value}</div>;
      },
    },
    {
      title: "条形码",
      dataIndex: "goodsUPCId",
      width: "160px",
      cell: (value, index, record) => {
        return <div>{value}</div>;
      },
    },
    {
      title: "商品主图",
      dataIndex: "goodsPic",
      width: "100px",
      cell: (value, index, record) => {
        return <img src={value} height="60px" width="60px"></img>;
      },
    },
    {
      title: "商品售卖价",
      align: "right",
      dataIndex: "goodsPresentPrice",
      width: "120px",
      cell: (value, index, record) => {
        return <div>{`${value ? "¥" + value : "暂无"}`}</div>;
      },
    },
    {
      title: "商品库存",
      align: "right",
      dataIndex: "goodsNumber",
      width: "100px",
      cell: (value, index, record) => {
        return <div>{value}</div>;
      },
    },
    {
      title: "商品发布时间",
      dataIndex: "itemPublishDateTime",
      width: "160px",
      cell: (value, index, record) => {
        if (!value) return "暂无";
        else return formatTimeStamp(value, FORMAT.TIMETwo);
      },
    },
    {
      title: "商品更新时间",
      dataIndex: "itemUpdateDateTime",
      width: "160px",
      cell: (value, index, record) => {
        if (!value) return "暂无";
        else return formatTimeStamp(value, FORMAT.TIMETwo);
      },
    },
    {
      title: "所属店铺名称",
      dataIndex: "storeName",
      width: "160px",
      cell: (value, index, record) => {
        return <div>{value}</div>;
      },
    },
    {
      title: "所属店铺ID",
      dataIndex: "storeId",
      width: "160px",
      cell: (value, index, record) => {
        return <div>{value}</div>;
      },
    },
    {
      title: "审核状态",
      dataIndex: "auditState",
      width: "120px",
      cell: (value, index, record) => {
        return (
          <div className="blod">
            {auditStateEnum.map((e) => {
              if (e.value == value) return e.label;
            })}
          </div>
        );
      },
    },
    {
      title: "操作时间",
      dataIndex: "operationDateTime",
      width: "160px",
      cell: (value, index, record) => {
        if (!value) return value;
        else return formatTimeStamp(value, FORMAT.TIMETwo);
      },
    },
    {
      title: "操作人",
      dataIndex: "operationUser",
      width: "130px",
      cell: (value, index, record) => {
        return <div className="blod">{value ? value : "暂无"}</div>;
      },
    },
    {
      title: "操作",
      lock: "right",
      width: "160px",
      cell: (value, index, record) => {
        if(record.storeState == -1){
          return ''
        }
        return (
          <div className="opbtns">
            <Button
              size="small"
              type="primary"
              onClick={() =>
                dialogVisibleClick(
                  record,
                  record.auditState == 1 ? "remove" : "add"
                )
              }
            >
              {record.auditState == 1 ? "审核不通过" : "审核通过"}
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="goods-list">
      <div className="goods-list-top">
        <Button
          size="small"
          type="primary"
          onClick={() => dialogVisibleClick(null, "add")}
        >
          批量审核通过
        </Button>

        <Button
          size="small"
          type="primary"
          onClick={() => dialogVisibleClick(null, "remove")}
        >
          批量审核不通过
        </Button>

        <div className="bottom">
          <span>
            (操作提示：为避免误操作，批量操作请勾选相同审核状态的商品)
          </span>
        </div>
      </div>
      <Table
        dataSource={tableList}
        loading={loading}
        hasBorder={false}
        primaryKey="goodsId"
        rowSelection={{
          onChange: (selectedRowKeysProps, records) => {
            setSelectedRowKeys(selectedRowKeysProps);
          },
          selectedRowKeys: selectedRowKeys,
          getProps: record => {
            return {
              disabled: record.storeState == -1
            };
          }
        }}
        rowProps={(record, index) => {
          if (record.storeState == -1) {
            return {
              className: "gray-row",
              style: { color: "gray" },
            };
          }
        }}
      >
        {columns.map((e, idx) => {
          return <Table.Column {...e} key={idx} />;
        })}
      </Table>
      <Pagination
        onChange={onPageChange}
        onPageSizeChange={(pageSize) => {
          setPageSize(pageSize);
        }}
        total={total}
        current={page}
        pageSize={pageSize}
        pageSizeSelector="dropdown"
        pageSizeList={[10, 20, 50]}
        popupProps={{ align: "bl tl" }}
        style={{ float: "right", marginTop: "10px" }}
      />
    </div>
  );
}
