import React, { Fragment, useEffect, useState } from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import Filter from "./filter";
import * as api from "@/adator/api";
import CommodityTable from "./commodityTable";
import { Message } from "@alife/next";

export default function channelAudit({ location, match, history }) {
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(80);
  const [pageSize, setPageSize] = useState(10);
  const [query, setQuery] = useState({});
  const [tableList, setTableList] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getTableList(1);
  }, [pageSize]);

  // 请求前校验
  const beforeSearch = (request) => {
    let canSearch = true;
    const { goodIds, upcs, goodsPresentPrice = {} } = request;
    function idValidate(name, value) {
      if (value && value.length > 0 && value.split(",").length > 50) {
        Message.error(`${name}数量最多50个`);
        return false;
      } else if (value && value.indexOf("，") > 0) {
        Message.error(`${name}输入不能有中文逗号`);
        return false;
      } else if (value && value.indexOf(" ") != -1) {
        Message.error(`${name}输入不能有空格`);
        return false;
      } else if (value && value.indexOf("\n") > 0) {
        Message.error(`${name}输入不能有回车`);
        return false;
      }
    }
    if (typeof idValidate("商品ID", goodIds) == "boolean") {
      canSearch = idValidate("商品ID", goodIds);
    }
    if (typeof idValidate("条形码", upcs) == "boolean") {
      canSearch = idValidate("条形码", upcs);
    }
    if (goodsPresentPrice.end < goodsPresentPrice.start) {
      Message.error("商品价格最大值不能小于最小值");
      canSearch = false;
    }
    return canSearch;
  };
  /**
   * 商品列表查询
   **/
  const getTableList = (page = 1) => {
    // 补充参数
    let request = {
      pageSize: pageSize,
      pageIndex: page,
      ...query,
    };
    if (!beforeSearch(request)) return;
    setLoading(true);
    api
      .getAuditList({
        ...request,
      })
      .then((response) => {
        setTableList(response.data.data.data);
        setTotal(response.data.data.totalCount);
        setPage(page);
        setLoading(false);
      })
      .catch((error) => {
        api.onRequestError(error);
      });
  };

  return (
    <div className="channelAudit">
      <Filter
        query={query}
        setQuery={setQuery}
        getTableList={getTableList}
      ></Filter>
      <CommodityTable
        query={query}
        tableList={tableList}
        page={page}
        total={total}
        getTableList={getTableList}
        pageSize={pageSize}
        setPageSize={setPageSize}
        loading={loading}
      ></CommodityTable>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(channelAudit),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const SuperMarketChannelAudit = permissionAccess(LogTimePutInPage);
