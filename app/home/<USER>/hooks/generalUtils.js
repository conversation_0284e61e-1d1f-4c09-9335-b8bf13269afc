import React, { createContext, useEffect, useState } from "react";
import { onRequestError } from "@/utils/api";
import { getSkuCategory, getStoreMajorCategory } from "../api";
import { deepCopy } from "../utils";

const GeneralUtilsContext = createContext();

const GeneralUtilsProvider = ({ children }) => {
  const [storeMajorDataSource, setStoreMajorDataSource] = useState([]);
  const [catDataSource, setCatDataSource] = useState([]);

  useEffect(() => {
    /*获取商家主营类目*/
    const fetchData = async () => {
      try {
        const response = await getStoreMajorCategory();
        const data = (response || []).map((item) => ({
          label: item.name,
          value: item.code,
        }));
        setStoreMajorDataSource(data);
      } catch (error) {
        console.error("Error fetching store major categories:", error);
      }
    };

    /*获取商品分类数据*/
    const fetchSkuCategory = async () => {
      try {
        let request = getSkuCategory;
        let resp = await request();
        const dataSource = (resp.data || []).map((data) => {
          const dataItem = deepCopy(data);
          dataItem.value = dataItem.value.toString();
          dataItem.children &&
            dataItem.children.map((subItem) => {
              subItem.value = subItem.value.toString();
              subItem.children &&
                subItem.children.map((thirdItem) => {
                  thirdItem.value = thirdItem.value.toString();
                });
            });
          return dataItem;
        });
        setCatDataSource(dataSource);
      } catch (error) {
        console.error("error=======getSkuCategory", error);
        onRequestError(error);
      }
    };

    // 获取商家主营类目，首次加载，后续复用缓存的数据
    if (storeMajorDataSource.length === 0) {
      fetchData();
    }
    // 获取商品分类数据，首次加载，后续复用缓存的数据
    if (catDataSource.length === 0) {
      fetchSkuCategory();
    }
  }, []);

  return (
    <GeneralUtilsContext.Provider
      value={{ catDataSource, storeMajorDataSource }}
    >
      {children}
    </GeneralUtilsContext.Provider>
  );
};

export { GeneralUtilsContext, GeneralUtilsProvider };
