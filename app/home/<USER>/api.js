import { putInReq, request } from "@/utils/request";
import { onRequestSuccess } from "@/utils/api";

const requestInstance = request();
// 获取门店主营类目
export function getStoreMajorCategory() {
  return putInReq
    .get("/api/common/queryStoreMajorCategory")
    .then(onRequestSuccess);
}

/*全部类目*/
export function getSkuCategory() {
  return requestInstance
    .get(`/api/v2/config/queryAllSkuCategory`)
    .then(onRequestSuccess);
}

/*榜单新建*/
export function saveRank(params) {
  return putInReq.post(`/api/rank/save`, params).then(onRequestSuccess);
}

/*榜单详情*/
export function viewRank(params) {
  return putInReq.post(`/api/rank/detail`, params).then(onRequestSuccess);
}
