import { putInReq } from '@/utils/request';
import { onRequestSuccess }  from '@/utils/api'


/** 获取 Schema 列表 */
export function queryResourceSchemaVersions(params) {
  return putInReq.post('/api/deliverymanage/queryResourceSchemaVersions', params).then(onRequestSuccess)
}

/** 获取 Schema 元数据列表 */
export function queryMetadataSchemaVersions(params) {
  return putInReq.post('/api/deliverymanage/queryMetadataSchemaVersions', params).then(onRequestSuccess)
}

/** 获取 Schema 非资源位列表 */
export function queryOtherSchemaVersions(params) {
  return putInReq.post('/api/deliverymanage/queryOtherSchemaVersions', params).then(onRequestSuccess)
}