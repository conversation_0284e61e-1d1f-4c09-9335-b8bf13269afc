import React, {Fragment} from 'react';
import {Form, Checkbox, Radio, Select, Input, Button, Breadcrumb, Message} from '@alife/next';
import {crowdEum, PoolPageBase, timeEum} from '../common/index';
import {PageWrapper} from '@/comps/PageWrapper';
import {GroupFilter, CompletePage,BreadcrumbTips} from '../comps';

import './style.scss';
import {TimeRangePicker} from "@/components/TimeRangePicker";
import moment from "moment";
import {Steps,operatorEum} from "@/home/<USER>/common";
import {BaseInfo} from "./baseInfo";
import {PoolRules} from "./poolRules";
import * as api from "@/adator/api";
import {promisify} from "@/utils/others";

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {span: 7},
  wrapperCol: {
    span: 11
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

// const formLayout = {
//   style: {
//     margin: '20px 0px 10px 0px'
//   },
// }


/*
* 智能选品
* */
export class IntelligentPoolPage extends PoolPageBase {
  constructor(props) {
    super(props);
    let timePeriodGroup = timeEum.map((v)=>v.value);
    let crowdGroup = crowdEum.map((v)=>v.value);
    this.state = {
      step: 0,
      isShowNext:false,
      breadcrumbList: [
        {"title": '选品集管理', link: "#/pool/list"},
        {"title": '新建商品选品集', link: ""}
      ],
      poolId:this.params.poolId || '',
      effectRules: [
        {
          filterFieldId: "timePeriod",
          filterFieldKey: "timePeriod",
          // filterFieldValue: JSON.stringify(timePeriodGroup),
          filterFieldValue: JSON.stringify([]),
          operator: operatorEum["timePeriod"]
        },
        {
          filterFieldId: "crowd",
          filterFieldKey: "crowd",
          // filterFieldValue: JSON.stringify(crowdGroup),
          filterFieldValue: JSON.stringify([]),
          operator: operatorEum["crowd"]
        },
        {
          filterFieldId: "isCatComp",
          filterFieldKey: "isCatComp",
          filterFieldValue: "Y_CAT",
          operator: operatorEum["isCatComp"]
        },
        {
          filterFieldId: "isRiskComp",
          filterFieldKey: "isRiskComp",
          filterFieldValue: "1",
          operator: operatorEum["isRiskComp"]
        }
      ],
      params:{},
      baseInfo:{},
      tempPoolId:'',
      submitPoolId: '',
      submitDisabled: false,
      isShowSecond:false,
      isLoading: true
    }

    this.baseInfoRef = React.createRef();
    // this.poolRulesRef = React.createRef();
  }

  get poolId() {
    return this.params.poolId
  }

  get isEdit(){
    return !!this.poolId
  }

  componentDidMount() {
    if(this.isEdit) {
      this.getPoolDetail();
    }
    this.getTempId();
  }

  getTempId = async () =>{
    try {
      let request = api.getTempPoolId;
      let resp = await request();
      this.setState({
        tempPoolId:resp.data
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getPoolDetail() {
    const {poolId,baseInfo,params} = this.state;
    try {
      const request = api.getPoolDetailByPoolId;
      let resp = await request(poolId);
      baseInfo.poolName = resp.data.poolName;
      baseInfo.effectAt = resp.data.effectAt;
      baseInfo.expireAt = resp.data.expireAt;
      baseInfo.effectRange = [moment(resp.data.effectAt), moment(resp.data.expireAt)],
      baseInfo.outSynPlatforms = resp.data.outSynPlatforms;
      baseInfo.refreshMode = resp.data.refreshMode;
      baseInfo.isNotice = resp.data.isNotice;
      baseInfo.noticeUid = resp.data.noticeUid;
      params.refreshMode = resp.data.refreshMode;
      params.poolName = resp.data.poolName;
      params.outSynPlatforms = resp.data.outSynPlatforms;
      params.isNotice = resp.data.isNotice;
      params.noticeUid = resp.data.noticeUid;
      params.effectRange = [moment(baseInfo.effectAt),moment(baseInfo.expireAt)];
      this.setState({
        detailData: resp.data,
        effectRules: resp.data.effectRules,
        baseInfo,
        params
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  nextStep = () => {
    // let self = this;
    this.setState({
      step: 1 + this.state.step
    },()=>{
      this.changeLoading(false);
    })
  }

  prevStep = () => {
    this.setState({
      step: this.state.step - 1
    })
  }

  updateBaseInfo = (name, value) => {
    let {baseInfo, params} = this.state;
    baseInfo[name] = value;
    params[name] = value;
    this.setState({
      baseInfo,
      params
    },()=>{
      console.log(this.state.baseInfo);
    })
  }

  updatePoolRules = (name, value) => {
    let {effectRules} = this.state;
    let item = {
      filterFieldId: name,
      filterFieldKey: name,
      filterFieldValue: value,
      operator: operatorEum[name]
    };
    let group = effectRules.filter((v) => v.filterFieldId == name);
    if (group.length == 0) {
      effectRules.push(item);
    } else {
      group[0].filterFieldValue = value;
    }
    this.setState({
      params:{
        effectRules,
        ...this.state.params
      }
    },()=>{
      console.log(this.state.params);
    })
  }

  ctrlReq = () =>{
    const {params, baseInfo, effectRules,tempPoolId} = this.state;
    return {
      sourcePoolId: '23001',
      poolType: 1,
      poolName: params.poolName,
      effectAt: params.effectRange[0].valueOf(),
      expireAt: params.effectRange[1].valueOf(),
      isNotice: params.isNotice || 0,
      noticeUid: params.noticeUid || '',
      effectRules: effectRules,
      poolStoreType: '',
      tempPoolId,
      poolResultLimit: '1000000',
      refreshMode: params.refreshMode,
      // refreshMode: 1,
      outSynPlatforms: params.outSynPlatforms
    };
  }

  validateScene = () => {
    const {effectRules} = this.state;
    let result = [];
    effectRules.map((v) => {
      if ((v.filterFieldKey == 'timePeriod' || v.filterFieldKey == 'crowd') && v.filterFieldValue != "[]") {
        result.push(v.filterFieldKey);
      }
    })
    return result.length == 0
  }

  savePool = async () => {
    const {params, baseInfo, effectRules} = this.state;
    await (promisify(this.baseInfoRef.current.field.validate)());
    if(this.validateScene()){
      Message.warning('场景设定:时段和人群至少选一项');
      return;
    }
    // try {
    let createPoolV2Req = this.ctrlReq();
    let request = api.createPool;
    if (this.isEdit) {
      createPoolV2Req.poolId = this.poolId;
      request = api.updatePool;
    }
    let resp = await request(createPoolV2Req);
    if (!resp.data.data.success) {
      Message.warning(resp.data.data.errMessage);
    } else {
      Message.success('操作成功');
      this.history.push("/pool/list");
    }
  }

  submitPool = async () => {
    // this.savePool();
    let {params,effectRules} = this.state;
    await (promisify(this.baseInfoRef.current.field.validate)());
    if(this.validateScene()){
      Message.warning('场景设定:时段和人群至少选一项');
      return;
    }
    this.setState({
      submitDisabled:true
    })
    try {
      let createPoolV2Req = this.ctrlReq();
      let request = api.createPool;
      if (this.isEdit) {
        createPoolV2Req.poolId = this.poolId;
        request = api.updatePool;
      }
      let resp = await request(createPoolV2Req);
      let id = resp.data.data.data;
      if(id) {
        let result = await api.publishPool(id);
        Message.success('提交成功');
        this.setState({
          submitPoolId:id,
        })
        this.nextStep();
      }else{
        Message.info(resp.errMessage);
      }
    } catch (error) {
      api.onRequestError(error);
      this.setState({
        submitDisabled:false
      })
    }
  }

  showNextStep = (bool) => {
    this.setState({
      isShowNext: bool
    })
  }

  showSecond = (bool) =>{
    this.setState({
      isShowSecond: bool
    })
  }

  saveDs = (dataSource) =>{
    this.setState({
      dataSource
    })
  }

  saveTotal = (total) =>{
    this.setState({
      total
    })
  }

  saveDeleteDs = (deleteDataSource) =>{
    this.setState({
      deleteDataSource
    })
  }

  changeLoading = (bool) => {
    this.setState({isLoading: bool})
  }

  render() {
    const {step,breadcrumbList,effectRules,baseInfo,tempPoolId,detailData,submitPoolId,isShowNext,submitDisabled,isShowSecond,dataSource,deleteDataSource,total,isLoading} = this.state;
    return (
      <PoolPageBase.Container className="intelligent-pool">
        <BreadcrumbTips list={breadcrumbList} />
        <PageWrapper>
          <Steps current={step} middleText={'基本信息'} />
          {step == 0 &&
          <PoolRules isLoading={isLoading} isShowSecond={isShowSecond} showSecond={this.showSecond} saveDs={this.saveDs}
                     saveDeleteDs={this.saveDeleteDs} saveTotal={this.saveTotal} total={total} dataSource={dataSource}
                     deleteDataSource={deleteDataSource} showNextStep={this.showNextStep} tempPoolId={tempPoolId}
                     changeLoading={this.changeLoading} effectRules={effectRules} nextStep={this.nextStep}
                     updatePoolRules={this.updatePoolRules} isEdit={this.isEdit} /> }
          {step == 1 && <BaseInfo sourcePoolId={'23001'} isEdit={this.isEdit} ref={this.baseInfoRef} baseInfo={baseInfo}
                                  detailData={detailData} updateBaseInfo={this.updateBaseInfo}/>}
          {step == 2 && <CompletePage {...this.props} poolId={submitPoolId}/>}
          {(isShowNext && step != 2) && <div className={`next-step-panel step${step}`}>
            {step != 0 && <Button onClick={this.prevStep}>返回上一步</Button>}
            {step == 0 && <Button type={'primary'} onClick={this.nextStep}>下一步</Button>}
            {step == 1 && <Button type={'primary'} onClick={this.savePool}>保存信息</Button>}
            {step == 1 && <Button type={'primary'} disabled={submitDisabled} onClick={this.submitPool}>立即发布</Button>}
          </div>}
        </PageWrapper>
      </PoolPageBase.Container>
    )

  }
}
