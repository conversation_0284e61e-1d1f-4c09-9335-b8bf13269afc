import React, {Fragment} from 'react';
import {Form, Checkbox, Radio, Select, Input, Button, Field, Grid} from '@alife/next';
import {PoolPageBase, unique} from '../common/index';
import {PageWrapper} from '@/comps/PageWrapper';

import './style.scss';
import {TimeRangePicker} from "@/components/TimeRangePicker";
import moment from "moment";
import {refreshModeEum, outSynPlatformsEum, refreshRealModeEum, refreshBYRealModeEum} from "@/home/<USER>/common";
import * as api from "@/adator/api";

const {Row, Col} = Grid;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {span: 7},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

const timeRangeValidator = (rule, value, cb) => {
  const [start, end] = value;
  if (!start || !end) return cb('请输入正确的时间段')
  else {
    if(start.isBefore(moment().startOf('day'))) return cb('开始时间不能小于当前时间')
    // if (end.clone().subtract(4, 'month').isAfter(start.clone())) return cb('时间段不能超过4个月')
  }
  cb()
}

/*
* 智能选品-基本信息
* */
export class BaseInfo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      defaultTime: [moment().set({hour: 0, minute: 0, second: 0, millisecond:0}), moment().add(1, 'months').set({ hour: 23, minute: 59, second: 59, millisecond:0})],
      curMonth: 1,
      defaultRefreshMode: (props.sourcePoolId != '41001') ? 0 : 3,
      baseInfo:props.baseInfo,
      isEdit: props.isEdit,
      sourcePoolId:props.sourcePoolId,
      outSynPlatformsDs:[]
    }
    if(!props.isEdit) {
      this.props.updateBaseInfo("outSynPlatforms", (props.sourcePoolId != '41001') ? ["xt"] : ["billion_item"]);
      this.props.updateBaseInfo("effectRange", this.state.defaultTime);
      this.props.updateBaseInfo("refreshMode", this.state.defaultRefreshMode);
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        this.props.updateBaseInfo(name, value);
      }
    });
  }

  componentDidMount() {
    this.initField(this.state.baseInfo);
    this.fetchOutSynPlatforms();
  }

  componentWillReceiveProps(newProps) {
    this.setState({baseInfo: newProps.baseInfo,isEdit:newProps.isEdit,sourcePoolId:newProps.sourcePoolId}, () => {
      this.initField(this.state.baseInfo);
    });
  }

  initField = (baseInfo) => {
    if (baseInfo) {
      this.field.setValues({
        poolName:baseInfo.poolName,
        outSynPlatforms:baseInfo.outSynPlatforms,
        effectRange:baseInfo.effectRange,
        isNotice:baseInfo.isNotice,
        noticeUid:baseInfo.noticeUid,
        refreshMode:baseInfo.refreshMode
      });
      this.setState({
        isNotice:baseInfo.isNotice,
        noticeUid:baseInfo.noticeUid
      })
    }
  }

  onSubmit = () => {}

  checkNotice = (value) =>{
    this.props.updateBaseInfo("isNotice", value ? 1 : 0);
  }

  changeNoticeId = (value) =>{
    this.props.updateBaseInfo("noticeUid", value);
  }

  setDate = (month) => {
    let time =  [moment().set({ hour: 0, minute: 0, second: 0, millisecond:0}), moment().add(month, 'months').set({ hour: 23, minute: 59, second: 59, millisecond:0})]
    // let time = [moment(), moment().add(month, 'months')];
    if (month == 4) {
      time =  [moment().set({ hour: 0, minute: 0, second: 0, millisecond:0}), moment().add(month, 'months').add(-1,'days').set({ hour: 23, minute: 59, second: 59, millisecond:0})]
    }
    this.setState({
      curMonth: month
    }, () => {
      this.props.updateBaseInfo("effectRange", time);
      this.field.setValue("effectRange", time)
    })
  }

  fetchOutSynPlatforms= async () => {
    let {sourcePoolId,outSynPlatformsDs} = this.state;
    outSynPlatformsDs = [];
    try {
      let request = api.synPoolPlatformList;
      let resp = await request(sourcePoolId);
      if(resp.data.data.data.length>0){
        outSynPlatformsDs = resp.data.data.data.map((v)=>{
          return {
            label:v.label,
            value:v.platformCode
          }
        })
      }
      this.setState({
        outSynPlatformsDs
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  render() {
    const {defaultTime, curMonth,isEdit,outSynPlatformsDs} = this.state;
    const dateGroup = [
      {label: '两个月', value: '2'},
      {label: '三个月', value: '3'},
      {label: '四个月', value: '4'},
    ]
    let isNoticeValue = this.field.getValue("isNotice");
    return (
      <Form style={{margin: '10px'}} field={this.field}>
        <FormItem label="数据集名称" required asterisk={false} requiredMessage="数据集名称必填" {...formItemLayout}>
          <Input style={{width:'70%'}} placeholder="请输入数据集名称,不超过20个字" name="poolName" maxLength={20} />
        </FormItem>
        <FormItem label="有效期" required asterisk={false} {...formItemLayout} requiredMessage="有效期必填"  validator={timeRangeValidator} validatorTrigger={['onChange']}>
          <TimeRangePicker defaultValue={defaultTime} name="effectRange" disabledDate={(date) => date.isBefore(moment().startOf('day'))}></TimeRangePicker>
          {dateGroup.map((v) => {
            return <Button size="small" className={`${curMonth == v.value ? 'cur' : ''}`} onClick={() => this.setDate(v.value)}>{v.label}</Button>
          })}
        </FormItem>
        <FormItem>
          <Row>
            <Col span={7}></Col>
            <Col span={16}>
              <div className="time-limit">
                <label name='isNotice'><Checkbox checked={this.state.isNotice == 1 ? 'checked' : ''} onChange={this.checkNotice}/>选品池过期前接收过期提醒</label>
              </div>
            </Col>
          </Row>
        </FormItem>
        {Boolean(isNoticeValue && isNoticeValue==1) && <FormItem>
          <Row>
            <Col span={7}></Col>
            <Col span={16}>
              <div className="message-receipt">
                消息接收人：<Input style={{width: '300px'}} value={this.state.noticeUid} onChange={this.changeNoticeId} placeholder='可设置三个消息接受人,工号以逗号隔开' name="noticeUid"/>
              </div>
            </Col>
          </Row>
        </FormItem>}
        <FormItem label="更新机制" required asterisk={false}  requiredMessage="更新机制必填" {...formItemLayout}>
          <Radio.Group disabled={isEdit}
                       dataSource={parseInt(this.props.sourcePoolId) > 24001 ? ((parseInt(this.props.sourcePoolId) != 41001) ? refreshRealModeEum : refreshBYRealModeEum) : refreshModeEum}
                       name="refreshMode"/>
        </FormItem>
        <FormItem label="使用场景" requiredMessage="使用场景必填" asterisk={false} required {...formItemLayout}>
          <Checkbox.Group dataSource={outSynPlatformsDs} name="outSynPlatforms" disabled={this.props.sourcePoolId == '41001'} />
        </FormItem>
        {/*<Form.Submit*/}
        {/*  type="primary"*/}
        {/*  validate*/}
        {/*  onClick={() => this.onSubmit()}*/}
        {/*>*/}
        {/*  发布*/}
        {/*</Form.Submit>*/}
      </Form>
    )

  }
}
