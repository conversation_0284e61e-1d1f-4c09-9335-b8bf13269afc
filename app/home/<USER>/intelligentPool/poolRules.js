import React, {Fragment} from 'react';
import {Form, Checkbox, Radio, Select, Input, Button, Icon, Field, Message, Dialog} from '@alife/next';
import {PoolPageBase} from '../common/index';
import {PageWrapper} from '@/comps/PageWrapper';
import {GroupFilter} from '../comps';
import { isString } from '@/utils/validators';

import './style.scss';
import {CheckBoxWithAll} from "@/components/CheckBoxWithAll";
import moment from "moment";
import {operatorEum,timeEum,crowdEum} from "@/home/<USER>/common";

import {BaseInfo} from "./baseInfo";
import * as api from "@/adator/api";
import {SamplePool} from "@/home/<USER>/comps/samplePool";

const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {span:3},
  wrapperCol: {
    span: 18
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'right'
};


/*
* 智能选品
* */
export class PoolRules extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      step: 0,
      tabidx:1,
      isShowRules: false,
      effectFilterFieldBizModelList: [
        {
          filterFieldId: "goodsId",
          filterFieldKey: "goodsId",
          filterFieldValue:JSON.stringify([]),
          operator: operatorEum["goodsId"]
        }
      ],
      effectRules:props.effectRules,
      isShowSecond:props.isShowSecond,
      dataSource:props.dataSource,
      deleteDataSource:props.deleteDataSource,
      total:props.total,
      isLoading:props.isLoading,
      isEdit:props.isEdit
    }

    this.field = new Field(this, {
      onChange: (name, value) => {
        this.props.updatePoolRules(name, JSON.stringify(value));
      }
    });
  }

  componentDidMount() {
    this.initField(this.state.effectRules);
    // this.getGoodViewList();
  }

  componentWillReceiveProps(newProps) {
    this.setState({
      effectRules: newProps.effectRules,
      tempPoolId: newProps.tempPoolId,
      isShowSecond: newProps.isShowSecond,
      dataSource: newProps.dataSource,
      total:newProps.total,
      deleteDataSource: newProps.deleteDataSource,
      isLoading:newProps.isLoading,
      isEdit:newProps.isEdit
    }, () => {
      this.initField(this.state.effectRules);
    });
  }

  initField = (effectRules) => {
    if (effectRules.length > 0) {
      effectRules.map((v) => {
        if (["isCatComp"].includes(v.filterFieldKey)) {
          this.field.setValue(v.filterFieldKey, v.filterFieldValue);
        }else{
          this.field.setValue(v.filterFieldKey, JSON.parse(v.filterFieldValue));
        }
        // this.field.setValue(v.filterFieldKey, JSON.parse(v.filterFieldValue));
      })
    }
  }

  showMoreConfig = (bool) => {
    this.setState({
      isShowRules: bool
    })
  }

  validateScene = () => {
    const {effectRules} = this.state;
    let result = [];
    effectRules.map((v) => {
      if ((v.filterFieldKey == 'timePeriod' || v.filterFieldKey == 'crowd') && v.filterFieldValue != "[]") {
        result.push(v.filterFieldKey);
      }
    })
    return result.length == 0
  }

  onSelectChange = (value) =>{
    let {effectFilterFieldBizModelList} = this.state;
    let filterFieldValue = effectFilterFieldBizModelList.length > 0 ? effectFilterFieldBizModelList[0].filterFieldValue : 0;
    effectFilterFieldBizModelList = [];
    let item = {
      filterFieldId:value,
      filterFieldKey:value,
      operator:operatorEum[value],
      filterFieldValue
    };
    effectFilterFieldBizModelList.push(item);
    this.setState({effectFilterFieldBizModelList});
  }

  onInputChange = (value) =>{
    let {effectFilterFieldBizModelList} = this.state;
    /*if (effectFilterFieldBizModelList.length == 0) {
      Message.warning('请选条件');
    }*/
    let item = effectFilterFieldBizModelList[0];
    item.filterFieldValue = value ? JSON.stringify(value.split(",")) : "[]";
    this.setState({effectFilterFieldBizModelList});
  }

  onInspectView = () =>{
    let {tabidx} = this.state;
    if(this.validateScene()){
      Message.warning('场景设定:时段和人群至少选一项');
      return;
    }
    // this.setState({isShowSecond:true});
    this.props.showSecond(true);
    this.props.showNextStep(true);
    if(this.sampleRef) {
      this.sampleRef.getTableData();
    }
    // if(tabidx==1){
    //   this.onViewList(1);
    // }else{
    //   this.getDeleteGoodList();
    // }
  }

  onViewList = (type) => { //1:预览，2：查询
    this.setState({
      type,
      pageIndex: 1
    }, () => {
      this.getGoodViewList();
    })
  }

  getGoodViewList = async () => {
    let {pageIndex,effectRules,effectFilterFieldBizModelList,type,pageSize,orderBy,orderDirection} = this.state;
    try {
      let request = api.queryPoolViewList;
      let resp = await request({
        tempPoolId: this.state.tempPoolId,
        sourcePoolId:"23001",
        pageSize: pageSize || 100,
        pageIndex: pageIndex ||  1,
        orderDirection:orderDirection || 'desc',
        orderBy:orderBy || 'd7ValidOrderCnt',
        effectFilterFieldBizModelList: (type == 1) ? effectRules : effectRules.concat(effectFilterFieldBizModelList)
      });
      this.setState({
        dataSource: resp.data,
        total:resp.totalCount,
      },()=>{
        this.props.saveDs(resp.data);
        this.props.saveTotal(resp.totalCount);
        this.props.changeLoading(false);
      })
    } catch (error) {
      console.log(error);
      this.props.changeLoading(false);
      api.onRequestError(error)
    }
  }

  getDeleteGoodList = async () => {
    try {
      let request = api.queryDeleteList;
      let resp = await request(this.props.tempPoolId);
      this.setState({
        deleteDataSource: resp.data
      },()=>{
        this.props.saveDeleteDs(resp.data);
      })
    } catch (error) {
      console.log(error);
      api.onRequestError(error)
    }
  }

  changeTabidx = (tab) => {
    let {effectFilterFieldBizModelList} = this.state;
    this.setState({tabidx:tab},()=>{
      if (tab == 1) {
        this.getGoodViewList()
      } else {
        effectFilterFieldBizModelList[0].filterFieldValue = "[]";
        this.setState({effectFilterFieldBizModelList});
        this.getDeleteGoodList();
      }
    })
  }

  onPageAndSizeChange = (pageIndex, pageSize) => {
    this.setState({pageIndex, pageSize}, () => {
      this.changeTabidx(this.state.tabidx);
    })
  }

  onSort = (key, order) => {
    this.setState({
      orderBy: key,
      orderDirection: order
    },()=>{
      this.changeTabidx(this.state.tabidx)
    })
  }

  onChangeMethod = () =>{
    Dialog.show({
      title: "提示",
      content: '圈选方式修改后，当前设置不保存。仍要修改吗？',
      // messageProps: {
      //   type: "warning"
      // },
      okProps: {children: '仍然修改'},
      cancelProps: {children: '取消'},
      footerActions: ['cancel', 'ok'],
      onOk: () => location.href="#/pool/list/tagPool",
      onCancel: () => console.log("cancel")
    });
  }

  onRef = (ref) => {
    this.sampleRef = ref;
  }

  render() {
    const {step, isShowRules, dataSource,deleteDataSource,total,tabidx,isShowSecond,isLoading,isEdit} = this.state;
    const filterDs = [
      {label: '商品名称', value: 'goodsNameKeyWord'},
      {label: '商品条码', value: 'upcId'},
      {label: '商品ID', value: 'goodsId'},
    ]
    return (
      <>
        <div className="section-first">
          {/*<div className="header">圈选规则</div>*/}
          <h3><i className="order-num current">1</i>圈选方式：智能选品<Button style={{marginLeft:'8px'}} text type={'primary'} onClick={()=>this.onChangeMethod()} disabled={isEdit}>修改</Button></h3>
          <h3><i className="order-num current">2</i>场景设定<span>至少选填一项</span></h3>
          <Form field={this.field}>
            <FormItem label="时段" {...formItemLayout}>
              {/*<Checkbox.Group name='timePeriod' dataSource={timeEum} />*/}
              <CheckBoxWithAll dataSource={timeEum} name='timePeriod'/>
            </FormItem>
            <FormItem label="人群" {...formItemLayout}>
              {/*<Checkbox.Group name='crowd' dataSource={crowdEum} />*/}
              <CheckBoxWithAll dataSource={crowdEum} name='crowd'/>
            </FormItem>
          </Form>
          {!isShowRules && <div className="more-config-open" onClick={()=>this.showMoreConfig(true)}>更多配置<i/></div>}
          {isShowRules && <div className="more-config-close" onClick={()=>this.showMoreConfig(false)}>更多配置 <span>收起</span></div>}
          {/*<GroupFilter className={isShowRules ? 'block' : 'none'} {...this.props} />*/}
          {isShowRules && <GroupFilter {...this.props} {...this.state} />}
          <Button type="primary" onClick={()=>this.onInspectView()} >预览</Button>
        </div>
        {isShowSecond && <div className="section-second">
          {/*{tabidx==1 && <div className="rules">*/}
          {/*  <Select dataSource={filterDs} defaultValue={'goodsId'} onChange={this.onSelectChange} />*/}
          {/*  <Input onChange={this.onInputChange} />*/}
          {/*  <Button type="secondary" onClick={()=>this.onViewList(2)} style={{marginLeft:'10px'}}>查询</Button>*/}
          {/*  /!*<Button type="primary" onClick={this.reset}>重置</Button>*!/*/}
          {/*</div>}*/}
          {/*<ViewResult type={1} onSort={this.onSort} onPageAndSizeChange={this.onPageAndSizeChange} dataSource={dataSource} total={total} getGoodViewList={this.getGoodViewList} getDeleteGoodList={this.getDeleteGoodList} poolId={this.props.tempPoolId} changeTabidx={this.changeTabidx} />*/}
          <SamplePool  onRef={this.onRef}  sourcePoolId={'23001'} type={1} tempPoolId={this.props.tempPoolId} effectRules={this.props.effectRules}/>
        </div>}
      </>
    )
  }
}
