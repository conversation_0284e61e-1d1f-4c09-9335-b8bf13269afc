.intelligent-pool{
  margin-bottom:50px;
  .c-new-page-wrapper__content{
    padding: 24px 0;
  }
  .next-btn{
    &.cur{
      color:red;
      border-color:red;
    }
  }

  .more-config-open {
    border-radius: 4px;
    background-color: #FAFAFA;
    height: 40px;
    line-height: 40px;
    width: 50%;
    border: none;
    margin: 0 0 0 24px;
    cursor: pointer;
    text-align: center;

    &:hover {
      color: #FF7000;

      & > i {
        //background: url("../../../images/icon-add-circle2.png");
      }
    }

    & > i {
      //background: url("../../../images/icon-add-circle.png");
      background-size: 16px 16px;
      width: 16px;
      height: 16px;
      display: inline-block;
      //margin-top: 5px;
      position:relative;
      top: 3px;
      margin-left: 5px;
    }
  }

  .more-config-close {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #666666;
    margin-left: 50px;
    cursor: pointer;;

    & > span {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #999999;
      margin-left: 8px;
    }
  }
  .section-first,.section-second{
    //margin-top:20px;
    .total-info{
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      margin-bottom:24px;
      &>span{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
        margin-left: 8px;
      }
    }

    .order-num{
      width:16px;
      height:16px;
      line-height:16px;
      text-align:center;
      display:inline-block;
      border: 1px solid #FF7000;
      background-color: #FFFFFF;
      font-family: AlibabaSans102-Bold;
      font-size: 14px;
      font-style:normal;
      color: #FF7000;
      border-radius: 50%;
      margin-right:8px;
      &.current{
        background-color: #FF7000;
        color: #fff;
      }
    }
    .next-form{
      width:100%;
      margin-top:20px;
    }
    .header {
      background: rgba(255, 112, 0, 0.04);
      height: 48px;
      line-height: 48px;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #FF7000;
      padding-left: 24px;
    }
    &>h3{
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #666666;
      padding:24px 0 0 0px;
      font-weight:normal;
      &>span{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
        margin-left:8px;
      }
    }
    &>.next-btn{
      margin:24px;
    }
  }
  .section-first{
    h3{
      padding-left: 24px;
    }
  }
  .section-second{
    padding-left: 24px;
  }
  .next-step-panel {
    height: 68px;
    width: calc(100vw - 240px);
    background: #fff;
    position: fixed;
    bottom: 10px;
    overflow: hidden;
    z-index:10;
    //right: 10px;
    box-shadow: 0 -1px 10px 0 rgba(0,0,0,0.06);
    &.step0{
      padding-left: calc(100vw - 340px);
    }
    &.step1{
      padding-left: calc(100vw - 560px);
    }

    & > .next-btn {
      //float: right;
      margin:20px 8px 0 0;
    }
  }
  .total-info{

  }
  .deep-filter{
    &.block{
      display: block;
    }
    &.none{
      display: none;
    }
  }
  .rules{
    .next-select-inner{
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &>.next-input{
      border-left:0;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .time-limit {
    margin:12px 0;
    font-size: 14px;
    color: #333333;
    .next-checkbox-wrapper{
      margin-right: 7px;
    }
  }
}
