import React from 'react';
import {Grid} from '@alife/next';

import './style.scss';

const {Row, Col} = Grid;
/*
* 选类型
* */

const DEFAULT_Label_IMG = require('../../../images/icon-label.png');
const DEFAULT_Upload_IMG = require('../../../images/icon-upload.png');


export default function SelectMethod(props) {
  const methods = [
    {
      key:'tagPool',
      title: '标签选品',
      tips: '根据特征规则选品，圈选数量',
      num:'最大1000万',
      link: '/pool/list/tagPool',
      iconClass: 'icon-label',
      src: DEFAULT_Label_IMG
    },
    // {
    //   key:'intelligentPool',
    //   title: '智能选品',
    //   tips: '根据算法智能选品，圈选数量',
    //   num:'最大100万',
    //   link: '/pool/list/intelligentPool',
    //   iconClass: 'icon-intelligent',
    //   src: DEFAULT_Intelligent_IMG
    // },
    {
      key:'uploadPool',
      title: '文件上传',
      tips: '通过文件上传批量导入商品，数量',
      num:'上限10万',
      link: '/pool/list/fileUpload',
      iconClass: 'icon-upload',
      src: DEFAULT_Upload_IMG
    },
  ]
  const methods2 = [
    {
      key:'selectedStore',
      title: '标签选店',
      tips: '根据特征规则选品，圈选数量',
      num:'最大20万',
      link: '/pool/list/selectedStore',
      iconClass: 'icon-label',
      src: DEFAULT_Label_IMG
    },
    {
      key:'uploadPool',
      title: '文件上传',
      tips: '通过文件上传批量导入商品，数量',
      num:'上限10万',
      link: '/pool/list/fileUpload?sourcePoolId=32001',
      iconClass: 'icon-upload',
      src: DEFAULT_Upload_IMG
    },
  ]
  const method = (+props.poolType === 1) ? methods : methods2;
  const toLink = (v) => {
    props.history.push(v.link);
  }

  return (
    <div className="selection-method">
      <Row>
        {method.map((item, index) => {
          return <Col span={8} onClick={() => toLink(item)} className={(+index === +props.current) ? 'active' : ''}>
          <img src={item.src} className={`icon ${item.iconClass}`}/>
            <p className="title">{item.title}</p>
            <p className="tips">{item.tips}<span className="count">{item.num}</span></p>
          </Col>
        })}
      </Row>
    </div>
  )
}
