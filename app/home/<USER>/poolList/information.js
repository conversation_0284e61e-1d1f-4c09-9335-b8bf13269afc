import { Table, Balloon, Message, Icon, But<PERSON>, Dialog } from "@alife/next";
import React, { Fragment, useEffect, useState } from "react";
import Clipboard from "clipboard";
import "./style.scss";
import {
  changeEumToObject,
  dateRender,
  informationEnum,
  usageScenarioEnum
} from "../common/index";
import * as api from "@/adator/api";

export default function Information(props) {
  const { poolId } = props;
  const [dataSource, setDataSource] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [openRowKeys, setOpenRowKeys] = useState([]); //默认展开行

  useEffect(() => {
    if (poolId) {
      requestInfo();
    }
  }, [poolId]);

  const requestInfo = async () => {
    setIsLoading(true);
    let request = api.getPoolUsage;
    try {
      let resp = await request(poolId);
      setDataSource([
        ...resp.data.data.data[0].relationUsageInfoDTOListMap["PUTIN_ACTIVITY"],
        ...resp.data.data.data[0].relationUsageInfoDTOListMap["WELKIN_SCHEDULE"]
      ]);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      api.onRequestError(error);
    }
  };

  const clipboard = new Clipboard("#copy");
  clipboard.on("success", function (e) {
    console.log(e);
    Message.success("复制成功");
  });
  clipboard.on("error", function (e) {
    console.log(e);
  });
  const columns = [
    {
      title: "业务类型",
      dataIndex: "bizType",
      width: "150px",
      cell: (value) => {
        return usageScenarioEnum[value] || '其他'
      },
    },
    {
      title: "业务类型ID",
      dataIndex: "bizDataId",
      width: "150px",
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "状态",
      dataIndex: "state",
      cell: (value, index, record) => {
        return (
          <div>
            <div className={value ? "open" : "close"}></div>
            {value ? "已启用" : "已下线"}
          </div>
        );
      },
      width: "150px",
    },
    {
      title: "ext",
      dataIndex: "ext",
      cell: (value, index, record) => {
        return (
          <div style={{ display: "flex", flexWrap: "nowarp" }}>
            {value && (
              <div style={{ margin: "auto 0 auto 5px" }}>
                <Icon
                  type="copy"
                  style={{ cursor: "pointer" }}
                  className="btn-copy"
                  id="copy"
                  data-clipboard-text={value}
                />
              </div>
            )}
          </div>
        );
      },
      width: "100px",
    },
    {
      title: "描述",
      dataIndex: "desc",
      width: "170px",
    },
  ];
  const childrenColumn = [
    {
      title: "业务类型",
      dataIndex: "bizType",
      cell: (value, index, record) => {
        return <>{informationEnum[value]}</>;
      },
      width: "150px",
    },
    {
      title: "业务ID",
      dataIndex: "bizDataId",
      width: "100px",
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "状态",
      dataIndex: "state",
      cell: (value, index, record) => {
        return (
          <div>
            <div className={value ? "open" : "close"}></div>
            {value ? "已启用" : "已下线"}
          </div>
        );
      },
      width: "150px",
    },
    {
      title: "ext",
      dataIndex: "ext",
      cell: (value, index, record) => {
        return (
          <div style={{ display: "flex", flexWrap: "nowarp" }}>
            <div style={{ margin: "auto 0 auto 5px" }}>
              <Icon
                type="copy"
                style={{ cursor: "pointer" }}
                className="btn-copy"
                id="copy"
                data-clipboard-text={value}
              />
            </div>
          </div>
        );
      },
      width: "100px",
    },
    {
      title: "描述",
      dataIndex: "desc",
      cell: (value) => {
        return <>{descShow(value)}</>;
      },
      width: "210px",
    },
  ];
  const descShow = (value) => {
    if (value == '无关联投放活动数据数据' || value == '') {
      return value ? value : '无'
    }
    const keyList = value.length > 1 ? value.split(",") : [];

    return (
      <Button
        text
        onClick={() => {
          Dialog.show({
            v2: "true",
            closeable: "esc,mask",
            footerActions: ["cancel"],
            style: {
              width: "600px",
              height: "100%",
            },
            content: (
              <div className="dialog-desc">
                {keyList.length > 0 ? (
                  keyList.map((item) => {
                    const nextItem = item.split("=");
                    if (nextItem.length == 2) {
                      return (
                        <div className="keyValue">
                          <div className="lable">{nextItem[0]}:</div>
                          <div className="value">{nextItem[1]}</div>
                        </div>
                      );
                    } else if (nextItem.length >= 2) {
                      return (
                        <>
                          {nextItem.length > 1 &&
                            nextItem[nextItem.length - 1] != "" && (
                              <div className="keyValue">
                                <div className="lable">
                                  {nextItem.map((newItem, index) => {
                                    if (index != nextItem.length - 1) {
                                      return newItem + " ";
                                    } else {
                                      return ":";
                                    }
                                  })}
                                </div>
                                <div className="value">
                                  {nextItem[nextItem.length - 1]}
                                </div>
                              </div>
                            )}
                        </>
                      );
                    }
                  })
                ) : (
                  <div className="no-resout">当前无描述</div>
                )}
              </div>
            ),
          });
        }}
      >
        点击查看
      </Button>
    );
  };
  const expandedRowRender = (record, index) => {
    let newDataSource = [];
    Object.keys(record.relationUsageInfoDTOListMap).map((item) => {
      newDataSource = [
        ...newDataSource,
        ...record.relationUsageInfoDTOListMap[item],
      ];
    });
    return (
      <div style={{ marginTop: 10 }} className="expand-group">
        <Table
          dataSource={newDataSource}
          hasBorder={false}
          className={`child-table child-table-scope`}
        >
          {childrenColumn.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
      </div>
    );
  };
  const onRowOpen = (openRowKeys, currentRowKey) => {
    setOpenRowKeys(openRowKeys);
  };

  return (
    <div className="pool-information-content">
      <Table
        dataSource={dataSource}
        loading={isLoading}
        hasBorder={false}
        primaryKey="bizDataId"
        expandedRowIndent={[0, 0]}
        expandedRowRender={expandedRowRender}
        onRowOpen={onRowOpen}
        openRowKeys={openRowKeys}
      >
        {columns.map((e, idx) => {
          return <Table.Column {...e} key={idx} />;
        })}
      </Table>
    </div>
  );
}
