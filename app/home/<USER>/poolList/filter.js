import React, {Fragment, useEffect, useState} from 'react';
import {Form, Checkbox, Radio, Select, Input, Button, Grid, Field} from '@alife/next';
import {PoolPageBase} from '../common/index';

import {TimeRangePicker} from "@/components/TimeRangePicker";
import moment from "moment";
import {poolTypeEnum,publishStatusEnum,wayEnum} from "@/home/<USER>/common";
import './style.scss';
import {PageBase} from '@/home/<USER>/index'
import * as api from "@/adator/api";
import SelectType from "./selectType";
import {isAllNumber} from "@/utils/validators";
const FormItem = Form.Item;
const { Row, Col } = Grid;
const formItemLayout = {
  labelCol: {span: 8},
  wrapperCol: {
    span: 16
  },
  style: {
    width: '100%'
  }
};

// const formLayout = {
//   style: {
//     margin: '20px 0px 10px 0px'
//   },
// }
export default function Filter(props) {
  console.log(props);
  console.log('Filter');
  const [visible, setVisible] = useState(false);
  const [step, setStep] = useState(0);
  const [query, setQuery] = useState({});
  const [creatorUname, setCreatorUname] = useState('');

  const field = Field.useField({
    onChange: (name, value) => {
      query[name] = value;
      setQuery(query);
      props.getQuery(query);
    },
  });

  useEffect(() => {
    getUserName();
  }, [])

  const getUserName = () =>{
    try {
      let request = api.getBucUser;
      request().then((resp)=>{
        let name = resp.data.data.lastName;
        let {empId} = resp.data.data;
        query.creatorUname = name;
        field.setValue("creatorUname", name);
        setQuery(query);
        props.getCurrentName(name);
        props.getCurrentEmpId(empId);
        props.getQuery(query);
        props.getPoolList(1, query);
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  const nextStep = () => {
    setStep(1 + step)
  }

  const prevStep = () => {
    setStep(step - 1)
  }

  const searchPoolList = () =>{
    if(query.hasOwnProperty("poolType")){
      let item = query.poolType;
      query.poolType = item.toString().split(",");
    }
    setQuery(query);
    props.getQuery(query);
    props.getPoolList(1, query);
  }

  const resetPoolList = () =>{
    field.reset();
    query.creatorUname = '';
    setQuery(query);
    field.setValue('state','');
    // searchPoolList();
    props.getQuery({});
    props.getPoolList(1, {});
  }

  // 批量下线
  const offlinelPoolList = () =>{
    props.batchOfflinelPoolList()
  }


  /**检测id格式 */
  const checkId = function(rule, value, callback){
    const errors = isAllNumber(value);
    if(errors.length && value){
      callback('格式错误')
    } else {
      callback();
    }
  }

  return (
    <Form className="filter" field={field}>
      <div className='top'>
        <span className="pool-title">选品选商</span>
        <Button type='primary' className="btn-create-pool" onClick={()=>setVisible(true)}>新建选品集</Button>
      </div>
      <SelectType {...props} visible={visible} setVisible={setVisible} />
      <Row>
        <Col span={6}>
          <FormItem label="选品集类型" {...formItemLayout}>
            <Select dataSource={poolTypeEnum}  name="poolType" style={{width: '100%'}}/>
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="名称" {...formItemLayout}>
            <Input name="poolName" />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="ID " {...formItemLayout}  validator={checkId}>
            <Input name="poolId" />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="创建人" {...formItemLayout}>
            <Input name='creatorUname' />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={6}>
          <FormItem label="状态" {...formItemLayout}>
            <Select name='state' defaultValue={""} dataSource={publishStatusEnum} style={{width:'100%'}} />
          </FormItem>
        </Col>
        <Col span={6}>
          <FormItem label="圈选方式" {...formItemLayout}>
            <Select name='createMode' defaultValue={""} dataSource={wayEnum} style={{width:'100%'}} />
          </FormItem>
        </Col>
        <Col span={12} style={{textAlign:'right'}}>
          <Button type="secondary"  onClick={searchPoolList} style={{marginLeft:'95px'}}>查询</Button>
          <Button className="btn_reset" onClick={resetPoolList}>重置</Button>
          <Button type="primary" onClick={offlinelPoolList}>批量下线</Button>
        </Col>
      </Row>
    </Form>
  )
}
