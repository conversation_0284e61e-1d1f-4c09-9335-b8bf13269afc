import React, { useEffect, useState } from "react";
import Clipboard from "clipboard";
import {
  Button,
  Table,
  Message,
  Dialog,
  Pagination,
  Divider,
  Dropdown,
  Icon,
  Menu,
  Balloon,
} from "@alife/next";
import {
  crowdEum,
  dealQualityScoreToArray,
  getTreeName,
  refreshModeListEum,
  timeEum,
  deepCopy,
  getPermission,
  changeToYuan,
} from "../common/index";
import moment from "moment";
import Filter from "./filter";
import Information from "./information";
import "./style.scss";
import * as oldApi from "@/utils/api";
import * as api from "@/adator/api";
import { poolOffLine, removePool } from "@/utils/api/ali.store.sku";
import { goldLog, logTimeComponent, track } from "@/utils/aplus";
import OldRules from "./oldRules";
import {
  changeEumToObject,
  dateRender,
  statusRender,
  poolTypeEnum,
  isCatCompEum,
  isRiskCompEum,
  idModeEnum,
  poolEum,
  storeEum,
} from "../common/index";
import { optionMap } from "../common/map";
import { labelMap } from "../common/oldMap";
import { AccessBtn } from "@/components/Button";
import { formatTimeStamp, FORMAT } from "@/utils/time";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { MktConditionsPreview } from "@/components/controls/MktConditions";
import * as qs from "query-string";
import {changeRedirectUrl} from "@/containers/channel/market/common";
import { getFilterFieldPolicy } from "@/selection";

const PAGE_SIZE = 10;

function PoolList({ history }) {
  const [currentName, setCurrentName] = useState("");
  const [currentEmpId, setCurrentEmpId] = useState("");
  const isLoading = false;
  const [queryGroup, setQueryGroup] = useState({});
  const [dataSource, setDataSource] = useState([]);
  const size = PAGE_SIZE;
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [submitDisabled, setSubmitDisabled] = useState(false);
  const [newLabelMap, setNewLabelMap] = useState(deepCopy(labelMap));
  const [otherMap, setOtherMap] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const clipboard = new Clipboard("#copy");
  const columns = [
    {
      title: "",
      lock: "left",
      cell: (value, index, record) => {
        return (
          <em
            className={`icon_${record.poolType == 1 ? "good" : "shop"} small`}
            style={{ margin: "0 5px" }}
          >
            {record.poolType == 1 ? "品" : "店"}
          </em>
        );
      },
      width: "40px",
    },
    {
      title: "名称/ID",
      lock: "left",
      dataIndex: "poolName",
      width: "200px",
      cell: (value, index, record) => {
        let name = value.length > 10 ? `${value.slice(0, 10)}...` : value;
        return (
          <div className="pool-info">
            <Button
              className="pool-name"
              text
              onClick={() => onView(record)}
              title={value}
            >
              {name}
              <div>
                <Icon
                  type="arrow-right"
                  size="xxs"
                  style={{ marginTop: "1px" }}
                />
              </div>
            </Button>
            <p className="pool-id">
              ID：{record.poolId}
              <span
                className="btn-copy"
                id="copy"
                data-clipboard-text={record.poolId}
              >
                复制
              </span>
            </p>
          </div>
        );
      },
    },
    { title: "状态", dataIndex: "state", cell: statusRender, width: "150px" },
    {
      title: "类型",
      dataIndex: "poolType",
      width: "100px",
      cell: (value) => {
        let item = poolTypeEnum.filter((v) => v.value == value);
        const label = item && item.length > 0 ? item[0].label : "-";
        return label;
      },
    },
    {
      title: "圈选方式",
      dataIndex: "createMode",
      width: "100px",
      cell: (value) => {
        return value;
      },
    },
    {
      title: "圈选规则",
      dataIndex: "effectRules",
      width: "140px",
      cell: (value, index, record) => {
        if (record.newPlatformFlag === 1 || record.createMode == "文件上传") {
          return renderFilterRules(value, record);
        } else {
          return (
            <OldRules
              record={record}
              value={value}
              newLabelMap={newLabelMap}
              otherMap={otherMap}
            />
          );
        }
      },
    },
    {
      title: "圈选数量",
      dataIndex: "num",
      width: "100px",
      cell: (value) => {
        return <span className="item-num">{value}</span>;
      },
    },
    {
      title: "最新更新时间",
      dataIndex: "operateDate",
      cell: dateRender,
      width: "170px",
    },
    {
      title: "更新方式",
      dataIndex: "refreshMode",
      width: "170px",
      cell: (value) => {
        return changeEumToObject(refreshModeListEum)[value];
      },
    },
    {
      title: "到期时间",
      dataIndex: "expireAt",
      width: "170px",
      cell: (value, index, record) => {
        return (
          <>
            <div className="expire-at">
              {formatTimeStamp(moment(value), FORMAT.DATE)}
            </div>
            {record.createMode != "文件上传" && record.isNotice == 1 && (
              <div className={`item-notice ok`}>
                <span>到期提醒多人</span>
              </div>
            )}
          </>
        );
      },
    },
    { title: "创建人", dataIndex: "creatorUname", width: "130px" },
    {
      title: "创建时间",
      dataIndex: "createAt",
      cell: dateRender,
      width: "170px",
    },
    // {title: '生效时间', cell: timeRangeRender, width: '180px'},
    { title: "PV(30天)", dataIndex: "pv", width: "100px" },
    { title: "UV(30天)", dataIndex: "uv", width: "100px" },
    {
      title: "操作",
      lock: "right",
      cell: (value, index, record) => {
        return <div className="opbtns">{renderNewOption(record)}</div>;
      },
      width: "200px",
    },
  ];

  useEffect(()=>{
    changeRedirectUrl();
  },[])

  const getQuery = (query) => {
    let newQueryGroup = deepCopy(query);
    setQueryGroup(newQueryGroup);
  };

  const getCurrentName = (name) => {
    setCurrentName(name);
    let newQueryGroup = deepCopy(queryGroup);
    newQueryGroup.creatorUname = name;
    setQueryGroup(newQueryGroup);
  };

  clipboard.on("success", function (e) {
    console.log(e);
    Message.success("复制成功");
  });
  clipboard.on("error", function (e) {
    console.log(e);
  });

  /*获取所在城市数据*/
  const fetchCities = async () => {
    try {
      let request = api.getCities;
      // let resp = await request();
      request().then((resp) => {
        console.log(resp);
        newLabelMap.others.city = resp.data.data;
        setNewLabelMap(newLabelMap);
        otherMap.city = resp.data.data;
        setOtherMap(otherMap);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取商品分类数据*/
  const fetchSkuCategory = async () => {
    try {
      let request = api.getSkuCategory;
      let resp = await request();
      const dataSource = resp.data.map((dataItem) => {
        dataItem.value = dataItem.value.toString();
        dataItem.children &&
          dataItem.children.map((subItem) => {
            subItem.value = subItem.value.toString();
            subItem.children &&
              subItem.children.map((thirdItem) => {
                thirdItem.value = thirdItem.value.toString();
              });
          });
        return dataItem;
      });
      newLabelMap.others.goodsCategory = dataSource;
      setNewLabelMap(newLabelMap);
      otherMap.goodsCategory = resp.data.data;
      setOtherMap(otherMap);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取主营类目数据*/
  const fetchMainCategory = async () => {
    try {
      let request = api.getNewAllStoreMainCategory;
      let resp = await request();
      newLabelMap.others.newStoreType = resp.data;
      setNewLabelMap(newLabelMap);
      otherMap.newStoreType = resp.data.data;
      setOtherMap(otherMap);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*获取新主营类目数据*/
  const fetchNewAllStoreMainCategory = async () => {
    try {
      let request = api.getNewAllStoreMainCategory;
      let resp = await request();
      newLabelMap.others.newMainCategoryId = resp.data;
      setNewLabelMap(newLabelMap);
      otherMap.newMainCategoryId = resp.data.data;
      setOtherMap(otherMap);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*门店类目*/
  const fetchStoreCategory = async () => {
    try {
      let request = api.getCategoryStore;
      let resp = await request();
      newLabelMap.others.storeCategoryIds = resp.data.data;
      setNewLabelMap(newLabelMap);
      otherMap.storeCategoryIds = resp.data.data;
      setOtherMap(otherMap);
    } catch (error) {
      api.onRequestError(error);
    }
  };

  /*商品活动类型*/
  // const fetchMarketingType= async () => {
  //   let {sourcePoolId} = this.state;
  //   try {
  //     let request = api.queryMarketingType;
  //     let resp = await request(sourcePoolId);
  //     this.ctrlGroupOptions("item_activity_types",resp.data.data.data);
  //     this.ctrlGroupOptions("activity_child_type",resp.data.data.data);
  //   } catch (error) {
  //     api.onRequestError(error)
  //   }
  // }

  useEffect(() => {
    fetchCities();
    fetchSkuCategory();
    fetchMainCategory();
    fetchStoreCategory();
    fetchNewAllStoreMainCategory();
    // (async () => {
    //   await fetchCities();
    //   await fetchSkuCategory();
    //   await fetchMainCategory();
    //   await fetchStoreCategory();
    // })()
  }, []);

  // const searchPoolList = () => {
  //   this.getPoolList(this.state.query);
  // }

  const filterRulesTitle = (value, record) => {
    let result;
    let item;
    let sourcePoolName;
    switch (record.createMode) {
      case "智能选品":
        result = <label className="mode-title">场景设定：</label>;
        break;
      case "标签选品":
        item = poolEum.filter((v) => v.id == record.sourcePoolId);
        sourcePoolName = item.length > 0 ? item[0].title : "";
        result = (
          <label className="mode-title">
            底池：{record.sceneName ? record.sceneName : sourcePoolName}
            <br />
            规则：
          </label>
        );
        break;
      case "标签选店":
        item = storeEum.filter((v) => v.id == record.sourcePoolId);
        sourcePoolName = item.length > 0 ? item[0].title : "";
        result = (
          <label className="mode-title">
            底池：{sourcePoolName}
            <br />
            规则：
          </label>
        );
        break;
      case "文件上传":
        result = (
          <label className="mode-title">
            类型：{idModeEnum[record.idMode]}
          </label>
        );
        break;
    }
    return result;
  };

  const renderFilterRules = (value, record) => {
    return (
      <div className="item-rules">
        {filterRulesTitle(value, record)}
        {record.createMode != "文件上传" && (
          <Balloon
            className="item-rules-ballon"
            triggerType="click"
            title=""
            trigger={
              <Button text style={{ textDecoration: "underline" }}>
                {value.length}条
              </Button>
            }
            align="t"
            closable={false}
          >
            {getFieldLabel(value, record)}
          </Balloon>
        )}
      </div>
    );
  };

  const getFieldLabel = (value, record) => {
    let result = "";
    switch (record.createMode) {
      case "文件上传":
        result = "文件上传";
        break;
      case "标签选品":
        result = getLabel(value, record);
        break;
      case "标签选店":
        result = getLabel(value, record);
        break;
      case "智能选品":
        result = getIntelLabel(value);
        break;
      default:
        break;
    }
    return result;
  };

  const getLabel = (value, record) => {
    return value.map((v) => {
      let keyEum = optionMap[v.filterFieldKey];
      return (
        <>
          <div>
            <strong>{v.filterFieldLabel}</strong>
          </div>
          {/*<div>{(v.filterFieldKey != 'item_pic_standard') ? changeEumToObject(keyEum)[v.filterFieldValue] : v.filterFieldValue}</div>*/}
          <div>{getLabelItem(v)}</div>
        </>
      );
    });
  };

  const getLabelItem = (item) => {
    try {
      const filterFieldComponentType = item.filterFieldComponentType;
      let result;
      if (item.filterFieldValue || item.filterFieldValue == "") {
        let options = optionMap[item.filterFieldKey];
        switch (filterFieldComponentType) {
          case "arrayInput": //文本框
            // 兼容发错值的场景
            result = (
              <span>
                {JSON.parse(item.filterFieldValue) instanceof Array
                  ? JSON.parse(item.filterFieldValue).join(",")
                  : JSON.parse(item.filterFieldValue)}
              </span>
            );
            break;
          case "radio": //单选框
          case "select": //单选下拉框
            result = (
              <span>{changeEumToObject(options)[item.filterFieldValue]}</span>
            );
            break;
          case "cascaderSelect": //下拉级联多选
          case "multipleSelect": //下拉多选
          case "picStandard": //质量分
          case "selectSearch": //带搜索的下拉多选
            result = <span>{getCascaderContent(item)}</span>;
            break;
          case "rangeNumberInput": //店铺评分类
            const valueRange = changeToYuan(item);
            result = (
              <span>
                {valueRange.start}~{valueRange.end}
              </span>
            );
            break;
          case "checkbox": //复选框
            const value = item.filterFieldValue
              ? JSON.parse(item.filterFieldValue)
                  .map((m) => changeEumToObject(options)[m])
                  .join(",")
              : "";
            result = <span>{value}</span>;
            break;
          case "mktConditions":
            result = (
              <MktConditionsPreview
                value={item.filterFieldValue}
                style={{
                  fontSize: 14,
                  color: "#999",
                  marginBottom: 5,
                }}
                dataSource={options}
              />
            );
            break;
          default:
            const policy = getFilterFieldPolicy(filterFieldComponentType);
            if (policy) {
              result = policy.renderPreview(
                {
                  value: item.filterFieldValue,
                  dataSource: options,
                },
                "listPage",
              );
            } else {
              result = <span>{item.filterFieldValue}</span>;
            }
            break;
        }
      } else {
        result = <span></span>;
      }
      return result;
    } catch (e) {
      console.error(e);
      return <></>;
    }
  };

  const getCascaderContent = (item) => {
    let result = "";
    if (item && item.filterFieldValue) {
      let values = JSON.parse(item.filterFieldValue);
      if (item.filterFieldComponentType == "picStandard") {
        values = dealQualityScoreToArray(JSON.parse(item.filterFieldValue));
      }
      if (values && values.length > 0) {
        result = values
          .map((v) => {
            let labelValue = v.label;
            if (item.filterFieldComponentType == "picStandard") {
              labelValue = getTreeName(optionMap["item_pic_standard"], v.value);
            }
            return labelValue;
          })
          .join(",");
      }
    }
    return result;
  };

  const getIntelLabel = (value) => {
    return (
      <div>
        {value.map((v) => {
          let filterFieldValueGroup = v.filterFieldValue;
          if (filterFieldValueGroup == "[]") return "";
          if (v.filterFieldKey == "timePeriod") {
            filterFieldValueGroup = JSON.parse(v.filterFieldValue)
              .map((m) => changeEumToObject(timeEum)[m])
              .join(",");
          } else if (v.filterFieldKey == "crowd") {
            filterFieldValueGroup = JSON.parse(v.filterFieldValue)
              .map((m) => changeEumToObject(crowdEum)[m])
              .join(",");
          } else if (v.filterFieldKey == "isCatComp") {
            filterFieldValueGroup =
              changeEumToObject(isCatCompEum)[v.filterFieldValue];
            // filterFieldValueGroup = changeEumToObject(isCatCompEum)[JSON.parse(v.filterFieldValue)];
          } else if (v.filterFieldKey == "isRiskComp") {
            filterFieldValueGroup =
              changeEumToObject(isRiskCompEum)[v.filterFieldValue];
          } else if (
            v.filterFieldKey == "goodsCategory" ||
            v.filterFieldKey == "newMainCategoryId"
          ) {
            filterFieldValueGroup = JSON.parse(v.filterFieldValue)
              .map((m) => m.label)
              .join(",");
          } else if (
            v.filterFieldKey == "item_name" ||
            v.filterFieldKey == "filter_item_name"
          ) {
            filterFieldValueGroup = JSON.parse(v.filterFieldValue).join(",");
          }
          return (
            <p>
              <label>{v.filterFieldLabel}：</label>
              <span>{filterFieldValueGroup}</span>
            </p>
          );
        })}
      </div>
    );
  };

  const renderNewOption = (record) => {
    let { state, createMode = "" } = record;
    let divider = <Divider direction="ver" />;
    let detailEle = (
      <Button text={true} onClick={() => onView(record)}>
        查看
      </Button>
    );
    let editEle = (
      <AccessBtn
        // 百亿补贴相关底池已经下线，这类底池禁止编辑
        disabled={["41001", "24005"].includes(`${record.sourcePoolId}`)}
        getPermission={() => getPermission(record.poolId)}
        btnText={"编辑"}
        callback={() => onEdit(record)}
      />
    );
    let copyEle = (
      <AccessBtn
        getPermission={() => getPermission(record.poolId)}
        btnText={"复制"}
        callback={() => onCopy(record)}
      />
    );
    let publishEle = (
      <AccessBtn
        getPermission={() => getPermission(record.poolId)}
        disabled={submitDisabled}
        btnText={"发布"}
        callback={() => onPublish(record)}
      />
    );
    let rePublishEle = (
      <AccessBtn
        getPermission={() => getPermission(record.poolId)}
        disabled={submitDisabled}
        btnText={"重新发布"}
        callback={() => onPublish(record)}
      />
    );
    let deleteEle = (
      <AccessBtn
        getPermission={() => getPermission(record.poolId)}
        btnText={"删除"}
        callback={() => onRemove(record)}
      />
    );
    let offLineEle = (
      <AccessBtn
        getPermission={() => getPermission(record.poolId)}
        btnText={"下线"}
        callback={() => onOffLine(record)}
      />
    );
    let analyseEle = (
      <Button text={true} onClick={() => onAnalyse(record)}>
        洞察
      </Button>
    );
    const selectPoolAnalysisEle = record.resultReportUrl ? (
      <Button text={true} onClick={() => window.open(record.resultReportUrl)}>
        选品分析
      </Button>
    ) : undefined;
    let usageInformation = (
      <AccessBtn
        getPermission={() => getPermission(record.poolId)}
        btnText={"使用场景(试用)"}
        callback={() => showUsageInformation(record)}
      />
    );

    let morePanel;
    let options;
    switch (state) {
      case 0 /*未发布：查看、发布、更多：编辑、复制、删除*/:
        morePanel = (
          <Dropdown
            trigger={
              <span>
                更多&nbsp;
                <span>
                  <Icon type="arrow-down" size="xxs" />
                </span>
              </span>
            }
            triggerType={["click"]}
            cache
          >
            <Menu>
              <Menu.Item>
                <div className="menu-item">{editEle}</div>
              </Menu.Item>
              {["标签选店", "标签选品"].includes(createMode) && (
                <Menu.Item>
                  <div className="menu-item">{copyEle}</div>
                </Menu.Item>
              )}
              <Menu.Item>
                <div className="menu-item">{deleteEle}</div>
              </Menu.Item>
              <Menu.Item>
                <div className="menu-item">{usageInformation}</div>
              </Menu.Item>
            </Menu>
          </Dropdown>
        );
        options = (
          <>
            {detailEle}
            {divider}
            {publishEle}
            {divider}
            {morePanel}
          </>
        );
        break;
      case 5 /*发布中：查看，更多：复制、删除*/:
        morePanel = (
          <Dropdown
            trigger={
              <span>
                更多&nbsp;
                <Icon type="arrow-down" size="xxs" />
              </span>
            }
            triggerType={["hover"]}
            cache
          >
            <Menu>
              {record.createMode != "文件上传" &&
                ["标签选店", "标签选品"].includes(createMode) && (
                  <Menu.Item>
                    <div className="menu-item">{copyEle}</div>
                  </Menu.Item>
                )}
              <Menu.Item>
                <div className="menu-item">{deleteEle}</div>
              </Menu.Item>
              <Menu.Item>
                <div className="menu-item">{usageInformation}</div>
              </Menu.Item>
            </Menu>
          </Dropdown>
        );
        options = (
          <>
            {detailEle}
            {divider}
            {morePanel}
          </>
        );
        break;
      case 15 /*已发布：查看、洞察、更多：编辑、复制、下线*/:
        morePanel = (
          <Dropdown
            trigger={
              <span>
                更多&nbsp;
                <Icon type="arrow-down" size="xxs" />
              </span>
            }
            triggerType={["hover"]}
            cache
          >
            <Menu>
              <Menu.Item>
                <div className="menu-item">{editEle}</div>
              </Menu.Item>
              {record.createMode != "文件上传" &&
                ["标签选店", "标签选品"].includes(createMode) && (
                  <Menu.Item>
                    <div className="menu-item">{copyEle}</div>
                  </Menu.Item>
                )}
              <Menu.Item>
                <div className="menu-item">{offLineEle}</div>
              </Menu.Item>
              {/* <Menu.Item>{putInEle}</Menu.Item> */}
              <Menu.Item>
                <div className="menu-item">{usageInformation}</div>
              </Menu.Item>
              {/* <Menu.Item>{exportExcel}</Menu.Item> */}
            </Menu>
          </Dropdown>
        );
        options = (
          <>
            {detailEle}
            {divider}
            {/* {showBtnAnalyseAll && generateInsight} */}
            {/* {showBtnAnalyseAll && divider} */}
            {selectPoolAnalysisEle && selectPoolAnalysisEle}
            {selectPoolAnalysisEle && divider}
            {morePanel}
          </>
        );
        break;
      case 1 /*需要发布：查看、发布、更多：编辑、洞察、复制、下线*/:
        morePanel = (
          <Dropdown
            trigger={
              <span>
                更多&nbsp;
                <Icon type="arrow-down" size="xxs" />
              </span>
            }
            triggerType={["hover"]}
            cache
          >
            <Menu>
              <Menu.Item>
                <div className="menu-item">{editEle}</div>
              </Menu.Item>
              {/* {showBtnAnalyseAll && (
                <Menu.Item>{generateInsight}</Menu.Item>
              )} */}
              {["标签选店", "标签选品"].includes(createMode) && (
                <Menu.Item>
                  <div className="menu-item">{copyEle}</div>
                </Menu.Item>
              )}
              <Menu.Item>
                <div className="menu-item">{offLineEle}</div>
              </Menu.Item>
              <Menu.Item>
                <div className="menu-item">{usageInformation}</div>
              </Menu.Item>
            </Menu>
          </Dropdown>
        );
        options = (
          <>
            {detailEle}
            {divider}
            {rePublishEle}
            {divider}
            {morePanel}
          </>
        );
        break;
      case 20 /*发布失败：查看、发布、更多：编辑、复制、删除*/:
        morePanel = (
          <Dropdown
            trigger={
              <span>
                更多&nbsp;
                <Icon type="arrow-down" size="xxs" />
              </span>
            }
            triggerType={["hover"]}
            cache
          >
            <Menu>
              <Menu.Item>
                <div className="menu-item">{editEle}</div>
              </Menu.Item>
              {record.createMode != "文件上传" &&
                ["标签选店", "标签选品"].includes(createMode) && (
                  <Menu.Item>
                    <div className="menu-item">{copyEle}</div>
                  </Menu.Item>
                )}
              <Menu.Item>
                <div className="menu-item">{deleteEle}</div>
              </Menu.Item>
              <Menu.Item>
                <div className="menu-item">{usageInformation}</div>
              </Menu.Item>
            </Menu>
          </Dropdown>
        );
        options = (
          <>
            {detailEle}
            {divider}
            {rePublishEle}
            {divider}
            {morePanel}
          </>
        );
        break;
      case 25 /*已下线：查看、洞察、更多：复制、删除*/:
        morePanel = (
          <Dropdown
            trigger={
              <span>
                更多&nbsp;
                <Icon type="arrow-down" size="xxs" />
              </span>
            }
            triggerType={["hover"]}
            cache
            key={record.poolId + "" + record.state}
          >
            <Menu>
              {record.createMode != "文件上传" &&
                ["标签选店", "标签选品"].includes(createMode) && (
                  <Menu.Item>
                    <div className="menu-item">{copyEle}</div>
                  </Menu.Item>
                )}
              <Menu.Item>
                <div className="menu-item">{deleteEle}</div>
              </Menu.Item>
              <Menu.Item>
                <div className="menu-item">{usageInformation}</div>
              </Menu.Item>
            </Menu>
          </Dropdown>
        );
        options = (
          <>
            {detailEle}
            {divider}
            {/* {showBtnAnalyseAll && generateInsight} */}
            {/* {showBtnAnalyseAll && divider} */}
            {selectPoolAnalysisEle && selectPoolAnalysisEle}
            {selectPoolAnalysisEle && divider}
            {morePanel}
          </>
        );
        break;
      default:
        break;
    }
    return options;
  };

  const showUsageInformation = (record) => {
    Dialog.show({
      v2: true,
      content: (
        <div className="pool-information">
          <Information poolId={record.poolId}></Information>
        </div>
      ),
      closeable: "esc,mask",
      footerActions: ["cancel"],
      style: {
        width: "100%",
        height: "100%",
      },
      isFullScreen: true,
    });
  };

  const onOffLine = (record) => {
    let { newPlatformFlag, sourcePoolId, poolId, poolName, state } = record;
    Dialog.confirm({
      title: "提示",
      content: "确定下线此条数据么?",
      onOk: async () => {
        if (newPlatformFlag == 1) {
          // 新池子
          let request =
            record.createMode != "文件上传" ? api.offLinePool : poolOffLine;
          try {
            await request(record.poolId);
            Message.success("操作成功");
            getPoolList(1, queryGroup);
          } catch (error) {
            api.onRequestError(error);
          }
        } else {
          let request = oldApi.ali.poolOffLine;
          request(record.poolId)
            .then(api.onRequestSuccess)
            .then(() => {
              Message.success("操作成功");
              getPoolList(1, queryGroup);
            })
            .catch(api.onRequestError);
        }
      },
    });
  };

  const onView = (record, showBtnAnalyse) => {
    let { newPlatformFlag, sourcePoolId, poolId, poolName, state, poolType } =
      record;
    if (newPlatformFlag == 1) {
      // 新池子
      if (record.createMode != "文件上传") {
        /*文件上传暂无详情页*/
        // window.open("https://selection.kunlun.alibaba-inc.com/#/"+`/pool/list/detail/${record.poolId}?showBtnAnalyse=${showBtnAnalyse ? 1 : 2}&isSelectedStore=${poolType == 2 ? true : false}`);
        history.push(
          `/pool/list/detail/${record.poolId}?showBtnAnalyse=${
            showBtnAnalyse ? 1 : 2
          }&isSelectedStore=${poolType == 2 ? true : false}`
        );
      } else {
        track("clickEvent", [
          "/selection_kunlun.MANAGE-POOL-pool-list.view-pool-btn",
          `poolId=${record.poolId}`,
        ]);
        let query = {
          basePoolId: record.sourcePoolId,
          name: record.poolName,
          canModify: record.canModify,
          poolState: record.state,
          dataType: 1,
          createMode: record.createMode,
        };
        let path = `/${
          poolType == 2 ? "storepool" : "commoditypool"
        }/list/detail/${record.poolId}?${qs.stringify(query)}`;

        history.push(path);
      }
    } else {
      // 老池子
      // /#/commoditypool/list/detail/122484?basePoolId=20001&createMode=CREATE_RULE&dataType=1&name=222&poolState=0
      history.push(
        `/${
          poolType == 2 ? "storepool" : "commoditypool"
        }/list/detail/${poolId}?basePoolId=${sourcePoolId}&createMode=CREATE_RULE&dataType=1&name=${poolName}&poolState=${state}`
      );
    }
  };

  const onEdit = (record) => {
    let { newPlatformFlag, sourcePoolId, poolId, poolName, state } = record;
    if (newPlatformFlag == 1) {
      // 新池子
      switch (record.createMode) {
        case "文件上传":
          history.push(
            `/pool/list/fileUpload/${record.poolId}?sourcePoolId=${sourcePoolId}`
          );
          break;
        case "标签选品":
          history.push(`/pool/list/tagPool/${record.poolId}`);
          break;
        case "标签选店":
          history.push(`/pool/list/selectedStore/${record.poolId}`);
          break;
        case "智能选品":
          history.push(`/pool/list/intelligentPool/${record.poolId}`);
          break;
        default:
          break;
      }
    } else {
      let dataType = parseInt(sourcePoolId) >= 20001 ? 1 : 2;
      let recordType = record.poolType == 2 ? "storepool" : "commoditypool";
      history.push(
        `/${recordType}/creation/query/${poolId}?basePoolId=${sourcePoolId}&name=${poolName}&poolState=${state}&dataType=${dataType}`
      );
    }
  };

  const onCopy = async (record) => {
    let { newPlatformFlag, sourcePoolId, poolId, poolName, state, createMode } =
      record;
    if (newPlatformFlag == 1) {
      // 新池子
      if (createMode === "标签选店") {
        history.push(`/pool/list/selectedStore/-${record.poolId}`);
      } else {
        history.push(`/pool/list/tagPool/-${record.poolId}`);
      }
    } else {
      let dataType = parseInt(sourcePoolId) >= 20001 ? 1 : 2;
      // let request = oldApi.ali.getPoolDetail;
      // try {
      //   await request({id:poolId})
      //   Message.success('操作成功')
      let recordType = record.poolType == 2 ? "storepool" : "commoditypool";
      history.push(
        `/${recordType}/creation/query/${poolId}?basePoolId=${sourcePoolId}&basePoolName=${poolName}&dataType=${dataType}&isCopy=true`
      );
      // } catch (error) {
      //   api.onRequestError(error)
      // }
    }
  };

  const onPublish = async (record) => {
    let { newPlatformFlag, sourcePoolId, poolId, poolName, state } = record;
    if (newPlatformFlag == 1) {
      setSubmitDisabled(true);
      let request = api.rePublishPool;
      try {
        await request(record.poolId);
        Message.success("操作成功");
        getPoolList(1, queryGroup);
        setSubmitDisabled(false);
      } catch (error) {
        setSubmitDisabled(false);
        api.onRequestError(error);
      }
    } else {
      let dataType = parseInt(sourcePoolId) >= 20001 ? 1 : 2;
      let request = oldApi.ali.publishPool;
      await request({ isStore: false, id: poolId });
      let recordType = record.poolType == 2 ? "storepool" : "commoditypool";
      history.push(
        `/${recordType}/list/progress/${poolId}?basePoolId=${sourcePoolId}&dataType=${dataType}&name=${poolName}`
      );
    }
  };

  const onAnalyse = (record) => {
    history.push(`/pool/list/analyse/${record.poolId}`);
  };

  const onRemove = (record) => {
    let { newPlatformFlag } = record;
    Dialog.confirm({
      title: "提示",
      content: "确定删除此条数据么?",
      onOk: async () => {
        if (newPlatformFlag == 1) {
          let request =
            record.createMode != "文件上传" ? api.deletePool : removePool;
          try {
            await request(record.poolId);
            Message.success("操作成功");
            getPoolList(1, queryGroup);
          } catch (error) {
            api.onRequestError(error);
          }
        } else {
          let request = oldApi.ali.removePool;
          try {
            await request(record.poolId).then(api.onRequestSuccess);
            Message.success("操作成功");
            getPoolList(1, queryGroup);
          } catch (error) {
            api.onRequestError(error);
          }
        }
      },
    });
  };

  useEffect(() => {
    // getPoolList(queryGroup);
  }, []);

  /**
   * 池子查询列表
   **/
  const getPoolList = (page = 1, query) => {
    // 补充参数
    try {
      let request = api.getPoolList;
      let param = {
        rangeType: 0,
        // newPlatformFlag: 1,
        ...query,
      };
      request({
        pageSize: size,
        pageIndex: page,
        ...param,
      }).then((resp) => {
        setDataSource(resp.data);
        setTotal(resp.totalCount);
        setPage(page);
        setSelectedRowKeys([]);
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  const onPageChange = (page) => {
    getPoolList(page, queryGroup);
  };

  const batchOfflinelPoolList = () => {
    Dialog.confirm({
      title: "提示",
      content: "确定批量下线这些数据么?",
      onOk: async () => {
        try {
          let request = api.batchOffLinePool;
          let param = {
            poolIdList: selectedRowKeys,
          };
          request(param).then((resp) => {
            if (resp.success) {
              Message.success("批量下线成功");
              getPoolList(1, queryGroup);
            } else {
              Message.error(resp.errMessage);
            }
          });
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  return (
    <div className="pool-list">
      <Filter
        getPoolList={getPoolList}
        history={history}
        getQuery={getQuery}
        getCurrentName={getCurrentName}
        getCurrentEmpId={setCurrentEmpId}
        batchOfflinelPoolList={batchOfflinelPoolList}
      />
      <div className="table-panel">
        <Table
          dataSource={dataSource}
          loading={isLoading}
          hasBorder={false}
          primaryKey="poolId"
          rowSelection={{
            onChange: (selectedRowKeysProps, records) => {
              setSelectedRowKeys(selectedRowKeysProps);
            },
            selectedRowKeys: selectedRowKeys,
            getProps: (record) => {
              return {
                disabled: [15, 1].includes(record.state) ? false : true,
              };
            },
          }}
        >
          {columns.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
        <Pagination
          onChange={onPageChange}
          total={total}
          current={page}
          pageSize={size}
          pageSizeSelector="dropdown"
          pageSizeList={[10, 20]}
          popupProps={{ align: "bl tl" }}
          style={{ float: "right", marginTop: "10px" }}
        />
      </div>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(PoolList),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const poolListPage = permissionAccess(LogTimePutInPage);
