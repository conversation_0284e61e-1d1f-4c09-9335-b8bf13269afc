import React, {Fragment, useEffect, useState} from 'react';
import {Button, Dialog, Grid} from '@alife/next';
import './style.scss';
import {BreadcrumbTips, CompletePage} from "@/home/<USER>/comps";
import {PageWrapper} from "@/comps/PageWrapper";
import {PoolPageBase, Steps} from "@/home/<USER>/common";
import {BaseInfo} from "@/home/<USER>/intelligentPool/baseInfo";
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {withRouter} from "react-router-dom";
import {permissionAccess} from "@/components/PermissionAccess";
import SelectMethod from "@/home/<USER>/poolList/selectMethod";

const {Row, Col} = Grid;
/*
* 选圈选方式
* */

const DEFAULT_Label_IMG = require('../../../images/icon-label.png');
const DEFAULT_Intelligent_IMG = require('../../../images/icon-intelligent.png');
const DEFAULT_Upload_IMG = require('../../../images/icon-upload.png');

function poolCreate(props) {
  const {location, match, history} = props;
  const breadcrumbList = match.params.poolType == 1? [
    {"title": '选品集管理', link: "#/pool/list"},
    {"title": '新建商品选品集', link: ""}
  ]:[
    {"title": '选品集管理', link: "#/pool/list"},
    {"title": '新建门店选品集', link: ""}
  ];
  return (
    <div className="pool-create">
      <BreadcrumbTips list={breadcrumbList}/>
      <Steps current={0} middleText={'基本信息'} />
      <div className='list-create'>
        <h3>1. 选择圈选方式</h3>
        <SelectMethod history={history} poolType={match.params.poolType}/>
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(poolCreate), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const poolCreatePage = permissionAccess(LogTimePutInPage)

