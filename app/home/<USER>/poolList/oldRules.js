import React, {Fragment, useEffect, useState} from 'react';
import {
  Button,
  Balloon
} from '@alife/next';
import {isArray, isString, isObject, isAllNumber} from '@/utils/validators';
import {dealQualityScoreToArray, deepCopy, getTreeName} from "@/home/<USER>/common";
import {labelMap} from "@/home/<USER>/common/oldMap";
import * as api from "@/adator/api";
import {optionMap} from "@/home/<USER>/common/map";

export default function OldRules(props) {
  const {record, value, newLabelMap,otherMap} = props;
  // const [newLabelMap, setNewLabelMap] = useState(deepCopy(labelMap));
  const [rulesEles, setRulesEles] = useState([]);
  const clientH = document.documentElement.clientHeight - 100;

  const groupActivityIdsTypesEnum = {
    'marketing': '营销活动ID',
    'brand': '品牌活动ID',
    'invite': '招商活动ID',
    'mktType': '营销活动类型'
  }

  const renderOldFilterRules = () => {
    if(record && record.ext) {
      let ext = {};
      let extGroup = JSON.parse(record.ext);
      for (let o in extGroup) {
        if (o != 'basePoolId') { //剔掉不需要展示的
          ext = {
            ...extGroup[o],
            ...ext
          }
        }
      }
      for (const o in ext) {
        let value = ext[o];
        let valueText = '';
        let labelText = '';
        let specialGroup = ['goodsType','isCatCompStandardStr',"goodsType"]; //有恶心的空串作为枚举值的字段
        let multipeGroup = ['city','goodsCategory','newMainCategoryId'];  //需要调接口的
        if (value != "" || specialGroup.includes(o)) {
          if (multipeGroup.includes(o) && (newLabelMap.others[o] && newLabelMap.others[o].length > 0)) { //城市、商品分类
            valueText = value.map((v) => {
              // 店铺的门店新增功能需要调用接口查询，数据结构与city不同，需要单独判断
              let labelValue
              if(o == 'newMainCategoryId'){
                labelValue = getTreeName(newLabelMap.others[o], v.toString());
              }else{
                labelValue = getTreeName(newLabelMap.others[o], v.value.toString());
              }
              return labelValue;
            }).join(",");
          } else if ((o == 'newStoreType' || o == 'storeCategoryIds') && (newLabelMap.others[o] && newLabelMap.others[o].length > 0)) {  //主营类目
            valueText = value.map((v) => {
              let labelValue = getTreeName(newLabelMap.others[o], v.toString());
              return labelValue;
            }).join(",");
          } else if ((o == 'storeCategoryId') && (newLabelMap.others["storeCategoryIds"] && newLabelMap.others["storeCategoryIds"].length > 0)) {  //主营类目 ~ 单独拎出来解决字段不统一问题
            valueText = value.map((v) => {
              let labelValue = getTreeName(newLabelMap.others["storeCategoryIds"], v.toString());
              return labelValue;
            }).join(",");
          }else if(o=='itemPicQualityScore'){
            let valueArray = dealQualityScoreToArray(value);
            valueText = valueArray.map((v) => {
              let labelValue = getTreeName(optionMap['item_pic_standard'], v.value);
              return labelValue;
            }).join(",");
          } else if (o == 'activityIdsTypes') { //复合框
            labelText = groupActivityIdsTypesEnum[value];
            valueText =  ext['activityIdsTypesValue'];
          } else {
            if (isString(value) || isAllNumber(value) || value == '') { //输入框类
              valueText = newLabelMap.others[o] ? newLabelMap.others[o][value] : value;
            }
            if (isArray(value) && value.length > 0) { //多选框  id类
              valueText = value.map((v) => {
                if (newLabelMap.others[o] && isObject(newLabelMap.others[o])) {
                  return newLabelMap.others[o][v]
                } else {
                  return v;
                }
              }).join(",");
            }
            if (JSON.stringify(value) == "{}") {
              valueText = '-';
            }
            if (value.hasOwnProperty('start') || value.hasOwnProperty('end')) {  //价格类
              valueText = `${value.start ? value.start : ''}~${value.end ? value.end : ''}`;
            }
          }
          if (o != 'activityIdsTypesValue') {
            rulesEles.push(
              <p key={labelText == "" ? newLabelMap.sku[o] : labelText}>
                <label>{labelText == "" ? newLabelMap.sku[o] : labelText}</label>
                <span>{valueText}</span>
              </p>
            )
            setRulesEles(rulesEles);
          }
        }
      }
    }
  }


  useEffect(() => { //
      renderOldFilterRules();
  }, [])

  return (
    <div className="item-rules">
      老池子
      <Balloon
        className="item-rules-ballon"
        triggerType="click"
        title=""
        trigger={<Button text style={{textDecoration: 'underline'}}>{rulesEles.length}条</Button>}
        align="l"
        closable={false}
      >
        <div style={{maxHeight: `${clientH}px`, overflowY: 'auto'}}>
          {rulesEles}
        </div>
      </Balloon>
    </div>
  )
}
