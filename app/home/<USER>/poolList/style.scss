.pool-list{
  margin:12px;
  .pool-name{
    cursor: pointer;
  }
  .filter{
    background-color: #fff;
    .top{
      overflow: hidden;
    }
    .next-row{
      margin-right: 20px;
    }
    .pool-title{
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #363636;
      line-height: 20px;
      margin:20px;
      display: inline-block;
    }
    .btn-create-pool{
      float: right;
      margin:20px 20px 20px 0;
    }
    .next-btn{
      margin-left:10px;
    }
  }
  .table-panel{
    background-color: #fff;
    padding:20px;
    overflow: hidden;
    .next-table{
      .poolId{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
      }
      th .next-table-cell-wrapper{
        padding:10px 12px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #666666;
      }
      td .next-table-cell-wrapper{
        padding:8px 5px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #666666;
        overflow: auto;
      }
      .item-num{
        text-align: right;
        display: block;
        margin-right:10px;
      }
    }
  }
  .opbtns{
    margin: 0 5px;
    &>span{
      position: relative;
      top: 1px;
      cursor: pointer;
    }
    .next-btn{
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333333;

      &:hover {
        color: #ff7000
      }
      //border-right:1px solid #EBEBEB;
    }
  }
  .pool-info{
    .pool-name{
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333333;
      line-height: 20px;

      &:hover {
        color: #FF7000;
        text-decoration: underline;
      }
    }
    p{
      margin:0;
      line-height:20px;
      padding:0;
      &.pool-id{
        font-family: PingFangSC-Regular !important;
        font-size: 14px;
        color: #666666;
        line-height: 20px;
        .btn-copy{
          cursor: pointer;
          font-family: PingFangSC-Regular;
          font-size: 12px;
          color: #999999;
          margin-left: 4px;
        }
      }
    }
  }
  .item-notice{
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #999999;
    &.no{
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #FF7000;
    }
  }

}
.select-type{
  .next-row{
    .next-col{
      background: #FFFFFF;
      border: 1px solid #EBEBEB;
      border-radius: 4px;
      margin-right:5px;
      height: 76px;
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #333333;
      padding:24px 0 0 24px;
      cursor: pointer;;

      &:hover {
        box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
      }
      &.active{
        background: rgba(255,112,0,0.04);
        border: 1px solid rgba(255,112,0,0.40);
      }
    }
  }

}

.pool-create{
  .step-wraper{
    margin-bottom: 20px;
    background-color: #fff;
    padding: 20px 0 5px;
    .next-step-item-title{
      margin-top: 0 !important;
    }
  }

  & > .list-create {
    background-color: #fff;
    margin: 12px;
    padding: 20px;

    & > h3 {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      font-weight: normal;
      margin: 0;
      padding: 0;
      cursor: pointer;

      & > .next-btn {
        cursor: pointer;
      }

      //&>span{
      //  font-family: PingFangSC-Regular;
      //  font-size: 14px;
      //  color: #999999;
      //  margin-left:8px;
      //}
    }

    //.order-num{
    //  width:16px;
    //  height:16px;
    //  line-height:16px;
    //  text-align:center;
    //  display:inline-block;
    //  border: 1px solid #FF7000;
    //  font-family: AlibabaSans102-Bold;
    //  font-size: 14px;
    //  font-style:normal;
    //  border-radius: 50%;
    //  margin-right:8px;
    //  background-color: #FF7000;
    //  color: #fff;
    //  &.current{
    //    background-color: #FFFFFF;
    //    color: #FF7000;
    //  }
    //}
    .arrow-fold {
      margin-left: 12px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
    }

    .selection-method {
      width: 480px;
      margin: 14px 0 0 24px;

      .next-col {
        margin-right: 12px;
        border: 1px solid #EBEBEB;
        background-color: #FFFFFF;
        border-radius: 4px;
        text-align: center;
        cursor: pointer;
        width: 50%;

        .icon {
          display: inline-block;
          margin-top: 20px;

          &.icon-upload {
            height: 36px;
            //width: 40px;
            background-size: 100% 100%;
          }

          &.icon-intelligent {
            height: 36px;
            //width: 40px;
            background-size: 100% 100%;
          }

          &.icon-label {
            height: 35px;
            background-size: 100% 100%;
          }
        }

        .title {
          font-family: PingFangSC-Medium;
          font-size: 16px;
          color: #333333;
          margin-top: 0;
          padding: 0;
          margin-bottom: 4px;
        }

        .tips {
          font-family: PingFangSC-Regular !important;
          font-size: 12px;
          color: #999999;
          width: 120px;
          margin: 0 auto;
          margin-bottom: 12px;

          .count {
            color: #4988FD;
          }
        }

        &:hover, &.active {
          background: rgba(255, 112, 0, 0.04);
          border: 1px solid rgba(255, 112, 0, 0.40);
        }
      }
    }
  }
  //&>h3{
  //  font-family: PingFangSC-Medium;
  //  font-size: 16px;
  //  color: #666666;
  //  padding:24px 0 0 0px;
  //  font-weight:normal;
  //  margin-bottom: 24px;
  //  &>span{
  //    font-family: PingFangSC-Regular;
  //    font-size: 14px;
  //    color: #999999;
  //    margin-left:8px;
  //  }
  //}
  //.order-num{
  //  width:16px;
  //  height:16px;
  //  line-height:16px;
  //  text-align:center;
  //  display:inline-block;
  //  border: 1px solid #FF7000;
  //  font-family: AlibabaSans102-Bold;
  //  font-size: 14px;
  //  font-style:normal;
  //  border-radius: 50%;
  //  margin-right:8px;
  //  background-color: #FF7000;
  //  color: #fff;
  //  &.current{
  //    background-color: #FFFFFF;
  //    color: #FF7000;
  //  }
  //}
  //.selection-method{
  //  width:480px;
  //  margin:24px 0 0 24px;
  //  .next-col{
  //    margin-right:12px;
  //    border: 1px solid #EBEBEB;
  //    background-color: #FFFFFF;
  //    border-radius: 4px;
  //    text-align: center;
  //    cursor: pointer;
  //    width:50%;
  //    .icon{
  //      display: inline-block;
  //      margin-top:20px;
  //      &.icon-upload{
  //        height: 36px;
  //        //width: 40px;
  //        background-size:100% 100%;
  //      }
  //      &.icon-intelligent{
  //        height: 36px;
  //        //width: 40px;
  //        background-size:100% 100%;
  //      }
  //      &.icon-label{
  //        height: 35px;
  //        background-size:100% 100%;
  //      }
  //    }
  //    .title{
  //      font-family: PingFangSC-Medium;
  //      font-size: 16px;
  //      color: #333333;
  //      margin-top: 0;
  //    }
  //    .tips{
  //      font-family: PingFangSC-Regular !important;
  //      font-size: 12px;
  //      color: #999999;
  //      width:120px;
  //      margin:0 auto;
  //      margin-bottom: 12px;
  //    }
  //    &:hover{
  //      background: rgba(255,112,0,0.04);
  //      border: 1px solid rgba(255,112,0,0.40);
  //    }
  //  }
  //}
}

.icon_shop{
  height: 28px;
  width: 28px;
  border-radius: 14px;
  background: #0CC1C1;
  font-family: PingFangSC-Semibold;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  display: inline-block;
  margin-right: 8px;
  &.small{
    width:20px;
    height:20px;
    line-height:20px;
    font-size: 12px;
  }
}
.icon_good{
  height: 28px;
  width: 28px;
  border-radius: 14px;
  background-color: #4494F9;
  font-family: PingFangSC-Semibold;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 28px;
  text-align: center;
  font-style: normal;
  display: inline-block;
  margin:0 5px;
  &.small{
    width:20px;
    height:20px;
    line-height:20px;
    font-size: 12px;
  }
}
.item-rules{
  .mode-title,.next-btn-text{
    font-family: PingFangSC-Regular !important;
    font-size: 14px;
    color: #666666;
  }
}
.item-rules-ballon{
  strong{
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: #333333;
  }
  span{
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #999999;
    margin-bottom: 5px;
    display: block;
  }
}
.pool-information{
  background-color: #fff;
  overflow: hidden;
  .pool-information-content{
    min-height: 600px;
    max-height: 800px;
    overflow: scroll;
  }
  .pool-information-content >.next-table{
    margin-top: 20px;
    th .next-table-cell-wrapper{
      padding:11px 16px;
    }
    td .next-table-cell-wrapper{
      font-family:  PingFangSC;
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      padding:9px 16px;
    }
    td.next-table-prerow .next-table-cell-wrapper{
      padding:10px;
      .next-icon{
        &.next-table-expand-unfold {
          //background-color:red;
          background:transparent url("https://img.alicdn.com/imgextra/i2/O1CN01X8yOoS1ODMZCelFZ5_!!6000000001671-2-tps-12-12.png") no-repeat left center;

          &:before {
            content: '';
          }
        }
        &.next-table-expand-fold{
          background:transparent url("https://img.alicdn.com/imgextra/i1/O1CN01vci7DQ228rIqf8INS_!!6000000007076-2-tps-12-12.png") no-repeat left center;

          &:before {
            content: '';
          }
        }
      }
    }
  }
  .open{
    height: 5px;
    width: 5px;
    display: inline-block;
    background-color: #00B365;
    border-radius: 50%;
    margin-right: 5px;
  }
  .close{
    height: 5px;
    width: 5px;
    display: inline-block;
    background-color: #666666;
    border-radius: 50%;
    margin-right: 5px;
  }
  .extline{
    text-overflow: ellipsis;
    display: -webkit-box;
    overflow: hidden; 
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    cursor: pointer;
  }
  .expand-group{
    position: relative;
    margin-top: 0;
    .child-table{
      border-radius: 4px;
      &.child-table-scope0{
        &.next-table{
          th{
            border-bottom: 1px solid #84E095 !important;
          }
        }
      }
      &.next-table{
        table {
          background-color: #F7F7F7 !important;
        }

        th,td{
          background-color: #F7F7F7 !important;
        }
        .next-table-row.next-scope-class{
          td {
            background: #fff !important;
            border-bottom: 1px solid #84E095 !important;
            border-top: 1px solid #84E095 !important;
            &:last-child{
              border-right: 1px solid #84E095 !important;
            }
          }
        }
        th .next-table-cell-wrapper{
          font-family:  PingFangSC;
          font-weight: 500;
          font-size: 12px;
          color: #999999;
        }
      }
    }
  }
}
.dialog-desc{
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  color: #333333;
  .keyValue{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    margin-bottom: 10px;
    .lable{
      font-weight: bolder;
      font-family: PingFangSC-Medium;
      font-size: 15px;
      width: 50%;
      text-align: right;
    }
    .value{
      margin-left: 10px;
      text-align: left;
    }
  }
}
.next-menu.next-ver.next-overlay-inner{
  .next-menu-item[role="menuitem"]{
    padding: 0 !important;
  }
}

.menu-item{
  *{
    width: 100% !important;
    text-align: left;
  }
  > button{
    margin: 0 20px;
  }
}
.tips{
  display: inline-block;
  width: 350px;
  margin-top: 20px;
}