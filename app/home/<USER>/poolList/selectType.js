import React, {Fragment, useEffect, useState} from 'react';
import {Button, Dialog, Field, Form, Grid, Input, Message, Select} from '@alife/next';
import {PageBase} from '../../base/index';
import './style.scss';
import * as api from "@/adator/api";
import {poolTypeEnum, publishStatusEnum} from "@/home/<USER>/common";

const {Row, Col} = Grid;
/*
* 选类型
* */

export default function SelectType(props) {
  console.log(props);
  const [poolType, setPoolType] = useState('1');

  const onOk = () => {
    if (poolType != "") {
      props.setVisible(false);
      props.history.push(`/pool/list/create/${poolType}`);
    } else {
      Message.warning("请选择选品集类型");
    }
  }

  const groups = [
    {title: '商品', icon: '品', value: '1'},
    { title: '门店', icon: '店', value: '2'}
  ];
  return (
    <Dialog
      title="选择选品集类型"
      visible={props.visible}
      className="select-type"
      style={{width: 540}}
      okProps={{children: '下一步'}}
      cancelProps={{children: '取消'}}
      footerActions={['cancel', 'ok']}
      onOk={onOk}
      onCancel={() => props.setVisible(false)}
      onClose={() => props.setVisible(false)}
    >
      <Row>
        {groups.map((item) => {
          return <Col key={item.value} className={`${poolType == item.value ? 'active' : ''}`} onClick={() => setPoolType(item.value)}>
            <em className={`icon_${item.value == 1 ? 'good' : 'shop'}`}>{item.icon}</em>{item.title}
          </Col>
        })}
      </Row>
    </Dialog>
  )
}
