import React, { useEffect, useRef, useState } from "react";
import { goldLog, logTimeComponent } from "@/utils/aplus";
import { withRouter } from "react-router-dom";
import { Button, Field, Form, Input, NumberPicker, Message, Select, Switch } from "@alife/next";

import { permissionAccess } from "@/components/PermissionAccess";
import { BreadcrumbTips } from "@/home/<USER>/comps";
import { onRequestError } from "@/utils/api";
import * as api from "@/utils/api/supermarketChannelBigStore";
import DayOfWeekWidget from '@/utils/Form/src/components/widgets/DayOfWeekWidget'
import {
  CategorySelect, PoolIdsSelect, TimeRangeArray, FormItem, ChannelSelect,
  VersionSelect, UserSelect, AOICascaderSelect, CityCascaderSelect
} from '../components'
import { XUAN_PIN_JI, SHANG_PIN_JU_LEI, supplyTypeList, sceneTypeList } from "../utils";
import { DatePicker, Radio } from "@alife/theme-nr-op";
import moment from "moment";
import "./style.scss";

// 选投首页地址
const selectionCommodityUrl = {
  daily:
    "https://market.wapa.taobao.com/app/op-fe/o2o-selection-admin/index.html#/pool/list",
  localdev: "https://pre-kunlun.alibaba-inc.com/selection#/pool/list",
  ppe: "https://pre-kunlun.alibaba-inc.com/selection#/pool/list",
  prod: "https://selection.kunlun.alibaba-inc.com/#/pool/list",
};

function validatorTimeRanges(value, timeSelection) {
  return new Promise((resolve, reject) => {
    if (timeSelection === '1') {
      const hasNull = value ? value.findIndex(item => {
        const [start, end] = item.split('-')
        return !item || !start || !end
      }) > -1 : true

      if (hasNull) {
        reject('请补充完整时段')
      } else {
        resolve()
      }
    } else {
      resolve();
    }
  })
}

function validatorVersion(value, allVersion) {
  return new Promise((resolve, reject) => {
    if (allVersion === false) {
      const hasNull = value ? value.findIndex(item => {
        return !item.operateType || !item.value
      }) > -1 : true

      if (hasNull) {
        reject('请填写版本')
      } else {
        resolve()
      }
    } else {
      resolve();
    }
  })
}

function BigStoreSceneCreate({ location, match, history, ...others }) {
  // sceneId 复制场景是负整数，编辑场景是正整数, 创建场景是 undefined
  const sceneId = useRef(match.params.id);
  const [selectedCitys, setSelectedCitys] = useState() // 存储选中的 城市id,即2级id
  const getSceneTypeName = (_sceneId) => {
    if (_sceneId < 0) {
      return "复制";
    } else if (_sceneId > 0) {
      return "编辑";
    } else {
      return "创建";
    }
  };

  const breadcrumbList = [
    {
      title: "场景管理",
      link: "#/channelManage/supermarketChannelBigStore/sceneManage",
    },
    { title: `${getSceneTypeName(sceneId.current)}场景`, link: "" },
  ];
  const field = Field.useField({
    values: {
      mainType: XUAN_PIN_JI,
      subType: XUAN_PIN_JI,
      sceneType: '2',
      workDay: [1, 2, 3, 4, 5, 6, 7],
      timeSelection: '0',
    },
    onChange: (name, value) => {
      if (name === "mainType") {
        // 切换类型时，清空数据
        field.setValues({
          mainType: value,
          mainPoolIds: undefined,
          mainCatId: undefined,
        });
      } else if (name === "subType") {
        // 切换类型时，清空数据
        field.setValues({
          subType: value,
          subPoolIds: undefined,
          subCatId: undefined,
        });
      } else if (name === 'timeSelection') {
        field.setValues({
          timeRanges: undefined
        })
      } else if (name === 'channelList') {
        field.setValues({
          allVersion: true,
          versionList: undefined
        })
      } else {
        field.setValue(name, value);
      }
    },
  });

  useEffect(() => {
    const value = field.getValue('mainType')
    // 切换类型时，清除校验
    if (value === XUAN_PIN_JI) {
      field.remove('mainCatId')
    }
    if (value === SHANG_PIN_JU_LEI) {
      field.remove('mainPoolIds')
    }
  }, [field.getValue('mainType')])


  useEffect(() => {
    const value = field.getValue('subType')
    // 切换类型时，清除校验
    if (value === XUAN_PIN_JI) {
      field.remove('subCatId')
    }
    if (value === SHANG_PIN_JU_LEI) {
      field.remove('subPoolIds')
    }
  }, [field.getValue('subType')])

  useEffect(() => {
    if (!sceneId.current) {
      return;
    }

    api
      .getMall4Scene({
        // sceneId 复制场景是负整数，需要转换，编辑场景是正整数，无需转换
        sceneId: sceneId.current < 0 ? -sceneId.current : sceneId.current,
      })
      .then((res = {}) => {
        // 基础信息回填
        const defaultValue = {
          sceneId: res.sceneId,
          sceneName: res.sceneName,
          sceneDisplayName: res.sceneDisplayName,
          weight: res.extMap.weight,
        };
        // 供给池信息回填
        if (res.mall4ItemRule) {
          defaultValue.mainPoolIds = res.mall4ItemRule.mainPoolIds;
          defaultValue.mainType = res.mall4ItemRule.mainType;
          defaultValue.subPoolIds = res.mall4ItemRule.subPoolIds;
          defaultValue.subType = res.mall4ItemRule.subType;
          if (res.mall4ItemRule.mainCatId) {
            defaultValue.mainCatId = res.mall4ItemRule.mainCatId;
          }
          if (res.mall4ItemRule.mainCat3Id && res.mall4ItemRule.mainCat3Id.hasSelectedDisplay) {
            // 兼容老数据
            defaultValue.mainCatId = res.mall4ItemRule.mainCat3Id.hasSelectedDisplay;
          }
          if (res.mall4ItemRule.subCatId) {
            defaultValue.subCatId = res.mall4ItemRule.subCatId;
          }
          if (res.mall4ItemRule.subCat3Id && res.mall4ItemRule.subCat3Id.hasSelectedDisplay) {
            // 兼容老数据
            defaultValue.subCatId = res.mall4ItemRule.subCat3Id.hasSelectedDisplay;
          }
          defaultValue.sceneType = res.sceneType;
          if (res.mall4ItemRule.startTime) {
            defaultValue.startTime = moment(res.mall4ItemRule.startTime)
          }
          if (res.mall4ItemRule.endTime) {
            defaultValue.endTime = moment(res.mall4ItemRule.endTime)
          }
          defaultValue.workDay = res.mall4ItemRule.workDay
          defaultValue.timeSelection = res.mall4ItemRule.timeSelection
          defaultValue.timeRanges = res.mall4ItemRule.timeRanges
          defaultValue.channelList = res.mall4ItemRule.channelList
          if (res.mall4ItemRule.channelVersionList) {
            defaultValue.allVersion = res.mall4ItemRule.channelVersionList.allVersion
            defaultValue.versionList = res.mall4ItemRule.channelVersionList.versionGroup.versionList
          }
          defaultValue.citys = res.mall4ItemRule.citys;
          defaultValue.userTagGroupIdsList = res.mall4ItemRule.userTagGroupIdsList;
          defaultValue.extAoiCodes = res.mall4ItemRule.extAoiCodes;

          if (res.mall4ItemRule.selectedCitys) {
            setSelectedCitys(res.mall4ItemRule.selectedCitys)
          }
        }
        field.setValues(defaultValue);
      })
      .catch((e) => {
        console.log(e, 'eee')
        onRequestError(e);
      });
  }, []);

  const onSubmit = async () => {
    const { errors } = await field.validatePromise()
    if (errors) {
      const firstErr = Object.values(errors)[0].errors[0];
      Message.error(firstErr || '请检查表单');
      return
    }

    // 创建、复制、编辑场景都在这里处理
    const {
      sceneName,
      sceneDisplayName,
      weight,
      mainPoolIds,
      mainType,
      mainCatId,
      subPoolIds,
      subType,
      subCatId,
      sceneType,
      startTime,
      endTime,
      workDay,
      timeSelection,
      timeRanges,
      channelList,
      allVersion,
      versionList,
      citys,
      userTagGroupIdsList,
      extAoiCodes,
    } = field.getValues();

    const params = {
      sceneName,
      sceneDisplayName,
      mall4ItemRule: {
        mainPoolIds,
        mainType,
        mainCatId,
        subPoolIds,
        subType,
        subCatId,
        startTime: startTime ? moment(startTime).valueOf() : null,
        endTime: endTime ? moment(endTime).valueOf() : null,
        workDay,
        timeSelection,
        timeRanges,
        channelList,
        citys,
        selectedCitys,
        userTagGroupIdsList,
        extAoiCodes,
      },
      weight: +weight,
      sceneType,
    };
    const hasChannelVersionList = ["*", "android", "ios"].findIndex(item => {
      return ["*", "android", "ios"].includes(item)
    }) > -1
    if (hasChannelVersionList) {
      params.mall4ItemRule.channelVersionList = {
        allVersion,
        channel: channelList ? ["*", "android", "ios"].filter(val => channelList.includes(val)) : null,
        versionGroup: {
          relationType: 'and',
          groupList: [], //暂时不知道啥用，写死空数组传给后端
          versionList: allVersion ? null : versionList
        }
      }
    }
    /**
     *  sceneId
     *    『复制』场景是负整数，走创建流程。不传sceneId
     *    「编辑」场景是正整数，走编辑场景，传sceneId，更新数据
     */
    if (0 < sceneId.current) {
      params.sceneId = sceneId.current;
    }

    api
      .editMall4Scene(params)
      .then((res) => {
        Message.success("保存成功");
        history.go(-1);
      })
      .catch((e) => {
        onRequestError(e);
      });
  };

  const showVersionSelect = (field.getValue('channelList') || []).findIndex(item => {
    return ["*", "android", "ios"].includes(item)
  }) > -1

  return (
    <div className="big-store-page">
      <BreadcrumbTips list={breadcrumbList} />
      <Form className="bsp-form" field={field} >
        <FormItem label="场景id">
          <Input name="sceneId" disabled placeholder="创建后自动生成" />
        </FormItem>
        <FormItem
          label="场景名称"
          required={true}
          requiredMessage="场景名称不能为空"
        >
          <Input
            name="sceneName"
            placeholder="最多20字（只在后台查看显示）"
            maxLength={20}
          />
        </FormItem>
        <FormItem
          label="前台名称"
          required={true}
          requiredMessage="前台名称不能为空"
        >
          <Input
            name="sceneDisplayName"
            placeholder="最多10个字（导购消费）"
            maxLength={10}
          />
        </FormItem>
        <FormItem
          label={`主-供给来源`}
          required
          requiredMessage="主-供给来源不能为空"
        >
          <Select
            name="mainType"
            style={{ width: "100%" }}
            dataSource={supplyTypeList}
            value={field.getValue("mainType") || XUAN_PIN_JI}
          />
        </FormItem>


        {field.getValue("mainType") === XUAN_PIN_JI ? (
          <FormItem
            label={`主 - 选品集ID`}
            required
            requiredMessage={`选品集ID不能为空`}
            hasFeedback
          >
            <PoolIdsSelect
              name="mainPoolIds"
              value={field.getValue("mainPoolIds")}
              maxCommodityLength={8}
            />
            <a href={selectionCommodityUrl[window.configEnv]} target="_blank">
              去创建
            </a>
          </FormItem>
        ) : null}

        {field.getValue("mainType") === SHANG_PIN_JU_LEI ? (
          <FormItem
            label={`主 - 商品类目`}
            required
            requiredMessage={`商品类目不能为空`}
            hasFeedback
          >
            <CategorySelect
              name="mainCatId"
              value={field.getValue("mainCatId")}
            />
          </FormItem>
        ) : null}

        <FormItem
          label={`副-供给来源`}
          required
          requiredMessage="副-供给来源不能为空"
        >
          <Select
            name="subType"
            style={{ width: "100%" }}
            dataSource={supplyTypeList}
            value={field.getValue("subType") || XUAN_PIN_JI}
          />
        </FormItem>

        {field.getValue("subType") === XUAN_PIN_JI ? (
          <FormItem
            label={`副 - 选品集ID`}
            required
            requiredMessage={`选品集ID不能为空`}
            hasFeedback
          >
            <PoolIdsSelect
              name="subPoolIds"
              value={field.getValue("subPoolIds")}
              maxCommodityLength={8}
            />
            <a href={selectionCommodityUrl[window.configEnv]} target="_blank">
              去创建
            </a>
          </FormItem>
        ) : null}

        {field.getValue("subType") === SHANG_PIN_JU_LEI ? (
          <FormItem
            label={`副 - 商品类目`}
            required
            requiredMessage={`商品类目不能为空`}
            hasFeedback
          >
            <CategorySelect
              name="subCatId"
              value={field.getValue("subCatId")}
            />
          </FormItem>
        ) : null}
        <FormItem label="权重" required={true} requiredMessage="权重不能为空">
          <NumberPicker name="weight" max={999999} hasFeedback={false} style={{ width: '100%' }} />
        </FormItem>
        <FormItem label="场景类型" required requiredMessage="场景类型不能为空">
          <Radio.Group name="sceneType" dataSource={sceneTypeList} />
        </FormItem>
        <FormItem label="开始时间:" required requiredMessage="开始时间不能为空">
          <DatePicker
            name="startTime"
            showTime
            placeholder="请选择开始时间"
          />
        </FormItem>
        <FormItem label="结束时间:" required requiredMessage="结束时间不能为空">
          <DatePicker
            showTime={{
              defaultValue: moment("23:59:59", "HH:mm:ss", true),
            }}
            name="endTime"
            placeholder="请选择结束时间"
          />
        </FormItem>
        <FormItem label="星期"  >
          <DayOfWeekWidget name="workDay" />
        </FormItem>
        <FormItem label="时段:">
          <Radio.Group name="timeSelection" defaultValue={0}>
            <Radio value={'0'}>全天</Radio>
            <Radio value={'1'}>选择</Radio>
          </Radio.Group>
        </FormItem>
        {field.getValue("timeSelection") === '1' ? (
          <FormItem
            label=" "
            validator={(_rule, value) => validatorTimeRanges(value, field.getValue("timeSelection"))}
          >
            <TimeRangeArray name="timeRanges" />
          </FormItem>
        ) : null}

        <FormItem label="投放渠道:" >
          <ChannelSelect name="channelList" multiple />
        </FormItem>
        {showVersionSelect ? (
          <FormItem label="投放版本号(仅针对app):">
            <Switch
              style={{ width: "130px" }}
              checkedChildren="默认全版本"
              unCheckedChildren="不默认全版本"
              name="allVersion"
            />
          </FormItem>
        ) : null}
        {showVersionSelect && field.getValue('allVersion') === false ? (
          <FormItem
            label=" "
            validator={(_rule, value) => validatorVersion(value, field.getValue('allVersion'))}
          >
            <VersionSelect name="versionList" />
          </FormItem>
        ) : null}

        <FormItem label="投放城市:">
          <CityCascaderSelect
            name="citys"
            multiple
            showSearch
            onSelectCitysChange={(value) => {
              setSelectedCitys(value)
            }}
          />
        </FormItem>
        <FormItem label="投放人群：" >
          <UserSelect name="userTagGroupIdsList" />
          <a
            className="to-create"
            href="https://boreas.kunlun.alibaba-inc.com/page/crm/index.html?from=header-v2#/crm/crowedSelect"
            target="_blank"
          >
            去创建
          </a>
        </FormItem>
        <FormItem label="投放AOI类型:">
          <AOICascaderSelect name="extAoiCodes" multiple showSearch />
        </FormItem>


        <FormItem label=" ">
          <Button
            type="primary"
            onClick={() => {
              onSubmit();
            }}
          >
            保存
          </Button>
        </FormItem>
      </Form>
    </div>
  );
}

export const LogTimeBigStoreSceneCreatePage = logTimeComponent(
  withRouter(BigStoreSceneCreate),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const BigStoreSceneCreatePage = permissionAccess(
  LogTimeBigStoreSceneCreatePage,
  async (params) => {
    return await api.validateAclResource(params);
  }
);
