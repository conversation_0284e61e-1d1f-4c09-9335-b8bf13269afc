import React, { useState, useEffect } from "react";
import { withRouter } from "react-router-dom";
import { <PERSON><PERSON>, Field, Message } from "@alife/next";
import cms from "classnames";

import { goldLog, logTimeComponent } from "@/utils/aplus";
import { onRequestError } from "@/utils/api";
import { BreadcrumbTips } from "@/home/<USER>/comps";
import { permissionAccess } from "@/components/PermissionAccess";

import { viewRank, saveRank } from "../api";
import {
  BaseInfoForm,
  FormItem,
  SecondTitle,
  RecallRuleForm,
  TabSettingItem,
} from "../components/index";
import { GeneralUtilsProvider } from "../hooks/generalUtils";
import {
  XUAN_PIN_JI,
  REN_QI_BANG,
  PIN_PAI_BANG,
  FENG_SHENG_BANG,
  formatBangDanField2Request,
  formatBaseInfoField2Request,
  formatRecallRuleField2Request,
  formatBangDanRes2Field,
  formatBaseInfoRes2Field,
  formatRecallRuleRes2Field,
  getValidBangDanCount,
  genId,
  onItemDown,
  onItemRemove,
  onItemUp,
} from "../utils";

import "./style.scss";

const breadcrumbList = [
  { title: "榜单管理", link: "#/rankManage/list" },
  { title: "新建榜单", link: "" },
];

function MainTitle({ title }) {
  return <h1>{title}</h1>;
}
function ThirdTitle({ title }) {
  return <h3>{title}</h3>;
}

function RankCreateV2(props) {
  const originRankId = props.match.params.id || "";
  const rankId = originRankId < 0 ? -originRankId : originRankId;
  const [submitLoading, setSubmitLoading] = useState();

  // 基础榜单
  const baseInfoField = Field.useField({
    values: {
      nameImgUrl: [],
      timeSelection: 0,
      workDay: [1, 2, 3, 4, 5, 6, 7],
      boardCategory: 1,
      maxRecallCount: 10,
    },
    onChange: (name, value) => {
      if (name == "timeSelection") {
        baseInfoField.setValues({
          timeRanges:
            +value === 1
              ? [
                  {
                    key: genId(),
                    startTime: "",
                    endTime: "",
                  },
                ]
              : undefined,
          [name]: value,
        });
      }
    },
  });

  // fields 疯省榜、人气榜、品牌榜
  const fengShengRecallRuleField = Field.useField({
    values: {
      topItemSortRule: 3,
    },
    onChange: (name, value) => {
      fengShengRecallRuleField.setValue(name, value);
    },
  });
  const renQiBangRecallRuleField = Field.useField({
    values: {
      topItemSortRule: 3,
      shopScatter: 0,
    },
    onChange: (name, value) => {
      renQiBangRecallRuleField.setValue(name, value);
    },
  });
  const pinPaiRecallRuleField = Field.useField({
    values: {
      shopScatter: 0,
    },
    onChange: (name, value) => {
      pinPaiRecallRuleField.setValue(name, value);
    },
  });

  // 展示数据 疯省榜、人气榜、品牌榜
  const [fengShengBangList, setFengShengBangList] = useState([
    {
      key: genId(),
      supplyType: XUAN_PIN_JI,
    },
  ]);
  const [renQiBangList, setRenQiBangList] = useState([
    {
      key: genId(),
      supplyType: XUAN_PIN_JI,
    },
  ]);
  const [pinPaiBangList, setPinPaiBangList] = useState([
    {
      key: genId(),
      supplyType: XUAN_PIN_JI,
    },
  ]);

  // 展开/收起控制 疯省榜、人气榜、品牌榜
  const [fengShengBangVisible, setFengShengBangVisible] = useState(false);
  const [pinPaiBangVisible, setPinPaiBangVisible] = useState(false);
  const [renQiBangVisible, setRenQiBangVisible] = useState(false);

  useEffect(() => {
    // 编辑、复制场景下，获取数据并进行回显
    if (rankId) {
      viewRank({ boardId: rankId })
        .then((result) => {
          // 处理基础数据回填
          const defaultBaseInfoValue = formatBaseInfoRes2Field(result);
          baseInfoField.setValues(defaultBaseInfoValue);
          // 处理榜单数据回填
          if (result.rankInfo && result.rankInfo.length > 0) {
            result.rankInfo.map((rankInfoItem) => {
              // 格式化 tab(1-10) 数据
              const _defaultValue = rankInfoItem.pureDataSourceList.map(
                (item) => {
                  return formatBangDanRes2Field(item);
                }
              );
              // 格式化召回规则数据
              const defaultRecallRuleValue = formatRecallRuleRes2Field(
                rankInfoItem.recallRule
              );

              if (rankInfoItem.type === FENG_SHENG_BANG) {
                setFengShengBangList(_defaultValue);
                fengShengRecallRuleField.setValues(defaultRecallRuleValue);
              }
              if (rankInfoItem.type === REN_QI_BANG) {
                setRenQiBangList(_defaultValue);
                renQiBangRecallRuleField.setValues(defaultRecallRuleValue);
              }
              if (rankInfoItem.type === PIN_PAI_BANG) {
                setPinPaiBangList(_defaultValue);
                pinPaiRecallRuleField.setValues(defaultRecallRuleValue);
              }
            });
          }
        })
        .catch((e) => {
          console.error("e=======viewRank", e);
          onRequestError(e);
        });
    }
  }, []);

  const onItemAdd = (list, updateList) => {
    const _value = [...list];
    _value.push({
      key: genId(),
      supplyType: XUAN_PIN_JI,
    });
    updateList(_value);
  };

  const onSubmit = async () => {
    let validateError = undefined;
    // 校验基础信息
    const { errors, values } = await baseInfoField.validatePromise();

    const dateRanges = values.timeRanges;
    const checkDateRanges = +values.timeSelection === 1;
    let dateRangeHasError = "";
    if (checkDateRanges) {
      if (dateRanges && dateRanges.length > 0) {
        dateRanges.map((date) => {
          if (!date.startTime || !date.endTime) {
            dateRangeHasError = true;
          }
        });
      } else {
        dateRangeHasError = true;
      }
    }

    if (dateRangeHasError) {
      validateError = { errorMsg: "请检查时段填写是否正确" };
    } else if (errors) {
      validateError = errors;
    }

    if (validateError) {
      Message.error(validateError.errorMsg || "请检查基础信息填写是否正确");
      return;
    }

    // 榜单分类，如果是非标品榜单，不提交品牌榜单信息
    const { boardCategory } = baseInfoField.getValues();
    const baseInfo = formatBaseInfoField2Request(baseInfoField.getValues());

    // 校验榜单信息
    const rankInfo = [];
    const fsbQueryList = [];
    const rqbQueryList = [];
    const ppbQueryList = [];
    if (fengShengBangList.length > 0) {
      const fengShengFieldList = fengShengBangList.map((item) => {
        return item.field;
      });
      fengShengFieldList.forEach((item, _i) => {
        if (item) {
          const { itemCat, poolIds } = item.getValues();
          if (itemCat || poolIds) {
            const bangDanData = formatBangDanField2Request(
              item.getValues(),
              _i
            );
            fsbQueryList.push(bangDanData);
          }
        }
      });
      if (fsbQueryList.length > 0) {
        // 本不需要格式化，格式化主要是为了表明 recallRule 里都需要具体哪些数据
        const _recallRule = formatRecallRuleField2Request(
          fengShengRecallRuleField.getValues()
        );
        rankInfo.push({
          type: FENG_SHENG_BANG, // 疯省榜
          index: 3,
          pureDataSourceList: [...fsbQueryList],
          recallRule: {
            ..._recallRule,
          },
        });
      }
    }

    if (renQiBangList.length > 0) {
      const renQiFieldList = renQiBangList.map((item) => {
        return item.field;
      });
      renQiFieldList.forEach((item, _i) => {
        if (item) {
          const { itemCat, poolIds } = item.getValues();
          if (itemCat || poolIds) {
            const bangDanData = formatBangDanField2Request(
              item.getValues(),
              _i
            );
            rqbQueryList.push(bangDanData);
          }
        }
      });
      if (rqbQueryList.length > 0) {
        // 本不需要格式化，格式化主要是为了表明 recallRule 里都需要具体哪些数据
        const _recallRule = formatRecallRuleField2Request(
          renQiBangRecallRuleField.getValues()
        );
        rankInfo.push({
          type: REN_QI_BANG, // 人气榜
          index: 1,
          pureDataSourceList: [...rqbQueryList],
          recallRule: {
            ..._recallRule,
          },
        });
      }
    }

    // 2 非标品榜单
    if (+boardCategory !== 2 && pinPaiBangList.length > 0) {
      const pinPaiFieldList = pinPaiBangList.map((item) => {
        return item.field;
      });
      pinPaiFieldList.forEach((item, _i) => {
        if (item) {
          const { itemCat, poolIds } = item.getValues();
          if (itemCat || poolIds) {
            const bangDanData = formatBangDanField2Request(
              item.getValues(),
              _i
            );
            ppbQueryList.push(bangDanData);
          }
        }
      });
      if (ppbQueryList.length > 0) {
        // 本不需要格式化，格式化主要是为了表明 recallRule 里都需要具体哪些数据
        const _recallRule = formatRecallRuleField2Request(
          pinPaiRecallRuleField.getValues()
        );
        rankInfo.push({
          type: PIN_PAI_BANG, // 品牌榜
          index: 2,
          pureDataSourceList: [...ppbQueryList],
          recallRule: {
            ..._recallRule,
          },
        });
      }
    }

    console.log("onSubmit=======rankInfo", boardCategory, rankInfo);
    if (rankInfo.length > 0) {
      const requestDTO = {
        ...baseInfo,
        rankInfo,
      };
      if (originRankId < 0) {
        delete requestDTO.id;
      }
      saveRank(requestDTO)
        .then((result) => {
          console.log(result);
          location.href = "#/rankManage/list";
        })
        .catch((error) => {
          onRequestError(error);
        })
        .finally(() => {
          setSubmitLoading(false);
        });
    } else {
      Message.error("榜单至少一个有效tab");
      return;
    }
  };

  return (
    <div className={"container"}>
      <BreadcrumbTips list={breadcrumbList} />
      <div className="body rankv2-create">
        <MainTitle title="基础配置" />
        <BaseInfoForm field={baseInfoField} />

        <GeneralUtilsProvider>
          <MainTitle title="榜单配置" />
          {/* 疯省榜 */}
          <SecondTitle
            title={`疯省榜（${getValidBangDanCount(fengShengBangList)}/10）`}
            isOpen={fengShengBangVisible}
            showOpenBtn
            onOpenBtnClick={() =>
              setFengShengBangVisible(!fengShengBangVisible)
            }
          />
          <div
            className={cms({
              hidden: !fengShengBangVisible,
            })}
          >
            <ThirdTitle title="tab设置（1-10）" />
            <div className="tab_setting_list">
              {fengShengBangList.map((item, i) => {
                return (
                  <TabSettingItem
                    key={item.key}
                    currentNum={i + 1}
                    showFixedPureDataSource
                    showDownBtn={
                      i !== fengShengBangList.length - 1 &&
                      fengShengBangList.length > 1
                    }
                    showRemoveBtn={true}
                    showUpBtn={i !== 0 && fengShengBangList.length > 1}
                    value={item}
                    onItemDown={() =>
                      onItemDown(
                        item.key,
                        fengShengBangList,
                        setFengShengBangList
                      )
                    }
                    onItemRemove={() =>
                      onItemRemove(
                        item.key,
                        fengShengBangList,
                        setFengShengBangList
                      )
                    }
                    onItemUp={() =>
                      onItemUp(
                        item.key,
                        fengShengBangList,
                        setFengShengBangList
                      )
                    }
                    onSaveField={(field) => {
                      const _fengShengBangList = [...fengShengBangList];
                      _fengShengBangList[i].field = field;
                      setFengShengBangList(_fengShengBangList);
                    }}
                  />
                );
              })}

              {fengShengBangList.length < 10 ? (
                <Button
                  onClick={() =>
                    onItemAdd(fengShengBangList, setFengShengBangList)
                  }
                  text
                  type="primary"
                >
                  添加
                </Button>
              ) : null}
            </div>

            <ThirdTitle title="商品召回规则" />
            <RecallRuleForm field={fengShengRecallRuleField} showTopSortRule />
          </div>

          {/* 人气榜 */}
          <SecondTitle
            title={`人气榜（${+getValidBangDanCount(renQiBangList)}/10）`}
            isOpen={renQiBangVisible}
            showOpenBtn
            onOpenBtnClick={() => setRenQiBangVisible(!renQiBangVisible)}
          />
          <div
            className={cms({
              hidden: !renQiBangVisible,
            })}
          >
            <ThirdTitle title="tab设置（1-10）" />
            <div className="tab_setting_list">
              {renQiBangList.map((item, i) => {
                return (
                  <TabSettingItem
                    key={item.key}
                    currentNum={i + 1}
                    showFixedPureDataSource
                    showDownBtn={
                      i !== renQiBangList.length - 1 && renQiBangList.length > 1
                    }
                    showRemoveBtn={true}
                    showUpBtn={i !== 0 && renQiBangList.length > 1}
                    value={item}
                    onItemDown={() =>
                      onItemDown(item.key, renQiBangList, setRenQiBangList)
                    }
                    onItemRemove={() =>
                      onItemRemove(item.key, renQiBangList, setRenQiBangList)
                    }
                    onItemUp={() =>
                      onItemUp(item.key, renQiBangList, setRenQiBangList)
                    }
                    onSaveField={(field) => {
                      const _renQiBangList = [...renQiBangList];
                      _renQiBangList[i].field = field;
                      setRenQiBangList(_renQiBangList);
                    }}
                  />
                );
              })}

              {renQiBangList.length < 10 ? (
                <Button
                  onClick={() => onItemAdd(renQiBangList, setRenQiBangList)}
                  text
                  type="primary"
                >
                  添加
                </Button>
              ) : null}
            </div>

            <ThirdTitle title="商品召回规则" />
            <RecallRuleForm
              field={renQiBangRecallRuleField}
              showTopSortRule
              showShopScatter
            />
          </div>

          {/* 品牌榜 */}
          {baseInfoField.getValue("boardCategory") !== 2 ? (
            <>
              <SecondTitle
                title={`品牌榜（${getValidBangDanCount(pinPaiBangList)}/10）`}
                isOpen={pinPaiBangVisible}
                showOpenBtn
                onOpenBtnClick={() => setPinPaiBangVisible(!pinPaiBangVisible)}
              />
              <div
                className={cms({
                  hidden: !pinPaiBangVisible,
                })}
              >
                <ThirdTitle title="tab设置（1-10）" />
                <div className="tab_setting_list">
                  {pinPaiBangList.map((item, i) => {
                    return (
                      <TabSettingItem
                        key={item.key}
                        currentNum={i + 1}
                        showDownBtn={
                          i !== pinPaiBangList.length - 1 &&
                          pinPaiBangList.length > 1
                        }
                        showRemoveBtn={true}
                        showTopBrandIdList
                        showUpBtn={i !== 0 && pinPaiBangList.length > 1}
                        value={item}
                        onItemDown={() =>
                          onItemDown(
                            item.key,
                            pinPaiBangList,
                            setPinPaiBangList
                          )
                        }
                        onItemRemove={() =>
                          onItemRemove(
                            item.key,
                            pinPaiBangList,
                            setPinPaiBangList
                          )
                        }
                        onItemUp={() =>
                          onItemUp(item.key, pinPaiBangList, setPinPaiBangList)
                        }
                        onSaveField={(field) => {
                          const _pinPaiBangList = [...pinPaiBangList];
                          _pinPaiBangList[i].field = field;
                          setPinPaiBangList(_pinPaiBangList);
                        }}
                      />
                    );
                  })}

                  {pinPaiBangList.length < 10 ? (
                    <Button
                      onClick={() =>
                        onItemAdd(pinPaiBangList, setPinPaiBangList)
                      }
                      text
                      type="primary"
                    >
                      添加
                    </Button>
                  ) : null}
                </div>

                <ThirdTitle title="商品召回规则" />
                <RecallRuleForm field={pinPaiRecallRuleField} />
              </div>
            </>
          ) : null}
        </GeneralUtilsProvider>
        <FormItem label=" ">
          <Button
            loading={submitLoading}
            type="primary"
            onClick={() => {
              onSubmit();
            }}
          >
            保存
          </Button>
        </FormItem>
      </div>
    </div>
  );
}

export const LogTimePutInPage = logTimeComponent(
  withRouter(RankCreateV2),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const rankCreatePage = permissionAccess(LogTimePutInPage);
