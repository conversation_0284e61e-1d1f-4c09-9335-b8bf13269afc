import React, {Fragment, useEffect, useState,useRef} from 'react';
import {Button, Dialog, Grid, Message} from '@alife/next';
import {withRouter} from "react-router-dom";
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {permissionAccess} from "@/components/PermissionAccess";
import RankSet from "@/containers/channel/market/comps/rankSet";
import './style.scss';
import {BreadcrumbTips} from "@/home/<USER>/comps";
import SchemaForm from "@/utils/Form/src";
import {getNormalTemplateSchema} from "@/home/<USER>/setSchema/request";
import * as api from "@/utils/api";
import {baseQueryRankSchemaDTO} from "@/home/<USER>/common";
import {showQrCodeMap} from "@/containers/channel/market/common";
import {saveRank, viewRank} from "@/utils/api";
const {Row, Col} = Grid
/**
 * 榜单创建表单
 * @param props
 * @returns {JSX.Element}
 */

function rankCreate(props) {
  console.log(props);
  const breadcrumbList = [
    {"title": '榜单管理', link: "#/rankManage/list"},
    {"title": '新建榜单', link: ""}
  ];
  const originRankId = props.match.params.id || '';
  const rankId = originRankId < 0 ? -originRankId : originRankId;
  const [formData,setFormData] = useState({});
  const [scheduleTemplate, setScheduleTemplate] = useState({});
  const [rankSchema, setRankSchema] = useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);

  const onFormChange = (formData) => {
    console.log('submit',formData);
    setFormData(formData);
    if(formData.boardCategory) {
      filterRankSchema(scheduleTemplate.rankSchema,formData.boardCategory);
    }
  }


  useEffect(() => {
    sessionStorage.clear();
    getRank();
    // getRank();
    // getAll();
    return () => {
      sessionStorage.clear();
    };
  }, [])

  /**
   * 非资源位的获取元数据schema——常规链路
   * */
  const getNormalSchema = (data = {}) => {
    getNormalTemplateSchema(baseQueryRankSchemaDTO)
      .then((result) => {
        let scheduleTemplate = JSON.parse(result);
        setScheduleTemplate(scheduleTemplate);
        setRankSchema(scheduleTemplate.rankSchema);
        if(data.boardCategory) {  // 需要根据榜单类型的时候，需要初始化不同的schema
          filterRankSchema(scheduleTemplate.rankSchema,data.boardCategory);
        }
        // if(rankId) {
        //   getRank();
        // }
      })
      .catch(api.onRequestError);
  }

  /**
   * 过滤榜单的表单项
   * 选择标品榜单：人气榜展示指定商品置顶：输入cluster_id
   * 选择非标品榜单，1.榜单配置模块仅展示人气榜  2.人气榜指定叶子类目置顶：输入叶子类目ID
   */
  const filterRankSchema = (schema,boardCategory) => {
    if (schema && schema.length > 0) {
      let _rankSchema = JSON.parse(JSON.stringify(schema));
      let popularitySchemaGroup = _rankSchema.filter((o) => o.type == 1);
      let {showItemCat,showMultilineIdText} = (popularitySchemaGroup && popularitySchemaGroup.length > 0) ? popularitySchemaGroup[0].schema : {};
      if (boardCategory == '1') { // 标品   不展示叶子类目  展示指定商品置顶
        // showItemCat.hidden = true;
        if (showMultilineIdText && showMultilineIdText.length > 0) {
          showMultilineIdText[0].hidden = false;
        }
        // _rankSchema = JSON.parse(JSON.stringify(schema));
        // initRank();
        // initRank(_rankSchema);
      } else { // 非标品 展示叶子类目，不展示指定商品置顶
        // showItemCat.hidden = false;
        if (showMultilineIdText && showMultilineIdText.length > 0) {
          showMultilineIdText[0].hidden = true;
        }
        _rankSchema = popularitySchemaGroup;
        // initRank(_rankSchema);
      }
      setRankSchema(_rankSchema);
    }
  }

  const deepEqual = (obj1, obj2) => {
    // 类型不同，直接返回 false
    if (typeof obj1 !== typeof obj2) {
      return false;
    }

    // 如果是基本类型，直接比较值
    if (typeof obj1 !== 'object' || obj1 === null || obj2 === null) {
      return obj1 === obj2;
    }

    // 获取两个对象的属性数组
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    // 属性个数不同，直接返回 false
    if (keys1.length !== keys2.length) {
      return false;
    }

    // 递归比较每个属性的值
    for (let key of keys1) {
      if (!deepEqual(obj1[key], obj2[key])) {
        return false;
      }
    }

    return true;
  };

  const getRank = () =>{
    if(rankId) {
      viewRank({boardId: rankId})
        .then((result) => {
          setFormData(result);
          let {rankInfo = []} = result;
          sessionStorage.setItem('rankInfo', JSON.stringify(rankInfo));
          getNormalSchema(result);
        })
        .catch(api.onRequestError);
    }else{
      getNormalSchema();
    }
  }
  const formRef = useRef(null);

  const filterRankInfo = () => {
    let rankInfo = JSON.parse(sessionStorage.getItem('rankInfo'));
    let _rankInfo = [];
    rankInfo.map((p)=>{
      const emptyObj = {supplyType: 1, shopList: [], poolIds: [], itemCat: [], categoryData: [], tabIndex: 1};
      let _pureDataSourceList = p.pureDataSourceList.filter((o)=>!deepEqual(o,emptyObj));
      let hasDataSource = _pureDataSourceList.filter((o) => o.itemCat.length > 0 || o.poolIds.length > 0);
      if (hasDataSource && hasDataSource.length > 0) {
        _rankInfo.push(p);
      }
    })
    console.log('_rankInfo',_rankInfo);
    if (formData.boardCategory == '2') {
      _rankInfo = _rankInfo.filter((o) => o.index != 2);
    }
    if (_rankInfo.length > 0) {
      return _rankInfo;
    } else {
      Message.error('榜单至少一个有效tab');
    }
  }

  const submit = () => {
    // 看看schemaform是否有报错，如果有则不能发布。
    console.log(filterRankInfo());
    const errors = formRef.current.fieldErrorMap;
    if (!Object.keys(errors).length && formData && filterRankInfo()) {
      setSubmitLoading(true);
      let requestDTO = {
        ...formData,
        rankInfo: filterRankInfo()
      }
      delete requestDTO.id;
      if (originRankId && originRankId > 0) {
        requestDTO.id = rankId;
      }
      saveRank(requestDTO)
        .then((result) => {
          console.log(result);
          setSubmitLoading(false);
          sessionStorage.clear();
          location.href = '#/rankManage/list';
        })
        .catch((error) => {
          setSubmitLoading(false);
          api.onRequestError(error);
        });
    }
  }

  let showSchemaForm = Boolean(((rankId && formData.id) || !rankId) && scheduleTemplate);
  let schema = scheduleTemplate ? scheduleTemplate['detail'] : '';
  let showRankSet = (rankSchema && rankSchema.length > 0);
  return (
    <div className={'container'}>
      <BreadcrumbTips list={breadcrumbList}/>
      <div className={'body'}>
        <Row>
          <Col span={'2'}></Col>
          <Col span={'19'}>
            <div className="rank-create">
              {showSchemaForm &&
              <SchemaForm ref={formRef} liveValidate schema={schema} formData={formData} onChange={(formData) => onFormChange(formData)}/>}
              {showRankSet && <RankSet rankSchema={rankSchema} boardCategory={formData.boardCategory}   />}
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={'8'}/>
          <Col span={'16'}>
            {/*<Button>取消</Button>*/}
            &nbsp;
            {(showSchemaForm && showRankSet) && <Button type="primary"  disabled={submitLoading}  onClick={submit}>
              发布
            </Button>}
          </Col>
        </Row>
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(rankCreate), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const rankCreatePage = permissionAccess(LogTimePutInPage)

