export function deepCopy(obj) {
  let result = Array.isArray(obj) ? [] : {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === "object" && obj[key] !== null) {
        result[key] = deepCopy(obj[key]);
      } else {
        result[key] = obj[key];
      }
    }
  }
  return result;
}

export const XUAN_PIN_JI = 1;
export const SHANG_PIN_JU_LEI = 2;
export const supplyTypeList = [
  {
    label: "指定选品集",
    value: XUAN_PIN_JI,
  },
  {
    label: "不指定，商品聚类",
    value: SHANG_PIN_JU_LEI,
  },
];

export const sceneTypeList = [
  {
    label: "搭配购",
    value: '2',
  }, {
    label: "普通货架",
    value:'3',
  },
]

export const weekDaysEnum = [
  {
    label: "周一",
    value: 1,
  }, {
    label: "周二",
    value: 2,
  }, {
    label: "周三",
    value: 3,
  }, {
    label: "周四",
    value: 4,
  }, {
    label: "周五",
    value: 5,
  }, {
    label: "周六",
    value: 6,
  }, {
    label: "周日",
    value: 7,
  }
]