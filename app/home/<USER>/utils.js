import moment from "moment";
import { momentToTimeStamp } from "@/utils/time";

// 生成唯一标识，用来控制上下移动的顺序
export const genId = (() => {
  let _id = 0;
  return () => {
    return ++_id;
  };
})();

export function deepCopy(obj) {
  let result = Array.isArray(obj) ? [] : {};
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === "object" && obj[key] !== null) {
        result[key] = deepCopy(obj[key]);
      } else {
        result[key] = obj[key];
      }
    }
  }
  return result;
}

export const REN_QI_BANG = 1;
export const PIN_PAI_BANG = 2;
export const FENG_SHENG_BANG = 3;

export const XUAN_PIN_JI = 1;
export const SHANG_PIN_JU_LEI = 2;
export const supplyTypeList = [
  {
    label: "指定选品集",
    value: XUAN_PIN_JI,
  },
  {
    label: "不指定，商品聚类",
    value: SHANG_PIN_JU_LEI,
  },
];

// 选投首页地址
export const selectionCommodityUrl = {
  daily:
    "https://market.wapa.taobao.com/app/op-fe/o2o-selection-admin/index.html#/pool/list",
  localdev: "https://pre-kunlun.alibaba-inc.com/selection#/pool/list",
  ppe: "https://pre-kunlun.alibaba-inc.com/selection#/pool/list",
  prod: "https://selection.kunlun.alibaba-inc.com/#/pool/list",
};

export const formatBaseInfoField2Request = (value) => {
  // 基础榜单 field 格式 转为接口需要的格式
  const {
    id,
    configName,
    name,
    nameImgUrl,
    beginTime,
    endTime,
    workDay,
    timeSelection,
    timeRanges,
    boardCategory,
    minDisplayCount,
    maxRecallCount,
    shareContent,
    weight,
  } = value || {};
  let _nameImgUrl = "";
  if (nameImgUrl.length > 0 && nameImgUrl[0]) {
    _nameImgUrl = nameImgUrl[0].url;
  }

  const _timeRanges = [];
  if (timeRanges && timeRanges.length > 0) {
    timeRanges.map((timeRange) => {
      _timeRanges.push(`${timeRange.startTime}-${timeRange.endTime}`);
    });
  }

  const baseInfoReq = {
    id,
    configName,
    name,
    nameImgUrl: _nameImgUrl,
    beginTime: momentToTimeStamp(beginTime),
    endTime: momentToTimeStamp(endTime),
    workDay,
    timeSelection,
    timeRanges: _timeRanges,
    boardCategory,
    minDisplayCount,
    maxRecallCount,
    shareContent,
    weight,
  };
  return baseInfoReq;
};

export const formatBangDanField2Request = (value, i) => {
  // 榜单 field 格式 转为接口需要的格式
  const {
    fixedPureDataSource,
    itemCat,
    poolIds,
    topItemBrandIdListStr,
    supplyType,
    tabName,
  } = value;

  let _itemCat = [];
  let fixedPureDataSourceList = [];
  if (itemCat) {
    _itemCat = [...itemCat.hasSelectedDisplay];
  }

  if (fixedPureDataSource && fixedPureDataSource.length > 0) {
    fixedPureDataSourceList = fixedPureDataSource
      .map((item, _i) => {
        if (item) {
          let _fixedItemCat = [];
          if (item.itemCat) {
            _fixedItemCat = [...item.itemCat.hasSelectedDisplay];
          }
          // 产品确认，定坑设置非必填，无需校验
          return {
            tabIndex: _i + 1, // 提交的时候，tabIndex 要和页面的展示顺序一致。
            recallNum: item.recallNum,
            supplyType: item.supplyType,
            itemCat: _fixedItemCat,
            poolIds: item.poolIds,
          };
        } else {
          return null;
        }
      })
      .filter(Boolean);
  }

  const bangDanData = {
    tabIndex: i + 1, // 提交的时候，tabIndex 要和页面的展示顺序一致。
    fixedPureDataSourceList, // 固定坑位 —— 疯省榜、人气榜有，其他榜都没有
    itemCat: _itemCat,
    poolIds,
    shopList: [], // TODO：poolIds 对应的列表，找后端却是否有用到，前端是否可以不传
    topItemBrandIdListStr, // 指定品牌置顶 —— 品牌榜有，其他榜都没有。
    supplyType,
    tabName,
  };

  return bangDanData;
};

export const formatRecallRuleField2Request = (value) => {
  // 召回规则 field 格式 转为接口需要的格式
  const {
    topItemSortRule,
    shopScatter,
    openStatusSet,
    maxDeliveryPrice,
    shopMajorCategoryContent,
  } = value || {};
  const recallRuleData = {
    topItemSortRule,
    shopScatter,
    openStatusSet,
    maxDeliveryPrice,
    shopMajorCategoryContent,
  };
  return recallRuleData;
};

export const formatBaseInfoRes2Field = (value) => {
  // 接口数据转为 field 格式 —— 基础信息
  const {
    id,
    configName,
    name,
    nameImgUrl,
    beginTime,
    endTime,
    workDay,
    timeSelection,
    timeRanges,
    boardCategory,
    minDisplayCount,
    maxRecallCount,
    shareContent,
    weight,
  } = value || {};
  let _nameImgUrl = [];
  if (nameImgUrl) {
    _nameImgUrl = [
      {
        url: nameImgUrl,
      },
    ];
  }

  const _timeRanges = [];
  if (timeRanges && timeRanges.length > 0) {
    timeRanges.map((timeRange) => {
      _timeRanges.push({
        key: genId(),
        startTime: timeRange.split("-")[0],
        endTime: timeRange.split("-")[1],
      });
    });
  }

  const baseInfoField = {
    id,
    configName,
    name,
    nameImgUrl: _nameImgUrl,
    beginTime: beginTime ? moment(beginTime) : null,
    endTime: endTime ? moment(endTime) : null,
    workDay,
    timeSelection: +timeSelection,
    timeRanges: _timeRanges,
    boardCategory: +boardCategory,
    minDisplayCount,
    maxRecallCount,
    shareContent,
    weight,
  };
  return baseInfoField;
};

export const formatBangDanRes2Field = (value) => {
  // 接口数据转为 field 格式 —— 榜单数据
  const {
    tabIndex,
    fixedPureDataSourceList,
    itemCat,
    poolIds,
    shopList,
    topItemBrandIdListStr,
    supplyType,
    tabName,
  } = value || {};
  let _itemCat = [];
  let _fixedPureDataSource = [];
  if (itemCat) {
    _itemCat = {
      hasSelected: [...itemCat],
      hasSelectedDisplay: [...itemCat],
    };
  }
  if (fixedPureDataSourceList && fixedPureDataSourceList.length > 0) {
    _fixedPureDataSource = fixedPureDataSourceList.map((item, i) => {
      if (item) {
        return {
          // key 仅前端使用，作为唯一标识，
          key: genId(),
          recallNum: item.recallNum,
          supplyType: item.supplyType,
          itemCat: {
            hasSelected: [...item.itemCat],
            hasSelectedDisplay: [...item.itemCat],
          },
          poolIds: item.poolIds,
        };
      } else {
        return null;
      }
    });
  }
  const bangDanField = {
    // key 只前端使用，用来控制上下移动的顺序，
    key: genId(),
    tabIndex,
    fixedPureDataSource: _fixedPureDataSource,
    itemCat: _itemCat,
    poolIds,
    shopList, // TODO: 目前看没啥用，与后端确认是否可以删除
    topItemBrandIdListStr,
    supplyType: +supplyType,
    tabName,
  };
  return bangDanField;
};

export const formatRecallRuleRes2Field = (value) => {
  // 接口数据转为 field 格式 —— 召回规则
  const {
    topItemSortRule,
    shopScatter,
    openStatusSet,
    maxDeliveryPrice,
    shopMajorCategoryContent,
  } = value || {};
  const recallRuleField = {
    topItemSortRule: topItemSortRule ? +topItemSortRule : undefined, // 品牌榜没有这个字段
    shopScatter: +shopScatter,
    openStatusSet: openStatusSet
      ? openStatusSet.map((item) => +item)
      : undefined,
    maxDeliveryPrice,
    shopMajorCategoryContent,
  };
  return recallRuleField;
};

export const getValidBangDanCount = (list) => {
  if (list && list.length > 0) {
    let validCount = 0;
    const fieldList = list.map((item) => item.field).filter(Boolean);
    fieldList.forEach((item) => {
      if (item) {
        const { itemCat, poolIds } = item.getValues();
        if (itemCat || poolIds) {
          validCount++;
        }
      }
    });
    return validCount;
  } else {
    return 0;
  }
};

export const onItemDown = (key, list, updateList) => {
  // 通过 key 在 list 中找到当前元素，并且将当前元素与后一项交换
  const _value = [...list];
  const index = _value.findIndex((item) => item.key === key);
  if (index < _value.length - 1) {
    const temp = _value[index];
    _value[index] = _value[index + 1];
    _value[index + 1] = temp;
    updateList(_value);
  }
};

export const onItemRemove = (key, list, updateList) => {
  // 通过 key 在 list 中找到当前元素，并且将其删除
  const _value = [...list];
  const index = _value.findIndex((item) => item.key === key);
  _value.splice(index, 1);
  updateList(_value);
};

export const onItemUp = (key, list, updateList) => {
  // 通过 key 在 list 中找到当前元素，并且将当前元素与前一项交换
  const _value = [...list];
  const index = _value.findIndex((item) => item.key === key);
  if (index > 0) {
    const temp = _value[index];
    _value[index] = _value[index - 1];
    _value[index - 1] = temp;
    updateList(_value);
  }
};
