.announcement {
  height: 40px;
  line-height: 40px;
  position: fixed;
  z-index: 2;
  width: 100%;
  margin:0px 0px 5px;
  color: #333;
  text-align: center;
  //display: none;
  .copy{
    cursor: pointer;
  }
  .ver-slick .content {
    line-height: 20px;
    text-align: left;
    font-size: 12px !important;
    background-color: #cfe8e2;
    margin-top: 0;
    margin-bottom: 0;
    padding:0 10px;
    height:40px;
    max-width: calc(100vw - 198px);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.announceDialog{
  width:700px;
  .next-message-content{
    line-height: 24px !important;
    margin-top: 15px !important;
  }
}
