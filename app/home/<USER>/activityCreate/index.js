import React, {Fragment, useEffect, useState} from 'react';
import { Breadcrumb } from '@alife/next';
import './style.scss';
import {goldLog, logTimeComponent, track} from "@/utils/aplus";
import {Link, withRouter} from "react-router-dom";
import {permissionAccess} from "@/components/PermissionAccess";
import { ActivityEntity } from '@ali/luna-rmc';

function ActivityCreate({history,match}) {
  const {activityId} = match.params;
  console.log(activityId);

  const onSuccessCallBack = id => {
    console.log('onSuccessCallBack', id);
    location.href = '#/PutIn/list';
  };

  return (
    <div className='activity-create-container'>
      <div className="nav-wrapper">
        <Breadcrumb>
          <Breadcrumb.Item link="javascript:void(0);"> <Link to={"/putIn/list"}>投放活动</Link></Breadcrumb.Item>
          <Breadcrumb.Item link="javascript:void(0);">
            {(activityId && activityId != '') ? '编辑' : '新建'}投放活动
          </Breadcrumb.Item>
        </Breadcrumb>
      </div>
      <div className='activity-create'>
        <ActivityEntity style={{width:'700px'}}  fieldProps={{
          deliverySceneType: 'MEETING_PLACE',
        }} activityId={activityId} onSuccessCallBack={onSuccessCallBack}/>
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(ActivityCreate), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const ActivityCreatePage = permissionAccess(LogTimePutInPage)





