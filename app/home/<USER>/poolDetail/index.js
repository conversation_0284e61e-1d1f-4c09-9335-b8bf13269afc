import React from 'react';
import { Field } from '@alife/next';
import {deepCopy, PoolPageBase} from '../common';
import {PageWrapper} from '@/comps/PageWrapper';
import { BreadcrumbTips } from '../comps';

import '../intelligentPool/style.scss';
import {BaseInfo} from "../poolDetail/baseInfo";
import {PoolRules} from "../poolDetail/poolRules";
import * as api from "@/adator/api";
import {SamplePool} from "@/home/<USER>/comps/samplePool";
import {ViewStoreDetail} from "@/home/<USER>/comps/viewStoreDetail";
import {getQueryString} from "@/utils/others";

/*
* 详情页
* */
export class PoolDetailPage extends PoolPageBase {
  constructor(props) {
    super(props);

    this.field = new Field(this, {
      onChange: (name, value) => {
        // this.props.updateBaseInfo(name, value);
        this.setState({
          [name]:value
        })
      }
    });

    this.state = {
      location:props.location,
      step: 0,
      tabidx:1,
      total:0,
      isHiddenNav:false,
      breadcrumbList: [
        {"title": '选品集管理', link: "#/pool/list"},
        {"title": '选品集详情', link: ""}
      ],
      poolId: this.params.poolId || '',
      effectFilterFieldBizModelList: [],
      detailData:"",
      effectRules:{},
      isLoading:true,
      empId:'',
      isSelectedStore: getQueryString('isSelectedStore') === 'true'
    }
  }

  componentDidMount() {
    this.getUser();
    if (this.state.location.search.indexOf('hiddenNav=true') > -1) {
      let newBreadcrumbList = deepCopy(this.state.breadcrumbList)
      newBreadcrumbList[0] = {"title": '管理门店池', link: "#/storepoolInvite/list"}
      this.setState({breadcrumbList:newBreadcrumbList,isHiddenNav : true})
      let wrapper = document.querySelector(".app-wrapper");
      wrapper.classList.add("invite")
    }
  }


  async getUser(){
    try {
      api.getBucUser()
        .then((resp) => {
          this.setState({empId: resp.data.data.empId}, () => {
            this.getPoolDetail();
          })
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  async getPoolDetail() {
    const {poolId} = this.state;
    try {
      const request = api.getPoolDetailByPoolId;
      let resp = await request(poolId);
      this.setState({
        detailData:resp.data
      },()=>{
        this.ctrlRules();
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  nextStep = () => {
    this.setState({
      step: 1 + this.state.step
    })
  }

  prevStep = () => {
    this.setState({
      step: this.state.step - 1
    })
  }

  ctrlRules = () =>{
    let {detailData,effectRules} = this.state;
    if (detailData && detailData.effectRules && detailData.effectRules.length > 0) {
      detailData.effectRules.map((v) => {
        effectRules[v.filterFieldKey] = v.filterFieldValue;
      })
    }
    this.setState({
      effectRules
    })
  }

  resetViewList = () =>{
    this.setState({
      resultUpcIds:'',
      resultItemNames:'',
      resultItemIds:'',
      itemStatus:''
    },()=>{
      this.field.setValues({
        resultUpcIds:'',
        resultItemNames:'',
        resultItemIds:'',
        itemStatus:''
      })
    })
  }

  onSort = (key, order) => {
    this.setState({
      orderBy: key,
      orderDirection: order
    }, () => {
      this.changeTabidx(this.state.tabidx)
    })
  }

  onRef = (ref) => {
    this.sampleRef = ref;
  }

  render() {
    const {breadcrumbList, poolId,detailData,effectRules,empId, isSelectedStore} = this.state;

    return (
      <PoolPageBase.Container className="pool-detail">
        <BreadcrumbTips list={breadcrumbList} />
        <PageWrapper className="supply">
          {(detailData && detailData !== "") && <BaseInfo　getPoolDetail={this.getPoolDetail} detailData={detailData} {...this.props} empId={empId} poolId={poolId} isHiddenNav={this.state.isHiddenNav}/>}
          {(detailData && detailData !== "") && <PoolRules detailData={detailData} effectRules={effectRules} sourcePoolId={detailData.sourcePoolId}/>}
          <div className="section">
            {detailData && detailData !== "" && (isSelectedStore ? <ViewStoreDetail onRef={this.onRef}  type={2} effectRules={effectRules} refreshMode={detailData.refreshMode} poolId={this.state.poolId}  sourcePoolId={detailData.sourcePoolId} />
              :<SamplePool 
                addGoodsBtnDisabled={+detailData.state === 25} // 25 已下线
                effectRules={effectRules} 
                onRef={this.onRef}  
                poolId={this.state.poolId}  
                refreshMode={detailData.refreshMode} 
                sourcePoolId={detailData.sourcePoolId} 
                type={2} 
              />)
              }
          </div>
        </PageWrapper>
      </PoolPageBase.Container>
    )

  }
}
