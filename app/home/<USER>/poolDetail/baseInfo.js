import React, {Fragment} from 'react';
import {<PERSON>rid, Button, Dialog, Message} from '@alife/next';
import './style.scss';
import {
  publishStatusEnum,
  changeEumToObject,
  poolTypeEnum,
  poolEum,
  refreshModeListEum,
  stateMap,
  getTreeName,
  getPermission
} from '../common'
import * as api from "@/adator/api";
import {BalloonTime,BalloonInput} from '../common/components';
const { Row, Col } = Grid;
import {formatTimeStamp,FORMAT} from "@/utils/time";
import moment from "moment";
import {getQueryString} from "@/utils/others";
import {AccessBtn} from "@/components/Button";

/*
* 详情页-基本信息
* */
export class BaseInfo extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isHiddenNav: props.isHiddenNav,
      detailData: props.detailData,
      outSynPlatformsDs:[],
      outSynPlatformsValue:'',
      sceneClassName:[]
    }
  }

  componentDidMount() {
    this.fetchOutSynPlatforms();
    this.fetchSceneCatTree()
  }

  componentWillReceiveProps(newProps) {
    this.setState({detailData: newProps.detailData});
  }

  onEdit = () =>{
    let {detailData} = this.state;
    switch (detailData.createMode) {
      case '文件上传':
        this.props.history.push(`/pool/list/dataSetUpload/${detailData.poolId}`);
        break;
      case '标签选品':
        this.props.history.push(`/pool/list/tagPool/${detailData.poolId}`);
        break;
      case '标签选店':
        this.props.history.push(`/pool/list/selectedStore/${detailData.poolId}`);
        break;
      case '智能选品':
        this.props.history.push(`/pool/list/intelligentPool/${detailData.poolId}`);
        break;
      default:
        break;
    }
  }

  onRemove = () =>{
    Dialog.confirm({
      title: '提示',
      content: '确定删除此条数据么?',
      onOk: async () => {
        let request = api.deletePool;
        try {
          await request(this.props.poolId)
          Message.success('操作成功')
          this.props.history.push(`/pool/list`);
          // location.reload();
        } catch (error) {
          api.onRequestError(error)
        }
      }
    });
  }

  onPublish = async() =>{
    let request = api.publishPool;
    try {
      await request(this.props.poolId)
      Message.success('操作成功')
      location.reload();
    } catch (error) {
      api.onRequestError(error)
    }
  }

  onOffLine = () =>{
    Dialog.confirm({
      title: '提示',
      content: '确定下线此条数据么?',
      onOk: async () => {
        let request = api.offLinePool;
        try {
          await request(this.props.poolId)
          Message.success('操作成功');
          location.reload();
        } catch (error) {
          api.onRequestError(error)
        }
      }
    });
  }

  fetchOutSynPlatforms= async () => {
    let {outSynPlatformsDs,detailData} = this.state;
    outSynPlatformsDs = [];
    try {
      let request = api.newSynPoolPlatformList;
      let resp = await request(detailData.sourcePoolId);
      if(resp.data.data.data.length>0){
        // outSynPlatformsDs = resp.data.data.data.map((v)=>{
        //   return {
        //     label:v.label,
        //     value:v.platformCode
        //   }
        // })
      }
      this.setState({
        outSynPlatformsDs:resp.data.data.data
      })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  /*获取场景分类*/
  fetchSceneCatTree = async () => {
    try {
      if (this.state.sceneClassName.length <= 0) {
        let request = api.queryNewSceneCatTree;
        let resp = await request();
        this.setState({
          sceneClassName:resp.data.data.data
        })
      }
    } catch (error) {
      api.onRequestError(error);
    }
  };
  
  onAnalyse = () =>{
    this.props.history.push(`/pool/list/analyse/${this.state.detailData.poolId}`);
  }

  onCopy = () =>{
    this.props.history.push(`/pool/list/tagPool/-${this.state.detailData.poolId}`);
  }

  render() {
    let {detailData, empId} = this.props;
    let {state, outSynPlatforms, noticeUid} = detailData;
    let {outSynPlatformsValue, outSynPlatformsDs} = this.state;
    let newOutSynPlatformsDsStr;
    if (outSynPlatformsDs.length > 0) {
      newOutSynPlatformsDsStr = JSON.stringify(outSynPlatformsDs).replace(/platformCode/g, "value");
      outSynPlatformsValue = outSynPlatforms.map((v) => getTreeName(JSON.parse(newOutSynPlatformsDsStr), v)).join(",");
    }
    let leftDay = moment(detailData.expireAt).diff(moment(), 'day'); // 如果昨天的时间戳和今天的实际时间相差不到一天，那么diff函数也会返回0， 所以今天和昨天都会返回0.
    const hasExpired = moment(detailData.expireAt).isBefore(moment()); // 是否已过期
    // let downTimeDiff = moment().diff(moment(detailData.operateDate), 'months');
    // let showBtnAnalyse = (state == 15 || state == 1 || (state == 25 && downTimeDiff <= 3)) && detailData.sourcePoolId !='41001';
    let showBtnAnalyse = (getQueryString('showBtnAnalyse') == 1);

    let newNoticeUid = noticeUid;
    if (noticeUid.indexOf(empId) >= 0) {
      const index = noticeUid.split(',').findIndex((o) => o == empId);
      const noticeUidGroup = noticeUid.split(',');
      noticeUidGroup.splice(index, 1);
      newNoticeUid = noticeUidGroup.join(',');
    }
    return (
      <div className="base-info">
        <Row style={{marginBottom:'30px'}}>
          <Col span={12}>
            <div>
              <span className="name">{detailData.poolName}</span>
              <span className={`status ${stateMap[detailData.state]}`}>{changeEumToObject(publishStatusEnum)[detailData.state]}</span>
            </div>
            <div className="id">ID:{detailData.poolId}</div>
          </Col>
          {!this.state.isHiddenNav && <Col span={12} className="btn-panel">
            {(state == 0 || state == 1) &&  <AccessBtn getPermission={()=>getPermission(detailData.poolId)}  btnText={'发布'} text={false}  callback={this.onPublish}/>}
            {(state == 10 || state == 15) && <AccessBtn getPermission={()=>getPermission(detailData.poolId)} btnText={'下线'} text={false} type={'normal'} callback={this.onOffLine}/>}
            {(showBtnAnalyse) && <Button onClick={this.onAnalyse}>洞察</Button>}
            {(detailData.createMode != '文件上传' && detailData.createMode == '标签选品') && <AccessBtn getPermission={()=>getPermission(detailData.poolId)} btnText={'复制'} text={false} type={'normal'} callback={this.onCopy}/>}
            {state != 25 && state != 5 && (
                <AccessBtn
                  // 百亿补贴相关底池已经下线，这类底池禁止编辑
                  disabled={["41001", "24005"].includes(`${detailData.sourcePoolId}`)}
                  getPermission={() => getPermission(detailData.poolId)}
                  btnText={"编辑"}
                  text={false}
                  type={"normal"}
                  callback={this.onEdit}
                />
              )}
            <AccessBtn getPermission={()=>getPermission(detailData.poolId)} btnText={'删除'} text={false} type={'normal'} callback={this.onRemove}/>
          </Col>}
          {/*<Col span={12} className="btn-panel">*/}
          {/*  <AccessBtn getPermission={this.getPermission}  btnText={'发布'} text={false}  callback={this.onPublish}/>*/}
          {/*  <AccessBtn getPermission={this.getPermission} btnText={'下线'} text={false} type={'normal'} callback={this.onOffLine}/>*/}
          {/*  <Button onClick={this.onAnalyse}>洞察</Button>*/}
          {/*  <AccessBtn getPermission={this.getPermission} btnText={'复制'} text={false} type={'normal'} callback={this.onCopy}/>*/}
          {/*  <AccessBtn getPermission={this.getPermission} btnText={'编辑'} text={false} type={'normal'} callback={this.onEdit}/>*/}
          {/*  <AccessBtn getPermission={this.getPermission} btnText={'删除'} text={false} type={'normal'} callback={this.onRemove}/>*/}
          {/*</Col>*/}
        </Row>
        <Row>
          <Col span={12}>
            <label>应用渠道：</label>
            <span>{detailData.outSynPlatforms ? outSynPlatformsValue : '--'}</span>
          </Col>
          <Col span={12}>
            <label>营销场景：</label>
            <span>{detailData.catPath ? detailData.catPath : '--'}</span>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <label>类型：</label>
            <span>{changeEumToObject(poolTypeEnum)[detailData.poolType]}</span>
          </Col>
          <Col span={12}>
            <label>更新机制：</label>
            <span>{changeEumToObject(refreshModeListEum)[detailData.refreshMode]} (最近更新{formatTimeStamp(moment(detailData.operateDate))})</span>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <label>底池：</label>
            <span>{detailData.sceneBaseId?detailData.sceneName:poolEum.map((item)=>{
              if (item.id == detailData.sourcePoolId) {
                return item.title
              }
            })}</span>
          </Col>
          <Col span={12}>
            <label>有效期：</label>
            <span>{formatTimeStamp(moment(detailData.effectAt),FORMAT.DATE)} ~ {formatTimeStamp(moment(detailData.expireAt),FORMAT.DATE)}</span>
            {!hasExpired ? <span className="left">(剩余<em className="day">{leftDay}</em>天过期)<BalloonTime poolId={detailData.poolId} effectAt={detailData.effectAt} expireAt={detailData.expireAt}/></span> :
              <span className="left" style={{marginLeft:'5px'}}>已过期</span>}
            {detailData.isNotice == 1 && <p style={{marginLeft: '75px'}} className="left info-notice"> <BalloonInput type={3} poolId={detailData.poolId} noticeUid={newNoticeUid} /></p>}
            {detailData.isNotice == 0 && <p style={{marginLeft: '75px'}} className="left"><BalloonInput type={2} poolId={detailData.poolId} /></p>}
          </Col>
        </Row>
      </div>
    )

  }
}
