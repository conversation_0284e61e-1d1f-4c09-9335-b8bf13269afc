@import '../../../styles/common.scss';
.pool-detail{
  .base-info{
    background-color: #fff;
    padding:24px;
    .left{
      font-family: PingFangSC-Regular !important;
      font-size: 14px;
      color: #999;
      font-weight: normal;
      .day{
        color: #FF7000;
        font-style: normal;
      }
    }
    .next-row{
      margin:15px 0;
    }
    .btn-panel{
      .next-btn{
        float:right;
        margin-left: 5px;
      }
    }
    .info-notice{
      display: inline-block;
      border: 1px solid #fff;
      padding:4px;
      &:hover{
        cursor: pointer;
        background: rgba(255,112,0,0.04);
        border: 1px solid rgba(255,112,0,0.40);
        border-radius: 4px;
      }
    }
    .name{
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #363636;
      font-style: normal;
    }
    .status{
      background: rgba(0,204,102,0.04);
      border: 1px solid #00CC66;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      border-radius: 2px;
      //color: #00CC66;
      padding:0 4px;
      display: inline-block;
      margin-left: 12px;

      &.success {
        color: $success_high;
        border-color: $success_high;
      }

      &.notice {
        color: $notice_high;
        border-color: $notice_high;
      }

      &.warning {
        color: $warning_high;
        border-color: $warning_high;
      }

      &.error {
        color: $error_high;
        border-color: $error_high;
      }

      &.help {
        color: $help_high;
        border-color: $help_high;
      }

      &.filed {
        color: $font_tip;
        border-color: $font_tip;
      }

    }
    .id{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #666666;
    }
    label{
      width:70px;
      text-align: right;
      display: inline-block;
    }
  }
  .pool-rules{
    padding:24px;
    background-color: #fff;
    margin-top: 10px;

    .next-row{
      margin:15px 0;
      .title{
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #333333;
      }
      .name{
        font-family: PingFangSC-Medium;
        font-size: 20px;
        color: #363636;
        font-style: normal;
      }
      label{
        width:90px;
        text-align: right;
        display: inline-block;
      }
    }

  }
  .pool-rules-label{
    padding:24px;
    background-color: #fff;
    margin-top: 10px;
    overflow: hidden;
    .title{
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
    }
    .name{
      font-family: PingFangSC-Medium;
      font-size: 20px;
      color: #363636;
      font-style: normal;
    }
    .item{
      padding:0 10px 10px 0;
      &>label{
        float: left;
        //width:150px;
        //text-align: right;
        display: block;
      }
      &>span{
        word-break:break-all;
        position: relative;
        top: 2px;
      }
    }
  }
  .section{
    background-color: #fff;
    margin-top:10px;
    padding: 24px;
    .total-info{
      background-color: #fff;
      font-family: PingFangSC-Medium;
      font-size: 16px;
      color: #333333;
      margin-bottom:24px;
      &>span{
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #999999;
        margin-left: 8px;
      }
    }
  }
}
