import React from 'react';
import {Grid} from '@alife/next';
import {
  changeEumToObject, changeToYuan,
  crowdEum,
  dealQualityScoreToArray, getTreeName,
  isCatCompEum,
  isRiskCompEum,
  timeEum 
} from '../common/index';

import './style.scss';
import { MktConditionsPreview } from "@/components/controls/MktConditions";
import {optionMap} from "@/home/<USER>/common/map";
import * as api from "@/adator/api";
import { deepCopy, fetchGroupOption } from "@/home/<USER>/common";
import { getFilterFieldPolicy } from '@/selection';
const {Row, Col} = Grid;

/*
* 详情页-规则
* */
export class PoolRules extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      step: 0,
      isShowRules: false,
      sourcePoolId:props.sourcePoolId || '',
      newOptionMap : []
    }
  }

  componentDidMount() {
    if(this.state.sourcePoolId){
      console.log("🚀 ~ file: poolRules.js:40 ~ PoolRules ~ componentDidMount ~ sourcePoolId:", this.state.sourcePoolId)
      this.getRulesBySource()
    }
  }

  showMoreConfig = () => {
    this.setState({
      isShowRules: true
    })
  }

  getRulesBySource = async () =>{
    try {
      if (this.state.sourcePoolId) {
        let resp = await api.getRulesBySource(this.state.sourcePoolId);
        let requestList = []
        let newOptionMap = []
        console.log("🚀 ~ file: poolRules.js:56 ~ PoolRules ~ getRulesBySource= ~ resp:", resp)

        resp.data && resp.data.map((item) => {
          if (+item.filterFieldDataType === 1) {
            newOptionMap[item.filterFieldId]= JSON.parse(item.filterFieldData);
          } else if (+item.filterFieldDataType === 2) {
            requestList[item.filterFieldId] = item.filterFieldData;
          } else {
            newOptionMap[item.filterFieldId] = optionMap[item.filterFieldKey];
          }
        });
        // 批量请求底池规则和圈选规则中为动态数据源的数据
        (async () => {
          let promiseList = [];
          Object.keys(requestList).forEach(async (key) => {
            promiseList.push(fetchGroupOption(key, requestList[key]));
          });
          const resolveList = await Promise.all(promiseList);
          resolveList.map((item) => {
            Object.keys(item).forEach((keyItem) => {
              newOptionMap[keyItem] = item[keyItem] || [];
            });
          });
          this.setState({newOptionMap:deepCopy(newOptionMap)})
        })();
        }
      // 圈选池子相关指标
    } catch (error) {
      api.onRequestError(error);
    }
  }

  renderFilterRules = (key, value) => {
    let result = '--';
    if(value || value ==='') {
      if (key === 'timePeriod') {
        result = (JSON.parse(value) && JSON.parse(value).length > 0) ? JSON.parse(value).map((m) => changeEumToObject(timeEum)[m]).join(",") : '--';
      } else if (key === 'crowd') {
        result = (JSON.parse(value) && JSON.parse(value).length > 0) ? JSON.parse(value).map((m) => changeEumToObject(crowdEum)[m]).join(",") : '--';
      } else if (key === 'isCatComp') {
        // result = changeEumToObject(isCatCompEum)[JSON.parse(value)];
        result = changeEumToObject(isCatCompEum)[value];
      } else if (key === 'isRiskComp') {
        result = changeEumToObject(isRiskCompEum)[value];
      } else if (key === 'goodsCategory' || key === 'newMainCategoryId') {
        result = (JSON.parse(value) && JSON.parse(value).length > 0) ? JSON.parse(value).map((m) => m.label).join(",") : '--';
      } else if (key === 'item_name' || key === 'filter_item_name') {
        result = (JSON.parse(value) && JSON.parse(value).length > 0) ? JSON.parse(value).join(",") : '--';
      }
    }
    return result;
  }

  getLabelItem = (item) =>{
    const filterFieldComponentType = item.filterFieldComponentType;
    let result;
    if (item.filterFieldValue || item.filterFieldValue === '') {
      let options = this.state.newOptionMap[item.filterFieldKey] || optionMap[item.filterFieldKey];
      switch (filterFieldComponentType) {
        case 'arrayInput': //文本框
          result = <span>{JSON.parse(item.filterFieldValue) instanceof Array ? JSON.parse(item.filterFieldValue).join(","):JSON.parse(item.filterFieldValue)}</span>
          break;
        case 'radio': //单选框
        case 'select': //单选下拉框
          result = <span>{changeEumToObject(options)[item.filterFieldValue]}</span>
          break;
        case 'cascaderSelect': //下拉级联多选
        case 'multipleSelect': //下拉多选
        case 'picStandard': //质量分
        case 'selectSearch': //带搜索的下拉多选
          result = <span>{this.getCascaderContent(item)}</span>
          break;
        case 'rangeNumberInput': //店铺评分类
          const valueRange = changeToYuan(item);
          result = <span>{valueRange.start}~{valueRange.end}</span>
          break;
        case 'checkbox': //复选框
          const value = (item.filterFieldValue) ? JSON.parse(item.filterFieldValue).map((m) => changeEumToObject(options)[m]).join(",") : '';
          result = <span>{value}</span>
          break;
        case 'mktConditions':
          result = (
            <MktConditionsPreview
              value={item.filterFieldValue}
              dataSource={options}
            />
          );
          break;
        default:
          const policy = getFilterFieldPolicy(filterFieldComponentType)
          if (policy) {
            result = policy.renderPreview(
              {
                value: item.filterFieldValue,
                dataSource: options,
              },
              "detailPage",
            );
          } else {
            result = <span>{item.filterFieldValue}</span>
          }
          break;
      }
    }else{
      result = <span></span>
    }
    return result;
  }

  getCascaderContent = (item) => {
    let result = "";
    if(item && item.filterFieldValue) {
      let values = JSON.parse(item.filterFieldValue);
      if (item.filterFieldComponentType === "picStandard") {
        values = dealQualityScoreToArray(JSON.parse(item.filterFieldValue));
      }
      if (values && values.length > 0) {
        result = values.map((v) => {
          let labelValue = v.label;
          if (item.filterFieldComponentType === "picStandard") {
            labelValue = getTreeName(optionMap['item_pic_standard'], v.value);
          }
          return labelValue;
        }).join(",");
      }
    }
    return result;
  }

  render() {
    // const {step, isShowRules} = this.state;
    let {detailData} = this.props;
    if (detailData && detailData.createMode === '智能选品') {
      let {effectRules} = this.props;
      return <div className="pool-rules">
        <Row style={{marginBottom: '30px'}}>
          <Col span={12}>
            <div>
              <span className="title">圈选规则</span>
            </div>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <label>人群：</label>
            <span>{this.renderFilterRules("crowd", effectRules.crowd)}</span>
          </Col>
          <Col span={12}>
            <label>风控等级：</label>
            <span>{this.renderFilterRules("isRiskComp", effectRules.isRiskComp)}</span>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <label>时段：</label>
            <span>{this.renderFilterRules('timePeriod', effectRules.timePeriod)}</span>
          </Col>
          <Col span={12}>
            <label>正确类目：</label>
            <span>{this.renderFilterRules('isCatComp', effectRules.isCatComp)}</span>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <label>商品类目：</label>
            <span>{this.renderFilterRules('goodsCategory', effectRules.goodsCategory)}</span>
          </Col>
          <Col span={12}>
            <label>门店主营：</label>
            <span>{this.renderFilterRules("newMainCategoryId", effectRules.newMainCategoryId)}</span>
          </Col>
        </Row>
        <Row>
          <Col span={12}>
            <label>商品名称：</label>
            <span>{this.renderFilterRules('item_name', effectRules.item_name)}</span>
          </Col>
          <Col span={12}>
            <label>剔除关键词：</label>
            <span>{this.renderFilterRules("filter_item_name", effectRules.filter_item_name)}</span>
          </Col>
        </Row>
      </div>
    } else if (detailData && detailData.createMode === '标签选品') {
      let {effectRules} = detailData;
      return <div className="pool-rules-label">
        <Row style={{marginBottom: '30px'}}>
          <Col span={12}>
            <div>
              <span className="title">圈选规则</span>
            </div>
          </Col>
        </Row>
        <Row wrap>
          {(effectRules && effectRules.length > 0) && effectRules.map((v) => {
            return <Col span="8" className="item">
              <label>{v.filterFieldLabel}：</label>
              {this.getLabelItem(v)}
            </Col>
          })}
        </Row>
      </div>
    } else if (detailData && detailData.createMode === '标签选店') {
      let {effectRules} = detailData;
      return <div className="pool-rules-label">
        <Row style={{marginBottom: '30px'}}>
          <Col span={12}>
            <div>
              <span className="title">圈选规则</span>
            </div>
          </Col>
        </Row>
        <Row wrap>
          {(effectRules && effectRules.length > 0) && effectRules.map((v) => {
            return <Col span="8" className="item">
              <label>{v.filterFieldLabel}：</label>
              {this.getLabelItem(v)}
            </Col>
          })}
        </Row>
      </div>
    } else {
      return null;
    }
  }
}
