import React, {Fragment, useEffect, useState} from 'react';
import {Button, Dialog, Grid} from '@alife/next';
import {withRouter} from "react-router-dom";
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {permissionAccess} from "@/components/PermissionAccess";
import RankSet from "@/containers/channel/market/comps/rankSet";
import './style.scss';
import {BreadcrumbTips} from "@/home/<USER>/comps";
import SchemaForm from "@/utils/Form/src";
import RankDetail from "@/containers/channel/market/comps/rankDetail";
import SimpleTable from "@/components/SimpleTable";
import {
  baseQuerySceneSchemaDTO, filterResponseDTO,
  flattenObject,
  getChannelLabel,
  getChannelList,
  getCity,
  handleField
} from "@/home/<USER>/common";
import {versionDetailGroup} from "@/containers/channel/market/common";
import {getNormalTemplateSchema} from "@/home/<USER>/setSchema/request";
import * as api from "@/utils/api";
import {getDetailSceneCard} from "@/adator/api";
const {Row, Col} = Grid
/**
 * 榜单创建表单
 * @param props
 * @returns {JSX.Element}
 */


function sceneView(props) {
  const breadcrumbList = [
    {"title": '场景管理', link: "#/channelManage/gallery/sceneManage"},
    {"title": '查看场景', link: ""}
  ];
  const sceneId = props.match.params.id || '';
  const [scheduleTemplate, setScheduleTemplate] = useState({});
  const [fieldKeys, setFieldKeys] = useState({});
  const [scheduleInfo, setScheduleInfo] = useState({});

  useEffect(()=>{
    getNormalSchema();
  },[])

  /**
   * 非资源位的获取元数据schema——常规链路
   * */
  const getNormalSchema = () => {
    getNormalTemplateSchema(baseQuerySceneSchemaDTO)
      .then((result) => {
        let scheduleTemplate = JSON.parse(result);
        setScheduleTemplate(scheduleTemplate);
        setFieldKeys(scheduleTemplate.detail.properties);
        if(sceneId) {
          getDetail(scheduleTemplate);
        }
      })
      .catch(api.onRequestError);
  }

  const getDetail = (schedule) => {
    if(sceneId) {
      getDetailSceneCard({sceneId: sceneId})
        .then((result) => {
          const _detailData = filterResponseDTO(schedule,result);
          console.log('_detailData',_detailData);
          setScheduleInfo(_detailData);
        })
        .catch(api.onRequestError);
    }
  }

  return (
    <div className={'container'}>
      <BreadcrumbTips list={breadcrumbList}/>
      <div className="resource-body">
        <Row>
          <Col offset="3" span="18">
            <SimpleTable
              title={'基础信息'}
            >
              {Object.keys(fieldKeys).map((o) => {
                if (o != 'timeRanges') {
                  return <SimpleTable.Item label={fieldKeys[o].title}>
                    {handleField(o, scheduleInfo, fieldKeys[o])}
                  </SimpleTable.Item>
                }
              })}
            </SimpleTable>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(sceneView), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const SceneViewPage = permissionAccess(LogTimePutInPage)

