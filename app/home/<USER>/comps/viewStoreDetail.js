import React, { Fragment } from "react";
import {
  Button,
  Tab,
  Table,
  Input,
  Select,
  Icon,
  Pagination,
  Message,
  Dialog,
  Grid,
  Form,
  Field,
  Loading,
  Radio,
} from "@alife/next";
import { PageBase } from "../../base";
import "./index.scss";
import { FORMAT, formatTimeStamp } from "@/utils/time";
import PreviewImage from "@/components/PreviewImage";
import * as validators from "@/utils/validators";
import * as api from "@/adator/api";
import {
  getPermission,
  itemStatusEnum,
  operatorEum,
  poolEum,
  renderNewStateMap,
} from "@/home/<USER>/common";
import { validatorMap } from "@/home/<USER>/common/config";
import { renderShopStateMap } from "@/home/<USER>/common/index";
import { deepClone, promisify } from "@/utils/others";
import { track } from "@/utils/aplus";
import {
  CommodityColumns,
  CommodityTransformColumns,
  FailCommodityColumns,
  FailStoreColumns,
  NewDetailCommodityColumns,
  NewDetailStoreColumns,
  StoreColumns,
} from "@/containers/PoolPage";
import { AccessBtn } from "@/components/Button";

const DEFAULT_GOODS_IMG = require("../../../images/default-goods-pic.png");
const PAGE_SIZE = 50;
const { Row, Col } = Grid;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: {
    span: 17,
  },
  style: {
    width: "100%",
  },
  labelAlign: "center",
  labelTextAlign: "center",
};

/*
 * 选店详情
 */
export class ViewStoreDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      tabidx: 0, //0:初选门店，2：删除门店
      pagination: {
        page: 1,
        size: PAGE_SIZE,
        total: 0,
        pageTotal: 0,
      },
      rowSelection: {
        onChange: this.onSelectChange,
        onSelect: function (selected, record, records) {},
        onSelectAll: function (selected, records) {},
        selectedRowKeys: [],
        // getProps: record => {
        //   return {
        //     disabled: record.id === 100306660941
        //   };
        // }
      },
      type: props.type, //:1 创建页面，2：详情页面
      columns: [
        //淘内-门店池详情-查询结果
        {
          title: "门店",
          dataIndex: "shopName",
          lock: "left",
          width: 220,
          cell: (name, index, record) => {
            // let style = record.filterKeyword ? {color: "red", "text-decoration": "line-through"} : {};
            return (
              <Row>
                <Col span={36}>
                  <p className="name">{name ? name : "-"}</p>
                  <p className="tips">ELE门店ID:{record.shopId}</p>
                  <p className="tips">淘内门店ID:{record.storeId}</p>
                  <p className="tips">淘系卖家ID:{record.sellerId}</p>
                </Col>
              </Row>
            );
          },
        },
        { title: "标签", dataIndex: "storeLabel", width: 100 },
        { title: "所属品牌", dataIndex: "brand", width: 100 },
        {
          title: "营业状态",
          dataIndex: "businessStatus",
          width: 120,
          cell: (state) => renderShopStateMap(state),
        },
        { title: "门店7天销量", dataIndex: "sevenDayOrderNum", width: 140 },
        { title: "所在城市", dataIndex: "city", width: 140 },
        { title: "门店主营类目", dataIndex: "mainCategoryName", width: 140 },
        { title: "SKU数量", dataIndex: "skuCnt", width: 140 },
        { title: "店铺评分", dataIndex: "storeScore", width: 140 },
        {
          title: "店铺上月综合评分",
          dataIndex: "shopGeneralLevelLastMonthName",
          width: 140,
        },
        {
          title: "操作",
          lock: "right",
          cell: (value, index, record) => {
            return this.renderOpt(record);
          },
          width: "80px",
        },
      ],
      failColumns: [
        //淘内-门店池详情-查询结果
        {
          title: "门店",
          dataIndex: "shopName",
          lock: "left",
          width: 220,
          cell: (name, index, record) => {
            // let style = record.filterKeyword ? {color: "red", "text-decoration": "line-through"} : {};
            return (
              <Row>
                <Col span={36}>
                  <p className="name">{name ? name : "-"}</p>
                  <p className="tips">ELE门店ID:{record.shopId}</p>
                  <p className="tips">淘内门店ID:{record.storeId}</p>
                  <p className="tips">淘系卖家ID:{record.sellerId}</p>
                </Col>
              </Row>
            );
          },
        },
        { title: "标签", dataIndex: "storeLabel", width: 100 },
        { title: "所属品牌", dataIndex: "brand", width: 100 },
        {
          title: "营业状态",
          dataIndex: "businessStatus",
          width: 120,
          cell: (state) => renderShopStateMap(state),
        },
        { title: "门店7天销量", dataIndex: "sevenDayOrderNum", width: 140 },
        { title: "所在城市", dataIndex: "city", width: 140 },
        { title: "门店主营类目", dataIndex: "mainCategoryName", width: 140 },
        { title: "SKU数量", dataIndex: "skuCnt", width: 140 },
        { title: "店铺评分", dataIndex: "storeScore", width: 140 },
        {
          title: "店铺上月综合评分",
          dataIndex: "shopGeneralLevelLastMonthName",
          width: 140,
        },
        { title: "失败原因", dataIndex: "message", width: 140 },
      ],
      effectRules: props.effectRules,
      sourcePoolId: props.sourcePoolId,
      isLoading: true,
      tempPoolId: props.tempPoolId || "",
      poolId: props.poolId || "",
      effectFilterFieldBizModelList: [
        {
          filterFieldId: "storeId",
          filterFieldKey: "storeId",
          filterFieldValue: JSON.stringify([]),
          operator: operatorEum["storeId"],
        },
      ],
      isSearch: false,
      dataSource: [],
      deleteDataSource: [],
      failedDataSource: [],
      filterKey: "storeId",
      filterValue: "",
      isMore: false,
      isDialogBatchAddInputVisible: false,
      refreshMode:
        props.refreshMode || props.refreshMode == 0 ? props.refreshMode : -1,
    };

    this.field = new Field(this, {
      onChange: (name, value) => {
        // this.props.updateBaseInfo(name, value);
        this.setState({
          [name]: value,
        });
      },
    });

    this.viewField = new Field(this, {});

    this.batchInputField = new Field(this, {});
  }

  createColumn = (tabidx) => {
    let result;
    if (tabidx == 3) {
      result = this.state.failColumns;
    } else {
      result = this.state.columns;
    }
    return result;
  };

  renderOpt = (record) => {
    let { tabidx, type, sourcePoolId, poolId } = this.state;
    const current = poolEum.filter((v) => v.id == sourcePoolId);
    const isReal =
      current && current.length > 0 ? Boolean(current[0].type == 2) : false;
    let delBtn = (
      <AccessBtn
        getPermission={() => getPermission(poolId)}
        btnText={"删除"}
        callback={() => this.deleteGood(record.storeId)}
      />
    );
    let cancelDelBtn = (
      <AccessBtn
        getPermission={() => getPermission(poolId)}
        btnText={"取消删除"}
        callback={() => this.deleteGood(record.storeId)}
      />
    );
    //初选门店 status为1 都有   预览的删除门店  4种情况
    if (tabidx === 0) {
      //tab为初选门店
      if (type == 1) {
        //预览
        return (
          <div className="opbtns">
            {record.status == 1 && delBtn}
            {record.status == 0 && <span>已删除</span>}
            {record.status == -1 && <span>已剔除</span>}
          </div>
        );
      } else {
        //详情
        return (
          <div className="opbtns">
            {record.status == 1 && sourcePoolId != "41001" && delBtn}
          </div>
        );
      }
    } else if (tabidx === 2) {
      //tab为删除门店
      if ((type == 1 || type == 2) && !isReal) {
        //预览
        return <div className="opbtns">{cancelDelBtn}</div>;
      }
    } else if (tabidx === 1 || tabidx === 3) {
      if (type == 2) {
        //预览
        return (
          <div className="opbtns">
            {record.status == 1 && sourcePoolId != "41001" && delBtn}
          </div>
        );
      }
    }

    // return <div className="opbtns">
    //   {((tabidx==1 && record.status == 1) || (type == 1 && tabidx == 2)) && <Button type={'primary'} text onClick={() => this.deleteGood(record.storeId)}>{`${tabidx == 1 ? '删除' : '取消删除'}`}</Button>}
    //   {(tabidx==1 && record.status == 0) && <span>已删除</span>}
    //   {(tabidx==1 && record.status == -1) && <span>已剔除</span>}
    // </div>
  };

  onSort = (key, order) => {
    this.setState(
      {
        orderBy: key,
        orderDirection: order,
      },
      () => {
        this.getTableData();
      }
    );
    // this.props.onSort(dataIndex, order);
  };

  onSelectChange = (ids, records) => {
    const { rowSelection } = this.state;
    rowSelection.selectedRowKeys = ids;
    this.setState({ rowSelection });
  };

  deleteGood = async (storeId) => {
    let { tabidx, type, poolId, tempPoolId, sourcePoolId, refreshMode } =
      this.state;
    let text = tabidx === 0 ? "删除" : "取消删除";
    Dialog.confirm({
      title: "提示",
      content: `确定${text}么?`,
      onOk: async () => {
        try {
          let req = {
            poolType: "shop",
            addOrDelete: 1,
            targetIdList: [storeId],
            // operateType: 0
          };
          if (type == 1) {
            // 创建
            req.tempPoolId = tempPoolId;
            req.operateType = 3;
            req.addOrDelete = tabidx === 0 ? 1 : 0;
          } else {
            //内页
            req.sourcePoolId = sourcePoolId;
            req.poolId = poolId;
            req.operateType = tabidx == 2 ? 1 : 2;
            // req.addOrDelete = (tabidx === 0) ? 1 : 0;
            req.addOrDelete = 1;
            req.dataFlag = true;
            if (tabidx === 1 || tabidx == 2) {
              req.isRemove = 1;
            }
          }
          req.refreshMode = refreshMode;
          let request = api.manualUpdate;
          let resp = await request(req);
          if (resp.success) {
            Message.success(`${text}成功`);
            this.initTable();
          } else {
            Message.warning(resp.errMessage || `${text}失败`);
          }
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  initTable = () => {
    this.getTableData();
    let { rowSelection } = this.state;
    rowSelection.selectedRowKeys = [];
    this.setState({ rowSelection });
  };

  batchDelete = async () => {
    let { tabidx, type, poolId, tempPoolId, sourcePoolId, refreshMode } =
      this.state;
    Dialog.confirm({
      title: "提示",
      content: "确定批量删除么?",
      onOk: async () => {
        try {
          let req = {
            poolType: "shop",
            addOrDelete: 1,
            targetIdList: this.state.rowSelection.selectedRowKeys,
            // operateType: 0
          };
          if (type == 1) {
            // 创建
            req.tempPoolId = tempPoolId;
            req.operateType = 3;
            req.addOrDelete = tabidx === 0 ? 1 : 0;
          } else {
            //内页
            req.sourcePoolId = sourcePoolId;
            req.poolId = poolId;
            req.operateType = tabidx == 2 ? 1 : 2;
            // req.addOrDelete = (tabidx === 0 || tabidx === 1) ? 1 : 0;
            req.addOrDelete = 1;
            req.dataFlag = true;
            if (tabidx === 1 || tabidx == 2) {
              req.isRemove = 1;
            }
          }
          req.refreshMode = refreshMode;
          let request = api.manualUpdate;
          let resp = await request(req);
          Message.success("删除成功");
          this.initTable();
        } catch (error) {
          api.onRequestError(error);
        }
      },
    });
  };

  componentWillReceiveProps(nextProps) {
    this.setState({
      poolId: nextProps.poolId,
      tempPoolId: nextProps.tempPoolId,
      effectRules: nextProps.effectRules,
      sourcePoolId: nextProps.sourcePoolId,
    });
  }

  onTabChange = (key) => {
    this.setState(
      {
        tabidx: parseInt(key),
        pagination: {
          page: 1,
          size: PAGE_SIZE,
          total: 0,
          pageTotal: 0,
        },
      },
      () => {
        this.getTableData();
        if (key == "3") {
        }
      }
    );
  };

  onPageChange = (page) => {
    let { pagination } = this.state;
    pagination.page = page;
    this.setState(
      {
        pagination,
      },
      () => {
        this.getTableData();
      }
    );
  };

  onPageSizeChange = (size) => {
    let { pagination } = this.state;
    pagination.size = size;
    this.setState(
      {
        pagination,
      },
      () => {
        this.getTableData();
      }
    );
  };

  componentDidMount() {
    this.getTableData();
    this.props.onRef(this);
  }

  getTableData = () => {
    let { type, tabidx } = this.state;
    if (type == 1) {
      //1 创建页面，
      if (tabidx === 0) {
        this.getViewGoodViewList();
      } else {
        this.getViewDeleteGoodList();
      }
    } else {
      //2：详情页面
      if (tabidx === 0) {
        this.getDetailGoodViewList();
      } else if (tabidx == 3) {
        this.getFailedDetailDeleteGoodList();
      } else {
        this.getDetailDeleteGoodList();
      }
    }
  };

  //预览
  getViewGoodViewList = async () => {
    let {
      pagination,
      pageIndex,
      tempPoolId,
      isSearch,
      effectRules,
      effectFilterFieldBizModelList,
      pageSize,
      orderBy,
      orderDirection,
      sourcePoolId,
      isLoading,
      dataSource,
      isMore,
    } = this.state;
    if (isSearch) {
      const outerForm = await promisify(this.viewField.validate)();
    }
    this.setState({ isLoading: true });
    try {
      let request = api.queryPoolViewList;
      let resp = await request({
        tempPoolId,
        sourcePoolId,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
        orderDirection: orderDirection || "desc",
        orderBy: orderBy || "d7ValidOrderCnt",
        effectFilterFieldBizModelList: !isSearch
          ? effectRules
          : effectRules.concat(effectFilterFieldBizModelList),
      });
      pagination.total = resp.totalCount;
      pagination.pageTotal = resp.totalPages;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = dataSource
          .concat(resp.data)
          .filter((v) => v.status == 1);
      } else {
        newDataSource = resp.data.filter((v) => v.status == 1);
      }
      this.setState({
        dataSource: newDataSource,
        pagination,
        isLoading: false,
        isMore: false,
      });
    } catch (error) {
      this.setState({ isLoading: false, isMore: false });
      api.onRequestError(error);
    }
  };

  getViewDeleteGoodList = async () => {
    try {
      let request = api.queryDeleteList;
      let resp = await request(this.state.tempPoolId);
      this.setState({
        deleteDataSource: resp.data,
      });
    } catch (error) {
      api.onRequestError(error);
    }
  };

  //内页
  getDetailGoodViewList = async () => {
    let {
      pagination,
      effectFilterFieldBizModelList,
      sourcePoolId,
      poolId,
      resultStoreIds,
      resultStoreNames,
      resultShopIds,
      brandNames,
      itemStatus,
      pageIndex,
      pageSize,
      orderDirection,
      orderBy,
      dataSource,
      isMore,
    } = this.state;
    this.setState({ isLoading: true });
    try {
      let request = api.queryStorePoolDetail;
      let resp = await request({
        sourcePoolId,
        poolId,
        operateType: 0,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
        orderBy: orderBy || "d7ValidOrderCnt",
        orderDirection: orderDirection || "desc",
        resultStoreIds: resultStoreIds ? resultStoreIds.split(",") : [],
        resultStoreNames: resultStoreNames ? resultStoreNames.split(",") : [],
        resultShopIds: resultShopIds ? resultShopIds.split(",") : [],
        brandNames: brandNames ? brandNames.split(",") : [],
        itemStatus,
      });
      pagination.total = resp.totalCount;
      pagination.pageTotal = resp.totalPages;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = dataSource.concat(resp.data);
      } else {
        newDataSource = resp.data;
      }
      this.setState({
        dataSource: newDataSource,
        isLoading: false,
        pagination,
        isMore: false,
      });
    } catch (error) {
      this.setState({ isLoading: false, isMore: false });
      api.onRequestError(error);
    }
  };

  getDetailDeleteGoodList = async () => {
    let {
      poolId,
      sourcePoolId,
      pagination,
      tabidx,
      deleteDataSource,
      isMore,
      resultStoreIds,
      resultStoreNames,
      resultShopIds,
      brandNames,
      itemStatus,
    } = this.state;
    try {
      let request = api.queryStoreManualPoolDetail;
      let resp = await request({
        sourcePoolId,
        poolId,
        operateType: tabidx,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
        resultStoreIds: resultStoreIds ? resultStoreIds.split(",") : [],
        resultStoreNames: resultStoreNames ? resultStoreNames.split(",") : [],
        resultShopIds: resultShopIds ? resultShopIds.split(",") : [],
        brandNames: brandNames ? brandNames.split(",") : [],
        itemStatus,
      });
      pagination.total = resp.totalCount;
      pagination.pageTotal = resp.totalPages;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = deleteDataSource.concat(resp.data);
      } else {
        newDataSource = resp.data;
      }
      this.setState({
        deleteDataSource: newDataSource,
        isLoading: false,
        pagination,
        isMore: false,
      });
    } catch (error) {
      this.setState({ isLoading: false, isMore: false });
      api.onRequestError(error);
    }
  };

  getFailedDetailDeleteGoodList = async () => {
    let { poolId, sourcePoolId, pagination, tabidx, failedDataSource, isMore } =
      this.state;
    try {
      let request = api.queryFailedStoreManualPoolDetail;
      let resp = await request({
        sourcePoolId,
        poolId,
        operateType: tabidx,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
      });
      pagination.total = resp.totalCount;
      pagination.total = resp.totalCount;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = failedDataSource.concat(resp.data);
      } else {
        newDataSource = resp.data;
      }
      this.setState({
        failedDataSource: newDataSource,
        pisLoading: false,
        pagination,
        isMore: false,
      });
    } catch (error) {
      this.setState({ isLoading: false, isMore: false });
      api.onRequestError(error);
    }
  };

  onChangeType = (value) => {
    let { effectFilterFieldBizModelList, filterKey } = this.state;
    let filterFieldValue =
      effectFilterFieldBizModelList.length > 0
        ? effectFilterFieldBizModelList[0].filterFieldValue
        : 0;
    effectFilterFieldBizModelList = [];
    let item = {
      filterFieldId: value,
      filterFieldKey: value,
      operator: operatorEum[value],
      filterFieldValue,
    };
    effectFilterFieldBizModelList.push(item);
    filterKey = value;
    this.setState({ effectFilterFieldBizModelList, filterKey });
  };

  onInputChange = (value) => {
    let { effectFilterFieldBizModelList, filterValue } = this.state;
    let item = effectFilterFieldBizModelList[0];
    item.filterFieldValue = value ? JSON.stringify(value.split(",")) : "[]";
    filterValue = value;
    this.setState({ effectFilterFieldBizModelList, filterValue });
  };

  resetViewList = () => {
    this.setState(
      {
        resultStoreIds: "",
        resultStoreNames: "",
        resultShopIds: "",
        brandNames: "",
        itemStatus: "",
      },
      () => {
        this.field.reset();
      }
    );
  };

  clickMore = () => {
    let { pagination } = this.state;
    pagination.page = 1 + pagination.page;
    this.setState({ pagination, isMore: true }, () => {
      this.getTableData();
    });
  };

  onBatchAdd = () => {
    this.setState({ isDialogBatchAddInputVisible: true });
  };

  onBatchAddInputCancel = () => {
    this.setState({ isDialogBatchAddInputVisible: false });
  };

  onBatchAddInputConfirm = () => {
    this.batchInputField.validate(async (errors, values) => {
      if (errors) return;
      else {
        let itemIds = values.batchIds.split(/[,\s]+/gm).map((e) => e.trim());
        try {
          this.onBatchAddInputCancel();
          this.openBatchAddTableConfirm(itemIds, values.dataFlag);
        } catch (error) {
          api.onRequestError(error);
        }
      }
    });
  };

  async openBatchAddTableConfirm(itemIds, dataFlag) {
    let { sourcePoolId, poolId, refreshMode } = this.state;
    try {
      let req = {
        poolId,
        poolType: "shop",
        targetIdList: itemIds,
        sourcePoolId: sourcePoolId,
        operateType: 1,
        addOrDelete: 1,
        dataFlag,
        // operateType: 0
      };
      req.refreshMode = refreshMode;
      let request = api.manualUpdate;
      let resp = await request(req);
      if (resp.success) {
        Message.success(`添加成功`);
        this.getTableData();
      } else {
        Message.warning(`失败`);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  }

  render() {
    const {
      tabidx,
      columns,
      total,
      pagination,
      rowSelection,
      type,
      dataSource,
      deleteDataSource,
      failedDataSource,
      isLoading,
      effectFilterFieldBizModelList,
      filterKey,
      filterValue,
      poolId,
    } = this.state;
    // let {dataSource} = this.props;
    const filterDs = [
      { label: "淘内门店ID", value: "storeId" },
      { label: "门店名称", value: "shopName" },
      { label: "ELE门店ID", value: "shopId" },
      { label: "所属品牌", value: "brandName" },
    ];

    const validatorsGroup = {
      shopName: "",
      shopId: validatorMap.upcIdCommaSeperated,
      storeId: validatorMap.upcIdCommaSeperated,
      brandName: "",
    };

    const hideDelete = this.props.sourcePoolId == "41001" && type == 2;
    const current = poolEum.filter((v) => v.id == this.props.sourcePoolId);
    const isReal =
      current && current.length > 0 ? Boolean(current[0].type == 2) : false;
    const showAdd =
      !isReal &&
      this.props.sourcePoolId != "23004" &&
      this.props.sourcePoolId != "33005"; //根据池子id判断是否允许添加门店
    // let filterValue = JSON.parse(effectFilterFieldBizModelList[0].filterFieldValue);
    // let filterKey = effectFilterFieldBizModelList[0].filterFieldKey;
    return (
      <div className="sample-pool">
        <div className="total-info">
          {" "}
          {`${type == 1 ? "抽样预览" : "圈选结果"}`}
        </div>
        {type == 1 && tabidx === 0 && (
          <div className="rules">
            <Form field={this.viewField} className="rules-views-form">
              <FormItem
                label={
                  <Select
                    dataSource={filterDs}
                    value={filterKey}
                    onChange={this.onChangeType}
                  />
                }
                validator={validatorsGroup[filterKey]}
              >
                <Input
                  style={{ width: "200px" }}
                  onChange={this.onInputChange}
                  value={filterValue}
                  name={effectFilterFieldBizModelList[0].filterFieldKey}
                />
              </FormItem>
            </Form>
            <Button
              type="secondary"
              onClick={() =>
                this.setState({ isSearch: true }, () => this.onPageChange(1))
              }
              style={{ marginLeft: "10px" }}
            >
              查询
            </Button>
          </div>
        )}
        {type == 2 && (tabidx === 0 || tabidx === 1) && (
          <div className="rules">
            <Form className="filter" field={this.field}>
              <Row>
                <Col span={6}>
                  <FormItem
                    validator={validators.upcIdCommaSeperated}
                    label="淘内门店ID"
                    {...formItemLayout}
                  >
                    <Input name="resultStoreIds" placeholder="英文逗号隔开" />
                  </FormItem>
                </Col>
                <Col span={6}>
                  <FormItem label="门店名称" {...formItemLayout}>
                    <Input name="resultStoreNames" />
                  </FormItem>
                </Col>
                <Col span={6}>
                  <FormItem
                    label="ELE门店ID "
                    {...formItemLayout}
                    validator={validators.idCommaSeperated}
                  >
                    <Input name="resultShopIds" placeholder="英文逗号隔开" />
                  </FormItem>
                </Col>
                <Col span={6}>
                  <FormItem label="所属品牌" {...formItemLayout}>
                    <Input name="brandNames" placeholder="" />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col span={20}>
                  {type == 2 && showAdd && (
                    <>
                      <AccessBtn
                        getPermission={() => getPermission(poolId)}
                        text={false}
                        style={{ marginRight: "5px" }}
                        btnText={"添加门店"}
                        callback={this.onBatchAdd}
                      />
                      <Dialog
                        className="add-dialog"
                        height="400px"
                        title={
                          <div className="add-dialog-title">
                            <p className="first">添加门店</p>
                          </div>
                        }
                        visible={this.state.isDialogBatchAddInputVisible}
                        onOk={this.onBatchAddInputConfirm}
                        onCancel={this.onBatchAddInputCancel}
                        onClose={this.onBatchAddInputCancel}
                        okProps={{
                          children: "继续",
                        }}
                      >
                        <Radio.Group
                          {...this.batchInputField.init("dataFlag", {
                            initValue: false,
                          })}
                        >
                          <Radio value={false}>按ele门店id</Radio>
                          <Radio value={true}>按淘内门店id</Radio>
                        </Radio.Group>
                        <p className="second">请输入门店ID</p>
                        <Input.TextArea
                          className="batchIds-textArea"
                          autoHeight={{ minRows: 7 }}
                          placeholder={`输入多个可用英文逗号、空格、回车隔开`}
                          {...this.batchInputField.init("batchIds", {
                            rules: [
                              {
                                required: true,
                                message: "请输入ID信息",
                              },
                              {
                                validator: validators.map(
                                  validators.createRegexMatchMiddleware(
                                    /^(\d+)([,\s]+\d+)*$/gm,
                                    "输入格式不合法"
                                  ),
                                  (_a, b) => ({ value: b.trim() })
                                ),
                                trigger: ["onBlur"],
                              },
                            ],
                          })}
                        />
                        {this.batchInputField.getError("batchIds") ? (
                          <div style={{ color: "red" }}>
                            {this.batchInputField
                              .getError("batchIds")
                              .join(",")}
                          </div>
                        ) : (
                          ""
                        )}
                      </Dialog>
                    </>
                  )}
                </Col>
                <Col span={4}>
                  <Button
                    className="btn_reset"
                    onClick={this.resetViewList}
                    style={{ float: "right" }}
                  >
                    重置
                  </Button>
                  <Button
                    onClick={this.getTableData}
                    style={{ marginRight: "5px", float: "right" }}
                  >
                    查询
                  </Button>
                </Col>
              </Row>
            </Form>
          </div>
        )}
        <div className={`view-result`}>
          <Tab shape="wrapped" onChange={this.onTabChange} activeKey={tabidx}>
            <Tab.Item title="初选门店" key={0}></Tab.Item>
            {!hideDelete && showAdd && type != 1 && (
              <Tab.Item title="添加门店" key={1}></Tab.Item>
            )}
            {!hideDelete && <Tab.Item title="删除门店" key={2}></Tab.Item>}
            {!hideDelete && !isReal && type != 1 && (
              <Tab.Item title="失败记录" key={3}></Tab.Item>
            )}
          </Tab>
          <div className="filter-panel">
            <span>共找到{pagination.total}个结果</span>
            {type == 1 && tabidx === 0 && (
              <span>
                ，当前展示{dataSource.length > 0 ? dataSource.length : 0}个
              </span>
            )}
            {(tabidx == 0 || tabidx == 1) && (
              <div className="batch-panel">
                <Icon type="prompt" style={{ color: "#FF7000" }} />
                已选<span>{rowSelection.selectedRowKeys.length}</span>项
                {!hideDelete && (
                  <AccessBtn
                    getPermission={() => getPermission(poolId)}
                    disabled={rowSelection.selectedRowKeys.length == 0}
                    btnText={"批量删除"}
                    text={false}
                    callback={this.batchDelete}
                  />
                )}
              </div>
            )}
          </div>
          <Table
            loading={isLoading}
            onSort={this.onSort}
            rowSelection={tabidx === 0 || tabidx === 1 ? rowSelection : false}
            dataSource={
              tabidx === 0
                ? dataSource
                : tabidx == 3
                ? failedDataSource
                : deleteDataSource
            }
            hasBorder={false}
            primaryKey="storeId"
          >
            {this.createColumn(tabidx).map((e, idx) => {
              return <Table.Column {...e} key={idx} />;
            })}
          </Table>
          {pagination.total > 0 && pagination.pageTotal > pagination.page && (
            <Loading
              tip="加载中..."
              visible={isLoading}
              className="btn-more"
              size="medium"
            >
              <div onClick={this.clickMore}>加载更多</div>
            </Loading>
          )}
          {/*{tabidx == 1 && <Pagination onChange={this.onPageChange}
                                      total={pagination.total > 1000 ? 1000 : pagination.total}
                                      current={pagination.page}
                                      pageSize={pagination.size}
                                      onPageSizeChange={this.onPageSizeChange}
                                      popupProps={{align: "bl tl"}}
                                      pageSizePosition="end"
                                      pageSizeSelector="dropdown"
                                      pageSizeList={[50, 100, 200]}
                                      style={{float: 'right', marginTop: '10px'}}
          />}*/}
        </div>
      </div>
    );
  }
}
