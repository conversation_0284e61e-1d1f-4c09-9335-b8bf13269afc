import React, { useState } from "react";
import { config } from "@/config";
import { Dialog, Icon, Loading, Message, Upload } from "@alifd/next";

const SUCCESS = "200";

const AddBlacklistDialog = ({ onClose, onSubmit }) => {
  // 文件list
  const [value, setValue] = useState([]);
  // 改变上传拖拽颜色
  const [isDrag, setIsDrag] = useState(false);
  const [loading, setLoading] = useState(false);

  const onSuccess = (info) => {
    setIsDrag(false);
    if (info.response.code === SUCCESS) {
      setValue([
        ...value,
        {
          uid: "0",
          name: info.response.data.fileName,
          state: "done",
          url: info.response.data.url,
        },
      ]);
    } else {
      Message.error(info.response.msg || "上传失败");
    }
  };

  const onDragOver = () => {
    // 设置上传背景颜色
    setIsDrag(true);
  };
  const onDragLeave = () => {
    // 取消上传背景颜色
    setIsDrag(false);
  };

  const onRemove = (removeItem) => {
    // 每次删除都要将DefaultValue的值重新刷一遍，避免重复出现
    let list = [];
    value.map((items) => {
      if (items.url !== removeItem.url) {
        list.push(items);
      }
    });
    setValue([...list]);
  };

  const beforeUpload = (file, options) => {
    // 在上传之前判断类型，如果类型不匹配直接失败。
    return new Promise((resolve, reject) => {
      if (
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel"
      ) {
        resolve(options);
      } else {
        setIsDrag(false);
        reject();
        Message.error("文件类型不匹配");
      }
    });
  };
  return (
    <Dialog
      visible
      onClose={onClose}
      onCancel={onClose}
      style={{ width: "600px" }}
      title="批量删除商品"
      onOk={() => {
        if (loading) {
          return;
        }
        
        const _value = value?.[0] || {};
        setLoading(true)
        onSubmit({
          fileName: _value.name,
          fileUrl: _value.url,
          callback: () => setLoading(false)
        })
      }}
    >
      <Loading visible={loading}>
        <div style={{ margin: "7px 0 10px 0" }} className="add-blacklist-dialog">
          请先下载
          <a
            target="_blank"
            download="模板_批量删除商品.csv"
            href="https://files.alicdn.com/tpsservice/ea331a5f7cde9a939ab83fca4607d12f.xlsx"
          >
            导入模板
          </a>
          ，按照要求填写完成，上传到系统。
        </div>
        <Upload.Dragger
          headers={{ "X-Requested-With": null }}
          action={`${config.API_ROOT}/api/ali/common/upload`}
          onSuccess={onSuccess}
          listType="text"
          onDragOver={onDragOver}
          onDragLeave={onDragLeave}
          beforeUpload={beforeUpload}
          value={value}
          limit={1}
          onRemove={onRemove}
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        >
          <div
            className={`next-upload-drag ${
              isDrag ? "next-upload-drag-onDragOver" : ""
            }`}
          >
            <p className="next-upload-drag-icon">
              <Icon type="upload" />
            </p>
            <p className="next-upload-drag-text">点击或将文件拖拽到这里上传</p>
            <p className="next-upload-drag-hint">支持扩展名：.xlsx,.xls</p>
          </div>
        </Upload.Dragger>
      </Loading>
    </Dialog>
  );
};

export default AddBlacklistDialog;
