import React, {Fragment} from 'react';
import {Button, Tab, Table, Input, Select, Icon, Pagination, Message, Dialog, Grid} from '@alife/next';
import {PageBase} from '../../base';
import './index.scss';
import {FORMAT, formatTimeStamp} from "@/utils/time";
import PreviewImage from "@/components/PreviewImage";
import * as api from "@/adator/api";
import {renderNewStateMap} from '@/home/<USER>/common';
const DEFAULT_GOODS_IMG = require('../../../images/default-goods-pic.png');
const PAGE_SIZE = 100;
const {Row,Col} = Grid;
/*
 * 预览结果 （公用组件）
*/
export class ViewResult extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      tabidx: 1,
      pagination: {
        page: 1,
        size: PAGE_SIZE,
        total: props.total || 0
      },
      rowSelection:{
        onChange: this.onSelectChange,
        onSelect: function(selected, record, records) {
          console.log("onSelect", selected, record, records);
        },
        onSelectAll: function(selected, records) {
          console.log("onSelectAll", selected, records);
        },
        selectedRowKeys: [],
        // getProps: record => {
        //   return {
        //     disabled: record.id === 100306660941
        //   };
        // }
      },
      type:props.type, //:1 创建页面，2：详情页面
      columns:[ //淘内-商品池详情-查询结果
        {
          title: "商品", dataIndex: "goodsName", lock: 'left', width: 280, cell: (name, index, record) => {
            // let style = record.filterKeyword ? {color: "red", "text-decoration": "line-through"} : {};
            return <Row>
              <Col span={8}>
                <PreviewImage src={record.goodsPic || DEFAULT_GOODS_IMG} title={record.goodsName}/>
              </Col>
              <Col span={16}>
                <p className="name">{name}</p>
                <p className="tips">商品ID:{record.goodsId}</p>
                <p className="tips">条形码:{record.goodsUPCId}</p>
              </Col>
            </Row>
          }
        },
        { title: '在售状态', dataIndex: 'goodsState', width: 100 , cell: (state) => renderNewStateMap(state)  },
        { title: '原价', dataIndex: 'goodsOriginPrice', width: 100 },
        { title: '活动价', dataIndex: 'goodsPresentPrice', width: 120 },
        { title: '库存', dataIndex: 'goodsNumber', width: 140 },
        {
          title: '近7日销量', dataIndex: 'd7ValidOrderCnt', width: 140, sortable: true, cell: (value, index, record) => {
            return value ? value : '--'
          }
        },
        {
          title: '门店', dataIndex: 'storeName', width: 180, cell: (name, index, record) => {
            return <div>
              <p className="name">{name ? name : '-'}</p>
              <p className="tips">ELE门店ID:{record.eleStoreId}</p>
              <p className="tips">淘内门店ID:{record.storeId}</p>
              <p className="tips">淘系卖家ID:{record.sellerId}</p>
            </div>
          }
        },
        {
          title: '操作', lock: 'right', cell: (value, index, record) => {
            return this.renderOpt(record);
          },
          width: '80px'
        }
        // {title: "商品ID", dataIndex: "goodsId", width: 200},
        // {title: "商品条形码", dataIndex: "goodsUPCId", width: 200},
        // {
        //   title: "活动id", dataIndex: "activityId", width: 200, cell: (name, index, record) => {
        //     return <span>{name ? name : '-'}</span>
        //   }
        // },
        // {
        //   title: "商品图片", dataIndex: "goodsPic", width: 100, cell: (imgsrc, index, record) => {
        //     return <span>{imgsrc}</span>
        //   }
        // },


        // { title: 'C端现价', dataIndex: 'goods2CPresentPrice', width: 120 },

        // { title: '销量', dataIndex: 'goodsSales', width: 100 },
        // { title: '门店名称', dataIndex: 'storeName', width: 150 },
        // { title: 'ELE门店ID', dataIndex: 'eleStoreId', width: 120 },
        // { title: '淘内门店ID', dataIndex: 'storeId', width: 150 },
        // { title: '淘系卖家ID', dataIndex: 'sellerId', width: 150 },
      ],
    }
  }

  renderOpt = (record) =>{
    let {tabidx,type} = this.state;
    //初选商品 status为1 都有   预览的删除商品
    return <div className="opbtns">
      {((tabidx==1 && record.status == 1) || (type == 1 && tabidx == 2)) && <Button type={'primary'} text onClick={() => this.deleteGood(record.goodsId)}>{`${tabidx == 1 ? '删除' : '取消删除'}`}</Button>}
      {(tabidx==1 && record.status == 0) && <span>已删除</span>}
    </div>
  }

  onSort = (dataIndex, order) => {
    this.props.onSort(dataIndex, order);
  }

  onSelectChange = (ids, records) => {
    const { rowSelection } = this.state;
    rowSelection.selectedRowKeys = ids;
    this.setState({ rowSelection });
  }

  deleteGood = async (goodsId) =>{
    let {tabidx,type,poolId} = this.state;
    let text = (tabidx == 1) ? '删除' : '启用';
    Dialog.confirm({
      title: '提示',
      content: `确定${text}么?`,
      onOk: async () => {
        try {
          let req = {
            poolType: 'goods',
            addOrDelete:1,
            targetIdList: [goodsId],
            // operateType: 0
          }
          if (type == 1) { // 创建
            req.tempPoolId = poolId;
            req.operateType = 3;
            req.addOrDelete = (tabidx == 1) ? 1 : 0;
          } else { //内页
            req.sourcePoolId = "23001";
            req.poolId = poolId;
            req.operateType = 2;
            req.addOrDelete = (tabidx == 1) ? 1 : 0;
            if (tabidx == 2) {
              req.isRemove = 1
            }
          }
          let request = api.manualUpdate;
          let resp = await request(req);
          if(resp.success) {
            Message.success(`${text}成功`)
            this.initTable();
          }else{
            Message.warning(resp.errMessage || `${text}失败`);
          }
        } catch (error) {
          api.onRequestError(error)
        }
      }
    });
  }

  initTable = () =>{
    if(this.state.tabidx == 1) {
      this.props.getGoodViewList();
    }else{
      this.props.getDeleteGoodList();
    }
    let {rowSelection} = this.state;
    rowSelection.selectedRowKeys = [];
    this.setState({rowSelection});
  }

  batchDelete = async () => {
    let {tabidx,type,poolId} = this.state;
    Dialog.confirm({
      title: '提示',
      content: '确定批量删除么?',
      onOk: async () => {
        try {
          let req = {
            poolType: 'goods',
            addOrDelete:1,
            targetIdList: this.state.rowSelection.selectedRowKeys,
            // operateType: 0
          }
          if (type == 1) { // 创建
            req.tempPoolId = poolId;
            req.operateType = 3;
            req.addOrDelete = (tabidx == 1) ? 1 : 0;
          } else { //内页
            req.sourcePoolId = "23001";
            req.poolId = poolId;
            req.operateType = 2;
            req.addOrDelete = (tabidx == 1) ? 1 : 0;
            if (tabidx == 2) {
              req.isRemove = 1
            }
          }
          let request = api.manualUpdate;
          let resp = await request(req);
          Message.success('删除成功')
          this.initTable();
        } catch (error) {
          api.onRequestError(error)
        }
      }
    });
  }
  componentWillReceiveProps(nextProps){
    let {pagination} = this.state;
    pagination.total = nextProps.total;
    this.setState({
      poolId:nextProps.poolId,
      pagination
    })
  }

  onTabChange = (key) => {
    this.setState({
      tabidx: key
    },()=>{
      this.props.changeTabidx(key);
    })
  }

  onPageChange = (page) => {
    let {pagination, query} = this.state;
    pagination.page = page;
    this.setState({
      pagination
    }, () => {
      this.props.onPageAndSizeChange(page,pagination.size);
    })
  }

  componentDidMount() {
    // this.getGoodViewList();
  }

  onPageSizeChange = (size) =>{
    let {pagination} = this.state;
    pagination.size = size;
    this.setState({
      pagination
    }, () => {
      this.props.onPageAndSizeChange(pagination.page,size);
    })
  }

  render() {
    const {tabidx, columns, total, pagination,rowSelection,type} = this.state;
    let {dataSource, deleteDataSource, isLoading} = this.props;
    return (
      <div className={`view-result ${this.props.className}`}>
        <Tab shape="wrapped" onChange={this.onTabChange} activeKey={tabidx}>
          <Tab.Item title="初选商品" key={1}></Tab.Item>
          <Tab.Item title="删除商品" key={2}></Tab.Item>
        </Tab>
        <div className="filter-panel">
          {tabidx == 1 && <span>共找到约{pagination.total}个结果</span>}
          {(type==1 && tabidx == 1) && <span>，当前抽样展示{pagination.total > 1000 ? 1000 : pagination.total}个</span>}
          {tabidx == 1 && <div className="batch-panel">
            <Icon type="prompt" style={{color: "#FF7000"}}/>
            已选<span>{rowSelection.selectedRowKeys.length}</span>项
            <Button onClick={this.batchDelete} disabled={rowSelection.selectedRowKeys.length == 0}>批量删除</Button>
          </div>}
        </div>
        <Table
          loading={isLoading}
          onSort={this.onSort}
          rowSelection={(tabidx == 1) ? rowSelection : false}
          dataSource={(tabidx == 1) ? dataSource : deleteDataSource} hasBorder={false} primaryKey="goodsId">
          {
            columns.map((e, idx) => {
              return <Table.Column {...e} key={idx}/>
            })
          }
        </Table>
        {tabidx == 1 && <Pagination onChange={this.onPageChange}
          total={pagination.total > 1000 ? 1000 : pagination.total}
          current={pagination.page}
          pageSize={pagination.size}
          onPageSizeChange={this.onPageSizeChange}
          popupProps={{align: "bl tl"}}
          pageSizePosition="end"
          pageSizeSelector="dropdown"
          pageSizeList={[50, 100]}
          style={{float: 'right', marginTop: '10px'}}
        />}
      </div>
    )
  }
}
