import React, {Fragment} from 'react';
import {Form, Checkbox, Radio, Select, CascaderSelect as BaseCascaderSelect, Icon, Button} from '@alife/next';
import {PageBase} from '../../base';
import './index.scss';
import {ali, onRequestError} from "@/utils/api";
import {composeSelector} from "@/components/CascaderSelect";
const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const formItemLayout = {
  labelCol: { span: 3},
  wrapperCol: {
    span: 9
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'right'
};

/*
 *完成页面 （公用组件）
*/
export class CompletePage extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      poolId: props.poolId || ''
    }
  }

  componentDidMount() {
    this.timer = setTimeout(()=>{
      this.props.history.push("/pool/list");
    },5000);
  }

  componentWillReceiveProps(nextProps){
    this.setState({
      poolId:nextProps.poolId
    })
  }

  seekDetail = () =>{
    this.timer && clearTimeout(this.timer);
    this.props.history.push(`/pool/list/detail/${this.state.poolId}`);
  }

  backList = () =>{
    this.timer && clearTimeout(this.timer);
    this.props.history.push("/pool/list");
  }

  render() {
    return (
      <div className="complete-page">
        <div className="success-info">
          <Icon type="success" style={{ color: "#1DC11D", marginRight: "10px" }} />
          已提交
        </div>
        <div className="success-tips">5s后自动跳转</div>
        <div className="success-btns">
          <Button onClick={this.seekDetail}>查看详情</Button>
          <Button onClick={this.backList}>返回列表</Button>
        </div>
      </div>
    )
  }
}
