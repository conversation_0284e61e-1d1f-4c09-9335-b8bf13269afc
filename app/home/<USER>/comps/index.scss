.view-result {
  margin: 20px 30px 20px 0;
  &.supply{
    background-color: #fff;
    padding:24px 0;
    margin:10px 0 0 0;
  }
  .next-btn{
    margin-bottom:10px;
  }
  .rules{
    margin:24px 0px;
    .next-btn{
      margin:0;
    }
  }
  .filter-panel{
    padding-top: 24px;
    margin-bottom: 24px;
    &>span{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #999999;
    }
  }
  .batch-panel{
    margin:12px 0;
    .next-icon {
      margin-right: 8px;
    }
    &>span{
      margin:0 5px;
      color: #FF7000;
    }
    .next-btn{
      margin:2px 0 0 19px;
    }
  }
  &>.next-table{
    .name{
      font-family: PingFangSC-Medium;
      font-size: 14px;
      color: #333333;
      margin:0;
      margin-bottom:8px;
    }
    .tips{
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #999999;
      margin:0;
      margin-bottom:6px;
    }
    .preview-img-wrapper{
      margin-top: 6px;
    }
  }

}

.sample-pool{
  .rules-views-form{
    .next-form-item{
      display: flex;
      .next-form-item-label{
        padding:0;
      }
      .next-form-item-control{
        position: relative;
        top: 1px;
      }
    }

  }
}

.complete-page {
  text-align: center;
  padding-top:44px;

  .success-info {
    margin: 0 auto;
    font-family: PingFangSC-Medium;
    font-size: 20px;
    color: #333333;
  }
  .success-tips{
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #999999;
    margin-top:10px;
  }
  .success-btns{
    padding-top:40px;
    .next-btn{
      margin:5px;
    }
  }
}

.breadcrumb{
  margin:10px 0 0 20px;
}

.btn-more{
  height: 40px;
  line-height: 40px;
  width: 100%;
  border-radius: 4px;
  background-color: #FAFAFA;
  text-align: center;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #666666;
  margin-top: 12px;
  cursor: pointer;
  &:hover{
    color: #FF7000;
  }
}

.deletion-reason{
  width: 500px;
  .deletion-reason-id{
    padding-top: 6px;
    line-height: 20px;
    word-break: break-all;
  }
  .deletion-reason-checkbox{
    label{
      width: 50%;
      text-align: left;
      display: inline-block;
      margin: 0;
    }
  }
}