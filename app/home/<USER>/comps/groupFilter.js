import React, {Fragment} from 'react';
import {Form, Radio, Field,Input} from '@alife/next';
import '../intelligentPool/style.scss';
import * as api from "@/adator/api";
import {composeSelector} from "@/comps/CascaderSelect";
import {isCatCompEum,isRiskCompEum} from '../common';
import { isString } from '@/utils/validators';
import * as validators from "@/utils/validators";

const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const formItemLayout = {
  labelCol: { span: 3},
  wrapperCol: {
    span: 9
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'right'
};

function fetchSkuCategory() {
  return api.getSkuCategory()
    .then((res) => {
      // if (res.status == 200) {
        // 商品类目下发的value是number，但是在cascadeSelector里返回的选中项都是string，因此这里需要统一一下格式，不然编辑的时候会找不到对应的项。
        const dataSource = res.data.map(dataItem => {
          dataItem.value = dataItem.value.toString();
          dataItem.children && dataItem.children.map(subItem => {
            subItem.value = subItem.value.toString();
            subItem.children && subItem.children.map(thirdItem => {
              thirdItem.value = thirdItem.value.toString();
            })
          })
          return dataItem;
        });
        return dataSource;
      // } else {
      //   return {};
      // }
    })
    .catch(api.onRequestError);
}
const SkuCategorySelector = composeSelector(fetchSkuCategory);

function fetchMainCategory() {
  return api
    .getNewAllStoreMainCategory()
    .then((res) => {
      // if (res.status == 200) {
        return res.data;
      // } else {
      //   return {};
      // }
    })
    .catch(api.onRequestError);
}
const MainCategorySelector = composeSelector(fetchMainCategory);

/*
 *提供商品/门店基础筛选能力，可指定/提出商品类目、商品类目是否正确、风控合规数据、门店业态进行二次筛选 （公用组件）
*/
export class GroupFilter extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      effectRules: props.effectRules
    }
    this.field = new Field(this, {
      onChange: (name, value) => {
        if (name == 'item_name' || name == 'filter_item_name') {
          this.props.updatePoolRules(name, value != "" ? JSON.stringify(value.split(",")) : JSON.stringify([]));
        } else {
          this.props.updatePoolRules(name, isString(value) ? value : JSON.stringify(value));
        }
        // if (name == 'goodsCategory') {
        //   let newValue = value.map((v) => {
        //     return {
        //       level: v.level,
        //       value: v.value
        //     }
        //   })
        //   this.props.updatePoolRules(name, JSON.stringify(newValue));
        // } else if (name == 'newMainCategoryId') {
        //   let newValue = value.map((v) => v.value);
        //   this.props.updatePoolRules(name, JSON.stringify(newValue));
        // } else {
        //   this.props.updatePoolRules(name, JSON.stringify(value));
        // }
      },
    });
    this.initField(props.effectRules);
  }

  componentWillReceiveProps(newProps) {
    this.setState({effectRules: newProps.effectRules}, () => {
      this.initField(this.state.effectRules);
    });
  }

  initField = (effectRules) => {
    if (effectRules.length > 0) {
      effectRules.map((v) => {
        if (["isCatComp"].includes(v.filterFieldKey)) {
          this.field.setValue(v.filterFieldKey, v.filterFieldValue);
        }else{
          this.field.setValue(v.filterFieldKey, JSON.parse(v.filterFieldValue));
        }
        // if( (v.filterFieldKey!="newMainCategoryId")) {
        //   this.field.setValue(v.filterFieldKey, JSON.parse(v.filterFieldValue));
        // }else{
        //   let newFilterFieldValue = JSON.parse(v.filterFieldValue).map((v) => {
        //     return {
        //       value: v
        //     }
        //   })
        //   this.field.setValue(v.filterFieldKey, newFilterFieldValue);
        // }
      })
    }
  }

  render() {
    const { init } = this.field;
    return (
      <div className={`deep-filter ${this.props.className}`} field={this.field}>
        <Form>
          <FormItem label="商品名称" {...formItemLayout} >
            <Input
              {...init('item_name',
                {
                  rules: [{
                    validator: validators.upcIdCommaSeperated,
                    trigger: ['onChange']
                  }]
                })}
              placeholder="请输入商品名称" />
          </FormItem>
          <FormItem label="剔除关键词" {...formItemLayout}>
            <Input {...init("filter_item_name")}  placeholder="请输入剔除关键词"/>
          </FormItem>
          <FormItem
            label="商品类目"
            {...formItemLayout}
            requiredMessage="商品类目不能为空"
          >
            <SkuCategorySelector
              name="goodsCategory"
              {...init("goodsCategory")}
            />
          </FormItem>
          <FormItem label="类目是否正确" {...formItemLayout}>
            <RadioGroup {...init("isCatComp")} dataSource={isCatCompEum} placeholder="请选择商品分类" />
          </FormItem>
          <FormItem label="风控等级" {...formItemLayout}>
            <RadioGroup {...init("isRiskComp")} dataSource={isRiskCompEum} />
          </FormItem>
          <FormItem
            label="门店主营"
            hasFeedback
            {...formItemLayout}
            requiredMessage="门店主营类目不能为空"
          >
            <MainCategorySelector
              name="newMainCategoryId"
              {...init("newMainCategoryId")}
            />
          </FormItem>
        </Form>
      </div>
    )
  }
}
