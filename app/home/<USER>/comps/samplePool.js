import React, {Fragment} from 'react';
import {
  Button,
  Tab,
  Table,
  Input,
  Select,
  Icon,
  Message,
  Dialog,
  Grid,
  Form,
  Field,
  Loading,
  Radio,
  Checkbox
} from '@alife/next';
import './index.scss';
import PreviewImage from "@/components/PreviewImage";
import * as validators from '@/utils/validators';
import * as api from "@/adator/api";
import {itemStatusEnum, operatorEum, poolEum, renderNewStateMap,getPermission} from '@/home/<USER>/common';
import {validatorMap} from '@/home/<USER>/common/config';
import {deepClone, promisify} from "@/utils/others";
import {AccessBtn} from "@/components/Button";
import AddBlackListDialog from "./AddBlacklistDialog";
import { type4ManualUpdateByFile } from "./constants";

const DEFAULT_GOODS_IMG = require('../../../images/default-goods-pic.png');
const PAGE_SIZE = 50;
const {Row, Col} = Grid;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {span: 7},
  wrapperCol: {
    span: 17
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'center'
};

/*
 * 预览结果 （公用组件）
*/
export class SamplePool extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      tabidx: 0, //0:初选商品，2：删除商品
      pagination: {
        page: 1,
        size: PAGE_SIZE,
        total: 0,
        pageTotal:0
      },
      rowSelection: {
        onChange: this.onSelectChange,
        onSelect: function (selected, record, records) {
          console.log("onSelect", selected, record, records);
        },
        onSelectAll: function (selected, records) {
          console.log("onSelectAll", selected, records);
        },
        selectedRowKeys: [],
      },
      type: props.type, //:1 创建页面，2：详情页面
      columns: [ //淘内-商品池详情-查询结果
        {
          title: "商品", dataIndex: "goodsName", lock: 'left', width: 280, cell: (name, index, record) => {
            // let style = record.filterKeyword ? {color: "red", "text-decoration": "line-through"} : {};
            return <Row>
              <Col span={8}>
                <PreviewImage src={record.goodsPic || DEFAULT_GOODS_IMG} title={record.goodsName}/>
              </Col>
              <Col span={16}>
                <p className="name">{record.goodsName}</p>
                <p className="tips">商品ID:{record.goodsId}</p>
                <p className="tips">SKU ID:{record.tbSkuId || '-'}</p>
                <p className="tips">条形码:{record.goodsUPCId}</p>
              </Col>
            </Row>
          }
        },
        {title: '在售状态', dataIndex: 'goodsState', width: 100, cell: (state) => renderNewStateMap(state)},
        {title: '原价', dataIndex: 'goodsOriginPrice', width: 100},
        {title: '活动价', dataIndex: 'goodsPresentPrice', width: 120},
        {title: '库存', dataIndex: 'goodsNumber', width: 140},
        {title: '近7日销量', dataIndex: 'd7ValidOrderCnt', width: 140, sortable: true},
        {
          title: '门店', dataIndex: 'storeName', width: 180, cell: (name, index, record) => {
            return <div>
              <p className="name">{name ? name : '-'}</p>
              <p className="tips">ELE门店ID:{record.eleStoreId}</p>
              <p className="tips">淘内门店ID:{record.storeId}</p>
              <p className="tips">淘系卖家ID:{record.sellerId}</p>
            </div>
          }
        },
        {
          title: '操作', lock: 'right', cell: (value, index, record) => {
            return this.renderOpt(record);
          },
          width: '80px'
        }
      ],
      failColumns: [ //淘内-商品池详情-查询结果
        {
          title: "商品", dataIndex: "goodsName", lock: 'left', width: 280, cell: (name, index, record) => {
            // let style = record.filterKeyword ? {color: "red", "text-decoration": "line-through"} : {};
            return <Row>
              <Col span={8}>
                <PreviewImage src={record.goodsPic || DEFAULT_GOODS_IMG} title={record.goodsName}/>
              </Col>
              <Col span={16}>
                <p className="name">{record.goodsName}</p>
                <p className="tips">商品ID:{record.goodsId}</p>
                <p className="tips">SKU ID:{record.tbSkuId || '-'}</p>
                <p className="tips">条形码:{record.goodsUPCId}</p>
              </Col>
            </Row>
          }
        },
        {title: '在售状态', dataIndex: 'goodsState', width: 100, cell: (state) => renderNewStateMap(state)},
        {title: '原价', dataIndex: 'goodsOriginPrice', width: 100},
        {title: '活动价', dataIndex: 'goodsPresentPrice', width: 120},
        {title: '库存', dataIndex: 'goodsNumber', width: 140},
        {title: '近7日销量', dataIndex: 'd7ValidOrderCnt', width: 140, sortable: true},
        {
          title: '门店', dataIndex: 'storeName', width: 180, cell: (name, index, record) => {
            return <div>
              <p className="name">{name ? name : '-'}</p>
              <p className="tips">ELE门店ID:{record.eleStoreId}</p>
              <p className="tips">淘内门店ID:{record.storeId}</p>
              <p className="tips">淘系卖家ID:{record.sellerId}</p>
            </div>
          }
        },
        {title: '失败原因', dataIndex: 'message', width: 140},
      ],
      effectRules: props.effectRules,
      sourcePoolId: props.sourcePoolId,
      isLoading:true,
      tempPoolId:props.tempPoolId || '',
      poolId:props.poolId || '',
      effectFilterFieldBizModelList: [
        {
          filterFieldId: "goodsId",
          filterFieldKey: "goodsId",
          filterFieldValue:JSON.stringify([]),
          operator: operatorEum["goodsId"]
        }
      ],
      isSearch:false,
      dataSource:[],
      deleteDataSource:[],
      failedDataSource:[],
      filterKey:'goodsId',
      filterValue:'',
      isMore:false,
      isDialogBatchAddInputVisible:false,
      refreshMode: (props.refreshMode || props.refreshMode == 0) ? props.refreshMode : -1,
      delReasonList:[], //删除原因
      showAddBlacklistDialog: false,
    }

    this.field = new Field(this, {
      onChange: (name, value) => {
        this.setState({
          [name]:value
        })
      }
    });

    this.viewField = new Field(this, {});

    this.batchInputField = new Field(this, {});

    this.deletionReason = new Field(this, {});
  }

  createColumn = (tabidx) =>{
    let result;
    if (tabidx==3) {
      result = this.state.failColumns;
    }else if(tabidx==2){
      result = deepClone(this.state.columns)
      if (result[7].title != '删除原因') {
        result[8] = result[7]
        result[7] = {title: '删除原因', dataIndex: 'delReason', width: 140,cell:(value, index, record)=>{
          return record.delReason && record.delReason.length > 0 ? record.delReason : '无'
        }}
      }
    }else {
      result = this.state.columns;
    }
    return result;
  }

  renderOpt = (record) => {
    let {tabidx,type,sourcePoolId,poolId} = this.state;
    const current = poolEum.filter(v => v.id == sourcePoolId);
    const isReal = (current && current.length > 0) ? Boolean(current[0].type == 2) : false;
    let delBtn = <AccessBtn
      getPermission={()=>getPermission(poolId)}
      btnText={'删除'}
      callback={()=>this.deleteGood(record.goodsId, record.tbSkuId)}/>
    let cancelDelBtn = <AccessBtn
      getPermission={()=>getPermission(poolId)}
      btnText={'取消删除'}
      callback={()=>this.deleteGood(record.goodsId, record.tbSkuId)}/>;
    //初选商品 status为1 都有   预览的删除商品  4种情况
    if (tabidx === 0) { //tab为初选商品
      if (type == 1) { //预览
        return <div className="opbtns">
          {(record.status == 1) && delBtn}
          {(record.status == 0) && <span>已删除</span>}
          {(record.status == -1) && <span>已剔除</span>}
        </div>
      } else { //详情
        return <div className="opbtns">
          {(record.status == 1 && sourcePoolId != '41001') && delBtn}
        </div>
      }
    } else if (tabidx === 2) { //tab为删除商品
      if ((type == 1 || type == 2) && !isReal) { //预览
        return <div className="opbtns">
          {cancelDelBtn}
        </div>
      }
    }else if(tabidx ===1 || tabidx ===3){
      if (type == 2) { //预览
        return <div className="opbtns">
          {(record.status == 1 && sourcePoolId != '41001') && delBtn}
        </div>
      }
    }
  }

  onSort = (key, order) => {
    this.setState({
      orderBy: key,
      orderDirection: order
    },()=>{
      this.getTableData();
    })
  }

  onSelectChange = (ids, records) => {
    const { rowSelection } = this.state;
    this.setState({
      rowSelection: { ...rowSelection, selectedRowKeys: ids },
    });
  }

  deleteGood = async (goodsId, skuId) => {
    let {tabidx, type, poolId, tempPoolId, sourcePoolId,refreshMode,delReasonList} = this.state;
    let text = (tabidx === 0) ? '删除' : '取消删除';
    if(tabidx === 0){
      Dialog.show({
        content: <div className='deletion-reason'>
          <Form field={this.deletionReason} className="deletion-reason-form">
            <FormItem label='商品ID（skuId）：' {...formItemLayout}>
              <div className="deletion-reason-id">{`${goodsId}(${skuId})`}</div>
            </FormItem>
            <FormItem label='删除原因：' {...formItemLayout} required>
              <Checkbox.Group
                className="deletion-reason-checkbox"
                dataSource={delReasonList}
                onChange={(event) => {
                  this.deletionReason.setValue('deleteGood',event)
                }}
              />
            </FormItem>
          </Form>
        </div>,
        locale:{
          ok:'提交'
        },
        onOk: ()=>{
          return new Promise(async (resolve) =>{
            let deleteGood = this.deletionReason.getValue('deleteGood') || ''
            if (deleteGood && deleteGood.length > 0) {
              try {
                let req = {
                  poolType: 'goods',
                  addOrDelete: 1,
                  targetList: [{
                    targetId: goodsId,
                    subTargetId: skuId
                  }],
                  delReasonList:deleteGood
                  // operateType: 0
                }
                if (type == 1) { // 创建
                  req.tempPoolId = tempPoolId;
                  req.operateType = 3;
                  req.addOrDelete = (tabidx === 0) ? 1 : 0;
                } else { //内页
                  req.sourcePoolId = sourcePoolId;
                  req.poolId = poolId;
                  req.operateType = (tabidx == 2) ? 1 : 2;
                  // req.addOrDelete = (tabidx === 0) ? 1 : 0;
                  req.addOrDelete = 1;
                  req.dataFlag = true;
                  // 添加商品和恢复商品
                  if (tabidx === 1 || tabidx == 2) {
                    req.isRemove = 1
                  }
                }
                req.refreshMode = refreshMode;
                let request = api.manualUpdate;
                let resp = await request(req);
                if (resp.success) {
                  Message.success(`${text}成功`)
                  this.deletionReason.setValues({})
                  this.initTable();
                } else {
                  Message.warning(resp.errMessage || `${text}失败`);
                  resolve(false)
                }
              } catch (error) {
                api.onRequestError(error)
                resolve(false)
              }
              return resolve(true)
            }else{
              Message.error('请选择至少一项删除原因')
              return resolve(false)
            }
          })

        }
      })
    }else{
      Dialog.confirm({
        title: '提示',
        content: `确定${text}么?`,
        onOk: async () => {
          try {
            let req = {
              poolType: 'goods',
              addOrDelete: 1,
              targetList: [{
                targetId: goodsId,
                subTargetId: skuId
              }],
              // operateType: 0
            }
            if (type == 1) { // 创建
              req.tempPoolId = tempPoolId;
              req.operateType = 3;
              req.addOrDelete = (tabidx === 0) ? 1 : 0;
            } else { //内页
              req.sourcePoolId = sourcePoolId;
              req.poolId = poolId;
              req.operateType = (tabidx == 2) ? 1 : 2;
              // req.addOrDelete = (tabidx === 0) ? 1 : 0;
              req.addOrDelete = 1;
              req.dataFlag = true;
              if (tabidx === 1 || tabidx == 2) {
                req.isRemove = 1
              }
            }
            req.refreshMode = refreshMode;
            let request = api.manualUpdate;
            let resp = await request(req);
            if (resp.success) {
              Message.success(`${text}成功`)
              this.initTable();
            } else {
              Message.warning(resp.errMessage || `${text}失败`);
            }
          } catch (error) {
            api.onRequestError(error)
          }
        }
      });
    }
  }

  initTable = () => {
    this.getTableData();
    let {rowSelection} = this.state;
    this.setState({rowSelection: {
      ...rowSelection,
      selectedRowKeys: []
    }});
  }

  batchDelete = async () => {
    let {dataSource, tabidx, type, poolId, tempPoolId, sourcePoolId, refreshMode,rowSelection,delReasonList} = this.state;
    Dialog.confirm({
      content: <div className='deletion-reason'>
      <Form field={this.deletionReason} className="deletion-reason-form">
        <FormItem label='商品ID(skuId)：' {...formItemLayout}>
          <div className="deletion-reason-id">
            {rowSelection.selectedRowKeys
              ?.map(
                (_v) =>
                  `${_v}(${
                    dataSource?.find((_item) => _item.goodsId == _v)
                      ?.tbSkuId
                  })`
              )
              .join(",")}
          </div>
        </FormItem>
        <FormItem label='删除原因：' {...formItemLayout} required>
            <Checkbox.Group
              className="deletion-reason-checkbox"
              dataSource={delReasonList}
              onChange={(event) => {
                this.deletionReason.setValue('deleteGood',event)
              }}
            />
          </FormItem>
        </Form>
      </div>,
      locale:{
        ok:'提交'
      },
      onOk: () => {
        return new Promise(async (resolve) =>{
          const deleteGood = this.deletionReason.getValue('deleteGood') || ''
          const targetList = rowSelection?.selectedRowKeys?.map((_v) => {
            const curItem = dataSource?.find((_item) => _item.goodsId == _v);
            return {
              targetId: _v,
              subTargetId: curItem.tbSkuId
            }
          })
          if (deleteGood && deleteGood.length > 0) {
            try {
              let req = {
                poolType: 'goods',
                addOrDelete: 1,
                /**
                 * 这里要改，后端两个字段都兼容，店池不用改。
                 * 如果 targetList 中 subTargetId 没传，逻辑和也和 targetIdList 一样
                 *  targetId   // 目标实体id， 传 goodsId
                 *  subTargetId  // 目标实体子集ID，如sku场景传， 传 tbSkuId
                 * 
                 *  targetIdList: string[]
                 *      ↓
                 *  targetList: {targetId: string; subTargetId: string}[]
                 */
                // targetIdList: this.state.rowSelection.selectedRowKeys,
                targetList,
                delReasonList:deleteGood
                // operateType: 0
              }
              if (type == 1) { // 创建
                req.tempPoolId = tempPoolId;
                req.operateType = 3;
                req.addOrDelete = (tabidx === 0) ? 1 : 0;
              } else { //内页
                req.sourcePoolId = sourcePoolId;
                req.poolId = poolId;
                req.operateType = (tabidx == 2) ? 1 : 2;
                // req.addOrDelete = (tabidx === 0 || tabidx === 1) ? 1 : 0;
                req.addOrDelete = 1;
                req.dataFlag = true;
                // 添加商品和恢复商品
                if (+tabidx === 1 || +tabidx === 2) {
                  req.isRemove = 1
                }
              }
              req.refreshMode = refreshMode;
              let request = api.manualUpdate;
              let resp = await request(req);
              Message.success('删除成功')
              this.deletionReason.setValues({})
              this.initTable();
            } catch (error) {
              api.onRequestError(error)
              resolve(false)
            }
            return resolve(true)
          }else{
            Message.error('请选择至少一项删除原因')
            return resolve(false)
          }
        })

      }
    });
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      poolId: nextProps.poolId,
      tempPoolId:nextProps.tempPoolId,
      effectRules:nextProps.effectRules,
      sourcePoolId:nextProps.sourcePoolId
    })
  }

  onTabChange = (key) => {
    this.setState({
      tabidx: parseInt(key)
    }, () => {
      this.getTableData();
      if(key=='3'){
      }
    })
  }

  onPageChange = (page) => {
    let {pagination} = this.state;
    pagination.page = page;
    this.setState({
      pagination
    }, () => {
      this.getTableData();
    })
  }

  onPageSizeChange = (size) => {
    let {pagination} = this.state;
    pagination.size = size;
    this.setState({
      pagination
    }, () => {
      this.getTableData();
    })
  }

  onBatchDeleteGoodsSubmit = ({fileName, fileUrl, callback}) => {
    const { poolId, refreshMode, sourcePoolId } = this.state;
    api
      .manualUpdateByFile({
        poolType: type4ManualUpdateByFile.GOODS,
        addOrDelete: type4ManualUpdateByFile.ADD_BLACK_LIST,
        sourcePoolId,
        poolId,
        operateType: type4ManualUpdateByFile.BLACK_LIST,
        dataFlag: type4ManualUpdateByFile.TAO_NEI_ID,
        refreshMode,
        fileName,
        fileUrl, //oss文件地址
      })
      .then((res) => {
        console.log("res=======", res);
        if (res.success) {
          Message.success("批量删除成功");
          this.setState(
            {
              showAddBlacklistDialog: false,
            },
            () => {
              this.getTableData();
            }
          );
        } else {
          Message.error(res.errMessage || "批量删除失败，请重试");
        }
      })
      .catch((err) => {
        Message.error(err.message || "批量删除失败");
      })
      .finally(() => {
        callback && callback();
      });
  }

  componentDidMount() {
    this.getTableData();
    this.getDelReasonList()
    this.props.onRef(this);
  }

  getTableData = () => {
    let {type, tabidx} = this.state;
    if (type == 1) { //1 创建页面，
      if (tabidx === 0) {
        this.getViewGoodViewList();
      } else {
        this.getViewDeleteGoodList()
      }
    } else { //2：详情页面
      if (tabidx === 0) {
        this.getDetailGoodViewList();
      } else if(tabidx==3){
        this.getFailedDetailDeleteGoodList();
      }else{
        this.getDetailDeleteGoodList();
      }
    }
  }

  // 获取删除原因列表
  getDelReasonList = async ()=>{
    try {
      let request = api.getDelReasonList;
      let resp = await request();
      this.setState({delReasonList:resp})
    } catch (error) {
      api.onRequestError(error);
    }
  }

  //预览
  getViewGoodViewList = async () => {
    let {pagination, pageIndex, tempPoolId, isSearch, effectRules, effectFilterFieldBizModelList, pageSize, orderBy, orderDirection, sourcePoolId, isLoading,dataSource,isMore} = this.state;
    if(isSearch){
      const outerForm = await (promisify(this.viewField.validate)());
    }
    this.setState({isLoading:true})
    try {
      let request = api.queryPoolViewList;
      let resp = await request({
        tempPoolId,
        sourcePoolId,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
        orderDirection: orderDirection || 'desc',
        orderBy: orderBy || 'd7ValidOrderCnt',
        effectFilterFieldBizModelList:  (!isSearch) ? effectRules : effectRules.concat(effectFilterFieldBizModelList)
      });
      pagination.total = resp.totalCount;
      pagination.pageTotal = resp.totalPages;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = dataSource.concat(resp.data).filter((v) => v.status == 1);
      } else {
        newDataSource = resp.data.filter((v) => v.status == 1);
      }
      this.setState({
        dataSource: newDataSource,
        pagination,
        isLoading: false,
        isMore: false
      })
    } catch (error) {
      console.log(error);
      this.setState({isLoading:false,isMore:false})
      api.onRequestError(error)
    }
  }

  getViewDeleteGoodList = async () => {
    try {
      let request = api.queryDeleteList;
      let resp = await request(this.state.tempPoolId);
      this.setState({
        deleteDataSource: resp.data
      })
    } catch (error) {
      console.log(error);
      api.onRequestError(error)
    }
  }


  //内页
  getDetailGoodViewList = async () => {
    let {pagination,effectFilterFieldBizModelList,sourcePoolId, poolId, resultUpcIds, resultItemNames, resultItemIds, resultTbSkuIds, itemStatus, pageIndex, pageSize, orderDirection, orderBy, dataSource,isMore} = this.state;
    this.setState({isLoading:true})
    try {
      let request = api.queryItemPoolDetailGoodList;
      let resp = await request({
        sourcePoolId,
        poolId,
        operateType: 0,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
        orderBy: orderBy || 'd7ValidOrderCnt',
        orderDirection: orderDirection || 'desc',
        resultUpcIds: resultUpcIds ? resultUpcIds.split(",") : [],
        resultItemNames: resultItemNames ? resultItemNames.split(",") : [],
        resultItemIds: resultItemIds ? resultItemIds.split(",") : [],
        resultTbSkuIds: resultTbSkuIds ? resultTbSkuIds.split(",") : [],
        itemStatus
      });
      pagination.total = resp.totalCount;
      pagination.pageTotal = resp.totalPages;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = dataSource.concat(resp.data);
      } else {
        newDataSource = resp.data;
      }
      this.setState({
        dataSource: newDataSource,
        isLoading:false,
        pagination,
        isMore:false
      })
    } catch (error) {
      console.log(error);
      this.setState({isLoading:false,isMore:false})
      api.onRequestError(error)
    }
  }

  getDetailDeleteGoodList = async () => {
    let {
      poolId,
      sourcePoolId,
      pagination,
      tabidx,
      deleteDataSource,
      isMore,
      resultUpcIds,
      resultTbSkuIds,
      resultItemNames,
      resultItemIds,
      itemStatus,
    } = this.state;
    try {
      let request = api.queryItemManualPoolDetail;
      let resp = await request({
        sourcePoolId,
        poolId,
        operateType: tabidx,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
        resultUpcIds: resultUpcIds ? resultUpcIds.split(",") : [],
        resultItemNames: resultItemNames ? resultItemNames.split(",") : [],
        resultItemIds: resultItemIds ? resultItemIds.split(",") : [],
        resultTbSkuIds: resultTbSkuIds ? resultTbSkuIds.split(",") : [],
        itemStatus
      });
      pagination.total = resp.totalCount;
      pagination.pageTotal = resp.totalPages;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = deleteDataSource.concat(resp.data);
      } else {
        newDataSource = resp.data;
      }
      this.setState({
        deleteDataSource: newDataSource,
        isLoading:false,
        pagination,
        isMore:false
      })
    } catch (error) {
      console.log(error);
      this.setState({isLoading:false,isMore:false})
      api.onRequestError(error)
    }
  }

  getFailedDetailDeleteGoodList = async () => {
    let {poolId, sourcePoolId, pagination, tabidx, failedDataSource, isMore} = this.state;
    try {
      let request = api.queryFailedItemManualPoolDetail;
      let resp = await request({
        sourcePoolId,
        poolId,
        operateType: tabidx,
        pageSize: pagination.size || 100,
        pageIndex: pagination.page || 1,
      });
      pagination.total = resp.totalCount;
      pagination.total = resp.totalCount;
      let newDataSource;
      if (pagination.page >= 2 && isMore) {
        newDataSource = failedDataSource.concat(resp.data);
      } else {
        newDataSource = resp.data;
      }
      this.setState({
        failedDataSource: newDataSource,
        pisLoading:false,
        pagination,
        isMore:false
      })
    } catch (error) {
      console.log(error);
      this.setState({isLoading:false,isMore:false})
      api.onRequestError(error)
    }
  }

  onChangeType = (value) =>{
    let {effectFilterFieldBizModelList,filterKey} = this.state;
    let filterFieldValue = effectFilterFieldBizModelList.length > 0 ? effectFilterFieldBizModelList[0].filterFieldValue : 0;
    effectFilterFieldBizModelList = [];
    let item = {
      filterFieldId:value,
      filterFieldKey:value,
      operator:operatorEum[value],
      filterFieldValue
    };
    effectFilterFieldBizModelList.push(item);
    filterKey = value;
    this.setState({effectFilterFieldBizModelList,filterKey});
  }

  onInputChange = (value) =>{
    let {effectFilterFieldBizModelList,filterValue} = this.state;
    let item = effectFilterFieldBizModelList[0];
    item.filterFieldValue = value ? JSON.stringify(value.split(",")) : "[]";
    filterValue = value;
    this.setState({effectFilterFieldBizModelList,filterValue});
  }

  resetViewList = () =>{
    this.setState({
      resultUpcIds:'',
      resultItemNames:'',
      resultItemIds:'',
      resultTbSkuIds:'',
      itemStatus:''
    },()=>{
      this.field.reset();
    })
  }

  clickMore = () =>{
    let {pagination} = this.state;
    pagination.page = 1 + pagination.page;
    this.setState({pagination,isMore:true},()=>{
      this.getTableData()
    });
  }

  onBatchAdd = () => {
    this.setState({isDialogBatchAddInputVisible:true})
  }

  onBatchAddInputCancel = () => {
    this.setState({isDialogBatchAddInputVisible: false});
  }

  onBatchAddInputConfirm = () => {
    this.batchInputField.validate(async (errors, values) => {
      if (errors) return
      else {
        let itemIds = values.batchIds.split(/[,\s]+/gm).map(e => e.trim());
        try {
          this.onBatchAddInputCancel()
          this.openBatchAddTableConfirm(itemIds, values.dataFlag)
        } catch (error) {
          api.onRequestError(error)
        }
      }
    })
  }

  async openBatchAddTableConfirm(itemIds, dataFlag) {
    let {sourcePoolId, poolId, refreshMode} = this.state;
    try {
      let req = {
        poolId,
        poolType: 'goods',
        targetIdList: itemIds,
        sourcePoolId:sourcePoolId,
        operateType: 1,
        addOrDelete: 1,
        dataFlag  // 区分是否按淘系商品id， true 商品id， false skuId
        // operateType: 0
      }
      req.refreshMode = refreshMode;
      let request = api.manualUpdate;
      let resp = await request(req);
      if (resp.success) {
        console.log(resp.success);
        Message.success(`添加成功`);
        this.getTableData()
      } else {
        Message.warning(`失败`);
      }
    } catch (error) {
      api.onRequestError(error)
    }
  }

  render() {
    const {tabidx, columns, total, pagination, rowSelection, type, dataSource, deleteDataSource,failedDataSource, isLoading,effectFilterFieldBizModelList,filterKey,filterValue,poolId} = this.state;
    const { addGoodsBtnDisabled } = this.props;
    const filterDs = [
      {label: '商品名称', value: 'goodsNameKeyWord'},
      {label: '商品条码', value: 'upcId'},
      {label: '商品ID', value: 'goodsId'},
    ]
    const validatorsGroup = {
      "goodsId":validatorMap.idCommaSeperatedRequired,
      "upcId":validatorMap.upcIdCommaSeperated,
      "goodsNameKeyWord":""
    }
    const hideDelete = (this.props.sourcePoolId == '41001' && type == 2);
    const current = poolEum.filter(v => v.id == this.props.sourcePoolId);
    const isReal = (current && current.length > 0) ? Boolean(current[0].type == 2) : false;
    const showAdd = !isReal && this.props.sourcePoolId != '23004';
    return (
      <div className="sample-pool">
        <div className="total-info"> {`${type==1?'抽样预览':'圈选结果'}`}</div>
        {(type == 1 && tabidx === 0) && <div className="rules">
          <Form field={this.viewField} className="rules-views-form">
            <FormItem label={<Select dataSource={filterDs} value={filterKey} onChange={this.onChangeType}/>}  validator={validatorsGroup[filterKey]}>
              <Input style={{width:'200px'}} onChange={this.onInputChange} value={filterValue} name={effectFilterFieldBizModelList[0].filterFieldKey}/>
            </FormItem>
          </Form>
          <Button type="secondary" onClick={() => this.setState({isSearch:true},()=>this.onPageChange(1))} style={{marginLeft: '10px'}}>查询</Button>
        </div>}
        {(type == 2 && (tabidx === 0 || tabidx === 1)) && <div className="rules">
          <Form className="filter" field={this.field}>
            <Row>
              <Col span={6}>
                <FormItem validator={validators.upcIdCommaSeperated}
                          label="商品条形码" {...formItemLayout}>
                  <Input name="resultUpcIds" placeholder="英文逗号隔开"/>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="商品名称" {...formItemLayout}>
                  <Input name="resultItemNames" />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="商品ID " {...formItemLayout} validator={validators.idCommaSeperated}>
                  <Input name="resultItemIds"  placeholder="英文逗号隔开" />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label="商品状态" {...formItemLayout}>
                  <Select name='itemStatus' dataSource={itemStatusEnum} style={{width:'100%'}} />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={6}>
                <FormItem label="SKU ID " {...formItemLayout} validator={validators.idCommaSeperated}>
                  <Input name="resultTbSkuIds"  placeholder="英文逗号隔开" />
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={20}>
                {(type==2 && showAdd && !addGoodsBtnDisabled) && <>
                  <AccessBtn getPermission={()=>getPermission(poolId)}  btnText={'添加商品'} text={false}  callback={this.onBatchAdd}/>
                  <Dialog
                    className="add-dialog"
                    height="400px"
                    title={<div className="add-dialog-title"><p className="first">添加商品</p></div>}
                    visible={this.state.isDialogBatchAddInputVisible}
                    onOk={this.onBatchAddInputConfirm}
                    onCancel={this.onBatchAddInputCancel}
                    onClose={this.onBatchAddInputCancel}
                    okProps={{
                      children: '继续'
                    }}>
                    <Radio.Group {...this.batchInputField.init('dataFlag', {initValue: true})}>
                      {/*<Radio value={false}>按ele商品id</Radio>*/}
                      <Radio value={true}>按淘系商品id</Radio>
                      <Radio value={false}>按淘系商品SKU ID</Radio>
                    </Radio.Group>
                    {this.batchInputField.getValue("dataFlag") ? (
                      <>
                        <p className="second">请输入商品ID</p>
                        <Input.TextArea
                          className="batchIds-textArea"
                          autoHeight={{ minRows: 7 }}
                          placeholder={`输入多个可用英文逗号、空格、回车隔开`}
                          {...this.batchInputField.init("batchIds", {
                            rules: [
                              {
                                required: true,
                                message: "请输入ID信息",
                              },
                              {
                                validator: validators.map(
                                  validators.createRegexMatchMiddleware(
                                    /^(\d+)([,\s]+\d+)*$/gm,
                                    "输入格式不合法"
                                  ),
                                  (_a, b) => ({ value: b.trim() })
                                ),
                                trigger: ["onBlur"],
                              },
                            ],
                          })}
                        />
                      </>
                    ) : (
                      <>
                        <p className="second">请输入商品SKU ID</p>
                        <Input.TextArea
                          className="batchIds-textArea"
                          autoHeight={{ minRows: 7 }}
                          placeholder={`输入多个可用英文逗号、空格、回车隔开`}
                          {...this.batchInputField.init("batchIds", {
                            rules: [
                              {
                                required: true,
                                message: "请输入商品SKU ID",
                              },
                              {
                                validator: validators.map(
                                  validators.createRegexMatchMiddleware(
                                    /^(\d+)([,\s]+\d+)*$/gm,
                                    "输入格式不合法"
                                  ),
                                  (_a, b) => ({ value: b.trim() })
                                ),
                                trigger: ["onBlur"],
                              },
                            ],
                          })}
                        />
                      </>
                    )}
                    {this.batchInputField.getError('batchIds') ?
                      <div style={{ color: 'red' }}>{this.batchInputField.getError('batchIds').join(',')}</div> : ''}
                  </Dialog>
                </>}
              </Col>
              <Col span={4}>
                <Button className="btn_reset" onClick={this.resetViewList} style={{float:'right'}}>重置</Button>
                <Button onClick={this.getTableData} style={{marginRight:'5px',float:'right'}}>查询</Button>
              </Col>
            </Row>
          </Form>
        </div>}
        <div className={`view-result`}>
          <Tab shape="wrapped" onChange={this.onTabChange} activeKey={tabidx}>
            <Tab.Item title="初选商品" key={0}></Tab.Item>
            {(!hideDelete && showAdd && type!=1) && <Tab.Item title="添加商品" key={1}></Tab.Item>}
            {(!hideDelete) && <Tab.Item title="删除商品" key={2}></Tab.Item>}
            {(!hideDelete && !isReal && type!=1) && <Tab.Item title="失败记录" key={3}></Tab.Item>}
          </Tab>
          <div className="filter-panel">
            <span>共找到{pagination.total}个结果</span>
            {(type==1 && tabidx === 0) && <span>，当前展示{dataSource.length > 0 ? dataSource.length : 0}个</span>}
            {tabidx === 2 ? (
              <AccessBtn
                style={{ marginLeft: '10px' }}
                getPermission={() => getPermission(poolId)}
                btnText={"批量删除商品"}
                text={false}
                callback={() => {
                  this.setState({ showAddBlacklistDialog: true })
                }}
              />
            ) : null}
            {(tabidx == 0 || tabidx==1) && <div className="batch-panel">
              <Icon type="prompt" style={{color: "#FF7000"}}/>
              已选<span>{rowSelection.selectedRowKeys.length}</span>项
              {(!hideDelete) && <AccessBtn
                getPermission={() => getPermission(poolId)}
                disabled={rowSelection.selectedRowKeys.length == 0}
                btnText={'批量删除'}
                text={false}
                callback={this.batchDelete}
              />}
            </div>}
          </div>
          <Table
            key={tabidx}
            loading={isLoading}
            onSort={this.onSort}
            rowSelection={(tabidx ===0 || tabidx ===1) ? rowSelection : false}
            dataSource={((tabidx === 0) ? dataSource : (tabidx == 3) ? failedDataSource : deleteDataSource)}
            hasBorder={false} primaryKey="goodsId">
            {
              (this.createColumn(tabidx)).map((e, idx) => {
                return <Table.Column {...e} key={idx}/>
              })
            }
          </Table>
          {pagination.total > 0 && pagination.pageTotal > pagination.page &&
            <Loading tip="加载中..." visible={isLoading} className="btn-more" size='medium'>
              <div onClick={this.clickMore}>加载更多</div>
            </Loading>
          }
          {this.state.showAddBlacklistDialog ? (
            <AddBlackListDialog
              onClose={() => {
                this.setState({ showAddBlacklistDialog: false });
              }}
              onSubmit={this.onBatchDeleteGoodsSubmit}
            />
          ) : null}
        </div>
      </div>
    )
  }
}
