import React from "react"
import moment from "moment"
import { weekDaysEnum } from './utils'



export const renderDeliveryTime = (value) => {
  if (!value) return ''
  const { startTime, endTime, workDay, timeRanges } = value
  const startTimeText = startTime ? moment(startTime).format('YYYY-MM-DD HH:mm:ss') : null
  const endTimeText = endTime ? moment(endTime).format('YYYY-MM-DD HH:mm:ss') : null
  const workDayArray = workDay ? workDay.map(dayItem => {
    const data = weekDaysEnum.find(item => item.value === dayItem)
    return data ? data.label : ''
  }) : []

  return (
    <div>
      {startTimeText ? <div>开始：{startTimeText}</div> : null}
      {endTimeText ? <div>结束：{endTimeText}</div> : null}
      {workDayArray.length > 0 && (<div style={{ display: 'flex' }}>
        <div style={{ width: 42 }}>星期：</div>
        <div style={{ flex: 1 }}>{workDayArray.join('、')}</div>
      </div>)}
      {timeRanges && timeRanges.length > 0 && (
        <div style={{ display: 'flex' }}>
          <div style={{ width: 42 }}>时段：</div>
          <div style={{ flex: 1 }}>{timeRanges.map(item => <div>{item}</div>)}</div>
        </div>
      )}
    </div>
  )
}

export const renderCitys = (value) => {
  if (!Array.isArray(value)) return ''
  return value.map(item => item.label).join(',')
}