import React, {Fragment, useEffect, useState} from 'react';
import {Form, Checkbox, Radio, Select, CascaderSelect as BaseCascaderSelect, Icon, Button} from '@alife/next';
import {PageBase} from '../../base';
import './style.scss';
import {ali, onRequestError} from "@/utils/api";
import {composeSelector} from "@/components/CascaderSelect";
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
import { track} from "@/utils/aplus";
import * as qs from 'query-string';
const formItemLayout = {
  labelCol: { span: 3},
  wrapperCol: {
    span: 9
  },
  style: {
    width: '100%'
  },
  labelAlign: 'center',
  labelTextAlign: 'right'
};

/*
 *完成页面 （公用组件）
*/

export default function FinishPage(props) {

  const {poolId = '',recodeState} = props;

  const [timer, setTimer] = useState('');

  useEffect(() => {
    setTimer(setTimeout(()=>{
      props.history.push("/pool/list");
    },5000));

  }, [])


  const seekDetail =() =>{
    timer && clearTimeout(timer);
    let record = props.ctrlReq()
    track('clickEvent', ['/selection_kunlun.MANAGE-POOL-pool-list.view-pool-btn', `poolId=${poolId}`])
    let query = {
      basePoolId: record.sourcePoolId,
      name: record.poolName,
      canModify: record.canModify,
      poolState: recodeState,
      dataType: 1,
      createMode: record.createMode
    }
    let path = `/commoditypool/list/detail/${record.poolId}?${qs.stringify(query)}`
    props.history.push(path)
  }

  const backList = () =>{
    timer && clearTimeout(timer);
    props.history.push("/pool/list");
  }


  return (
    <div className="complete-page">
      <div className="success-info">
        <Icon type="success" style={{ color: "#1DC11D", marginRight: "10px" }} />
        已提交
      </div>
      <div className="success-tips">5s后自动跳转</div>
      <div className="success-btns">
        <Button onClick={seekDetail}>查看详情</Button>
        <Button onClick={backList}>返回列表</Button>
      </div>
    </div>
  )
}

