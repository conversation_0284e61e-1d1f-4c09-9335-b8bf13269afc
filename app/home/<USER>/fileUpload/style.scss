.next-loading-inline{
  width: 100%;
}
.tag-pool-father{
  background-color: #FFFFFF;
  margin: 10px 20px;
  padding-top: 10px;
}
.step-wraper{
  padding: 24px 0 0;
}
.CommodityUploadPage {
  background-color: #fff;
  padding: 0 20px;
  .order-num{
    width:18px;
    height:18px;
    line-height:16px;
    text-align:center;
    display:inline-block;
    border: 1px solid #FF7000;
    background-color: #FFFFFF;
    font-family: AlibabaSans102-Bold;
    font-size: 14px;
    font-style:normal;
    color: #FF7000;
    border-radius: 50%;
    margin-right:8px;
    &.current{
      background-color: #FF7000;
      color: #fff;
    }
  }
  h3{
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #333333;
    font-weight: normal;
    margin: 0;
    padding: 24px 0 0 8px;
    cursor: pointer;
  }
  > .next-medium{
    margin: 20px 0 !important;
  }
  .next-form.next-medium{
    margin: 20px 0 0 35px !important;
  }
  .next-breadcrumb-separator{
    line-height: 0;
  }
  .next-left >.next-form-item-control {
    text-align: center;
    padding: 0 24px 16px 0;
  }
  form {
    width: 100%;
  }
  .next-upload-drag-icon{
    margin-top: 80px;
  }
  .next-upload-drag{
    width: 584px;
    height: 258px;
  }
  .next-icon::before{
    width: 40px;
    height: 40px;
  }
  .next-icon{
    font-weight: lighter;
  }
  .next-upload-drag-text{
    font-family: PingFangSC-Regular;
    font-size: 16px;
    line-height: 16px;
    color: #333333;
    margin-top: 20px;
    margin-bottom: 0;
  }
  .next-upload-drag-hint{
    font-family: PingFangSC-Regular;
    font-size: 12px;
    line-height: 12px;
    margin-top: 10px;
    color: #999999;
  }
  .next-upload-drag-onDragOver{
    background: rgba(255,112,0,0.04);
    border: 1px dashed #FF7000;
  }
  .next-upload-list-item-name-wrap::before{
    content: "";
    display: inline-block;
    background-image: url("https://img.alicdn.com/imgextra/i3/O1CN0135Cc4e27fRXXEvkBp_!!6000000007824-2-tps-12-12.png");
    height: 12px;
    width: 12px;
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
  }
  .next-upload-list-item{
    position: relative;
    text-align: left;
    font-size: 12px;
    font-family: PingFangSC-Regular;
    padding-left: 30px;
    width: 584px;
  }
  .next-upload-list-item-done{
    background-color: rgb(250,250,250);
    color: rgb(147,147,147);;
    color: #666666;
  }
  .next-upload-list-item-error{
    background: rgba(255,68,51,0.06);
  }
  .next-upload-list-item-error::after{
    content: "文件格式错误";
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #FF4433;
    text-align: right;
    display: inline-block;
    position: absolute;
    right: 36px;
    top: 50%;
    transform: translateY(-50%);
  }
}