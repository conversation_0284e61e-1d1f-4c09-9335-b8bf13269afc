import React, { useEffect, useState } from "react";
import {
  Icon,
  Form,
  Upload,
  Field,
  Radio,
  Message,
  Loading,
  Button,
  Dialog
} from "@alife/next";
import { PageWrapper } from "@/components/PageWrapper";
import { PoolPageBase } from "../common";
import { promisify } from "@/utils/others";
import * as api from "@/utils/api";
import * as apiL from "@/adator/api";
import { config } from "@/config";
import "./style.scss";
import * as validators from "@/utils/validators";
import { set } from "lodash";

// const TemplateSingle = 'https://files.alicdn.com/tpsservice/b4beb2dac1f28e0986ee47576ceb2270.xlsx' // 老连接，先不删，留着排查问题
// 商品ID，商品条码，ELE门店ID， 淘内门店ID 使用模板
const TemplateSingle = 'https://files.alicdn.com/tpsservice/5e4bdefb6a26ef1bf13b8f47108173f7.xlsx'
// 商品条码*门店ID，商品条码*供应商ID，商品条码*品牌ID 使用模板
const TemplateMultiple = 'https://files.alicdn.com/tpsservice/76374039bdf30e81aa4f0600142172d5.xlsx'

const downloadUrls = {
  ITEM_ID: TemplateSingle, // 商品ID
  ITEM_UPC: TemplateSingle, // 商品条码
  ITEM_UPC_SHOP_ID: TemplateMultiple, // 商品条码*门店ID
  ITEM_UPC_SELLER_ID: TemplateMultiple, // 商品条码*供应商ID
  ITEM_UPC_SKU_BRAND_ID: TemplateMultiple, // 商品条码*品牌ID
  SHOP_ELE_ID: TemplateSingle, // ELE门店ID
  SHOP_ID: TemplateSingle, // 淘内门店ID
}

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout = {
  wrapperCol: {
    span: 20,
  },
  labelAlign: "center",
  labelTextAlign: "center",
  style: {
    verticalAlign: "top",
    color: "#666666",
    textAlign: "left",
    paddingRight: "12px",
  },
};
const formItemLayoutFile = {
  wrapperCol: {
    span: 20,
  },
  labelAlign: "center",
  labelTextAlign: "center",
};
const formLayout = {
  style: {
    margin: "20px auto",
  },
};

const idModeEnum = [
  { label: "商品ID", value: "ITEM_ID", type: "good" },
  { label: "商品条码", value: "ITEM_UPC", type: "good" },
  { label: "商品条码*门店ID", value: "ITEM_UPC_SHOP_ID", type: "good" },
  { label: "商品条码*供应商ID", value: "ITEM_UPC_SELLER_ID", type: "good" },
  { label: "商品条码*品牌ID", value: "ITEM_UPC_SKU_BRAND_ID", type: "good" },
  { label: "ELE门店ID", value: "SHOP_ELE_ID", type: "store" },
  { label: "淘内门店ID", value: "SHOP_ID", type: "store" },
];
/*
* 文件上传
* */
export default function DataSetUploadPage(props) {
  const {poolId,isEdit,location,history,fileUploadParams, isStoreUpload } = props;
  // 文件名称
  const [fileName, setFileName] = useState(fileUploadParams.fileName || "");
  // 文件URL
  const [fileUrl, setFileUrl] = useState(fileUploadParams.fileUrl || "");
  // 是否是店铺
  const [isStore, setIsStore] = useState(() => isStoreUpload || false);
  // 文件list
  const [defaultValue, setDefaultValue] = useState([]);
  // 无文件时弹出提醒
  const [isSubmit, setIsSubmit] = useState(false);
  // 是否是可下一步状态
  const [canSubmit, setCanSubmit] = useState(true);
  // 是否正在提交
  const [isLoad, setIsLoad] = useState(false);
  const field = Field.useField();
  // 判断是商品还是店铺
  const [curIdModeEnum, setCurIdModeEnum] = useState(
    isStore
      ? idModeEnum.filter((v) => v.type == "store")
      : idModeEnum.filter((v) => v.type == "good")
  );
  // 改变上传拖拽颜色
  const [isDrag, setIsDrag] = useState(false);
  
  useEffect(() => {
    if (isEdit) {
      setPool();
    }else if (fileUrl) {
      // 如果有，证明是从基础信息点击上一步进入，所以进行初始化
      field.setValues({
        poolId: fileUploadParams.poolId,
        refreshMode: fileUploadParams.refreshMode,
        fileName: fileUploadParams.fileName,
        idMode: fileUploadParams.idMode,
        fileUrl: fileUploadParams.fileUrl,
      });
      if ( field.getValue("fileUrl")) {
        setDefaultValue([
          {
            uid: "0",
            name: field.getValue("fileName"),
            state: "done",
            url: field.getValue("fileUrl"),
          },
        ]);
      }
    }
    setIsStore(location.pathname.startsWith("/storepool"));
    isStoreUpload && setIsStore(isStoreUpload)
  }, []);
  useEffect(() => {
    if (isStore) {
      setCurIdModeEnum(idModeEnum.filter((v) => v.type == "store"));
    } else {
      setCurIdModeEnum(idModeEnum.filter((v) => v.type == "good"));
    }
  }, [isStore]);
  // 如果初始化时有poolId，进行初始化
  const setPool = async () => {
    try {
      apiL.getPoolDetailByPoolId(poolId)
        .then((resp) => {
          field.setValues({
            poolId: poolId,
            refreshMode: resp.data.refreshMode,
            fileName: resp.data.fileName,
            idMode: resp.data.idMode,
            fileUrl: resp.data.fileUrl,
          });
          fileUploadParams.fileName=resp.data.fileName;
          fileUploadParams.fileUrl = resp.data.fileUrl;
          fileUploadParams.poolId = poolId;
          fileUploadParams.refreshMode = resp.data.refreshMode;
          fileUploadParams.idMode = resp.data.idMode;
          props.setFileUploadParams(fileUploadParams)
          if (resp.data.fileUrl) {
            setFileUrl(resp.data.fileUrl);
            setFileName(resp.data.fileName);
            setDefaultValue([
              {
                uid: "0",
                name: resp.data.fileName,
                state: "done",
                url: resp.data.fileUrl,
              },
            ]);
          }
        })
    } catch (error) {
      api.onRequestError(error)
    }
  };
  const nextStep =async ()=>{
    setIsSubmit(true);
    if(fileUrl||isEdit){
      const outerForm = await promisify(field.validate)();
      props.setFileUploadParams({
        fileName: fileName,
        fileUrl: fileUrl,
        poolRule: {
          basePoolId: isStore ? "32001" : "22001",
        },
        poolId: poolId,
        refreshMode: 0,
        idMode:outerForm.idMode
      })
      props.goStep(2);
    }
  }

  const onChangeMethod = () =>{
    Dialog.show({
      title: "提示",
      content: '圈选方式修改后，当前设置不保存。仍要修改吗？',
      okProps: {children: '仍然修改'},
      cancelProps: {children: '取消'},
      footerActions: ['cancel', 'ok'],
      onOk: () => history.push("/pool/list/tagPool"),
      onCancel: () => console.log("cancel")
    });
  }
  const onSuccess = (info) => {
    setIsDrag(false);
    if (info.response.code == "200") {
      // 如果上传成功则设置将此文件添加到DefaultValue中
      setFileName(info.response.data.fileName);
      setFileUrl(info.response.data.url);
      setDefaultValue([
        ...defaultValue,
        ...[
          {
            uid: "0",
            name: info.response.data.fileName,
            state: "done",
            url: info.response.data.url,
          },
        ],
      ]);
    } else {
      Message.error(info.response.msg || "上传失败");
    }
  };

  const onDragOver = () => {
    // 设置上传背景颜色
    setIsDrag(true);
  };
  const onDragLeave = () => {
    // 取消上传背景颜色
    setIsDrag(false);
  };

  const onRemove = (removeItem) => {
    // 每次删除都要将DefaultValue的值重新刷一遍，避免重复出现
    let list = [];
    defaultValue.map((items) => {
      if (items.url != removeItem.url) {
        list.push(items);
      }
    });
    setDefaultValue([...list]);
  };

  const beforeUpload = (file, options) => {
    // 在上传之前判断类型，如果类型不匹配直接失败。
    return new Promise((resolve, reject) => {
      if (
        file.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type == "application/vnd.ms-excel"
      ) {
        resolve(options);
      } else {
        setIsDrag(false);
        reject();
        Message.error("文件类型不匹配");
      }
    });
  };
  return (
      <PoolPageBase.Container className="CommodityUploadPage">
          <h3><i className="order-num current">1</i>圈选方式：文件上传<Button style={{marginLeft:'8px'}} text type={'primary'} onClick={onChangeMethod} disabled={isEdit}>修改</Button></h3>
          <h3><i className="order-num ">2</i>上传文件</h3>
          <Form {...formLayout} field={field}>
            <FormItem {...formItemLayout}>
              ID类型:&nbsp;&nbsp;
              <RadioGroup
                name="idMode"
                disabled={poolId}
                defaultValue={curIdModeEnum[0].value}
                dataSource={curIdModeEnum}
                style={{ minWidth: "220px" }}
              />
            </FormItem>
            <FormItem {...formItemLayoutFile}>
              <div style={{ margin: "7px 0 10px 0" }}>
                请先下载
                <a
                  target="_blank"
                  download="带说明模板.csv"
                  href={downloadUrls[field.getValue("idMode")]}
                >
                  导入模板
                </a>
                ，按照要求填写完成，上传到系统。
              </div>
              <Upload.Dragger
                headers={{ "X-Requested-With": null }}
                action={`${config.API_ROOT}/api/ali/common/upload`}
                onSuccess={onSuccess}
                disabled={poolId}
                listType="text"
                onDragOver={onDragOver}
                onDragLeave={onDragLeave}
                beforeUpload={beforeUpload}
                value={defaultValue}
                onRemove={onRemove}
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
              >
                <div
                  className={`next-upload-drag ${
                    isDrag ? "next-upload-drag-onDragOver" : ""
                  }`}
                >
                  <p className="next-upload-drag-icon">
                    <Icon type="upload" />
                  </p>
                  <p className="next-upload-drag-text">
                    点击或将文件拖拽到这里上传
                  </p>
                  {/*<p className="next-upload-drag-hint">支持扩展名：.csv</p>*/}
                  <p className="next-upload-drag-hint">
                    支持扩展名：.xlsx,.xls
                  </p>
                </div>
              </Upload.Dragger>
              {fileUrl == "" && isSubmit && (
                <div style={{ color: "#FF2D4B" }}>请上传文件</div>
              )}
            </FormItem>
            <FormItem label=" " colon={false}>
              <Form.Submit
                type="primary"
                validate
                onClick={nextStep}
                disabled={!canSubmit}
              >
                下一步
              </Form.Submit>
            </FormItem>
          </Form>
      </PoolPageBase.Container>
  );
}
