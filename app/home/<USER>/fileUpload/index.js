import React, {Fragment, useEffect, useState,createContext} from 'react';
import {Button, Dialog, Grid, Message} from '@alife/next';
import './style.scss';
import {BreadcrumbTips, CompletePage} from "@/home/<USER>/comps";
import {PageWrapper} from "@/comps/PageWrapper";
import {dealQualityScoreToObject, PoolPageBase, Steps, unique} from "@/home/<USER>/common";

import BaseInfo from './baseInfo';
import DataSetUploadPage from './dataSetUploadPage';
import {goldLog, logTimeComponent} from "@/utils/aplus";
import {
  dealQualityScoreToArray,
} from "@/home/<USER>/common";
import {withRouter} from "react-router-dom";
import {permissionAccess} from "@/components/PermissionAccess";
import * as api from "@/adator/api";
import moment from "moment";
import {promisify} from "@/utils/others";
import {optionMap} from "@/home/<USER>/common/map";
import {isArray, isString} from "@/utils/validators";
import FinishPage from "@/home/<USER>/fileUpload/finishPage";
import {getQueryString} from "@/utils/others";
const {Row, Col} = Grid;

function fileUpload(props) {
  const {location, match, history} = props;
  const incomingSourcePoolId = getQueryString('sourcePoolId');
  // 是否选店上传
  const isStoreUpload = incomingSourcePoolId === '32001';
  
  /**
   * 文件上传
   */

  const [step, setStep] = useState(0);
  const [fileUploadParams, setFileUploadParams] = useState({
    fileName: "",
    fileUrl: "",
    poolId: poolId,
    refreshMode: 0,
    idMode:""
  });
  const [sourcePoolId, setSourcePoolId] = useState(isStoreUpload ? '32001' : '22001');  //选中的底池的sourcePoolId
  const [detailData, setDetailData] = useState({});
  const [effectRules, setEffectRules] = useState([]);  //编辑标签表单的effectRules
  const [requestEffectRules, setRequestEffectRules] = useState([]);  //在请求接口中流通的requestEffectRules
  const [params, setParams] = useState({});
  const [baseInfo, setBaseInfo] = useState({});
  const [tempPoolId, setTempPoolId] = useState('');
  const [submitPoolId, setSubmitPoolId] = useState('');
  const [user,setUser] = useState({});
  const [recodeState,setRecodeState] = useState(0);
  let {poolId = ''} = match.params;
  const isEdit = poolId > 0;
  const isCopy = poolId < 0;
  poolId = poolId < 0 ? -poolId : poolId;

  const breadcrumbList = [
    {"title": '选品集管理', link: "#/pool/list"},
    {"title": isStoreUpload ? '新建门店选品集' : '新建商品选品集' , link: ""}
  ];
  useEffect(()=>{
    // if (isEdit) {
    //   getPoolDetail()
    // }
    getUser();
  },[])

  const getUser = () =>{
    try {
      api.getBucUser()
        .then((resp) => {
          setUser(resp.data.data);
          if(isEdit) {
            getPoolDetail(resp.data.data.empId);
          }
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  const  getPoolDetail = (empId)=>{
    try {
      api.getPoolDetailByPoolId(poolId)
        .then((resp) => {
          let {noticeUid} = resp.data;
          let newNoticeUid = noticeUid;
          if (noticeUid.indexOf(empId) >= 0) {
            const index = noticeUid.split(',').findIndex((o) => o == empId);
            const noticeUidGroup = noticeUid.split(',');
            noticeUidGroup.splice(index, 1);
            newNoticeUid = noticeUidGroup.join(',');
          }
          baseInfo.poolName = resp.data.poolName;
          baseInfo.effectAt = resp.data.effectAt;
          baseInfo.expireAt = resp.data.expireAt;
          baseInfo.isNotice = resp.data.isNotice;
          baseInfo.noticeUid = newNoticeUid;
          baseInfo.effectRange = [moment(resp.data.effectAt), moment(resp.data.expireAt)];
          baseInfo.outSynPlatforms = resp.data.outSynPlatforms;
          baseInfo.refreshMode = resp.data.refreshMode;
          params.refreshMode = resp.data.refreshMode;
          params.poolName = resp.data.poolName;
          params.outSynPlatforms = resp.data.outSynPlatforms;
          params.isNotice = resp.data.isNotice;
          params.noticeUid = resp.data.noticeUid;
          params.effectRange = [moment(baseInfo.effectAt),moment(baseInfo.expireAt)];
          fileUploadParams.fileName=resp.data.fileName;
          fileUploadParams.fileUrl = resp.data.fileUrl;
          fileUploadParams.poolId = poolId;
          fileUploadParams.refreshMode = resp.data.refreshMode;
          fileUploadParams.idMode = resp.data.idMode;
          setFileUploadParams(fileUploadParams)
          setBaseInfo(baseInfo);
          setParams(params);
        })
    } catch (error) {
      api.onRequestError(error)
    }
  }

  /*将requestEffectRules转化为effectRules*/
  const changeEffectRules = (nextEffectRules) =>{
    let newEffectRules = [];
    if (nextEffectRules && nextEffectRules.length > 0) {
      nextEffectRules.map((v) => {
        let value = v.filterFieldValue;
        // if(isArray(value) && value.length == 0){
        //   return;
        // }
        // if (value || value == '') {
        if (v.filterFieldComponentType == 'arrayInput') {
          value = JSON.parse(value).join(',');
        }
        if (v.filterFieldComponentType == 'picStandard') {
          value = dealQualityScoreToArray(JSON.parse(value)).map(_v => _v.value);
        }
        let typeGroup = ['rangeNumberInput', 'checkbox']
        if (typeGroup.includes(v.filterFieldComponentType)) {
          // value = JSON.parse(value).map(v => v.value);
          value = JSON.parse(value);
        }
        if (v.filterFieldComponentType == 'cascaderSelect' || v.filterFieldComponentType=='multipleSelect') {
          value = JSON.parse(value).map(_v => _v.value);
        }
        // let value = v.filterFieldValue ? ((v.type != 'arrayInput') ? v.filterFieldValue : JSON.parse(v.filterFieldValue)) : '';
        newEffectRules.push({
          filterFieldId: v.filterFieldId,
          filterFieldKey: v.filterFieldKey,
          filterFieldLabel: v.filterFieldLabel,
          filterFieldValue: value,
          filterFieldComponentType: v.filterFieldComponentType,
          filterFieldIdGroup: v.filterFieldIdGroup,
          operator: v.operator,
        })
        // }
      })
    }
    setEffectRules(newEffectRules);
  }

  /*步骤的切换，1：上一步，2：下一步*/
  const goStep = (type) => {
    setStep((type == 1) ? (step - 1) : (1 + step));
  }

  /*更新基本信息*/
  const updateBaseInfo = (name,value) =>{
    baseInfo[name] = value;
    params[name] = value;
    setParams(params);
    setBaseInfo(baseInfo);
  }


  const ctrlReq = () =>{
    let poolResultLimit ='100000';
    let {noticeUid = ''} = params;
    let noticeUidGroup = noticeUid?noticeUid.split(","):[];
    noticeUidGroup.push(user.empId);
    noticeUid = unique(noticeUidGroup).join(",");
    return {
      sourcePoolId: isStoreUpload ? '32001' : sourcePoolId,
      poolType: isStoreUpload ? 2 : 1,
      poolId:poolId || submitPoolId,
      poolName: params.poolName,
      effectAt: params.effectRange[0].valueOf(),
      expireAt: params.effectRange[1].valueOf(),
      isNotice: params.isNotice || 0,
      noticeUid,
      effectRules: requestEffectRules ,
      poolStoreType: '',
      tempPoolId,
      poolResultLimit,
      refreshMode: 0,
      outSynPlatforms: params.outSynPlatforms,
      createMode: "CREATE_EXCEL",
      fileUrl:fileUploadParams.fileUrl,
      fileName:fileUploadParams.fileName,
      idMode:fileUploadParams.idMode,
    };
  }

  const savePool = async () => {
    // await (promisify(this.baseInfoRef.current.field.validate)());
    let createPoolV2Req = ctrlReq();
    let request = api.createPool;
    if (isEdit) {
      createPoolV2Req.poolId = submitPoolId;
      request = api.updatePool;
    }
    let resp = await request(createPoolV2Req);
    if (!resp.data.data.success) {
      Message.warning(resp.data.data.errMessage);
    } else {
      Message.success('操作成功');
      history.push("/pool/list");
    }
  }

  const submitPool = async () => {
    try {
      let createPoolV2Req = ctrlReq();
      let request = api.createPool;
      if (isEdit) {
        request = api.updatePool;
      }
      let resp = await request(createPoolV2Req);
      let id = resp.data.data.data;
      if(id) {
        let result = await api.publishPool(id);
        setRecodeState(result.data.state)
        setSubmitPoolId(id);
        Message.success('发布成功');
        goStep(2);
      }else{
        Message.info(resp.errMessage);
      }
    } catch (error) {
      api.onRequestError(error);
    }
  }


  return (
    <div className="tag-pool">
        <BreadcrumbTips list={breadcrumbList}/>
        <div className="tag-pool-father">
          <Steps current={step} middleText={'基本信息'}/>
          {step == 0 &&
                  <DataSetUploadPage poolId={poolId} isEdit={isEdit} location={location} history={history}
                  isStoreUpload={isStoreUpload}
                  goStep={goStep} fileUploadParams={fileUploadParams} setFileUploadParams={setFileUploadParams} /> }
          {step == 1 &&
          <BaseInfo isEdit={isEdit} sourcePoolId={sourcePoolId} baseInfo={baseInfo} goStep={goStep} updateBaseInfo={updateBaseInfo} savePool={savePool} submitPool={submitPool} />}
          {step == 2 && <FinishPage history={history}  poolId={submitPoolId} ctrlReq={ctrlReq} recodeState={recodeState}/>}
          </div>
    </div>
  )
}

export const LogTimePutInPage = logTimeComponent(withRouter(fileUpload), (timeConsume) => {
  goldLog(['/selection_kunlun.CONFIG-TIME.config-time-putIn', `time=${timeConsume}`])
})

export const fileUploadPage = permissionAccess(LogTimePutInPage)

