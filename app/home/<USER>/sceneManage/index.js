import React, { Fragment, useEffect, useState, useCallback } from "react";
import Clipboard from "clipboard";
import {
  Form,
  Checkbox,
  Radio,
  Select,
  Input,
  Button,
  Table,
  Message,
  Dialog,
  Pagination,
  Tab,
  Divider,
  Dropdown,
  Icon,
  Menu,
  Grid,
  Balloon,
  Switch,
  Field, Breadcrumb,
} from "@alife/next";
import moment from "moment";
import { StatusLabel } from "@/comps/Label";
import "./style.scss";
import * as api from "@/utils/api";
import { goldLog, logTimeComponent, track } from "@/utils/aplus";
import { AccessBtn, DialogBtn } from "@/components/Button";
import { formatTimeStamp, FORMAT } from "@/utils/time";
import { withRouter } from "react-router-dom";
import { permissionAccess } from "@/components/PermissionAccess";
import { queryUserByKeyWord } from "@/containers/channel/market/request";
import {onRequestError} from "@/utils/api";
import {createSceneCard, getSceneListCard, deleteSceneCard} from "@/adator/api/index";
import {dateRender} from "../../poolPage/common/index";
import {isString,isAllNumber} from "@/utils/validators";
import { debounce } from "lodash";
import * as apiL from "@/adator/api";
const { Row, Col } = Grid;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: {
    span: 16,
  },
  style: {
    width: "100%",
  },
};
const PAGE_SIZE = 10;

function sceneManage({ history }) {
  const clipboard = new Clipboard("#copy");
  const [dataSource, setDataSource] = useState( []);
  const [isLoading, setIsLoading] = useState(false);
  const [size, setSize] = useState(10);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState({});
  const [roleDataSource, setRoleDataSource] = useState([]); //员工工号
  const [canSetSchema, setCanSetSchema] = useState(false);
  const field = Field.useField({
    onChange: (name, value) => {
      query[name] = value;
      setQuery(query);
    },
  });
  const columns = [
    {
      title: "ID",
      dataIndex: "sceneId",
      width: "200px",
      cell: (value, index, record) => {
        return (
          <div className="pool-info">
            <p className="pool-id">
              场景ID：{value}
              <span className="btn-copy" id="copy" data-clipboard-text={value}>
                复制
              </span>
            </p>
          </div>
        );
      },
    },
    {
      title: "场景名称",
      dataIndex: "sceneName",
      width: "200px",
      cell: (value, index, record) => {
        return (
          <div className="pool-info">
            <Button className="pool-name" text onClick={() => onView(record)} title={value}>
              {value}
            </Button>
          </div>
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "gmtCreate",
      cell: dateRender,
      width: "170px",
    },
    {title: "创建人", dataIndex: "creatorName", width: "130px"},
    // {
    //   title: "报名情况", dataIndex: "displayStatus", width: "130px", cell: (value, index, record) => {
    //     const {totalCount = '', applyCount = ''} = record;
    //
    //     return <div>{applyCount / totalCount}</div>;
    //   }
    // },
    {
      title: "商家端可查看情况", dataIndex: "displayStatus", width: "130px", cell: (value, index, record) => {
        return <Switch checked={value==1} onChange={(value) => editRecord(value,record)}/>
      }
    },
    {
      title: "操作",
      cell: (value, index, record) => {
        return <div className="opbtns">{renderNewOption(record)}</div>;
      },
      width: "200px",
    },
  ];

  const editRecord = (value,record) => {
    console.log(record);
    let requestDTO = {
      sceneId: record.sceneId || '',
      displayStatus: value ? 1 : 0
    }
    createSceneCard(requestDTO)
      .then((result) => {
        // searchPoolList();
        getSceneList(page, query);
      })
      .catch(api.onRequestError);
  }

  const onPageChange = (newPage) => {
    getSceneList(newPage,query);
  };

  const onPageSizeChange = (newSize) => {
    getSceneList(page,query,newSize);
  };

  useEffect(() => {
    getSetSchemaAuthority();
    getSceneList(1, query);
  }, []);

  /**检测id格式 */
  const checkId = function (rule, value, callback) {
    const errors = isAllNumber(value);
    if (errors.length && value) {
      callback("格式错误");
    } else {
      callback();
    }
  };

  const onSearchUser = useCallback(
    debounce((keyword) => {
      if (keyword) {
        var reg = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]+$");
        if (keyword != "" && !reg.test(keyword)) {
          Message.error("不能输入特殊字符");
          return;
        }
        queryUserByKeyWord(keyword)
          .then((data) => {
            const dataSource = data.map((v) => ({
              value: v.empId,
              label: `${v.lastName}_${v.empId}`,
              record: v,
            }));
            setRoleDataSource(dataSource);
          })
          .catch(onRequestError);
      }
    }, 800),

    [roleDataSource]
  );

  /**
   * 池子查询列表
   **/
  const getSceneList = (page = 1, query, newSize) => {
    console.log("🚀 ~ file: index.js:249 ~ getSceneList ~ query:", query,newSize,size)
    setIsLoading(true);
    // 补充参数
    const params = {};
    try {
      let request = getSceneListCard;
      request({
        pageSize: newSize ? newSize : size,
        pageNum: page,
        query,
      }).then((resp) => {
        console.log(resp);
        const {data = [], total, code} = resp.data;
        if (code == 200) {
          setDataSource(data);
          setTotal(total);
          setPage(page);
          setSize(newSize ? newSize : size)
        } else {
          api.onRequestError('网络异常');
        }
        setIsLoading(false);
      });
    } catch (error) {
      setIsLoading(false);
      api.onRequestError(error);
    }
  };
  // 查询
  const searchPoolList = () => {
    getSceneList(1, query);
  };

  // 重置表单
  const resetPoolList = () => {
    field.reset();
    field.setValues({});
    Object.keys(query).filter((key) => {
      delete query[key];
    });
    setQuery({});
    getSceneList(1, {});
  };

  const getPermission = async (id) => {
    try {
      let resp = await api.checkRankPool(id);   //todo 换掉接口
      let {rediectUrl} = resp.data.data;
      return rediectUrl;
    } catch (error) {
      onRequestError(error)
    }
  }

  const onView = (record) => {
    history.push(
      `/channelManage/gallery/sceneManage/view/${record.sceneId}`
    );
  };

  const onEdit = (record) => {
    history.push(
      `/channelManage/gallery/sceneManage/create/${record.sceneId}`
    );
  };

  const onCopy = (record) => {
    history.push(
      `/channelManage/gallery/sceneManage/create/-${record.sceneId}`
    );
  };

  const onOffLine = (record) => {
    Dialog.confirm({
      title: "提示",
      content: "确定删除当前场景么?",
      onOk: async () => {
        let request = deleteSceneCard;
        request({sceneId:record.sceneId})
          .then(() => {
            Message.success("操作成功");
            getSceneList(1, query);
          })
          .catch(api.onRequestError);
      },
    });
  };



  const renderNewOption = (record) => {
    let { state } = record;
    let divider = <Divider direction="ver" />;
    let detailEle = (
      <Button text={true} onClick={() => onView(record)}>
        查看
      </Button>
    );
    let editEle = (
      <Button text={true} onClick={() => onEdit(record)}>
        编辑
      </Button>
    );
    let copyEle = (
      <Button text={true} onClick={() => onCopy(record)}>
        复制
      </Button>
    );
    let offLineEle = (
      <Button text={true} onClick={() => onOffLine(record)}>
        删除
      </Button>
    );
    let options = <>
      {copyEle}
      {divider}
      {detailEle}
      {divider}
      {editEle}
      {divider}
      {offLineEle}
    </>
    // switch (state) {
    //   case 1:
    //   case 2:
    //   case 3:
    //   case 4:
    //     options = (
    //       <>
    //         {copyEle}
    //         {divider}
    //         {detailEle}
    //         {divider}
    //         {editEle}
    //         {divider}
    //         {offLineEle}
    //       </>
    //     );
    //     break;
    //   default:
    //     break;
    // }
    return options;
  };

  clipboard.on("success", function (e) {
    console.log(e);
    Message.success("复制成功");
  });
  clipboard.on("error", function (e) {
    console.log(e);
  });

  const getSetSchemaAuthority = async () => {
    try {
      let resp = await apiL.queryRoleValidate("set_schema");
      setCanSetSchema(resp === 'YES')
    } catch (error) {
      apiL.onRequestError(error);
    }
  };

  return (
    <div className="pool-list">
      <Form className="filter" field={field} >
        <div className='top'>
          <span className="pool-title">场景列表</span>
          <Button  type="primary" className="btn-create-pool"  onClick={(event) => {
            event.preventDefault();
            window.location.href = `${window.location.href}/create`;
          }}>
            新建场景
          </Button>
        </div>
        <Row>
          <Col span={6}>
            <FormItem label="场景ID " {...formItemLayout} validator={checkId}>
              <Input name="sceneId" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="场景名称" {...formItemLayout}>
              <Input name="sceneName" />
            </FormItem>
          </Col>
          <Col span={6}>
            <FormItem label="创建人" {...formItemLayout}>
              <Select
                name="creatorId"
                mode="single"
                hasClear
                hasArrow={false}
                onSearch={(value) => {
                  onSearchUser(value);
                }}
                showSearch
                dataSource={roleDataSource}
                style={{ width: "100%" }}
              />
            </FormItem>
          </Col>
          <Col span={6}>
            <div style={{float: 'right'}}>

              <Button
                type="secondary"
                onClick={searchPoolList}
              >
                查询
              </Button>
              <Button className="btn_reset" onClick={resetPoolList}>
                重置
              </Button>
              {canSetSchema && <Button type="primary" onClick={() => {
                history.push('/channelManage/gallery/sceneManage/schema?bizId=1&bizType=2')
              }}>
                schema
              </Button>}
            </div>
          </Col>
        </Row>
        {/*<Row>*/}
        {/*  <Col*/}
        {/*    span={24}*/}
        {/*    style={{*/}
        {/*      marginBottom: "20px",*/}
        {/*      display: "flex",*/}
        {/*      justifyContent: "end",*/}
        {/*    }}*/}
        {/*  >*/}
        {/*   */}
        {/*  </Col>*/}
        {/*</Row>*/}
      </Form>

      <div className="table-panel">
        <Table
          dataSource={dataSource}
          loading={isLoading}
          hasBorder={false}
          primaryKey="id"
        >
          {columns.map((e, idx) => {
            return <Table.Column {...e} key={idx} />;
          })}
        </Table>
        <Pagination
          onChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          total={total}
          current={page}
          pageSize={size}
          pageSizeSelector="dropdown"
          pageSizeList={[10, 20]}
          popupProps={{ align: "bl tl" }}
          style={{ float: "right", marginTop: "10px" }}
        />
      </div>
    </div>
  );
}

export const LogTimeSceneManagePage = logTimeComponent(
  withRouter(sceneManage),
  (timeConsume) => {
    goldLog([
      "/selection_kunlun.CONFIG-TIME.config-time-putIn",
      `time=${timeConsume}`,
    ]);
  }
);

export const SceneManageListPage = permissionAccess(LogTimeSceneManagePage);
