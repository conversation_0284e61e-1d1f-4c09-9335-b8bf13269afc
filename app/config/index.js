// https://yuque.antfin-inc.com/acl_buc_mozi/mozi_usersguide/ya69eg

import * as daily from "./daily";
import * as localdev from "./localdev";
import * as prod from "./prod";
import * as ppe from "./ppe";

export const ENV_ENUM = {
  daily: "daily",
  localdev: "localdev",
  prod: "prod",
  ppe: "ppe",
};

// localStorage.setItem("appenv", (location.href.indexOf('pre-kunlun.alibaba-inc.com') >= 0) ? "ppe" : '');
let isPreFlag = location.href.indexOf("pre-kunlun.alibaba-inc.com") >= 0;
// const isPre = (location.href.indexOf('selection.kunlun.alibaba-inc.com') >= 0)?'ppe':'';
if (location.href.indexOf("test.alibaba-inc.com") >= 0) {
  isPreFlag = true;
}
if (location.href.indexOf("test.selection.kunlun.alibaba-inc.com") >= 0) {
  isPreFlag = true;
}
if (location.href.indexOf("selection.pre-fc.alibaba-inc.com") >= 0) {
  // 强制通过 localStorage 让 kunlun-base 组件调用预发环境接口
  // 这个域名下只会有选投平台的页面，所以可以这么 hack，其他域名下是不允许这么做的
  localStorage.setItem("kunlun-base::force-env", "pre");
  isPreFlag = true;
}
const isPre = isPreFlag ? "ppe" : "";

export const env = localStorage.appenv || isPre || process.env.APP_ENV;
// for header api
const hdEnv = env === "prod" ? "prod" : "prepub";
window.CONFIG = { ENV: hdEnv };

export const isProduction = env === ENV_ENUM.prod;

export const config = getConfig(env);
console.log("app env running in: ", env, config);
window.configEnv = env; //日常：daily,预发：ppe，线上：prod

function getConfig(_env) {
  switch (_env) {
    case ENV_ENUM.daily:
      return daily;
    case ENV_ENUM.ppe:
      return ppe;
    case ENV_ENUM.prod:
      return { ...daily, ...prod };
    case ENV_ENUM.localdev:
      return { ...daily, ...ppe };
    default:
      return { ...daily, ...localdev };
  }
}
