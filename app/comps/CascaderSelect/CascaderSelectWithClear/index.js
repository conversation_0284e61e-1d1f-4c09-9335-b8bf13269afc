import React from 'react';
import { CascaderSelect as BaseCascaderSelect, Icon } from '@alife/next';

import './index.scss';

export class CascaderSelectWithClear extends React.Component {
  constructor(props){
    super(props);
  }

  onClick = () => {
    this.props.onChange([], []);
  }

  render(){
    const { value } = this.props;
    const deleteStyle = {
      visibility:  value && value.length ? 'visible' : 'hidden'
    }
    return (
      <div className="CascaderSelect">
        <BaseCascaderSelect {...this.props} />
        <Icon type="delete-filling" size='small' className="select-delete-filling" onClick={this.onClick} style={deleteStyle}/>
      </div>
    )
  }
}