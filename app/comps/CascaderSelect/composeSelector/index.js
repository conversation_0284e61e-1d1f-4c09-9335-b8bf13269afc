import React from 'react';

import { getCityList, onRequestError } from '@/utils/api';
import { switcher, branch } from '@/utils/switcher';
import { isArray, isFunction } from '@/utils/validators';
import { CascaderSelectWithClear } from '../CascaderSelectWithClear';
import './style.scss';

const selectorStyle = { width: '100%' };

const defaultProps = {
  style: selectorStyle,
  showSearch: true,
  multiple: true,
  value:[],
  name:'',
  expandTriggerType: 'hover',
  hasClear: true,
}

export const composeSelector = (dataSource, BaseCascaderSelect = CascaderSelectWithClear) => {
  return class Selector extends React.Component {
    static defaultProps = {
      multiple: true,
      showSearch: true,
      onChange: () => {},
      getSelectData:() => {},
      value:[]
    }

    constructor(props) {
      super(props)

      this.state = {
        data: []
      }

    }

    init = switcher(
      branch(isArray, (dataSource) => this.setState({ data: dataSource, value:[] })),
      branch(isFunction, async (dataSource) => {
        try {
          const data = await dataSource();
          this.setState({ data, value:[] })
        } catch(error){
          throw error
        }
      })
    )


    async componentDidMount(){
      this.init(dataSource)
    }

    handleChange = (value, data) => {
      if(!value) return
      const haveSelected = data.map(item => {
        const { value, label, pos } = item;
        return {
          value,
          label,
          level: pos.split('-').length - 1
        }
      })
      this.props.onChange(haveSelected)
      this.props.getSelectData(data)
    }

    render(){
      const { data } = this.state;
      const value = this.props.value || [];
      const propsSet = { ...defaultProps, ...this.props, value: value.map(item => item.value) };
      return (
        <BaseCascaderSelect
          className="compose_selector"
          {...propsSet}
          dataSource={data}
          onChange={this.handleChange}
          listStyle={{width: "200px",backgroundColor: "white"}}
        />
      )
    }
  }
}

function fetchCity() {
  return getCityList().then((res) => res).catch(onRequestError)
}

export const CitySelector = composeSelector(fetchCity)
