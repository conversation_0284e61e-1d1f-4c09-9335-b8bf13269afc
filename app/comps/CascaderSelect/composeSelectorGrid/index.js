import React from 'react';

import { getCity<PERSON>ist, getGridList, getSearchList, onRequestError } from '@/utils/api';
import { switcher, branch } from '@/utils/switcher';
import { isArray, isFunction } from '@/utils/validators';
import { Balloon } from '@alife/next';
import { CascaderSelectWithClear } from '../CascaderSelectWithClear';
import './style.scss';

const Tooltip = Balloon.Tooltip;

const selectorStyle = { width: '100%' };

const defaultProps = {
  style: selectorStyle,
  showSearch: true,
  multiple: true,
  value:[],
  name:'',
  expandTriggerType: 'hover',
  hasClear: true,
}

export const composeSelectorGrid = (dataSource, BaseCascaderSelect = CascaderSelectWithClear) => {
  return class Selector extends React.Component {
    static defaultProps = {
      multiple: true,
      showSearch: true,
      onChange: () => {},
      getSelectData:() => {},
      value:[]
    }

    constructor(props) {
      super(props)

      this.state = {
        data: []
      }
      this.searchKey = ""; // 当前搜索字段
      this.inited = false; // 网格数据是否初始化
    }

    init = switcher(
      branch(isArray, (dataSource) => this.setState({ data: this.updateData(dataSource), value:[] })),
      branch(isFunction, async (dataSource) => {
        try {
          const data = await dataSource();
          this.setState({ data:this.updateData(data), value:[] })
        } catch(error){
          throw error
        }
      })
    )

    updateData = (data) => {
      return data.map(item => {
        if (item.children && item.children.length) {
          return {
            ...item
          }
        } else {
          return {
            ...item,
            isLeaf: true
          }
        }
      })
    }

    async componentDidMount(){
      let { cityList } = this.props;
      this.init(dataSource)
      if (cityList&&cityList.length) {
        this.inited = true;
        cityList.forEach(item=>{
          if (item) {
            this.updateGrid(item);
          }
        })
      }
    }

    componentWillReceiveProps(newProps) {
      if (!this.inited && newProps.cityList && newProps.cityList.length) { //根据之前的gradings字段更新当前选项列表
        this.inited = true;
        newProps.cityList.forEach(item => {
          if (item) {
            this.updateGrid(item);
          }
        })
      }
    }
    /**
     * 更新对应城市下网格列表
     * @param  {number} val 待更新城市id
     * @return {}
     */
    updateGrid = (val) => {
      if (val) {
        let { data } = this.state;
        let city = this.getCityItem(data, val);
        if (city.loaded) {
          return
        }
        getGridList({ cityId: val }).then((res) => {
          let { data } = this.state;
          let city = this.getCityItem(data, val);
          if (res && res.length) {
            city.children = res.map(_item => ({ value: "grid_" + _item.gridId, label: _item.gridName, isLeaf: true }));
          } else {
            city.isLeaf = true;
          }
          city.loaded = true;
          this.setState({ data });
        }).catch(onRequestError)
      }
    }
    /**
     * 获取指定城市信息
     * @param  {array} data 城市列表
     * @param  {number} id   目标城市id
     * @return {}
     */
    getCityItem = (data,id) => {
      let item = {};
      data.forEach(sheng => {
        if (sheng.children) {
          sheng.children.forEach(city => {
            if (city.value == id) {
              item = city;
            }
          })
        }
      })
      return item;
    }

    handleChange = (value, data, extra) => {
      if(!value) return
      const haveSelected = data.map(item => {
        const { value, label, pos } = item;
        return {
          value,
          label,
          level: pos.split('-').length - 1
        }
      })
      this.props.onChange(haveSelected)
      this.props.getSelectData(data,extra)
    }
    /**
     * 动态更新城市列表
     * @param  {} item 城市列表点击项
     * @return {}
     */
    onLoadData = (item) => {
      let { children, value, pos, loaded } = item;
      if (pos.split("-").length == 3 && !loaded) {
        return getGridList({ cityId: value }).then((res) => {
          let { data } = this.state;
          let city = this.getCityItem(data, value);
          if (res && res.length) {
            city.children = res.map(_item => ({ value: "grid_" + _item.gridId, label: _item.gridName, isLeaf: true }));
          } else {
            city.isLeaf = true;
          }
          city.loaded = true;
          this.setState({ data });
        }).catch(onRequestError)
      } else {
        return Promise.resolve()
      }
    }
    /**
     * 处理搜索事件
     * @param  {string} key 当前搜索字段
     * @return {}
     */
    onSearch = (key) => {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        if (key) {
          getSearchList({ key }).then((res) => {
            if (res && res.length) {
              let list = [];
              res.forEach(item => {
                let { provinceId, provinceName, cityId, cityName, gridId, gridName } = item;
                if (gridId && !list.includes(cityId)) {
                  list.push(cityId)
                }
              });
              list.forEach(id=>this.updateGrid(id))
            }
          })
        }
      }, 500)
    }
    /**
     * 自定义本地过滤函数
     * @param  {String} searchValue 搜索的关键字
     * @param  {Array} path        节点路径
     * @return {Boolean}             是否匹配
     */
    filter = (searchValue, path) => {
      if (searchValue != this.searchKey) {
        this.searchKey = searchValue;
        this.onSearch(this.searchKey)
      }
      return path.some(
        item => {
          let { value, label } = item;
          let res = String(label)
          .toLowerCase()
          .indexOf(String(searchValue).toLowerCase()) > -1;
          if (String(value).includes("grid_")) {
            return res || String(value)
            .toLowerCase()
            .indexOf(String(searchValue).toLowerCase()) > -1;
          } else {
            return res
          }
        }
      );
    }

    displayRender = (label) => {
      if (label.length==3) {
        let [p,c,g] = label;
        return <Tooltip trigger={`${c}/${g.length>5?"..."+g.slice(-5):g}`} align="t">{g}</Tooltip>
      } else {
        return label[label.length-1]
      }
    }

    itemRender = (item) => {
      let { value, label } = item;
      if (String(value).includes("grid_")) {
        return [label,<span className="grid-id">{`(${value.split("_")[1]})`}</span>]
      } else {
        return label
      }
    }

    resultRender = (searchValue, path) => {
      const parts = [];
      path.forEach((item, i) => {
        const reExp = searchValue.replace(/[-.+*?^$()[\]{}|\\]/g, v => `\\${v}`);

        let label = "";
        if (String(item.value).includes("grid_")) {
          label = `${item.value.split("_")[1]}${item.label}`
        } else {
          label = item.label
        }

        const re = new RegExp(reExp, 'gi');
        const others = label.split(re);
        const matches = label.match(re);

        others.forEach((other, j) => {
          if (other) {
            parts.push(other);
          }
          if (j < others.length - 1) {
            parts.push(<em key={`${i}-${j}`}>{matches[j]}</em>);
          }
        });
        if (i < path.length - 1) {
          parts.push(' / ');
        }
      });
      return <span>{parts}</span>;
    }

    render(){
      const { data } = this.state;
      const value = this.props.value || [];
      const propsSet = { ...defaultProps, ...this.props, value: value.map(item => item.value) };

      return (
        <BaseCascaderSelect
          className="compose_selector"
          {...propsSet}
          filter={this.filter}
          dataSource={data}
          loadData={this.onLoadData}
          onChange={this.handleChange}
          displayRender={this.displayRender}
          itemRender={this.itemRender}
          resultRender={this.resultRender}
          listStyle={{width: "200px",backgroundColor: "white"}}
          popupClassName="grid-selector-wrapper"
        />
      )
    }
  }
}

function fetchCity() {
  return getCityList().then((res) => res).catch(onRequestError)
}

export const CitySelector = composeSelectorGrid(fetchCity)
