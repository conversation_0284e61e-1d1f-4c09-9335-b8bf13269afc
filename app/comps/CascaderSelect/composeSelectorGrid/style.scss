// .next-cascader-menu-wrapper {
//   width: 200px !important;
//   background-color: white !important;
// }

.compose_selector.next-select-multiple .next-input {
  max-height: 80px;
  overflow: scroll;
}

.compose_selector {
  .next-tag-closable.next-tag-small > .next-tag-body {
    max-width: calc(100% - 0px) !important;
  }
}

.grid-selector-wrapper {
  .next-cascader-inner {
    .next-cascader-menu-wrapper:nth-child(3) {
      width: 300px !important;;
      .next-menu-item-inner {
        height: auto;
        display: flex;
        align-items: center;

        .next-menu-item-text {
          white-space: normal;
          line-height: 20px;
          padding: 8px 0;

          .grid-id {
            color: #999;
            font-size: 12px;
          }
        }
      }
    }
  }
}
