
.c-new-page-wrapper {
  padding:10px 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;


  h2 {
    font-size: 20px;
    line-height: 20px;
    padding: 20px 0 20px 20px;
    background: #fff;
  }

  .withBorder{
    border-bottom: 1px solid #D9D9D9;
  }

  .second-title {
    font-size: 14px;
    color: #999999;
  }


  &__content {
    background-color: white;
    padding: 24px;
    flex: 1;
  }

  &.supply{ /*补充*/
    margin:0;
    .c-new-page-wrapper__content {
      padding:0px !important;
      background-color: transparent;
    }
  }
}

.c-page-section {
  margin: 20px 0;

  .title {
    font-size: 16px;
    color: #999999;
    line-height: 16px;
    margin-bottom: 30px;
  }

  &__content {
    // nothing right now
  }
}
