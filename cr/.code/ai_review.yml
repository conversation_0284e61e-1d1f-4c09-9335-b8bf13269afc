version: 1
rules:
  - path: "**/*.{js,ts,jsx,tsx}"
    rule: 字段名、变量名、函数名要明确语义，命名要能贴合业务语义和功能场景
  - path: "**/*.{js,ts,jsx,tsx}"
    rule: 可以被计算出来的数据，不应该单独使用 useState 来管理，应该使用 useMemo
  - path: "**/*.{ts,tsx}"
    rule: 关键业务模型对象的字段需要有明确的注释说明，同时要标注引入字段的项目
  - path: "**/*.{ts,tsx}"
    rule: service、model 定义要避免使用 any，要明确定义数据类型
  - path: "**/*.{ts,tsx,js,jsx}"
    rule: 不要使用 index、i 等数组下标作为 react 组件的 key
  - path: "**/*.{js,ts,jsx,tsx}"
    rule: 不要嵌套多个三目运算符，涉及到多个或者多层 if else 分支逻辑需要保证代码可读性
  - path: "**/*.{js,ts,jsx,tsx}"
    rule: 不要有 env 硬编码问题（prod、pre），如果涉及到环境标识的判断，每种环境对应的处理逻辑都要用，不能只处理一种环境，其他的不管了
  - path: "**/*.{js,ts,jsx,tsx}"
    rule: 不要在 useEffect 当中调用 onChange 函数，useEffect 和 onChange 不应该也没有理由混合在一起使用
  - path: "**/*.{js,ts,tsx,jsx}"
    rule: 不要既使用 useForm 或者 useField 来管理表单状态，又同时使用普通的 React state 来管理表单状态
  - path: "**/*.{js,ts,tsx,jsx}"
    rule: "Single responsibility principle violation: Using a single property to control multiple unrelated behaviors."
  - path: "**/*.{js,ts,tsx,jsx}"
    rule: "React prop handling issue: Avoid using `...rest` to pass props without explicit specification"
  - path: "**/*.{js,ts,tsx,jsx}"
    rule: "Magic number usage issue: Replace hardcoded values with constants for better maintainability and readability."
  - path: "**/*.{js,ts,tsx,jsx}"
    rule: 明显是调试用的 log 代码应该清理掉
